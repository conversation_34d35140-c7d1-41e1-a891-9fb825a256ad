FROM registry.cn-shenzhen.aliyuncs.com/edf/anolisos:7.9
#官网下载 jdk-8u291-linux-aarch64.tar.gz  jdk-8u291-linux-x64.tar.gz
ADD jdk1.8.0_291	/jdk1.8.0_291/

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone
RUN yum install -y kde-l10n-Chinese glibc-common fontconfig
RUN localedef -c -f UTF-8 -i zh_CN zh_CN.utf8
RUN chmod +x /jdk1.8.0_291/bin/java
ENV LC_ALL=zh_CN.UTF-8
ENV JAVA_HOME=/jdk1.8.0_291
ENV JRE_HOME=${JAVA_HOME}/jre
ENV CLASSPATH=.:${JAVA_HOME}/lib:${JRE_HOME}/lib
ENV PATH=${JAVA_HOME}/bin:$PATH