package cn.bctools.custom.api;

import cn.bctools.common.utils.R;
import cn.bctools.custom.dto.TextbookFeignClientApiDto;
import cn.bctools.custom.dto.TextbookDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 教材信息获取api
 *
 * <AUTHOR>
 */
@FeignClient(value = "app-resource",contextId = "textbook")
public interface TextbookApi {

    String PREFIX = "/api/textbook";


    /**
     * 查询教材列表
     */
    @PostMapping(PREFIX + "/list")
    R<List<TextbookDto>> list(@RequestBody TextbookFeignClientApiDto textbookApiDto);
}
