FROM registry.cn-shenzhen.aliyuncs.com/edf/sky-agent:8.8.0

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' > /etc/timezone
RUN yum install epel-release -y && yum update -y \
  && yum localinstall -y --nogpgcheck https://download1.rpmfusion.org/free/el/rpmfusion-free-release-7.noarch.rpm \
  && yum install -y ffmpeg ffmpeg-devel
RUN localedef -c -f UTF-8 -i zh_CN zh_CN.utf8
ADD ./target/app-resource.jar /app/app.jar

ENV LC_ALL=zh_CN.utf8
ENV LC_ALL=zh_CN.UTF-8
ENV skyname="app-resource"
ENV JAVA_OPTS=""
ENV skyip="localhost:11800"
ENV authentication=""
#镜像默认地址为 -javaagent:/skywalking-agent/skywalking-agent.jar
ENV skywalkingPath=""
ENV nacosAddr="cloud-nacos:8848"
ENV namespace=""
ENTRYPOINT ["sh","-c","java $skywalkingPath  -Dskywalking.agent.service_name=$skyname -Dskywalking.agent.authentication=$authentication -Dskywalking.collector.backend_service=$skyip -Dspring.cloud.nacos.discovery.server-addr=$nacosAddr -Dspring.cloud.nacos.discovery.namespace=$namespace  $JAVA_OPTS -jar /app/app.jar"]