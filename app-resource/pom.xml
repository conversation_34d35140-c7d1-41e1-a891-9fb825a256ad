<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.bctools.custom</groupId>
        <artifactId>jvs-custom</artifactId>
        <version>2.1.8</version>
    </parent>

    <artifactId>app-resource</artifactId>
    <version>2.1.8</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mahout.version>0.9</mahout.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-oauth2</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-auth-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>message-push-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-design-api</artifactId>
            <version>2.1.8</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-database</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity</artifactId>
            <version>1.7</version>
        </dependency>

        <!-- Redis -->
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-elasticsearch</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jakarta.json-api</artifactId>
                    <groupId>jakarta.json</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>jakarta.json</groupId>
            <artifactId>jakarta.json-api</artifactId>
            <version>2.1.2</version>
        </dependency>
        <dependency>
            <groupId>jakarta.json.bind</groupId>
            <artifactId>jakarta.json.bind-api</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>commons-dbcp</groupId>
            <artifactId>commons-dbcp</artifactId>
            <version>1.4</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.4.0</version>
        </dependency>

        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.30</version>
        </dependency>

        <dependency>
            <groupId>com.hankcs</groupId>
            <artifactId>hanlp</artifactId>
            <version>portable-1.8.1</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.13.1</version>
        </dependency>

        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.10.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.21</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <!--mongodb 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-index</artifactId>
            <version>2.1.8</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-job</artifactId>
        </dependency>
        <!-- 多数据源 -->
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-dynamic</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>cn.bctools.custom</groupId>
            <artifactId>app-resource-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.bctools.custom</groupId>
            <artifactId>teacher-training-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.bctools.custom</groupId>
            <artifactId>teacher-research-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>net.bramp.ffmpeg</groupId>
            <artifactId>ffmpeg</artifactId>
            <version>0.8.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.mahout</groupId>
            <artifactId>mahout-core</artifactId>
            <version>${mahout.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>lucene-core</artifactId>
                    <groupId>org.apache.lucene</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.mahout</groupId>
            <artifactId>mahout-math</artifactId>
            <version>${mahout.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.mahout</groupId>
            <artifactId>mahout-integration</artifactId>
            <version>${mahout.version}</version>
        </dependency>

    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.4</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <version>2.6</version>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <!--不进行过滤的扩展名-->
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pptx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>