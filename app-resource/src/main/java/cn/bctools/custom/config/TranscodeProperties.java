package cn.bctools.custom.config;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-07-15
 */
@Data
@Accessors(chain = true)
@ConfigurationProperties(prefix = "transcode")
@Component
public class TranscodeProperties {

    /**
     * 工作文件夹
     */
    private String workFolder;
    /**
     * ffmpeg 路径
     */
    private String ffmpegPath;
    /**
     * ffprobe 路径
     */
    private String ffprobePath;

    /**
     * OSS 存储桶名称
     */
    private String bucketName;

    /**
     * 任务尝试次数
     */
    private int tryTimes;

    // 切片设置
    /**
     * 切片时间间隔，单位秒
     */
    private String hlsTimeInterval;
}
