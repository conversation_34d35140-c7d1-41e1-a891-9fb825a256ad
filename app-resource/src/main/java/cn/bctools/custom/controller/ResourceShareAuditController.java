package cn.bctools.custom.controller;


import cn.bctools.common.utils.R;
import cn.bctools.custom.entity.data.ResourceAuditLogs;
import cn.bctools.custom.entity.enums.AuditEnum;
import cn.bctools.custom.entity.vo.auditlog.ShareAuditLogPageVo;
import cn.bctools.custom.entity.vo.shareaudit.ShareAuditInfoDetailVo;
import cn.bctools.custom.entity.vo.shareaudit.ShareAuditPageVo;
import cn.bctools.custom.service.ResourceAuditLogsService;
import cn.bctools.custom.service.ResourceShareAuditService;
import cn.bctools.custom.service.TranscodingTasksService;
import cn.bctools.log.annotation.Log;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 共享资源审核表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@AllArgsConstructor
@RestController
@Api(tags = "共享资源审核管理")
@RequestMapping("/resource-share-audit")
public class ResourceShareAuditController {

    private final ResourceShareAuditService resourceShareAuditService;

    private final TranscodingTasksService transcodingTasksService;

    private final ResourceAuditLogsService logsService;

    @Log(back = false)
    @ApiOperation("分页列表")
    @PostMapping("/list")
    public R<Page<ShareAuditInfoDetailVo>> list(Page<ShareAuditInfoDetailVo> page,@RequestBody ShareAuditPageVo auditPageVo) {
        resourceShareAuditService.getPage(page, auditPageVo, transcodingTasksService);
        return R.ok(page);
    }

    @Log(back = false)
    @ApiOperation("审核资源详情")
    @GetMapping("/info/{shareAuditId}")
    public R<ShareAuditInfoDetailVo> info(@PathVariable(value = "shareAuditId") String shareAuditId) {
        ShareAuditInfoDetailVo info = resourceShareAuditService.getInfo(shareAuditId, transcodingTasksService);
        return R.ok(info);
    }

    @Log(back = false)
    @ApiOperation("审核")
    @PostMapping("/audit/{shareAuditId}")
    public R<Boolean> audit(@PathVariable(value = "shareAuditId") String shareAuditId, @RequestParam(value = "type")AuditEnum type) {
        resourceShareAuditService.audit(shareAuditId,type);
        return R.ok();
    }

    @Log(back = false)
    @ApiOperation("我的共享-分页列表")
    @PostMapping("/shareList/mine")
    public R<Page<ShareAuditInfoDetailVo>> myShareList(Page<ShareAuditInfoDetailVo> page,@RequestBody ShareAuditPageVo auditPageVo) {
        resourceShareAuditService.myShareList(page, auditPageVo);
        return R.ok(page);
    }

    @Log(back = false)
    @ApiOperation("我的共享-取消共享")
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/cancelShare/{shareAuditId}")
    public R<Boolean> cancelShare(@PathVariable(value = "shareAuditId") String shareAuditId) {
        resourceShareAuditService.cancelShare(shareAuditId);
        return R.ok();
    }

    @Log(back = false)
    @ApiOperation("日志分页列表")
    @PostMapping("/log/list")
    public R<Page<ResourceAuditLogs>> list(Page<ResourceAuditLogs> page, @RequestBody ShareAuditLogPageVo logPageVo) {
        logsService.getPage(page, logPageVo);
        return R.ok(page);
    }
}
