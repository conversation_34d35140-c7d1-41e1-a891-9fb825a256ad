//package cn.bctools.custom.controller;
//
//
//import cn.bctools.common.utils.R;
//import cn.bctools.custom.entity.data.Resources;
//import cn.bctools.custom.entity.data.Topics;
//import cn.bctools.custom.entity.vo.BaseSelectVo;
//import cn.bctools.custom.entity.vo.resource.ResourceDetailVo;
//import cn.bctools.custom.entity.vo.resource.ResourceEditVo;
//import cn.bctools.custom.entity.vo.resource.ResourceUploadVo;
//import cn.bctools.custom.entity.vo.shareaudit.ShareAuditPageVo;
//import cn.bctools.custom.service.ResourcesService;
//import cn.bctools.log.annotation.Log;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//import lombok.extern.slf4j.Slf4j;
//import lombok.AllArgsConstructor;
//
///**
// * <p>
// * 资源文件表 前端控制器
// * </p>
// *
// * <AUTHOR>
// * @since 2024-07-04
// */
//@Slf4j
//@AllArgsConstructor
//@RestController
//@RequestMapping("/resources")
//public class ResourcesController {
//
//    @Autowired
//    ResourcesService resourcesService;
//
//    @Log(back = false)
//    @ApiOperation("分页列表(弃用)")
//    @GetMapping("/list")
//    public R<Page<Topics>> list(Page<Topics> page, BaseSelectVo pageVo){
//        resourcesService.getPage(page,pageVo);
//        return R.ok();
//    }
//
//    @PostMapping("/upload/mine")
//    @Transactional(rollbackFor = Exception.class)
//    @Log
//    @ApiOperation(value = "上传我的资源(弃用)", notes = "上传我的资源(弃用)")
//    public R uploadMine(@Validated @RequestBody ResourceUploadVo resourceVo) {
//        resourcesService.uploadMine(resourceVo);
//        return R.ok().setMsg("上传成功");
//    }
//
//
//    @GetMapping("/info/{resourceId}")
//    @Log
//    @ApiOperation(value = "查看指定资源详情(弃用)", notes = "查看指定资源详情(弃用)")
//    public R<ResourceDetailVo> getInfo(@PathVariable(value = "resourceId") String resourceId) {
//        ResourceDetailVo info = resourcesService.getInfo(resourceId);
//        return R.ok(info);
//    }
//
//    @PostMapping("/edit")
//    @Transactional(rollbackFor = Exception.class)
//    @Log
//    @ApiOperation(value = "编辑资源(弃用)", notes = "编辑资源(弃用)")
//    public R<Boolean> edit(@Validated @RequestBody ResourceEditVo resource) {
//        resourcesService.edit(resource);
//        return R.ok(true,"编辑成功");
//    }
//
//    @PostMapping("/share/center/{resourceId}")
//    @Transactional(rollbackFor = Exception.class)
//    @Log
//    @ApiOperation(value = "共享资源(弃用)", notes = "共享资源(弃用)")
//    public R<Boolean> shareCenter(@PathVariable(value = "resourceId")String resourceId) {
//        resourcesService.shareCenter(resourceId);
//        return R.ok(true,"共享资源成功，等待审核处理");
//    }
//}
