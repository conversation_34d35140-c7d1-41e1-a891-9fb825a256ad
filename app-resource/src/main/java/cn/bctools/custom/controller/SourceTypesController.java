package cn.bctools.custom.controller;

import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.utils.R;
import cn.bctools.custom.entity.ImportFeedback;
import cn.bctools.custom.entity.data.SourceTypes;
import cn.bctools.custom.entity.vo.sourceType.SourceTypeListReVo;
import cn.bctools.custom.entity.vo.sourceType.SourceTypeRtnVo;
import cn.bctools.custom.entity.vo.sourceType.SourceTypeVo;
import cn.bctools.custom.entity.vo.tag.TagsListReVo;
import cn.bctools.custom.service.SourceTypesService;
import cn.bctools.log.annotation.Log;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@Api(value = "资源类型管理", tags = "资源类型管理接口")
@AllArgsConstructor
@RestController
@RequestMapping("/source_types")
public class SourceTypesController {

    @Autowired
    SourceTypesService sourceTypesService;

    @GetMapping("/list")
    @Log
    @ApiOperation(value = "查看资源类型列表", notes = "")
    public R<Page<SourceTypeRtnVo>> getSourceTypeList(SourceTypeListReVo sourceTypeListReVo, Page<SourceTypes> page) {
        return R.ok(sourceTypesService.getSourceTypeList(page, sourceTypeListReVo));
    }

    @GetMapping("/{id}")
    @Log
    @ApiOperation(value = "查看资源类型详情", notes = "")
    public R<SourceTypeRtnVo> getSourceType(@PathVariable("id") String id) {
        return R.ok(sourceTypesService.getSourceType(id));
    }

    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    @Log
    @ApiOperation(value = "新增资源类型", notes = "")
    public R<Boolean> addSourceType(@RequestBody SourceTypeVo sourceTypeVo) {
        sourceTypesService.addSourceType(sourceTypeVo);
        return R.ok(true, "新增成功");
    }

    @PostMapping("/edit")
    @Transactional(rollbackFor = Exception.class)
    @Log
    @ApiOperation(value = "编辑资源类型", notes = "")
    public R<Boolean> editSourceType(@RequestBody SourceTypeVo sourceTypeVo) {
        sourceTypesService.editSourceType(sourceTypeVo);
        return R.ok(true, "编辑成功");
    }


    @DeleteMapping("/{ids}")
    @Transactional(rollbackFor = Exception.class)
    @Log
    @ApiOperation(value = "删除资源类型", notes = "")
    public  R<Boolean> editTagCategories(@PathVariable("ids") List<String> ids) {
        sourceTypesService.removeByIds(ids);
        return R.ok(true, "删除成功");
    }

    @ApiOperation("下载模板")
    @GetMapping("/template/excel/download")
    public void downloadExcelTemplate(HttpServletResponse response) {
        sourceTypesService.downloadExcelTemplate(response);
    }

    @ApiOperation("导入")
    @PostMapping("/import")
    @Transactional(rollbackFor = Exception.class)
    public R<ImportFeedback> importUser(@RequestPart("file") MultipartFile file) {
        UserDto currentUser = UserCurrentUtils.getCurrentUser();
        ImportFeedback feedbackFile = sourceTypesService.importSourceTypeExcel(currentUser, file);
        return R.ok(feedbackFile, "导入成功，已生成反馈结果!");
    }

    @ApiOperation(value = "导出", notes = "")
    @PostMapping("/export")
    @Transactional(rollbackFor = Exception.class)
    public void export(HttpServletResponse response, @RequestBody SourceTypeListReVo sourceTypeListReVo) {
        sourceTypesService.exportExcel(response, sourceTypeListReVo);
    }

}
