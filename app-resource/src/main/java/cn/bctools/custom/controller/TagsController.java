package cn.bctools.custom.controller;

import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.utils.R;
import cn.bctools.custom.entity.ImportFeedback;
import cn.bctools.custom.entity.data.Tags;
import cn.bctools.custom.entity.vo.tag.TagTreeVo;
import cn.bctools.custom.entity.vo.tag.TagsListReVo;
import cn.bctools.custom.entity.vo.tag.TagsRtnVo;
import cn.bctools.custom.entity.vo.tag.TagsVo;
import cn.bctools.custom.entity.vo.tagCategory.TagCategoryListReVo;
import cn.bctools.custom.entity.vo.tagCategory.TagCategoryTreeRtnVo;
import cn.bctools.custom.service.TagsService;
import cn.bctools.custom.sync.nfdjt.handler.SyncNfdjtResourceInfoHandler;
import cn.bctools.log.annotation.Log;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Api(value = "标签项", tags = "标签项管理接口")
@AllArgsConstructor
@RestController
@RequestMapping("/tags")
public class TagsController {

    @Autowired
    private TagsService tagsService;


    @GetMapping("/list")
    @Transactional(rollbackFor = Exception.class)
    @Log
    @ApiOperation(value = "标签项列表", notes = "")
    public R<Page<TagsRtnVo>> list(TagsListReVo tagsVo, Page<Tags> page) {
        return R.ok(tagsService.getList(page, tagsVo));
    }

    @GetMapping("/relation-list")
    @ApiOperation(value = "标签分类关联列表", notes = "")
    public R<List<TagCategoryTreeRtnVo>> relationList(TagCategoryListReVo tagCategoryListReVo) {
        return R.ok(tagsService.getTagCategoryTree(tagCategoryListReVo));
    }


    @GetMapping("/mytags")
    @Transactional(rollbackFor = Exception.class)
    @Log
    @ApiOperation(value = "我的标签项列表", notes = "")
    public R<Page<TagsRtnVo>> mytags(TagsListReVo tagsVo, Page<Tags> page) {
        return R.ok(tagsService.mytags(page, tagsVo));
    }

    @PostMapping("/mytags-tree")
    @ApiOperation(value = "我的标签项树", notes = "")
    public R<List<TagCategoryTreeRtnVo>> mytagsTree(@RequestBody TagTreeVo tagTreeVo) {
        return R.ok(tagsService.getMyTagCategoryTree(tagTreeVo));
    }


    @PostMapping("/add")
    @Transactional(rollbackFor = Exception.class)
    @Log
    @ApiOperation(value = "新增标签项", notes = "")
    public R<Boolean> add(@RequestBody TagsVo tagsVo) {
        tagsService.add(tagsVo);
        return R.ok(true, "新增成功");
    }

    @PostMapping("/edit")
    @Transactional(rollbackFor = Exception.class)
    @Log
    @ApiOperation(value = "编辑标签项", notes = "")
    public R<Boolean> edit(@RequestBody TagsVo tagsVo) {
        tagsService.edit(tagsVo);
        return R.ok(true, "编辑成功");
    }


    @DeleteMapping("/{ids}")
    @Transactional(rollbackFor = Exception.class)
    @Log
    @ApiOperation(value = "删除标签项", notes = "")
    public  R<Boolean> del(@PathVariable("ids") List<String> ids) {
        tagsService.removeByIds(ids);
        return R.ok(true, "删除成功");
    }


    @ApiOperation("下载模板")
    @GetMapping("/template/excel/download")
    public void downloadExcelTemplate(HttpServletResponse response) {
        tagsService.downloadExcelTemplate(response);
    }

    @ApiOperation("导入")
    @PostMapping("/import")
    @Transactional(rollbackFor = Exception.class)
    public R<ImportFeedback> importUser(@RequestPart("file") MultipartFile file) {
        UserDto currentUser = UserCurrentUtils.getCurrentUser();
        ImportFeedback feedbackFile = tagsService.importExcel(currentUser, file);
        return R.ok(feedbackFile, "导入成功，已生成反馈结果!");
    }

    @ApiOperation(value = "导出标签", notes = "")
    @PostMapping("/export")
    @Transactional(rollbackFor = Exception.class)
    public void export(HttpServletResponse response, @RequestBody TagsListReVo tagsListReVo) {
        tagsService.exportExcel(tagsListReVo, response);
    }


}
