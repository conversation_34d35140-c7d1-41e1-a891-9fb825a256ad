package cn.bctools.custom.controller;


import cn.bctools.common.utils.R;
import cn.bctools.custom.entity.data.Topics;
import cn.bctools.custom.entity.vo.topic.ExportTopicQueryVo;
import cn.bctools.custom.entity.vo.topic.TopicPageVo;
import cn.bctools.custom.entity.vo.topic.TopicVo;
import cn.bctools.custom.service.TopicsService;
import cn.bctools.document.entity.DcLibrary;
import cn.bctools.document.entity.enums.DcLibraryLogOperationTypeEnum;
import cn.bctools.document.log.DocumentLog;
import cn.bctools.document.message.aspect.MessagePush;
import cn.bctools.log.annotation.Log;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
import lombok.AllArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 资源专题表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@AllArgsConstructor
@RestController
@Api(tags = "专题管理")
@RequestMapping("/topics")
public class TopicsController {

    private final TopicsService topicsService;

    @Log(back = false)
    @ApiOperation("分页列表")
    @GetMapping("/list")
    public R<Page<Topics>> list(Page<Topics> page, TopicPageVo topicPageVo) {
        topicsService.getPage(page, topicPageVo);
        return R.ok(page);
    }

    @Log(back = false)
    @ApiOperation("新增专题")
    @PostMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> save(@Validated @RequestBody TopicVo topics) {
        Boolean flag = topicsService.saveTopic(topics);
        return R.ok(flag, "新增成功");
    }

    @Log(back = false)
    @ApiOperation("编辑专题")
    @PostMapping("/edit")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> edit(@RequestBody TopicVo topics) {
        Boolean flag = topicsService.editTopic(topics);
        return R.ok(flag, "编辑成功");
    }

    @Log(back = false)
    @ApiOperation("上架专题")
    @PostMapping("/shelf")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> shelf(@RequestBody List<String> ids) {
        topicsService.shelf(ids);
        return R.ok();
    }

    @Log(back = false)
    @ApiOperation("下架专题")
    @PostMapping("/unshelf")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> unshelf(@RequestBody List<String> ids) {
        topicsService.unshelf(ids);
        return R.ok();
    }

    @Log
    @DeleteMapping("/del")
    @ApiOperation("删除专题")
    @Transactional(rollbackFor = Exception.class)
    @DocumentLog(operationType = DcLibraryLogOperationTypeEnum.DELETE)
    @MessagePush(messagePushTye = DcLibraryLogOperationTypeEnum.DELETE)
    public R<List<DcLibrary>> del(@RequestBody List<String> ids) {
        List<DcLibrary> del = topicsService.del(ids);
        return R.ok(del);
    }

    @Log(back = false)
    @ApiOperation("下拉列表")
    @GetMapping("/selectList")
    public R<List<Topics>> list() {
        List<Topics> topics = topicsService.getSelectList();
        return R.ok(topics);
    }

    @PostMapping("/export")
    @Log(back = false)
    @ApiOperation(value = "导出专题信息", notes = "导出专题信息")
    public R<Boolean> export(@RequestBody ExportTopicQueryVo vo , HttpServletResponse response) throws IOException {
        topicsService.export(vo,response);

        return R.ok(true, "导出成功");
    }
}
