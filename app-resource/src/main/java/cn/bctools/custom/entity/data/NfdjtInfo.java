package cn.bctools.custom.entity.data;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 南方大讲堂信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("nfdjt_info")
@ApiModel(value="NfdjtInfo对象", description="南方大讲堂信息表")
public class NfdjtInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    @ApiModelProperty(value = "活动id")
    @TableField("activity_id")
    private String activityId;

    @ApiModelProperty(value = "类型")
    @TableField("nfdjt_type")
    private String nfdjtType;

    @ApiModelProperty(value = "方式")
    @TableField("way")
    private String way;

    @ApiModelProperty(value = "学段")
    @TableField("period")
    private String period;

    @ApiModelProperty(value = "学科")
    @TableField("subject")
    private String subject;

    @ApiModelProperty(value = "年级")
    @TableField("grade")
    private String grade;

    @ApiModelProperty(value = "年份（非字典）")
    @TableField("year")
    private String year;

    @ApiModelProperty(value = "场次")
    @TableField("session_number")
    private String sessionNumber;

    @ApiModelProperty(value = "活动类型,1:预告,2:回放")
    @TableField("activity_type")
    private Integer activityType;

    @ApiModelProperty(value = "是否轮播，1:是,0:否")
    @TableField("is_banner")
    private Boolean isBanner;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by_id", fill = FieldFill.INSERT)
    private String createById;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by_id", fill = FieldFill.INSERT_UPDATE)
    private String updateById;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识（0-正常,1-删除）")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;

    @ApiModelProperty(value = "租户id")
    @TableField("tenant_id")
    private String tenantId;

}
