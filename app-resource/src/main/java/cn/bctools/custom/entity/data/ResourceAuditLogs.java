package cn.bctools.custom.entity.data;

import cn.bctools.custom.entity.enums.AuditEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 审核日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("resource_audit_logs")
@ApiModel(value="ResourceAuditLogs对象", description="审核日志表")
public class ResourceAuditLogs implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    @ApiModelProperty(value = "审核id")
    @TableField("audit_id")
    private String auditId;

    @ApiModelProperty(value = "用户id")
    @TableField("user_id")
    private String userId;

    @ApiModelProperty(value = "账户名称")
    @TableField("account_name")
    private String accountName;

    @ApiModelProperty(value = "用户姓名")
    @TableField("real_name")
    private String realName;

    @ApiModelProperty(value = "ip地址")
    @TableField("ip")
    private String ip;

    @ApiModelProperty(value = "操作类型")
    @TableField("operate_type")
    private AuditEnum operateType;

    @ApiModelProperty(value = "文件路径")
    @TableField("file_path")
    private String filePath;

    @ApiModelProperty(value = "文件存储桶")
    @TableField("bucket_name")
    private String bucketName;

    @ApiModelProperty(value = "操作结果")
    @TableField("operate_result")
    private Boolean operateResult;

    @ApiModelProperty(value = "操作时间")
    @TableField("operate_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operateTime;

    @ApiModelProperty(value = "租户id")
    @TableField("tenant_id")
    private String tenantId;


}
