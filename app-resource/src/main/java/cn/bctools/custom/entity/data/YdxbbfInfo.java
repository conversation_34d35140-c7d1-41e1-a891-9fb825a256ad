package cn.bctools.custom.entity.data;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 粤东西北帮扶详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ydxbbf_info")
@ApiModel(value="YdxbbfInfo对象", description="粤东西北帮扶详情表")
public class YdxbbfInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    @ApiModelProperty(value = "活动id")
    @TableField("activity_id")
    private String activityId;

    @ApiModelProperty(value = "负责人id")
    @TableField("activity_fzr_id")
    private String activityFzrId;

    @ApiModelProperty(value = "年份(非字典)")
    @TableField("year")
    private String year;

    @ApiModelProperty(value = "负责人")
    @TableField("activity_fzr")
    private String activityFzr;

    @ApiModelProperty(value = "承办单位")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty(value = "活动地点(区域字典)")
    @TableField("activity_place")
    private String activityPlace;

    @ApiModelProperty(value = "是否轮播,0:否,1:是")
    @TableField("is_banner")
    private Boolean isBanner;

    @ApiModelProperty(value = "活动开始时间")
    @TableField("start_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "活动结束时间")
    @TableField("end_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by_id", fill = FieldFill.INSERT)
    private String createById;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by_id", fill = FieldFill.INSERT_UPDATE)
    private String updateById;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识（0-正常,1-删除）")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;

    @ApiModelProperty(value = "租户id")
    @TableField("tenant_id")
    private String tenantId;


}
