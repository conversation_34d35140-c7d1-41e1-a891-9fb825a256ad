package cn.bctools.custom.entity.vo.knowledgeBase;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class KnowledgeBaseRtnVo {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "学段学科id")
    private String perAndSubId;

    @ApiModelProperty(value = "学段学科名称")
    private String perAndSub;

    @ApiModelProperty(value = "学段id")
    private String periodId;

    @ApiModelProperty(value = "学段名称")
    private String periodName;

    @ApiModelProperty(value = "学科id")
    private String subjectId;

    @ApiModelProperty(value = "学科名称")
    private String subjectName;

    @ApiModelProperty(value = "知识点目录名称")
    private String name;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识（0-正常,1-删除）")
    private Boolean delFlag;

    @ApiModelProperty(value = "租户id")
    private String tenantId;

}
