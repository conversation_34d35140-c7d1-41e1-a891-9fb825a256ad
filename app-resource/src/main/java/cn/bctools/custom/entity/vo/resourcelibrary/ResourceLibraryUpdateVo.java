package cn.bctools.custom.entity.vo.resourcelibrary;

import cn.bctools.document.entity.DcLibrary;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class ResourceLibraryUpdateVo extends DcLibrary {

    @ApiModelProperty("标签ids")
    private List<String> tags;

    @ApiModelProperty(value = "知识点ids")
    private List<String> knowledgePointIds;

    @ApiModelProperty(value = "资源文档id")
    private String libraryId;

    @ApiModelProperty(value = "资源简介")
    private String introduction;

    @ApiModelProperty(value = "资源类别")
    private String sourceTypeId;

    @ApiModelProperty(value = "学段")
    private String period;

    @ApiModelProperty(value = "学科")
    private String subject;

    @ApiModelProperty(value = "年级")
    private String grade;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "教材id")
    private String textbookId;

    @ApiModelProperty(value = "教材目录id")
    private String textbookCatalogueId;
}
