package cn.bctools.custom.entity.vo.resourcelibrary;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class TagResourceReqVo {
    @ApiModelProperty(value = "资源id数组")
    @NotNull(message = "资源id数组不能为空")
    private List<String> resourceIds;

    @ApiModelProperty(value = "标签id数组")
    @NotNull(message = "标签id数组不能为空")
    private List<String> tagIds;

}
