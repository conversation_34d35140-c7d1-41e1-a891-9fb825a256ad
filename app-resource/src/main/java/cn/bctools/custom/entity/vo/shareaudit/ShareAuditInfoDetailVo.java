package cn.bctools.custom.entity.vo.shareaudit;

import cn.bctools.custom.entity.data.Knowledge;
import cn.bctools.custom.entity.data.ResourceAuditSnapshot;
import cn.bctools.custom.entity.data.Tags;
import cn.bctools.custom.entity.enums.AuditEnum;
import cn.bctools.custom.entity.vo.transcodingTask.PreviewLinkVo;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class ShareAuditInfoDetailVo extends ResourceAuditSnapshot {

    @ApiModelProperty(value = "审核信息id")
    private String shareAuditId;

    @ApiModelProperty(value = "文档id")
    private String libraryId;

    @ApiModelProperty(value = "资源id")
    private String resourceId;

    @ApiModelProperty(value = "资源审核信息快照id")
    private String auditSnapshotId;

    @ApiModelProperty(value = "审核人id")
    private String auditById;

    @ApiModelProperty(value = "审核人")
    private String auditBy;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "审核状态,0:未通过,1:已通过,2:待审核")
    private AuditEnum auditStatus;


    @ApiModelProperty("标签信息")
    private List<Tags> tagInfos;

    @ApiModelProperty("知识点信息")
    private List<Knowledge> knowledgePointInfos;

    @ApiModelProperty(value = "专题信息")
    private String topicName;

    @ApiModelProperty(value = "资源类别")
    private String sourceTypeName;

    @ApiModelProperty(value = "学段")
    private String periodName;

    @ApiModelProperty(value = "学科")
    private String subjectName;

    @ApiModelProperty(value = "学段学科")
    private String perAndSub;

    @ApiModelProperty(value = "年级")
    private String gradeName;

    @ApiModelProperty(value = "年份")
    private String yearName;

    @ApiModelProperty(value = "所有者")
    private String possessor;

    @ApiModelProperty(value = "教材")
    private String textbookName;

    @ApiModelProperty(value = "教材目录")
    private String textbookCatalogueName;

    @ApiModelProperty(value = "文件链接")
    private String fileLink;

    @ApiModelProperty(value = "标签中文显示")
    private String tagNames;

    @ApiModelProperty(value = "知识点中文显示")
    private String knowledgeNames;

    @ApiModelProperty(value = "浏览量")
    private Integer viewNum;

    @ApiModelProperty(value = "收藏量")
    private Integer collectNum;

    @ApiModelProperty(value = "下载量")
    private Integer downLoadNum;

    @ApiModelProperty(value = "引用量")
    private Integer quoteNum;

    @ApiModelProperty(value = "预览链接信息，可能为空或不返回")
    private PreviewLinkVo previewLink;
}
