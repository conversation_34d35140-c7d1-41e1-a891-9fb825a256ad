package cn.bctools.custom.entity.vo.shareaudit;

import cn.bctools.custom.entity.enums.AuditEnum;
import cn.bctools.custom.entity.vo.BaseSelectVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = false)
public class ShareAuditPageVo extends BaseSelectVo {

    @ApiModelProperty("审核状态,0:未通过,1:已通过,2:待审核")
    private AuditEnum auditStatus;
}
