package cn.bctools.custom.entity.vo.topic;

import cn.bctools.custom.entity.data.Topics;
import cn.bctools.oss.dto.BaseFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class TopicVo extends Topics {
    @ApiModelProperty("封面文件(图片类型)")
    @NotNull(message = "请上传专题封面")
    private BaseFile coverImg;
}
