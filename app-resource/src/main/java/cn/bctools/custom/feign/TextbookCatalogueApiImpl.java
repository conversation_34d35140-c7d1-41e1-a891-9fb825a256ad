package cn.bctools.custom.feign;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.R;
import cn.bctools.custom.api.TextbookCatalogueApi;
import cn.bctools.custom.dto.TextBookCatalogueTreeDto;
import cn.bctools.custom.entity.vo.textBook.TextBookCatalogueTreeVo;
import cn.bctools.custom.service.TextBookCatalogueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 教材目录信息获取api
 * 具体实现方法
 * <AUTHOR>
 */
@Slf4j
@Api("教材目录信息获取api")
@RestController
@AllArgsConstructor
public class TextbookCatalogueApiImpl implements TextbookCatalogueApi {

    private final TextBookCatalogueService textBookCatalogueService;

    @Override
    @ApiOperation("查询教材目录")
    public R<List<TextBookCatalogueTreeDto>> list(String id) {
        List<TextBookCatalogueTreeVo> list = textBookCatalogueService.view(id);
        return R.ok(BeanCopyUtil.copys(list, TextBookCatalogueTreeDto.class));
    }
}
