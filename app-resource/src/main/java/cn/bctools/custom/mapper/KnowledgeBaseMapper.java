package cn.bctools.custom.mapper;

import cn.bctools.custom.entity.data.KnowledgeBase;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface KnowledgeBaseMapper extends BaseMapper<KnowledgeBase> {
}
