package cn.bctools.custom.mapper;

import cn.bctools.custom.entity.data.ResourceAuditLogs;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 审核日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface ResourceAuditLogsMapper extends BaseMapper<ResourceAuditLogs> {

}
