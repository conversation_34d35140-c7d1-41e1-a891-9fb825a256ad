<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.custom.mapper.NfdjtInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.custom.entity.data.NfdjtInfo">
        <id column="id" property="id" />
        <result column="activity_id" property="activityId" />
        <result column="type" property="type" />
        <result column="way" property="way" />
        <result column="period" property="period" />
        <result column="subject" property="subject" />
        <result column="grade" property="grade" />
        <result column="activity_type" property="activityType" />
        <result column="is_banner" property="isBanner" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, activity_id, type, way, period, subject, grade, activity_type, is_banner
    </sql>

</mapper>
