<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.custom.mapper.TranscodingTasksMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.custom.entity.data.TranscodingTasks">
        <id column="id" property="id" />
        <result column="resource_id" property="resourceId" />
        <result column="status" property="status" />
        <result column="try_times" property="tryTimes" />
        <result column="tried_times" property="triedTimes" />
        <result column="start_time" property="startTime" />
        <result column="dpi" property="dpi" />
        <result column="fps" property="fps" />
        <result column="worker" property="worker" />
        <result column="vinfo" property="vinfo" />
        <result column="type" property="type" />
        <result column="error_info" property="errorInfo" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_by_id" property="createById" />
        <result column="update_by_id" property="updateById" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, resource_id, status, try_times, tried_times, start_time, dpi, fps, worker, vinfo, type, error_info, create_by, update_by, create_by_id, update_by_id, create_time, update_time
    </sql>

</mapper>
