package cn.bctools.custom.service;

import cn.bctools.custom.entity.data.ResourceAuditLogs;
import cn.bctools.custom.entity.vo.auditlog.ShareAuditLogPageVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 审核日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
public interface ResourceAuditLogsService extends IService<ResourceAuditLogs> {

    void getPage(Page<ResourceAuditLogs> page, ShareAuditLogPageVo logPageVo);
}
