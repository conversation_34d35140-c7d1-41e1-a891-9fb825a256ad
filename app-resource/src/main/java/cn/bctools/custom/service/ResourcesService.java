package cn.bctools.custom.service;

import cn.bctools.custom.entity.data.Resources;
import cn.bctools.custom.entity.data.Topics;
import cn.bctools.custom.entity.vo.BaseSelectVo;
import cn.bctools.custom.entity.vo.resource.ResourceDetailVo;
import cn.bctools.custom.entity.vo.resource.ResourceEditVo;
import cn.bctools.custom.entity.vo.resource.ResourceUploadVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 资源文件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface ResourcesService extends IService<Resources> {

    void uploadMine(ResourceUploadVo resourceVo);

    ResourceDetailVo getInfo(String resourceId);


    void edit(ResourceEditVo resourceEditVo);

    void shareCenter(String resourceId);

    void getPage(Page<Topics> page, BaseSelectVo pageVo);

    Resources findById(String resourceId);
}
