package cn.bctools.custom.service;

import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.custom.entity.ImportFeedback;
import cn.bctools.custom.entity.data.SourceTypes;
import cn.bctools.custom.entity.vo.sourceType.SourceTypeListReVo;
import cn.bctools.custom.entity.vo.sourceType.SourceTypeRtnVo;
import cn.bctools.custom.entity.vo.sourceType.SourceTypeVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

public interface SourceTypesService extends IService<SourceTypes> {

    Page<SourceTypeRtnVo> getSourceTypeList(Page<SourceTypes> page, SourceTypeListReVo sourceTypeListReVo);

    SourceTypeRtnVo getSourceType(String sourceTypeId);
    void addSourceType(SourceTypeVo sourceTypeVo);
    void editSourceType(SourceTypeVo sourceTypeVo);

    /**
     * 下载模板
     *
     * @param response
     */
    void downloadExcelTemplate(HttpServletResponse response);

    ImportFeedback importSourceTypeExcel(UserDto userDto, MultipartFile file);

    void exportExcel(HttpServletResponse response, SourceTypeListReVo sourceTypeListReVo);


}
