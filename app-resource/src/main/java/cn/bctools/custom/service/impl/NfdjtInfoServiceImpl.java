package cn.bctools.custom.service.impl;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.custom.entity.data.NfdjtInfo;
import cn.bctools.custom.entity.vo.nfdjt.NfdjtDetailVo;
import cn.bctools.custom.mapper.NfdjtInfoMapper;
import cn.bctools.custom.service.NfdjtInfoService;
import cn.bctools.design.use.api.DataModelApi;
import cn.bctools.design.use.api.dto.DataModelSearchDto;
import cn.bctools.design.use.api.enums.DataModelQueryType;
import cn.bctools.document.entity.DcLibrary;
import cn.bctools.document.entity.DcLibraryLog;
import cn.bctools.document.entity.enums.DcLibraryLogOperationTypeEnum;
import cn.bctools.document.entity.enums.DcLibraryTypeEnum;
import cn.bctools.document.service.DcLibraryLogService;
import cn.bctools.document.service.DcLibraryService;
import cn.bctools.oss.template.OssTemplate;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 南方大讲堂信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class NfdjtInfoServiceImpl extends ServiceImpl<NfdjtInfoMapper, NfdjtInfo> implements NfdjtInfoService {

    private static final String NFDJT_DETAIL_INFO = "1789954946847838209";
    private static final String NFDJT_ACTIVITY_AGENDAS = "1796382321521299458";
    private static final String NFDJT_JOIN_METHOD = "1792384816026587138";
    private static final String NFDJT_EXPERT = "1789861834947399682";
    private static final String NFDJT_LIVE_REPLAY = "1802642368237715458";
    private final DataModelApi dataModelApi;
    private final OssTemplate ossTemplate;

    @Override
    public NfdjtDetailVo getDetailInfo(String activityId){
        //获取直播管理基本信息
        Map<String, Object> nfdjtDetailMap = dataModelApi.getMap(NFDJT_DETAIL_INFO, activityId,null);
        NfdjtDetailVo nfdjtDetailVo = BeanCopyUtil.copy(nfdjtDetailMap, NfdjtDetailVo.class);

        List<Map<String, Object>> activityAgendasMap = getDataMapList(activityId,NFDJT_ACTIVITY_AGENDAS);
        //活动议程信息
        List<NfdjtDetailVo.ActivityAgendas> activityAgendasList = BeanCopyUtil.copys(activityAgendasMap, NfdjtDetailVo.ActivityAgendas.class);
        if(CollectionUtils.isNotEmpty(activityAgendasList)){
            nfdjtDetailVo.setActivityAgendasList(activityAgendasList);
        }
        List<Map<String, Object>> expertMap = getDataMapList(activityId, NFDJT_EXPERT);
        //专家信息
        List<NfdjtDetailVo.Expert> expertList = BeanCopyUtil.copys(expertMap, NfdjtDetailVo.Expert.class);
        if(CollectionUtils.isNotEmpty(expertList)){
            nfdjtDetailVo.setExpertList(expertList);
        }
        //参与方式
        List<Map<String, Object>> joinMethodMap = getDataMapList(activityId, NFDJT_JOIN_METHOD);
        List<NfdjtDetailVo.JoinMethod> joinMethodList = BeanCopyUtil.copys(joinMethodMap, NfdjtDetailVo.JoinMethod.class);
        if(CollectionUtils.isNotEmpty(joinMethodList)){
            nfdjtDetailVo.setJoinMethodList(joinMethodList);
        }
        //活动资源
        DcLibraryService dcLibraryService = SpringContextUtil.getBean(DcLibraryService.class);
        List<DcLibrary> dcLibraryList = dcLibraryService.list(new LambdaQueryWrapper<DcLibrary>().eq(DcLibrary::getParentId, activityId).eq(DcLibrary::getShelfStatus, true)
                .notIn(DcLibrary::getType, Arrays.asList(DcLibraryTypeEnum.knowledge, DcLibraryTypeEnum.directory)));
        if(!CollectionUtils.isEmpty(dcLibraryList)){
            dcLibraryList.parallelStream().peek(e -> {
                e.setFileLink(ossTemplate.fileLink(e.getFilePath(), e.getBucketName()));
            }).collect(Collectors.toList());
            nfdjtDetailVo.setDcLibraryList(dcLibraryList);
        }
        //浏览量
        DcLibrary dcLibrary = dcLibraryService.getById(activityId);
        nfdjtDetailVo.setViewNum(dcLibrary.getViewNum());
        return nfdjtDetailVo;
    }

    private List<Map<String, Object>> getDataMapList(String activityId,String modelId) {
        DataModelSearchDto dataModelSearchDto = new DataModelSearchDto();
        dataModelSearchDto.setId(modelId);
        dataModelSearchDto.setSize(0);
        dataModelSearchDto.setCurrent(0);

        DataModelSearchDto.SearchGroup searchGroup = buildActivityIdGroup(activityId);
        dataModelSearchDto.setGroup(Collections.singletonList(searchGroup));

//        if (CollectionUtils.isNotEmpty(fieldKeyList)) {
//            // 指定查询字段
//            String[] fields = new String[fieldKeyList.size()];
//            query.fields().include(fieldKeyList.toArray(fields));
//        }
        List<Map<String, Object>> activityAgendasMap = dataModelApi.searchWithEcho(dataModelSearchDto).getData();
        return activityAgendasMap;
    }

    private DataModelSearchDto.SearchGroup buildActivityIdGroup(String activityId) {
        DataModelSearchDto.SearchGroup group = new DataModelSearchDto.SearchGroup();
        DataModelSearchDto.SearchItem item = new DataModelSearchDto.SearchItem();
        group.setItems(new ArrayList<>());
        item.setKey("activity_id");
        item.setValue(activityId);
        item.setType("字符串");
        item.setQueryType(DataModelQueryType.eq);
        item.setAndOr(false);
        group.getItems().add(item);
        group.setAndOr(false);
        return group;
    }

}
