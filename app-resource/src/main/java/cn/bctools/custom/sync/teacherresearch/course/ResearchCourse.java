package cn.bctools.custom.sync.teacherresearch.course;

import cn.bctools.oss.dto.BaseFile;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 共同体详情表
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "course", autoResultMap = true)
@ApiModel(value = "Course对象", description = "共同体详情表")
public class ResearchCourse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键，共同体id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "负责人")
    @TableField("leader")
    private String leader;

    @ApiModelProperty(value = "负责人id")
    @TableField("leader_id")
    private String leaderId;

    @ApiModelProperty(value = "共同体名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "研修主题")
    @TableField("theme")
    private String theme;

    @ApiModelProperty(value = "开始时间")
    @TableField("activity_start_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityStartTime;

    @ApiModelProperty(value = "结束时间")
    @TableField("activity_end_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityEndTime;

    @ApiModelProperty(value = "开始时间")
    @TableField("enroll_start_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enrollStartTime;

    @ApiModelProperty(value = "结束时间")
    @TableField("enroll_end_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enrollEndTime;

    @ApiModelProperty(value = "年份")
    @TableField("year")
    private String year;

    @ApiModelProperty(value = "封面信息")
    @TableField(value = "cover_img",typeHandler = JacksonTypeHandler.class)
    private List<BaseFile> coverImg;

    @ApiModelProperty(value = "封面信息")
    @TableField(exist = false)
    private String cover;

    @ApiModelProperty(value = "附件信息")
    @TableField(value = "attachments",typeHandler = JacksonTypeHandler.class)
    private List<BaseFile> attachments;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人")
    @TableField("create_by")
    private String createBy;

    @ApiModelProperty(value = "创建人ID")
    @TableField("create_by_id")
    private String createById;

    @ApiModelProperty(value = "更新人")
    @TableField("update_by")
    private String updateBy;

    @ApiModelProperty(value = "更新人ID")
    @TableField("update_by_id")
    private String updateById;

    @ApiModelProperty(value = "删除标识（0-正常,1-删除）")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;

    @ApiModelProperty(value = "租户id")
    @TableField("tenant_id")
    private String tenantId;

}
