package cn.bctools.custom.sync.teacherresearch.course;

import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ResearchCourseService {

    List<ResearchCourse> selectPagedCourses(@Param("lastUpdateTime") LocalDateTime lastUpdateTime, @Param("offset") int offset, @Param("pageSize") int pageSize);


    ResearchCoursePerAndSubVo getCoursePeriodAndSubject(String courseId);

    String getCourseYear(String courseId);

}
