package cn.bctools.custom.sync.teachertraining.course;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.mapper.CourseMapper;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
@DS("training")
public class TrainingCourseServiceImpl extends ServiceImpl<CourseMapper, TrainingCourse> implements TrainingCourseService {

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Override
    public  List<TrainingCourse> selectPagedCourses(LocalDateTime lastUpdateTime, int offset, int pageSize) {

        String sql = "SELECT id, name, year, update_time, del_flag, create_time, create_by, create_by_id, update_time, update_by, update_by_id,leader_id,leader,cover_img as cover " +
                "FROM `teacher-training`.course WHERE update_time > ? ORDER BY update_time LIMIT ?, ? ";
        return BeanCopyUtil.copys(jdbcTemplate.queryForList(sql, lastUpdateTime, offset, pageSize), TrainingCourse.class);
//        return baseMapper.selectPagedCourses(lastUpdateTime,offset,pageSize);
    }

    @Override
    public TrainingCoursePerAndSubVo getCoursePeriodAndSubject(String courseId) {
        String sql ="SELECT period_id,subject_id FROM `teacher-training`.course_period_subjects WHERE course_id= ? LIMIT 1";
        Map<String, Object> stringObjectMap = jdbcTemplate.queryForMap(sql, courseId);
        if (ObjectNull.isNull(stringObjectMap)){
            return null;
        }
        return BeanCopyUtil.copy(stringObjectMap, TrainingCoursePerAndSubVo.class);
    }

    @Override
    public String getCourseYear(String courseId) {
        String sql ="SELECT year FROM `teacher-training`.course WHERE id= ? LIMIT 1";
        Map<String, Object> stringObjectMap = jdbcTemplate.queryForMap(sql, courseId);
        if (ObjectNull.isNull(stringObjectMap)){
            return "";
        }
        return stringObjectMap.get("year").toString();
    }
}
