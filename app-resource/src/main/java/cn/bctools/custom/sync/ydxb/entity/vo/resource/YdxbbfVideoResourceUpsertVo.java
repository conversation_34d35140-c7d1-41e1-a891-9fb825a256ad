package cn.bctools.custom.sync.ydxb.entity.vo.resource;

import cn.bctools.custom.entity.data.ResourceLibrary;
import cn.bctools.document.entity.DcLibrary;
import cn.bctools.document.po.DocumentEsPo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> 2
 */
@Accessors(chain = true)
@Data
public class YdxbbfVideoResourceUpsertVo {
    private List<DcLibrary> dcLibraries;

    private List<ResourceLibrary> resourceLibraries;

    private List<DocumentEsPo> esInfos;

    private List<String> delResourceIds;
}
