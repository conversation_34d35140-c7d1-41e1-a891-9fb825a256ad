package cn.bctools.custom.sync.ydxb.handler;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.custom.sync.util.UpsertUtil;
import cn.bctools.custom.sync.ydxb.entity.vo.activity.YdxbbfActivity;
import cn.bctools.custom.sync.ydxb.service.YdxbServiceImpl;
import cn.bctools.redis.utils.RedisUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class SyncYdxbbfActivityInfoHandler {

    private final RedisUtils redisUtils;

    @Autowired
    MongoTemplate mongoTemplate;

    private final YdxbServiceImpl ydxbService;

    private static final int PAGE_SIZE = 500;

    private static final String LAST_UPDATE_TIME_KEY = "jvs:app-resource:ydxbbf:activity:updateTime";

    private static final String DATA_MODEL_NAME = "1801511978264530945";

    private static final String DEL_LAST_UPDATE_TIME_KEY = "jvs:app-resource:ydxbbf:del_activity:updateTime";

    private static final String DEL_DATA_MODEL_NAME = "del_1801511978264530945";

    @XxlJob("resource-sync-ydxbbf-activity-info")
    public void syncYdxbbfActivityInfo() {
        XxlJobHelper.log("定时任务【同步粤东西北帮扶活动信息】开始执行");
        //构造查询条件
        Query query = UpsertUtil.syncDefaultQuery(redisUtils.getTimeValue(LAST_UPDATE_TIME_KEY));
        //审批已通过/管理员创建
        query.addCriteria(Criteria.where("flowTaskState").in("1",1));
        LocalDateTime now = LocalDateTime.now().minusSeconds(5);
        int skip = 0;

        while (true) {
            query.skip(skip);
            List<Map> data = mongoTemplate.find(query, Map.class, DATA_MODEL_NAME);
            List<YdxbbfActivity> activities = BeanCopyUtil.copys(data, YdxbbfActivity.class);

            if (activities.isEmpty()) {
                break;
            }
            try {
                ydxbService.handleActivityInfo(activities);
                redisUtils.setTimeValue(LAST_UPDATE_TIME_KEY, activities.get(activities.size() - 1).getUpdateTime());
            } catch (Exception e) {
                XxlJobHelper.log("同步粤东西北帮扶活动信息出错：{}", e.getMessage());
                throw e;
            }

            XxlJobHelper.log("同步粤东西北帮扶活动信息成功，同步：{} 条数据", activities.size());
            skip += PAGE_SIZE;
        }

        XxlJobHelper.log("定时任务【同步粤东西北帮扶活动信息成功】结束，最后更新时间{}", DateUtil.format(now, DatePattern.NORM_DATETIME_PATTERN));
        // 更新同步时间为当前时间
        redisUtils.setTimeValue(LAST_UPDATE_TIME_KEY, now);
    }


    @XxlJob("resource-sync-ydxbbf-del-activity-info")
    @Transactional(rollbackFor = Exception.class)
    public void syncDelActivityInfo() {
        XxlJobHelper.log("定时任务【同步删除粤东西北帮扶活动信息】开始执行");
        Query query = UpsertUtil.syncDefaultQuery(redisUtils.getTimeValue(DEL_LAST_UPDATE_TIME_KEY));
        int skip = 0;
        LocalDateTime now = LocalDateTime.now().minusSeconds(5);

        while (true) {
            query.skip(skip);
            List<Map> maps = mongoTemplate.find(query, Map.class, DEL_DATA_MODEL_NAME);
            if (CollectionUtil.isEmpty(maps)) {
                break;
            }
            Set<String> ids = maps.stream().map(e -> (String) e.get("id")).collect(Collectors.toSet());
            ydxbService.delActivities(ids);

            XxlJobHelper.log("同步删除粤东西北帮扶活动信息成功，删除：{} 条数据", ids.size());
            skip += PAGE_SIZE;
        }

        XxlJobHelper.log("定时任务【同步删除粤东西北帮扶活动信息成功】结束，最后更新时间{}", DateUtil.format(now, DatePattern.NORM_DATETIME_PATTERN));
        // 更新同步时间为当前时间
        redisUtils.setTimeValue(DEL_LAST_UPDATE_TIME_KEY, now);
    }
}