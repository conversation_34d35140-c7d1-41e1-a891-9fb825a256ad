package cn.bctools.custom.sync.ydxb.handler;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.custom.sync.util.UpsertUtil;
import cn.bctools.custom.sync.ydxb.entity.vo.resource.YdxbbfHelpResource;
import cn.bctools.custom.sync.ydxb.entity.vo.resource.YdxbbfVideoResource;
import cn.bctools.custom.sync.ydxb.service.YdxbServiceImpl;
import cn.bctools.redis.utils.RedisUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class SyncYdxbbfResourceInfoHandler {

    private final RedisUtils redisUtils;

    private final YdxbServiceImpl ydxbService;

    @Autowired
    MongoTemplate mongoTemplate;

    private static final int PAGE_SIZE = 500;

    private static final String HELP_LAST_UPDATE_TIME_KEY = "jvs:app-resource:ydxbbf:resource:help:updateTime";

    private static final String VIDEO_LAST_UPDATE_TIME_KEY = "jvs:app-resource:ydxbbf:resource:video:updateTime";

    private static final String HELP_RESOURCE = "1802597091871080449";

    private static final String VIDEO_RESOURCE = "1804403280988651522";

    private static final String DEL_HELP_LAST_UPDATE_TIME_KEY = "jvs:app-resource:ydxbbf:resource:del_help:updateTime";

    private static final String DEL_VIDEO_LAST_UPDATE_TIME_KEY = "jvs:app-resource:ydxbbf:resource:del_video:updateTime";

    private static final String DEL_HELP_RESOURCE = "del_1802597091871080449";

    private static final String DEL_VIDEO_RESOURCE = "del_1804403280988651522";

    @XxlJob("resource-sync-ydxb-help-resource-info")
    public void syncHelpResourceInfo() {
        XxlJobHelper.log("定时任务【资源平台同步粤东西北帮扶帮扶资源信息】开始执行");
        //TODO 同步内容到ES需要处理文件内Content吗？
        //构造查询条件
        Query query = UpsertUtil.syncDefaultQuery(redisUtils.getTimeValue(HELP_LAST_UPDATE_TIME_KEY));
        LocalDateTime now = LocalDateTime.now().minusSeconds(5);
        int skip = 0;

        while (true) {
            query.skip(skip);
            List<Map> data = mongoTemplate.find(query, Map.class, HELP_RESOURCE);
            List<YdxbbfHelpResource> resources = BeanCopyUtil.copys(data, YdxbbfHelpResource.class);
            if (resources.isEmpty()) {
                break;
            }
            try {
                ydxbService.handleHelpResource(resources);
                redisUtils.setTimeValue(HELP_LAST_UPDATE_TIME_KEY, resources.get(resources.size() - 1).getUpdateTime());
            } catch (Exception e) {
                XxlJobHelper.log("资源平台同步粤东西北帮扶帮扶资源信息出错：{}", e.getMessage());
                throw e;
            }

            XxlJobHelper.log("资源平台同步粤东西北帮扶帮扶资源信息，同步：{} 条数据", resources.size());
            skip += PAGE_SIZE;

        }
        XxlJobHelper.log("定时任务【资源平台同步粤东西北帮扶帮扶资源信息】结束，最后更新时间{}", DateUtil.format(now, DatePattern.NORM_DATETIME_PATTERN));
        // 更新同步时间为当前时间
        redisUtils.setTimeValue(HELP_LAST_UPDATE_TIME_KEY, now);
    }


    @XxlJob("resource-sync-ydxb-video-resource-info")
    public void syncVideoResourceInfo() {
        XxlJobHelper.log("定时任务【资源平台同步粤东西北帮扶帮扶视频资源信息】开始执行");
        //TODO 同步内容到ES需要处理文件内Content吗？
        //构造查询条件
        Query query = UpsertUtil.syncDefaultQuery(redisUtils.getTimeValue(VIDEO_LAST_UPDATE_TIME_KEY));
        LocalDateTime now = LocalDateTime.now().minusSeconds(5);
        int skip = 0;

        while (true) {
            query.skip(skip);
            List<Map> data = mongoTemplate.find(query, Map.class, VIDEO_RESOURCE);
            List<YdxbbfVideoResource> resources = BeanCopyUtil.copys(data, YdxbbfVideoResource.class);
            if (resources.isEmpty()) {
                break;
            }
            ;
            try {
                ydxbService.handleVideoResource(resources);
                redisUtils.setTimeValue(VIDEO_LAST_UPDATE_TIME_KEY, resources.get(resources.size() - 1).getUpdateTime());
            } catch (Exception e) {
                XxlJobHelper.log("资源平台同步粤东西北帮扶帮扶视频资源信息出错：{}", e.getMessage());
                throw e;
            }

            XxlJobHelper.log("资源平台同步粤东西北帮扶帮扶视频资源信息，同步：{} 条数据", resources.size());
            skip += PAGE_SIZE;

        }
        XxlJobHelper.log("定时任务【资源平台同步粤东西北帮扶帮扶视频资源信息】结束，最后更新时间{}", DateUtil.format(now, DatePattern.NORM_DATETIME_PATTERN));
        // 更新同步时间为当前时间
        redisUtils.setTimeValue(VIDEO_LAST_UPDATE_TIME_KEY, now);
    }

    @XxlJob("resource-sync-ydxbbf-del-help-resource-info")
    @Transactional(rollbackFor = Exception.class)
    public void syncDelHelpResourceInfo() {

        XxlJobHelper.log("定时任务【同步删除粤东西北帮扶帮扶资源信息】开始执行");
        Query query = UpsertUtil.syncDefaultQuery(redisUtils.getTimeValue(VIDEO_LAST_UPDATE_TIME_KEY));
        LocalDateTime now = LocalDateTime.now().minusSeconds(5);
        int skip = 0;

        while (true) {
            query.skip(skip);
            List<Map> maps = mongoTemplate.find(query, Map.class, DEL_HELP_RESOURCE);
            if (CollectionUtil.isEmpty(maps)) {
                break;
            }
            Set<String> ids = maps.stream().map(e -> (String) e.get("id")).collect(Collectors.toSet());
            ydxbService.delResources(ids);

            XxlJobHelper.log("同步删除粤东西北帮扶帮扶资源信息，删除：{} 条数据", ids.size());
            skip += PAGE_SIZE;
        }
        XxlJobHelper.log("定时任务【同步删除粤东西北帮扶帮扶资源信息】结束，最后更新时间{}", DateUtil.format(now, DatePattern.NORM_DATETIME_PATTERN));
        // 更新同步时间为当前时间
        redisUtils.setTimeValue(DEL_HELP_LAST_UPDATE_TIME_KEY, now);
    }


    @XxlJob("resource-sync-ydxbbf-del-video-resource-info")
    @Transactional(rollbackFor = Exception.class)
    public void syncDelVideoResourceInfo() {
        XxlJobHelper.log("定时任务【同步删除粤东西北帮扶帮扶视频资源信息】开始执行");
        Query query = UpsertUtil.syncDefaultQuery(redisUtils.getTimeValue(VIDEO_LAST_UPDATE_TIME_KEY));
        LocalDateTime now = LocalDateTime.now().minusSeconds(5);
        int skip = 0;

        while (true) {
            query.skip(skip);
            List<Map> maps = mongoTemplate.find(query, Map.class, DEL_VIDEO_RESOURCE);
            if (CollectionUtil.isEmpty(maps)) {
                break;
            }
            Set<String> ids = maps.stream().map(e -> (String) e.get("id")).collect(Collectors.toSet());
            ydxbService.delResources(ids);

            XxlJobHelper.log("同步删除粤东西北帮扶帮扶视频资源信息，删除：{} 条数据", ids.size());
            skip += PAGE_SIZE;
        }

        XxlJobHelper.log("定时任务【同步删除粤东西北帮扶帮扶视频资源信息】结束，最后更新时间{}", DateUtil.format(now, DatePattern.NORM_DATETIME_PATTERN));
        // 更新同步时间为当前时间
        redisUtils.setTimeValue(DEL_VIDEO_LAST_UPDATE_TIME_KEY, now);
    }

}
