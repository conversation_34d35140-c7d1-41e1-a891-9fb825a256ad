package cn.bctools.custom.template.textbookVersion;

import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.custom.entity.data.TextBookVersion;

import cn.bctools.custom.service.TextBookVersionService;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * @Author: csh
 * @Description: 导入机构Excel解析处理
 */
public class ImportListener implements ReadListener<TextBookVersionExcelTemplate> {

    /**
     * 用户信息
     */
    private UserDto userDto;

    private final TextBookVersionService textBookVersionService = SpringContextUtil.getBean(TextBookVersionService.class);

    public ImportListener(UserDto userDto) {
        this.userDto = userDto;
    }

    /**
     * 单次缓存的数据量
     */
    private static final int BATCH_COUNT = 1000;

    /**
     * 缓存数据
     */
    private List<TextBookVersionExcelTemplate> excelDatas = new ArrayList<>();

    /**
     * 导入反馈
     */
    private List<TextBookVersionExcelTemplate> importFeedbackList = new ArrayList<>();

    /**
     * 错误记录
     */
    private List<String> errorMsgList = new ArrayList<>();

    /**
     * 导入成功数、导入失败数
     */
    private int successNum = 0;
    private int failNum = 0;

    /**
     * 需要自动创建的数据
     */
    private List<TextBookVersion> addDatas = new ArrayList<>();

    private String errorMsg;
    /**
     * 在转换异常 获取其他异常下会调用本接口。抛出异常则停止读取。如果这里不抛出异常则 继续读取下一行
     *
     * @param e
     * @param analysisContext
     * @throws Exception
     */
    @Override
    public void onException(Exception e, AnalysisContext analysisContext) throws Exception {

    }

    /**
     * 这里会一行行的返回头
     *
     * @param map
     * @param analysisContext
     */
    @Override
    public void invokeHead(Map<Integer, CellData> map, AnalysisContext analysisContext) {
        // 不处理头
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data
     * @param context
     */
    @Override
    public void invoke(TextBookVersionExcelTemplate data, AnalysisContext context) {
        ReadRowHolder readRowHolder = context.readRowHolder();
        Integer rowIndex = readRowHolder.getRowIndex();

        // 校验数据
        List<String> checkList = checkData(data);

        if (CollectionUtils.isEmpty(checkList)) {
            // 记录成功数
            successNum++;
            data.setRowIndex(rowIndex);
            data.setFeedback("成功！");
            // 加入缓存
            excelDatas.add(data);
        } else {
            // 记录失败数
            failNum++ ;
            data.setRowIndex(rowIndex);
            data.setFeedback("失败！" + checkList.toString().replace("[","").replace("]",""));
            // 记录失败行信息
            errorMsgList.add("第" + data.getRowIndex() + "行：" + checkList.toString().replace("[","").replace("]",""));
        }

        data.setSuccessNum(successNum);
        data.setFailNum(failNum);
        // 添加导入反馈
        importFeedbackList.add(data);

        // 分批处理
        if (excelDatas.size() >= BATCH_COUNT) {
            // 清理缓存
            clear();
            convertData();
            // 当前批次处理完毕，清空缓存
            excelDatas = new ArrayList<>();
        }
    }

    /**
     * 额外信息
     *
     * @param cellExtra
     * @param analysisContext
     */
    @Override
    public void extra(CellExtra cellExtra, AnalysisContext analysisContext) {
        // 不处理额外信息
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        excelDatas.sort(Comparator.comparing(TextBookVersionExcelTemplate::getRowIndex));
        // 清理缓存
        clear();
        convertData();
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        return true;
    }

    /**
     * 数据校验
     *
     * @param data
     */
    private List<String> checkData(TextBookVersionExcelTemplate data) {
        List<String> countErrorMsg = new ArrayList<>();
        if (StringUtils.isBlank(data.getName())) {
            errorMsg = "教材版本名称不能为空";
            countErrorMsg.add(errorMsg);
        } else {
            if (textBookVersionService.count(new LambdaQueryWrapper<TextBookVersion>().eq(TextBookVersion::getName, data.getName())) > 0) {
                errorMsg = "教材版本名称不能重复";
                countErrorMsg.add(errorMsg);
            }
        }
        return countErrorMsg;
    }

    /**
     * 清理缓存
     */
    private void clear() {
        // 清理新增的部门信息
        addDatas = new ArrayList<>();
    }

    /**
     * 数据转换
     */
    private void convertData() {
        if (CollectionUtils.isEmpty(excelDatas)) {
            return;
        }
        for (TextBookVersionExcelTemplate data : excelDatas) {
            TextBookVersion textBookVersion = convertExcelData(data);
            addDatas.add(textBookVersion);
        }

        // 保存
        save();
    }

    private TextBookVersion convertExcelData(TextBookVersionExcelTemplate data) {

        TextBookVersion textBookVersion = new TextBookVersion();
        textBookVersion.setName(data.getName());
        textBookVersion.setDescription(data.getDescription());
        textBookVersion.setCreateBy(userDto.getRealName());
        textBookVersion.setCreateById(userDto.getId());
        textBookVersion.setTenantId(userDto.getTenantId());

        return textBookVersion;

    }

    /**
     * 保存
     */
    private void save() {
        // 保存部门信息
        saveData();
    }

    /**
     * 保存
     */
    private void saveData() {
        if (CollectionUtils.isEmpty(addDatas)) {
            return;
        }
        List<TextBookVersion> textBookVersions = BeanCopyUtil.copys(addDatas, TextBookVersion.class);
        textBookVersionService.saveBatch(textBookVersions);
    }

    /**
     * 返回导入反馈
     */
    public List<TextBookVersionExcelTemplate> getFeedbackList() {
        return importFeedbackList;
    }

    /**
     * 返回错误信息
     */
    public List<String> getErrorMsgList() {
        return errorMsgList;
    }
}
