package cn.bctools.document.index.impl;

import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.index.design.component.service.ComponentBaseService;
import cn.bctools.index.design.enums.ComponentType;
import cn.bctools.index.design.service.ComponentLoadService;
import cn.bctools.index.dto.FormQueryParamsDto;
import cn.bctools.index.dto.OptionsBase;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class ComponentLoadServiceImpl implements ComponentLoadService {

    @Override
    public OptionsBase load(String type, List<FormQueryParamsDto> queryParams, Map<String, Object> metaData) {
        ComponentType homeType = ComponentType.valueOf(type);
        if(ObjectUtil.isNull(homeType)){
            return null;
        }
        Class<? extends ComponentBaseService> aClass = homeType.getAClass();
        if(aClass==null){
            return null;
        }
        return SpringContextUtil.getBean(aClass).fillData(queryParams,metaData);
    }
}
