package cn.bctools.document.service.impl;

import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.document.entity.DcLibrary;
import cn.bctools.document.entity.DcLibraryCollect;
import cn.bctools.document.entity.DcLibraryLog;
import cn.bctools.document.entity.enums.DcLibraryLogOperationTypeEnum;
import cn.bctools.document.mapper.DcLibraryLogMapper;
import cn.bctools.document.service.DcLibraryLogService;
import cn.bctools.document.service.DcLibraryService;
import cn.bctools.oauth2.utils.AuthorityManagementUtils;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR> Generator
 */
@AllArgsConstructor
@Service
public class DcLibraryLogServiceImpl extends ServiceImpl<DcLibraryLogMapper, DcLibraryLog> implements DcLibraryLogService {

    @Override
    public List<DcLibraryLog> frequently(DcLibraryLog dcLibraryLog) {
        LambdaQueryWrapper<DcLibraryLog> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotEmpty(dcLibraryLog.getOperationType())) {
            queryWrapper.eq(DcLibraryLog::getOperationType, dcLibraryLog.getOperationType());
        }
        queryWrapper.eq(DcLibraryLog::getUserId, UserCurrentUtils.getUserId())
                .orderByDesc(DcLibraryLog::getCreateTime)
                .groupBy(DcLibraryLog::getDcLibraryId)
                .last("limit 20");
        return this.list(queryWrapper);
    }

    @Override
    public List<String> getDownLoad() {
        String userId = UserCurrentUtils.getUserId();
        List<DcLibraryLog> list = this.list(Wrappers.<DcLibraryLog>lambdaQuery()
                .select(DcLibraryLog::getDcLibraryId, DcLibraryLog::getCreateTime)
                .eq(DcLibraryLog::getUserId, userId).eq(DcLibraryLog::getOperationType, DcLibraryLogOperationTypeEnum.DOWNLOAD)
                .orderByDesc(DcLibraryLog::getCreateTime));
        if (ObjectUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(DcLibraryLog::getDcLibraryId).collect(Collectors.toList());
    }

    @Override
    public Dict groupByCount(String id) {
        QueryWrapper<DcLibraryLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("operation_type as operationType,count(type) as count");
        Dict set = Dict.create();
        if (!"-1".equals(id)) {
            DcLibraryService dcLibraryService = SpringContextUtil.getBean(DcLibraryService.class);
            queryWrapper.eq("knowledge_id", id).or().eq("dc_library_id", id);
            DcLibrary dcLibrary = dcLibraryService.getById(id);
            //获取创建人信息
            UserDto userDto = AuthorityManagementUtils.getUserById(dcLibrary.getCreateById());
            set.set("userDto", userDto);
        }
        queryWrapper.and(e -> e.eq("operation_type", DcLibraryLogOperationTypeEnum.DOWNLOAD).or().eq("operation_type", DcLibraryLogOperationTypeEnum.SEE))
                .groupBy("operation_type");
        List<Map<String, Object>> list = this.listMaps(queryWrapper);
        list.forEach(e -> {
            String operationType = (String) e.get("operationType");
            String name = Objects.requireNonNull(DcLibraryLogOperationTypeEnum.get(operationType)).name();
            set.put(name, e.get("count"));
        });
        return set;
    }
}
