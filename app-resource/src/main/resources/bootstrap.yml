server:
  port: ${random.int[50001,50099]}
version: @project.version@
spring:
  application:
    name: @project.artifactId@
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        server-addr: http://jvs-nacos:8848
        group: jvs
        namespace: ${spring.cloud.nacos.discovery.group}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        group: ${spring.cloud.nacos.discovery.group}
        namespace: ${spring.cloud.nacos.discovery.group}
    inetutils:
      #选择使用此网段进行处理
      preferred-networks: 10.*
  config:
    import:
      #公共配置
      - optional:nacos:application.yml
      #公共配置项目配置
      - optional:nacos:${spring.application.name}.yml
      #父级配置
      - optional:nacos:${project.parent.artifactId}.yml
      #授权配置
      - optional:nacos:license.yml

swagger:
  title: "资源平台模块"
  description: "资源平台模块，用于提供资源接口"
mybatis-plus:
  mapper-locations: classpath*:cn/bctools/custom/mapper/xml/*Mapper.xml
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

transcode:
  workFolder: /var/app-resource/transcode
  ffmpegPath: /usr/bin/ffmpeg
  ffprobePath: /usr/bin/ffprobe
  bucketName: jvs-public
  tryTimes: 1
  hlsTimeInterval: 10
