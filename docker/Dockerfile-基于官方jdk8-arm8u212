FROM registry.cn-shenzhen.aliyuncs.com/edf/jdk8-arm64:8u212-jre-alpine
MAINTAINER edf

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone

ADD jvs-design/jvs-design-mgr/fonts /usr/share/fonts/zh_CN
RUN chmod 775 /usr/share/fonts/zh_CN
RUN apk add --no-cache fontconfig

ENV LANG C.UTF-8
ENV SPRING_PROFILES_ACTIVE=""
ENV JAVA_OPTS=""

CMD java -Dspring.profiles.active=${SPRING_PROFILES_ACTIVE} $JAVA_OPTS -jar /app/app.jar
