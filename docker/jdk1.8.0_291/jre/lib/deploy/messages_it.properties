#
# Copyright (c) 2004, 2011, Oracle and/or its affiliates. All rights reserved.
# ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
#

error.internal.badmsg=errore interno, messaggio sconosciuto
error.badinst.nojre=Installazione errata. Impossibile trovare il JRE nel file di configurazione
error.launch.execv=Errore durante la chiamata di Java Web Start (execv)
error.launch.sysexec=Errore durante la chiamata di Java Web Start (SysExec) 
error.listener.failed=Apertura: sysCreateListenerSocket non riuscito
error.accept.failed=Apertura: accept non riuscito
error.recv.failed=Apertura: recv non riuscito
error.invalid.port=Apertura: impossibile identificare una porta valida
error.read=Tentativo di lettura dopo la fine del buffer
error.xmlparsing=Errore durante l'analisi XML: trovato un tipo di token errato
error.splash.exit=Uscita dal processo di schermata iniziale di Java Web Start in corso...\n
# "Last WinSock Error" means the error message for the last operation that failed.
error.winsock=\tErrore ultima operazione WinSock: 
error.winsock.load=Impossibile caricare winsock.dll
error.winsock.start=WSAStartup non riuscito
error.badinst.nohome=Installazione errata: JAVAWS_HOME non impostato 
error.splash.noimage=Apertura: impossibile caricare l'immagine della schermata iniziale
error.splash.socket=Apertura: socket del server non riuscita
error.splash.cmnd=Apertura: comando non riconosciuto
error.splash.port=Apertura: porta non specificata
error.splash.send=Apertura: send non riuscito
error.splash.timer=Apertura: impossibile creare il timer per l'arresto
error.splash.x11.open=Apertura: impossibile aprire il display X11
error.splash.x11.connect=Apertura: connessione X11 non riuscita
# Javaws usage: '\' is a joining of two sentence,which are connected including
# the invisible character '\n'.
message.javaws.usage=\nUso:\tjavaws [opzioni di esecuzione] <file jnlp>\t\n\tjavaws [opzioni di controllo]\t\t\n\ndove le opzioni di esecuzione sono:\t\t\t\n-verbose       \tvisualizza output aggiuntivo\t\n-offline       \tesegue l'applicazione in modalit\u00E0 non in linea\t\n-system        \tesegue l'applicazione solo dalla cache del sistema\n-Xnosplash     \tesegue l'applicazione senza visualizzare la schermata iniziale\t\n-J<opzione>     \tfornisce l'opzione alla VM\t\n-wait          \tavvia il processo Java e ne attende il completamento\t\n\nle opzioni di controllo sono:\t\n-viewer        \tmostra il visualizzatore cache nel pannello di controllo Java\n-clearcache    \trimuove tutte le applicazioni non installate dalla cache\n-uninstall     \trimuove tutte le applicazioni dalla cache\n-uninstall <file jnlp>              \trimuove l'applicazione dalla cache\t\n-import [opzioni di importazione] <file jnlp>\timporta l'applicazione nella cache\t\t\n\nle opzioni di importazione sono:\t\t\t\t\t\t\n-silent        \tesegue l'installazione in background (senza un'interfaccia utente)\t\n-system        \timporta l'applicazione nella cache del sistema\t\n-codebase <url>\trecupera le risorse dal codebase specificato\t\n-shortcut      \tinstalla i collegamenti senza chiedere conferma all'utente\t\n-association   \tinstalla le associazioni senza chiedere conferma all'utente\t\n\n
