/*
 * Copyright (c) 1999, Oracle and/or its affiliates. All rights reserved.
 * ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */

// IDL not generated by rmic, do not edit
// These are all in IDL module CORBA
// The Java classes are in the package org.omg.CORBA
// See ValueType Semantics:Standard Value Box Definitions (5.3) in CORBA 2.3 spec

#ifndef __org_omg_CORBA__
#define __org_omg_CORBA__

#pragma prefix "omg.org"

module CORBA{

    valuetype StringValue string;
    valuetype WStringValue wstring;

};

#include "ir.idl"

#pragma prefix ""

#endif
