<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (c) 1992, 2014, Oracle and/or its affiliates. All rights reserved.
ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
-->

<!DOCTYPE transformations PUBLIC "-//NetBeans//DTD Module Automatic Dependencies 1.0//EN" "http://www.netbeans.org/dtds/module-auto-deps-1_0.dtd">

<transformations version="1.0">
    <transformationgroup>
        <description>openide.jar split into smaller parts. See http://openide.netbeans.org/proposals/arch/modularize.html and use 'ant fix-dependencies' if your project is projectized.</description>
        <exclusion codenamebase="org.openide.loaders" prefix="false"/>
        <!--
        <exclusion codenamebase="org.netbeans.core" prefix="true"/>
        -->
        <transformation>
            <trigger-dependency type="cancel">
                <module-dependency codenamebase="org.openide" major="1" spec="6.0"/>
            </trigger-dependency>
            <implies>
                <result>
                    <module-dependency codenamebase="org.openide.filesystems" spec="6.0"/>
                    <module-dependency codenamebase="org.openide.util" spec="6.0"/>
                    <module-dependency codenamebase="org.openide.util.enumerations" spec="6.0"/>
                    <module-dependency codenamebase="org.openide.modules" spec="6.0"/>
                    <module-dependency codenamebase="org.openide.nodes" spec="6.0"/>
                    <module-dependency codenamebase="org.openide.explorer" spec="6.0"/>
                    <module-dependency codenamebase="org.openide.awt" spec="6.0"/>
                    <module-dependency codenamebase="org.openide.dialogs" spec="6.0"/>
                    <module-dependency codenamebase="org.openide.compat" spec="6.0"/>
                    <module-dependency codenamebase="org.openide.options" spec="6.0"/>
                    <module-dependency codenamebase="org.openide.windows" spec="6.0"/>
                    <module-dependency codenamebase="org.openide.text" spec="6.0"/>
                    <module-dependency codenamebase="org.openide.actions" spec="6.0"/>
                    <!-- better to include this as well, as people sometimes forget to specify dep on loaders -->
                    <module-dependency codenamebase="org.openide.loaders" spec="5.3"/>
                </result>
            </implies>
        </transformation>
    </transformationgroup>

</transformations>
