'\" t
.\" Copyright (c) 1998, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: extcheck
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: 基本ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "extcheck" "1" "2013年11月21日" "JDK 8" "基本ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
extcheck \- ターゲットのJavaアーカイブ(JAR)ファイルと現在インストールされている拡張機能のJARファイル間のバージョンの競合を検出します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBextcheck\fR [\fIoptions\fR] \fItargetfile\&.jar\fR
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fItargetfile\&.jar\fR
.RS 4
バージョンの競合を検出するために、現在インストールされている拡張機能JARファイルと比較するターゲットJARファイル。
.RE
.SH "説明"
.PP
\fBextcheck\fRコマンドは、指定されたJARファイルのタイトルおよびバージョンがJava SE SDKにインストールされている拡張機能と競合していないかをチェックします。拡張機能をインストールする前に、このユーティリティを使用して、同じバージョンまたはより新しいバージョンの拡張機能がすでにインストールされていないかどうかを調べることができます。
.PP
\fBextcheck\fRコマンドは、\fBtargetfile\&.jar\fRファイルのマニフェスト内のヘッダーSpecification\-titleおよびSpecification\-versionを、拡張機能ディレクトリ内に現在インストールされているすべてのJARファイル内の対応するヘッダーと比較します。デフォルトでは、拡張機能ディレクトリは、Oracle Solarisの場合は\fBjre/lib/ext\fR、Windowsの場合は\fB\ejre\elib\eext\fRです。\fBextcheck\fRコマンドは、\fBjava\&.lang\&.Package\&.isCompatibleWith\fRメソッドと同様の方法でバージョン番号を比較します。
.PP
競合が検出されない場合、リターン・コードは0です。
.PP
拡張機能ディレクトリ内のいずれかのJARファイルのマニフェストに、同一の\fBSpecification\-title\fR、および同一またはより新しい\fBSpecification\-version\fR番号がある場合は、ゼロでないエラー・コードが返されます。\fBtargetfile\&.jar\fRのマニフェスト・ファイルに\fBSpecification\-title\fRまたは\fBSpecification\-version\fR属性がない場合も、ゼロでないエラー・コードが返されます。
.SH "オプション"
.PP
\-verbose
.RS 4
拡張機能ディレクトリ内のJARファイルを、チェック時に一覧表示します。また、ターゲットJARファイルのマニフェストの属性、および競合するJARファイルについても報告します。
.RE
.PP
\-J\fIoption\fR
.RS 4
Java Virtual Machine (JVM)に\fIoption\fRを渡します。optionには、Java起動ツールのリファレンス・ページに記載されているオプションを1つ指定します。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。java(1)を参照してください。
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jar(1)
.RE
.br
'pl 8.5i
'bp
