'\" t
.\" Copyright (c) 1994, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: java
.\" Language: Japanese
.\" Date: 2015年3月3日
.\" SectDesc: 基本ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "java" "1" "2015年3月3日" "JDK 8" "基本ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
java \- Javaアプリケーションを起動します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava\fR [\fIoptions\fR] \fIclassname\fR [\fIargs\fR]
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava\fR [\fIoptions\fR] \fB\-jar\fR \fIfilename\fR [\fIargs\fR]
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
空白で区切られたコマンド行オプション。オプションを参照してください。
.RE
.PP
\fIclassname\fR
.RS 4
起動するクラスの名前。
.RE
.PP
\fIfilename\fR
.RS 4
呼び出されるJavaアーカイブ(JAR)ファイルの名前。\fB\-jar\fRオプションと一緒にのみ使用します。
.RE
.PP
\fIargs\fR
.RS 4
空白で区切られた\fBmain()\fRメソッドに渡される引数。
.RE
.SH "説明"
.PP
\fBjava\fRコマンドはJavaアプリケーションを開始します。Java Runtime Environment (JRE)を起動した後、指定したクラスをロードし、そのクラスの\fBmain()\fRメソッドを呼び出すことにより、これを行います。このメソッドは、\fIpublic\fRおよび\fIstatic\fRとして宣言する必要があります。また、値は返せません。さらに、\fBString\fR配列をパラメータとして指定できる必要があります。メソッド宣言は次の形式を含みます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpublic static void main(String[] args)\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBjava\fRコマンドを使用して、\fBmain()\fRメソッドがあるか\fBjavafx\&.application\&.Application\fRを拡張するクラスをロードすることで、JavaFXアプリケーションを起動できます。後者の場合、起動ツールは\fBApplication\fRクラスのインスタンスを構成し、その\fBinit()\fRメソッドをコールし、\fBstart(javafx\&.stage\&.Stage)\fRメソッドをコールします。
.PP
デフォルトでは、\fBjava\fRコマンドのオプションではない最初の引数は、呼び出されるクラスの完全修飾名です。\fB\-jar\fRオプションを指定した場合、その引数は、アプリケーションのクラス・ファイルとリソース・ファイルを含むJARファイルの名前になります。起動クラスは、ソース・コードの\fBMain\-Class\fRマニフェスト・ヘッダーによって指定される必要があります。
.PP
JREは、ブートストラップ・クラス・パス、インストール済拡張機能およびユーザーのクラス・パスの3箇所から、起動クラス(およびアプリケーションで使用されている他のクラス)を検索します。
.PP
クラス・ファイル名またはJARファイル名の後の引数は、\fBmain()\fRメソッドに渡されます。
.SH "オプション"
.PP
\fBjava\fRコマンドは、次のカテゴリに分類できる広範なオプションをサポートしています。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
標準オプション
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
非標準オプション
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
高度なランタイム・オプション
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
高度なJITコンパイラ・オプション
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
高度なサービスアビリティ・オプション
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
高度なガベージ・コレクション・オプション
.RE
.PP
標準のオプションは、Java Virtual Machine (JVM)のすべての実装でサポートすることが保証されます。これらは、JREのバージョンの確認、クラス・パスの設定、冗長な出力の有効化などの一般的なアクションに使用されます。
.PP
標準以外のオプションは、Java HotSpot仮想マシンに固有の汎用オプションであるため、すべてのJVM実装でサポートされることは保証されず、変更される可能性があります。これらのオプションは\fB\-X\fRで開始します。
.PP
拡張オプションは、不用意に使用しないことをお薦めします。これらは、特定のシステム要件を持つことが多く、システム構成パラメータへの特権アクセスが必要な場合があるJava HotSpot仮想マシン操作の特定の領域を調整するために使用される開発者オプションです。これらは、すべてのJVM実装でサポートされることは保証されず、変更される可能性があります。拡張オプションは\fB\-XX\fRで始まります。
.PP
最新リリースで非推奨または削除されるオプションを追跡するために、ドキュメントの最後に非推奨で削除されたオプションという名前のセクションがあります。
.PP
ブール・オプションは、デフォルトで無効になっている機能を有効にしたり、デフォルトで有効になっている機能を無効にするために使用されます。このようなオプションは、パラメータを必要としません。ブール値\fB\-XX\fRオプションは、プラス記号(\fB\-XX:+\fR\fIOptionName\fR)を使用して有効にし、マイナス記号(\fB\-XX:\-\fR\fIOptionName\fR)を使用して無効にします。
.PP
引数が必要なオプションの場合、引数は、オプション名を空白、コロン(:)または等号(=)で区切ったものになるか、あるいは引数がオプションの後に直接続く場合もあります(正確な構文は各オプションによって異なります)。サイズをバイト単位で指定するよう求められている場合、接尾辞を使用しないか、あるいはキロバイト(KB)には接尾辞\fBk\fRまたは\fBK\fR、メガバイト(MB)には接尾辞\fBm\fRまたは\fBM\fR、ギガバイト(GB)には接尾辞\fBg\fRまたは\fBG\fRを使用します。たとえば、サイズを8GBに設定するには、\fB8g\fR、\fB8192m\fR、\fB8388608k\fRまたは\fB8589934592\fRのいずれかを引数として指定できます。パーセントの指定が必要な場合は、0から1の数値を使用します(たとえば、25%の場合は\fB0\&.25\fRを指定します)。
.SS "標準オプション"
.PP
これらは、JVMのすべての実装でサポートされる最も一般的に使用されるオプションです。
.PP
\-agentlib:\fIlibname\fR[=\fIoptions\fR]
.RS 4
指定したネイティブ・エージェント・ライブラリをロードします。ライブラリ名の後に、ライブラリに固有のオプションのカンマ区切りリストを使用できます。
.sp
オプション\fB\-agentlib:foo\fRを指定した場合、JVMは、\fBLD_LIBRARY_PATH\fRシステム変数(OS Xでは、この変数は\fBDYLD_LIBRARY_PATH\fRになります)で指定された場所に\fBlibfoo\&.so\fRという名前のライブラリをロードしようとします。
.sp
次の例では、スタックの深さ3で、20ミリ秒ごとにヒープ・プロファイリング・ツール(HPROF)ライブラリをロードして、サンプルのCPU情報を取得する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-agentlib:hprof=cpu=samples,interval=20,depth=3\fR
 
.fi
.if n \{\
.RE
.\}
次の例では、メイン・クラスのロード前にJVMを一時停止して、Javaデバッグ・ワイヤ・プロトコル(JDWP)ライブラリをロードして、ポート8000でのソケット接続用にリスニングする方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-agentlib:jdwp=transport=dt_socket,server=y,address=8000\fR
 
.fi
.if n \{\
.RE
.\}
ネイティブ・エージェント・ライブラリの詳細は、次を参照してください。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
http://docs\&.oracle\&.com/javase/8/docs/api/java/lang/instrument/package\-summary\&.htmlにある\fBjava\&.lang\&.instrument\fRパッケージの説明
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
http://docs\&.oracle\&.com/javase/8/docs/platform/jvmti/jvmti\&.html#startingにあるJVMツール・インタフェース・ガイドのエージェントのコマンド行オプションに関する項
.RE
.RE
.PP
\-agentpath:\fIpathname\fR[=\fIoptions\fR]
.RS 4
絶対パス名で指定されたネイティブ・エージェント・ライブラリをロードします。このオプションは\fB\-agentlib\fRと同等ですが、ライブラリのフル・パスおよびファイル名を使用します。
.RE
.PP
\-client
.RS 4
Java HotSpot Client VMを選択します。64ビット・バージョンのJava SE Development Kit (JDK)では、現在、このオプションは無視され、かわりにServer JVMが使用されます。
.sp
デフォルトのJVM選択は、http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/vm/server\-class\&.htmlにある
サーバークラス・マシンの検出を参照してください。
.RE
.PP
\-D\fIproperty\fR=\fIvalue\fR
.RS 4
システム・プロパティの値を設定します。\fIproperty\fR変数は、プロパティの名前を表す、空白のない文字列です。\fIvalue\fR変数は、プロパティの値を表す文字列です。\fIvalue\fRが空白を含む文字列の場合、それを引用符で囲みます(例:
\fB\-Dfoo="foo bar"\fR)。
.RE
.PP
\-d32
.RS 4
アプリケーションを32ビット環境で実行します。32ビット環境がインストールされていないかサポートされていない場合は、エラーが報告されます。デフォルトでは、64ビット・システムが使用されている場合を除き、アプリケーションは32ビット環境で実行されます。
.RE
.PP
\-d64
.RS 4
アプリケーションを64ビット環境で実行します。64ビット環境がインストールされていないかサポートされていない場合は、エラーが報告されます。デフォルトでは、64ビット・システムが使用されている場合を除き、アプリケーションは32ビット環境で実行されます。
.sp
現在のところ、Java HotSpot Server VMのみが64ビットの操作をサポートしているため、\fB\-d64\fR使用時には\fB\-server\fRオプションが暗黙的に使用されます。\fB\-d64\fR使用時には、\fB\-client\fRオプションは無視されます。この仕様は、将来のリリースでは変更になる可能性があります。
.RE
.PP
\-disableassertions[:[\fIpackagename\fR]\&.\&.\&.|:\fIclassname\fR]
.br
\-da[:[\fIpackagename\fR]\&.\&.\&.|:\fIclassname\fR]
.RS 4
アサーションを無効にします。デフォルトでは、アサーションはすべてのパッケージおよびクラスで無効になっています。
.sp
引数なしの\fB\-disableassertions\fR
(\fB\-da\fR)を指定すると、すべてのパッケージおよびクラスでアサーションが無効になります。\fB\&.\&.\&.\fRで終わる\fIpackagename\fR引数を指定すると、指定したパッケージとそのサブパッケージ内でアサーションが無効になります。引数として\fB\&.\&.\&.\fRのみを指定すると、現在の作業ディレクトリにある名前のないパッケージ内でアサーションが無効になります。\fIclassname\fR引数を指定すると、切替えによって、指定したクラス内でアサーションが無効になります。
.sp
\fB\-disableassertions\fR
(\fB\-da\fR)オプションは、すべてのクラス・ローダーおよびシステム・クラスに適用されます(システム・クラスにはクラス・ローダーはありません)。このルールには1つ例外があります。オプションの引数が指定されていない場合は、システム・クラスに適用されません。これにより、システム・クラスを除くすべてのクラスでアサーションを簡単に無効にすることができます。\fB\-disablesystemassertions\fRオプションを使用すると、すべてのシステム・クラスでアサーションを無効にすることができます。
.sp
特定のパッケージやクラスでアサーションを明示的に有効にするには、\fB\-enableassertions\fR
(\fB\-ea\fR)オプションを使用します。両方のオプションを同時に使用できます。たとえば、パッケージ\fBcom\&.wombat\&.fruitbat\fR
(およびそのサブパッケージ)ではアサーションを有効にして、クラス\fBcom\&.wombat\&.fruitbat\&.Brickbat\fRではアサーションを無効にして、\fBMyClass\fRアプリケーションを実行するには、次のコマンドを使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava \-ea:com\&.wombat\&.fruitbat\&.\&.\&. \-da:com\&.wombat\&.fruitbat\&.Brickbat MyClass\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-disablesystemassertions
.br
\-dsa
.RS 4
すべてのシステム・クラス内でアサーションを無効にします。
.RE
.PP
\-enableassertions[:[\fIpackagename\fR]\&.\&.\&.|:\fIclassname\fR]
.br
\-ea[:[\fIpackagename\fR]\&.\&.\&.|:\fIclassname\fR]
.RS 4
アサーションを有効にします。デフォルトでは、アサーションはすべてのパッケージおよびクラスで無効になっています。
.sp
引数なしの\fB\-enableassertions\fR
(\fB\-ea\fR)を指定すると、すべてのパッケージおよびクラスでアサーションが有効になります。\fB\&.\&.\&.\fRで終わる\fIpackagename\fR引数を指定すると、指定したパッケージとそのサブパッケージ内でアサーションが有効になります。引数として\fB\&.\&.\&.\fRのみを指定すると、現在の作業ディレクトリにある名前のないパッケージ内でアサーションが有効になります。\fIclassname\fR引数を指定すると、切替えによって、指定したクラス内でアサーションが有効になります。
.sp
\fB\-enableassertions\fR
(\fB\-ea\fR)オプションは、すべてのクラス・ローダーおよびシステム・クラスに適用されます(システム・クラスにはクラス・ローダーはありません)。このルールには1つ例外があります。オプションの引数が指定されていない場合は、システム・クラスに適用されません。これにより、システム・クラスを除くすべてのクラスでアサーションを簡単に有効にすることができます。\fB\-enablesystemassertions\fRオプションは、すべてのシステム・クラスでアサーションを有効にする別のスイッチを提供します。
.sp
特定のパッケージやクラスでアサーションを明示的に無効にするには、\fB\-disableassertions\fR
(\fB\-da\fR)オプションを使用します。単一コマンドにこれらのスイッチのインスタンスを複数指定した場合は、指定したスイッチが順番に処理されてからクラスがロードされます。たとえば、パッケージ\fBcom\&.wombat\&.fruitbat\fR
(およびそのサブパッケージ)でのみアサーションを有効にして、クラス\fBcom\&.wombat\&.fruitbat\&.Brickbat\fRではアサーションを無効にして、\fBMyClass\fRアプリケーションを実行するには、次のコマンドを使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava \-ea:com\&.wombat\&.fruitbat\&.\&.\&. \-da:com\&.wombat\&.fruitbat\&.Brickbat MyClass\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-enablesystemassertions
.br
\-esa
.RS 4
すべてのシステム・クラス内でアサーションを有効にします。
.RE
.PP
\-help
.br
\-?
.RS 4
実際にJVMを実行せずに、\fBjava\fRコマンドの使用状況情報を表示します。
.RE
.PP
\-jar \fIfilename\fR
.RS 4
JARファイルにカプセル化されたプログラムを実行します。\fIfilename\fR引数は、使用するアプリケーションの開始点として機能する\fBpublic static void main(String[] args)\fRメソッドを定義する、\fBMain\-Class:\fR\fIclassname\fRという形式の1行を含むマニフェストを持つJARファイルの名前です。
.sp
\fB\-jar\fRオプションを使用すると、指定したJARファイルがすべてのユーザー・クラスのソースになり、クラス・パスの他の設定は無視されます。
.sp
JARファイルの詳細は、次のリソースを参照してください。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jar(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/jar/index\&.htmlにあるJavaアーカイブ(JAR)ファイルのガイド
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
http://docs\&.oracle\&.com/javase/tutorial/deployment/jar/index\&.htmlにある
「レッスン: JARファイルのプログラムのパッケージ化」
.RE
.RE
.PP
\-javaagent:\fIjarpath\fR[=\fIoptions\fR]
.RS 4
指定したJavaプログラミング言語エージェントをロードします。Javaアプリケーションのインストゥルメントの詳細は、http://docs\&.oracle\&.com/javase/8/docs/api/java/lang/instrument/package\-summary\&.htmlにあるJava APIドキュメントの\fBjava\&.lang\&.instrument\fRパッケージの説明を参照してください。
.RE
.PP
\-jre\-restrict\-search
.RS 4
ユーザー・プライベートなJREをバージョン検索に含めます。
.RE
.PP
\-no\-jre\-restrict\-search
.RS 4
ユーザー・プライベートなJREをバージョン検索から除外します。
.RE
.PP
\-server
.RS 4
Java HotSpot Server VMを選択します。64ビット・バージョンのJDKでは、Server VMのみをサポートしているため、その場合、このオプションは暗黙的です。
.sp
デフォルトのJVM選択は、http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/vm/server\-class\&.htmlにある
サーバークラス・マシンの検出を参照してください。
.RE
.PP
\-showversion
.RS 4
バージョン情報を表示し、アプリケーションの実行を続行します。このオプションは\fB\-version\fRオプションと同等ですが、\-versionはバージョン情報の表示後にJVMに終了を指示する点が異なります。
.RE
.PP
\-splash:\fIimgname\fR
.RS 4
\fIimgname\fRで指定されたイメージを含むスプラッシュ画面を表示します。たとえば、アプリケーションの起動時に\fBimages\fRディレクトリの\fBsplash\&.gif\fRファイルを表示するには、次のオプションを使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-splash:images/splash\&.gif\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-verbose:class
.RS 4
ロードされた各クラスに関する情報を表示します。
.RE
.PP
\-verbose:gc
.RS 4
各ガベージ・コレクション(GC)イベントに関する情報を表示します。
.RE
.PP
\-verbose:jni
.RS 4
ネイティブ・メソッドの使用およびその他のJava Native Interface (JNI)アクティビティに関する情報を表示します。
.RE
.PP
\-version
.RS 4
バージョン情報を表示してから終了します。このオプションは\fB\-showversion\fRオプションと同等ですが、\-showversionはバージョン情報の表示後にJVMに終了を指示しない点が異なります。
.RE
.PP
\-version:\fIrelease\fR
.RS 4
アプリケーションの実行に使用するリリース・バージョンを指定します。コールされた\fBjava\fRコマンドのバージョンがこの指定内容を満たさず、かつ適切な実装がシステム上で見つかった場合には、その適切な実装が使用されます。
.sp
\fIrelease\fR引数は、正確なバージョン文字列、または空白で区切られたバージョン文字列とバージョン範囲のリストを指定します。\fIバージョン文字列\fRは、次の形式で、開発者のバージョン番号を指定します:
\fB1\&.\fR\fIx\fR\fB\&.0_\fR\fIu\fR
(\fIx\fRはメジャー・バージョン番号、\fIu\fRは更新バージョン番号です)。\fIバージョン範囲\fRは、このバージョン以降を指定するにはバージョン文字列の後にプラス記号(\fB+\fR)を続けたもの、または一致する接頭辞を含む任意のバージョン文字列を指定するには一部のバージョン文字列の後にアスタリスク(\fB*\fR)を続けたもので構成されます。論理\fIOR\fRの組合せには空白、2つのバージョンの文字列/範囲の論理\fIAND\fRの組合せにはアンパサンド(\fB&\fR)を使用して、バージョン文字列とバージョン範囲を組み合せることができます。たとえば、クラスまたはJARファイルの実行にJRE 6u13 (1\&.6\&.0_13)または6u10 (1\&.6\&.0_10)以降の任意のJRE 6のいずれかを必要とする場合、次を指定します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-version:"1\&.6\&.0_13 1\&.6* & 1\&.6\&.0_10+"\fR
 
.fi
.if n \{\
.RE
.\}
引用符が必要なのは、\fIrelease\fRパラメータに空白がある場合のみです。
.sp
JARファイルの場合は、バージョン要件をコマンド行に指定するよりも、JARファイルのマニフェスト内に指定することが推奨されています。
.RE
.SS "非標準オプション"
.PP
これらのオプションは、Java HotSpot仮想マシンに固有の汎用オプションです。
.PP
\-X
.RS 4
使用可能なすべての\fB\-X\fRオプションのヘルプを表示します。
.RE
.PP
\-Xbatch
.RS 4
バックグラウンド・コンパイルを無効にします。デフォルトでは、JVMでは、バックグラウンド・コンパイルが終了するまで、メソッドをバックグラウンド・タスクとしてコンパイルし、インタプリタ・モードでメソッドを実行します。\fB\-Xbatch\fRフラグを指定すると、バックグラウンド・コンパイルが無効になり、すべてのメソッドのコンパイルが完了するまでフォアグラウンド・タスクとして処理されます。
.sp
このオプションは\fB\-XX:\-BackgroundCompilation\fRと同等です。
.RE
.PP
\-Xbootclasspath:\fIpath\fR
.RS 4
ブート・クラス・ファイルを検索するディレクトリ、JARファイルおよびZIPアーカイブの、コロン(:)で区切られたリストを指定します。これらは、JDKに含まれるブート・クラス・ファイルのかわりに使用されます。
.sp
JREバイナリ・コード・ライセンスに違反するため、\fBrt\&.jar\fRでクラスをオーバーライドする目的で、このオプションを使用するアプリケーションをデプロイしないでください。
.RE
.PP
\-Xbootclasspath/a:\fIpath\fR
.RS 4
デフォルトのブートストラップ・クラス・パスの最後に追加するディレクトリ、JARファイルおよびZIPアーカイブの、コロン(:)で区切られたリストを指定します。
.sp
JREバイナリ・コード・ライセンスに違反するため、\fBrt\&.jar\fRでクラスをオーバーライドする目的で、このオプションを使用するアプリケーションをデプロイしないでください。
.RE
.PP
\-Xbootclasspath/p:\fIpath\fR
.RS 4
デフォルトのブートストラップ・クラス・パスの先頭に追加するディレクトリ、JARファイルおよびZIPアーカイブの、コロン(:)で区切られたリストを指定します。
.sp
JREバイナリ・コード・ライセンスに違反するため、\fBrt\&.jar\fRでクラスをオーバーライドする目的で、このオプションを使用するアプリケーションをデプロイしないでください。
.RE
.PP
\-Xcheck:jni
.RS 4
Java Native Interface (JNI)機能に対して追加チェックを行います。具体的には、これは、JNIリクエストを処理する前に、JNI関数に渡されるパラメータと実行環境のデータを検証します。無効なデータが見つかった場合は、ネイティブ・コードに問題があることを示しているため、JVMはリカバリ不能なエラーを発生して終了します。このオプションを使用すると、パフォーマンス低下が予想されます。
.RE
.PP
\-Xcomp
.RS 4
最初の呼出しでメソッドのコンパイルを強制的に実行します。デフォルトでは、クライアントVM(\fB\-client\fR)は1,000の解釈されたメソッド呼出しを実行し、サーバーVM(\fB\-server\fR)は10,000の解釈されたメソッド呼出しを実行して、効率的なコンパイルのための情報を収集します。\fB\-Xcomp\fRオプションを指定すると、解釈されたメソッド呼出しが無効になり、効率を犠牲にしてコンパイルのパフォーマンスが向上します。
.sp
\fB\-XX:CompileThreshold\fRオプションを使用して、コンパイルの前に、解釈されたメソッド呼出しの数を変更することもできます。
.RE
.PP
\-Xdebug
.RS 4
何も行いません。後方互換性のために用意されています。
.RE
.PP
\-Xdiag
.RS 4
追加の診断メッセージを表示します。
.RE
.PP
\-Xfuture
.RS 4
クラス・ファイル形式の仕様への準拠を強化する、厳密なクラス・ファイル形式のチェックが有効になります。将来のリリースでは、より厳密なチェックがデフォルトになるため、新しいコードを開発するときには、開発者はこのフラグを使用することをお薦めします。
.RE
.PP
\-Xint
.RS 4
インタプリタ専用モードでアプリケーションを実行します。ネイティブ・コードへのコンパイルは無効になり、すべてのバイトコードがインタプリタによって実行されます。ジャスト・イン・タイム(JIT)コンパイラが提供するパフォーマンス上の利点は、このモードでは実現されません。
.RE
.PP
\-Xinternalversion
.RS 4
\fB\-version\fRオプションより詳細なJVMバージョン情報を表示してから終了します。
.RE
.PP
\-Xloggc:\fIfilename\fR
.RS 4
詳細なGCイベント情報をロギング用にリダイレクトするファイルを設定します。このファイルに書き込まれる情報は、記録された各イベントの前に行われる最初のGCイベント以降に経過した時間を指定した\fB\-verbose:gc\fRの出力と類似しています。\fB\-Xloggc\fRオプションは\fB\-verbose:gc\fRをオーバーライドします(これらの両方が同じ\fBjava\fRコマンドで指定された場合)。
.sp
例:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Xloggc:garbage\-collection\&.log\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-Xmaxjitcodesize=\fIsize\fR
.RS 4
JITコンパイルされたコードの最大コード・キャッシュ・サイズ(バイト単位)を指定します。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。デフォルトでは、この値は48MBに設定されています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Xmaxjitcodesize=48m\fR
 
.fi
.if n \{\
.RE
.\}
このオプションは\fB\-XX:ReservedCodeCacheSize\fRと同等です。
.RE
.PP
\-Xmixed
.RS 4
ネイティブ・コードにコンパイルされたホット・メソッドを除き、インタプリタによってすべてのバイトコードを実行します。
.RE
.PP
\-Xmn\fIsize\fR
.RS 4
若い世代(ナーサリ)のヒープの初期サイズおよび最大サイズ(バイト単位)を設定します。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。
.sp
ヒープの若い世代リージョンは新しいオブジェクトに使用されます。GCは、他のリージョンよりこのリージョンで、より頻繁に実行されます。若い世代のサイズが小さすぎる場合、多数のマイナー・ガベージ・コレクションが実行されます。サイズが大きすぎる場合、フル・ガベージ・コレクションのみが実行されますが、完了までに時間がかかることがあります。若い世代のサイズは、全体のヒープ・サイズの半分から4分の1の間にしておくことをお薦めします。
.sp
次の例では、若い世代の初期サイズおよび最大サイズを様々な単位を使用して256MBに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Xmn256m\fR
\fB\-Xmn262144k\fR
\fB\-Xmn268435456\fR
 
.fi
.if n \{\
.RE
.\}
若い世代のヒープの初期サイズと最大サイズの両方を設定する\fB\-Xmn\fRオプションのかわりに、初期サイズの設定には\fB\-XX:NewSize\fRを、最大サイズの設定には\fB\-XX:MaxNewSize\fRを使用できます。
.RE
.PP
\-Xms\fIsize\fR
.RS 4
ヒープの初期サイズ(バイト単位)を設定します。指定する値は、1MBより大きい1024の倍数にする必要があります。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。
.sp
次の例では、割り当てられたメモリーのサイズを様々な単位を使用して6MBに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Xms6291456\fR
\fB\-Xms6144k\fR
\fB\-Xms6m\fR
 
.fi
.if n \{\
.RE
.\}
このオプションを設定しない場合、初期サイズは、古い世代と若い世代に割り当てられたサイズの合計として設定されます。若い世代のヒープの初期サイズは、\fB\-Xmn\fRオプションまたは\fB\-XX:NewSize\fRオプションを使用して設定できます。
.RE
.PP
\-Xmx\fIsize\fR
.RS 4
メモリー割当てプールの最大サイズ(バイト単位)を指定します。指定する値は、2MBより大きい1024の倍数にする必要があります。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。デフォルト値は、実行時にシステム構成に基づいて選択されます。サーバー・デプロイメントでは、\fB\-Xms\fRおよび\fB\-Xmx\fRは通常同じ値に設定されます。http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/vm/gctuning/index\&.htmlの\fIJava SE HotSpot仮想マシンのガベージ・コレクション・チューニング・ガイド\fRのエルゴノミクスに関する項を参照してください。
.sp
次の例では、割り当てられたメモリーの許可される最大サイズを様々な単位を使用して80MBに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Xmx83886080\fR
\fB\-Xmx81920k\fR
\fB\-Xmx80m\fR
 
.fi
.if n \{\
.RE
.\}
\fB\-Xmx\fRオプションは\fB\-XX:MaxHeapSize\fRと同等です。
.RE
.PP
\-Xnoclassgc
.RS 4
クラスのガベージ・コレクション(GC)を無効にします。これにより、GC時間を節約でき、アプリケーション実行中の中断が短縮されます。
.sp
起動時に\fB\-Xnoclassgc\fRを指定すると、アプリケーション内のクラス・オブジェクトは、GCの間はそのまま残り、常にライブであるとみなされます。これにより、より多くのメモリーが永久に占有されることになり、注意して使用しないと、メモリー不足の例外がスローされます。
.RE
.PP
\-Xprof
.RS 4
実行中のプログラムをプロファイルし、プロファイリング・データを標準出力に送信します。このオプションは、プログラム開発用のユーティリティとして提供されています。本番稼働システムでの使用を目的としたものではありません。
.RE
.PP
\-Xrs
.RS 4
JVMによるオペレーティング・システム・シグナルの使用を減らします。
.sp
シャットダウン・フックは、JVMが突然終了した場合でも、シャットダウン時にユーザー・クリーンアップ・コード(データベース接続のクローズなど)を実行することによって、Javaアプリケーションのシャットダウンを順番に有効にします。
.sp
JVMは、予期しない終了のシャットダウン・フックを実装するためにシグナルをキャッチします。JVMは、\fBSIGHUP\fR、\fBSIGINT\fRおよび\fBSIGTERM\fRを使用して、シャットダウン・フックの実行を開始します。
.sp
JVMは、デバッグの目的でスレッド・スタックをダンプするという機能を実現するために、同様のメカニズムを使用します。JVMは、スレッド・ダンプを実行するために\fBSIGQUIT\fRを使用します。
.sp
JVMを埋め込んでいるアプリケーションは、\fBSIGINT\fRや\fBSIGTERM\fRなどのシグナルを頻繁にトラップする必要があり、その結果、JVMのシグナル・ハンドラと衝突する可能性があります。\fB\-Xrs\fRオプションは、この問題に対処するために使用できます。\fB\-Xrs\fRが使用されている場合、\fBSIGINT\fR、\fBSIGTERM\fR、\fBSIGHUP\fRおよび\fBSIGQUIT\fRのシグナル・マスクはJVMによって変更されず、これらのシグナルのシグナル・ハンドラはインストールされません。
.sp
\fB\-Xrs\fRを指定すると、次の2つの結果が生じます:
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBSIGQUIT\fRによるスレッド・ダンプは使用できません。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
シャットダウン・フック処理の実行は、JVMが終了しようとしている時点で\fBSystem\&.exit()\fRを呼び出すなどして、ユーザー・コード側で行う必要があります。
.RE
.RE
.PP
\-Xshare:\fImode\fR
.RS 4
クラス・データ共有モードを設定します。このオプションで使用可能な\fImode\fR引数は次のとおりです。
.PP
auto
.RS 4
可能な場合、共有クラスのデータを使用します。これは、Java HotSpot 32\-Bit Client VMの場合のデフォルト値です。
.RE
.PP
on
.RS 4
クラス・データ共有の使用が必要です。クラス・データ共有を使用できない場合、エラー・メッセージを出力して終了します。
.RE
.PP
off
.RS 4
共有クラス・データを使用しません。これは、Java HotSpot 32\-Bit Server VM、Java HotSpot 64\-Bit Client VMおよびJava HotSpot 64\-Bit Server VMの場合のデフォルト値です。
.RE
.PP
dump
.RS 4
クラス・データ共有アーカイブを手動で生成します。
.RE
.RE
.PP
\-XshowSettings:\fIcategory\fR
.RS 4
設定を表示して続行します。このオプションで使用可能な\fIcategory\fR引数は次のとおりです。
.PP
all
.RS 4
設定のすべてのカテゴリを表示します。これがデフォルト値です。
.RE
.PP
locale
.RS 4
ロケールに関連する設定を表示します。
.RE
.PP
properties
.RS 4
システム・プロパティに関連する設定を表示します。
.RE
.PP
vm
.RS 4
JVMの設定を表示します。
.RE
.RE
.PP
\-Xss\fIsize\fR
.RS 4
スレッドのスタック・サイズ(バイト単位)を設定します。KBを示す場合は文字\fBk\fRまたは\fBK\fR、MBを示す場合は文字\fBm\fRまたは\fBM\fR、GBを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。デフォルト値はプラットフォームによって異なります。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Linux/ARM (32ビット): 320KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Linux/i386 (32ビット): 320KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Linux/x64 (64ビット): 1024KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
OS X (64ビット): 1024KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Oracle Solaris/i386 (32ビット): 320KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Oracle Solaris/x64 (64ビット): 1024KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Windows: 仮想メモリーによって異なります。
.RE
.sp
次の例では、スレッド・スタック・サイズを異なる単位で1024KBに設定します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Xss1m\fR
\fB\-Xss1024k\fR
\fB\-Xss1048576\fR
 
.fi
.if n \{\
.RE
.\}
このオプションは\fB\-XX:ThreadStackSize\fRと同等です。
.RE
.PP
\-Xusealtsigs
.RS 4
JVM内部シグナルの\fBSIGUSR1\fRおよび\fBSIGUSR2\fRのかわりに、代替シグナルを使用します。このオプションは\fB\-XX:+UseAltSigs\fRと同等です。
.RE
.PP
\-Xverify:\fImode\fR
.RS 4
バイトコード・ベリファイアのモードを設定します。バイトコードの検証は一部の問題のトラブルシューティングに役立ちますが、実行中のアプリケーションへのオーバーヘッドを増大させます。このオプションで使用可能な\fImode\fR引数は次のとおりです。
.PP
なし
.RS 4
バイトコードを検証しません。これにより、起動時間が短縮され、Javaによって提供される保護も軽減されます。
.RE
.PP
remote
.RS 4
ブートストラップ・クラス・ローダーでロードされていないこれらのクラスを検証します。これは、\fB\-Xverify\fRオプションを指定しない場合のデフォルトの動作です。
.RE
.PP
all
.RS 4
すべてのクラスを検証します。
.RE
.RE
.SS "高度なランタイム・オプション"
.PP
これらのオプションは、Java HotSpot VMの実行時の動作を制御します。
.PP
\-XX:+DisableAttachMechanism
.RS 4
JVMにツールをアタッチするメカニズムを無効にするオプションを有効にします。デフォルトでは、このオプションは無効になっており、これは、アタッチ・メカニズムを有効にすると、\fBjcmd\fR、\fBjstack\fR、\fBjmap\fR、\fBjinfo\fRなどのツールを使用できることを意味します。
.RE
.PP
\-XX:ErrorFile=\fIfilename\fR
.RS 4
リカバリ不能なエラーが発生した場合にエラー・データが書き込まれるパスおよびファイル名を指定します。デフォルトでは、このファイルは、現在の作業ディレクトリに作成され、名前は\fBhs_err_pid\fR\fIpid\fR\fB\&.log\fR
(\fIpid\fRはエラーの原因となったプロセスの識別子)になります。次の例では、デフォルトのログ・ファイルを設定する方法を示します(プロセスの識別子は\fB%p\fRとして指定されます)。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:ErrorFile=\&./hs_err_pid%p\&.log\fR
 
.fi
.if n \{\
.RE
.\}
次の例では、エラー・ログを\fB/var/log/java/java_error\&.log\fRに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:ErrorFile=/var/log/java/java_error\&.log\fR
 
.fi
.if n \{\
.RE
.\}
(領域不足、権限の問題または別の問題により)指定したディレクトリにファイルを作成できない場合、ファイルはオペレーティング・システムの一時ディレクトリに作成されます。一時ディレクトリは\fB/tmp\fRです。
.RE
.PP
\-XX:+FailOverToOldVerifier
.RS 4
新しいタイプ・チェッカが失敗した場合の、古いベリファイアへの自動フェイルオーバーを有効にします。デフォルトでは、このオプションは無効になっており、最近のバイトコード・バージョンを使用したクラスには、これは無視されます(つまり、無効として処理されます)。古いバージョンのバイトコードを使用したクラスには、これを有効化できます。
.RE
.PP
\-XX:+FlightRecorder
.RS 4
アプリケーションの実行中に、Java Flight Recorder (JFR)の使用を有効にします。これは商用機能で、\fB\-XX:+UnlockCommercialFeatures\fRオプションを次のように指定することも必要になります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava \-XX:+UnlockCommercialFeatures \-XX:+FlightRecorder\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:FlightRecorderOptions=\fIparameter\fR=\fIvalue\fR
.RS 4
JFRの動作を制御するパラメータを設定します。このオプションを使用できるのは、JFRが有効な場合(\fB\-XX:+FlightRecorder\fRオプションが指定されている場合)のみです。
.sp
次のリストには、使用可能なすべてのJFRパラメータが含まれます。
.PP
defaultrecording={true|false}
.RS 4
記録が連続的なバックグラウンド記録か、または限定された時間に対して実行されるかを指定します。デフォルトでは、このパラメータは\fBfalse\fR
(記録は限定された時間に対して実行される)に設定されています。記録を連続的に実行するには、パラメータを\fBtrue\fRに設定します。
.RE
.PP
disk={true|false}
.RS 4
JFRでディスクに連続記録を書き込む必要があるかどうかを指定します。デフォルトでは、このパラメータは\fBfalse\fR
(ディスクへの連続記録は無効)に設定されています。これを有効にするには、パラメータを\fBtrue\fRに設定し、\fBdefaultrecording=true\fRも設定します。
.RE
.PP
dumponexit={true|false}
.RS 4
JVMが制御された方式で終了したときに、JFRデータのダンプ・ファイルを生成するかどうかを指定します。デフォルトでは、このパラメータは\fBfalse\fR
(終了時にダンプ・ファイルは生成されない)に設定されています。これを有効にするには、パラメータを\fBtrue\fRに設定し、\fBdefaultrecording=true\fRも設定します。
.sp
ダンプ・ファイルは、\fBdumponexitpath\fRパラメータによって指定された場所に書き込まれます。
.RE
.PP
dumponexitpath=\fIpath\fR
.RS 4
\fBdumponexit=true\fRパラメータを設定した場合、JVMが制御された方式で終了したときに、作成されるJFRデータを含むダンプ・ファイルのパスおよび名前を指定します。パスの設定は、\fBdefaultrecording=true\fRも設定した場合のみ有効になります。
.sp
指定したパスがディレクトリである場合、JVMは、作成の日付および時間を示すファイル名を割り当てます。指定したパスにすでに存在するファイルのファイル名が含まれる場合、指定したファイル名に日付およびタイム・スタンプを追加して新しいファイルが作成されます。
.RE
.PP
globalbuffersize=\fIsize\fR
.RS 4
データ保存に使用する総プライマリ・メモリー量(バイト単位)を指定します。サイズをKBで指定する場合は\fBk\fRまたは\fBK\fR、サイズをMBで指定する場合は\fBm\fRまたは\fBM\fR、サイズをGBで指定する場合は\fBg\fRまたは\fBG\fRを追加します。デフォルトでは、サイズは462848バイトに設定されています。
.RE
.PP
loglevel={quiet|error|warning|info|debug|trace}
.RS 4
JFRによってログ・ファイルに書き込まれるデータの量を指定します。デフォルトでは、\fBinfo\fRに設定されています。
.RE
.PP
maxage=\fItime\fR
.RS 4
デフォルト記録のディスク・データの最大保持時間を指定します。時間を指定する際に、秒単位の場合は\fBs\fR、分の場合は\fBm\fR、時間の場合は\fBh\fR、日の場合は\fBd\fRを追加します(たとえば、\fB30s\fRという指定は30秒を意味します)。デフォルトでは、最大保持時間は15分(\fB15m\fR)に設定されています。
.sp
このパラメータは、\fBdisk=true\fRパラメータが設定されている場合にのみ有効です。
.RE
.PP
maxchunksize=\fIsize\fR
.RS 4
記録内のデータ・チャンクの最大サイズ(バイト単位)を指定します。サイズをKBで指定する場合は\fBk\fRまたは\fBK\fR、サイズをMBで指定する場合は\fBm\fRまたは\fBM\fR、サイズをGBで指定する場合は\fBg\fRまたは\fBG\fRを追加します。デフォルトでは、データ・チャンクの最大サイズは12MBに設定されています。
.RE
.PP
maxsize=\fIsize\fR
.RS 4
デフォルト記録のディスク・データの最大保持サイズ(バイト単位)を指定します。サイズをKBで指定する場合は\fBk\fRまたは\fBK\fR、サイズをMBで指定する場合は\fBm\fRまたは\fBM\fR、サイズをGBで指定する場合は\fBg\fRまたは\fBG\fRを追加します。デフォルトでは、ディスク・データの最大サイズは制限されず、このパラメータは0に設定されています。
.sp
このパラメータは、\fBdisk=true\fRパラメータが設定されている場合にのみ有効です。
.RE
.PP
repository=\fIpath\fR
.RS 4
一時ディスク記憶域のリポジトリ(ディレクトリ)を指定します。デフォルトでは、システムの一時ディレクトリが使用されます。
.RE
.PP
samplethreads={true|false}
.RS 4
スレッドのサンプリングを有効化するかどうかを指定します。スレッドのサンプリングは、このパラメータでサンプリング・イベントが有効になっている場合に発生します。デフォルトでは、このパラメータは有効になります。
.RE
.PP
settings=\fIpath\fR
.RS 4
イベント設定ファイル(タイプはJFC)のパスと名前を指定します。デフォルトでは、\fBJAVA_HOME/jre/lib/jfr\fRにある\fBdefault\&.jfc\fRファイルが使用されます。
.RE
.PP
stackdepth=\fIdepth\fR
.RS 4
JFRによるスタック・トレースのスタックの深さ。デフォルトでは、深さは64メソッド呼出しに設定されています。最大は2048で、最小は1です。
.RE
.PP
threadbuffersize=\fIsize\fR
.RS 4
スレッドごとのローカル・バッファ・サイズ(バイト単位)を指定します。サイズをKBで指定する場合は\fBk\fRまたは\fBK\fR、サイズをMBで指定する場合は\fBm\fRまたは\fBM\fR、サイズをGBで指定する場合は\fBg\fRまたは\fBG\fRを追加します。このパラメータの値が高いと、競合することなく、より多くのデータを収集してグローバル記憶域にフラッシュすることができます。スレッドが多い環境ではアプリケーションのフットプリントも増加します。デフォルトでは、ローカル・バッファ・サイズは5KBに設定されています。
.RE
.sp
カンマで区切って、複数のパラメータの値を指定できます。たとえば、ディスクに連続記録を書き込み、データ・チャンクの最大サイズを10MBに設定するようにJFRに指示するには、次のように指定します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:FlightRecorderOptions=defaultrecording=true,disk=true,maxchunksize=10M\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:LargePageSizeInBytes=\fIsize\fR
.RS 4
Solarisでは、Javaヒープに使用されるラージ・ページの最大サイズ(バイト単位)を設定します。\fIsize\fR引数は、2の累乗(2、4、8、16、\&.\&.\&.)である必要があります。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。デフォルトでは、サイズは0に設定され、これは、JVMではラージ・ページのサイズが自動的に選択されていることを意味します。
.sp
次の例では、ラージ・ページのサイズを4メガバイト(MB)に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:LargePageSizeInBytes=4m\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MaxDirectMemorySize=\fIsize\fR
.RS 4
新規I/O (\fBjava\&.nio\fRパッケージ)の直接バッファ割当ての最大合計サイズ(バイト単位)を設定します。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。デフォルトでは、サイズは0に設定され、これは、JVMではNIOの直接バッファ割当てのサイズが自動的に選択されていることを意味します。
.sp
次の例では、NIOサイズを異なる単位で1024KBに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxDirectMemorySize=1m\fR
\fB\-XX:MaxDirectMemorySize=1024k\fR
\fB\-XX:MaxDirectMemorySize=1048576\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:NativeMemoryTracking=\fImode\fR
.RS 4
JVMのネイティブ・メモリー使用状況のトラッキングのモードを指定します。このオプションで使用可能な\fImode\fR引数は次のとおりです。
.PP
off
.RS 4
JVMのネイティブ・メモリー使用状況を追跡しません。これは、\fB\-XX:NativeMemoryTracking\fRオプションを指定しない場合のデフォルトの動作です。
.RE
.PP
summary
.RS 4
JVMサブシステム(Javaヒープ、クラス、コード、スレッドなど)によるメモリー使用状況のみ追跡します。
.RE
.PP
detail
.RS 4
JVMサブシステムによるメモリー使用状況のトラッキングに加えて、個々の\fBCallSite\fR
(個々の仮想メモリー・リージョンおよびそのコミット済リージョン)によるメモリー使用状況を追跡します。
.RE
.RE
.PP
\-XX:ObjectAlignmentInBytes=\fIalignment\fR
.RS 4
Javaオブジェクトのメモリー配置を設定します(バイト単位)。デフォルトでは、値が8バイトに設定されます。指定される値は、2の累乗にして8から256(両端を含む)の範囲内にする必要があります。このオプションにより、大きいJavaヒープ・サイズで圧縮ポインタを使用できます。
.sp
バイト単位のヒープ・サイズ制限は次のように計算されます:
.sp
\fB4GB * ObjectAlignmentInBytes\fR
.sp
注意: 配置の値が増えると、オブジェクト間の未使用の領域も増えます。結果として、大きいヒープ・サイズで圧縮ポインタを使用するメリットがわからない可能性があります。
.RE
.PP
\-XX:OnError=\fIstring\fR
.RS 4
リカバリ不能なエラーが発生したときに実行する、カスタム・コマンドまたは一連のセミコロン区切りのコマンドを設定します。文字列に空白が含まれている場合は、引用符で囲む必要があります。
.sp
次の例では、\fB\-XX:OnError\fRオプションを使用してコア・イメージを作成するために\fBgcore\fRコマンドを実行する方法、およびリカバリ不能なエラーの場合にデバッガを起動してプロセスに接続する方法を示します(\fB%p\fRは現在のプロセスを指定します)。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:OnError="gcore %p;dbx \- %p"\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:OnOutOfMemoryError=\fIstring\fR
.RS 4
\fBOutOfMemoryError\fR例外が最初にスローされたときに実行する、カスタム・コマンドまたは一連のセミコロン区切りのコマンドを設定します。文字列に空白が含まれている場合は、引用符で囲む必要があります。コマンド文字列の例は、\fB\-XX:OnError\fRオプションの説明を参照してください。
.RE
.PP
\-XX:+PerfDataSaveToFile
.RS 4
有効な場合、Javaアプリケーションの終了時にjstat(1)バイナリ・データを保存します。このバイナリ・データは\fBhsperfdata_\fR\fI<pid>\fRという名前のファイルに保存されます。\fI<pid>\fRは、実行したJavaアプリケーションのプロセス識別子です。次のように\fBjstat\fRを使用して、このファイルに含まれるパフォーマンス・データを表示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjstat \-class file:///\fR\fB\fI<path>\fR\fR\fB/hsperfdata_\fR\fB\fI<pid>\fR\fR
\fBjstat \-gc file:///\fR\fB\fI<path>\fR\fR\fB/hsperfdata_\fR\fB\fI<pid>\fR\fR
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+PrintCommandLineFlags
.RS 4
コマンド行に表示された、人間工学的に選択したJVMフラグの印刷を有効にします。これは、ヒープ領域サイズや選択されたガベージ・コレクタなど、JVMによって設定されたエルゴノミック値を確認する場合に役立ちます。デフォルトでは、このオプションは無効であり、フラグは印刷されません。
.RE
.PP
\-XX:+PrintNMTStatistics
.RS 4
ネイティブ・メモリーのトラッキングが有効な場合に、JVMの終了時に収集されたネイティブ・メモリーのトラッキング・データの印刷を有効にします(\fB\-XX:NativeMemoryTracking\fRを参照してください)。デフォルトでは、このオプションは無効であり、ネイティブ・メモリーのトラッキング・データは印刷されません。
.RE
.PP
\-XX:+RelaxAccessControlCheck
.RS 4
ベリファイア内のアクセス制御チェックの量を減らします。デフォルトでは、このオプションは無効になっており、最近のバイトコード・バージョンを使用したクラスには、これは無視されます(つまり、無効として処理されます)。古いバージョンのバイトコードを使用したクラスには、これを有効化できます。
.RE
.PP
\-XX:+ResourceManagement
.RS 4
アプリケーションの実行中にリソース管理の使用を有効にします。
.sp
これは商用機能で、\fB\-XX:+UnlockCommercialFeatures\fRオプションを次のように指定することも必要になります。
.sp
\fBjava \-XX:+UnlockCommercialFeatures \-XX:+ResourceManagement\fR
.RE
.PP
\-XX:ResourceManagementSampleInterval=\fIvalue\fR (ミリ秒)
.RS 4
ミリ秒単位でリソース管理測定のサンプリング間隔を制御するパラメータを設定します。
.sp
リソース管理が有効な場合のみ(つまり、\fB\-XX:+ResourceManagement\fRオプションが指定されている場合)、このオプションを使用できます。
.RE
.PP
\-XX:+ShowMessageBoxOnError
.RS 4
JVMでリカバリ不能なエラーが発生した場合、ダイアログ・ボックスの表示を有効にします。これにより、JVMにデバッガを接続してエラーの原因を調査できるように、JVMを終了しないようにして、プロセスをアクティブなままにします。デフォルトでは、このオプションは無効です。
.RE
.PP
\-XX:StartFlightRecording=\fIparameter\fR=\fIvalue\fR
.RS 4
JavaアプリケーションのJFR記録を開始します。このオプションは、実行中に記録を開始する\fBJFR\&.start\fR診断コマンドと同等です。JFR記録の開始時に、次のパラメータを設定できます。
.PP
compress={true|false}
.RS 4
\fBgzip\fRファイル圧縮ユーティリティを使用して、ディスクでJFR記録ログ・ファイル(タイプはJFR)を圧縮するかどうかを指定します。このパラメータは、\fBfilename\fRパラメータが指定されている場合のみ有効です。デフォルトでは、\fBfalse\fR
(記録を圧縮しない)に設定されています。圧縮を有効にするには、パラメータを\fBtrue\fRに設定します。
.RE
.PP
defaultrecording={true|false}
.RS 4
記録が連続的なバックグラウンド記録か、または限定された時間に対して実行されるかを指定します。デフォルトでは、このパラメータは\fBfalse\fR
(記録は限定された時間に対して実行される)に設定されています。記録を連続的に実行するには、パラメータを\fBtrue\fRに設定します。
.RE
.PP
delay=\fItime\fR
.RS 4
Javaアプリケーションの起動時間と記録開始との間の遅延を指定します。時間を指定する際に、秒単位の場合は\fBs\fR、分の場合は\fBm\fR、時間の場合は\fBh\fR、日の場合は\fBd\fRを追加します(たとえば、\fB10m\fRという指定は10分を意味します)。デフォルトでは、遅延は存在せず、このパラメータは0に設定されています。
.RE
.PP
duration=\fItime\fR
.RS 4
記録の期間を指定します。時間を指定する際に、秒単位の場合は\fBs\fR、分の場合は\fBm\fR、時間の場合は\fBh\fR、日の場合は\fBd\fRを追加します(たとえば、\fB5h\fRという指定は5時間を意味します)。デフォルトでは、期間は制限されず、このパラメータは0に設定されています。
.RE
.PP
filename=\fIpath\fR
.RS 4
JFR記録ログ・ファイルのパスと名前を指定します。
.RE
.PP
name=\fIidentifier\fR
.RS 4
JFR記録の識別子を指定します。デフォルトでは、\fBRecording x\fRに設定されています。
.RE
.PP
maxage=\fItime\fR
.RS 4
デフォルト記録のディスク・データの最大保持時間を指定します。時間を指定する際に、秒単位の場合は\fBs\fR、分の場合は\fBm\fR、時間の場合は\fBh\fR、日の場合は\fBd\fRを追加します(たとえば、\fB30s\fRという指定は30秒を意味します)。デフォルトでは、最大保持時間は15分(\fB15m\fR)に設定されています。
.RE
.PP
maxsize=\fIsize\fR
.RS 4
デフォルト記録のディスク・データの最大保持サイズ(バイト単位)を指定します。サイズをKBで指定する場合は\fBk\fRまたは\fBK\fR、サイズをMBで指定する場合は\fBm\fRまたは\fBM\fR、サイズをGBで指定する場合は\fBg\fRまたは\fBG\fRを追加します。デフォルトでは、ディスク・データの最大サイズは制限されず、このパラメータは0に設定されています。
.RE
.PP
settings=\fIpath\fR
.RS 4
イベント設定ファイル(タイプはJFC)のパスと名前を指定します。デフォルトでは、\fBJAVA_HOME/jre/lib/jfr\fRにある\fBdefault\&.jfc\fRファイルが使用されます。
.RE
.sp
カンマで区切って、複数のパラメータの値を指定できます。たとえば、記録を現在の作業ディレクトリのtest\&.jfrに保存し、ログ・ファイルを圧縮するようJFRに指示するには、次のように指定します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:StartFlightRecording=filename=test\&.jfr,compress=true\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:ThreadStackSize=\fIsize\fR
.RS 4
スレッドのスタック・サイズ(バイト単位)を設定します。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。デフォルト値はプラットフォームによって異なります。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Linux/ARM (32ビット): 320KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Linux/i386 (32ビット): 320KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Linux/x64 (64ビット): 1024KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
OS X (64ビット): 1024KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Oracle Solaris/i386 (32ビット): 320KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Oracle Solaris/x64 (64ビット): 1024KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Windows: 仮想メモリーによって異なります。
.RE
.sp
次の例では、スレッド・スタック・サイズを異なる単位で1024KBに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:ThreadStackSize=1m\fR
\fB\-XX:ThreadStackSize=1024k\fR
\fB\-XX:ThreadStackSize=1048576\fR
 
.fi
.if n \{\
.RE
.\}
このオプションは\fB\-Xss\fRと同等です。
.RE
.PP
\-XX:+TraceClassLoading
.RS 4
クラスがロードされるときのクラスのトレースを有効にします。デフォルトでは、このオプションは無効であり、クラスはトレースされません。
.RE
.PP
\-XX:+TraceClassLoadingPreorder
.RS 4
クラスが参照される順序で、ロードされたすべてのクラスのトレースを有効にします。デフォルトでは、このオプションは無効であり、クラスはトレースされません。
.RE
.PP
\-XX:+TraceClassResolution
.RS 4
定数プールの解決のトレースを有効にします。デフォルトでは、このオプションは無効であり、定数プールの解決はトレースされません。
.RE
.PP
\-XX:+TraceClassUnloading
.RS 4
クラスがアンロードされるときのクラスのトレースを有効にします。デフォルトでは、このオプションは無効であり、クラスはトレースされません。
.RE
.PP
\-XX:+TraceLoaderConstraints
.RS 4
ローダー制約の記録のトレースを有効にします。デフォルトでは、このオプションは無効であり、ローダー制約の記録は追跡されません。
.RE
.PP
\-XX:+UnlockCommercialFeatures
.RS 4
商用機能の使用を有効にします。商用機能は、http://www\&.oracle\&.com/technetwork/java/javase/terms/products/index\&.htmlにある\fIJava SE製品\fRページで定義されているように、Oracle Java SE AdvancedまたはOracle Java SE Suiteパッケージに付属しています。
.sp
デフォルトでは、このオプションは無効であり、JVMは商用機能なしで実行されます。JVMプロセスが有効化された後、そのプロセスの使用を無効にすることはできません。
.RE
.PP
\-XX:+UseAltSigs
.RS 4
JVM内部シグナルの\fBSIGUSR1\fRおよび\fBSIGUSR2\fRのかわりに、代替シグナルの使用を有効にします。デフォルトでは、このオプションは無効であり、代替シグナルは使用されません。このオプションは\fB\-Xusealtsigs\fRと同等です。
.RE
.PP
\-XX:\-UseBiasedLocking
.RS 4
バイアス・ロックの使用を無効にします。かなりの量の非競合の同期化がある一部のアプリケーションは、このフラグを有効にすると大幅な高速化が実現しますが、特定のパターンのロックがあるアプリケーションは、速度が低下することがあります。バイアス・ロックの方法の詳細は、http://www\&.oracle\&.com/technetwork/java/tuning\-139912\&.html#section4\&.2\&.5にあるJavaチューニングのホワイト・ペーパーの例を参照してください。
.sp
デフォルトでは、このオプションは有効になっています。
.RE
.PP
\-XX:\-UseCompressedOops
.RS 4
圧縮されたポインタの使用を無効にします。デフォルトではこのオプションが有効であり、Javaヒープ・サイズが32GBより小さい場合に圧縮ポインタが使用されます。このオプションを有効にすると、オブジェクト参照は、64ビットのポインタではなく32ビットのオフセットとして表され、これにより、32GBより小さいJavaヒープ・サイズのアプリケーションの実行時に、通常、パフォーマンスが向上します。このオプションは、64ビットのJVMでのみ機能します。
.sp
Javaヒープ・サイズが32GBより大きい場合にも圧縮ポインタを使用できます。\fB\-XX:ObjectAlignmentInBytes\fRオプションを参照してください。
.RE
.PP
\-XX:+UseHugeTLBFS
.RS 4
Linux用のこのオプションは、\fB\-XX:+UseLargePages\fRを指定するのと同じです。このオプションは、デフォルトでは無効になっています。このオプションは、メモリーの予約時にすべてのラージ・ページを事前に割り当てます。そのため、JVMはラージ・ページ・メモリー領域を動的に拡張または縮小できません。この動作を行う場合は、\fB\-XX:UseTransparentHugePages\fRを参照してください。
.sp
詳細は、"ラージ・ページ"を参照してください。
.RE
.PP
\-XX:+UseLargePages
.RS 4
ラージ・ページのメモリーの使用を有効にします。デフォルトでは、このオプションは無効であり、ラージ・ページのメモリーは使用されません。
.sp
詳細は、"ラージ・ページ"を参照してください。
.RE
.PP
\-XX:+UseMembar
.RS 4
スレッドの状態の遷移でメンバーの発行を有効にします。このオプションは、有効になっているARMサーバーを除くすべてのプラットフォーム上で、デフォルトでは無効になっています。(ARMサーバーでこのオプションを無効にしないことをお薦めします。)
.RE
.PP
\-XX:+UsePerfData
.RS 4
\fBperfdata\fR機能を有効にします。このオプションはデフォルトで有効になっており、JVMのモニタリングおよびパフォーマンス・テストが可能になります。これを無効にすると、\fBhsperfdata_userid\fRディレクトリの作成を抑制します。\fBperfdata\fR機能を無効にするには、\fB\-XX:\-UsePerfData\fRを指定します。
.RE
.PP
\-XX:+UseTransparentHugePages
.RS 4
Linuxでは、動的に拡張または縮小できるラージ・ページの使用を有効化してください。このオプションは、デフォルトでは無効になっています。OSが他のページを移動してヒュージ・ページを作成するため、透過的ヒュージ・ページでパフォーマンスの問題が検出される場合があります。このオプションは試験的に使用できます。
.sp
詳細は、"ラージ・ページ"を参照してください。
.RE
.PP
\-XX:+AllowUserSignalHandlers
.RS 4
アプリケーションによるシグナル・ハンドラのインストールを有効にします。デフォルトでは、このオプションは無効であり、アプリケーションはシグナル・ハンドラをインストールすることは許可されていません。
.RE
.SS "高度なJITコンパイラ・オプション"
.PP
これらのオプションは、Java HotSpot VMで実行される動的なjust\-in\-time (JIT)コンパイラを制御します。
.PP
\-XX:+AggressiveOpts
.RS 4
積極的なパフォーマンス最適化機能の使用を有効にします。これは今後のリリースでデフォルトになる予定です。デフォルトでは、このオプションは無効であり、試験的なパフォーマンス機能は使用されません。
.RE
.PP
\-XX:AllocateInstancePrefetchLines=\fIlines\fR
.RS 4
インスタンス割当てポインタの前にプリフェッチする行数を設定します。デフォルトでは、プリフェッチする行数は1に設定されています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:AllocateInstancePrefetchLines=1\fR
 
.fi
.if n \{\
.RE
.\}
Java HotSpot Server VMのみが、このオプションをサポートしています。
.RE
.PP
\-XX:AllocatePrefetchDistance=\fIsize\fR
.RS 4
オブジェクト割当てのプリフェッチ距離のサイズ(バイト単位)を設定します。新規オブジェクトの値で書き込もうとするメモリーは、最後に割り当てられたオブジェクトのアドレスから、この距離までプリフェッチされます。各Javaスレッドには独自の割当てポイントがあります。
.sp
負の値は、プリフェッチ距離はプラットフォームに基づいて選択されることを示します。正の値は、プリフェッチするバイト数です。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。デフォルト値は\-1に設定されています。
.sp
次の例では、プリフェッチ距離を1024バイトに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:AllocatePrefetchDistance=1024\fR
 
.fi
.if n \{\
.RE
.\}
Java HotSpot Server VMのみが、このオプションをサポートしています。
.RE
.PP
\-XX:AllocatePrefetchInstr=\fIinstruction\fR
.RS 4
割当てポインタの前にプリフェッチするプリフェッチ命令を設定します。Java HotSpot Server VMのみが、このオプションをサポートしています。使用可能な値は0から3までです。値の背後にある実際の命令は、プラットフォームによって異なります。デフォルトでは、プリフェッチ命令は0に設定されています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:AllocatePrefetchInstr=0\fR
 
.fi
.if n \{\
.RE
.\}
Java HotSpot Server VMのみが、このオプションをサポートしています。
.RE
.PP
\-XX:AllocatePrefetchLines=\fIlines\fR
.RS 4
コンパイルされたコードで生成されるプリフェッチ命令を使用して、最後のオブジェクト割当て後にロードするキャッシュ行数を設定します。最後に割り当てられたオブジェクトがインスタンスの場合は、デフォルト値は1になり、配列の場合は3になります。
.sp
次の例では、ロードされるキャッシュ行数を5に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:AllocatePrefetchLines=5\fR
 
.fi
.if n \{\
.RE
.\}
Java HotSpot Server VMのみが、このオプションをサポートしています。
.RE
.PP
\-XX:AllocatePrefetchStepSize=\fIsize\fR
.RS 4
順次プリフェッチ命令のステップ・サイズ(バイト単位)を設定します。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。デフォルトでは、ステップ・サイズは16バイトに設定されています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:AllocatePrefetchStepSize=16\fR
 
.fi
.if n \{\
.RE
.\}
Java HotSpot Server VMのみが、このオプションをサポートしています。
.RE
.PP
\-XX:AllocatePrefetchStyle=\fIstyle\fR
.RS 4
プリフェッチ命令に生成されるコード・スタイルを設定します。\fIstyle\fR引数は、0から3までの整数です。
.PP
0
.RS 4
プリフェッチ命令を生成しません。
.RE
.PP
1
.RS 4
各割当ての後で、プリフェッチ命令を実行します。これはデフォルトのパラメータです。
.RE
.PP
2
.RS 4
スレッド・ローカルな割当てブロック(TLAB)ウォーターマーク・ポインタを使用して、プリフェッチ命令を実行するタイミングを決定します。
.RE
.PP
3
.RS 4
割当てプリフェッチ用のSPARCでBIS命令を使用します。
.RE
.sp
Java HotSpot Server VMのみが、このオプションをサポートしています。
.RE
.PP
\-XX:+BackgroundCompilation
.RS 4
バックグラウンド・コンパイルを有効にします。このオプションはデフォルトで有効になっています。バックグラウンド・コンパイルを無効にするには、\fB\-XX:\-BackgroundCompilation\fRを指定します(これは\fB\-Xbatch\fRを指定するのと同等です)。
.RE
.PP
\-XX:CICompilerCount=\fIthreads\fR
.RS 4
コンパイルに使用するコンパイラ・スレッドの数を設定します。デフォルトでは、スレッド数は、サーバーJVMの場合は2、クライアントJVMの場合は1に設定されており、層コンパイルが使用されている場合、コア数に合せて増減します。次の例では、スレッドの数を2に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CICompilerCount=2\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:CodeCacheMinimumFreeSpace=\fIsize\fR
.RS 4
コンパイルに必要な最小空き領域(バイト単位)を設定します。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。最小空き領域より少ない領域しか残っていない場合、コンパイルは停止します。デフォルトでは、このオプションは500KBに設定されています。次の例では、最小空き領域を1024MBに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CodeCacheMinimumFreeSpace=1024m\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:CompileCommand=\fIcommand\fR,\fImethod\fR[,\fIoption\fR]
.RS 4
メソッドで実行するコマンドを指定します。たとえば、コンパイル元から\fBString\fRクラスの\fBindexOf()\fRメソッドを実行するには、次を使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileCommand=exclude,java/lang/String\&.indexOf\fR
 
.fi
.if n \{\
.RE
.\}
スラッシュ(\fB/\fR)で区切られたすべてのパッケージおよびサブパッケージを含む、完全クラス名を指定します。切取りと貼付けの操作を容易にするために、\fB\-XX:+PrintCompilation\fRオプションおよび\fB\-XX:+LogCompilation\fRオプションによって生成されるメソッド名の形式を使用することもできます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileCommand=exclude,java\&.lang\&.String::indexOf\fR
 
.fi
.if n \{\
.RE
.\}
署名なしでメソッドを指定すると、コマンドは指定した名前を持つすべてのメソッドに適用されます。ただし、クラス・ファイル形式でメソッドの署名を指定することもできます。この場合、引数を引用符で囲む必要があり、囲まないと、シェルによりセミコロンがコマンドの終了として扱われます。たとえば、コンパイル元から\fBString\fRクラスの\fBindexOf(String)\fRメソッドのみ除外するには、次を使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileCommand="exclude,java/lang/String\&.indexOf,(Ljava/lang/String;)I"\fR
 
.fi
.if n \{\
.RE
.\}
また、クラス名およびメソッド名にワイルドカードとしてアスタリスク(*)を使用できます。たとえば、コンパイル元からすべてのクラスのすべての\fBindexOf()\fRメソッドを除外するには、次を使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileCommand=exclude,*\&.indexOf\fR
 
.fi
.if n \{\
.RE
.\}
カンマとピリオドは空白の別名で、これにより、シェルを介してコンパイラ・コマンドを渡すことが容易になります。引数を引用符で囲むことで、空白をセパレータとして使用して\fB\-XX:CompileCommand\fRに引数を渡すことができます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileCommand="exclude java/lang/String indexOf"\fR
 
.fi
.if n \{\
.RE
.\}
\fB\-XX:CompileCommand\fRオプションを使用してコマンド行で渡されたコマンドを解析した後に、JITコンパイラは\fB\&.hotspot_compiler\fRファイルからコマンドを読み取ります。このファイルにコマンドを追加するか、または\fB\-XX:CompileCommandFile\fRオプションを使用して別のファイルを指定することができます。
.sp
複数のコマンドを追加するには、\fB\-XX:CompileCommand\fRオプションを複数回指定するか、または改行セパレータ(\fB\en\fR)を使用して各引数を区切ります。次のコマンドを使用できます。
.PP
break
.RS 4
指定したメソッドのコンパイルの最初に停止するために、JVMのデバッグ時のブレークポイントを設定します。
.RE
.PP
compileonly
.RS 4
指定したメソッドを除いたすべてのメソッドを、コンパイルから除外します。別の方法として、\fB\-XX:CompileOnly\fRオプションを使用して複数のメソッドを指定できます。
.RE
.PP
dontinline
.RS 4
指定したメソッドをインライン化しないようにします。
.RE
.PP
exclude
.RS 4
指定したメソッドをコンパイルから除外します。
.RE
.PP
help
.RS 4
\fB\-XX:CompileCommand\fRオプションのヘルプ・メッセージを印刷します。
.RE
.PP
inline
.RS 4
指定したメソッドをインライン化しようとします。
.RE
.PP
log
.RS 4
指定したメソッドを除くすべてのメソッドに対して、(\fB\-XX:+LogCompilation\fRオプションを使用して)コンパイル・ロギングを除外します。デフォルトでは、コンパイルされたすべてのメソッドにロギングが実行されます。
.RE
.PP
option
.RS 4
このコマンドは、最後の引数(\fIoption\fR)のかわりに、指定したメソッドにJITコンパイル・オプションを渡すために使用できます。コンパイル・オプションは、メソッド名の後の末尾に設定されます。たとえば、\fBStringBuffer\fRクラスの\fBappend()\fRメソッドに対して\fBBlockLayoutByFrequency\fRオプションを有効にするには、次を使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileCommand=option,java/lang/StringBuffer\&.append,BlockLayoutByFrequency\fR
 
.fi
.if n \{\
.RE
.\}
カンマまたは空白で区切って、複数のコンパイル・オプションを指定できます。
.RE
.PP
print
.RS 4
指定したメソッドのコンパイル後に生成されたアセンブラ・コードを出力します。
.RE
.PP
quiet
.RS 4
コンパイル・コマンドを出力しません。デフォルトでは、\fB\-XX:CompileCommand\fRオプションを使用して指定したコマンドが出力されます。たとえば、\fBString\fRクラスの\fBindexOf()\fRメソッドのコンパイルから除外する場合、次が標準出力に出力されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBCompilerOracle: exclude java/lang/String\&.indexOf\fR
 
.fi
.if n \{\
.RE
.\}
他の\fB\-XX:CompileCommand\fRオプションの前に\fB\-XX:CompileCommand=quiet\fRオプションを指定することによって、これを抑制できます。
.RE
.RE
.PP
\-XX:CompileCommandFile=\fIfilename\fR
.RS 4
JITコンパイラ・コマンドの読取り元のファイルを設定します。デフォルトでは、JITコンパイラによって実行されるコマンドを格納するために、\fB\&.hotspot_compiler\fRファイルが使用されます。
.sp
コマンド・ファイルの各行は、コマンドが使用されるコマンド、クラス名およびメソッド名を表します。たとえば、次の行は、\fBString\fRクラスの\fBtoString()\fRメソッドに対してアセンブリ・コードを出力します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBprint java/lang/String toString\fR
 
.fi
.if n \{\
.RE
.\}
メソッドで実行するJITコンパイラのコマンドの指定の詳細は、\fB\-XX:CompileCommand\fRオプションを参照してください。
.RE
.PP
\-XX:CompileOnly=\fImethods\fR
.RS 4
コンパイルを制限する(カンマで区切られた)メソッドのリストを設定します。指定したメソッドのみがコンパイルされます。完全クラス名(パッケージおよびサブパッケージを含む)で各メソッドを指定します。たとえば、\fBString\fRクラスの\fBlength()\fRメソッドおよび\fBList\fRクラスの\fBsize()\fRメソッドのみをコンパイルするには、次を使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileOnly=java/lang/String\&.length,java/util/List\&.size\fR
 
.fi
.if n \{\
.RE
.\}
スラッシュ(\fB/\fR)で区切られたすべてのパッケージおよびサブパッケージを含む、完全クラス名を指定します。切取りと貼付けの操作を容易にするために、\fB\-XX:+PrintCompilation\fRオプションおよび\fB\-XX:+LogCompilation\fRオプションによって生成されるメソッド名の形式を使用することもできます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileOnly=java\&.lang\&.String::length,java\&.util\&.List::size\fR
 
.fi
.if n \{\
.RE
.\}
ワイルドカードはサポートされていませんが、クラス名またはパッケージ名だけを指定してクラスまたはパッケージのすべてのメソッドをコンパイルすることも、メソッドだけを指定して任意のクラスのこの名前を持つメソッドをコンパイルすることもできます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileOnly=java/lang/String\fR
\fB\-XX:CompileOnly=java/lang\fR
\fB\-XX:CompileOnly=\&.length\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:CompileThreshold=\fIinvocations\fR
.RS 4
コンパイル前に解釈されたメソッド呼出しの数を設定します。デフォルトでは、サーバーJVMでは、JITコンパイラは、10,000の解釈されたメソッド呼出しを実行して、効率的なコンパイルのための情報を収集します。クライアントJVMの場合、デフォルト設定は1,500呼出しです。層コンパイルが有効な場合、このオプションは無視されます。オプション\fB\-XX:+TieredCompilation\fRを参照してください。次の例では、解釈されたメソッド呼出しの数を5,000に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileThreshold=5000\fR
 
.fi
.if n \{\
.RE
.\}
\fB\-Xcomp\fRオプションを指定して、コンパイルの前に、Javaメソッドの解釈を完全に無効にすることができます。
.RE
.PP
\-XX:+DoEscapeAnalysis
.RS 4
エスケープ分析の使用を有効にします。このオプションはデフォルトで有効になっています。エスケープ分析の使用を無効にするには、\fB\-XX:\-DoEscapeAnalysis\fRを指定します。Java HotSpot Server VMのみが、このオプションをサポートしています。
.RE
.PP
\-XX:InitialCodeCacheSize=\fIsize\fR
.RS 4
初期コード・キャッシュ・サイズ(バイト単位)を設定します。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。デフォルト値は500KBに設定されています。初期コード・キャッシュ・サイズをシステムの最小メモリー・ページ・サイズより小さくしないでください。次の例では、初期コード・キャッシュ・サイズを32KBに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:InitialCodeCacheSize=32k\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+Inline
.RS 4
メソッドのインライン化を有効にします。このオプションは、パフォーマンスを向上させるためにデフォルトで有効になっています。メソッドのインライン化を無効にするには、\fB\-XX:\-Inline\fRを指定します。
.RE
.PP
\-XX:InlineSmallCode=\fIsize\fR
.RS 4
インライン化が必要なコンパイルされたメソッドの最大コード・サイズ(バイト単位)を設定します。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。指定したサイズより小さいサイズのコンパイルされたメソッドのみが、インライン化されます。デフォルトでは、最大コード・サイズは1000バイトに設定されています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:InlineSmallCode=1000\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+LogCompilation
.RS 4
現在の作業ディレクトリにある\fBhotspot\&.log\fRという名前のファイルへのコンパイル・アクティビティのロギングを有効にします。\fB\-XX:LogFile\fRオプションを使用して、異なるログ・ファイル・パスと名前を指定できます。
.sp
デフォルトでは、このオプションは無効であり、コンパイル・アクティビティは記録されません。\fB\-XX:+LogCompilation\fRオプションは、診断JVMオプションのロックを解除する\fB\-XX:UnlockDiagnosticVMOptions\fRオプションとともに使用する必要があります。
.sp
\fB\-XX:+PrintCompilation\fRオプションを使用して、メソッドをコンパイルするたびに、コンソールに出力されたメッセージを含む詳細な診断出力を有効化できます。
.RE
.PP
\-XX:MaxInlineSize=\fIsize\fR
.RS 4
インライン化するメソッドの最大バイトコード・サイズ(バイト単位)を設定します。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。デフォルトでは、最大バイトコード・サイズは35バイトに設定されています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxInlineSize=35\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MaxNodeLimit=\fInodes\fR
.RS 4
単一のメソッドのコンパイル時に使用されるノードの最大数を設定します。デフォルトでは、ノードの最大数は65,000に設定されています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxNodeLimit=65000\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MaxTrivialSize=\fIsize\fR
.RS 4
インライン化する単純メソッドの最大バイトコード・サイズ(バイト単位)を設定します。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。デフォルトでは、単純メソッドの最大バイトコード・サイズは6バイトに設定されています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxTrivialSize=6\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+OptimizeStringConcat
.RS 4
\fBString\fR連結操作の最適化を有効にします。このオプションはデフォルトで有効になっています。\fBString\fR連結操作の最適化を無効にするには、\fB\-XX:\-OptimizeStringConcat\fRを指定します。Java HotSpot Server VMのみが、このオプションをサポートしています。
.RE
.PP
\-XX:+PrintAssembly
.RS 4
外部の\fBdisassembler\&.so\fRライブラリを使用して、バイトコード化されたネイティブのメソッドのアセンブリ・コードの出力を有効にします。これにより、生成されたコードを表示することができ、パフォーマンスの問題の診断に役立ちます。
.sp
デフォルトでは、このオプションは無効であり、アセンブリ・コードは印刷されません。\fB\-XX:+PrintAssembly\fRオプションは、診断JVMオプションのロックを解除する\fB\-XX:UnlockDiagnosticVMOptions\fRオプションとともに使用する必要があります。
.RE
.PP
\-XX:+PrintCompilation
.RS 4
メソッドをコンパイルするたびに、コンソールにメッセージを出力することによって、JVMからの詳細な診断出力を有効にします。これにより、実際にコンパイルされるメソッドを確認できます。デフォルトでは、このオプションは無効であり、診断出力は印刷されません。
.sp
\fB\-XX:+LogCompilation\fRオプションを使用して、コンパイル・アクティビティをファイルに記録することもできます。
.RE
.PP
\-XX:+PrintInlining
.RS 4
インライン化の決定内容の出力を有効にします。これにより、インライン化されるメソッドを確認できます。
.sp
デフォルトでは、このオプションは無効であり、インライン化情報は出力されません。\fB\-XX:+PrintInlining\fRオプションは、診断JVMオプションのロックを解除する\fB\-XX:+UnlockDiagnosticVMOptions\fRオプションとともに使用する必要があります。
.RE
.PP
\-XX:ReservedCodeCacheSize=\fIsize\fR
.RS 4
JITコンパイルされたコードの最大コード・キャッシュ・サイズ(バイト単位)を設定します。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。このオプションは2GBの制限があります。そうでない場合は、エラーが生成されます。最大コード・キャッシュ・サイズを初期コード・キャッシュ・サイズより小さくしないでください。\fB\-XX:InitialCodeCacheSize\fRオプションを参照してください。このオプションは\fB\-Xmaxjitcodesize\fRと同等です。
.RE
.PP
\-XX:RTMAbortRatio=\fIabort_ratio\fR
.RS 4
RTM中止率は、すべての実行済RTMトランザクションに対するパーセンテージ(%)として指定されます。中止されたトランザクション数がこの率を超えた場合、コンパイルされたコードが非最適化されます。この率は、\fB\-XX:+UseRTMDeopt\fRオプションが有効な場合に使用されます。このオプションのデフォルト値は50です。つまり、すべてのトランザクションの50%が中止された場合、コンパイルされたコードが非最適化されます。
.RE
.PP
\-XX:RTMRetryCount=\fInumber_of_retries\fR
.RS 4
中止またはビジーの場合、RTMロック・コードは、標準のロック・メカニズムにフォールバックする前にこのオプションによって指定された回数再試行されます。このオプションのデフォルト値は5です。\fB\-XX:UseRTMLocking\fRオプションを有効化する必要があります。
.RE
.PP
\-XX:+TieredCompilation
.RS 4
層コンパイルの使用を有効にします。デフォルトでは、このオプションは有効になっています。Java HotSpot Server VMのみが、このオプションをサポートしています。
.RE
.PP
\-XX:+UseAES
.RS 4
Intel、AMDおよびSPARCハードウェアに対して、ハードウェアベースのAES組込みを有効化します。Intel Westmere (2010以降)、AMD Bulldozer (2011以降)およびSPARC (T4以降)が、サポートされているハードウェアです。UseAESは、UseAESIntrinsicsとともに使用します。
.RE
.PP
\-XX:+UseAESIntrinsics
.RS 4
UseAESとUseAESIntrinsicsフラグはデフォルトで有効化されており、Java HotSpot Server VM 32ビットおよび64ビットに対してのみサポートされています。ハードウェアベースのAES組込みを無効化するには、\fB\-XX:\-UseAES \-XX:\-UseAESIntrinsics\fRを指定します。たとえば、ハードウェアAESを有効化するには、次のフラグを使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:+UseAES \-XX:+UseAESIntrinsics\fR
 
.fi
.if n \{\
.RE
.\}
32ビットおよび64ビットに対してUseAESおよびUseAESIntrinsicsフラグをサポートするには、\fB\-server\fRオプションを使用してJava HotSpot Server VMを選択します。これらのフラグは、クライアントVMではサポートされていません。
.RE
.PP
\-XX:+UseCodeCacheFlushing
.RS 4
コンパイラをシャットダウンする前に、コード・キャッシュのフラッシュを有効にします。このオプションはデフォルトで有効になっています。コンパイラをシャットダウンする前にコード・キャッシュのフラッシュを無効にするには\fB\-XX:\-UseCodeCacheFlushing\fRを指定します。
.RE
.PP
\-XX:+UseCondCardMark
.RS 4
カード表の更新前に、カードがすでにマークされているかどうかのチェックを有効にします。このオプションは、デフォルトでは無効になっており、複数のソケットを持つマシン上でのみ使用する必要があります。これにより、同時操作にかなり依存しているJavaアプリケーションのパフォーマンスが向上します。Java HotSpot Server VMのみが、このオプションをサポートしています。
.RE
.PP
\-XX:+UseRTMDeopt
.RS 4
中止率に応じて、RTMロックを自動調整します。この率は、\fB\-XX:RTMAbortRatio\fRオプションによって指定されます。中止されたトランザクション数が中止率を超えた場合、ロックを含むメソッドがすべてのロックで標準のロックとして非最適化および再コンパイルされます。このオプションは、デフォルトでは無効になっています。\fB\-XX:+UseRTMLocking\fRオプションを有効化する必要があります。
.RE
.PP
\-XX:+UseRTMLocking
.RS 4
フォールバック・ハンドラとして標準のロック・メカニズムを使用して、展開されたすべてのロックに対してRestricted Transactional Memory (RTM)ロック・コードを生成します。このオプションは、デフォルトでは無効になっています。RTMに関連するオプションは、Transactional Synchronization Extensions (TSX)をサポートするx86 CPU上のJava HotSpot Server VMに対してのみ使用可能です。
.sp
RTMは、x86命令セット拡張でマルチスレッド・アプリケーションの作成を容易にするIntelのTSXの一部です。RTMでは、新しい命令
\fBXBEGIN\fR、\fBXABORT\fR、\fBXEND\fRおよび\fBXTEST\fRが導入されています。\fBXBEGIN\fRおよび\fBXEND\fR命令は、トランザクションとして実行するための命令セットを囲みます。トランザクションの実行時に競合が見つからなかった場合、メモリーとレジスタの変更が、\fBXEND\fR命令で同時にコミットされます。\fBXABORT\fR命令ではトランザクションを明示的に中止でき、\fBXEND\fR命令では命令セットがトランザクション内で実行中かどうかを確認できます。
.sp
トランザクションのロックは、別のスレッドが同じトランザクションにアクセスしようとしたときに展開されます。したがって、そのトランザクションへのアクセスを最初にリクエストしなかったスレッドはブロックされます。RTMでは、トランザクションが中止または失敗した場合のために、フォールバックの操作セットを指定する必要があります。RTMロックとは、TSXのシステムに委譲されているロックです。
.sp
RTMにより、重要なリージョンにおいて衝突が少なく競合度の高いロックのパフォーマンスが向上されます(これは、複数のスレッドによって同時にアクセスできないコードです)。また、RTMにより、粗粒度ロックのパフォーマンスも向上されますが、一般的にマルチスレッド・アプリケーションでのパフォーマンスはよくありません。(粗粒度ロックとは、ロックの取得および解放のオーバーヘッドを最小化するために長い期間ロックを保持する戦略であり、一方、細粒度ロックとは必要な場合のみロックし可能なかぎり早期にロック解除することで最大限の並行処理の達成を試みる戦略です。)さらに、異なるスレッドによって使用されている軽度な競合ロックの場合、RTMにより、誤ったキャッシュ・ライン共有(キャッシュ・ライン・ピンポンとも呼ばれる)を削減できます。これは、異なるプロセッサからの複数のスレッドが異なるリソースにアクセスしている場合に発生しますが、リソースは同じキャッシュ・ラインを共有します。結果として、プロセッサは他のプロセッサのキャッシュ・ラインを繰り返し無効にし、これにより、キャッシュではなくメイン・メモリーからの読取りが強制されます。
.RE
.PP
\-XX:+UseSHA
.RS 4
SPARCハードウェアのSHA暗号化ハッシュ関数のハードウェアベースの組込みを有効にします。\fBUseSHA\fRは、\fBUseSHA1Intrinsics\fR、\fBUseSHA256Intrinsics\fRおよび\fBUseSHA512Intrinsics\fRオプションと組み合せて使用します。
.sp
\fBUseSHA\fRおよび\fBUseSHA*Intrinsics\fRフラグはデフォルトで有効であり、SPARC T4以上のJava HotSpot Server VM 64ビットでのみサポートされます。
.sp
SHA操作に対して\fBsun\&.security\&.provider\&.Sun\fRプロバイダを使用する場合のみ、この機能を適用できます。
.sp
すべてのハードウェアベースのSHA組込みを無効化するには、\fB\-XX:\-UseSHA\fRを指定してください。特定のSHA組込みのみ無効化するには、適切な対応するオプションを使用してください。たとえば、\fB\-XX:\-UseSHA256Intrinsics\fRなどです。
.RE
.PP
\-XX:+UseSHA1Intrinsics
.RS 4
SHA\-1暗号化ハッシュ関数の組込みを有効にします。
.RE
.PP
\-XX:+UseSHA256Intrinsics
.RS 4
SHA\-224およびSHA\-256暗号化ハッシュ関数の組込みを有効にします。
.RE
.PP
\-XX:+UseSHA512Intrinsics
.RS 4
SHA\-384およびSHA\-512暗号化ハッシュ関数の組込みを有効にします。
.RE
.PP
\-XX:+UseSuperWord
.RS 4
スカラー演算のスーパーワード演算への変換を有効にします。このオプションはデフォルトで有効になっています。スカラー演算のスーパーワード演算への変換を無効にするには、\fB\-XX:\-UseSuperWord\fRを指定します。Java HotSpot Server VMのみが、このオプションをサポートしています。
.RE
.SS "高度なサービスアビリティ・オプション"
.PP
これらのオプションは、システム情報を収集し、詳細なデバッグを実行する機能を提供します。
.PP
\-XX:+ExtendedDTraceProbes
.RS 4
パフォーマンスに影響を与える追加の\fBdtrace\fRツール・プローブを有効にします。デフォルトでは、このオプションは無効になっており、\fBdtrace\fRは標準プローブのみを実行します。
.RE
.PP
\-XX:+HeapDumpOnOutOfMemory
.RS 4
\fBjava\&.lang\&.OutOfMemoryError\fR例外がスローされた場合に、ヒープ・プロファイラ(HPROF)を使用して、現在のディレクトリ内のファイルへのJavaヒープのダンプを有効にします。\fB\-XX:HeapDumpPath\fRオプションを使用して、ヒープ・ダンプ・ファイルのパスおよび名前を明示的に設定できます。デフォルトでは、このオプションは無効であり、\fBOutOfMemoryError\fR例外がスローされた場合にヒープはダンプされません。
.RE
.PP
\-XX:HeapDumpPath=\fIpath\fR
.RS 4
\fB\-XX:+HeapDumpOnOutOfMemoryError\fRオプションが設定されている場合、ヒープ・プロファイラ(HPROF)が提供するヒープ・ダンプを書き込むパスおよびファイル名を設定します。デフォルトでは、このファイルは、現在の作業ディレクトリに作成され、名前は\fBjava_pid\fR\fIpid\fR\fB\&.hprof\fR
(\fIpid\fRはエラーの原因となったプロセスの識別子)になります。次の例では、デフォルトのファイルを明示的に設定する方法を示します(\fB%p\fRは現在のプロセスの識別子を表します)。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:HeapDumpPath=\&./java_pid%p\&.hprof\fR
 
.fi
.if n \{\
.RE
.\}
次の例では、ヒープ・ダンプ・ファイルを\fB/var/log/java/java_heapdump\&.hprof\fRに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:HeapDumpPath=/var/log/java/java_heapdump\&.hprof\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:LogFile=\fIpath\fR
.RS 4
ログ・データが書き込まれるパスおよびファイル名を設定します。デフォルトでは、ファイルは現在の作業ディレクトリに作成され、名前は\fBhotspot\&.log\fRです。
.sp
次の例では、ログ・ファイルを\fB/var/log/java/hotspot\&.log\fRに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:LogFile=/var/log/java/hotspot\&.log\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+PrintClassHistogram
.RS 4
\fB[Control]+[C]\fRイベント(\fBSIGTERM\fR)後にクラス・インスタンスのヒストグラムの印刷を有効にします。デフォルトでは、このオプションは無効です。
.sp
このオプションを設定すると、\fBjmap \-histo\fRコマンド、または\fBjcmd \fR\fIpid\fR\fB GC\&.class_histogram\fRコマンド(\fIpid\fRは現在のJavaプロセスの識別子)を実行する場合と同じになります。
.RE
.PP
\-XX:+PrintConcurrentLocks
.RS 4
印刷を有効にします ロック(次の後) \- イベント。デフォルトでは、このオプションは無効です。
.sp
\fB[Control]+[C]\fRイベント(\fBSIGTERM\fR)後に\fBjava\&.util\&.concurrent\fRロックの印刷を有効にします。デフォルトでは、このオプションは無効です。
.sp
このオプションを設定すると、\fBjstack \-l\fRコマンド、または\fBjcmd \fR\fIpid\fR\fB Thread\&.print \-l\fRコマンド(\fIpid\fRは現在のJavaプロセスの識別子)を実行する場合と同じになります。
.RE
.PP
\-XX:+UnlockDiagnosticVMOptions
.RS 4
JVMの診断を目的としたオプションをアンロックします。デフォルトでは、このオプションは無効であり、診断オプションは使用できません。
.RE
.SS "高度なガベージ・コレクション・オプション"
.PP
これらのオプションは、ガベージ・コレクション(GC)がJava HotSpot VMによってどのように実行されるかを制御します。
.PP
\-XX:+AggressiveHeap
.RS 4
Javaヒープの最適化を有効にします。これにより、コンピュータの構成(RAMおよびCPU)に基づいて、様々なパラメータが、メモリー割当てが集中した長時間実行ジョブに最適になるように設定されます。デフォルトでは、このオプションは無効であり、ヒープは最適化されません。
.RE
.PP
\-XX:+AlwaysPreTouch
.RS 4
JVMの初期化中にJavaヒープ上のすべてのページのタッチを有効にします。これにより、\fBmain()\fRメソッドの入力前に、すべてのページがメモリーに取得されます。このオプションは、物理メモリーにマップされたすべての仮想メモリーを含む長時間実行のシステムをシミュレートするテストで使用できます。デフォルトでは、このオプションは無効になっており、JVMヒープ領域がいっぱいになると、すべてのページがコミットされます。
.RE
.PP
\-XX:+CMSClassUnloadingEnabled
.RS 4
並行マークスイープ(CMS)ガベージ・コレクタを使用する場合に、アンロードするクラスを有効にします。このオプションはデフォルトで有効になっています。CMSガベージ・コレクタのクラス・アンロードを無効にするには、\fB\-XX:\-CMSClassUnloadingEnabled\fRを指定します。
.RE
.PP
\-XX:CMSExpAvgFactor=\fIpercent\fR
.RS 4
並行コレクション統計の指数平均を計算する際に、現在のサンプルを重み付けするために使用される時間の割合(0から100まで)を設定します。デフォルトでは、指数平均係数は25%に設定されています。次の例では、係数を15%に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CMSExpAvgFactor=15\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:CMSInitiatingOccupancyFraction=\fIpercent\fR
.RS 4
CMS収集サイクルを開始する古い世代の占有率(0から100まで)を設定します。デフォルト値は\-1に設定されています。負の値(デフォルトを含む)は、\fB\-XX:CMSTriggerRatio\fRが開始占有率の値を定義するために使用されることを意味します。
.sp
次の例では、占有率を20%に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CMSInitiatingOccupancyFraction=20\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+CMSScavengeBeforeRemark
.RS 4
CMSコメント・ステップの前にスカベンジの試行を有効にします。デフォルトでは、このオプションは無効です。
.RE
.PP
\-XX:CMSTriggerRatio=\fIpercent\fR
.RS 4
CMS収集サイクルが開始する前に割り当てられる\fB\-XX:MinHeapFreeRatio\fRによって指定される値の割合(0から100まで)を設定します。デフォルト値は80%に設定されています。
.sp
次の例では、占有率を75%に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CMSTriggerRatio=75\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:ConcGCThreads=\fIthreads\fR
.RS 4
並行GCに使用されるスレッドの数を設定します。デフォルト値は、JVMに使用できるCPUの数によって異なります。
.sp
たとえば、並行GCのスレッド数を2に設定するには、次のオプションを指定します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:ConcGCThreads=2\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+DisableExplicitGC
.RS 4
\fBSystem\&.gc()\fRの呼出しの処理を無効にするオプションを有効にします。このオプションはデフォルトで無効になっており、これは\fBSystem\&.gc()\fRへの呼出しが処理されることを意味します。\fBSystem\&.gc()\fRの呼出しの処理が無効になっている場合、JVMは必要に応じてGCを実行します。
.RE
.PP
\-XX:+ExplicitGCInvokesConcurrent
.RS 4
\fBSystem\&.gc()\fRリクエストを使用することによって、並行GCの呼出しを有効にします。このオプションはデフォルトで無効になっており、\fB\-XX:+UseConcMarkSweepGC\fRオプションとともに使用する場合のみ、有効にすることができます。
.RE
.PP
\-XX:+ExplicitGCInvokesConcurrentAndUnloadsClasses
.RS 4
\fBSystem\&.gc()\fRリクエストを使用し、並行GCサイクル中にクラスをアンロードすることによって、並行GCの呼出しを有効にします。このオプションはデフォルトで無効になっており、\fB\-XX:+UseConcMarkSweepGC\fRオプションとともに使用する場合のみ、有効にすることができます。
.RE
.PP
\-XX:G1HeapRegionSize=\fIsize\fR
.RS 4
ガベージファースト(G1)コレクタを使用する際にJavaヒープを細分化するリージョンのサイズを設定します。値には、1MBから32MBまでを指定できます。デフォルトのリージョン・サイズは、ヒープ・サイズに基づいて人間工学的に決定されます。
.sp
次の例では、細分化されたサイズを16MBに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:G1HeapRegionSize=16m\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+G1PrintHeapRegions
.RS 4
割り当てられたリージョンおよびG1コレクタによって再要求されたものに関する情報の印刷を有効にします。デフォルトでは、このオプションは無効です。
.RE
.PP
\-XX:G1ReservePercent=\fIpercent\fR
.RS 4
G1コレクタの昇格が失敗する可能性を減らすためのfalseの上限として予約されたヒープの割合(0から50まで)を設定します。デフォルトでは、このオプションは10%に設定されています。
.sp
次の例では、予約されたヒープを20%に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:G1ReservePercent=20\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:InitialHeapSize=\fIsize\fR
.RS 4
メモリー割当てプールの初期サイズ(バイト単位)を設定します。指定する値は、0、または1MBより大きい1024の倍数のいずれかにする必要があります。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。デフォルト値は、実行時にシステム構成に基づいて選択されます。http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/vm/gctuning/index\&.htmlの\fIJava SE HotSpot仮想マシンのガベージ・コレクション・チューニング・ガイド\fRのエルゴノミクスに関する項を参照してください。
.sp
次の例では、割り当てられたメモリーのサイズを様々な単位を使用して6MBに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:InitialHeapSize=6291456\fR
\fB\-XX:InitialHeapSize=6144k\fR
\fB\-XX:InitialHeapSize=6m\fR
 
.fi
.if n \{\
.RE
.\}
このオプションを0に設定した場合、初期サイズは、古い世代と若い世代に割り当てられたサイズの合計として設定されます。若い世代のヒープのサイズは、\fB\-XX:NewSize\fRオプションを使用して設定できます。
.RE
.PP
\-XX:InitialSurvivorRatio=\fIratio\fR
.RS 4
スループット・ガベージ・コレクタが使用するサバイバ領域の初期比を設定します(\fB\-XX:+UseParallelGC\fRおよび/または\fB\-XX:+UseParallelOldGC\fRオプションによって有効になります)。\fB\-XX:+UseParallelGC\fRオプションおよび\fB\-XX:+UseParallelOldGC\fRオプションを使用することによって、スループット・ガベージ・コレクタで適応サイズ指定をデフォルトで有効にします。初期値から始めて、アプリケーションの動作に従って、サバイバ領域がサイズ変更されます。(\fB\-XX:\-UseAdaptiveSizePolicy\fRオプションを使用して)適応サイズ指定を無効にした場合、\fB\-XX:SurvivorRatio\fRオプションを使用して、アプリケーションの実行全体のサバイバ領域のサイズを設定する必要があります。
.sp
次の式を使用して、若い世代のサイズ(Y)およびサバイバ領域の初期比(R)に基づいて、サバイバ領域の初期サイズ(S)を計算できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBS=Y/(R+2)\fR
 
.fi
.if n \{\
.RE
.\}
等式内の2は、2つのサバイバ領域を示します。サバイバ領域の初期比に指定する値を大きくすると、サバイバ領域の初期サイズは小さくなります。
.sp
デフォルトでは、サバイバ領域の初期比は8に設定されています。若い世代の領域サイズのデフォルト値(2MB)を使用した場合、サバイバ領域の初期サイズは0\&.2MBになります。
.sp
次の例では、サバイバ領域の初期比を4に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:InitialSurvivorRatio=4\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:InitiatingHeapOccupancyPercent=\fIpercent\fR
.RS 4
並行GCサイクルを開始するヒープ占有率(0から100まで)を設定します。これは、1つの世代のみ(たとえばG1ガベージ・コレクタなど)ではなく、ヒープ全体の占有に基づいて並行GCサイクルをトリガーするガベージ・コレクタによって使用されます。
.sp
デフォルトでは、開始値は45%に設定されています。値0は、GCサイクルが停止しないことを意味します。次の例では、開始ヒープ占有率を75%に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:InitiatingHeapOccupancyPercent=75\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MaxGCPauseMillis=\fItime\fR
.RS 4
最大GC休止時間(ミリ秒単位)のターゲットを設定します。これはソフト・ゴールのため、JVMは実現のために最善の努力をします。デフォルトでは、休止時間の最大値はありません。
.sp
次の例では、最大ターゲット休止時間を500ミリ秒に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxGCPauseMillis=500\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MaxHeapSize=\fIsize\fR
.RS 4
メモリー割当てプールの最大サイズ(バイト単位)を設定します。指定する値は、2MBより大きい1024の倍数にする必要があります。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。デフォルト値は、実行時にシステム構成に基づいて選択されます。サーバー・デプロイメントでは、\fB\-XX:InitialHeapSize\fRおよび\fB\-XX:MaxHeapSize\fRは通常同じ値に設定されます。http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/vm/gctuning/index\&.htmlの\fIJava SE HotSpot仮想マシンのガベージ・コレクション・チューニング・ガイド\fRのエルゴノミクスに関する項を参照してください。
.sp
次の例では、割り当てられたメモリーの許可される最大サイズを様々な単位を使用して80MBに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxHeapSize=83886080\fR
\fB\-XX:MaxHeapSize=81920k\fR
\fB\-XX:MaxHeapSize=80m\fR
 
.fi
.if n \{\
.RE
.\}
Oracle Solaris 7およびOracle Solaris 8 SPARCプラットフォームの場合のこの値の上限は、およそ4,000MBからオーバーヘッドの量を引いたものです。Oracle Solaris 2\&.6およびx86プラットフォームの場合の上限は、およそ2,000MBからオーバーヘッドの量を引いたものです。Linuxプラットフォームの場合の上限は、およそ2,000MBからオーバーヘッドの量を引いたものです。
.sp
\fB\-XX:MaxHeapSize\fRオプションは\fB\-Xmx\fRと同等です。
.RE
.PP
\-XX:MaxHeapFreeRatio=\fIpercent\fR
.RS 4
GCイベント後の空きヒープ領域の許可されている最大の割合(0から100まで)を設定します。空きヒープ領域がこの値を超えて拡大した場合、そのヒープは縮小します。デフォルトでは、この値は70%に設定されています。
.sp
次の例では、空きヒープの最大比率を75%に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxHeapFreeRatio=75\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MaxMetaspaceSize=\fIsize\fR
.RS 4
クラス・メタデータに割り当てることができるネイティブ・メモリーの最大量を設定します。デフォルトでは、このサイズは制限されていません。アプリケーションのメタデータの量は、アプリケーション自体、他の実行中アプリケーション、およびシステムで使用可能なメモリーの量によって異なります。
.sp
次の例では、クラス・メタデータの最大サイズを256MBに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxMetaspaceSize=256m\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MaxNewSize=\fIsize\fR
.RS 4
若い世代(ナーサリ)のヒープの最大サイズ(バイト単位)を設定します。デフォルト値は人間工学的に設定されます。
.RE
.PP
\-XX:MaxTenuringThreshold=\fIthreshold\fR
.RS 4
適応GCサイズ指定で使用する最大殿堂入りしきい値を設定します。最大値は15です。デフォルト値は、パラレル(スループット)コレクタの場合は15、CMSコレクタの場合は6です。
.sp
次の例では、最大殿堂入りしきい値を10に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxTenuringThreshold=10\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MetaspaceSize=\fIsize\fR
.RS 4
最初に超えたときにガベージ・コレクションをトリガーする、割り当てられたクラス・メタデータ領域のサイズを設定します。このガベージ・コレクションのしきい値は、使用されるメタデータの量によって増加または減少します。デフォルトのサイズはプラットフォームによって異なります。
.RE
.PP
\-XX:MinHeapFreeRatio=\fIpercent\fR
.RS 4
GCイベント後の空きヒープ領域の許可されている最小の割合(0から100まで)を設定します。空きヒープ領域がこの値を下回った場合、そのヒープは拡大します。デフォルトでは、この値は40%に設定されています。
.sp
次の例では、空きヒープの最小比率を25%に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MinHeapFreeRatio=25\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:NewRatio=\fIratio\fR
.RS 4
若い世代のサイズと古い世代のサイズとの比率を設定します。デフォルトでは、このオプションは2に設定されています。次の例では、若い/古いの比率を1に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:NewRatio=1\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:NewSize=\fIsize\fR
.RS 4
若い世代(ナーサリ)のヒープの初期サイズ(バイト単位)を設定します。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。
.sp
ヒープの若い世代リージョンは新しいオブジェクトに使用されます。GCは、他のリージョンよりこのリージョンで、より頻繁に実行されます。若い世代のサイズが小さすぎる場合、多数のマイナーGCが実行されます。サイズが大きすぎる場合、フルGCのみが実行されますが、完了までに時間がかかることがあります。若い世代のサイズは、全体のヒープ・サイズの半分から4分の1の間にしておくことをお薦めします。
.sp
次の例では、若い世代の初期サイズを様々な単位を使用して256MBに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:NewSize=256m\fR
\fB\-XX:NewSize=262144k\fR
\fB\-XX:NewSize=268435456\fR
 
.fi
.if n \{\
.RE
.\}
\fB\-XX:NewSize\fRオプションは\fB\-Xmn\fRと同等です。
.RE
.PP
\-XX:ParallelGCThreads=\fIthreads\fR
.RS 4
若い世代と古い世代でパラレル・ガベージ・コレクションに使用するスレッドの数を設定します。デフォルト値は、JVMに使用できるCPUの数によって異なります。
.sp
たとえば、パラレルGCのスレッド数を2に設定するには、次のオプションを指定します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:ParallelGCThreads=2\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+ParallelRefProcEnabled
.RS 4
パラレル参照処理を有効にします。デフォルトでは、このオプションは無効です。
.RE
.PP
\-XX:+PrintAdaptiveSizePolicy
.RS 4
適応世代サイズ指定に関する情報の出力を有効にします。デフォルトでは、このオプションは無効です。
.RE
.PP
\-XX:+PrintGC
.RS 4
GCごとのメッセージの出力を有効にします。デフォルトでは、このオプションは無効です。
.RE
.PP
\-XX:+PrintGCApplicationConcurrentTime
.RS 4
最後の休止(たとえばGC休止など)以降に経過した時間の出力を有効にします。デフォルトでは、このオプションは無効です。
.RE
.PP
\-XX:+PrintGCApplicationStoppedTime
.RS 4
休止(たとえばGC休止など)が継続した時間の出力を有効にします。デフォルトでは、このオプションは無効です。
.RE
.PP
\-XX:+PrintGCDateStamps
.RS 4
GCごとの日付スタンプの出力を有効にします。デフォルトでは、このオプションは無効です。
.RE
.PP
\-XX:+PrintGCDetails
.RS 4
GCごとの詳細メッセージの出力を有効にします。デフォルトでは、このオプションは無効です。
.RE
.PP
\-XX:+PrintGCTaskTimeStamps
.RS 4
個々のGCワーカー・スレッド・タスクごとのタイムスタンプの出力を有効にします。デフォルトでは、このオプションは無効です。
.RE
.PP
\-XX:+PrintGCTimeStamps
.RS 4
GCごとのタイムスタンプの出力を有効にします。デフォルトでは、このオプションは無効です。
.RE
.PP
\-XX:+PrintStringDeduplicationStatistics
.RS 4
詳細な重複除外統計を印刷します。デフォルトでは、このオプションは無効です。\fB\-XX:+UseStringDeduplication\fRオプションを参照してください。
.RE
.PP
\-XX:+PrintTenuringDistribution
.RS 4
殿堂入り期間情報の出力を有効にします。次に、出力の例を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBDesired survivor size 48286924 bytes, new threshold 10 (max 10)\fR
\fB\- age 1: 28992024 bytes, 28992024 total\fR
\fB\- age 2: 1366864 bytes, 30358888 total\fR
\fB\- age 3: 1425912 bytes, 31784800 total\fR
\fB\&.\&.\&.\fR
 
.fi
.if n \{\
.RE
.\}
期間1オブジェクトは、最も若いサバイバです(前のスカベンジの後に作成され、最新のスカベンジで存続し、Eden領域からサバイバ領域に移動しました)。期間2オブジェクトは、2つのスカベンジで存続します(2番目のスカベンジ中に、あるサバイバ領域から次の領域にコピーされました)。このように続きます。
.sp
前述の例では、28,992,024バイトが1つのスカベンジで存続し、Eden領域からサバイバ領域にコピーされました。1,366,864バイトは期間2オブジェクトなどにより占有されています。各行の3番目の値は、期間n以下のオブジェクトの累積サイズです。
.sp
デフォルトでは、このオプションは無効です。
.RE
.PP
\-XX:+ScavengeBeforeFullGC
.RS 4
それぞれのフルGCの前に若い世代のGCを有効にします。このオプションはデフォルトで有効になっています。フルGCの前に若い世代のスカベンジを行うと、古い世代領域から若い世代領域へのアクセスが可能なオブジェクトの数を減らすことができるため、これを無効に\fIしない\fRことをお薦めします。各フルGCの前に若い世代のGCを無効にするには、\fB\-XX:\-ScavengeBeforeFullGC\fRを指定します。
.RE
.PP
\-XX:SoftRefLRUPolicyMSPerMB=\fItime\fR
.RS 4
ソフト・アクセスが可能なオブジェクトが最後に参照されてからヒープ上でアクティブなままになっている時間(ミリ秒単位)を設定します。デフォルト値は、ヒープ内の空きメガバイト当たりで1秒の存続期間です。\fB\-XX:SoftRefLRUPolicyMSPerMB\fRオプションは、現在のヒープ・サイズ(Java HotSpot Client VM用)または最大可能ヒープ・サイズ(Java HotSpot Server VM用)の1メガバイト当たりのミリ秒を表す整数値を受け入れます。この違いは、Client VMは、ヒープを大きくするのではなく、ソフト参照をフラッシュする傾向があるのに対し、Server VMは、ソフト参照をフラッシュするのではなく、ヒープを大きくする傾向があることを意味します。後者の場合、\fB\-Xmx\fRオプションの値は、ソフト参照がどのくらい迅速にガベージ・コレクションされるかに重要な影響を及ぼします。
.sp
次の例では、値を2\&.5秒に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:SoftRefLRUPolicyMSPerMB=2500\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:StringDeduplicationAgeThreshold=\fIthreshold\fR
.RS 4
指定した期間に到達しつつある\fBString\fRオブジェクトは、重複除外の候補とみなされます。オブジェクトの期間は、オブジェクトがガベージ・コレクションで存続した回数の測定値です。これは、殿堂入りと呼ばれる場合もあります。\fB\-XX:+PrintTenuringDistribution\fRオプションを参照してください。この期間に到達する前に古いヒープ・リージョンに昇格された\fBString\fRオブジェクトは、常に重複除外の候補とみなされます。このオプションのデフォルト値は\fB3\fRです。\fB\-XX:+UseStringDeduplication\fRオプションを参照してください。
.RE
.PP
\-XX:SurvivorRatio=\fIratio\fR
.RS 4
Eden領域のサイズとサバイバ領域のサイズとの比率を設定します。デフォルトでは、このオプションは8に設定されています。次の例では、Eden/サバイバ領域の比率を4に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:SurvivorRatio=4\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:TargetSurvivorRatio=\fIpercent\fR
.RS 4
若いガベージ・コレクションの後に使用されるサバイバ領域の目的の割合(0から100まで)を設定します。デフォルトでは、このオプションは50%に設定されています。
.sp
次の例では、ターゲットのサバイバ領域の比率を30%に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:TargetSurvivorRatio=30\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:TLABSize=\fIsize\fR
.RS 4
スレッド・ローカルな割当てバッファ(TLAB)の初期サイズ(バイト単位)を設定します。キロバイトを示す場合は文字\fBk\fRまたは\fBK\fR、メガバイトを示す場合は文字\fBm\fRまたは\fBM\fR、ギガバイトを示す場合は文字\fBg\fRまたは\fBG\fRを追加します。このオプションが0に設定されている場合、JVMでは初期サイズが自動的に選択されます。
.sp
次の例では、TLABの初期サイズを512KBに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:TLABSize=512k\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+UseAdaptiveSizePolicy
.RS 4
適応世代サイズ指定の使用を有効にします。このオプションはデフォルトで有効になっています。適応世代サイズ指定を無効にするには、\fB\-XX:\-UseAdaptiveSizePolicy\fRを指定し、メモリー割当てプールのサイズを明示的に設定します(\fB\-XX:SurvivorRatio\fRオプションを参考にしてください)。
.RE
.PP
\-XX:+UseCMSInitiatingOccupancyOnly
.RS 4
CMSコレクタの開始のための唯一の基準としての占有値の使用を有効にします。デフォルトでは、このオプションは無効になっており、他の基準が使用されます。
.RE
.PP
\-XX:+UseConcMarkSweepGC
.RS 4
古い世代へのCMSガベージ・コレクタの使用を有効にします。アプリケーションの待機時間の要件を、スループット(\fB\-XX:+UseParallelGC\fR)ガベージ・コレクタによって満たすことができない場合、CMSガベージ・コレクタを使用することをお薦めします。G1ガベージ・コレクタ(\fB\-XX:+UseG1GC\fR)は別の代替となります。
.sp
デフォルトでは、このオプションは無効になっており、コレクタは、マシンの構成およびJVMのタイプに基づいて、自動的に選択されます。このオプションが有効な場合、\fB\-XX:+UseParNewGC\fRオプションは自動的に設定され、無効化しない必要がありますが、理由はJDK 8では\fB\-XX:+UseConcMarkSweepGC \-XX:\-UseParNewGC\fRオプションの組合せが非推奨であるためです。
.RE
.PP
\-XX:+UseG1GC
.RS 4
ガベージファースト(G1)・ガベージ・コレクタの使用を有効にします。これはサーバー形式のガベージ・コレクタで、大量のRAMを持つマルチプロセッサ・マシンを対象としています。高い確率でGC休止時間の目標を達成し、同時に適切なスループットも維持します。G1コレクタは、GC待機時間の限定された要件(安定した予測可能な0\&.5秒未満の休止時間)を持つ、大きいヒープ(約6GB以上のサイズ)が必要なアプリケーションに推奨されます。
.sp
デフォルトでは、このオプションは無効になっており、コレクタは、マシンの構成およびJVMのタイプに基づいて、自動的に選択されます。
.RE
.PP
\-XX:+UseGCOverheadLimit
.RS 4
\fBOutOfMemoryError\fR例外がスローされるまでに、GCでJVMによって要した時間の割合を制限するポリシーの使用を有効にします。デフォルトでは、このオプションは有効になっており、ガベージ・コレクションに合計時間の98%より多く費やされ、ヒープのリカバリが2%未満である場合、パラレルGCによって\fBOutOfMemoryError\fRがスローされます。ヒープが小さい場合、この機能は、アプリケーションが長期間ほとんどまたはまったく進捗なく実行している状態を回避するために使用できます。このオプションを無効にするには、\fB\-XX:\-UseGCOverheadLimit\fRを指定します。
.RE
.PP
\-XX:+UseNUMA
.RS 4
アプリケーションで短い待機時間のメモリーの使用を増加させることで、不均一なメモリー・アーキテクチャ(NUMA)を使用したマシン上のアプリケーションのパフォーマンス最適化を有効にします。デフォルトでは、このオプションは無効になっており、NUMAに対する最適化は行われません。このオプションは、パラレル・ガベージ・コレクタ(\fB\-XX:+UseParallelGC\fR)が使用されている場合のみ使用可能です。
.RE
.PP
\-XX:+UseParallelGC
.RS 4
複数のプロセッサを利用してアプリケーションのパフォーマンスを向上させる、パラレル・スカベンジ・ガベージ・コレクタ(スループット・コレクタとも呼ばれる)の使用を有効にします。
.sp
デフォルトでは、このオプションは無効になっており、コレクタは、マシンの構成およびJVMのタイプに基づいて、自動的に選択されます。これを有効にした場合、明示的に無効にしないかぎり、\fB\-XX:+UseParallelOldGC\fRオプションが自動的に有効になります。
.RE
.PP
\-XX:+UseParallelOldGC
.RS 4
フルGCへのパラレル・ガベージ・コレクタの使用を有効にします。デフォルトでは、このオプションは無効です。これを有効にすると、\fB\-XX:+UseParallelGC\fRオプションが自動的に有効になります。
.RE
.PP
\-XX:+UseParNewGC
.RS 4
若い世代でのコレクションへのパラレル・スレッドの使用を有効にします。デフォルトでは、このオプションは無効です。\fB\-XX:+UseConcMarkSweepGC\fRオプションを設定すると、これは自動的に有効になります。JDK 8では、\fB\-XX:+UseConcMarkSweepGC\fRオプションを指定せずに\fB\-XX:+UseParNewGC\fRオプションを使用することは非推奨です。
.RE
.PP
\-XX:+UseSerialGC
.RS 4
シリアル・ガベージ・コレクタの使用を有効にします。ガベージ・コレクションから特別な機能を必要としない、小規模で単純なアプリケーションの場合には、これは一般に最適な選択です。デフォルトでは、このオプションは無効になっており、コレクタは、マシンの構成およびJVMのタイプに基づいて、自動的に選択されます。
.RE
.PP
\-XX:+UseSHM
.RS 4
Linuxでは、JVMで共有メモリーを使用してラージ・ページを設定できるようにします。
.sp
詳細は、"ラージ・ページ"を参照してください。
.RE
.PP
\-XX:+UseStringDeduplication
.RS 4
文字列の重複除外を有効化します。デフォルトでは、このオプションは無効です。このオプションを使用するには、ガベージファースト(G1)・ガベージ・コレクタを有効にする必要があります。\fB\-XX:+UseG1GC\fRオプションを参照してください。
.sp
多くの\fBString\fRオブジェクトが同じであるということから、\fIString deduplication\fRにより、Javaヒープ上の\fBString\fRオブジェクトのメモリー・フットプリントが削減されます。各\fBString\fRオブジェクトが独自の文字配列をポイントするのではなく、同一の\fBString\fRオブジェクトは同じ文字配列をポイントし共有できます。
.RE
.PP
\-XX:+UseTLAB
.RS 4
若い世代の領域でのスレッド・ローカルな割当てブロック(TLAB)の使用を有効にします。このオプションはデフォルトで有効になっています。TLABの使用を無効にするには、\fB\-XX:\-UseTLAB\fRを指定します。
.RE
.SS "非推奨で削除されたオプション"
.PP
これらのオプションは、以前のリリースには含まれていましたが、以降は不要とみなされています。
.PP
\-Xincgc
.RS 4
インクリメンタル・ガベージ・コレクションを有効にします。このオプションはJDK 8では非推奨で、それに代わるものはありません。
.RE
.PP
\-Xrun\fIlibname\fR
.RS 4
指定したデバッグ/プロファイリングのライブラリをロードします。このオプションは、\fB\-agentlib\fRオプションに取って代わられました。
.RE
.PP
\-XX:CMSIncrementalDutyCycle=\fIpercent\fR
.RS 4
並行コレクタの実行が許可されているマイナー・コレクション間の時間の割合(0から100まで)を設定します。\fB\-XX:+CMSIncrementalMode\fRオプションの非推奨を受けて、このオプションはJDK 8では非推奨となり、それに代わるものはありません。
.RE
.PP
\-XX:CMSIncrementalDutyCycleMin=\fIpercent\fR
.RS 4
\fB\-XX:+CMSIncrementalPacing\fRが有効な場合にデューティ・サイクルの下限であるマイナー・コレクション間の時間の割合(0から100まで)を設定します。\fB\-XX:+CMSIncrementalMode\fRオプションの非推奨を受けて、このオプションはJDK 8では非推奨となり、それに代わるものはありません。
.RE
.PP
\-XX:+CMSIncrementalMode
.RS 4
CMSコレクタの増分モードを有効にします。\fBCMSIncremental\fRで始まるその他のオプションとともに、このオプションはJDK 8では非推奨となり、それに代わるものはありません
.RE
.PP
\-XX:CMSIncrementalOffset=\fIpercent\fR
.RS 4
増分モードのデューティ・サイクルをマイナー・コレクション間で期間内に右に移動する時間の割合(0から100まで)を設定します。\fB\-XX:+CMSIncrementalMode\fRオプションの非推奨を受けて、このオプションはJDK 8では非推奨となり、それに代わるものはありません。
.RE
.PP
\-XX:+CMSIncrementalPacing
.RS 4
JVMの実行中に収集された統計に基づいて、増分モードのデューティ・サイクルの自動調整を有効にします。\fB\-XX:+CMSIncrementalMode\fRオプションの非推奨を受けて、このオプションはJDK 8では非推奨となり、それに代わるものはありません。
.RE
.PP
\-XX:CMSIncrementalSafetyFactor=\fIpercent\fR
.RS 4
デューティ・サイクルを計算する際に、保守を追加するために使用される時間の割合(0から100まで)を設定します。\fB\-XX:+CMSIncrementalMode\fRオプションの非推奨を受けて、このオプションはJDK 8では非推奨となり、それに代わるものはありません。
.RE
.PP
\-XX:CMSInitiatingPermOccupancyFraction=\fIpercent\fR
.RS 4
GCを開始する永久世代占有率(0から100まで)を設定します。このオプションはJDK 8では非推奨で、それに代わるものはありません。
.RE
.PP
\-XX:MaxPermSize=\fIsize\fR
.RS 4
永久世代領域の最大サイズ(バイト単位)を設定します。このオプションは、JDK 8で非推奨になり、\fB\-XX:MaxMetaspaceSize\fRオプションに取って代わられました。
.RE
.PP
\-XX:PermSize=\fIsize\fR
.RS 4
超えた場合にはガベージ・コレクションをトリガーする、永久世代に割り当てられた領域(バイト単位)を設定します。このオプションは、JDK 8で非推奨になり、\fB\-XX:MetaspaceSize\fRオプションに取って代わられました。
.RE
.PP
\-XX:+UseSplitVerifier
.RS 4
検証プロセスの分割を有効にします。デフォルトでは、このオプションは以前のリリースでは有効になっており、検証は、タイプ参照(コンパイラによって実行)と、タイプ・チェック(JVMランタイムによって実行)の2つのフェーズに分割されていました。このオプションはJDK 8で非推奨となり、検証はデフォルトで分割され、無効にする方法はありません。
.RE
.PP
\-XX:+UseStringCache
.RS 4
一般に割り当てられた文字列のキャッシングを有効にします。このオプションはJDK 8から削除され、それに代わるものはありません。
.RE
.SH "パフォーマンス・チューニングの例"
.PP
次の例では、スループットの最適化またはレスポンス時間の短縮化のいずれかを行うための、試験的なチューニング・フラグの使用方法を示します。
.PP
\fB例 1 \fRスループットを向上するためのチューニング
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava \-d64 \-server \-XX:+AggressiveOpts \-XX:+UseLargePages \-Xmn10g  \-Xms26g \-Xmx26g\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fB例 2 \fRレスポンス時間を速くするためのチューニング
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava \-d64 \-XX:+UseG1GC \-Xms26g Xmx26g \-XX:MaxGCPauseMillis=500 \-XX:+PrintGCTimeStamp\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.SH "ラージ・ページ"
.PP
ヒュージ・ページとも呼ばれるラージ・ページは、標準のメモリー・ページ・サイズ(プロセッサおよびオペレーティング・システムによって異なります)よりはるかに大きいメモリー・ページです。ラージ・ページは、プロセッサのTranslation\-Lookaside Bufferを最適化します。
.PP
Translation\-Lookaside Buffer (TLB)は、最近使用された仮想から物理へのアドレス変換を保持するページ変換キャッシュです。TLBは、少ないシステム・リソースです。プロセッサが複数のメモリー・アクセスが必要な場合のある階層ページ表から読み取る必要があるため、TLBミスは負荷がかかる可能性があります。大きいメモリー・ページ・サイズを使用して、単一のTLBエントリで大きいメモリー範囲を表すことができます。TLB不足が少なくなり、メモリー集約型のアプリケーションのパフォーマンスが向上する可能性があります。
.PP
ただし、ラージ・ページのページ・メモリーは、システムのパフォーマンスに悪影響を与える場合があります。たとえば、大量のメモリーがアプリケーションで確保される場合、通常メモリー不足や他のアプリケーションの過剰なページングが発生し、システム全体が遅くなる可能性があります。また、長時間稼働しているシステムは、過剰な断片化が発生する可能性があります。これにより、十分な大きさのページ・メモリーを予約できない可能性があります。これが発生した場合、OSまたはJVMのいずれかが通常のページの使用に戻ります。
.SS "ラージ・ページのサポート"
.PP
SolarisおよびLinuxは、ラージ・ページをサポートします。
.sp
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
.br
.ps +1
\fBSolaris\fR
.RS 4
.PP
Solaris 9以上には、Multiple Page Size Support(MPSS)が含まれています。追加の構成は必要ありません。http://www\&.oracle\&.com/technetwork/server\-storage/solaris10/overview/solaris9\-features\-scalability\-135663\&.htmlを参照してください。
.RE
.sp
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
.br
.ps +1
\fBLinux\fR
.RS 4
.PP
2\&.6カーネルは、ラージ・ページをサポートします。一部のベンダーは、2\&.4ベースのリリースのコードをバックポートしています。システムがラージ・ページ・メモリーをサポートしているかどうかを確認するには、次を試行してください:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB# cat /proc/meminfo | grep Huge\fR
\fBHugePages_Total: 0\fR
\fBHugePages_Free: 0\fR
\fBHugepagesize: 2048 kB\fR
 
.fi
.if n \{\
.RE
.\}
.PP
出力に3つの"Huge"変数が示されている場合、システムはラージ・ページ・メモリーをサポートしていますが、構成する必要があります。コマンドが何も出力しない場合、システムはラージ・ページをサポートしていません。ラージ・ページ・メモリーを使用するシステムを構成するには、\fBroot\fRとしてログインして、次の手順を実行してください:
.sp
.RS 4
.ie n \{\
\h'-04' 1.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  1." 4.2
.\}
オプション\fB\-XX:+UseSHM\fR(\fB\-XX:+UseHugeTLBFS\fRのかわり)を使用する場合、\fBSHMMAX\fR値を増やしてください。Javaヒープ・サイズより大きくする必要があります。4GB以下の物理RAMを使用したシステムで、次によりすべてのメモリーが共有可能になります:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB# echo 4294967295 > /proc/sys/kernel/shmmax\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 2.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  2." 4.2
.\}
オプション\fB\-XX:+UseSHM\fRまたは\fB\-XX:+UseHugeTLBFS\fRを使用する場合、ラージ・ページの数を指定してください。次の例では、4GBシステムの3GBがラージ・ページに予約されます(2048KBのラージ・ページ・サイズを仮定する場合、3GB = 3 * 1024MB = 3072MB = 3072 * 1024KB = 3145728KB and 3145728KB / 2048KB = 1536):
.sp
.if n \{\
.RS 4
.\}
.nf
\fB# echo 1536 > /proc/sys/vm/nr_hugepages\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.if n \{\
.sp
.\}
.RS 4
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
.br
.ps +1
\fB注記\fR
.ps -1
.br
.TS
allbox tab(:);
l.
T{
注意
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
システムを再起動した後に\fB/proc\fRに含まれる値がリセットされるので注意してください。そのため、初期化スクリプト(\fBrc\&.local\fRや\fBsysctl\&.conf\fRなど)で設定できます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
OSカーネル・パラメータ\fB/proc/sys/kernel/shmmax\fRまたは\fB/proc/sys/vm/nr_hugepages\fRを構成(またはサイズ変更)する場合、JavaプロセスがJavaヒープ以外の領域に対してラージ・ページを割り当てることがあります。これらの手順を使用して、次の領域に対してラージ・ページを割り当てることができます:
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Javaヒープ
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Permanent世代
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
コード・キャッシュ
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
パラレルGCのマーキング・ビットマップ・データ構造
.RE
.sp
その結果、Javaヒープのサイズに\fBnr_hugepages\fRパラメータを構成すると、領域のサイズが非常に大きいためにJVMがPermanent世代およびラージ・ページのコード・キャッシュ領域の割当てに失敗する場合があります。
.RE
T}
.TE
.sp 1
.sp .5v
.RE
.RE
.SH "終了ステータス"
.PP
通常、次の終了値が起動ツールから返されるのは、起動元が不正な引数で呼び出されたか、深刻なエラーが発生したか、あるいはJVMにより例外がスローされた場合です。ただし、Javaアプリケーションは、API呼出し\fBSystem\&.exit(exitValue)\fRを使用して任意の値を返すことを選択することもできます。値は次のとおりです。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB0\fR: 正常終了
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB>0\fR: エラー発生
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javac(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jdb(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javah(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jar(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jstat(1)
.RE
.br
'pl 8.5i
'bp
