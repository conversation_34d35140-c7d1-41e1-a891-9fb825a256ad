'\" t
.\" Copyright (c) 2012, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jcmd
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: トラブルシューティング・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jcmd" "1" "2013年11月21日" "JDK 8" "トラブルシューティング・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jcmd \- 実行中のJava Virtual Machine (JVM)に診断コマンド・リクエストを送信します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjcmd\fR [\fB\-l\fR|\fB\-h\fR|\fB\-help\fR]
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjcmd\fR \fIpid\fR|\fImain\-class\fR \fBPerfCounter\&.print\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjcmd\fR \fIpid\fR|\fImain\-class\fR \fB\-f\fR \fIfilename\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjcmd\fR \fIpid\fR|\fImain\-class\fR \fIcommand\fR[ \fIarguments\fR]
.fi
.if n \{\
.RE
.\}
.SH "説明"
.PP
\fBjcmd\fRユーティリティは、JVMに診断コマンド・リクエストを送信するために使用されます。これはJVMが稼働しているのと同じマシンで使用し、JVMの起動に使用したものと同じ有効ユーザーおよびグループ識別子を持っている必要があります。
.PP
\fB注意:\fR
リモート・マシンから、または別の識別子で診断コマンドを起動するには、\fBcom\&.sun\&.management\&.DiagnosticCommandMBean\fRインタフェースを使用できます。\fBDiagnosticCommandMBean\fRインタフェースの詳細は、http://docs\&.oracle\&.com/javase/8/docs/jre/api/management/extension/com/sun/management/DiagnosticCommandMBean\&.htmlにあるAPIドキュメントを参照してください。
.PP
\fBjcmd\fRを引数なしまたは\fB\- l\fRオプションを指定して実行した場合は、実行中のJavaプロセス識別子のリストが、メイン・クラスおよびプロセスの起動に使用されたコマンド行引数とともに出力されます。\fB\-h\fRまたは\fB\-help\fRオプションを指定して\fBjcmd\fRを実行すると、ツールのヘルプ・メッセージが出力されます。
.PP
プロセス識別子(\fIpid\fR)またはメイン・クラス(\fImain\-class\fR)を最初の引数として指定した場合、\fBjcmd\fRは、識別子を指定した場合はJavaプロセスに、メイン・クラスの名前を指定した場合はすべてのJavaプロセスに診断コマンド・リクエストを送信します。プロセス識別子として\fB0\fRを指定して、すべての使用可能なJavaプロセスに診断コマンド・リクエストを送信することもできます。診断コマンド・リクエストとして、次のいずれかを使用します。
.PP
Perfcounter\&.print
.RS 4
指定したJavaプロセスで使用可能なパフォーマンス・カウンタが出力されます。パフォーマンス・カウンタのリストはJavaプロセスによって異なる場合があります。
.RE
.PP
\-f \fIfilename\fR
.RS 4
診断コマンドを読み取り、指定したJavaプロセスに送信するファイルの名前。\fB\-f\fRオプションでのみ使用します。ファイル内の各コマンドは、1行で記述する必要があります。番号記号(\fB#\fR)で始まる行は無視されます。すべての行が読み取られるか、\fBstop\fRキーワードを含む行が読み取られると、ファイルの処理が終了します。
.RE
.PP
\fIcommand\fR [\fIarguments\fR]
.RS 4
指定されたJavaプロセスに送信するコマンド。指定したプロセスで使用できる診断コマンドのリストは、このプロセスに対して\fBhelp\fRコマンドを送信すれば表示されます。各診断コマンドに独自の引数セットがあります。コマンドの説明、構文および使用可能な引数のリストを表示するには、\fBhelp\fRコマンドの引数としてコマンド名を使用します。
.sp
\fB注意:\fR
引数にスペースが含まれている場合は、一重引用符または二重引用符(\fB\*(Aq\fRまたは\fB"\fR)で囲む必要があります。加えて、オペレーティング・システム・シェルが引用符を処理しないように、バックスラッシュ(\fB\e\fR)で一重引用符または二重引用符をエスケープする必要があります。または、これらの引数を一重引用符で囲んでから、二重引用符で囲むこともできます(または二重引用符で囲んでから、一重引用符で囲む)。
.RE
.SH "オプション"
.PP
各オプションは互いに排他的です。
.PP
\-f \fIfilename\fR
.RS 4
指定されたファイルからコマンドを読み取ります。このオプションは、最初の引数としてプロセス識別子またはメイン・クラスを指定する場合にのみ使用できます。ファイル内の各コマンドは、1行で記述する必要があります。番号記号(\fB#\fR)で始まる行は無視されます。すべての行が読み取られるか、\fBstop\fRキーワードを含む行が読み取られると、ファイルの処理が終了します。
.RE
.PP
\-h
.br
\-help
.RS 4
ヘルプ・メッセージが出力されます。
.RE
.PP
\-l
.RS 4
実行中のJavaプロセス識別子のリストをメイン・クラスおよびコマンド行引数とともに出力します。
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jps(1)
.RE
.br
'pl 8.5i
'bp
