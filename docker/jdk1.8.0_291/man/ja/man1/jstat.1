'\" t
.\" Copyright (c) 2004, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jstat
.\" Language: Japanese
.\" Date: 2015年3月3日
.\" SectDesc: モニタリング・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jstat" "1" "2015年3月3日" "JDK 8" "モニタリング・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jstat \- Java Virtual Machine (JVM)の統計をモニターします。このコマンドは試験的なもので、サポートされていません。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjstat\fR [ \fIgeneralOption\fR | \fIoutputOptions vmid\fR [ \fIinterval\fR[s|ms] [ \fIcount \fR] ]
.fi
.if n \{\
.RE
.\}
.PP
\fIgeneralOption\fR
.RS 4
単独で使用する一般的なコマンド行オプションです(\fB\-help\fRまたは\fB\-options\fR)。一般的なオプションを参照してください。
.RE
.PP
\fIoutputOptions\fR
.RS 4
単一の\fBstatOption\fRと\fB\-t\fR、\fB\-h\fRおよび\fB\-J\fRのいずれかのオプションで構成される1つ以上の出力オプション。出力オプションを参照してください。
.RE
.PP
\fIvmid\fR
.RS 4
ターゲットJVMを示す文字列である仮想マシン識別子です。一般的な構文は次のとおりです。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[protocol:][//]lvmid[@hostname[:port]/servername]\fR
 
.fi
.if n \{\
.RE
.\}
\fBvmid\fR文字列の構文は、URIの構文に対応しています。\fBvmid\fR文字列は、ローカルJVMを表す単純な整数から、通信プロトコル、ポート番号、および他の実装固有の値を示す複雑な構造まで、様々に異なります。仮想マシン識別子を参照してください。
.RE
.PP
\fIinterval\fR [s|ms]
.RS 4
秒(s)またはミリ秒(ms)のうち指定した単位でのサンプリング間隔です。デフォルトの単位はミリ秒です。正の整数にする必要があります。指定した場合、\fBjstat\fRコマンドは各間隔で出力を生成します。
.RE
.PP
\fIcount\fR
.RS 4
表示するサンプル数です。デフォルト値は無限で、ターゲットJVMが終了するまで、または\fBjstat\fRコマンドが終了するまで、\fBjstat\fRコマンドは統計データを表示します。この値は、正の整数である必要があります。
.RE
.SH "説明"
.PP
\fBjstat\fRコマンドは、設置されているJava HotSpot VMのパフォーマンス統計データを表示します。ターゲットJVMは、仮想マシン識別子または\fBvmid\fRオプションによって識別されます。
.SH "仮想マシン識別子"
.PP
\fBvmid\fR文字列の構文は、URIの構文に対応しています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[protocol:][//]lvmid[@hostname[:port]/servername]\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fIprotocol\fR
.RS 4
通信プロトコルです。\fIprotocol\fR値が省略され、ホスト名が指定されていない場合、デフォルトのプロトコルが、プラットフォーム固有の最適化されたローカル・プロトコルになります。\fIprotocol\fR値が省略され、ホスト名が指定されている場合は、デフォルト・プロトコルは\fBrmi\fRになります。
.RE
.PP
\fIlvmid\fR
.RS 4
ターゲットJVMのローカル仮想マシン識別子です。\fBlvmid\fRは、システム上のJVMを一意に識別するプラットフォーム固有の値です。\fBlvmid\fRは、仮想マシン識別子の唯一の必須要素です。\fBlvmid\fRは、一般的にはターゲットJVMプロセスに対するオペレーティング・システムのプロセス識別子ですが、必ずしもそうであるとは限りません。\fBjps\fRコマンドを使用して、\fBlvmid\fRを特定できます。また、Solaris、LinuxおよびOS Xプラットフォームでは\fBps\fRコマンドを使用して、WindowsではWindowsタスク・マネージャを使用して、\fBlvmid\fRを特定できます。
.RE
.PP
\fIhostname\fR
.RS 4
ターゲット・ホストを示すホスト名またはIPアドレスです。\fIhostname\fR値が省略されている場合は、ターゲット・ホストはローカル・ホストになります。
.RE
.PP
\fIport\fR
.RS 4
リモート・サーバーと通信するためのデフォルト・ポートです。\fIhostname\fR値が省略されているか、最適化されたローカル・プロトコルが\fIprotocol\fR値に指定されている場合、\fIport\fR値は無視されます。それ以外の場合、\fBport\fRパラメータの扱いは、実装によって異なります。デフォルトの\fBrmi\fRプロトコルの場合、ポート値は、リモート・ホスト上のrmiregistryのポート番号を示します。\fIport\fR値が省略され、\fIprotocol\fR値で\fBrmi\fRが指定されている場合、デフォルトのrmiregistryポート(1099)が使用されます。
.RE
.PP
\fIservername\fR
.RS 4
\fBservername\fRパラメータの扱いは、実装によって異なります。最適化されたローカル・プロトコルの場合、このフィールドは無視されます。\fBrmi\fRプロトコルの場合、これは、リモート・ホスト上のRMIリソース・オブジェクトの名前を表します。
.RE
.SH "オプション"
.PP
\fBjstat\fRコマンドは、一般的なオプションと出力オプションの2つのタイプのオプションをサポートしています。一般的なオプションを使用した場合、\fBjstat\fRコマンドは簡単な使用率およびバージョン情報を表示します。出力オプションによって、統計データ出力の内容と形式が決まります。
.PP
すべてのオプションとその機能は、将来のリリースで変更または廃止される可能性があります。
.SS "一般的なオプション"
.PP
いずれかの一般的なオプションを指定した場合、他のオプションまたはパラメータは一切指定できません。
.PP
\-help
.RS 4
ヘルプ・メッセージを表示します。
.RE
.PP
\-options
.RS 4
Staticオプションのリストを表示します。出力オプションを参照してください。
.RE
.SS "出力オプション"
.PP
一般的なオプションを指定しない場合に、出力オプションを指定できます。出力オプションは、\fBjstat\fRコマンドの出力の内容と形式を決定し、単一の\fBstatOption\fRといずれかの出力オプション(\fB\-h\fR、\fB\-t\fRおよび\fB\-J\fR)で構成されます。\fBstatOption\fRは最初に記述する必要があります。
.PP
出力は、各列が空白で区切られた表の形式になります。タイトルを含むヘッダー行によって、各列の意味がわかります。ヘッダーの表示頻度を設定するには、\fB\-h\fRオプションを使用します。列のヘッダー名は、様々なオプション間で一貫性が保たれています。一般に、2つのオプションで同じ名前の列が使用されていれば、2つの列のデータ・ソースは同じです。
.PP
\fB\-t\fRオプションを使用すると、Timestampというラベルの付いたタイムスタンプの列が、出力の最初の列として表示されます。Timestamp列には、ターゲットJVMの起動からの経過時間が、秒単位で表示されます。タイムスタンプの精度は、様々な要因によって異なり、大量の負荷のかかったシステムでのスレッド・スケジュールの遅延により変動します。
.PP
intervalおよびcountパラメータを使用して、\fBjstat\fRコマンドがその出力を表示する頻度と回数をそれぞれ指定します。
.PP
\fB注意:\fR
将来のリリースでこの形式は変更される可能性があるため、\fBjstat\fRコマンドの出力を解析するスクリプトは作成しなでください。\fBjstat\fRコマンドの出力を解析するスクリプトを作成する場合は、このツールの将来のリリースで、そのスクリプトを変更する必要があることに留意してください。
.PP
\-\fIstatOption\fR
.RS 4
\fBjstat\fRコマンドが表示する統計データ情報を指定します。次に、利用可能なオプションの一覧を示します。特定のプラットフォーム・インストールのオプションを一覧表示するには、一般的なオプションの\fB\-options\fRを使用します。Statオプションおよび出力を参照してください。
.sp
\fBclass\fR: クラス・ローダーの動作に関する統計データを表示します。
.sp
\fBcompiler\fR: Java HotSpot VM Just\-in\-Timeコンパイラの動作に関する統計データを表示します。
.sp
\fBgc\fR: ガベージ・コレクトされたヒープの動作に関する統計データを表示します。
.sp
\fBgccapacity\fR: 世代ごとの容量と対応する領域に関する統計データを表示します。
.sp
\fBgccause\fR: ガベージ・コレクション統計データのサマリー(\fB\-gcutil\fRと同じ)と、直前および現在(適用可能な場合)のガベージ・コレクション・イベントの原因を表示します。
.sp
\fBgcnew\fR: New世代の動作に関する統計データを表示します。
.sp
\fBgcnewcapacity\fR: New世代のサイズと対応する領域に関する統計データを表示します。
.sp
\fBgcold\fR: Old世代の動作とメタスペースに関する統計データを表示します。
.sp
\fBgcoldcapacity\fR: Old世代のサイズに関する統計データを表示します。
.sp
\fBgcmetacapacity\fR: メタスペースのサイズに関する統計データを表示します。
.sp
\fBgcutil\fR: ガベージ・コレクションのサマリーに関する統計データを表示します。
.sp
\fBprintcompilation\fR: Java HotSpot VMコンパイル・メソッドの統計データを表示します。
.RE
.PP
\-h \fIn\fR
.RS 4
\fIn\fRサンプル(出力行)ごとに列ヘッダーを表示します。ここで、\fIn\fRは正の整数値です。デフォルト値は0です。この場合、データの最初の行の上に列ヘッダーが表示されます。
.RE
.PP
\-t
.RS 4
タイムスタンプ列を出力の最初の列として表示します。タイムスタンプは、ターゲットJVMの起動時からの経過時間です。
.RE
.PP
\-J\fIjavaOption\fR
.RS 4
\fBjavaOption\fRをJavaアプリケーション起動ツールに渡します。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。オプションの完全なリストについては、java(1)を参照してください。
.RE
.SS "Statオプションおよび出力"
.PP
次の情報は、\fBjstat\fRコマンドが各\fIstatOption\fRについて出力する列をまとめたものです。
.PP
\-class \fIoption\fR
.RS 4
クラス・ローダーの統計データ。
.sp
\fBLoaded\fR: ロードされたクラスの数。
.sp
\fBBytes\fR: ロードされたKBの数。
.sp
\fBUnloaded\fR: アンロードされたクラスの数。
.sp
\fBBytes\fR: アンロードされたKBの数。
.sp
\fBTime\fR: クラスのロードやアンロード処理に要した時間。
.RE
.PP
\-compiler \fIoption\fR
.RS 4
Java HotSpot VM Just\-in\-Timeコンパイラの統計データ。
.sp
\fBCompiled\fR: 実行されたコンパイル・タスクの数。
.sp
\fBFailed\fR: 失敗したコンパイル・タスクの数。
.sp
\fBInvalid\fR: 無効にされたコンパイル・タスクの数。
.sp
\fBTime\fR: コンパイル・タスクの実行に要した時間。
.sp
\fBFailedType\fR: 最後に失敗したコンパイルのコンパイル・タイプ。
.sp
\fBFailedMethod\fR: 最後に失敗したコンパイルのクラス名とメソッド。
.RE
.PP
\-gc \fIoption\fR
.RS 4
ガベージ・コレクトされたヒープの統計データ。
.sp
\fBS0C\fR: Survivor領域0の現在の容量(KB)。
.sp
\fBS1C\fR: Survivor領域1の現在の容量(KB)。
.sp
\fBS0U\fR: Survivor領域0の使用率(KB)。
.sp
\fBS1U\fR: Survivor領域1の使用率(KB)。
.sp
\fBEC\fR: Eden領域の現在の容量(KB)。
.sp
\fBEU\fR: Eden領域の使用率(KB)。
.sp
\fBOC\fR: Old領域の現在の容量(KB)。
.sp
\fBOU\fR: Old領域の使用率(KB)。
.sp
\fBMC\fR: メタスペースの容量(KB)。
.sp
\fBMU\fR: メタスペースの使用率(KB)。
.sp
\fBCCSC\fR: 圧縮されたクラス領域の容量(KB)。
.sp
\fBCCSU\fR: 使用されている圧縮されたクラス領域(KB)。
.sp
\fBYGC\fR: 若い世代のガベージ・コレクション・イベントの数。
.sp
\fBYGCT\fR: 若い世代のガベージ・コレクション時間。
.sp
\fBFGC\fR: フルGCイベント数。
.sp
\fBFGCT\fR: フルガベージ・コレクション時間。
.sp
\fBGCT\fR: ガベージ・コレクション総時間。
.RE
.PP
\-gccapacity \fIoption\fR
.RS 4
メモリー・プール世代および領域容量。
.sp
\fBNGCMN\fR: New世代の最小容量(KB)。
.sp
\fBNGCMX\fR: New世代の最大容量(KB)。
.sp
\fBNGC\fR: New世代の現在の容量(KB)。
.sp
\fBS0C\fR: Survivor領域0の現在の容量(KB)。
.sp
\fBS1C\fR: Survivor領域1の現在の容量(KB)。
.sp
\fBEC\fR: Eden領域の現在の容量(KB)。
.sp
\fBOGCMN\fR: Old世代の最小容量(KB)。
.sp
\fBOGCMX\fR: Old世代の最大容量(KB)。
.sp
\fBOGC\fR: Old世代の現在の容量(KB)。
.sp
\fBOC\fR: Old領域の現在の容量(KB)。
.sp
\fBMCMN\fR: メタスペースの最小容量(KB)。
.sp
\fBMCMX\fR: メタスペースの最大容量(KB)。
.sp
\fBMC\fR: メタスペースの容量(KB)。
.sp
\fBCCSMN\fR: 圧縮されたクラス領域の最小容量(KB)。
.sp
\fBCCSMX\fR: 圧縮されたクラス領域の最大容量(KB)。
.sp
\fBCCSC\fR: 圧縮されたクラス領域の容量(KB)。
.sp
\fBYGC\fR: 若い世代のGCイベント数。
.sp
\fBFGC\fR: フルGCイベント数。
.RE
.PP
\-gccause \fIoption\fR
.RS 4
このオプションは、\fB\-gcutil\fRオプションと同じガベージ・コレクション統計データのサマリーを表示しますが、最後のガベージ・コレクション・イベントと(適用可能な場合は)現在のガベージ・コレクション・イベントの原因が含まれます。\fB\-gcutil\fRで一覧表示される列のほか、このオプションでは次の列が追加されます。
.sp
\fBLGCC\fR: 最後のガベージ・コレクションの原因
.sp
\fBGCC\fR: 現在のガベージ・コレクションの原因
.RE
.PP
\-gcnew \fIoption\fR
.RS 4
New世代の統計データ。
.sp
\fBS0C\fR: Survivor領域0の現在の容量(KB)。
.sp
\fBS1C\fR: Survivor領域1の現在の容量(KB)。
.sp
\fBS0U\fR: Survivor領域0の使用率(KB)。
.sp
\fBS1U\fR: Survivor領域1の使用率(KB)。
.sp
\fBTT\fR: 殿堂入りしきい値。
.sp
\fBMTT\fR: 最大殿堂入りしきい値。
.sp
\fBDSS\fR: 適切なSurvivorサイズ(KB)。
.sp
\fBEC\fR: Eden領域の現在の容量(KB)。
.sp
\fBEU\fR: Eden領域の使用率(KB)。
.sp
\fBYGC\fR: 若い世代のGCイベント数。
.sp
\fBYGCT\fR: 若い世代のガベージ・コレクション時間。
.RE
.PP
\-gcnewcapacity \fIoption\fR
.RS 4
New世代領域サイズの統計データ。
.sp
\fBNGCMN\fR: New世代の最小容量(KB)。
.sp
\fBNGCMX\fR: New世代の最大容量(KB)。
.sp
\fBNGC\fR: New世代の現在の容量(KB)。
.sp
\fBS0CMX\fR: Survivor領域0の最大容量(KB)。
.sp
\fBS0C\fR: Survivor領域0の現在の容量(KB)。
.sp
\fBS1CMX\fR: Survivor領域1の最大容量(KB)。
.sp
\fBS1C\fR: Survivor領域1の現在の容量(KB)。
.sp
\fBECMX\fR: Eden領域の最大容量(KB)。
.sp
\fBEC\fR: Eden領域の現在の容量(KB)。
.sp
\fBYGC\fR: 若い世代のGCイベント数。
.sp
\fBFGC\fR: フルGCイベント数。
.RE
.PP
\-gcold \fIoption\fR
.RS 4
OldおよびPermanent世代の統計データ。
.sp
\fBMC\fR: メタスペースの容量(KB)。
.sp
\fBMU\fR: メタスペースの使用率(KB)。
.sp
\fBCCSC\fR: 圧縮されたクラス領域の容量(KB)。
.sp
\fBCCSU\fR: 使用されている圧縮されたクラス領域(KB)。
.sp
\fBOC\fR: Old領域の現在の容量(KB)。
.sp
\fBOU\fR: Old領域の使用率(KB)。
.sp
\fBYGC\fR: 若い世代のGCイベント数。
.sp
\fBFGC\fR: フルGCイベント数。
.sp
\fBFGCT\fR: フルガベージ・コレクション時間。
.sp
\fBGCT\fR: ガベージ・コレクション総時間。
.RE
.PP
\-gcoldcapacity \fIoption\fR
.RS 4
Old世代の統計データ。
.sp
\fBOGCMN\fR: Old世代の最小容量(KB)。
.sp
\fBOGCMX\fR: Old世代の最大容量(KB)。
.sp
\fBOGC\fR: Old世代の現在の容量(KB)。
.sp
\fBOC\fR: Old領域の現在の容量(KB)。
.sp
\fBYGC\fR: 若い世代のGCイベント数。
.sp
\fBFGC\fR: フルGCイベント数。
.sp
\fBFGCT\fR: フルガベージ・コレクション時間。
.sp
\fBGCT\fR: ガベージ・コレクション総時間。
.RE
.PP
\-gcmetacapacity \fIoption\fR
.RS 4
Permanent世代の統計データ。
.sp
\fBMCMN\fR: メタスペースの最小容量(KB)。
.sp
\fBMCMX\fR: メタスペースの最大容量(KB)。
.sp
\fBMC\fR: メタスペースの容量(KB)。
.sp
\fBCCSMN\fR: 圧縮されたクラス領域の最小容量(KB)。
.sp
\fBCCSMX\fR: 圧縮されたクラス領域の最大容量(KB)。
.sp
\fBYGC\fR: 若い世代のGCイベント数。
.sp
\fBFGC\fR: フルGCイベント数。
.sp
\fBFGCT\fR: フルガベージ・コレクション時間。
.sp
\fBGCT\fR: ガベージ・コレクション総時間。
.RE
.PP
\-gcutil \fIoption\fR
.RS 4
ガベージ・コレクション統計データのサマリー
.sp
\fBS0\fR: Survivor領域0の使用率(現在の容量に対するパーセンテージ)。
.sp
\fBS1\fR: Survivor領域1の使用率(現在の容量に対するパーセンテージ)。
.sp
\fBE\fR: Eden領域の使用率(現在の容量に対するパーセンテージ)。
.sp
\fBO\fR: Old領域の使用率(現在の容量に対するパーセンテージ)。
.sp
\fBM\fR: メタスペースの使用率(領域の現在の容量に対するパーセンテージ)。
.sp
\fBCCS\fR: 圧縮されたクラス領域の使用率(パーセンテージ)。
.sp
\fBYGC\fR: 若い世代のGCイベント数。
.sp
\fBYGCT\fR: 若い世代のガベージ・コレクション時間。
.sp
\fBFGC\fR: フルGCイベント数。
.sp
\fBFGCT\fR: フルガベージ・コレクション時間。
.sp
\fBGCT\fR: ガベージ・コレクション総時間。
.RE
.PP
\-printcompilation \fIoption\fR
.RS 4
Java HotSpot VMコンパイル・メソッドの統計データ。
.sp
\fBCompiled\fR: 最近コンパイルされたメソッドで実行されたコンパイル・タスクの数。
.sp
\fBSize\fR: 最近コンパイルされたメソッドのバイト・コードのバイト数。
.sp
\fBType\fR: 最近コンパイルされたメソッドのコンパイル・タイプ。
.sp
\fBMethod\fR: 最近コンパイルされたメソッドを特定するクラス名とメソッド名。クラス名では、名前空間の区切り文字として、ドット(\&.)のかわりにスラッシュ(/)が使用されます。メソッド名は、指定されたクラス内のメソッドです。これらの2つのフィールドの形式は、HotSpot
\fB\-XX:+PrintCompilation\fRオプションと対応しています。
.RE
.SH "例"
.PP
この項では、21891の\fIlvmid\fRを持つローカルJVMをモニタリングする例を示します。
.SS "gcutilオプション"
.PP
この例では、lvmid 21891に接続して、250ミリ秒間隔で7つのサンプルを取得し、\-\fBgcutil\fRオプションでの指定に従って出力を表示します。
.PP
この例の出力は、若い世代のコレクションが3番目と4番目のサンプル間で行われたことを示しています。コレクションには0\&.078秒かかっており、オブジェクトがEden領域(E)からOld領域(O)に昇格したため、Old領域の使用率は66\&.80%から68\&.19%に増加しています。Survivor領域は、コレクション前は97\&.02%が使用されていましたが、コレクション後の使用は91\&.03%です。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjstat \-gcutil 21891 250 7\fR
\fB  S0     S1     E      O      M     CCS    YGC     YGCT    FGC    FGCT     GCT   \fR
\fB  0\&.00  97\&.02  70\&.31  66\&.80  95\&.52  89\&.14      7    0\&.300     0    0\&.000    0\&.300\fR
\fB  0\&.00  97\&.02  86\&.23  66\&.80  95\&.52  89\&.14      7    0\&.300     0    0\&.000    0\&.300\fR
\fB  0\&.00  97\&.02  96\&.53  66\&.80  95\&.52  89\&.14      7    0\&.300     0    0\&.000    0\&.300\fR
\fB 91\&.03   0\&.00   1\&.98  68\&.19  95\&.89  91\&.24      8    0\&.378     0    0\&.000    0\&.378\fR
\fB 91\&.03   0\&.00  15\&.82  68\&.19  95\&.89  91\&.24      8    0\&.378     0    0\&.000    0\&.378\fR
\fB 91\&.03   0\&.00  17\&.80  68\&.19  95\&.89  91\&.24      8    0\&.378     0    0\&.000    0\&.378\fR
\fB 91\&.03   0\&.00  17\&.80  68\&.19  95\&.89  91\&.24      8    0\&.378     0    0\&.000    0\&.378\fR
.fi
.if n \{\
.RE
.\}
.SS "列ヘッダー文字列の繰返し"
.PP
この例では、lvmid 21891に接続して、250ミリ秒間隔でサンプルを取得し、\fB\-gcnew\fRオプションでの指定に従って出力を表示します。さらに、\fB\-h3\fRオプションを使用して、データが3行表示されるごとに列ヘッダーを出力します。
.PP
この例では、ヘッダー文字列の繰返しが表示されているほか、2番目と3番目のサンプル間でYoung GCが行われたことがわかります。この継続時間は0\&.001秒でした。このコレクションでは、Survivor領域0の使用率(S0U)が適切なSurvivorサイズ(DSS)を超過することになるアクティブ・データが検出されました。この結果、オブジェクトは、Old世代(この出力には非表示)へ昇格され、殿堂入りしきい値(TT)が、31から2へ降格されました。
.PP
別のコレクションが、5番目と6番目のサンプル間で行われています。このコレクションでは、Survivorがほとんど見られず、殿堂入りしきい値を31に戻しました。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjstat \-gcnew \-h3 21891 250\fR
\fB S0C    S1C    S0U    S1U   TT MTT  DSS      EC       EU     YGC     YGCT\fR
\fB  64\&.0   64\&.0    0\&.0   31\&.7 31  31   32\&.0    512\&.0    178\&.6    249    0\&.203\fR
\fB  64\&.0   64\&.0    0\&.0   31\&.7 31  31   32\&.0    512\&.0    355\&.5    249    0\&.203\fR
\fB  64\&.0   64\&.0   35\&.4    0\&.0  2  31   32\&.0    512\&.0     21\&.9    250    0\&.204\fR
\fB S0C    S1C    S0U    S1U   TT MTT  DSS      EC       EU     YGC     YGCT\fR
\fB  64\&.0   64\&.0   35\&.4    0\&.0  2  31   32\&.0    512\&.0    245\&.9    250    0\&.204\fR
\fB  64\&.0   64\&.0   35\&.4    0\&.0  2  31   32\&.0    512\&.0    421\&.1    250    0\&.204\fR
\fB  64\&.0   64\&.0    0\&.0   19\&.0 31  31   32\&.0    512\&.0     84\&.4    251    0\&.204\fR
\fB S0C    S1C    S0U    S1U   TT MTT  DSS      EC       EU     YGC     YGCT\fR
\fB  64\&.0   64\&.0    0\&.0   19\&.0 31  31   32\&.0    512\&.0    306\&.7    251    0\&.204\fR
 
.fi
.if n \{\
.RE
.\}
.SS "サンプルごとのタイムスタンプの挿入"
.PP
この例では、lvmid21891へ接続し、250ミリ秒間隔で3つのサンプルを取得しています。\fB\-t\fRオプションを使用して、最初の列にサンプルごとのタイムスタンプを表示しています。
.PP
Timestamp列には、ターゲットJVMの起動時からの経過時間が、秒単位でレポートされています。さらに、\fB\-gcoldcapacity\fR出力では、割当リクエストまたは昇格リクエストあるいはその両方を満たすためにヒープが拡張するたびに、Old世代の容量(OGC)とOld領域の容量(OC)とが増加していることがわかります。81番目のフル・ガベージ・コレクション(FGC)の後、Old世代の容量(OGC)は11,696KBから13,820KBに増加しました。この世代(および領域)の最大容量は、60,544KB (OGCMX)なので、まだ拡張できる余裕が残されています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBTimestamp      OGCMN    OGCMX     OGC       OC       YGC   FGC    FGCT    GCT\fR
\fB          150\&.1   1408\&.0  60544\&.0  11696\&.0  11696\&.0   194    80    2\&.874   3\&.799\fR
\fB          150\&.4   1408\&.0  60544\&.0  13820\&.0  13820\&.0   194    81    2\&.938   3\&.863\fR
\fB          150\&.7   1408\&.0  60544\&.0  13820\&.0  13820\&.0   194    81    2\&.938   3\&.863\fR
 
.fi
.if n \{\
.RE
.\}
.SS "リモートJVMのインストゥルメンテーションのモニター"
.PP
この例は、\fB\-gcutil\fRオプションを使用して、remote\&.domainというシステム上のlvmid 40496に接続し、サンプルを秒単位で無期限に取得しています。
.PP
lvmidは、リモート・ホストの名前と結合されて、\fB40496@remote\&.domain\fRの\fIvmid\fRを構成しています。結果として、このvmidは、\fBrmi\fRプロトコルを使用して、リモート・ホスト上のデフォルトの\fBjstatd\fRサーバーと通信します。\fBjstatd\fRサーバーは、\fBrmiregistry\fRコマンドを使用して、\fBrmiregistry\fRコマンドのデフォルトのポート(ポート1099)にバインドされた\fBremote\&.domain\fRに配置されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjstat \-gcutil 40496@remote\&.domain 1000\fR
\fB\fI\&.\&.\&. output omitted\fR\fR
 
.fi
.if n \{\
.RE
.\}
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
java(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jps(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jstatd(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
rmiregistry(1)
.RE
.br
'pl 8.5i
'bp
