'\" t
.\" Copyright (c) 1998, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: keytool
.\" Language: Japanese
.\" Date: 2015年3月3日
.\" SectDesc: セキュリティ・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "keytool" "1" "2015年3月3日" "JDK 8" "セキュリティ・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
keytool \- 暗号化鍵、X\&.509証明書チェーンおよび信頼できる証明書を含むキーストア(データベース)を管理します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool\fR [\fIcommands\fR]
.fi
.if n \{\
.RE
.\}
.PP
\fIcommands\fR
.RS 4
コマンドを参照してください。これらのコマンドは、次のようにタスク別に分類されます。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
キーストアへのデータの作成または追加
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-gencert
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-genkeypair
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-genseckey
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-importcert
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-importpassword
.RE
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
別のキーストアの内容のインポート
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-importkeystore
.RE
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
証明書リクエストの生成
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-certreq
.RE
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
データのエクスポート
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-exportcert
.RE
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
データの表示
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-list
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-printcert
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-printcertreq
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-printcrl
.RE
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
キーストアの管理
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-storepasswd
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-keypasswd
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-delete
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-changealias
.RE
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
ヘルプの表示
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-help
.RE
.RE
.RE
.SH "説明"
.PP
\fBkeytool\fRコマンドは、鍵と証明書を管理するためのユーティリティです。これにより、ユーザーは自分の公開鍵と秘密鍵のペアおよび関連する証明書を管理し、デジタル署名を使用した自己認証(他のユーザーまたはサービスに対して自分自身を認証すること)や、データの整合性と証明書に関するサービスを利用することができます。\fBkeytool\fRコマンドでは、通信しているピアの公開鍵をキャッシュすることもできます(証明書のフォームで)。
.PP
証明書とは、あるエンティティ(人物、会社など)からのデジタル署名付きの文書のことです。証明書には、他のあるエンティティの公開鍵(およびその他の情報)が特別な値を持っていることが書かれています。(証明書を参照してください。)データにデジタル署名が付いている場合は、デジタル署名を検証することで、データの整合性およびデータが本物であることをチェックできます。データの整合性とは、データが変更されたり、改変されたりしていないことを意味します。また、データが本物であるとは、そのデータが、データを作成して署名したと称する人物から渡されたデータであることを意味します。
.PP
また、\fBkeytool\fRコマンドを使用すれば、対称暗号化/復号化(DES)で使用される秘密鍵およびパスフレーズを管理することもできます。
.PP
\fBkeytool\fRコマンドは、鍵と証明書をキーストアに格納します。キーストアの別名を参照してください。
.SH "コマンドとオプションに関する注意"
.PP
様々なコマンドとその説明については、コマンドを参照してください。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
どのコマンド名およびオプション名にも先頭にマイナス記号(\-)が付きます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
各コマンドのオプションは任意の順序で指定できます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
イタリックになっていないすべての項目、または中カッコか角カッコで囲まれているすべての項目は、そのとおりに指定する必要があります。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
オプションを囲む中カッコは、そのオプションをコマンド行で指定しなかった場合に、デフォルト値が使用されることを意味します。オプションのデフォルト値を参照してください。中カッコは、\fB\-v\fR、\fB\-rfc\fRおよび\fB\-J\fRオプションを囲むためにも使用されますが、これらのオプションはコマンド行で指定された場合にのみ意味を持ちます。指定されていない場合以外、デフォルト値はありません。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
オプションを囲む角カッコは、そのオプションをコマンド行で指定しなかった場合に、値の入力を求められることを意味します。\fB\-keypass\fRオプションの場合、オプションをコマンド行で指定しなかった場合は、\fBkeytool\fRコマンドがまずキーストアのパスワードを使用して非公開/秘密鍵の復元を試みます。この試みが失敗した場合、\fBkeytool\fRコマンドにより、非公開/秘密鍵のパスワードの入力を求められます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
イタリックの項目の実際の値(オプションの値)は、指定する必要があります。たとえば、\fB\-printcert\fRコマンドの形式は次のとおりです。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-printcert {\-file \fR\fB\fIcert_file\fR\fR\fB} {\-v}\fR
.fi
.if n \{\
.RE
.\}
.sp
\fB\-printcert\fRコマンドを指定する場合は、\fIcert_file\fRを実際のファイル名で置き換えます。例:
\fBkeytool \-printcert \-file VScert\&.cer\fR
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
オプションの値に空白(スペース)が含まれている場合は、値を引用符で囲む必要があります。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-help\fRオプションがデフォルトです。\fBkeytool\fRコマンドは、\fBkeytool \-help\fRと同じです。
.RE
.SH "オプションのデフォルト値"
.PP
次の例で、様々なオプション値のデフォルト値を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-alias "mykey"\fR
\fB \fR
\fB\-keyalg\fR
\fB    "DSA" (when using \-genkeypair)\fR
\fB    "DES" (when using \-genseckey)\fR
\fB \fR
\fB\-keysize\fR
\fB    2048 (when using \-genkeypair and \-keyalg is "RSA")\fR
\fB    1024 (when using \-genkeypair and \-keyalg is "DSA")\fR
\fB    256 (when using \-genkeypair and \-keyalg is "EC")\fR
\fB    56 (when using \-genseckey and \-keyalg is "DES")\fR
\fB    168 (when using \-genseckey and \-keyalg is "DESede")\fR
\fB \fR
\fB\-validity 90\fR
\fB \fR
\fB\-keystore <the file named \&.keystore in the user\*(Aqs home directory>\fR
\fB \fR
\fB\-storetype <the value of the "keystore\&.type" property in the\fR
\fB    security properties file, which is returned by the static\fR
\fB    getDefaultType method in java\&.security\&.KeyStore>\fR
\fB \fR
\fB\-file\fR
\fB    stdin (if reading)\fR
\fB    stdout (if writing)\fR
\fB \fR
\fB\-protected false\fR
 
.fi
.if n \{\
.RE
.\}
.PP
公開/秘密鍵ペアの生成において、署名アルゴリズム(\fB\-sigalg\fRオプション)は、基になる秘密鍵のアルゴリズムから派生します。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
基になる秘密鍵がDSAタイプである場合は、\fB\-sigalg\fRオプションのデフォルト値はSHA1withDSAになります。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
基になる秘密鍵がRSAタイプである場合は、\fB\-sigalg\fRオプションのデフォルト値はSHA256withRSAになります。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
基になる秘密鍵がECタイプである場合は、\fB\-sigalg\fRオプションのデフォルト値はSHA256withECDSAになります。
.RE
.PP
\fB\-keyalg\fRおよび\fB\-sigalg\fR引数の完全なリストについては、
http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/crypto/CryptoSpec\&.html#AppAの「Java Cryptography Architecture (JCA) Reference Guide」を参照してください。
.SH "一般オプション"
.PP
\fB\-v\fRオプションは、\fB\-help\fRコマンドを除くすべてのコマンドで使用できます。\fB\-v\fRオプションを指定した場合、コマンドは冗長モードで実行され、詳細な情報が出力されます。
.PP
任意のコマンドで指定できる\fB\-Jjavaoption\fR引数もあります。\fB\-Jjavaoption\fRを指定した場合、指定された\fBjavaoption\fR文字列がJavaインタプリタに直接渡されます。このオプションには、空白を含めることはできません。このオプションは、実行環境またはメモリー使用を調整する場合に便利です。指定できるインタプリタ・オプションを一覧表示するには、コマンド行で\fBjava \-h\fRまたは\fBjava \-X\fRと入力してください。
.PP
次のオプションは、キーストアに対する操作を行うすべてのコマンドで指定できます。
.PP
\-storetype \fIstoretype\fR
.RS 4
この修飾子は、インスタンスを生成するキーストアのタイプを指定します。
.RE
.PP
\-keystore \fIkeystore\fR
.RS 4
キーストアの場所を指定します。
.sp
特定の\fBkeytool\fRコマンドを実行する際に、JKS
\fBstoretype\fRが使用され、かつキーストア・ファイルがまだ存在していなかった場合、新しいキーストア・ファイルが作成されます。たとえば、\fBkeytool \-genkeypair\fRの呼出し時に\fB\-keystore\fRオプションが指定されなかった場合、\fB\&.keystore\fRという名前のデフォルト・キーストア・ファイルがユーザーのホーム・ディレクトリ内にまだ存在していなければ、そこに作成されます。同様に、\fB\-keystore ks_file\fRというオプションが指定されてもそのks_fileが存在しなかった場合、そのファイルが作成されます。JKS
\fBstoretype\fRの詳細は、\fIの\fRKeyStoreの実装キーストアの別名に関する項を参照してください。
.sp
\fB\-keystore\fRオプションからの入力ストリームは、\fBKeyStore\&.load\fRメソッドに渡されます。URLとして\fBNONE\fRが指定されている場合は、nullのストリームが\fBKeyStore\&.load\fRメソッドに渡されます。\fBNONE\fRは、KeyStoreがファイルベースではない場合に指定してください。たとえば、ハードウェア・トークン・デバイス上に存在している場合などです。
.RE
.PP
\-storepass[:\fIenv\fR| :\fIfile\fR] argument
.RS 4
キーストアの整合性を保護するために使用するパスワードを指定します。
.sp
修飾子\fBenv\fRまたは\fBfile\fRを指定しない場合、パスワードの値は\fBargument\fRになります。この値は、6文字以上にする必要があります。それ以外の場合、パスワードは次のようにして取得されます。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBenv\fR:
\fBargument\fRという名前の環境変数からパスワードを取得します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBfile\fR: argumentという名前のファイルからパスワードを取得します。
.RE
.sp
\fB注意:\fR
\fB\-keypass\fR、\fB\-srckeypass\fR、\-\fBdestkeypass\fR、\fB\-srcstorepass\fR、\fB\-deststorepass\fRなどのパスワードを必要とするその他のオプションはすべて、\fIenv\fRおよび\fIfile\fR修飾子を受け付けます。パスワード・オプションと修飾子は、必ずコロン(:)で区切ってください。
.sp
パスワードは、キーストアの内容にアクセスするすべてのコマンドで使用されます。この種のコマンドを実行するときに、コマンド行で\fB\-storepass\fRオプションを指定しなかった場合は、パスワードの入力を求められます。
.sp
キーストアから情報を取得する場合、パスワードは省略可能です。パスワードが指定されていない場合は、取得した情報の整合性を検証できず、警告が表示されます。
.RE
.PP
\-providerName \fIprovider_name\fR
.RS 4
セキュリティ・プロパティ・ファイル内に含まれる暗号化サービス・プロバイダ名を特定するために使用されます。
.RE
.PP
\-providerClass \fIprovider_class_name\fR
.RS 4
暗号化サービス・プロバイダがセキュリティ・プロパティ・ファイルに指定されていないときは、そのマスター・クラス・ファイルの名前を指定するときに使用されます。
.RE
.PP
\-providerArg \fIprovider_arg\fR
.RS 4
\fB\-providerClass\fRオプションとともに使用され、\fBprovider_class_name\fRのコンストラクタのオプションの文字列入力引数を表します。
.RE
.PP
\-protected
.RS 4
\fBtrue\fRまたは\fBfalse\fRのいずれか。パスワードを専用PINリーダーなどの保護された認証パス経由で指定する必要がある場合は、この値を\fBtrue\fRに指定する必要があります。\fB\-importkeystore\fRコマンドには2つのキーストアが関連しているため、ソース・キーストアと宛先キーストアにそれぞれ次の2つのオプション、\fB\-srcprotected\fRと\-\fBdestprotected\fRが用意されています。
.RE
.PP
\-ext \fI{name{:critical} {=value}}\fR
.RS 4
X\&.509証明書エクステンションを示します。このオプションを\fB\-genkeypair\fRおよび\fB\-gencert\fRで使用して、生成される証明書または\fB\-certreq\fRにエクステンションを埋め込み、証明書リクエストでリクエストされるエクステンションを示すことができます。このオプションは複数回指定できます。\fBname\fR引数には、サポートされているエクステンション名(名前付きエクステンションを参照)または任意のOID番号を指定できます。指定されている場合、\fBvalue\fR引数はエクステンションの引数を指します。\fIvalue\fRを省略した場合、エクステンションのデフォルト値またはエクステンションが引数を必要としないことを示します。\fB:critical\fR修飾子が指定された場合、エクステンションの\fBisCritical\fR属性は\fBtrue\fRで、指定されない場合は\fBfalse\fRです。\fB:critical\fRのかわりに\fB:c\fRを使用できます。
.RE
.SH "名前付きエクステンション"
.PP
\fBkeytool\fRコマンドは、次の名前のエクステンションをサポートしています。名前の大/小文字は区別されません。
.PP
BCまたはBasicContraints
.RS 4
\fB値\fR: 完全な形式は、\fBca:{true|false}[,pathlen:<len>]\fRまたは\fB<len>\fR(\fBca:true,pathlen:<len>\fRの短縮形)です。<\fBlen\fR>を省略すると、\fBca:true\fRの意味になります。
.RE
.PP
KUまたはKeyUsage
.RS 4
\fB値\fR:
\fBusage\fR(\fBusage\fR)*、\fIusage\fRには\fBdigitalSignature\fR、\fBnonRepudiation\fR
(contentCommitment)、\fBkeyEncipherment\fR、\fBdataEncipherment\fR、\fBkeyAgreement\fR、\fBkeyCertSign\fR、\fBcRLSign\fR、\fBencipherOnly\fR、\fBdecipherOnly\fRのいずれかを指定できます。\fIusage\fR引数は、不明確にならないかぎり、最初の数文字(\fBdigitalSignature\fRの場合は\fBdig\fR)またはキャメルケース・スタイル(\fBdigitalSignature\fRの場合は\fBdS\fR、\fBcRLSign\fRの場合は\fBcRLS\fR)で省略表記できます。\fBusage\fR値は、大文字と小文字が区別されます。
.RE
.PP
EKUまたはExtendedKeyUsage
.RS 4
\fB値\fR:
\fBusage\fR(\fBusage\fR)*、\fIusage\fRには\fBanyExtendedKeyUsage\fR、\fBserverAuth\fR、\fBclientAuth\fR、\fBcodeSigning\fR、\fBemailProtection\fR、\fBtimeStamping\fR、\fBOCSPSigning\fRまたは任意の\fIOID文字列\fRのいずれかを指定できます。\fIusage\fR引数は、不明確にならないかぎり、最初の数文字またはキャメルケース・スタイルで省略表記できます。\fBusage\fR値は、大文字と小文字が区別されます。
.RE
.PP
SANまたはSubjectAlternativeName
.RS 4
\fB値\fR:
\fBtype\fR:\fBvalue\fR(t\fBype:value\fR)*、\fBtype\fRには\fBEMAIL\fR、\fBURI\fR、\fBDNS\fR、\fBIP\fRまたは\fBOID\fRを指定できます。\fBvalue\fR引数は、\fBtype\fRの文字列形式の値です。
.RE
.PP
IANまたはIssuerAlternativeName
.RS 4
\fB値\fR:
\fBSubjectAlternativeName\fRと同じです。
.RE
.PP
SIAまたはSubjectInfoAccess
.RS 4
\fB値\fR:
\fBmethod\fR:\fBlocation\-type\fR:\fBlocation\-value\fR
(\fBmethod:location\-type\fR:\fBlocation\-value\fR)*、\fBmethod\fRには\fBtimeStamping\fR、\fBcaRepository\fRまたは任意のOIDを指定できます。\fBlocation\-type\fRおよび\fBlocation\-value\fR引数には、\fBSubjectAlternativeName\fRエクステンションでサポートされる任意の\fBtype\fR:\fBvalue\fRを指定できます。
.RE
.PP
AIAまたはAuthorityInfoAccess
.RS 4
\fB値\fR:
\fBSubjectInfoAccess\fRと同じです。\fBmethod\fR引数には、\fBocsp\fR、\fBcaIssuers\fRまたは任意のOIDを指定できます。
.RE
.PP
\fBname\fRがOIDの場合、OCTET STRINGタイプと長さのバイトを除外したエクステンションについては、値は\fBextnValue\fRの16進ダンプのDERエンコーディングです。HEX文字列では、標準の16進数(0\-9、a\-f、A\-F)以外の文字は無視されます。したがって、01:02:03:04と01020304の両方とも同一の値として受け付けられます。値がない場合、エクステンションの値フィールドは空になります。
.PP
\fB\-gencert\fRでのみ使用する\fBhonored\fRという特別な名前は、証明書リクエストに含まれるエクステンションを優先する方法を示します。この名前の値は、\fBall\fR(リクエストされるすべてのエクステンションが優先される)、\fBname{:[critical|non\-critical]}\fR(名前付きのエクステンションが優先されるが、別の\fBisCritical\fR属性を使用する)、および\fB\-name\fR(\fBall\fRとともに使用し、例外を示す)のカンマ区切りリストです。デフォルトでは、リクエストされるエクステンションは優先されません。
.PP
\fB\-ext honored\fRオプションに加え、別の名前の、またはOID
\fB\-ext\fRのオプションを指定した場合は、このエクステンションが、すでに優先されているエクステンションに追加されます。ただし、この名前(またはOID)を優先される値でも使用した場合は、その値と重要性がリクエストに含まれるものをオーバーライドします。
.PP
\fBsubjectKeyIdentifier\fRエクステンションは常に作成されます。自己署名でない証明書の場合は、\fBauthorityKeyIdentifier\fRが作成されます。
.PP
\fB注意:\fR
ユーザーは、エクステンション(および証明書の他のフィールド)の組合せによっては、インターネットの標準に準拠しない場合があることに注意してください。証明書の準拠に関する警告を参照してください。
.SH "コマンド"
.PP
\-gencert
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-rfc} {\-infile \fR\fB\fIinfile\fR\fR\fB} {\-outfile \fR\fB\fIoutfile\fR\fR\fB} {\-alias \fR\fB\fIalias\fR\fR\fB} {\-sigalg \fR\fB\fIsigalg\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-dname \fR\fB\fIdname\fR\fR\fB} {\-startdate \fR\fB\fIstartdate\fR\fR\fB {\-ext \fR\fB\fIext\fR\fR\fB}* {\-validity \fR\fB\fIvalDays\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[\-keypass \fR\fB\fIkeypass\fR\fR\fB] {\-keystore \fR\fB\fIkeystore\fR\fR\fB} [\-storepass \fR\fB\fIstorepass\fR\fR\fB]\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-storetype \fR\fB\fIstoretype\fR\fR\fB} {\-providername \fR\fB\fIprovider_name\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerClass \fR\fB\fIprovider_class_name\fR\fR\fB {\-providerArg \fR\fB\fIprovider_arg\fR\fR\fB}}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-v} {\-protected} {\-Jjavaoption}\fR
.fi
.if n \{\
.RE
.\}
証明書リクエスト・ファイル(\fBkeytool\fR
\fB\-certreq\fRコマンドで作成可能)に対するレスポンスとして証明書を生成します。このコマンドは、\fIinfile\fRから(省略した場合は、標準入力から)リクエストを読み込み、別名の秘密鍵を使用してそのリクエストに署名して、X\&.509証明書を\fIoutfile\fRに(省略した場合は、標準出力に)出力します。\fB\-rfc\fRを指定した場合、出力形式はBASE64符号化のPEMになります。それ以外の場合は、バイナリDERが作成されます。
.sp
\fBsigalg\fR値には、証明書に署名を付けるときに使用するアルゴリズムを指定します。\fBstartdate\fR引数は、証明書の有効開始日時です。\fBvalDays\fR引数は、証明書の有効日数を示します。
.sp
\fBdname\fRを指定すると、生成される証明書の主体として使用されます。それ以外の場合は、証明書リクエストからの名前が使用されます。
.sp
\fBext\fR値は、証明書に埋め込まれるX\&.509エクステンションを示します。\fB\-ext\fRの構文については、一般オプションを参照してください。
.sp
\fB\-gencert\fRオプションを使用すると、証明書チェーンを作成できます。次の例では、\fBe1\fRという証明書を作成します。この証明書の証明書チェーンには、3つの証明書が含まれています。
.sp
次のコマンドは、\fBca\fR、\fBca1\fR、\fBca2\fRおよび\fBe1\fRの4つの鍵ペアを作成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-alias ca \-dname CN=CA \-genkeypair\fR
\fBkeytool \-alias ca1 \-dname CN=CA \-genkeypair\fR
\fBkeytool \-alias ca2 \-dname CN=CA \-genkeypair\fR
\fBkeytool \-alias e1 \-dname CN=E1 \-genkeypair\fR
 
.fi
.if n \{\
.RE
.\}
次の2つのコマンドは、署名付き証明書のチェーンを作成します。\fBca\fRは\fBca1\fRに署名し、\fBca1\fRは\fBca2\fRに署名します。すべて自己発行です。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-alias ca1 \-certreq |\fR
\fB    keytool \-alias ca \-gencert \-ext san=dns:ca1 |\fR
\fB    keytool \-alias ca1 \-importcert\fR
 
\fBkeytool \-alias ca2 \-certreq |\fR
\fB    $KT \-alias ca1 \-gencert \-ext san=dns:ca2 |\fR
\fB    $KT \-alias ca2 \-importcert\fR
 
.fi
.if n \{\
.RE
.\}
次のコマンドは、証明書\fBe1\fRを作成してファイル\fBe1\&.cert\fRに格納します。この証明書は\fBca2\fRによって署名されます。その結果、\fBe1\fRの証明書チェーンには\fBca\fR、\fBca1\fRおよび\fBca2\fRが含まれることになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-alias e1 \-certreq | keytool \-alias ca2 \-gencert > e1\&.cert\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-genkeypair
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-alias \fR\fB\fIalias\fR\fR\fB} {\-keyalg \fR\fB\fIkeyalg\fR\fR\fB} {\-keysize \fR\fB\fIkeysize\fR\fR\fB} {\-sigalg \fR\fB\fIsigalg\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[\-dname \fR\fB\fIdname\fR\fR\fB] [\-keypass \fR\fB\fIkeypass\fR\fR\fB] {\-startdate \fR\fB\fIvalue\fR\fR\fB} {\-ext \fR\fB\fIext\fR\fR\fB}*\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-validity \fR\fB\fIvalDays\fR\fR\fB} {\-storetype \fR\fB\fIstoretype\fR\fR\fB} {\-keystore \fR\fB\fIkeystore\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[\-storepass \fR\fB\fIstorepass\fR\fR\fB]\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerClass \fR\fB\fIprovider_class_name\fR\fR\fB {\-providerArg \fR\fB\fIprovider_arg\fR\fR\fB}}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-v} {\-protected} {\-Jjavaoption}\fR
.fi
.if n \{\
.RE
.\}
鍵のペア(公開鍵および関連する秘密鍵)を生成します。公開鍵はX\&.509 v3自己署名証明書でラップされます。証明書は、単一の要素を持つ証明書チェーンとして格納されます。この証明書チェーンと秘密鍵は、aliasで特定される新しいキーストア・エントリに格納されます。
.sp
\fBkeyalg\fR値は鍵ペアの生成に使用するアルゴリズムを、\fBkeysize\fR値は生成する各鍵のサイズを、それぞれ指定します。\fBsigalg\fR値は、自己署名証明書に署名を付けるために使用するアルゴリズムを指定します。このアルゴリズムは\fBkeyalg\fR値と互換性がある必要があります。
.sp
\fBdname\fR値には、\fBalias\fR値に関連付け、自己署名証明書のissuerフィールドとsubjectフィールドとして使用するX\&.500識別名を指定します。コマンド行で識別名を指定しなかった場合は、識別名の入力を求められます。
.sp
\fBkeypass\fR値には、生成される鍵のペアのうち、秘密鍵を保護するのに使用するパスワードを指定します。パスワードを指定しなかった場合は、パスワードの入力を求められます。このとき、[Return]キーを押すと、キーストアのパスワードと同じパスワードが鍵のパスワードに設定されます。\fBkeypass\fR値は、6文字以上にする必要があります。
.sp
\fBstartdate\fR値には、証明書の発行時刻を指定します。これは、X\&.509証明書の「Validity」フィールドの「Not Before」値とも呼ばれます。
.sp
オプションの値は、次の2つの形式のいずれかで設定できます。
.sp
\fB([+\-]nnn[ymdHMS])+\fR
.sp
\fB[yyyy/mm/dd] [HH:MM:SS]\fR
.sp
最初の形式では、発行時刻は、指定される値の分、現在の時刻から移ります。指定される値は、一連の下位の値を連結したものになります。下位の各値で、プラス記号(「+」)は時間が進むことを、マイナス記号(「\-」)は時間が戻ることを意味しています。移る時間は\fBnnn\fRで、単位は年、月、日、時間、分または秒です(それぞれ、1文字の\fBy\fR、\fBm\fR、\fBd\fR、\fBH\fR、\fBM\fRまたは\fBS\fR」で示されています)。下位の各値で\fBjava\&.util\&.GregorianCalendar\&.add(int field, int amount)\fRメソッドを使用することで、発行時刻の追加の値が左から右へ計算されます。たとえば、指定すると、発行時刻は次のようになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBCalendar c = new GregorianCalendar();\fR
\fBc\&.add(Calendar\&.YEAR, \-1);\fR
\fBc\&.add(Calendar\&.MONTH, 1);\fR
\fBc\&.add(Calendar\&.DATE, \-1);\fR
\fBreturn c\&.getTime()\fR
 
.fi
.if n \{\
.RE
.\}
2番目の形式では、ユーザーは、年/月/日と時間:分:秒の2つの部分で厳密な開始時刻を設定します(地元の時間帯を使用)。ユーザーは、1つの部分のみを指定できます。これは、もう1つの部分は現在の日付(または時刻)と同じになるということです。ユーザーは、形式の定義に示されているように、桁数を厳密に指定する必要があります(短い場合は0で埋めます)。日付と時刻の両方が指定された状態で、2つの部分の間に空白文字が1つ(1つのみ)あります。時間は常に24時間形式で指定してください。
.sp
オプションを指定しないと、開始日付は現在の時刻になります。オプションは、最大で1回指定できます。
.sp
\fBvalDays\fRの値には、証明書の有効日数を指定します(\fB\-startdate\fRで指定された日付、または\fB\-startdate\fRが指定されていない場合は現在の日付から始まります)。
.sp
このコマンドは、以前のリリースでは\fB\-genkey\fRという名前でした。このリリースでは、引き続き古い名前がサポートされています。今後は、新しい名前\fB\-genkeypair\fRが優先されます。
.RE
.PP
\-genseckey
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-alias \fR\fB\fIalias\fR\fR\fB} {\-keyalg \fR\fB\fIkeyalg\fR\fR\fB} {\-keysize \fR\fB\fIkeysize\fR\fR\fB} [\-keypass \fR\fB\fIkeypass\fR\fR\fB]\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-storetype \fR\fB\fIstoretype\fR\fR\fB} {\-keystore \fR\fB\fIkeystore\fR\fR\fB} [\-storepass \fR\fB\fIstorepass\fR\fR\fB]\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerClass \fR\fB\fIprovider_class_name\fR\fR\fB {\-providerArg \fR\fB\fIprovider_arg\fR\fR\fB}} {\-v}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-protected} {\-Jjavaoption}\fR
.fi
.if n \{\
.RE
.\}
秘密鍵を生成し、それを新しい\fBKeyStore\&.SecretKeyEntry\fR(\fBalias\fRで特定される)内に格納します。
.sp
\fBkeyalg\fR値は鍵ペアの生成に使用するアルゴリズムを、\fBkeysize\fR値は生成する各鍵のサイズを、それぞれ指定します。\fBkeypass\fR値は、秘密鍵を保護するパスワードです。パスワードを指定しなかった場合は、パスワードの入力を求められます。このとき、[Return]キーを押すと、\fBkeystore\fRのパスワードと同じパスワードが鍵のパスワードに設定されます。\fBkeypass\fR値は、6文字以上にする必要があります。
.RE
.PP
\-importcert
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-alias \fR\fB\fIalias\fR\fR\fB} {\-file \fR\fB\fIcert_file\fR\fR\fB} [\-keypass \fR\fB\fIkeypass\fR\fR\fB] {\-noprompt} {\-trustcacerts}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-storetype \fR\fB\fIstoretype\fR\fR\fB} {\-keystore \fR\fB\fIkeystore\fR\fR\fB} [\-storepass \fR\fB\fIstorepass\fR\fR\fB]\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerName \fR\fB\fIprovider_name\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerClass \fR\fB\fIprovider_class_name\fR\fR\fB {\-providerArg \fR\fB\fIprovider_arg\fR\fR\fB}}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-v} {\-protected} {\-Jjavaoption}\fR
.fi
.if n \{\
.RE
.\}
ファイル\fBcert_file\fRから証明書または証明書チェーン(証明書チェーンの場合は、PKCS#7形式の応答または一連のX\&.509証明書で提供されるもの)を読み込み、\fBalias\fRによって特定される\fBkeystore\fRエントリに格納します。ファイルが指定されていない場合は、\fBstdin\fRから証明書または証明書チェーンを読み込みます。
.sp
\fBkeytool\fRコマンドでは、X\&.509 v1、v2、v3の証明書、およびPKCS#7形式の証明書から構成されているPKCS#7形式の証明書チェーンをインポートできます。インポートするデータは、バイナリ符号化方式、または出力可能符号化方式(Base64符号化とも呼ばれる)のどちらかで提供する必要があります。出力可能符号化方式は、インターネットRFC 1421証明書符号化規格で定義されています。この符号化方式の場合、証明書は\fB\-\fR\fB\-\-\-\-BEGIN\fRで始まる文字列で開始され、\fB\-\-\-\-\-END\fRで始まる文字列で終了する必要があります。
.sp
証明書は、信頼できる証明書のリストに追加するため、および認証局(CA)に証明書署名リクエストを送信した結果としてCAから受信した証明書応答をインポートするため(\fBの\fR\-certreqコマンドオプションを参照)という2つの理由でインポートします。
.sp
どちらのタイプのインポートを行うかは、\fB\-alias\fRオプションの値によって指定します。別名がキー・エントリをポイントしない場合、\fBkeytool\fRコマンドはユーザーが信頼できる証明書エントリを追加しようとしているものとみなします。この場合、別名がキーストア内に存在していないことが必要です。別名がすでに存在している場合、その別名の信頼できる証明書がすでに存在することになるので、\fBkeytool\fRコマンドはエラーを出力し、証明書のインポートを行いません。別名がキー・エントリをポイントする場合、\fBkeytool\fRコマンドはユーザーが証明書応答をインポートしようとしているものとみなします。
.RE
.PP
\-importpassword
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-alias \fR\fB\fIalias\fR\fR\fB} [\-keypass \fR\fB\fIkeypass\fR\fR\fB] {\-storetype \fR\fB\fIstoretype\fR\fR\fB} {\-keystore \fR\fB\fIkeystore\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[\-storepass \fR\fB\fIstorepass\fR\fR\fB]\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerClass \fR\fB\fIprovider_class_name\fR\fR\fB {\-providerArg \fR\fB\fIprovider_arg\fR\fR\fB}}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-v} {\-protected} {\-Jjavaoption}\fR
.fi
.if n \{\
.RE
.\}
パスフレーズをインポートし、\fBalias\fRで識別される新規\fBKeyStore\&.SecretKeyEntry\fRに格納します。パスフレーズは、標準入力ストリームを介して提供できます。または、ユーザーにそのプロンプトが表示されます。\fBkeypass\fRは、インポートされるパスフレーズの保護に使用されるパスワードです。パスワードを指定しなかった場合は、パスワードの入力を求められます。このとき、[Return]キーを押すと、\fBkeystore\fRのパスワードと同じパスワードが鍵のパスワードに設定されます。\fBkeypass\fRは、6文字以上にする必要があります。
.RE
.PP
\-importkeystore
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-srcstoretype \fR\fB\fIsrcstoretype\fR\fR\fB} {\-deststoretype \fR\fB\fIdeststoretype\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[\-srcstorepass \fR\fB\fIsrcstorepass\fR\fR\fB] [\-deststorepass \fR\fB\fIdeststorepass\fR\fR\fB] {\-srcprotected}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-destprotected} \fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-srcalias \fR\fB\fIsrcalias\fR\fR\fB {\-destalias \fR\fB\fIdestalias\fR\fR\fB} [\-srckeypass \fR\fB\fIsrckeypass\fR\fR\fB]} \fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[\-destkeypass \fR\fB\fIdestkeypass\fR\fR\fB] {\-noprompt}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-srcProviderName \fR\fB\fIsrc_provider_name\fR\fR\fB} {\-destProviderName \fR\fB\fIdest_provider_name\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerClass \fR\fB\fIprovider_class_name\fR\fR\fB {\-providerArg \fR\fB\fIprovider_arg\fR\fR\fB}} {\-v}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-protected} {\-Jjavaoption}\fR
.fi
.if n \{\
.RE
.\}
ソース・キーストアからターゲット・キーストアへ、単一のエントリまたはすべてのエントリをインポートします。
.sp
\fB\-srcalias\fRオプションが指定された場合、このコマンドは、その別名で特定される単一のエントリをターゲット・キーストアにインポートします。\fBdestalias\fR経由でターゲット別名が指定されなかった場合、\fBsrcalias\fRがターゲット別名として使用されます。ソースのエントリがパスワードで保護されていた場合、\fBsrckeypass\fRを使用してそのエントリが回復されます。\fIsrckeypass\fRが指定されなかった場合、\fBkeytool\fRコマンドは\fBsrcstorepass\fRを使用してそのエントリを回復しようとします。\fBsrcstorepass\fRが指定されなかったか正しくなかった場合、ユーザーはパスワードの入力を求められます。ターゲットのエントリは\fBdestkeypass\fRによって保護されます。\fBdestkeypass\fRが指定されなかった場合、ターゲット・エントリはソース・エントリのパスワードによって保護されます。たとえば、ほとんどのサード・パーティ・ツールでは、PKCS #12キーストアで\fBstorepass\fRと\fBkeypass\fRが同じである必要があります。これらのツールのPKCS #12キーストアを作成する場合は、常に\fB\-destkeypass\fRと\fB\-deststorepass\fRが同じになるように指定します。
.sp
\fB\-srcalias\fRオプションが指定されなかった場合、ソース・キーストア内のすべてのエントリがターゲット・キーストア内にインポートされます。各ターゲット・エントリは対応するソース・エントリの別名の下に格納されます。ソースのエントリがパスワードで保護されていた場合、\fBsrcstorepass\fRを使用してそのエントリが回復されます。\fBsrcstorepass\fRが指定されなかったか正しくなかった場合、ユーザーはパスワードの入力を求められます。ソース・キーストア内のあるエントリ・タイプがターゲット・キーストアでサポートされていない場合や、あるエントリをターゲット・キーストアに格納する際にエラーが発生した場合、ユーザーはそのエントリをスキップして処理を続行するか、または中止するかの選択を求められます。ターゲット・エントリはソース・エントリのパスワードによって保護されます。
.sp
ターゲット別名がターゲット・キーストア内にすでに存在していた場合、ユーザーは、そのエントリを上書きするか、あるいは異なる別名の下で新しいエントリを作成するかの選択を求められます。
.sp

\fB\-noprompt\fRオプションを指定した場合、ユーザーは新しいターゲット別名の入力を求められません。既存のエントリがそのターゲット別名で上書きされます。インポートできないエントリはスキップされ、警告が出力されます。
.RE
.PP
\-printcertreq
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-file \fR\fB\fIfile\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
PKCS#10形式の証明書リクエストの内容を出力します。このリクエストは、\fBkeytool\fR
\fB\-certreq\fRコマンドで生成できます。このコマンドは、ファイルからリクエストを読み取ります。ファイルが存在しない場合、リクエストは標準入力から読み取られます。
.RE
.PP
\-certreq
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-alias \fR\fB\fIalias\fR\fR\fB} {\-dname \fR\fB\fIdname\fR\fR\fB} {\-sigalg \fR\fB\fIsigalg\fR\fR\fB} {\-file \fR\fB\fIcertreq_file\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[\-keypass \fR\fB\fIkeypass\fR\fR\fB] {\-storetype \fR\fB\fIstoretype\fR\fR\fB} {\-keystore \fR\fB\fIkeystore\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[\-storepass \fR\fB\fIstorepass\fR\fR\fB] {\-providerName \fR\fB\fIprovider_name\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerClass \fR\fB\fIprovider_class_name\fR\fR\fB {\-providerArg \fR\fB\fIprovider_arg\fR\fR\fB}}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-v} {\-protected} {\-Jjavaoption}\fR
.fi
.if n \{\
.RE
.\}
PKCS#10形式を使用して証明書署名リクエスト(CSR)を生成します。
.sp
CSRは、証明書発行局(CA)に送信することを目的としたものです。CAは、証明書要求者を(通常はオフラインで)認証し、証明書または証明書チェーンを送り返します。この証明書または証明書チェーンは、キーストア内の既存の証明書チェーン(最初は1つの自己署名証明書から構成される)に置き換えて使用します。
.sp
aliasに関連付けられた秘密鍵は、PKCS#10証明書リクエストを作成するのに使用されます。秘密鍵にアクセスするには、正しいパスワードを指定する必要があります。コマンド行で\fBkeypass\fRを指定しておらず、秘密鍵のパスワードがキーストアのパスワードと異なる場合は、秘密鍵のパスワードの入力を求められます。\fBdname\fRが指定されている場合は、それがCSRで主体として使用されます。それ以外の場合は、別名に関連付けられたX\&.500識別名が使用されます。
.sp
\fBsigalg\fR値には、CSRに署名を付けるときに使用するアルゴリズムを指定します。
.sp
CSRは、ファイルcertreq_fileに格納されます。ファイルが指定されていない場合は、\fBstdout\fRにCSRが出力されます。
.sp
CAからのレスポンスをインポートするには、\fBimportcert\fRコマンドを使用します。
.RE
.PP
\-exportcert
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-alias \fR\fB\fIalias\fR\fR\fB} {\-file \fR\fB\fIcert_file\fR\fR\fB} {\-storetype \fR\fB\fIstoretype\fR\fR\fB} {\-keystore \fR\fB\fIkeystore\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[\-storepass \fR\fB\fIstorepass\fR\fR\fB] {\-providerName \fR\fB\fIprovider_name\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerClass \fR\fB\fIprovider_class_name\fR\fR\fB {\-providerArg \fR\fB\fIprovider_arg\fR\fR\fB}}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-rfc} {\-v} {\-protected} {\-Jjavaoption}\fR
.fi
.if n \{\
.RE
.\}
\fIalias\fRに関連付けられた証明書をキーストアから読み込み、ファイルcert_fileに格納します。ファイルが指定されていない場合は、\fBstdout\fRに証明書が出力されます。
.sp
デフォルトでは、証明書はバイナリ符号化で出力されます。\fB\-rfc\fRオプションが指定されている場合、出力可能符号化方式の出力はインターネットRFC 1421証明書符号化規格で定義されます。
.sp
\fBalias\fRが、信頼できる証明書を参照している場合は、該当する証明書が出力されます。それ以外の場合、\fBalias\fRは、関連付けられた証明書チェーンを持つ鍵エントリを参照します。この場合は、チェーン内の最初の証明書が返されます。この証明書は、\fBalias\fRによって表されるエンティティの公開鍵を認証する証明書です。
.sp
このコマンドは、以前のリリースでは\fB\-export\fRという名前でした。このリリースでは、引き続き古い名前がサポートされています。今後は、新しい名前\fB\-exportcert\fRが優先されます。
.RE
.PP
\-list
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-alias \fR\fB\fIalias\fR\fR\fB} {\-storetype \fR\fB\fIstoretype\fR\fR\fB} {\-keystore \fR\fB\fIkeystore\fR\fR\fB} [\-storepass \fR\fB\fIstorepass\fR\fR\fB]\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerName \fR\fB\fIprovider_name\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerClass \fR\fB\fIprovider_class_name\fR\fR\fB {\-providerArg \fR\fB\fIprovider_arg\fR\fR\fB}}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-v | \-rfc} {\-protected} {\-Jjavaoption}\fR
.fi
.if n \{\
.RE
.\}
\fBalias\fRで特定されるキーストア・エントリの内容を\fBstdout\fRに出力します。\fBalias\fRが指定されていない場合は、キーストア全体の内容が表示されます。
.sp
このコマンドは、デフォルトでは証明書のSHA1フィンガープリントを表示します。
\fB\-v\fRオプションが指定されている場合は、所有者、発行者、シリアル番号、拡張機能などの付加的な情報とともに、人間が読むことのできる形式で証明書が表示されます。\fB\-rfc\fRオプションが指定されている場合は、出力可能符号化方式で証明書の内容が出力されます。出力可能符号化方式は、インターネットRFC 1421証明書符号化規格で定義されています。
.sp
\fB\-v\fRオプションと\fB\-rfc\fRオプションを同時に指定することはできません。
.RE
.PP
\-printcert
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-file \fR\fB\fIcert_file\fR\fR\fB | \-sslserver \fR\fB\fIhost\fR\fR\fB[:\fR\fB\fIport\fR\fR\fB]} {\-jarfile \fR\fB\fIJAR_file\fR\fR\fB {\-rfc} {\-v}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-Jjavaoption}\fR
.fi
.if n \{\
.RE
.\}
ファイルcert_file、host:portにあるSSLサーバー、または署名付きJARファイル\fBJAR_file\fR(\fB\-jarfile\fRオプションを指定)から証明書を読み込み、人間が読むことのできる形式で証明書の内容を表示します。ポートが指定されていない場合は、標準のHTTPSポート443が想定されます。\fB\-sslserver\fRおよび\-fileオプションを同時に指定することはできません。それ以外の場合、エラーが報告されます。オプションが指定されていない場合は、\fBstdin\fRから証明書を読み込みます。
.sp
\fB\-rfc\fRが指定されている場合、\fBkeytool\fRコマンドは、インターネットRFC 1421証明書符号化標準で定義されているように、PEMモードで証明書を出力します。インターネットRFC 1421証明書符号化規格を参照してください。
.sp
ファイルまたは\fBstdin\fRから証明書を読み込む場合、その証明書は、インターネットRFC 1421証明書符号化標準で定義されているように、バイナリ符号化方式または出力可能符号化方式で表示できます。
.sp
SSLサーバーがファイアウォールの背後にある場合は、\fB\-J\-Dhttps\&.proxyHost=proxyhost\fRおよび\fB\-J\-Dhttps\&.proxyPort=proxyport\fRオプションをコマンド行で指定して、プロキシ・トンネリングを使用できます。http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/jsse/JSSERefGuide\&.htmlの
「Java Secure Socket Extension (JSSE) Reference Guide」を参照してください
.sp
\fB注意:\fR
このオプションはキーストアとは関係なく使用できます。
.RE
.PP
\-printcrl
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-file \fR\fB\fIcrl_\fR\fR\fB {\-v}\fR
.fi
.if n \{\
.RE
.\}
ファイル\fBcrl_\fRから証明書失効リスト(CRL)を読み込みます。CRLは、発行したCAによって失効されたデジタル証明書のリストです。CAは、\fBcrl_\fRを生成します。
.sp
\fB注意:\fR
このオプションはキーストアとは関係なく使用できます。
.RE
.PP
\-storepasswd
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[\-new \fR\fB\fInew_storepass\fR\fR\fB] {\-storetype \fR\fB\fIstoretype\fR\fR\fB} {\-keystore \fR\fB\fIkeystore\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[\-storepass \fR\fB\fIstorepass\fR\fR\fB] {\-providerName \fR\fB\fIprovider_name\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerClass \fR\fB\fIprovider_class_name\fR\fR\fB {\-providerArg \fR\fB\fIprovider_arg\fR\fR\fB}}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-v} {\-Jjavaoption}\fR
.fi
.if n \{\
.RE
.\}
キーストアの内容の整合性を保護するために使用するパスワードを変更します。\fBnew_storepass\fRには、新しいパスワードを指定します。new_storepassは、6文字以上である必要があります。
.RE
.PP
\-keypasswd
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-alias \fR\fB\fIalias\fR\fR\fB} [\-keypass \fR\fB\fIold_keypass\fR\fR\fB] [\-new \fR\fB\fInew_keypass\fR\fR\fB] {\-storetype \fR\fB\fIstoretype\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-keystore \fR\fB\fIkeystore\fR\fR\fB} [\-storepass \fR\fB\fIstorepass\fR\fR\fB] {\-providerName \fR\fB\fIprovider_name\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerClass \fR\fB\fIprovider_class_name\fR\fR\fB {\-providerArg \fR\fB\fIprovider_arg\fR\fR\fB}} {\-v}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-Jjavaoption}\fR
.fi
.if n \{\
.RE
.\}
\fBalias\fRによって特定される非公開/秘密鍵を保護するためのパスワードを、\fBold_keypass\fRから\fBnew_keypass\fRに変更します。new_keypassは、6文字以上である必要があります。
.sp
コマンド行で\fB\-keypass\fRオプションを指定しておらず、鍵のパスワードがキーストアのパスワードと異なる場合は、鍵のパスワードの入力を求められます。
.sp
コマンド行で\fB\-new\fRオプションを指定しなかった場合は、新しいパスワードの入力を求められます。
.RE
.PP
\-delete
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[\-alias \fR\fB\fIalias\fR\fR\fB] {\-storetype \fR\fB\fIstoretype\fR\fR\fB} {\-keystore \fR\fB\fIkeystore\fR\fR\fB} [\-storepass \fR\fB\fIstorepass\fR\fR\fB]\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerName \fR\fB\fIprovider_name\fR\fR\fB}  \fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerClass \fR\fB\fIprovider_class_name\fR\fR\fB {\-providerArg \fR\fB\fIprovider_arg\fR\fR\fB}}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-v} {\-protected} {\-Jjavaoption}\fR
.fi
.if n \{\
.RE
.\}
\fBalias\fRによって特定されるエントリをキーストアから削除します。コマンド行で別名を指定しなかった場合は、別名の入力を求められます。
.RE
.PP
\-changealias
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-alias \fR\fB\fIalias\fR\fR\fB} [\-destalias \fR\fB\fIdestalias\fR\fR\fB] [\-keypass \fR\fB\fIkeypass\fR\fR\fB] {\-storetype \fR\fB\fIstoretype\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-keystore \fR\fB\fIkeystore\fR\fR\fB} [\-storepass \fR\fB\fIstorepass\fR\fR\fB] {\-providerName \fR\fB\fIprovider_name\fR\fR\fB}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-providerClass \fR\fB\fIprovider_class_name\fR\fR\fB {\-providerArg \fR\fB\fIprovider_arg\fR\fR\fB}} {\-v}\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB{\-protected} {\-Jjavaoption}\fR
.fi
.if n \{\
.RE
.\}
指定された\fBalias\fRから新しい別名\fBdestalias\fRへ、既存のキーストア・エントリを移動します。ターゲット別名を指定しなかった場合、ターゲット別名の入力を求められます。元のエントリがエントリ・パスワードで保護されていた場合、\fB\-keypass\fRオプションでそのパスワードを指定できます。鍵パスワードが指定されなかった場合、\fBstorepass\fR(指定された場合)がまず試みられます。その試みが失敗すると、ユーザーはパスワードの入力を求められます。
.RE
.PP
\-help
.RS 4
基本的なコマンドとそのオプションの一覧を表示します。
.sp
特定のコマンドの詳細を参照するには、次のように入力してください:
\fBkeytool \-command_name \-help\fR。\fBcommand_name\fRはコマンドの名前です。
.RE
.SH "例"
.PP
この例では、公開/秘密鍵のペアおよび信頼できるエンティティからの証明書を管理するためのキーストアを作成する手順を示します。
.SS "鍵のペアの生成"
.PP
まず、キーストアを作成して鍵のペアを生成します。単一行に入力する、次のようなコマンドを使用できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-genkeypair \-dname "cn=Mark Jones, ou=Java, o=Oracle, c=US"\fR
\fB    \-alias business \-keypass <new password for private key>\fR
\fB    \-keystore /working/mykeystore\fR
\fB    \-storepass <new password for keystore> \-validity 180\fR
 
.fi
.if n \{\
.RE
.\}
.PP
コマンドは、workingディレクトリに\fBmykeystore\fRという名前のキーストアを作成し(キーストアはまだ存在していないと仮定)、作成したキーストアに、\fB<new password for keystore>\fRで指定したパスワードを割り当てます。生成する公開鍵と秘密鍵のペアに対応するエンティティの「識別名」は、通称がMark Jones、組織単位がJava、組織がOracle、2文字の国番号がUSです。公開鍵と秘密鍵のサイズはどちらも1024ビットで、鍵の作成にはデフォルトのDSA鍵生成アルゴリズムを使用します。
.PP
このコマンドは、デフォルトのSHA1withDSA署名アルゴリズムを使用して、公開鍵と識別名情報を含む自己署名証明書を作成します。証明書の有効期間は180日です。証明書は、別名\fBbusiness\fRで特定されるキーストア・エントリ内の秘密鍵に関連付けられます。秘密鍵には、\fB<new password for private key>\fRで指定したパスワードが割り当てられます。
.PP
オプションのデフォルト値を使用する場合、コマンドは大幅に短くなります。この場合、オプションは不要です。デフォルト値を持つオプションでは、オプションを指定しなければデフォルト値が使用されます。必須値の入力を求められます。使用可能な値は次のとおりです。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-genkeypair\fR
 
.fi
.if n \{\
.RE
.\}
.PP
この場合は、\fBmykey\fRという別名でキーストア・エントリが作成され、新しく生成された鍵のペア、および90日間有効な証明書がこのエントリに格納されます。このエントリは、ホーム・ディレクトリ内の\fB\&.keystore\fRという名前のキーストアに置かれます。キーストアは、まだ存在していない場合に作成されます。識別名情報、キーストアのパスワードおよび秘密鍵のパスワードの入力を求められます。
.PP
以降では、オプションを指定しないで\fB\-genkeypair\fRコマンドを実行したものとして例を示します。情報の入力を求められた場合は、最初に示した\fB\-genkeypair\fRコマンドの値を入力したものとします。たとえば識別名には\fBcn=Mark Jones\fR、\fBou=Java\fR、\fBo=Oracle\fR、\fBc=US\fRと指定します。
.SS "CAからの署名付き証明書のリクエスト"
.PP
自己署名証明書を作成する鍵のペアの生成。証明書に証明書発行局(CA)の署名が付いていれば、他のユーザーから証明書が信頼される可能性も高くなります。CAの署名を取得するには、まず、証明書署名リクエスト(CSR)を生成します。たとえば、次のようにします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-certreq \-file MarkJ\&.csr\fR
 
.fi
.if n \{\
.RE
.\}
.PP
CSR(デフォルト別名\fBmykey\fRによって特定されるエンティティのCSR)が作成され、MarkJ\&.csrという名前のファイルに置かれます。このファイルをCA (VeriSignなど)に提出します。CAは要求者を(通常はオフラインで)認証し、要求者の公開鍵を認証した署名付きの証明書を送り返します。場合によっては、CAが証明書のチェーンを返すこともあります。証明書のチェーンでは、各証明書がチェーン内のその前の署名者の公開鍵を認証します。
.SS "CAからの証明書のインポート"
.PP
作成した自己署名証明書は、証明書チェーンで置き換える必要があります。証明書チェーンでは、各証明書が、「ルート」CAを起点とするチェーン内の次の証明書の署名者の公開鍵を認証します。
.PP
CAからの証明書応答をインポートするには、キーストアか、\fBcacerts\fRキーストア・ファイル内に1つ以上の信頼できる証明書がある必要があります。コマンドの\fB\-importcert\fRを参照してください。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
証明応答が証明書チェーンの場合は、チェーンの最上位証明書が必要です。CAの公開鍵を認証するルートCA証明書。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
証明書応答が単一の証明書の場合は、発行CA(署名した)の証明書が必要です。その証明書が自己署名でない場合は、その署名者の証明書が必要であり、このようにして自己署名ルート証明書が必要になります。
.RE
.PP
\fBcacerts\fRキーストア・ファイルは、いくつかのVeriSignルートCA証明書を含んだ状態で出荷されているので、VeriSignの証明書を、信頼できる証明書としてキーストア内にインポートする必要がない場合があります。ただし、他のCAに対して署名付き証明書をリクエストしていて、このCAの公開鍵を認証する証明書が、\fBcacerts\fRにまだ追加されていない場合は、該当するCAからの証明書を、「信頼できる証明書」としてインポートする必要があります。
.PP
通常、CAからの証明書は、自己署名証明書、または他のCAによって署名された証明書です(後者の場合は、該当する他のCAの公開鍵を認証する証明書が必要)。ABC, Inc\&.,がCAで、ABCから自己署名証明書であるA\fBBCCA\&.cer\fRという名前のファイルを取得したとします(この証明書はCAの公開鍵を認証します)。信頼できる証明書として証明書をインポートするときは、証明書が有効であることを確認する必要があります。まず、証明書の内容を表示し\fBkeytool \-printcert\fRコマンドを使用するか、または\fB\-noprompt\fRオプションを指定しないで\fBkeytool \-importcert\fRコマンドを使用し、表示された証明書のフィンガープリントが、期待されるフィンガープリントと一致するかどうかを確認します。証明書を送信した人物に連絡し、この人物が提示した(または安全な公開鍵のリポジトリによって提示される)フィンガープリントと、上のコマンドで表示されたフィンガープリントとを比較します。フィンガープリントが一致すれば、送信途中で他の何者か(攻撃者など)による証明書のすり替えが行われていないことを確認できます。送信途中でこの種の攻撃が行われていた場合、チェックを行わずに証明書をインポートすると、攻撃者によって署名されたすべてのものを信頼することになります。
.PP
証明書が有効であると信頼する場合は、次のコマンドでキーストアに追加できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-importcert \-alias abc \-file ABCCA\&.cer\fR
 
.fi
.if n \{\
.RE
.\}
.PP
ABCCA\&.cerファイルのデータを含む信頼できる証明書のエントリがキーストア内に作成され、該当するエントリに\fBabc\fRという別名が割り当てられます。
.SS "CAからの証明書応答のインポート"
.PP
証明書署名リクエストの提出先のCAの公開鍵を認証する証明書をインポートした後は(または同種の証明書がすでにcacertsファイル内に存在している場合は)、証明応答をインポートし、自己署名証明書を証明書チェーンで置き換えることができます。このチェーンは、CAの応答がチェーンの場合に、リクエストに対するレスポンスとしてCAから送り返された証明書チェーンです。また、CAの応答が単一の証明書の場合は、この証明応答と、インポート先のキーストア内または\fBcacerts\fRキーストアファイル内にすでに存在する信頼できる証明書とを使用して構築した証明書チェーンです。
.PP
たとえば、証明書署名リクエストをVeriSignに送信する場合、送り返された証明書の名前がVSMarkJ\&.cerだとすると、次のようにして応答をインポートできます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-importcert \-trustcacerts \-file VSMarkJ\&.cer\fR
 
.fi
.if n \{\
.RE
.\}
.SS "公開鍵を認証する証明書のエクスポート"
.PP
\fBjarsigner\fRコマンドを使用してJava Archive (JAR)ファイルに署名する場合、このファイルを使用するクライアントは署名を認証する必要があります。クライアントが署名を認証する方法の1つに、まず自分の公開鍵の証明書を信頼できるエントリとしてクライアントのキーストアにインポートする方法があります。
.PP
そのためには、証明書をエクスポートして、クライアントに提供します。例として、次のコマンドを使用して、MJ\&.cerという名前のファイルに証明書をコピーできます。このコマンドでは、エントリに別名\fBmykey\fRがあると仮定しています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-exportcert \-alias mykey \-file MJ\&.cer\fR
 
.fi
.if n \{\
.RE
.\}
.PP
証明書と署名付きJARファイルを入手したクライアントは、\fBjarsigner\fRコマンドを使用して署名を認証できます。
.SS "キーストアのインポート"
.PP
コマンド\fBimportkeystore\fRを使用すれば、あるキーストアの全体を別のキーストア内にインポートできます。これは、鍵や証明書といったソースキーストア内のすべてのエントリが、単一のコマンドを使用してターゲットキーストア内にインポートされることを意味します。このコマンドを使用すれば、異なるタイプのキーストア内に含まれるエントリをインポートすることができます。インポート時には、ターゲット・キーストア内の新しいエントリはすべて、元と同じ別名および(秘密鍵や秘密鍵の場合は)保護用パスワードを持ちます。ソースキーストア内の非公開/秘密鍵をリカバリできない場合、\fBkeytool\fRコマンドはユーザーにパスワードの入力を求めます。このコマンドは、別名の重複を検出すると、ユーザーに新しい別名の入力を求めます。ユーザーは、新しい別名を指定することも、単純に既存の別名の上書きを\fBkeytool\fRコマンドに許可することもできます。
.PP
たとえば、通常のJKSタイプのキーストアkey\&.jks内のエントリをPKCS#11タイプのハードウェア・ベースのキーストア内にインポートするには、次のコマンドを使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-importkeystore\fR
\fB    \-srckeystore key\&.jks \-destkeystore NONE\fR
\fB    \-srcstoretype JKS \-deststoretype PKCS11\fR
\fB    \-srcstorepass <src keystore password>\fR
\fB    \-deststorepass <destination keystore pwd>\fR
 
.fi
.if n \{\
.RE
.\}
.PP
また、\fBimportkeystore\fRコマンドを使用すれば、あるソース・キーストア内の単一のエントリをターゲット・キーストアにインポートすることもできます。この場合は、前例のオプションに加えて、インポートする別名を指定する必要があります。\fB\-srcalias\fRオプションを指定する場合には、ターゲット別名もコマンドラインから指定できるほか、秘密/秘密鍵の保護用パスワードやターゲット保護用パスワードも指定できます。その方法を示すコマンドを次に示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-importkeystore\fR
\fB    \-srckeystore key\&.jks \-destkeystore NONE\fR
\fB    \-srcstoretype JKS \-deststoretype PKCS11\fR
\fB    \-srcstorepass <src keystore password>\fR
\fB    \-deststorepass <destination keystore pwd>\fR
\fB    \-srcalias myprivatekey \-destalias myoldprivatekey\fR
\fB    \-srckeypass <source entry password>\fR
\fB    \-destkeypass <destination entry password>\fR
\fB    \-noprompt\fR
 
.fi
.if n \{\
.RE
.\}
.SS "SSLサーバーの証明書の生成"
.PP
次に、3つのエンティティ、つまりルートCA(\fBroot\fR)、中間CA(\fBca\fR)およびSSLサーバー(\fBserver\fR)用の鍵ペアと証明書を生成する\fBkeytool\fRコマンドを示します。すべての証明書を同じキーストアに格納するようにしてください。これらの例では、RSAが推奨される鍵のアルゴリズムです。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-genkeypair \-keystore root\&.jks \-alias root \-ext bc:c\fR
\fBkeytool \-genkeypair \-keystore ca\&.jks \-alias ca \-ext bc:c\fR
\fBkeytool \-genkeypair \-keystore server\&.jks \-alias server\fR
\fB \fR
\fBkeytool \-keystore root\&.jks \-alias root \-exportcert \-rfc > root\&.pem\fR
\fB \fR
\fBkeytool \-storepass <storepass> \-keystore ca\&.jks \-certreq \-alias ca |\fR
\fB    keytool \-storepass <storepass> \-keystore root\&.jks\fR
\fB    \-gencert \-alias root \-ext BC=0 \-rfc > ca\&.pem\fR
\fBkeytool \-keystore ca\&.jks \-importcert \-alias ca \-file ca\&.pem\fR
\fB \fR
\fBkeytool \-storepass <storepass> \-keystore server\&.jks \-certreq \-alias server |\fR
\fB    keytool \-storepass <storepass> \-keystore ca\&.jks \-gencert \-alias ca\fR
\fB    \-ext ku:c=dig,kE \-rfc > server\&.pem\fR
\fBcat root\&.pem ca\&.pem server\&.pem |\fR
\fB    keytool \-keystore server\&.jks \-importcert \-alias server\fR
 
.fi
.if n \{\
.RE
.\}
.SH "用語"
.PP
キーストア
.RS 4
キーストアは、暗号化の鍵と証明書を格納するための機能です。
.RE
.PP
キーストアのエントリ
.RS 4
キーストアには異なるタイプのエントリを含めることができます。\fBkeytool\fRコマンドで最も適用範囲の広いエントリ・タイプは、次の2つです。
.sp
\fB鍵のエントリ\fR
\- 各エントリは、非常に重要な暗号化の鍵の情報を保持します。この情報は、許可していないアクセスを防ぐために、保護された形で格納されます。一般に、この種のエントリとして格納される鍵は、秘密鍵か、対応する公開鍵の証明書チェーンを伴う秘密鍵です。証明書チェーンを参照してください。\fBkeytool\fRコマンドがこの両方のタイプのエントリを処理できるのに対し、\fBjarsigner\fRツールは後者のタイプのエントリ、つまり秘密鍵とそれに関連付けられた証明書チェーンのみを処理します。
.sp
\fB信頼できる証明書のエントリ\fR: 各エントリは、第三者からの公開鍵証明書を1つ含んでいます。このエントリは、信頼できる証明書と呼ばれます。それは、証明書内の公開鍵が、証明書のSubject(所有者)によって特定されるアイデンティティに由来するものであることを、キーストアの所有者が信頼するからです。証明書の発行者は、証明書に署名を付けることによって、その内容を保証します。
.RE
.PP
キーストアの別名
.RS 4
キーストアのすべてのエントリ(鍵および信頼できる証明書エントリ)は、一意の別名を介してアクセスされます。
.sp
別名を指定するのは、\fB\-genseckey\fRコマンドを使用して秘密鍵を生成したり、\fB\-genkeypair\fRコマンドを使用して鍵ペア(公開鍵と秘密鍵)を生成したり、\fB\-importcert\fRコマンドを使用して証明書または証明書チェーンを信頼できる証明書のリストに追加するなど、特定のエンティティをキーストアに追加する場合です。これ以後、\fBkeytool\fRコマンドでエンティティを参照する場合は、このときに指定した別名を使用する必要があります。
.sp
たとえば、\fBduke\fRという別名を使用して新しい公開鍵と秘密鍵のペアを生成し、公開鍵を自己署名証明書でラップするとします。この場合は、次のコマンドを実行します。証明書チェーンを参照してください。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-genkeypair \-alias duke \-keypass dukekeypasswd\fR
 
.fi
.if n \{\
.RE
.\}
この例では、初期パスワードとして\fBdukekeypasswd\fRを指定しています。以後、別名\fBduke\fRに関連付けられた秘密鍵にアクセスするコマンドを実行するときは、このパスワードが必要になります。Dukeの秘密鍵のパスワードをあとから変更するには、次のコマンドを実行します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-keypasswd \-alias duke \-keypass dukekeypasswd \-new newpass\fR
 
.fi
.if n \{\
.RE
.\}
パスワードが、\fBdukekeypasswd\fRから\fBnewpass\fRに変更されます。テスト目的の場合、またはセキュアなシステムを使用している場合以外は、コマンド行やスクリプトでパスワードを指定しないでください。必要なパスワードのオプションをコマンド行で指定しなかった場合は、パスワードの入力を求められます。
.RE
.PP
キーストアの実装
.RS 4
\fBjava\&.security\fRパッケージで提供されている\fBKeyStore\fRクラスは、キーストア内の情報へのアクセスおよび情報の変更を行うための、明確に定義されたインタフェースを提供します。キーストアの固定実装としては、それぞれが特定のタイプのキーストアを対象とする複数の異なる実装が存在可能です。
.sp
現在、\fBkeytool\fRと\fBjarsigner\fRの2つのコマンド行ツールと、Policy Toolという名前のGUIベースのツールが、キーストアの実装を使用しています。\fBKeyStore\fRクラスは\fBpublic\fRであるため、ユーザーはKeyStoreを使用した他のセキュリティ・アプリケーションも作成できます。
.sp
キーストアには、Oracleが提供する組込みのデフォルトの実装があります。これは、JKSという名前の独自のキーストア・タイプ(形式)を利用するもので、キーストアをファイルとして実装しています。この実装では、個々の秘密鍵は個別のパスワードによって保護され、キーストア全体の整合性も(秘密鍵とは別の)パスワードによって保護されます。
.sp
キーストアの実装は、プロバイダベースです。具体的には、\fBKeyStore\fRによって提供されるアプリケーション・インタフェースがサービス・プロバイダ・インタフェース(SPI)に基づいて実装されます。つまり、対応する\fBKeystoreSpi\fR抽象クラス(これも\fBjava\&.security\fRパッケージに含まれています)があり、このクラスが、プロバイダが実装する必要のあるService Provider Interfaceのメソッドを定義しています。ここで、\fIプロバイダ\fRとは、Java Security APIによってアクセス可能なサービスのサブセットに対し、その固定実装を提供するパッケージまたはパッケージの集合のことです。キーストアの実装を提供するには、http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/crypto/HowToImplAProvider\&.htmlにある
Java暗号化アーキテクチャのプロバイダの実装方法で説明しているように、クライアントはプロバイダを実装し、\fBKeystoreSpi\fRサブクラスの実装を提供する必要があります。
.sp
アプリケーションでは、\fBKeyStore\fRクラスが提供する\fBgetInstance\fRファクトリ・メソッドを使用することで、様々なプロバイダから異なるタイプのキーストアの実装を選択できます。キーストアのタイプは、キーストア情報の格納形式とデータ形式を定義するとともに、キーストア内の非公開/秘密鍵とキーストアの整合性を保護するために使用されるアルゴリズムを定義します。異なるタイプのキーストアの実装には、互換性はありません。
.sp
\fBkeytool\fRコマンドは、任意のファイルベースのキーストア実装で動作します。コマンド行で渡されたキーストアの場所をファイル名として扱って、\fBFileInputStream\fRに変換し、ここからキーストア情報をロードします。\fBjarsigner\fRおよび\fBpolicytool\fRコマンドは、URLで指定できる任意の場所からキーストアを読み取ることができます。
.sp
\fBkeytool\fRと\fBjarsigner\fRの場合、\fB\-storetype\fRオプションを使用してコマンド行でキーストアのタイプを指定できます。Policy Toolの場合は、「キーストア」メニューによってキーストアのタイプを指定できます。
.sp
ユーザーがキーストアのタイプを明示的に指定しなかった場合、セキュリティ・プロパティ・ファイルで指定された\fBkeystore\&.type\fRプロパティの値に基づいて、ツールによってキーストアの実装が選択されます。このセキュリティ・プロパティ・ファイルは\fBjava\&.security\fRと呼ばれ、Windowsではセキュリティ・プロパティ・ディレクトリ\fBjava\&.home\elib\esecurity\fR、Oracle Solarisでは\fBjava\&.home/lib/security\fRにあります。\fBjava\&.home\fRは、実行時環境のディレクトリです。\fBjre\fRディレクトリは、SDKまたはJava Runtime Environment (JRE)の最上位のディレクトリにあります。
.sp
各ツールは、\fBkeystore\&.type\fRの値を取得し、この値で指定されたタイプのキーストアを実装しているプロバイダが見つかるまで、現在インストールされているすべてのプロバイダを調べます。そのプロバイダからのキーストアの実装を使用します。\fBKeyStore\fRクラスに定義されているstaticメソッド\fBgetDefaultType\fRを使用すると、アプリケーションやアプレットから\fBkeystore\&.type\fRプロパティの値を取得できます。次のコードは、デフォルトのキーストア・タイプ(\fBkeystore\&.type\fRプロパティで指定されたタイプ)のインスタンスを生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBKeyStore keyStore = KeyStore\&.getInstance(KeyStore\&.getDefaultType());\fR
 
.fi
.if n \{\
.RE
.\}
デフォルトのキーストア・タイプは\fBjks\fRで、これはOracleが提供する独自のタイプのキーストアの実装です。これは、セキュリティ・プロパティ・ファイル内の次の行によって指定されています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeystore\&.type=jks\fR
 
.fi
.if n \{\
.RE
.\}
各ツールでデフォルト以外のキーストアの実装を使用するには、上の行を変更して別のキーストアのタイプを指定します。たとえば、\fBpkcs12\fRと呼ばれるキーストアのタイプのキーストアの実装を提供するプロバイダ・パッケージがある場合、行を次のように変更します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeystore\&.type=pkcs12\fR
 
.fi
.if n \{\
.RE
.\}
\fB注意:\fR
キーストアのタイプの指定では、大文字と小文字は区別されません。たとえば、JKSとjksは同じものとして扱われます。
.RE
.PP
証明書
.RS 4
証明書(公開鍵証明書)とは、あるエンティティ(発行者)からのデジタル署名付きの文書のことです。証明書には、他のあるエンティティ(署名者)の公開鍵(およびその他の情報)が特別な値を持っていることが書かれています。次の用語は、証明書に関連しています。
.sp
\fB公開鍵\fR: 公開鍵は、特定のエンティティに関連付けられた数です。公開鍵は、該当するエンティティとの間に信頼できる関係を持つ必要があるすべての人に対して公開することを意図したものです。公開鍵は、署名を検証するのに使用されます。
.sp
\fBデジタル署名\fR: データがデジタル署名されると、そのデータは、エンティティのアイデンティティと、そのエンティティがデータの内容について知っていることを証明書する署名とともに格納されます。エンティティの秘密鍵を使用してデータに署名を付けると、データの偽造は不可能になります。
.sp
\fBアイデンティティ\fR: エンティティをアドレス指定する既知の方法。システムによっては、公開鍵をアイデンティティにするものがあります。公開鍵の他にも、Oracle Solaris UIDや電子メール・アドレス、X\&.509識別名など、様々なものをアイデンティティとすることができます。
.sp
\fB署名\fR: 署名は、なんらかのデータを基にエンティティの秘密鍵を使用して計算されます。署名者、証明書の場合は発行者とも呼ばれます。
.sp
\fB秘密鍵\fR: 秘密鍵は特定のエンティティのみが知っている数のことで、この数のことを、そのエンティティの秘密鍵といいます。秘密鍵は、他に知られないように秘密にしておくことが前提になっています。秘密鍵と公開鍵は、すべての公開鍵暗号化システムで対になって存在しています。DSAなどの典型的な公開鍵暗号化システムの場合、1つの秘密鍵は正確に1つの公開鍵に対応します。秘密鍵は、署名を計算するのに使用されます。
.sp
\fBエンティティ\fR: エンティティは、人、組織、プログラム、コンピュータ、企業、銀行など、一定の度合いで信頼の対象となる様々なものを指します。
.sp
公開鍵暗号化では、ユーザーの公開鍵にアクセスする必要があります。大規模なネットワーク環境では、互いに通信しているエンティティ間で以前の関係が引続き確立されていると仮定したり、使用されているすべての公開鍵を収めた信頼できるリポジトリが存在すると仮定したりすることは不可能です。このような公開鍵の配布に関する問題を解決するために証明書が考案されました。現在では、証明書発行局(CA)が信頼できる第三者として機能します。CAは、他のエンティティの証明書に署名する(発行する)行為を、信頼して任されているエンティティ(企業など)です。CAは法律上の契約に拘束されるので、有効かつ信頼できる証明書のみを作成するものとして扱われます。VeriSign、Thawte、Entrustをはじめ、多くの公的な証明書発行局が存在します。
.sp
Microsoftの認証サーバー、EntrustのCA製品などを所属組織内で利用すれば、独自の証明書発行局を運営することも可能です。\fBkeytool\fRコマンドを使用すると、証明書の表示、インポートおよびエクスポートを行うことができます。また、自己署名証明書を生成することもできます。
.sp
現在、\fBkeytool\fRコマンドはX\&.509証明書を対象にしています。
.RE
.PP
X\&.509証明書
.RS 4
X\&.509規格では、証明書に含める情報が定義されており、この情報を証明書に書き込む方法(データ形式)についても記述されています。証明書のすべてのデータは、ASN\&.1/DERと呼ばれる2つの関連規格を使用して符号化されます。Abstract Syntax Notation 1はデータについて記述しています。Definite Encoding Rulesは、データの保存および転送の方法について記述しています。
.sp
すべてのX\&.509証明書は、署名の他に次のデータを含んでいます。
.sp
\fBバージョン\fR: 証明書に適用されるX\&.509規格のバージョンを特定します。証明書に指定できる情報は、バージョンによって異なります。今のところ、3つのバージョンが定義されています。\fBkeytool\fRコマンドでは、v1、v2、v3の証明書をインポートおよびエクスポートできます。v3の証明書を生成します。
.sp
X\&.509 Version 1は、1988年から利用されて広く普及しており、最も一般的です。
.sp
X\&.509 Version 2では、Subjectや発行者の名前をあとで再利用できるようにするために、Subjectと発行者の一意識別子の概念が導入されました。ほとんどの証明書プロファイル文書では、名前を再使用しないことと、証明書で一意の識別子を使用しないことが、強く推奨されています。Version 2の証明書は、広くは使用されていません。
.sp
X\&.509 Version 3は最も新しい(1996年)規格で、エクステンションの概念をサポートしています。エクステンションは誰でも定義することができ、証明書に含めることができます。一般的なエクステンションとしては、KeyUsage(\fB署名専用\fRなど、鍵の使用を特定の目的に制限する)、AlternativeNames(DNS名、電子メール・アドレス、IPアドレスなど、他のアイデンティティを公開鍵に関連付けることができる)などがあります。エクステンションには、criticalというマークを付けて、そのエクステンションのチェックと使用を義務づけることができます。たとえば、criticalとマークされ、\fBkeyCertSign\fRが設定されたKeyUsageエクステンションが証明書に含まれている場合、この証明書をSSL通信中に提示すると、証明書が拒否されます。これは、証明書のエクステンションによって、関連する秘密鍵が証明書の署名専用として指定されており、SSLでは使用できないためです。
.sp
\fBシリアル番号\fR: 証明書を作成したエンティティは、そのエンティティが発行する他の証明書と区別するために、証明書にシリアル番号を割り当てます。この情報は、様々な方法で使用されます。たとえば、証明書が取り消されると、シリアル番号が証明書失効リスト(CRL)に格納されます。
.sp
\fB証明書アルゴリズム識別子\fR: 証明書に署名を付けるときにCAが使用したアルゴリズムを特定します。
.sp
\fB発行者名\fR: 証明書に署名を付けたエンティティのX\&.500識別名です。X\&.500識別名を参照してください。通常はCAです。この証明書を使用することは、証明書に署名を付けたエンティティを信頼することを意味します。ルートつまりトップレベルのCAの証明書など、場合によっては発行者が自身の証明書に署名を付けることがあります。
.sp
\fB有効期間\fR: 各証明書は限られた期間のみ有効です。この期間は開始の日時と終了の日時によって指定され、数秒の短い期間から100年という長期にわたることもあります。選択される有効期間は、証明書への署名に使用される秘密鍵の強度や証明書に支払う金額など、様々な要因で異なります。有効期間は、関連する秘密鍵が損われない場合に、エンティティが公開鍵を信頼できると期待される期間です。
.sp
\fB主体名\fR: 証明書で公開鍵を認証するエンティティの名前。この名前はX\&.500標準を使用するので、インターネット全体で一意なものと想定されます。これは、エンティティのX\&.500識別名(DN)です。X\&.500識別名を参照してください。次に例を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBCN=Java Duke, OU=Java Software Division, O=Oracle Corporation, C=US\fR
 
.fi
.if n \{\
.RE
.\}
これらはそれぞれ主体の通称(CN)、組織単位(OU)、組織(O)、国(C)を表します。
.sp
\fB主体の公開鍵情報\fR: 名前を付けられたエンティティの公開鍵とアルゴリズム識別子です。アルゴリズム識別子では、公開鍵に対して使用されている公開鍵暗号化システムおよび関連する鍵パラメータが指定されています。
.RE
.PP
証明書チェーン
.RS 4
\fBkeytool\fRコマンドでは、秘密鍵および関連する証明書チェーンを含むキーストアの鍵エントリを作成し、管理することができます。このようなエントリでは、秘密鍵に対応する公開鍵は、チェーンの最初の証明書に含まれています。
.sp
鍵を初めて作成すると、自己署名証明書という1つの要素のみを含むチェーンが開始されます。コマンドの\fB\-genkeypair\fRを参照してください。自己署名証明書は発行者(署名者)が主体と同じです。主体は、その公開鍵が証明書によって認証されるエンティティです。\fB\-genkeypair\fRコマンドを呼び出して新しい公開鍵と秘密鍵のペアを作成すると、公開鍵は常に自己署名証明書でラップされます。
.sp
この後、証明書署名リクエスト(CSR)が\fB\-certreq\fRコマンドで生成されて、CSRが証明書発行局(CA)に送信されると、CAからのレスポンスが\fB\-importcert\fRでインポートされ、元の自己署名証明書は証明書チェーンによって置き換えられます。\fBの\fR\-certreq\fBおよび\fR\-importcertコマンドオプションを参照してください。チェーンの最後にあるのは、Subjectの公開鍵を認証したCAが発行した証明書(応答)です。チェーン内のその前の証明書は、CAの公開鍵を認証する証明書です。
.sp
CAの公開鍵を認証する証明書は、多くの場合、自己署名証明書(つまりCAが自身の公開鍵を認証した証明書)であり、これはチェーンの最初の証明書になります。場合によっては、CAが証明書のチェーンを返すこともあります。この場合、チェーン内の最後の証明書(CAによって署名され、鍵エントリの公開鍵を認証する証明書)に変わりはありませんが、チェーン内のその前の証明書は、CSRの送信先のCAとは別のCAによって署名され、CSRの送信先のCAの公開鍵を認証する証明書になります。チェーン内のその前の証明書は、次のCAの鍵を認証する証明書になります。以下同様に、自己署名された「ルート」証明書に達するまでチェーンが続きます。したがって、チェーン内の(最初の証明書以後の)各証明書では、チェーン内の次の証明書の署名者の公開鍵が認証されていることになります。
.sp
多くのCAは、チェーンをサポートせずに発行済の証明書のみを返します。特に、中間のCAが存在しないフラットな階層構造の場合は、その傾向が顕著です。このような場合は、キーストアにすでに格納されている信頼できる証明書情報から、証明書チェーンを確立する必要があります。
.sp
別の応答形式(PKCS#7で定義されている形式)では、発行済証明書に加え、証明書チェーンのサポートが含まれています。\fBkeytool\fRコマンドでは、どちらの応答形式も扱うことができます。
.sp
トップレベル(ルート)CAの証明書は、自己署名証明書です。ただし、ルートの公開鍵への信頼は、ルート証明書自体からではなく、新聞など他のソースから取得されます。これは、VeriSignルートCAなどの識別名を使用して、誰でも自己署名型証明書を生成できるためです。ルートCAの公開鍵は広く知られています。ルートCAの公開鍵を証明書に格納する理由は、証明書という形式にすることで多くのツールから利用できるようになるからにすぎません。つまり、証明書は、ルートCAの公開鍵を運ぶ「媒体」として利用されるのみです。ルートCAの証明書をキーストアに追加するときは、\fB\-printcert\fRオプションを使用して、その前に証明書の内容を表示し、表示されたフィンガープリントと、新聞やルートCAのWebページなどから入手した既知のフィンガープリントとを比較する必要があります。
.RE
.PP
cacerts証明書ファイル
.RS 4
\fBcacerts\fRという名前の証明書ファイルは、Windowsではセキュリティ・プロパティ・ディレクトリ\fBjava\&.home\elib\esecurity\fR、Oracle Solarisでは\fBjava\&.home/lib/security\fRに置かれています。\fBjava\&.home\fRは、実行環境のディレクトリ(SDKの\fBjre\fRディレクトリまたはJREの最上位ディレクトリ)です。
.sp
\fBcacerts\fRファイルは、CAの証明書を含む、システム全体のキーストアです。システム管理者は、キーストア・タイプに\fBjks\fRを指定することで、\fBkeytool\fRコマンドを使用してこのファイルの構成と管理を行うことができます。\fBcacerts\fRキーストア・ファイルは、ルートCA証明書のデフォルト・セットを含んだ状態で出荷されています。デフォルトの証明書を一覧表示するには、次のコマンドを使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-list \-keystore java\&.home/lib/security/cacerts\fR
 
.fi
.if n \{\
.RE
.\}
\fBcacerts\fRキーストア・ファイルの初期パスワードは、\fBchangeit\fRです。システム管理者は、SDKのインストール後、このファイルのパスワードとデフォルト・アクセス権を変更する必要があります。
.sp
\fB注意:\fR
\fBcacerts\fRファイルを確認することが重要です。\fBcacerts\fRファイル内のCAは、署名および他のエンティティへの証明書発行のためのエンティティとして信頼されるため、\fBcacerts\fRファイルの管理は慎重に行う必要があります。\fBcacerts\fRファイルには、信頼するCAの証明書のみが含まれている必要があります。ユーザーは、自身の責任において、\fBcacerts\fRファイルにバンドルされている信頼できるルートCA証明書を検証し、信頼性に関する独自の決定を行います。
.sp
信頼できないCA証明書を\fBcacerts\fRファイルから削除するには、\fBkeytool\fRコマンドの\fBdelete\fRオプションを使用します。\fBcacerts\fRファイルはJREのインストール・ディレクトリにあります。このファイルを編集するアクセス権がない場合は、システム管理者に連絡してください
.RE
.PP
インターネットRFC 1421証明書符号化規格
.RS 4
多くの場合、証明書は、バイナリ符号化ではなく、インターネットRFC 1421規格で定義されている出力可能符号化方式を使用して格納されます。Base 64符号化とも呼ばれるこの証明書形式では、電子メールやその他の機構を通じて、他のアプリケーションに証明書を容易にエクスポートできます。
.sp
\fB\-importcert\fRと\fB\-printcert\fRコマンドでは、この形式の証明書とバイナリ符号化の証明書を読み込むことができます。\fB\-exportcert\fRコマンドでは、デフォルトでバイナリ符号化の証明書が出力されます。ただし、\fB\-rfc\fRオプションを指定した場合は、出力可能符号化方式の証明書が出力されます。
.sp
\fB\-list\fRコマンドでは、デフォルトで証明書のSHA1フィンガープリントが出力されます。\fB\-v\fRオプションが指定されている場合、証明書は人が理解できる形式で出力されます。\fB\-rfc\fRオプションが指定されている場合、証明書は出力可能符号化方式で出力されます。
.sp
出力可能符号化方式で符号化された証明書は、次のテキストで始まり、次のテキストで終了します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-\-\-\-\-BEGIN CERTIFICATE\-\-\-\-\-\fR
 
\fBencoded certificate goes here\&. \fR
 
\fB\-\-\-\-\-END CERTIFICATE\-\-\-\-\-\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
X\&.500識別名
.RS 4
X\&.500識別名は、エンティティを特定するために使用されます。たとえば、X\&.509証明書の\fBsubject\fRフィールドと\fBissuer\fR(署名者)フィールドで指定される名前は、X\&.500識別名です。\fBkeytool\fRコマンドは、次のサブパートをサポートしています。
.sp
\fBcommonName\fR: Susan Jonesなど、人の通称。
.sp
\fBorganizationUnit\fR: 小さな組織(部、課など)の名称。Purchasingなどです。
.sp
\fBlocalityName\fR: 地域(都市)名。Palo Altoなど。
.sp
\fBstateName\fR: 州名または地方名。Californiaなど。
.sp
\fBcountry\fR: 2文字の国コード。CHなど。
.sp
識別名文字列を\fB\-dname\fRオプションの値として指定する場合(たとえば\fB\-genkeypair\fRコマンドに)、文字列は次の形式にする必要があります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBCN=cName, OU=orgUnit, O=org, L=city, S=state, C=countryCode\fR
 
.fi
.if n \{\
.RE
.\}
イタリックの項目は、実際に指定する値を表します。短縮形のキーワードの意味は、次のとおりです。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBCN=commonName\fR
\fBOU=organizationUnit\fR
\fBO=organizationName\fR
\fBL=localityName\fR
\fBS=stateName\fR
\fBC=country\fR
 
.fi
.if n \{\
.RE
.\}
次に示すのは、識別名文字列の例です。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBCN=Mark Smith, OU=Java, O=Oracle, L=Cupertino, S=California, C=US\fR
 
.fi
.if n \{\
.RE
.\}
この文字列を使用したコマンドの例です。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-genkeypair \-dname "CN=Mark Smith, OU=Java, O=Oracle, L=Cupertino,\fR
\fBS=California, C=US" \-alias mark\fR
 
.fi
.if n \{\
.RE
.\}
キーワードの短縮形では、大文字と小文字は区別されません。たとえば、CN、cnおよびCnは、どれも同じものとして扱われます。
.sp
一方、キーワードの指定順序には意味があり、各サブコンポーネントは上に示した順序で指定する必要があります。ただし、サブコンポーネントをすべて指定する必要はありません。たとえば、次のように一部のサブコンポーネントのみを指定できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBCN=Steve Meier, OU=Java, O=Oracle, C=US\fR
 
.fi
.if n \{\
.RE
.\}
識別名文字列の値にカンマが含まれる場合に、コマンドラインで文字列を指定するときには、次のようにカンマをバックスラッシュ文字(\e)でエスケープする必要があります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBcn=Peter Schuster, ou=Java\e, Product Development, o=Oracle, c=US\fR
 
.fi
.if n \{\
.RE
.\}
識別名文字列をコマンドラインで指定する必要はありません。識別名を必要とするコマンドを実行するときに、コマンドラインで識別名を指定しなかった場合は、各サブコンポーネントの入力を求められます。この場合は、カンマをバックスラッシュ(\e)でエスケープする必要はありません。
.RE
.SH "警告"
.SS "信頼できる証明書のインポート警告"
.PP
\fB重要\fR: 信頼できる証明書として証明書をインポートする前に、証明書の内容を慎重に調べてください。
.PP
Windowsの例:
.PP
まず、\fB\-noprompt\fRオプションを指定せずに\fB\-printcert\fRコマンドまたは\fB\-importcert\fRコマンドを使用して、証明書を表示します。表示された証明書のフィンガープリントが、期待されるフィンガープリントと一致することを確認します。たとえば、証明書が送られてきて、この証明書を\fB\etmp\ecert\fRという名前でファイルに格納しているとします。この場合は、信頼できる証明書のリストにこの証明書を追加する前に、\fB\-printcert\fRコマンドを実行してフィンガープリントを表示できます。たとえば、次のようにします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB  keytool \-printcert \-file \etmp\ecert\fR
\fB    Owner: CN=ll, OU=ll, O=ll, L=ll, S=ll, C=ll\fR
\fB    Issuer: CN=ll, OU=ll, O=ll, L=ll, S=ll, C=ll\fR
\fB    Serial Number: 59092b34\fR
\fB    Valid from: Thu Sep 25 18:01:13 PDT 1997 until: Wed Dec 24 17:01:13 PST 1997\fR
\fB    Certificate Fingerprints:\fR
\fB         MD5:  11:81:AD:92:C8:E5:0E:A2:01:2E:D4:7A:D7:5F:07:6F\fR
\fB         SHA1: 20:B6:17:FA:EF:E5:55:8A:D0:71:1F:E8:D6:9D:C0:37:13:0E:5E:FE\fR
\fB         SHA256: 90:7B:70:0A:EA:DC:16:79:92:99:41:FF:8A:FE:EB:90:\fR
\fB                 17:75:E0:90:B2:24:4D:3A:2A:16:A6:E4:11:0F:67:A4\fR
.fi
.if n \{\
.RE
.\}
.PP
Oracle Solarisの例:
.PP
まず、\fB\-noprompt\fRオプションを指定せずに\fB\-printcert\fRコマンドまたは\fB\-importcert\fRコマンドを使用して、証明書を表示します。表示された証明書のフィンガープリントが、期待されるフィンガープリントと一致することを確認します。たとえば、あるユーザーから証明書が送られてきて、この証明書を\fB/tmp/cert\fRという名前でファイルに格納しているとします。この場合は、信頼できる証明書のリストにこの証明書を追加する前に、\fB\-printcert\fRコマンドを実行してフィンガープリントを表示できます。たとえば、次のようにします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB  keytool \-printcert \-file /tmp/cert\fR
\fB    Owner: CN=ll, OU=ll, O=ll, L=ll, S=ll, C=ll\fR
\fB    Issuer: CN=ll, OU=ll, O=ll, L=ll, S=ll, C=ll\fR
\fB    Serial Number: 59092b34\fR
\fB    Valid from: Thu Sep 25 18:01:13 PDT 1997 until: Wed Dec 24 17:01:13 PST 1997\fR
\fB    Certificate Fingerprints:\fR
\fB         MD5:  11:81:AD:92:C8:E5:0E:A2:01:2E:D4:7A:D7:5F:07:6F\fR
\fB         SHA1: 20:B6:17:FA:EF:E5:55:8A:D0:71:1F:E8:D6:9D:C0:37:13:0E:5E:FE\fR
\fB         SHA256: 90:7B:70:0A:EA:DC:16:79:92:99:41:FF:8A:FE:EB:90:\fR
\fB                 17:75:E0:90:B2:24:4D:3A:2A:16:A6:E4:11:0F:67:A4\fR
 
.fi
.if n \{\
.RE
.\}
.PP
次に、証明書を送信した人物に連絡し、この人物が提示したフィンガープリントと、上のコマンドで表示されたフィンガープリントとを比較します。フィンガープリントが一致すれば、送信途中で他の何者か(攻撃者など)による証明書のすり替えが行われていないことを確認できます。送信途中でこの種の攻撃が行われていた場合、チェックを行わずに証明書をインポートすると、攻撃者によって署名されたすべてのもの(攻撃的意図を持つクラス・ファイルを含んだJARファイルなど)を信頼することになります。
.PP
\fB注意:\fR
証明書をインポートする前に\fB\-printcert\fRコマンドを実行する必要はありません。キーストア内の信頼できる証明書のリストに証明書を追加する前に、\fB\-importcert\fRコマンドによって証明書の情報が表示され、確認を求めるメッセージが表示されるためです。ユーザーはインポート操作を停止できます。ただし、これを実行できるのは、\fB\-noprompt\fRオプションを指定せずに\fB\-importcert\fRコマンドを呼び出す場合のみです。\fB\-noprompt\fRオプションが指定されている場合、ユーザーとの対話は行われません。
.SS "パスワード警告"
.PP
キーストアに対する操作を行うほとんどのコマンドでは、ストアのパスワードが必要です。また、一部のコマンドでは、非公開/秘密鍵のパスワードが必要になることがあります。パスワードはコマンド行で指定できます(\fB\-storepass\fRオプションと\fB\-keypass\fRオプションを使用)。ただし、テスト目的の場合、またはセキュアなシステムを使用している場合以外は、コマンド行やスクリプトでパスワードを指定しないでください。必要なパスワードのオプションをコマンド行で指定しなかった場合は、パスワードの入力を求められます。
.SS "証明書の準拠に関する警告"
.PP
インターネット標準RFC 5280では、X\&.509証明書の準拠に関するプロファイルが定義されており、証明書のフィールドおよびエクステンションに有効な値および値の組合せが記載されています。標準については、
http://tools\&.ietf\&.org/rfc/rfc5280\&.txtを参照してください
.PP
\fBkeytool\fRコマンドでは、これらのルールすべてが適用されるわけではないため、標準に準拠しない証明書を生成できます。標準に準拠しない証明書は、JREや他のアプリケーションで拒否されることがあります。ユーザーは、\fB\-dname\fRや\fB\-ext\fRなどで適正なオプションを指定するようにしてください。
.SH "注意"
.SS "新しい信頼できる証明書のインポート"
.PP
\fBkeytool\fRコマンドは、キーストアに証明書を追加する前に、キーストア内にすでに存在する信頼できる証明書を使用して、インポートする証明書から(ルートCAの)自己署名証明書に至るまでの信頼のチェーンの構築を試みます。
.PP
\fB\-trustcacerts\fRオプションを指定した場合、追加の証明書は信頼できるすなわち\fBcacerts\fRという名前のファイルに含まれる証明書のチェーンとみなされます。
.PP
\fBkeytool\fRコマンドが、インポートする証明書から自己署名証明書(キーストアまたは\fBcacerts\fRファイルに含まれている自己署名証明書)に至るまでの信頼のパスの構築に失敗した場合は、インポートする証明書の情報を表示し、ユーザーに確認を求めます。この場合は、表示された証明書のフィンガープリントと、他のなんらかの(信頼できる)情報源(証明書の所有者など)から入手したフィンガープリントとを比較します。信頼できる証明書として証明書をインポートするときは、証明書が有効であることを慎重に確認する必要があります。信頼できる証明書のインポート警告を参照してください。インポート操作は、証明書を確認する時点で中止できます。\fB\-noprompt\fRオプションが指定されている場合、ユーザーとの対話は行われません。
.SS "証明書応答のインポート"
.PP
証明書応答をインポートするときは、キーストア内の信頼できる証明書、および(\fB\-trustcacert\fR\fBs\fRオプションが指定されている場合は)\fBcacerts\fRキーストア・ファイルで構成された証明書を使用して証明書応答が検査されます。cacerts証明書ファイルを参照してください。
.PP
証明書応答が信頼できるかどうかを決定する方法は次のとおりです。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
証明書応答が単一のX\&.509証明書である場合、\fBkeytool\fRコマンドは、証明書応答から(ルートCAの)自己署名証明書に至るまでの信頼チェーンの確立を試みます。証明書応答と、証明書応答の認証に使用される証明書の階層構造は、aliasの新しい証明書チェーンを形成します。信頼チェーンが確立されない場合、証明書応答はインポートされません。この場合、\fBkeytool\fRコマンドは証明書を出力せず、ユーザーに検証を求めるプロンプトを表示します。ユーザーが証明書応答の信頼性を判断するのは非常に難しいためです。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
証明書応答がPKCS#7形式の証明書チェーンまたは一連のX\&.509証明書である場合、チェーンは、ユーザーの証明書が最初に、0以上のCA証明書がその次にくるように並べられます。チェーンが自己署名のルートCA証明書で終わり、\fB \-trustcacerts\fRオプションが指定されている場合、\fBkeytool\fRコマンドは、その証明書と、キーストア内または\fBcacerts\fRキーストア・ファイル内の信頼できるすべての証明書を照合しようとします。チェーンが自己署名のルートCA証明書で終わっておらず、\fB\-trustcacerts\fRオプションが指定されている場合、\fBkeytool\fRコマンドは、キーストア内または\fBcacerts\fRキーストア・ファイル内の信頼できる証明書から自己署名のルートCA証明書を見つけてそれをチェーンの末尾に追加しようとします。その証明書が見つからず、\fB\-noprompt\fRオプションが指定されていない場合は、チェーン内の最後の証明書の情報が出力され、ユーザーは確認を求められます。
.RE
.PP
証明書応答内の公開鍵が\fBalias\fRですでに格納されているユーザーの公開鍵に一致した場合、古い証明書チェーンが応答内の新しい証明書チェーンで置き換えられます。以前の証明書チェーンを有効な\fBkeypass\fRで置き換えることができるのは、エントリの秘密鍵を保護するためのパスワードを指定した場合のみです。パスワードを指定しておらず、秘密鍵のパスワードがキーストアのパスワードと異なる場合は、秘密鍵のパスワードの入力を求められます。
.PP
このコマンドは、以前のリリースでは\fB\-import\fRという名前でした。このリリースでは、引き続き古い名前がサポートされています。今後は、新しい名前\fB\-importcert\fRが優先されます。
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jar(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jarsigner(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
http://docs\&.oracle\&.com/javase/tutorial/security/index\&.htmlにある
「コース: Java SEのセキュリティ機能」
.RE
.br
'pl 8.5i
'bp
