'\" t
.\" Copyright (c) 1997, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: native2ascii
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: 国際化ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "native2ascii" "1" "2013年11月21日" "JDK 8" "国際化ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
native2ascii \- サポートされている任意の文字エンコーディングの文字を含むファイルを、ASCIIおよびUnicodeでエスケープされたファイルに変換して(またはその逆)、ローカライズ可能なアプリケーションを作成します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBnative2ascii\fR [ \fIinputfile\fR ] [ \fIoutputfile\fR ]
.fi
.if n \{\
.RE
.\}
.PP
\fIinputfile\fR
.RS 4
ASCIIに変換するエンコードされたファイル。
.RE
.PP
\fIoutputfile\fR
.RS 4
変換されたASCIIファイル。
.RE
.SH "説明"
.PP
\fBnative2ascii\fRコマンドは、ASCII文字セットに含まれないすべての文字にUnicodeエスケープ(\fB\eu\fR\fIxxxx\fR)表記法を使用して、Java Runtime Environment (JRE)でサポートされているエンコードされたファイルを、ASCIIでエンコードされたファイルに変換します。このプロセスは、ISO\-8859\-1文字セットに含まれない文字が含まれているプロパティ・ファイルで必要です。このツールは、その逆の変換を実行することもできます。
.PP
\fBoutputfile\fR値を省略した場合、標準出力に出力されます。さらに、\fBinputfile\fR値を省略した場合、標準入力から入力されます。
.SH "オプション"
.PP
\-reverse
.RS 4
逆の処理を行います。つまり、ISO\-8859\-1でUnicodeエスケープを使用してエンコードされたファイルを、JREでサポートされる文字エンコーディングのファイルに変換します。
.RE
.PP
\-encoding \fIencoding_name\fR
.RS 4
変換処理で使用する文字エンコーディングの名前を指定します。このオプションが存在しない場合は、デフォルトの文字エンコーディング(\fBjava\&.nio\&.charset\&.Charset\&.defaultCharset\fRメソッドで定義された)が使用されます。\fBencoding_name\fR文字列は、JREでサポートされている文字エンコーディングの名前にする必要があります。http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/intl/encoding\&.doc\&.htmlにある
「サポートされているエンコーディング」を参照してください
.RE
.PP
\-J\fIoption\fR
.RS 4
Java Virtual Machine (JVM)に\fBoption\fRを渡します。optionには、Javaアプリケーション起動ツールのリファレンス・ページに記載されているオプションを1つ指定します。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。java(1)を参照してください。
.RE
.br
'pl 8.5i
'bp
