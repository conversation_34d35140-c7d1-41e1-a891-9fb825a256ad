'\" t
.\" Copyright (c) 2001, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: orbd
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Java IDLおよびRMI-IIOPツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "orbd" "1" "2013年11月21日" "JDK 8" "Java IDLおよびRMI-IIOPツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
orbd \- CORBA環境のサーバーにある永続オブジェクトをクライアントから検索して呼び出せるようにします。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBorbd\fR [ \fIoptions\fR ]
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.SH "説明"
.PP
\fBorbd\fRコマンドを使用すると、CORBA環境のサーバーにある永続オブジェクトをクライアントから透過的に検索して呼び出すことができます。orbdツールに含まれるサーバー・マネージャを使用すると、クライアントはCORBA環境でサーバー上にある永続オブジェクトを透過的に検索して呼び出すことができます。永続サーバーは、ネーミング・サービスに永続オブジェクト参照を発行する際、サーバーのポート番号のかわりにORBDのポート番号をオブジェクト参照に含めます。永続オブジェクト参照のオブジェクト参照にORBDポート番号を含めることには、次のような利点があります。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
ネーミング・サービスにあるオブジェクト参照が、サーバーのライフ・サイクルと無関係になります。たとえば、オブジェクト参照は、初めてインストールされたときはネーミング・サービスのサーバーによってネーミング・サービスに発行されますが、その後は、サーバーの開始またはシャットダウンの回数にかかわらず、呼び出したクライアントにORBDが正しいオブジェクト参照を返します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
クライアントは一度のみネーミング・サービスのオブジェクト参照をルックアップする必要がありますが、その後はサーバーのライフ・サイクルによる変更とは無関係にこの参照を利用することができます。
.RE
.PP
ORBDのサーバー・マネージャにアクセスするには、\fBservertool\fRを使用してサーバーを起動する必要があります。servertoolは、アプリケーション・プログラマが、永続サーバーの登録、登録解除、起動および停止を行うためのコマンド行インタフェースです。サーバー・マネージャの詳細は、サーバー・マネージャを参照してください。
.PP
\fBorbd\fRを起動すると、ネーミング・サービスも起動されます。ネーミング・サービスの詳細。ネーミング・サービスの起動と停止を参照してください。
.SH "オプション"
.PP
\-ORBInitialPort \fInameserverport\fR
.RS 4
必須。ネーム・サーバーを起動するポートの番号を指定します。\fBorbd\fRは、起動されると、このポート上で着信リクエストをリスニングします。Oracle Solarisソフトウェアでは、1024より小さいポートでプロセスを開始する場合、rootユーザーになる必要があります。このため、1024以上のポート番号を使用することをお薦めします。
.RE
.SS "必須でないオプション"
.PP
\-port \fIport\fR
.RS 4
ORBDを起動するポートを指定します。このポートで、永続オブジェクトに対するリクエストをORBDが受け取ります。このポートのデフォルト値は1049です。このポート番号は、永続Interoperable Object References (IOR)のポート・フィールドに追加されます。
.RE
.PP
\-defaultdb \fIdirectory\fR
.RS 4
ORBD永続格納ディレクトリ\fBorb\&.db\fRが作成されるベース・ディレクトリを指定します。このオプションが指定されていない場合、デフォルト値は\fB\&./orb\&.db\fRになります。
.RE
.PP
\-serverPollingTime \fImilliseconds\fR
.RS 4
\fBservertool\fRを使用して登録された永続サーバーが正常に動作していることをORBDが確認する間隔を指定します。デフォルト値は1000ミリ秒です。\fBmilliseconds\fRに指定する値は、有効な正の整数にする必要があります。
.RE
.PP
\-serverStartupDelay milliseconds
.RS 4
\fBservertool\fRを使用して登録された永続サーバーを再起動してから、位置転送の例外を送信するまでのORBDの待機時間を指定します。デフォルト値は1000ミリ秒です。\fBmilliseconds\fRに指定する値は、有効な正の整数にする必要があります。
.RE
.PP
\-J\fIoption\fR
.RS 4
Java Virtual Machineに\fBoption\fRを渡します。\fBoption\fRには、Javaアプリケーション起動ツールのリファレンス・ページに記載されているオプションを1つ指定します。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。java(1)を参照してください。
.RE
.SS "ネーミング・サービスの起動と停止"
.PP
ネーミング・サービスは、CORBAオブジェクトにネーミングを可能にするCORBAサービスです。ネーミングは名前をオブジェクト参照にバインドすることにより可能になります。ネーム・バインディングをネーミング・サービスに格納すれば、クライアントが名前を指定して目的のオブジェクト参照を取得できるようになります。
.PP
クライアントまたはサーバーを実行する前に、ORBDを起動します。ORBDには、永続ネーミング・サービスおよび一時ネーミング・サービスが組み込まれています。これらはどちらもCOSネーミング・サービスの実装です。
.PP
永続ネーミング・サービスは、ネーミング・コンテキストに対して永続性を提供します。つまり、この情報は、サービスの停止や起動後にも維持され、サービスに障害が発生した場合でも回復できます。ORBDを再起動すると、永続ネーミング・サービスはネーミング・コンテキストのグラフを復元し、すべてのクライアントとサーバーの名前のバインディングがそのまま(永続的に)保持されるようにします。
.PP
後方互換性のため、旧リリースのJDKに同梱されていた一時ネーミング・サービス\fBtnameserv\fRが、今回のリリースのJava SEにも同梱されています。一時ネーム・サービスでは、ネーム・サービスの実行中にのみネーミング・コンテキストが保持されます。サービスが中断されると、ネーミング・コンテキスト・グラフは失われます。
.PP
\fB\-ORBInitialPort\fR引数は、\fBorbd\fRの必須のコマンド行引数で、ネーミング・サービスが実行されるポートの番号を設定するために使用されます。次の手順では、Java IDL Object Request Broker Daemon用にポート1050を使用できることを前提としています。Oracle Solarisソフトウェアを使用する場合、1024より小さいポートでプロセスを開始するには、rootユーザーになる必要があります。このため、1024以上のポート番号を使用することをお薦めします。必要であれば別のポートに変更してください。
.PP
Solaris、LinuxまたはOS Xコマンド・シェルから\fBorbd\fRを開始するには、次のように入力します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBorbd \-ORBInitialPort 1050&\fR
 
.fi
.if n \{\
.RE
.\}
.PP
WindowsのMS\-DOSシステム・プロンプトでは、次のように入力します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBstart orbd \-ORBInitialPort 1050\fR
 
.fi
.if n \{\
.RE
.\}
.PP
これでORBDが実行され、サーバーとクライアントのアプリケーションを実行できるようになります。クライアントとサーバーのアプリケーションは、実行時に、ネーミング・サービスが実行されているポートの番号(必要な場合はさらにマシン名)を認識している必要があります。これを実現する1つの方法は、次のコードをアプリケーションに追加することです。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBProperties props = new Properties();\fR
\fBprops\&.put("org\&.omg\&.CORBA\&.ORBInitialPort", "1050");\fR
\fBprops\&.put("org\&.omg\&.CORBA\&.ORBInitialHost", "MyHost");\fR
\fBORB orb = ORB\&.init(args, props);\fR
 
.fi
.if n \{\
.RE
.\}
.PP
この例では、ネーミング・サービスは、ホスト\fBMyHost\fRのポート1050上で実行されます。別の方法として、コマンド行からサーバーまたはクライアントのアプリケーションを実行するときに、ポート番号またはマシン名あるいはその両方を指定する方法もあります。たとえば、次のコマンド行オプションを使用して、\fBHelloApplication\fRを起動できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava HelloApplication \-ORBInitialPort 1050 \-ORBInitialHost MyHost\fR
 
.fi
.if n \{\
.RE
.\}
.PP
ネーミング・サービスを停止するには、適切なオペレーティング・システム・コマンドを使用します。たとえば、Oracle Solaris上で\fBpkill\fR
\fBorbd\fRを実行したり、\fBorbd\fRが動作中のDOSウィンドウで\fB[Ctrl]+[C]\fRキーを押します。一時ネーミング・サービスの場合は、サービスが終了されると、ネーミング・サービスに登録された名前が消去される場合があります。Java IDLネーム・サービスは、明示的に停止されるまで実行されます。
.PP
ORBDに付属するネーミング・サービスの詳細は、
http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/jidlNaming\&.htmlの「Naming Service」を参照してください
.SH "サーバー・マネージャ"
.PP
ORBDのサーバー・マネージャにアクセスして、永続サーバーを実行するには、\fBservertool\fRを使用してサーバーを起動する必要があります。servertoolは、アプリケーション・プログラマが、永続サーバーの登録、登録解除、起動および停止を行うためのコマンド行インタフェースです。\fBservertool\fRを使用してサーバーを起動する場合は、\fBorbd\fRが実行されている場所と同じポートとホストで起動する必要があります。サーバーを異なるポートで実行すると、ローカル・コンテキスト用にデータベースに保存されている情報が無効になり、サービスが正しく動作しません。
.PP
http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/jidlExample\&.htmlの
「Java IDL: The "Hello World" Example」を参照してください
.PP
この例では、チュートリアルの手順に従って\fBidlj\fRコンパイラと\fBjavac\fRコンパイラを実行します。ORBDのサーバー・マネージャを実行するには、次の手順に従ってアプリケーションを実行します。
.PP
\fBorbd\fRを起動します。
.PP
Solaris、LinuxまたはOS Xコマンド・シェルで、次のように入力します:
\fBorbd \-ORBInitialPort 1050\fR
.PP
MS\-DOSシステム・プロンプト(Windows)で次のように入力します:
\fBs\fR\fBtart orbd \-ORBInitialPort 105\fR\fB0\fR
.PP
ポート1050はネーム・サーバーを実行するポートです。\fB\-ORBInitialPort\fRオプションは必須コマンド行引数です。Oracle Solarisソフトウェアを使用する場合、1024より小さいポートでプロセスを開始するには、rootユーザーになる必要があります。このため、1024以上のポート番号を使用することをお薦めします。
.PP
\fBservertool\fRを起動します:
\fBservertool \-ORBInitialPort 1050\fR。
.PP
前回の手順とネーム・サーバー(\fBorbd\fR)のポートが同じであることを確認します。たとえば\fB\-ORBInitialPort 1050\&.\fRのようになります。\fBservertool\fRは、ネーム・サーバーと同じポート上で起動する必要があります。
.PP
\fBservertool\fRコマンドライン・インタフェースで、\fBservertool\fRプロンプトから\fBHello\fRサーバーを起動します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBservertool  > register \-server HelloServer \-classpath \&. \-applicationName\fR
\fB                HelloServerApName\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBservertool\fRによってサーバーが登録されて、\fBHelloServerApName\fRという名前がサーバーに割り当てられ、登録されているすべてのサーバー一覧とともにサーバーIDが表示されます。他の端末ウィンドウまたはプロンプトからクライアント・アプリケーションを実行します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava HelloClient \-ORBInitialPort 1050 \-ORBInitialHost localhost\fR
 
.fi
.if n \{\
.RE
.\}
.PP
この例の\fB\-ORBInitialHost localhost\fRは省略することができます。ネーム・サーバーが\fBHello\fRクライアントとして同一ホスト上で動作しているからです。ネーム・サーバーが別のホストで実行されている場合は、\-\fBORBInitialHost nameserverhost\fRオプションを使用してIDLネーム・サーバーが実行されているホストを指定します。前の手順で行われたとおりにネーム・サーバー(\fBorbd\fR)ポートを指定します(例:
\fB\-ORBInitialPort 1050\fR)。ORBDのサーバー・マネージャの操作が終了したら、ネーム・サーバー(\fBorbd\fR)と\fBservertool\fRを停止するか終了してください。MS\-DOSプロンプトで\fBorbd\fRをシャットダウンするには、サーバーを実行しているウィンドウを選択して\fB[Ctrl]+[C]\fRキーを押します。
.PP
Oracle Solarisから\fBorbd\fRをシャットダウンするには、プロセスを検索して、\fBkill\fRコマンドで終了します。サーバーを明示的に停止するまでは、呼出し待機状態が続きます。\fBservertool\fRをシャットダウンするには、\fBquit\fRと入力してキーボードの\fB[Enter]\fRキーを押します。
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
servertool(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/jidlNaming\&.htmlの
「Naming Service」
.RE
.br
'pl 8.5i
'bp
