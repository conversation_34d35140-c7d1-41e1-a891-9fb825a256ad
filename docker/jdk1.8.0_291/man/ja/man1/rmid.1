'\" t
.\" Copyright (c) 1998, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: rmid
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Remote Method Invocation (RMI)ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "rmid" "1" "2013年11月21日" "JDK 8" "Remote Method Invocation (RMI)"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
rmid \- 起動システム・デーモンを開始すると、オブジェクトをJava Virtual Machine(VM)に登録してアクティブ化できるようになります。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBrmid\fR [\fIoptions\fR]
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.SH "説明"
.PP
\fBrmid\fRコマンドは、起動システム・デーモンを開始します。起動システム・デーモンを開始してからでないと、アクティブ化可能オブジェクトをアクティブ化システムに登録したり、JVM内でアクティブ化したりすることができません。アクティブ化可能なオブジェクトを使用するプログラムの作成方法の詳細は、\fIアクティブ化の使用\fRに関するチュートリアル(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/rmi/activation/overview\&.html)を参照してください
.PP
\fBrmid\fRコマンドを実行し、次のようにセキュリティ・ポリシー・ファイルを指定して、デーモンを起動します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBrmid \-J\-Djava\&.security\&.policy=rmid\&.policy\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBrmid\fRコマンドのOracleの実装を実行する場合、デフォルトでは、セキュリティ・ポリシー・ファイルを指定する必要があります。それは、\fBrmid\fRコマンドが起動グループ用にJVMを起動するために各\fBActivationGroupDesc\fR内の情報を使用できるかどうかを検証できるようにするためです特に、\fBActivationGroupDesc\fRコンストラクタに渡される\fBCommandEnvironment\fRや任意のプロパティによって指定されるコマンドおよびオプションは、\fBrmid\fRコマンドのセキュリティ・ポリシー・ファイルの中で明示的に許可することが必要になりました。\fBsun\&.rmi\&.activation\&.execPolicy\fRプロパティの値は、起動グループ用にJVMを起動するために\fBActivationGroupDesc\fR内の情報を使用できるかどうかを判断するときに\fBrmid\fRコマンドが使用するポリシーを決定します。詳細は、\-J\-Dsun\&.rmi\&.activation\&.execPolicy=policyオプションの説明を参照してください。
.PP
\fBrmid\fRコマンドを実行すると、デフォルト・ポート1098でアクティベータと内部レジストリが起動され、\fBActivationSystem\fRがこの内部レジストリ内の名前\fBjava\&.rmi\&.activation\&.ActivationSystem\fRにバインドされます。
.PP
レジストリに他のポートを指定するには、\fBrmid\fRコマンドの実行時に\fB\-port\fRオプションを指定する必要があります。たとえば、次のコマンドは、レジストリのデフォルト・ポート1099で、起動システム・デーモンとレジストリを起動します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBrmid \-J\-Djava\&.security\&.policy=rmid\&.policy \-port 1099\fR
 
.fi
.if n \{\
.RE
.\}
.SH "必要に応じてRMIDを開始"
.PP
\fBrmid\fRをコマンドラインから開始するには、\fBinetd\fR(Oracle Solarisの場合)、または\fBxinetd\fR(Linuxの場合)を構成して\fBrmid\fRを必要に応じて開始する方法もあります。
.PP
RMIDを開始すると、\fBSystem\&.inheritedChannel\fRメソッドを呼び出して、継承されたチャンネル(\fBinetd\fR/\fBxinetd\fRから継承)を取得しようとします。継承されたチャンネルがnullであるか、\fBjava\&.nio\&.channels\&.ServerSocketChannel\fRのインスタンスでなかった場合、RMIDはそのチャンネルは\fBinetd\fR/\fBxinetd\fRによって起動されたものではないと判断し、前述のように起動します。
.PP
継承されたチャンネルが\fBServerSocketChannel\fRインスタンスである場合は、RMIDはエクスポートするリモート・オブジェクト、つまり\fBjava\&.rmi\&.activation\&.ActivationSystem\fRがバインドされているレジストリと\fBjava\&.rmi\&.activation\&.Activator\fRリモート・オブジェクトに対するリクエストを受信するサーバー・ソケットとして、\fBServerSocketChannel\fRから取得した\fBjava\&.net\&.ServerSocket\fRを使用します。このモードでは、RMIDの動作は、次のことを除いて、コマンドラインから起動した場合と同じです。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBSystem\&.err\fRに対する出力は、ファイルにリダイレクトされる。このファイルは\fBjava\&.io\&.tmpdir\fRシステム・プロパティで指定されるディレクトリ(通常は\fB/var/tmp\fRまたは\fB/tmp\fR)にある。ファイル名の接頭辞は\fBrmid\-err\fRで、接尾辞は\fBtmp\fRである。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-port\fRオプションは使用できません。このオプションが指定されている場合、RMIDはエラー・メッセージが表示されて終了します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-log\fRオプションは必須。このオプションが指定されていない場合、RMIDはエラー・メッセージが表示されて終了します。
.RE
.PP
必要に応じてサービスを開始するように構成する方法の詳細は、\fBinetd\fR
(Oracle Solarisの場合)、または\fBxinetd\fR
(Linux)のマニュアル・ページを参照してください。
.SH "オプション"
.PP
\-C\fIオプション\fR
.RS 4
\fBrmid\fRコマンドの子プロセス(起動グループ)が作成されたときに、それぞれの子プロセスにコマンド行引数として渡されるオプションを指定します。たとえば、次のように指定すると、起動システム・デーモンによって生成される各仮想マシンにプロパティを渡すことができます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBrmid \-C\-Dsome\&.property=value\fR
 
.fi
.if n \{\
.RE
.\}
コマンド行引数を子プロセスに渡す機能は、デバッグを行う場合に便利です。たとえば、次のコマンドでは、すべての子JVMでserver\-callロギングが可能です。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBrmid \-C\-Djava\&.rmi\&.server\&.logCalls=true\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-J\fIoption\fR
.RS 4
RMIDを実行しているJavaインタプリタに渡すオプションを指定します。たとえば、\fBrmid\fRコマンドが\fBrmid\&.policy\fRという名前のポリシー・ファイルを使用するように指定するには、\fBrmid\fRのコマンド行で\fB\-J\fRオプションを使用して、\fBjava\&.security\&.policy\fRプロパティを定義します。次に例を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBrmid \-J\-Djava\&.security\&.policy\-rmid\&.policy\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-J\-Dsun\&.rmi\&.activation\&.execPolicy=\fIpolicy\fR
.RS 4
起動グループが実行されることになるJVMの起動に使用するコマンドおよびコマンド行オプションをチェックするために、RMIDが採用するポリシーを指定します。このオプションは、Java RMI起動デーモンのOracleの実装のみに存在することに注意してください。コマンド行にこのプロパティを指定しない場合、結果は\fB\-J\-Dsun\&.rmi\&.activation\&.execPolicy=default\fRを指定した場合と同じになります。\fBpolicy\fRに指定可能な値は、\fBdefault\fR、\fBpolicyClassName\fRまたは\fBnone\fRです。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
デフォルト
.sp
\fBdefault\fRまたは未指定値の\fBexecPolicy\fRの場合、\fBrmid\fRコマンドが実行できるのは、\fBrmid\fRコマンドが使用するセキュリティ・ポリシー・ファイルの中で、実行する権限が\fBrmid\fRに与えられているコマンドおよびコマンド行オプションのみです。デフォルトの実行ポリシーで使用できるのは、デフォルトの起動グループ実装のみです。
.sp
\fBrmid\fRコマンドは、起動グループ用のJVMを起動するときに、そのグループについて登録された起動グループ記述子である\fBActivationGroupDesc\fR内の情報を使用します。グループ記述子は、\fBActivationGroupDesc\&.CommandEnvironment\fRを指定します(省略可能)。これには、起動グループを開始するコマンドと、そのコマンド行に追加できるコマンド行オプションが含まれています。デフォルトでは、\fBrmid\fRコマンドは\fBjava\&.home\fRにある\fBjava\fRコマンドを使用します。グループ記述子には、コマンド行にオプションとして追加されるプロパティ・オーバーライドも含まれます(\fB\-D<property>=<value>\fRとして定義されます)。\fBcom\&.sun\&.rmi\&.rmid\&.ExecPermission\fR権限は\fBrmid\fRコマンドに、起動グループを開始するためにグループ記述子の\fBCommandEnvironment\fRで指定されたコマンドを実行する権限を付与します。\fBcom\&.sun\&.rmi\&.rmid\&.ExecOptionPermission\fR権限は起動グループの開始時に、グループ記述子でプロパティ・オーバーライドとして、または\fBCommandEnvironment\fRでオプションとして指定されたコマンド行オプションを、\fBrmid\fRコマンドが使用することを許可します。\fBrmid\fRコマンドに様々なコマンドおよびオプションを実行する権限を付与する場合、権限\fBExecPermission\fRおよび\fBExecOptionPermission\fRをすべてのコード・ソースに付与する必要があります。
.sp
\fBExecPermission\fR
.sp
\fBExecPermission\fRクラスは、起動グループを開始するために\fBrmid\fRコマンドが特定のコマンドを実行する権限を表します。
.sp
\fB構文\fR:
\fBExecPermission\fRの名前は、\fBrmid\fRコマンドに実行を許可するコマンドのパス名です。スラッシュ(/)およびアスタリスク(*)で終わるパス名は、そのディレクトリに含まれるすべてのファイルを示します。スラッシュはファイル区切り文字\fBFile\&.separatorChar\fRです。スラッシュ(/)およびマイナス符号(\-)で終わるパス名は、そのディレクトリに含まれるすべてのファイルとサブディレクトリ(再帰的に)を示します。特殊なトークン\fB<<ALL FILES>>\fRで構成されるパス名は、どのファイルとも一致します。
.sp
パス名にアスタリスク(*)を指定した場合は、現在のディレクトリ内のすべてのファイルを示します。パス名にマイナス符号(\-)を指定した場合は、現在のディレクトリ内のすべてのファイルおよび(再帰的に)現在のディレクトリに含まれるすべてのファイルとサブディレクトリを示します。
.sp
\fBExecOptionPermission\fR
.sp
\fBExecOptionPermission\fRクラスは、起動グループを開始するときに\fBrmid\fRコマンドで特定のコマンド行オプションを使用できる権限を表します。\fBExecOptionPermission\fRの名前は、コマンド行オプションの値です。
.sp
\fB構文\fR: オプションでは、ワイルドカードが限定的にサポートされます。アスタリスクは、ワイルドカード・マッチを表します。アスタリスク(*)は、オプション名そのものとして使用できます。つまり、任意のオプションを表すことができます。また、オプション名の末尾に使用することもできます。ただし、ドット(\&.)か等号(=)の直後にアスタリスク(*)を指定する必要があります。
.sp
例:
\fB*\fRや\fB\-Dmydir\&.*\fRや\fB\-Da\&.b\&.c=*\fRは有効ですが、\fB*mydir\fRや\fB\-Da*b\fRや\fBab*\fRは無効です。
.sp
\fBrmidのポリシー・ファイル\fR
.sp
\fBrmid\fRコマンドに様々なコマンドおよびオプションを実行する権限を許可する場合は、権限\fBExecPermission\fRおよび\fBExecOptionPermission\fRをすべてのコード・ソースに付与する必要があります(汎用的に)。これらの権限をチェックするのは\fBrmid\fRコマンドのみなので、これらの権限を汎用的に付与しても安全です。
.sp
\fBrmid\fRコマンドに各種の実行権限を付与するポリシー・ファイルの例を、次に示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBgrant {\fR
\fB    permission com\&.sun\&.rmi\&.rmid\&.ExecPermission\fR
\fB        "/files/apps/java/jdk1\&.7\&.0/solaris/bin/java";\fR
\fB \fR
\fB    permission com\&.sun\&.rmi\&.rmid\&.ExecPermission\fR
\fB        "/files/apps/rmidcmds/*";\fR
\fB \fR
\fB    permission com\&.sun\&.rmi\&.rmid\&.ExecOptionPermission\fR
\fB        "\-Djava\&.security\&.policy=/files/policies/group\&.policy";\fR
\fB \fR
\fB    permission com\&.sun\&.rmi\&.rmid\&.ExecOptionPermission\fR
\fB        "\-Djava\&.security\&.debug=*";\fR
\fB \fR
\fB    permission com\&.sun\&.rmi\&.rmid\&.ExecOptionPermission\fR
\fB        "\-Dsun\&.rmi\&.*";\fR
\fB};\fR
 
.fi
.if n \{\
.RE
.\}
最初に付与されている権限は、\fBrmid\fRコマンドに対し、パス名により明示的に指定される\fBjava\fRコマンドの1\&.7\&.0リリースの実行を許可します。デフォルトでは、\fBjava\&.home\fRにあるバージョンの\fBjava\fRコマンドを使用します。\fBrmid\fRコマンドが使用するのと同じバージョンが使用されるため、そのコマンドは、ポリシー・ファイルで指定する必要はありません。2番目の権限は、\fBrmid\fRコマンドに対して、ディレクトリ\fB/files/apps/rmidcmds\fR内の任意のコマンドの実行権限を許可します。
.sp
3番目に付与されている権限\fBExecOptionPermission\fRは、\fBrmid\fRコマンドに対して、セキュリティ・ポリシー・ファイルを\fB/files/policies/group\&.policy\fRとして定義している起動グループの開始を許可します。次の権限は、起動グループが\fBjava\&.security\&.debug property\fRを使用することを許可しています。最後の権限は、起動グループが\fBsun\&.rmi property\fR名の階層内の任意のプロパティを使用することを許可しています。
.sp
ポリシー・ファイルを指定して\fBrmid\fRコマンドを起動するには、\fBrmid\fRのコマンド行で\fBjava\&.security\&.policy\fRプロパティを指定する必要があります。次に例を示します。
.sp
\fBrmid \-J\-Djava\&.security\&.policy=rmid\&.policy\fR\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
<policyClassName>
.sp
デフォルトの動作では十分な柔軟性が得られない場合、管理者は、\fBrmid\fRの起動時に、\fBcheckExecCommand\fRメソッドが所属するクラスの名前を指定して、\fBrmid\fRコマンドが実行するコマンドをチェックすることができます。
.sp
\fBpolicyClassName\fRには、引数なしのコンストラクタを持ち、次のような\fBcheckExecCommand\fRメソッドを実装しているpublicクラスを指定します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB public void checkExecCommand(ActivationGroupDesc desc, String[] command)\fR
\fB        throws SecurityException;\fR
 
.fi
.if n \{\
.RE
.\}
起動グループを開始する前に、\fBrmid\fRコマンドは、ポリシーの\fBcheckExecCommand\fRメソッドを呼び出します。このとき、起動グループの記述子と、起動グループを開始するための完全なコマンドを含む配列をそのメソッドに渡します。\fBcheckExecCommand\fRが\fBSecurityException\fRをスローすると、\fBrmid\fRコマンドはその起動グループを開始せず、オブジェクトの起動を試行している呼出し側には\fBActivationException\fRがスローされます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
none
.sp
\fBsun\&.rmi\&.activation\&.execPolicy\fRプロパティの値が\fBnone\fRの場合、\fBrmid\fRコマンドは、起動グループを開始するコマンドをまったく検証しません。
.RE
.RE
.PP
\-log \fIdir\fR
.RS 4
起動システム・デーモンがデータベースおよび関連情報を書き込むのに使用するディレクトリの名前を指定します。デフォルトでは、\fBrmid\fRコマンドを実行したディレクトリに、logというログ・ディレクトリが作成されます。
.RE
.PP
\-port \fIport\fR
.RS 4
レジストリが使用するポートを指定します。起動システム・デーモンは、このレジストリの中で、\fBjava\&.rmi\&.activation\&.ActivationSystem\fRという名前で\fBActivationSystem\fRをバインドします。ローカル・マシン上の\fBActivationSystem\fRは、次のように\fBNaming\&.lookup\fRメソッドを呼び出すことによって取得できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBimport java\&.rmi\&.*; \fR
\fB    import java\&.rmi\&.activation\&.*;\fR
\fB \fR
\fB    ActivationSystem system; system = (ActivationSystem)\fR
\fB    Naming\&.lookup("//:port/java\&.rmi\&.activation\&.ActivationSystem");\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-stop
.RS 4
\fB\-port\fRオプションによって指定されたポートの、現在の\fBrmid\fRコマンドの呼出しを停止します。ポートが指定されていない場合は、このオプションはポート1098で実行されている\fBrmid\fRの呼出しを停止します。
.RE
.SH "環境変数"
.PP
CLASSPATH
.RS 4
ユーザー定義クラスへのパスをシステムに指定します。ディレクトリはコロンで区切られます。例:
\fB\&.:/usr/local/java/classes\fR
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
java(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
クラス・パスの設定
.RE
.br
'pl 8.5i
'bp
