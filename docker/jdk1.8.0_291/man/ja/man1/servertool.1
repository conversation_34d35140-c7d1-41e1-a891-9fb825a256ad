'\" t
.\" Copyright (c) 2001, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: servertool
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Java IDLおよびRMI-IIOPツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "servertool" "1" "2013年11月21日" "JDK 8" "Java IDLおよびRMI-IIOPツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
servertool \- 開発者が永続サーバーを登録、登録解除、起動、停止するための使いやすいユーザー・インタフェースを提供します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBservertool\fR \-ORBInitialPort \fInameserverport\fR [ \fIoptions\fR ] [ \fIcommands \fR]
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
commands
.RS 4
コマンド行コマンド。コマンドを参照してください。
.RE
.PP
\fBservertool\fR\fBservertool >\fR\fBservertool >\fR
.SH "説明"
.PP
\fBservertool\fRコマンドは、開発者が永続サーバーを登録、登録解除、起動、停止するためのコマンド行インタフェースを提供します。コマンド行コマンドを使用すると、サーバーに関する様々な統計情報を取得できます。コマンドを参照してください。
.SH "オプション"
.PP
\-ORBInitialHost \fInameserverhost\fR
.RS 4
このオプションは必須です。ネーム・サーバーが実行され、着信リクエストをリスニングするホスト・マシンを指定します。\fBnameserverhost\fR値は、\fBorb\fRが実行され、リクエストをリスニングしているポートを指定する必要があります。このオプションを指定しない場合、値はデフォルトで\fBlocalhost\fRに設定されます。\fBorbd\fRと\fBservertool\fRが異なるマシン上で実行されている場合は、\fBorbd\fRが実行されているホストの名前またはIPアドレスを指定する必要があります。
.sp
\fB注意:\fR
Oracle Solarisでは、1024より小さいポート上でプロセスを開始するには、rootユーザーになる必要があります。\fBnameserverport\fR値には、1024以上のポート番号を使用することをお薦めします。
.RE
.PP
\-J\fIoption\fR
.RS 4
Java Virtual Machineに\fBoption\fRを渡します。\fBoption\fRには、Javaアプリケーション起動ツールのリファレンス・ページに記載されているオプションを1つ指定します。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。java(1)を参照してください。
.RE
.SH "コマンド"
.PP
\fBservertool\fRコマンドは、コマンド行コマンドを使用して、または使用せずに起動できます。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBservertool\fRの起動時にコマンドを指定しなかった場合、コマンド行ツールにコマンド入力を求める\fBservertool\fRプロンプトが表示されます:
\fBservertool >\fR。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBservertool\fRの起動時にコマンドを指定した場合、Java IDL Server Toolが起動してコマンドを実行し、終了します。
.RE
.PP
register \-server \fIserver\-class\-name\fR \-classpath \fIclasspath\-to\-server\fR [ \-applicationName \fIapplication\-name\fR \-args \fIargs\-to\-server\fR \-vmargs \fIflags\-for\-JVM\fR ]
.RS 4
Object Request Broker Daemon (ORBD)に新規永続サーバーを登録します。サーバーが未登録の場合、登録してアクティブ化します。このコマンドによって、\fB\-server\fRオプションで識別されるサーバーの\fBメイン\fR・クラス内でインストール・メソッドが呼び出されます。このインストール・メソッドは、\fBpublic static void install(org\&.omg\&.CORBA\&.ORB)\fRになっている必要があります。インストール・メソッドはオプションであり、開発者はデータベース・スキーマの作成など独自のサーバー・インストール動作を指定できます。
.RE
.PP
unregister \-serverid \fIserver\-id\fR | \-applicationName \fIapplication\-name\fR
.RS 4
サーバーIDまたはアプリケーション名で、サーバーをORBDから登録解除します。このコマンドによって、\fB\-server\fRオプションで識別されるサーバーの\fBメイン\fR・クラス内でアンインストール・メソッドが呼び出されます。
\fBuninstall\fRメソッドは、\fBpublic static void uninstall(org\&.omg\&.CORBA\&.ORB)\fRになっている必要があります。\fBuninstall\fRメソッドはオプションであり、開発者は\fBinstall\fRメソッドの動作の取消など、独自のサーバー・アンインストール動作を指定できます。
.RE
.PP
getserverid \-applicationName \fIapplication\-name\fR
.RS 4
\fBapplication\-name\fR値に対応するサーバーIDを返します。
.RE
.PP
list
.RS 4
ORBDに登録されているすべての永続サーバーに関する情報を一覧表示します。
.RE
.PP
listappnames
.RS 4
現在ORBDに登録されているすべてのサーバーのアプリケーション名を一覧表示します。
.RE
.PP
listactive
.RS 4
ORBDによって起動され、現在実行されているすべての永続サーバーに関する情報を一覧表示します。
.RE
.PP
locate \-serverid \fIserver\-id\fR | \-applicationName \fIapplication\-name\fR [ \-endpointType \fIendpointType\fR ]
.RS 4
登録されたサーバーで作成したすべてのORBの特定のタイプについてエンドポイント(ポート)を検出します。サーバーが実行されていない場合、アクティブ化されます。\fBendpointType\fR値が指定されていない場合、サーバーのORBごとに関連付けられているplainタイプまたはnon\-protectedタイプのエンドポイントが返されます。
.RE
.PP
locateperorb \-serverid \fIserver\-id\fR | \-applicationName \fIapplication\-name\fR [ \-orbid \fIORB\-name\fR ]
.RS 4
登録されたサーバーの特定のObject Request Broker (ORB)で登録されたエンドポイント(ポート)を検出します。サーバーが実行されていない場合、アクティブ化されます。\fBorbid\fRが指定されていない場合、デフォルト値の\fB""\fRが\fBorbid\fRに割り当てられます。ORBが空文字列の\fBorbid\fRで作成されている場合、登録したポートがすべて返されます。
.RE
.PP
orblist \-serverid \fIserver\-id\fR | \-applicationName \fIapplication\-name\fR
.RS 4
サーバー上に定義されたORBの\fBORBId\fRを一覧表示します。\fBORBId\fRはサーバーで作成されたORBの文字列名です。サーバーが実行されていない場合、アクティブ化されます。
.RE
.PP
shutdown \-serverid \fIserver\-id\fR | \-applicationName application\-name
.RS 4
ORBDに登録されたアクティブなサーバーを停止します。このコマンドの実行中に、\fB\-serverid\fRパラメータまたは\fB\-applicationName\fRパラメータで指定されたクラス内に定義された\fBshutdown\fRメソッドも呼び出されてサーバー・プロセスを停止します。
.RE
.PP
startup \-serverid \fIserver\-id\fR | \-applicationName application\-name
.RS 4
ORBDに登録されたサーバーを起動またはアクティブ化します。サーバーが実行されていない場合、このコマンドがサーバーを起動します。サーバーがすでに実行されている場合は、エラー・メッセージが表示されます。
.RE
.PP
help
.RS 4
\fBservertool\fRコマンドを介してサーバーが利用できるすべてのコマンドをリストします。
.RE
.PP
quit
.RS 4
\fBservertool\fRコマンドを終了します。
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
orbd(1)
.RE
.br
'pl 8.5i
'bp
