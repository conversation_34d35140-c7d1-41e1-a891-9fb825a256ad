'\" t
.\" Copyright (c) 1995, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: appletviewer
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: 基本ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "appletviewer" "1" "2013年11月21日" "JDK 8" "基本ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
appletviewer \- Webブラウザの外側でアプレットを実行します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBappletviewer\fR [\fIoptions\fR] \fIurl\fR\&.\&.\&.
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
空白で区切られたコマンド行オプション。オプションを参照してください。
.RE
.PP
\fIurl\fR
.RS 4
表示するドキュメントまたはリソースの場所。空白で区切られた複数のURLを指定できます。
.RE
.SH "説明"
.PP
\fBappletviewer\fRコマンドは\fIurls\fRに指定されたドキュメントあるいはリソースと接続して、そのドキュメントが参照するそれぞれのアプレットを独自のウィンドウで表示します。urlsによって参照されたドキュメントが、\fBOBJECT\fR、\fBEMBED\fRまたは\fBAPPLET\fRタグでどのアプレットも参照していない場合、\fBappletviewer\fRコマンドは何も行いません。\fBappletviewer\fRコマンドでサポートされるHTMLタグの詳細は、http://docs\&.oracle\&.com/javase/8/docs/technotes/tools/appletviewertags\&.htmlにある
アプレット・ビューア・タグを参照してください。
.PP
\fBappletviewer\fRコマンドは、RFC2396で定義されたエスケープ・メカニズムに従ってエンコードされたURLを必要とします。サポートされるのは、エンコードされたURLのみです。ただし、ファイル名については、RFC2396の仕様に従ってエンコードを解除しておく必要があります。
.PP
\fB注意:\fR
The
\fBappletviewer\fRコマンドは開発専用です。詳細は、http://docs\&.oracle\&.com/javase/8/docs/technotes/samples/aboutCodeSamples\&.htmlにある
サンプル/テスト・アプリケーションおよびコードについてを参照してください。
.SH "オプション"
.PP
\-debug
.RS 4
\fBjdb\fRコマンドを使用してJavaデバッガでアプレット・ビューアを開始して、ドキュメント中のアプレットをデバッグします。
.RE
.PP
\-encoding \fIencoding\-name\fR
.RS 4
入力HTMLファイルのエンコーディング名を指定します。
.RE
.PP
\-J\fIjavaoption\fR
.RS 4
文字列\fBjavaoption\fRは、アプレット・ビューアを実行するJavaインタプリタに1つの引数として渡されます。引数にスペースを含めないでください。複数の引数は、すべてが接頭辞\fB\-J\fRで始まる必要があります。これは、コンパイラの実行環境またはメモリー使用の調整に有効です。
.RE
.PP

.br
'pl 8.5i
'bp
