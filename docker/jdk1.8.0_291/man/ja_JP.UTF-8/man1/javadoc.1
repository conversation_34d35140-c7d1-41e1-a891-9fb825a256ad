'\" t
.\" Copyright (c) 1994, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: javadoc
.\" Language: Japanese
.\" Date: 2015年3月3日
.\" SectDesc: 基本ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "javadoc" "1" "2015年3月3日" "JDK 8" "基本ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
javadoc \- Javaソース・ファイルから、APIドキュメントのHTMLページを生成します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc\fR {\fIpackages\fR|\fIsource\-files\fR} [\fIoptions\fR] [\fI@argfiles\fR]
.fi
.if n \{\
.RE
.\}
.PP
\fIpackages\fR
.RS 4
\fBjava\&.lang java\&.lang\&.reflect java\&.awt\fRなど、空白で区切ってドキュメント化するパッケージの名前。サブパッケージもドキュメント化する場合は、\fB\-subpackages\fRオプションを使用してパッケージを指定します。
.sp
デフォルトでは、\fBjavadoc\fRは、現在のディレクトリおよびサブディレクトリで指定されたパッケージを探します。\fB\-sourcepath\fRオプションを使用して、パッケージを探すディレクトリのリストを指定します。
.RE
.PP
\fIsource\-files\fR
.RS 4
\fBClass\&.java Object\&.java Button\&.java\fRのように空白で区切った、ドキュメント化するJavaソース・ファイルの名前。デフォルトでは、\fBjavadoc\fRは、現在のディレクトリで指定されたクラスを探します。ただし、\fB/home/<USER>/java/awt/Graphics*\&.java\fRのように、クラス・ファイルのフルパスを指定し、ワイルドカード文字を使用できます。現在のディレクトリからの相対パスも指定できます。
.RE
.PP
\fIoptions\fR
.RS 4
空白で区切られたコマンド行オプション。オプションを参照してください。
.RE
.PP
\fI@argfiles\fR
.RS 4
\fBjavadoc\fRコマンド・オプション、パッケージ名およびソース・ファイル名のリストを任意の順序で含むファイルの名前。
.RE
.SH "説明"
.PP
\fBjavadoc\fRコマンドは、一連のJavaソース・ファイルにある宣言およびドキュメンテーション・コメントを解析し、デフォルトでは、publicクラス、protectedクラス、ネストされたクラス(匿名の内部クラスは除く)、インタフェース、コンストラクタ、メソッド、およびフィールドについて記述した一連のHTMLページを生成します。\fBjavadoc\fRコマンドは、APIドキュメントの生成や、一連のソース・ファイルの実装ドキュメントの生成に使用できます。
.PP
\fBjavadoc\fRコマンドは、パッケージ全体、個々のソース・ファイル、またはその両方に対して実行できます。パッケージ全体のドキュメント化を行うには、\fB\-subpackages\fRオプションを使用してディレクトリおよびそのサブディレクトリを再帰的にたどるか、パッケージ名の明示的なリストを渡します。個々のソース・ファイルをドキュメント化するには、Javaソース・ファイル名のリストを渡します。簡単な例を参照してください。
.SS "ソース・ファイルの処理"
.PP
\fBjavadoc\fRコマンドは、ソースで終わるファイル、およびソース・ファイルで説明しているその他のファイルを処理します。個々のソース・ファイル名を渡して\fBjavadoc\fRを実行する場合、どのソース・ファイルを処理するかを正確に指定できます。ただし、多くの開発者はこの方法では作業しません。パッケージ名を渡すほうが簡単だからです。ソース・ファイル名を明示的に指定しなくても、\fBjavadoc\fRコマンドは3つの方法で実行できます。パッケージ名を渡し、\fB\-subpackages\fRオプションを使用するか、またはソース・ファイル名にワイルドカードを使用することができます。これらの場合、\fBjavadoc\fRコマンドがソース・ファイルの処理を行うのは、そのファイルが次のすべての要件を満たす場合のみです。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
ファイル名の接頭辞(\fB\&.java\fRを削除)が有効なクラス名である。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
ソース・ツリーのルートからの相対的なパス名が、区切り文字をドットに変換すると、有効なパッケージ名になる。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
パッケージ文に有効なパッケージ名が含まれている。
.RE
.PP
リンクの処理
.PP
処理の実行中に、\fBjavadoc\fRコマンドは、その実行でドキュメント化されるパッケージ、クラス、およびメンバーの名前に対して、相互参照リンクを追加します。リンクは、次の場所に表示されます。@タグの説明については、javadocタグを参照してください。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
宣言(戻り値の型、引数の型、フィールドの型)。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB@see\fRタグから生成された\fI「関連項目」\fRセクション。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB{@link}\fRタグから生成されたインライン・テキスト。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB@throws\fRタグから生成された例外の名前。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
インタフェースのメンバーに対する\fI「定義」\fRリンクと、クラスのメンバーに対する\fI「オーバーライド」\fRリンク。メソッド・コメントの継承を参照してください。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
パッケージ、クラス、およびメンバーをリストしているサマリー表。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
パッケージおよびクラスの継承ツリー。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
索引。
.RE
.PP
コマンド行で指定しなかったクラスについての既存のテキスト(別に生成したテキスト)に対してリンクを追加するには、\fB\-link\fRおよび\fB\-linkoffline\fRオプションを利用できます。
.PP
処理の詳細
.PP
\fBjavadoc\fRコマンドは実行するたびに1つの完全なドキュメントを生成します。前の実行の結果を変更または直接取り込む、増分ビルドを行いません。ただし、\fBjavadoc\fRコマンドは、他の実行の結果にリンクできます。
.PP
\fBjavadoc\fRコマンドの実装にはJavaコンパイラが必要で、Javaコンパイラに依存しています。\fBjavadoc\fRコマンドは\fBjavac\fRコマンドの一部を呼び出し、宣言をコンパイルして、メンバーの実装を無視します。\fBjavadoc\fRコマンドは、クラス階層を含むクラスの豊富な内部表現とクラスの「使用」関係を構築し、HTMLを生成します。さらに、J\fBjavadoc\fRコマンドは、ソース・コードのドキュメンテーション・コメントから、ユーザーの提供したドキュメントも取得します。ドキュメンテーション・コメントを参照してください。
.PP
\fBjavadoc\fRコマンドは、メソッド本体を持たない純粋なスタブ・ファイルであるソース・ファイルに対して実行できます。したがって、APIの実装前の設計の早い段階で、ドキュメンテーション・コメントを記述して\fBjavadoc\fRコメントを実行できます。
.PP
コンパイラに依存することによって、HTML出力は、実際の実装に正確に対応します。実際の実装は、明示的なソース・コードにではなく、暗黙のソース・コードに依存する場合があります。たとえば、\fBjavadoc\fRコマンドは、コンパイル済クラス・ファイルには存在するがソース・コードには存在しないデフォルト・コンストラクタをドキュメント化します。
.PP
多くの場合、\fBjavadoc\fRコマンドでは、ソース・ファイルのコードが不完全またはエラーを含んでいる場合でもドキュメントを生成できます。すべてのデバッグやトラブルシューティングを完了する前にドキュメントを生成できます。\fBjavadoc\fRコマンドはドキュメンテーション・コメントの基本的なチェックを行います。
.PP
\fBjavadoc\fRコマンドは、ドキュメントの内部構造を構築する際、参照クラスをすべてロードします。このため、
\fBjavadoc\fRコマンドは、ブートストラップ・クラス、拡張機能、またはユーザー・クラスにかかわらず、すべての参照クラスを検索できる必要があります。クラスの検出方法
(http://docs\&.oracle\&.com/javase/8/docs/technotes/tools/findingclasses\&.html)を参照してください
.PP
通常、作成するクラスは、拡張クラスとして、または\fBjavadoc\fRコマンドのクラス・パスでロードされる必要があります。
.SS "Javadocのドックレット"
.PP
\fBjavadoc\fRコマンドの出力の内容と形式は、ドックレットを使用してカスタマイズできます。\fBjavadoc\fRコマンドには、標準ドックレットと呼ばれるデフォルトの組込みドックレットがあります。標準ドックレットは、HTML形式のAPIドキュメントを生成します。標準ドックレットを修正またはサブクラスを作成することや、HTML、XML、MIF、RTFなどの好みの出力形式を生成する独自のドックレットを記述することも可能です。
.PP
\fB\-doclet\fRオプションでカスタム・ドックレットが指定されていない場合、\fBjavadoc\fRコマンドは、デフォルトの標準ドックレットを使用します。\fBjavadoc\fRコマンドには、使用されているドックレットに関係なく使用できるいくつかのオプションがあります。標準ドックレットでは、これらの他に、いくつかのコマンド行オプションが追加されます。オプションを参照してください。
.SH "ソース・ファイル"
.PP
\fBjavadoc\fRコマンドは、次のタイプのソース・ファイルから出力を生成します。そのファイルは、クラスのJava言語ソース・ファイル(\fB\&.java\fR)、パッケージ・コメント・ファイル、概要コメント・ファイル、およびその他の未処理のファイルです。ここでは、ドキュメント化しないがソース・ツリーに存在する場合があるテスト・ファイルやテンプレート・ファイルについても説明します。
.SS "クラスのソース・ファイル"
.PP
それぞれのクラスまたはインタフェース、およびそのメンバーは、独自のドキュメンテーション・コメントを持つことができ、それをソース・ファイル内に保持します。ドキュメンテーション・コメントを参照してください。
.SS "パッケージ・コメント・ファイル"
.PP
それぞれのパッケージは、独自のドキュメンテーション・コメントを持つことができ、それを専用のソース・ファイルに保持します。その内容は、\fBjavadoc\fRコマンドによって生成されるパッケージのサマリー・ページに組み込まれます。このコメントには、通常、そのパッケージ全体に当てはまるドキュメントを記述します。
.PP
パッケージ・コメント・ファイルを作成するには、次のいずれかのファイルにコメントを格納できます。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBpackage\-info\&.java\fRファイルには、パッケージ宣言、パッケージ注釈、パッケージ・コメント、およびJavadocタグを格納できます。このファイルが優先されます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBpackage\&.html\fRファイルには、パッケージ・コメントとJavadocタグのみを格納できます。パッケージ注釈は格納できません。
.RE
.PP
各パッケージは、\fBpackage\&.html\fRファイルまたは\fBpackage\-info\&.java\fRファイルのいずれかを1つ持つことができますが、その両方を持つことはできません。このどちらかのファイルをソース・ファイルとともに、ソース・ツリー内のそのパッケージ・ディレクトリ内に配置してください。
.PP
package\-info\&.javaファイル
.PP
\fBpackage\-info\&.java\fRファイルには、次の構造のパッケージ・コメントを含めることができます。コメントは、パッケージ宣言の前に配置されます。
.PP
\fB注意:\fR
コメント区切り文字である\fB/**\fRおよび\fB*/\fRが存在する必要がありますが、中間の行の先頭のアスタリスクは省略可能です。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * Provides the classes necessary to create an  \fR
\fB * applet and the classes an applet uses \fR
\fB * to communicate with its applet context\&.\fR
\fB * <p>\fR
\fB * The applet framework involves two entities:\fR
\fB * the applet and the applet context\&.\fR
\fB * An applet is an embeddable window (see the\fR
\fB * {@link java\&.awt\&.Panel} class) with a few extra\fR
\fB * methods that the applet context can use to \fR
\fB * initialize, start, and stop the applet\&.\fR
\fB *\fR
\fB * @since 1\&.0\fR
\fB * @see java\&.awt\fR
\fB */\fR
\fBpackage java\&.lang\&.applet;\fR
 
.fi
.if n \{\
.RE
.\}
.PP
package\&.htmlファイル
.PP
\fBpackage\&.html\fRファイルには、次の構造のパッケージ・コメントを含めることができます。コメントは、\fB<body>\fR要素に配置されます。
.PP
ファイル:
\fBjava/applet/package\&.html\fR
.sp
.if n \{\
.RS 4
.\}
.nf
\fB<HTML>\fR
\fB<BODY>\fR
\fBProvides the classes necessary to create an applet and the \fR
\fBclasses an applet uses to communicate with its applet context\&.\fR
\fB<p>\fR
\fBThe applet framework involves two entities: the applet\fR
\fBand the applet context\&. An applet is an embeddable\fR
\fBwindow (see the {@link java\&.awt\&.Panel} class) with a\fR
\fBfew extra methods that the applet context can use to\fR
\fBinitialize, start, and stop the applet\&. \fR
 
\fB@since 1\&.0 \fR
\fB@see java\&.awt\fR
\fB</BODY>\fR
\fB</HTML>\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBpackage\&.html\fRファイルは通常のHTMLファイルであり、パッケージ宣言を含んでいません。パッケージ・コメント・ファイルの内容はHTMLで記述しますが、例外が1つあります。このドキュメンテーション・コメントには、コメント区切り文字である\fB/**\fRと\fB*/\fR、または行頭のアスタリスクを含めない、という点です。コメントを書く場合は、最初の文をパッケージのサマリーとし、\fB<body>\fRタグと最初の文の間にタイトルやその他のテキストを含めないようにします。パッケージ・タグを含めることができます。すべてのブロック・タグは、主説明の後に配置する必要があります。\fB@see\fRタグをパッケージ・コメント・ファイルに追加する場合には、完全修飾名を使用する必要があります。
.PP
コメント・ファイルの処理
.PP
\fBjavadoc\fRコメントを実行すると、パッケージ・コメント・ファイルが検索されます。パッケージ・コメント・ファイルが見つかった場合は、\fBjavadoc\fRコマンドは次の手順を実行します。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
処理できるようにコメントをコピーします。package\&.htmlの場合、\fBjavadoc\fRコマンドは、\fB<body>\fRと\fB</body>\fR
HTMLタグ間ですべてのコンテンツをコピーします。\fB<title>\fRタグ、ソース・ファイルの著作権文または他の情報を配置する\fB<head>\fRセクションを含めることができますが、これらは生成されたドキュメントに表示されません。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
パッケージ・タグを処理します。パッケージ・タグを参照してください。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
処理したテキストを生成されたパッケージのサマリー・ページの下部に挿入します。Javaプラットフォーム、Standard Edition API仕様の概要
(http://docs\&.oracle\&.com/javase/8/docs/api/overview\-summary\&.html)を参照してください
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
パッケージのサマリー・ページの先頭に、パッケージ・コメントの最初の文をコピーします。さらに、\fBjavadoc\fRコマンドは、概要ページのパッケージ・リストに、パッケージ名とパッケージ・コメントの最初の文を追加します。Javaプラットフォーム、Standard Edition API仕様の概要
(http://docs\&.oracle\&.com/javase/8/docs/api/overview\-summary\&.html)を参照してください
.sp
文の終わりは、クラスやメンバーの主説明の最初の文の終わりと同じルールによって判断されます。
.RE
.SS "概要コメント・ファイル"
.PP
ドキュメント化する各アプリケーションまたはパッケージ・セットは、独自の概要ドキュメンテーション・コメントを持つことができ、それは専用のソース・ファイルに保持されます。その内容は、\fBjavadoc\fRコマンドによって生成される概要ページに組み込まれます。このコメントには、通常、アプリケーションまたはパッケージ・セット全体に当てはまるドキュメントを記述します。
.PP
このファイルにはoverview\&.htmlなどの名前を付けることができ、どこに配置してもかまいません。一般的な場所は、ソース・ツリーの最上部です。
.PP
たとえば、\fBjava\&.applet\fRパッケージのソース・ファイルが/home/<USER>/src/java/appletディレクトリに格納されている場合、概要コメント・ファイルは/home/<USER>/src/overview\&.htmlに作成できます。
.PP
異なるパッケージのセットに対して\fBjavadoc\fRコマンドを複数回実行する場合は、同じ1つのソース・ファイルのセットに対して複数の概要コメント・ファイルを作成できます。たとえば、内部ドキュメント用に\fB\-private\fRを指定して\fBjavadoc\fRコマンドを1回実行した後、公開ドキュメント用にそのオプションを指定しないで再度実行することができます。この場合、各概要コメント・ファイルの1文目で、そのドキュメントを公開用または内部用として記述できます。
.PP
概要コメント・ファイルの内容は、HTMLで記述された1つの大きなドキュメンテーション・コメントです。最初の文はアプリケーションまたはパッケージのセットのサマリーとします。\fB<body>\fRタグと最初の文の間にタイトルやその他のテキストを含めないようにします。{\fB@link}\fRなどのインライン・タグを除くすべてのタグは、主説明の後に配置する必要があります。\fB@see\fRタグを追加する場合には、完全修飾名を使用する必要があります。
.PP
\fBjavadoc\fRコマンドの実行時に、\fB\-overview\fRオプションを使用して概要コメント・ファイル名を指定します。このファイルは、パッケージ・コメント・ファイルと同じように処理されます。\fBjavadoc\fRコマンドは次の手順を実行します。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB<body>\fRと\fB</body>\fRタグ間で、処理するすべてのコンテンツをコピーします。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
概要タグがあれば処理します。概要タグを参照してください。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
処理したテキストを生成された概要ページの下部に挿入します。JavaプラットフォームStandard Edition API仕様の概要
(http://docs\&.oracle\&.com/javase/8/docs/api/overview\-summary\&.html)を参照してください
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
概要サマリー・ページの先頭に、概要コメントの最初の文をコピーします。
.RE
.SS "未処理のファイル"
.PP
ソース・ファイルには、\fBjavadoc\fRコマンドによって宛先ディレクトリにコピーされる、任意のファイルを含めることができます。このようなファイルには、通常、グラフィック・ファイル、サンプルのJavaソースおよびクラス・ファイル、一般的なJavaソース・ファイルのドキュメンテーション・コメントの影響を受けない多くの内容を含む独立したHTMLファイルなどがあります。
.PP
未処理のファイルを含めるには、doc\-filesというディレクトリにファイルを配置します。doc\-filesディレクトリは、ソース・ファイルを含む任意のパッケージ・ディレクトリのサブディレクトリになることができます。doc\-filesサブディレクトリは、パッケージごとに1つ用意できます。
.PP
たとえば、ボタンのイメージを\fBjava\&.awt\&.Button\fRクラスのドキュメントに含める場合には、そのイメージ・ファイルを/home/<USER>/src/java/awt/doc\-files/ディレクトリに置きます。doc\-filesディレクトリを/home/<USER>/src/java/doc\-filesに置かないでください。javaはパッケージではないからです。ソース・ファイルを含めることもできません。
.PP
\fBjavadoc\fRコマンドはファイルを参照しないので、未処理のファイルへのすべてのリンクは、コードに含まれている必要があります。\fBjavadoc\fRコマンドはディレクトリとそのすべての内容を宛先にコピーします。次の例では、Button\&.javaドキュメンテーション・コメントのリンクがどのように見えるかを示しています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * This button looks like this: \fR
\fB * <img src="doc\-files/Button\&.gif">\fR
\fB */\fR
 
.fi
.if n \{\
.RE
.\}
.SS "テストおよびテンプレート・ファイル"
.PP
ソース・ツリーのテストおよびテンプレート・ファイルを、ソース・ファイルが存在するディレクトリまたはサブディレクトリと同じディレクトリに格納できます。テストおよびテンプレート・ファイルが処理されるのを防ぐには、\fBjavadoc\fRコマンドを実行し、明示的に個別のソース・ファイル名を渡します。
.PP
テスト・ファイルは、有効な、コンパイル可能なソース・ファイルです。テンプレート・ファイルは、有効な、互換性のあるソース・ファイルではありませんが、多くの場合、\fB\&.java\fR接尾辞を持っています。
.PP
テスト・ファイル
.PP
テスト・ファイルを、名前なしパッケージや、ソース・ファイルが存在するパッケージとは別のパッケージに属するようにする場合、テスト・ファイルをソース・ファイルの下のサブディレクトリに配置し、そのディレクトリに無効な名前を付けます。テスト・ファイルをソースと同じディレクトリに配置し、パッケージ名を示すコマンド行引数を指定して\fBjavadoc\fRコマンドを呼び出すと、テスト・ファイルは警告またはエラーを引き起こします。ファイルが無効な名前を持つサブディレクトリ内に存在する場合、テスト・ファイル・ディレクトリはスキップされ、エラーまたは警告は発行されません。たとえば、ソース・ファイルのテスト・ファイルをcom\&.package1に追加するには、無効なパッケージ名のサブディレクトリに配置します。次のディレクトリ名にはハイフンが含まれているため無効です。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBcom/package1/test\-files/\fR
 
.fi
.if n \{\
.RE
.\}
.PP
テスト・ファイルにドキュメンテーション・コメントが含まれる場合、\fBjavadoc\fRコマンドの個別の実行で、ワイルドカードを含んだテスト・ソース・ファイル名(\fBcom/package1/test\-files/*\&.java\fRなど)を渡して、テスト・ファイルのドキュメントを生成するように設定できます。
.PP
テンプレート・ファイル
.PP
テンプレート・ファイルをソース・ディレクトリに配置するが、\fBjavadoc\fRコマンドを実行するときにエラーを生成しない場合、ファイルに\fBBuffer\-Template\&.java\fRなどの無効な名前を付けて、処理させないようにします。\fBjavadoc\fRコマンドは、接尾辞の\fB\&.java\fRが削除されると有効なクラス名になる名前を持つソース・ファイルのみを処理します。
.SH "生成されるファイル"
.PP
デフォルトでは、\fBjavadoc\fRコマンドは、HTML形式のドキュメントを生成する標準ドックレットを使用します。標準ドックレットは、ここで説明する、基本内容ページ、相互参照ページ、サポート・ページを生成します。各HTMLページは個別のファイルに対応します。\fBjavadoc\fRコマンドは、2つのタイプのファイルを生成します。最初のタイプには、クラスおよびインタフェースに応じた名前が付けられます。2番目のタイプには、最初のタイプのファイルとの競合を防ぐために、ハイフンが含まれます(package\-summary\&.htmlなど)。
.SS "基本内容ページ"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
記載されているクラスまたはインタフェースごとに1つのクラスまたはインタフェース・ページ(classname\&.html)。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
記載されているパッケージごとに1つのパッケージ・ページ(package\-summary\&.html)。\fBjavadoc\fRコマンドは、ソース・ツリーのパッケージ・ディレクトリ内にあるpackage\&.htmlまたはpackage\-info\&.javaという名前のファイル内のHTMLテキストをすべて組み入れます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
パッケージのセット全体に対して1つの概要ページ(overview\-summary\&.html)。概要ページは、生成ドキュメントの先頭ページになります。\fBjavadoc\fRコマンドは、\fB\-overview\fRオプションで指定されたファイル内のHTMLテキストをすべて組み入れます。概要ページが作成されるのは、\fBjavadoc\fRコマンドに複数のパッケージ名を渡した場合のみです。HTMLフレームおよびオプションを参照してください。
.RE
.SS "相互参照ページ"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
パッケージのセット全体に対して1つのクラス階層ページ(overview\-tree\&.html)。階層ページを表示するには、ナビゲーション・バーの「概要」をクリックしてから、「階層ツリー」をクリックします。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
パッケージごとに1つのクラス階層ページ(package\-tree\&.html)。階層ページを表示するには、特定のパッケージ、クラス、またはインタフェースのページに移動し、「階層ツリー」をクリックしてそのパッケージの階層を表示します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
パッケージごとに1つの使用ページ(package\-use\&.html)と、クラスおよびインタフェースごとに1つずつの使用ページ(class\-use/classname\&.html)。使用ページでは、指定したクラス、インタフェース、またはパッケージの一部を使用しているパッケージ、クラス、メソッド、コンストラクタ、およびフィールドについて記述されます。たとえば、クラスまたはインタフェースAを例にすると、その使用ページには、Aのサブクラス、Aとして宣言されたフィールド、Aを返すメソッド、A型のパラメータを持つメソッドおよびコンストラクタが組み込まれます。使用ページを表示するには、パッケージ、クラス、またはインタフェースに移動し、ナビゲーション・バーの「使用」リンクをクリックします。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
すべての非推奨APIとその推奨する代替をリストする非推奨APIページ(deprecated\-list\&.html)。非推奨APIは将来の実装で削除される可能性があるので使用しないでください。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
定数フィールドの値用の定数フィールド値ページ(constant\-values\&.html)。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
直列化された形式ページ(serialized\-form\&.html)。フィールドおよびメソッドの説明を含む、直列化可能かつ外部化可能なクラスに関する情報用のページです。このページ内の情報は、APIを使用する開発者ではなく、再実装者に必要な情報です。直列化された形式ページへアクセスするには、直列化されたクラスに移動して、そのクラス・コメントにある「関連項目」セクションで「直列化された形式」をクリックします。標準ドックレットは直列化された形式ページを生成します。このページには、Serializableを実装するすべてのクラス(publicまたは非public)が、その\fBreadObject\fRや\fBwriteObject\fRメソッド、直列化されたフィールド、および\fB@serial\fR、\fB@serialField\fR、\fB@serialData\fRタグからのドキュメンテーション・コメントとともにリストされます。直列化可能なpublicクラスを除外するには、そのクラス(またはそのパッケージ)を\fB@serial\fR
excludeでマークします。直列化可能なpackage\-privateクラスを含めるには、そのクラス(またはそのパッケージ)を\fB@serial\fR
includeでマークします。リリース1\&.4では、\fB\-private\fRオプションを指定せずに\fBjavadoc\fRコマンドを実行することにより、publicクラスおよびprivateクラスの完全に直列化された形式を生成できます。オプションを参照してください。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
索引ページ(\fBindex\-*\&.html\fR)。すべてのクラス名、インタフェース名、コンストラクタ名、フィールド名、およびメソッド名がアルファベット順に並んでいます。索引ページは、Unicodeを扱えるように国際化されています。1つのファイルとして生成することも、先頭文字(英語の場合A\(enZ)ごとに別々のファイルとして生成することもできます。
.RE
.SS "サポート・ページ"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
ヘルプ・ページ(help\-doc\&.html)。ナビゲーション・バーや前述の各ページに関する説明が記載されています。デフォルトのヘルプ・ファイルを独自のカスタム・ヘルプ・ファイルでオーバーライドするには、\fB\-helpfile\fRを使用します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
表示用のHTMLフレームを作成する1つのindex\&.htmlファイル。フレーム付きの先頭ページを表示するにはこのファイルをロードします。index\&.htmlファイルには、テキスト・コンテンツは含まれていません。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
複数のフレーム・ファイル(\fB*\-frame\&.html\fR)。パッケージ、クラス、およびインタフェースのリストが含まれています。フレーム・ファイルはHTMLフレームを表示します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
パッケージ・リスト・ファイル(package\-list)。\fB\-link\fRおよび\fB\-linkoffline\fRオプションで使用されます。パッケージ・リスト・ファイルはテキスト・ファイルであり、どのリンクからもアクセスできません。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
スタイルシート・ファイル(stylesheet\&.css)。生成されるページの一部の要素について色、フォント・ファミリ、フォント・サイズ、フォント・スタイル、および配置を制御します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
doc\-filesディレクトリ。宛先ディレクトリにコピーするイメージ、サンプル・コード、ソース・コードなどのファイルが格納されます。これらのファイルは、\fBjavadoc\fRコマンドによって処理されません。このディレクトリは、ソース・ツリーの中に存在する場合にのみ処理されます。
.RE
.PP
オプションを参照してください。
.SS "HTMLフレーム"
.PP
\fBjavadoc\fRコマンドは、コマンドに渡された値に基づき、最小限必要な数(2または3)のフレームを生成します。\fBjavadoc\fRコマンドに引数として1つのパッケージ名または1つのパッケージに含まれるソース・ファイルを渡す場合は、パッケージのリストが省略されます。そのかわりに、\fBjavadoc\fRコマンドは左側の列に1つのフレームを作成し、クラスのリストを表示します。複数のパッケージ名を渡した場合は、\fBjavadoc\fRコマンドは、すべてのパッケージをリストする第3のフレームと概要ページ(overview\-summary\&.html)を作成します。フレームを省略するには、「フレームなし」リンクをクリックするか、overview\-summary\&.htmlページからページ・セットを表示します。
.SS "生成されるファイルの構造"
.PP
生成されるクラス・ファイルおよびインタフェース・ファイルは、Javaソース・ファイルおよびクラス・ファイルと同じディレクトリ階層に編成されます。1つのサブパッケージにつき1つのディレクトリ、という構造になります。
.PP
たとえば、\fBjava\&.applet\&.Applet\fRクラス用に生成されるドキュメントは、java/applet/Applet\&.htmlに格納されます。
.PP
生成先ディレクトリの名前が\fBapidocs\fRだとすると、\fBjava\&.applet\fRパッケージのファイルの構造は、次のとおりです。前述のように、\fIframe\fRという語を名前に含むファイルは、すべて左上または左下のフレームに表示されます。それ以外のHTMLファイルは、すべて右側のフレームに表示されます。
.PP
ディレクトリは太字です。アスタリスク(*)は、\fBjavadoc\fRコマンドへの引数がパッケージ名ではなくソース・ファイル名である場合に省略されるファイルおよびディレクトリを示しています。引数がソース・ファイル名の場合、空のパッケージ・リストが作成されます。doc\-filesディレクトリは、ソース・ツリー内に存在する場合にのみ、生成先に作成されます。生成されるファイルを参照してください。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBapidocs\fR: 最上位レベル・ディレクトリ
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
index\&.html: HTMLフレームを設定する初期ページ
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
*overview\-summary\&.html: パッケージ・リストとサマリー
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
overview\-tree\&.html: すべてのパッケージのクラス階層
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
deprecated\-list\&.html: すべてのパッケージの非推奨API
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
constant\-values\&.html: すべてのパッケージの静的フィールド値
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
serialized\-form\&.html: すべてのパッケージの直列化されたフォーム
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
*overview\-frame\&.html: 左上のフレームに表示するすべてのパッケージ
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
allclasses\-frame\&.html: 左下のフレームに表示するすべてのクラス
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
help\-doc\&.html: Javadocページの編成に関するヘルプ
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
index\-all\&.html:
\fB\-splitindex\fRオプションなしで作成されたデフォルトの索引
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBindex\-files\fR:
\fB\-splitindex\fRオプションを指定して作成されたディレクトリ
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
index\-<number>\&.html:
\fB\-splitindex\fRオプションを指定して作成された索引ファイル
.RE
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
package\-list: 外部参照を解決するためのパッケージ名
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
stylesheet\&.css: フォント、色、位置などを定義します
.RE
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBjava\fR: パッケージ・ディレクトリ
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBapplet\fR: サブパッケージ・ディレクトリ
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Applet\&.html:
\fBApplet\fRクラス・ページ
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
AppletContext\&.html:
\fBAppletContext\fRインタフェース
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
AppletStub\&.html:
\fBAppletStub\fRインタフェース
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
AudioClip\&.html:
\fBAudioClip\fRインタフェース
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
package\-summary\&.html: クラスとサマリー
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
package\-frame\&.html: 左下のフレームに表示するパッケージ・クラス
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
package\-tree\&.html: このパッケージのクラス階層
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
package\-use\&.html: このパッケージが使用される場所
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBdoc\-files\fR: イメージおよびサンプル・ファイルのディレクトリ
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBclass\-use\fR: イメージおよびサンプル・ファイルの場所
.sp
\- Applet\&.html: Appletクラスの使用
.sp
\- AppletContext\&.html:
\fBAppletContext\fRインタフェースの使用
.sp
\- AppletStub\&.html:
\fBAppletStub\fRインタフェースの使用
.sp
\- AudioClip\&.html:
\fBAudioClip\fRインタフェースの使用
.RE
.RE
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBsrc\-html\fR: ソース・コード・ディレクトリ
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBjava\fR: パッケージ・ディレクトリ
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBapplet\fR: サブパッケージ・ディレクトリ
.sp
\- Applet\&.html: Appletソース・コード
.sp
\- AppletContext\&.html:
\fBAppletContext\fRソース・コード
.sp
\- AppletStub\&.html:
\fBAppletStub\fRソース・コード
.sp
\- AudioClip\&.html:
\fBAudioClip\fRソース・コード
.RE
.RE
.RE
.SS "生成されるAPI宣言"
.PP
\fBjavadoc\fRコマンドは、それぞれのクラス、インタフェース、フィールド、コンストラクタ、およびメソッドの記述の最初に、そのAPI用の宣言を生成します。たとえば、\fBBoolean\fRクラスの宣言は、次のようになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpublic final class Boolean\fR
\fBextends Object\fR
\fBimplements Serializable\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBBoolean\&.valueOf\fRメソッドの宣言は次のとおりです。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpublic static Boolean valueOf(String s)\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBjavadoc\fRコマンドは、修飾子\fBpublic\fR、\fBprotected\fR、\fBprivate\fR、\fBabstract\fR、\fBfinal\fR、\fBstatic\fR、\fBtransient\fR、および\fBvolatile\fRを含めることができますが、\fBsynchronized\fRおよび\fBnative\fRはできません。\fBsynchronized\fRおよび\fBnative\fR修飾子は、実装の詳細とみなされているため、API仕様には含まれません。
.PP
APIでは、並行性セマンティクスについて、キーワード\fBsynchronized\fRに依存するのではなく、コメントの主説明としてドキュメント化する必要があります。たとえば、「1つのenumerationを複数のスレッドから並行して使用することはできない」のように記述します。ドキュメントには、これらのセマンティクスを実現する方法を記述しないでください。たとえば、\fBHashtable\fRオプションはスレッドセーフである必要がありますが、「エクスポートされるすべてのメソッドを同期化してそれを実現する」のように指定する根拠はありません。より高度な並行性のために、バケット・レベルで内部的に同期化する権限を保有しておくことをお薦めします。
.SH "ドキュメンテーション・コメント"
.PP
このセクションでは、ソース・コードのコメントとコメントの継承について説明します。
.SS "ソース・コード・コメント"
.PP
ソース・コードの任意のクラス、インタフェース、メソッド、コンストラクタ、またはフィールドの宣言の前に、ドキュメンテーション・コメントを記述することができます。各パッケージにもドキュメンテーション・コメントを作成できます。構文は若干異なりますが、概要にもドキュメンテーション・コメントを作成できます。ドキュメンテーション・コメントは、\fB/**\fRと、終わりを表す\fB*/\fRの間にある文字から構成されます。先頭のアスタリスクは各行で使用でき、次の項で詳しく説明します。コメントのテキストは、複数行にわたって記述できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * This is the typical format of a simple documentation comment\fR
\fB * that spans two lines\&.\fR
\fB */\fR
 
.fi
.if n \{\
.RE
.\}
.PP
スペースを節約するには、コメントを1行に入れます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/** This comment takes up only one line\&. */\fR
 
.fi
.if n \{\
.RE
.\}
.PP
コメントの配置
.PP
ドキュメンテーション・コメントは、クラス、インタフェース、コンストラクタ、メソッド、またはフィールド宣言の直前に配置される場合にのみ認識されます。メソッドの本体に置かれているドキュメンテーション・コメントは無視されます。\fBjavadoc\fRコマンドは、宣言文ごとに1つのドキュメンテーション・コメントしか認識しません。タグを使用できる場所を参照してください。
.PP
よくある間違いは、クラス・コメントとクラス宣言の間に\fBimport\fR文を置いてしまうことです。\fBjavadoc\fRコマンドはクラス・コメントを無視するので、\fBimport\fR文をこの場所に配置しないでください。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * This is the class comment for the class Whatever\&.\fR
\fB */\fR
\fB \fR
\fBimport com\&.example;   // MISTAKE \- Important not to put import statement here\fR
\fB \fR
\fBpublic class Whatever{ }\fR
 
.fi
.if n \{\
.RE
.\}
.PP
コメントのパーツ
.PP
ドキュメンテーション・コメントには、主説明とその後に続くタグ・セクションが含まれます。主説明は、開始区切り文字\fB/**\fRで始まり、タグ・セクションまで続きます。タグ・セクションは、先頭文字が\fB@\fRの行で定義される最初のブロック・タグから始まります(先頭のアスタリスク、空白文字、先頭の区切り文字\fB/**\fRは除く)。主説明を記述せず、タグ・セクションのみのコメントを記述することもできます。主説明は、タグ・セクション以降に続けることはできません。タグの引数は、複数行にわたって記述できます。タグの数に制限はありません。何回も記述できるタグと、1回しか記述できないタグがあります。たとえば、次の\fB@see\fRタグからタグ・セクションは始まります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * This sentence holds the main description for this documentation comment\&.\fR
\fB * @see java\&.lang\&.Object\fR
\fB */\fR
 
.fi
.if n \{\
.RE
.\}
.PP
ブロックおよびインライン・タグ
.PP
タグは、\fBjavadoc\fRコマンドが処理するドキュメンテーション・コメント内の特殊なキーワードです。タグには2つのタイプがあります。1つは\fB@tag\fRタグのように表記されるブロック・タグ(スタンドアロン・タグとも呼ばれる)、もう1つは\fB{@tag}\fRタグのように中カッコで囲んで表記されるインライン・タグです。ブロック・タグが解釈されるには、行頭のアスタリスク、空白文字、区切り文字(\fB/**\fR)を除いて、行の先頭に置く必要があります。これは、\fB@\fR文字をテキスト内の別の場所で使用しても、タグの開始として解釈されないことを意味しています。\fB@\fR文字を使用して行を開始しても、それが解釈されないようにするには、HTMLエンティティ\fB&#064;\fRを使用します。それぞれのブロック・タグには、関連付けられたテキストがあります。このテキストは、タグの後から、次のタグの前、またはドキュメンテーション・コメントの最後までの間に記述されたテキストです(タグまたはコメント区切り文字を除く)。この関連テキストは、複数行にわたって記述できます。インライン・タグは、テキストを記述できる場所であればどこにでも置くことができ、解釈されます。次の例にはブロック・タグ\fB@deprecated\fRとインライン・タグ\fB{@link}\fRが含まれています。javadocタグを参照してください。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * @deprecated  As of JDK 1\&.1, replaced by {@link #setBounds(int,int,int,int)}\fR
\fB */\fR
 
.fi
.if n \{\
.RE
.\}
.PP
HTMLでのコメントの記述
.PP
テキストはHTMLエンティティとHTMLタグを使用してHTMLで記述される必要があります。使用するブラウザがサポートする任意のHTMLのバージョンを使用できます。標準ドックレットは、カスケーディング・スタイル・シートおよびフレームを含め、ドキュメンテーション・コメント以外の部分でHTML 3\&.2に準拠したコードを生成します。フレーム・セットのため、生成されたファイルにはHTML 4\&.0が推奨されます。
.PP
たとえば、より小さい記号(<)およびより大きい記号(>)のエンティティは、\fB&lt;\fRおよび\fB&gt;\fRと記述する必要があります。同様に、アンパサンド(&)は\fB&amp;\fRと記述する必要があります。次の例では、太字のHTMLタグ\fB<b>\fRを使用しています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * This is a <b>doc</b> comment\&.\fR
\fB * @see java\&.lang\&.Object\fR
\fB */\fR
 
.fi
.if n \{\
.RE
.\}
.PP
先頭のアスタリスク
.PP
\fBjavadoc\fRコマンドによるドキュメンテーション・コメントの解析時に、各行の先頭にあるアスタリスク(*)文字は破棄されます。最初のアスタリスク(*)文字より前にある空白やタブも破棄されます。行頭のアスタリスクを省略した場合、インデントを保持したままでサンプル・コードを\fB<PRE>\fRタグ内のドキュメンテーション・コメントに直接貼り付けられるように、先頭の空白文字は削除されなくなります。ブラウザは、空白文字をタブよりも一律に解釈します。インデントの起点は(区切り文字\fB/**\fRまたは\fB<PRE>\fRタグではなく)左マージンになります。
.PP
最初の文
.PP
各ドキュメンテーション・コメントの最初の文は、宣言されているエンティティに関する簡潔かつ完全なサマリー文である必要があります。この文は、空白、タブ、または行終了文字が続く最初のピリオド、または最初のブロック・タグがある位置で終わります。最初の文は、\fBjavadoc\fRコマンドによってHTMLページの先頭にあるメンバーのサマリーの部分にコピーされます。
.PP
複数フィールドの宣言
.PP
Javaプラットフォームでは、1つの文で複数のフィールドを宣言できます。ただし、この文には、1つのドキュメンテーション・コメントしか記述できません。そのコメントが、すべてのフィールドに対してコピーされます。フィールドごとにドキュメンテーション・コメントを記述する必要がある場合は、各フィールドを別々の文で宣言する必要があります。たとえば、次のドキュメンテーション・コメントは、1つの宣言として記述すると不適切です。この場合は、宣言を2つに分けることをお薦めします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/** \fR
\fB * The horizontal and vertical distances of point (x,y)\fR
\fB */\fR
\fBpublic int x, y;      // Avoid this \fR
\fB \fR
.fi
.if n \{\
.RE
.\}
.PP
\fBjavadoc\fRコマンドは、上のコードから次のようなドキュメントを生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpublic int x\fR
 
.fi
.if n \{\
.RE
.\}
.PP
The horizontal and vertical distances of point (x, y)\&.
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpublic int y\fR
 
.fi
.if n \{\
.RE
.\}
.PP
The horizontal and vertical distances of point (x, y)\&.
.PP
ヘッダー・タグの使用
.PP
メンバーに対してドキュメンテーション・コメントを記述するときには、\fB<H1>\fRおよび\fB<H2>\fRなどのHTML見出しタグを使用しないことをお薦めします。\fBjavadoc\fRコマンドは、完全な構造化ドキュメントを作成するので、このような構造化タグが使用されていると、生成ドキュメントの形式が悪影響を受けることがあります。ただし、クラスやパッケージのコメントでは、これらの見出しを使用して独自の構造を指定してかまいません。
.SS "メソッド・コメントの継承"
.PP
\fBjavadoc\fRコマンドでは、クラスおよびインタフェースでメソッド・コメントを継承して、欠落したテキストを入力したり、明示的にメソッド・コメントを継承することができます。コンストラクタ、フィールド、およびネストされたクラスは、ドキュメンテーション・コメントを継承しません。
.PP
\fB注意:\fR
ドキュメンテーション・コメントをコピーに利用するには、継承したメソッドのソース・ファイルが\fB\-sourcepath\fRオプションで指定したパスのみに置かれている必要があります。コマンド行で、クラスもパッケージも渡す必要はありません。この点はリリース1\&.3\&.\fIn\fR以前とは対照的です。これまでは、クラスがドキュメント化されるクラスであることが必要でした。
.PP
欠落テキストの入力
.PP
主説明、または\fB@return\fR、\fB@param\fR、\fB@throws\fRタグがメソッド・コメントから欠落している場合、\fBjavadoc\fRコマンドは、対応する主説明またはタグ・コメントを、それがオーバーライドまたは実装しているメソッド(ある場合)からコピーします。メソッド・コメントの継承を参照してください。
.PP
特定のパラメータの\fB@param\fRタグが見つからない場合、そのパラメータのコメントが、上位の継承階層のメソッドからコピーされます。特定の例外の\fB@throws\fRタグが見つからない場合、その例外が宣言されている場合にかぎり、\fB@throws\fRタグがコピーされます。
.PP
この動作はリリース1\&.3以前の動作とは対照的です。これまでのバージョンでは、主説明またはタグが存在すれば、コメントは一切継承されませんでした。
.PP
javadocタグおよびオプションを参照してください。
.PP
明示的な継承
.PP
\fB{@inheritDoc}\fRインライン・タグをメソッドの主説明または\fB@return\fR、\fB@param\fR、\fB@throws\fRタグ・コメントに挿入します。対応する継承された主説明またはタグ・コメントは、その場所にコピーされます。
.SS "クラスおよびインタフェースの継承"
.PP
コメントの継承は、クラスおよびインタフェースからの継承の、考えられるすべての場合に発生します。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
クラスのメソッドがスーパークラスのメソッドをオーバーライドしている場合
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
インタフェースのメソッドがスーパーインタフェースのメソッドをオーバーライドしている場合
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
クラスのメソッドがインタフェースのメソッドを実装している場合
.RE
.PP
最初の2つのケースでは、\fBjavadoc\fRコマンドは、オーバーライドしているメソッドのドキュメント内に\fI「オーバーライド」\fRという小見出しを生成します。コメントが継承されているかどうかにかかわらず、オーバーライドされているメソッドへのリンクが含まれます。
.PP
3つ目のケース(特定のクラスのメソッドがインタフェースのメソッドを実装している場合)では、\fBjavadoc\fRコマンドは、オーバーライドしているメソッドのドキュメント内に\fI「定義」\fRという小見出しを生成します。コメントが継承されているかどうかにかかわらず、実装されているメソッドへのリンクが含まれます。
.SS "メソッド・コメントのアルゴリズム"
.PP
メソッドにドキュメンテーション・コメントがない、または\fB{@inheritDoc}\fRタグがある場合、\fBjavadoc\fRコマンドは次のアルゴリズムを使用して適用できるコメントを検索します。アルゴリズムは、最も特定される適用可能なドキュメンテーション・コメントを探し、スーパークラスよりもインタフェースを優先するように設計されています。
.sp
.RS 4
.ie n \{\
\h'-04' 1.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  1." 4.2
.\}
直接に実装されている(または、拡張されている)インタフェースを、メソッドの宣言で\fBimplements\fR(または\fBextends\fR)という語の後に出現する順序で、1つずつ調べます。このメソッドについて最初に見つかったドキュメンテーション・コメントを採用します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 2.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  2." 4.2
.\}
手順1でドキュメンテーション・コメントが見つからなかった場合は、直接実装されている(または、拡張されている)インタフェースのそれぞれに対して、このアルゴリズム全体を再帰的に適用します(その際の順序は、手順1でインタフェースを調べたときの順序と同じ)。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 3.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  3." 4.2
.\}
手順2でドキュメンテーション・コメントが見つからなかった場合で、このクラスが\fBObject\fR以外のクラスであるが、インタフェースではない場合は、次のように処理します。
.sp
.RS 4
.ie n \{\
\h'-04' 1.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  1." 4.2
.\}
スーパークラスにこのメソッドについてのドキュメンテーション・コメントが記述されている場合は、そのコメントを採用します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 2.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  2." 4.2
.\}
手順3aでドキュメンテーション・コメントが見つからなかった場合は、スーパークラスに対して、このアルゴリズム全体を再帰的に適用します。
.RE
.RE
.SH "JAVADOCタグ"
.PP
\fBjavadoc\fRコマンドは、Javaのドキュメンテーション・コメント内に埋め込まれた特別なタグを解析します。\fBjavadoc\fRタグを使用すると、完全な整形式のAPIをソース・コードから自動的に生成できます。タグはアットマーク記号(\fB@\fR)で始まり、大文字と小文字が区別されます。これらのタグは、表示されているとおりに大文字と小文字を使用して入力する必要があります。タグは、行の先頭(先頭の空白文字と省略可能なアスタリスクの後)に置く必要があります。そうしないと、テキストとして扱われます。慣例として、同じ名前のタグは1箇所にまとめます。たとえば、\fB@see\fRタグが複数ある場合は、すべて同じ場所にまとめて配置します。詳細は、タグを使用できる場所を参照してください。
.PP
タグには、次のタイプがあります。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
ブロック・タグ: ブロック・タグは主説明に続くタグ・セクション内にのみ配置します。ブロック・タグは、\fI@tag\fRの形式をとります。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
インライン・タグ: インライン・タグは主説明内またはブロック・タグのコメント内の任意の場所に配置します。インライン・タグは\fI{@tag}\fRのように中カッコで囲みます。
.RE
.PP
カスタム・タグについては、\-tag tagname:Xaoptcmf:"taghead"を参照してください。タグを使用できる場所も参照してください。
.SS "タグの説明"
.PP
<AUTHOR>
.RS 4
JDK 1\&.0で導入
.sp
\fB\-author\fRオプションが使用されている場合、指定した名前のテキストの作成者エントリを生成されるドキュメントに追加します。1つのドキュメンテーション・コメントに複数の\fB@author\fRタグを含めることができます。1つの\fB@author\fRタグに1つの名前を指定することも、複数の名前を指定することもできます。前者の場合は、\fBjavadoc\fRコマンドによって名前と名前の間にカンマ(,)と空白文字が挿入されます。後者の場合は、テキスト全体が解析されることなく、生成ドキュメントにコピーされます。したがって、カンマではなく、各言語に対応した名前区切り文字を使用する必要があるときに、1行に複数の名前を指定できます。JavadocツールでのDocコメントの記述方法の@authorに関する項
(http://www\&.oracle\&.com/technetwork/java/javase/documentation/index\-137868\&.html#@author)を参照してください。
.RE
.PP
{@code \fItext\fR}
.RS 4
JDK 1\&.5で導入
.sp
\fB<code>{@literal}</code>\fRと同じです。
.sp
テキストをHTMLマークアップまたはネストされたJavadocタグとして解釈せずに、textをコード・フォントで表示します。これにより、ドキュメンテーション・コメントでは、パラメータの型(\fB<Object>\fR)、不等号(\fB3 < 4\fR)、矢印(\fB<\-\fR)などで、通常の山カッコ(<および>)をHTMLエンティティ(\fB&lt;\fRおよび\fB&gt;\fR)のかわりに使用できます。たとえば、ドキュメンテーション・コメント・テキスト\fB{@code A<B>C}\fRは、\fBA<B>C\fRとして変更されずに生成されたHTMLページに表示されます。つまり、\fB<B>\fRが太字として解釈されず、そのフォントはコード・フォントになります。コード・フォントなしで同じ機能を実現するには、\fB{@literal}\fRタグを使用します。
.RE
.PP
@deprecated \fIdeprecated\-text\fR
.RS 4
JDK 1\&.0で導入
.sp
このAPIは動作し続けますが、このAPIを使用しないことを薦めるコメントを追加します。\fBjavadoc\fRコマンドは、\fBdeprecated\-text\fRを主説明の前に移動してイタリックにし、その前に太字の警告「推奨されていません。」を追加します。このタグは、すべてのドキュメンテーション・コメント、つまり概要、パッケージ、クラス、インタフェース、コンストラクタ、メソッド、およびフィールドで有効です。
.sp
非推奨テキストの最初の文では、そのAPIが推奨されなくなった時期と、代替として使用するAPIをユーザーに提示する必要があります。\fBjavadoc\fRコマンドは、この最初の文を、サマリー・セクションと索引にコピーします。その後の文で非推奨になった理由を説明することもできます。代替APIを指し示す\fB{@link}\fRタグ(Javadoc 1\&.2以降の場合)を含める必要があります。
.sp
\fI@deprecated annotation\fRタグを使用してプログラム要素を非推奨にします。APIを非推奨にする方法と時期
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javadoc/deprecation/deprecation\&.html)を参照してください。
.sp
JavadocツールでのDocコメントの記述方法の@deprecatedに関する項

(http://www\&.oracle\&.com/technetwork/java/javase/documentation/index\-137868\&.html#@deprecated)も参照してください。
.RE
.PP
{@docRoot}
.RS 4
JDK 1\&.3で導入
.sp
生成されるページからの、生成ドキュメントの(生成先)ルート・ディレクトリへの相対パスを表します。このタグは、著作権のページや会社のロゴなど、生成されるすべてのページから参照するファイルを組み込むときに便利です。通常は、各ページの最下部から著作権のページにリンクします。
.sp
この\fB{@docRoot}\fRタグは、コマンド行でもドキュメンテーション・コメント内でも使用できます。このタグは、任意のタグ(\fB@return\fR、\fB@param\fRおよび\fB@deprecated\fRタグなど)のテキスト部分を含む、すべてのドキュメンテーション・コメント、つまり概要、パッケージ、クラス、インタフェース、コンストラクタ、メソッド、およびフィールドで有効です。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
コマンド行で、header、footerまたはbottomが定義されている場合、\fBjavadoc \-bottom \*(Aq<a href="{@docRoot}/copyright\&.html">Copyright</a>\*(Aq\fRとなります。
.sp
\fB{@docRoot}\fRタグをmakefile内でこのように利用する場合、一部の\fBmakefile\fRプログラムでは、中カッコ\fB{}\fR文字を特別にエスケープする必要があります。たとえば、Inprise MAKEバージョン5\&.2をWindows上で実行する場合は、\fB{{@docRoot}}\fRのように、中カッコを二重にする必要があります。\fB\-bottom\fRオプションなどのオプションへの引数を囲むのに、二重(一重ではなく)引用符も必要です(\fBhref\fR引数を囲む引用符は省略)。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
ドキュメンテーション・コメントでは
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * See the <a href="{@docRoot}/copyright\&.html">Copyright</a>\&.\fR
\fB */\fR
 
.fi
.if n \{\
.RE
.\}
このタグが必要な理由は、生成ドキュメントが、サブパッケージと同じ深さを持つ階層構造のディレクトリに格納されるからです。式:
\fB<a href="{@docRoot}/copyright\&.html">\fRは、\fBjava/lang/Object\&.java\fRの場合\fB<a href="\&.\&./\&.\&./copyright\&.html">\fRに、\fBjava/lang/ref/Reference\&.java\fRの場合\fB<a href="\&.\&./\&.\&./\&.\&./copyright\&.html">\fRに解決されます。
.RE
.RE
.PP
@exception \fIclass\-name description\fR
.RS 4
JDK 1\&.0で導入
.sp
\fB@throws\fRタグと同じです。@throws class\-name descriptionを参照してください。
.RE
.PP
{@inheritDoc}
.RS 4
JDK 1\&.4で導入
.sp
最も近い継承可能なクラスまたは実装可能なインタフェースから、このタグの位置にある現在のドキュメンテーション・コメントに、ドキュメントを継承(コピー)します。これにより、より汎用的なコメントを継承ツリーの上位に記述し、コピーしたテキストを使用して記述することができます。
.sp
このタグは、ドキュメンテーション・コメントの次の位置でのみ有効です。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
メソッドの主説明ブロック内。この場合、主説明は、上位階層のクラスまたはインタフェースからコピーされます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
メソッドの\fB@return\fR、\fB@param\fR、\fB@throws\fRタグのテキスト引数内。この場合、タグ・テキストは、上位階層の対応するタグからコピーされます。
.RE
.sp
継承階層でコメントを見つける方法に関する説明は、メソッド・コメントの継承を参照してください。このタグが見つからない場合、コメントは、この項で説明するルールに応じて、自動的に継承されるかどうかが決まります。
.RE
.PP
{@link \fIpackage\&.class#member label\fR}
.RS 4
JDK 1\&.2で導入
.sp
表示テキストlabelとともにインライン・リンクを挿入します。labelは、参照クラスの指定されたパッケージ、クラス、またはメンバーの名前のドキュメントを指し示します。このタグは、\fB@return\fR、\fB@param\fRおよび\fB@deprecated\fRタグなどの任意のタグのテキスト部分を含む、すべてのドキュメンテーション・コメント、つまり概要、パッケージ、クラス、インタフェース、コンストラクタ、メソッド、およびフィールドで有効です。JavadocツールでのDocコメントの記述方法の@linkに関する項
(http://www\&.oracle\&.com/technetwork/java/javase/documentation/index\-137868\&.html#{@link)を参照してください。
.sp
このタグは\fB@see\fRタグに似ています。どちらのタグも、\fBpackage\&.class#member\fRと\fBlabel\fRの参照方法と、有効な構文が同じです。主な違いは、\fB{@link}\fRタグでは、「関連項目」セクションにリンクが配置されるかわりに、インライン・リンクが生成されるという点です。インライン・テキストの他の部分と区別するために、\fB{@link}\fRタグの最初と最後に中カッコを記述します。ラベル内で右中カッコ(\fB}\fR)を使用する必要がある場合、HTMLエンティティ記法\fB&#125;\fRを使用します。
.sp
1つ文の中で使用できる\fB{@link}\fRタグの数に制限はありません。このタグは、ドキュメンテーション・コメントの主説明部分、または\fB@deprecated\fR、\fB@return\fR、\fB@param\fRタグなどの任意のタグのテキスト部分で使用できます。
.sp
たとえば、次のコメントでは\fBgetComponentAt(int,int)\fRメソッドを参照しています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBUse the {@link #getComponentAt(int, int) getComponentAt} method\&.\fR
 
.fi
.if n \{\
.RE
.\}
標準ドックレットでは、このコードから次のHTMLが生成されます(このコメントが同じパッケージの別のクラスを参照している場合)。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBUse the <a href="Component\&.html#getComponentAt(int, int)">getComponentAt</a> method\&.\fR
 
.fi
.if n \{\
.RE
.\}
前の行は、次のようにWebページに表示されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBUse the getComponentAt method\&.\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
{@linkplain \fIpackage\&.class#member label\fR}
.RS 4
JDK 1\&.4で導入
.sp
\fB{@link}\fRタグと同じ動作をしますが、リンク・ラベルがコード・フォントではなくプレーン・テキストで表示される点が異なります。ラベルがプレーン・テキストで記述されていると便利です。たとえば、「\fBRefer to {@linkplain add() the overridden method}\fR\&.」は「Refer to the overridden method」と表示されます。
.RE
.PP
{@literal \fItext\fR}
.RS 4
JDK 1\&.5で導入
.sp
テキストをHTMLマークアップまたはネストされたJavadocタグとして解釈せずに、textを表示します。これにより、ドキュメンテーション・コメントでは、パラメータの型(\fB<Object>\fR)、不等号(\fB3 < 4\fR)、矢印(<\-)などで、山カッコ(\fB<および>\fR)をHTMLエンティティ(\fB&lt;\fRおよび\fB&gt;\fR)のかわりに使用できます。たとえば、ドキュメンテーション・コメント・テキスト\fB{@literal A<B>C}\fRは、\fBA<B>C\fRとしてブラウザの生成されたHTMLページで変更されずに表示されます。\fB<B>\fRは太字として解釈されません(コード・フォントになりません)。コード・フォントで同じ機能を実現するには、\fB{@code}\fRタグを使用します。
.RE
.PP
@param \fIparameter\-name description\fR
.RS 4
JDK 1\&.0で導入
.sp
「パラメータ」セクションに、指定された\fBparameter\-name\fRの後に指定されたdescriptionを続けてパラメータを追加します。ドキュメンテーション・コメントを記述するときには、descriptionを複数行にわたって記述することもできます。このタグは、メソッド、コンストラクタ、またはクラスのドキュメンテーション・コメント内でのみ有効です。JavadocツールでのDocコメントの記述方法の@paramに関する項
(http://www\&.oracle\&.com/technetwork/java/javase/documentation/index\-137868\&.html#@param)を参照してください。
.sp
\fBparameter\-name\fRは、メソッドまたはコンストラクタでのパラメータの名前か、クラス、メソッドまたはコンストラクタの型パラメータの名前になります。山カッコでこのパラメータ名を囲み、型パラメータを使用することを指定します。
.sp
クラスの型パラメータの例:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * @param <E> Type of element stored in a list\fR
\fB */\fR
\fBpublic interface List<E> extends Collection<E> {\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
メソッドの型パラメータの例:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * @param string  the string to be converted\fR
\fB * @param type    the type to convert the string to\fR
\fB * @param <T>     the type of the element\fR
\fB * @param <V>     the value of the element\fR
\fB */\fR
\fB<T, V extends T> V convert(String string, Class<T> type) {\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
@return \fIdescription\fR
.RS 4
JDK 1\&.0で導入
.sp
「戻り値」セクションを追加して、descriptionのテキストを書き込みます。このテキストでは、戻り値の型と、取り得る値の範囲について記述する必要があります。このタグは、メソッドのドキュメンテーション・コメントでのみ有効です。JavadocツールでのDocコメントの記述方法の@returnに関する項
(http://www\&.oracle\&.com/technetwork/java/javase/documentation/index\-137868\&.html#@return)を参照してください。
.RE
.PP
@see \fIreference\fR
.RS 4
JDK 1\&.0で導入
.sp
\fI「関連項目」\fR見出しを追加して、referenceを指すリンク、またはテキスト・エントリを書き込みます。1つのドキュメンテーション・コメントには任意の数の\fB@see\fRタグを含めることができますが、それらはすべて同じ見出しの下にグループ化されます。\fB@see\fRタグには、3つのタイプの形式があります。この形式が最も一般的です。このタグは、すべてのドキュメンテーション・コメント、つまり概要、パッケージ、クラス、インタフェース、コンストラクタ、メソッド、またはフィールドで有効です。パッケージ、クラス、またはメンバーに対するインライン・リンクを文中に挿入する方法は、\fB{@link}\fRを参照してください。
.sp
\fB形式1\fR。@see
\fBstring\fRタグ形式は、\fIstring\fRのテキスト・エントリを追加します。リンクは生成されません。stringは、書籍またはURLではアクセスできない情報の参照先です。\fBjavadoc\fRコマンドは、最初の文字として二重引用符(")を検索して、この形式を前述の形式と区別します。たとえば、\fB@see "The Java Programming Language"\fRは次のテキストを生成します。
.sp
\fB関連項目\fR:
.sp
"The Java Programming Language"
.sp
\fB形式2\fR。\fB@see <a href="URL#value">label</a>\fRフォームは、\fBURL#value\fRで定義されているようにリンクを追加します。\fBURL#value\fRパラメータは、相対URLまたは絶対URLです。\fBjavadoc\fRコマンドは、最初の文字として「より小さい」記号(\fB<\fR)を検索して、この形式を他の形式と区別します。たとえば、\fB@see <a href="spec\&.html#section">Java Spec</a>\fRは、次のリンクを生成します:
.sp
\fB関連項目\fR:
.sp
Java Spec
.sp
\fB形式3\fR。\fB@see package\&.class#member label\fR形式は、表示テキスト・ラベルとともにリンクを追加します。このラベルは参照されているJava言語の指定された名前のドキュメントを指し示します。ラベルはオプションです。ラベルを省略した場合は、表示テキストのかわりに、名前が適切に短縮されて表示されます。\fB\-noqualifier\fRオプションを使用すると、この表示テキストからパッケージ名が全体的に削除されます。ラベルは、自動生成される表示テキストとは異なる表示テキストにする場合に使用します。「名前が表示される方法」を参照してください。
.sp
Java SE 1\&.2だけは、ラベルではなく名前が\fB<code>\fR
HTMLタグ内に自動的に表示されます。Java SE 1\&.2\&.2からは、ラベルを使用するかしないかにかかわらず、\fB<code>\fRタグは常に表示テキストを囲む形で含まれます。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBpackage\&.class#member\fRには、参照されている任意の有効なプログラム要素の名前を指定します。つまり、パッケージ、クラス、インタフェース、コンストラクタ、メソッド、またはフィールドの名前です。ただし、メンバー名の前の文字は、シャープ記号(\fB#\fR)にする必要があります。classは、任意のトップレベルまたはネストされたクラスか、インタフェースを表します。memberは、任意のコンストラクタ、メソッドまたはフィールドを表します(ネストされたクラスまたはインタフェースではありません)。この名前が、ドキュメント化されるクラスに含まれている場合、\fBjavadoc\fRコマンドは、その名前へのリンクを作成します。外部参照クラスへのリンクを作成するには、\fB\-link\fRオプションを使用します。参照クラスに属していない名前のドキュメントを参照するには、他の2つの\fB@see\fRタグ形式のどちらかを使用します。「名前の指定」を参照してください。
.sp
\fB注意:\fR
外部参照クラスは、コマンド行で\fBjavadoc\fRコマンドに渡されないクラスです。生成ドキュメント内で外部参照クラスにリンクしている箇所は、外部参照または外部リンクと呼ばれます。たとえば、\fBjava\&.awt package\fRに対してのみ\fBjavadoc\fRコマンドを実行した場合、\fBObject\fRなどの\fBjava\&.lang\fR内のすべてのクラスが外部参照クラスになります。\fB\-link\fRおよび\fB\-linkoffline\fRオプションを使用して、外部参照クラスへリンクします。外部参照クラスのソース・コメントは\fBjavadoc\fRコマンドの実行には使用できません。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBlabel\fRは、省略可能なテキストで、リンクのラベルとして表示されます。ラベルには空白を含めることができます。\fBlabel\fRを省略すると、\fBpackage\&.class\&.member\fRが、現在のクラスおよびパッケージに応じて適切に短縮されて表示されます。「名前が表示される方法」を参照してください。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
空白文字が、\fBpackage\&.class#member\fRと\fBlabel\fRの間の区切り文字になります。カッコの内側の空白文字はラベルの先頭とは解釈されないため、メソッドのパラメータ間に空白文字を入れてもかまいません。
.RE
.sp
この例では、\fB@see\fRタグ(\fBCharacter\fRクラス内)が、\fBString\fRクラスのequalsメソッドを参照しています。タグには、名前\fBString#equals(Object)\fRとラベル\fBequals\fRの両方の引数が含まれています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * @see String#equals(Object) equals\fR
\fB */\fR
 
.fi
.if n \{\
.RE
.\}
標準ドックレットは、次のようなHTMLを生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB<dl>\fR
\fB<dt><b>See Also:</b>\fR
\fB<dd><a href="\&.\&./\&.\&./java/lang/String#equals(java\&.lang\&.Object)"><code>equals<code></a>\fR
\fB</dl>\fR
 
.fi
.if n \{\
.RE
.\}
前述のコードは、ブラウザに次のように表示され、ラベルは表示リンク・テキストになっています。
.sp
\fB関連項目\fR:
.sp
equals
.RE
.PP
名前の指定
.PP
この\fBpackage\&.class#member\fRという名前は、\fBjava\&.lang\&.String#toUpperCase()\fRのような完全修飾名にすることも、\fBString#toUpperCase()\fRや\fB#toUpperCase()\fRのような非完全修飾名にすることもできます。名前が完全修飾より短い場合は、\fBjavadoc\fRコマンドは、標準のJavaコンパイラの検索順序を使用して探します。「@seeタグの検索順序」を参照してください。名前は、メソッド引数の間など、カッコ内のスペースを含めることができます。部分的に修飾した短い名前を指定することの利点は、入力する文字数が減ることや、ソース・コードが読みやすくなることです。次のリストに様々な形式の名前を示します。ここで、\fBClass\fRにはクラスまたはインタフェースを、Typeにはクラス、インタフェース、配列、またはプリミティブを、methodにはメソッドまたはコンストラクタを、それぞれ指定できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\fBTypical forms for\fR\fR\fB\fB @see package\&.class#member\fR\fR\fB \fR
\fB\fBReferencing a member of the current class\fR\fR
\fB@see #field\fR
\fB@see #method(Type, Type,\&.\&.\&.)\fR
\fB@see #method(Type argname, Type argname,\&.\&.\&.)\fR
\fB@see #constructor(Type, Type,\&.\&.\&.)\fR
\fB@see #constructor(Type argname, Type argname,\&.\&.\&.) \fR
 
\fB\fBReferencing another class in the current or imported packages\fR\fR
\fB@see Class#field\fR
\fB@see Class#method(Type, Type,\&.\&.\&.)\fR
\fB@see Class#method(Type argname, Type argname,\&.\&.\&.)\fR
\fB@see Class#constructor(Type, Type,\&.\&.\&.)\fR
\fB@see Class#constructor(Type argname, Type argname,\&.\&.\&.)\fR
\fB@see Class\&.NestedClass\fR
\fB@see Class \fR
 
\fB\fBReferencing an element in another package (fully qualified)\fR\fR
\fB@see package\&.Class#field\fR
\fB@see package\&.Class#method(Type, Type,\&.\&.\&.)\fR
\fB@see package\&.Class#method(Type argname, Type argname,\&.\&.\&.)\fR
\fB@see package\&.Class#constructor(Type, Type,\&.\&.\&.)\fR
\fB@see package\&.Class#constructor(Type argname, Type argname,\&.\&.\&.)\fR
\fB@see package\&.Class\&.NestedClass\fR
\fB@see package\&.Class\fR
\fB@see package\fR
 
.fi
.if n \{\
.RE
.\}
.PP
前のリストに関するメモ:
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
最初のタイプの形式(パッケージとクラスを省略)の場合、\fBjavadoc\fRコマンドは、現在のクラスの階層のみを検索します。つまり、現在のクラスかインタフェース、そのスーパークラスかスーパーインタフェース、またはその外側を囲んでいるクラスかインタフェースからメンバーを検索します(検索項目1\(en3)。現在のパッケージの他の部分や、他のパッケージは検索しません(検索項目4\(en5)。「@seeタグの検索順序」を参照してください。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
メソッドまたはコンストラクタの入力時に、\fBgetValue\fRのようにカッコなしの名前を使用した場合、同じ名前のフィールドが存在していなければ、\fBjavadoc\fRコマンドはそのメソッドへのリンクを作成します。このメソッドがオーバーロードされている場合、\fBjavadoc\fRコマンドは、検索で最初に見つかったメソッドにリンクします。結果は前もって特定できません。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
ネストされたクラスは、すべての形式について、\fBouter\&.inner\fRとして指定する必要があります。単純に\fBinner\fRとはしないでください。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
すでに述べたように、クラスとメンバーとの間の区切り文字としては、ドット(\fB\&.\fR)ではなくシャープ記号(\fB#\fR)を使用します。このように指定すると、\fBjavadoc\fRコマンドは、あいまいさを解決できます。ドットは、クラス、ネストされたクラス、パッケージ、およびサブパッケージを区切るためにも使用されるからです。ただし、\fBjavadoc\fRコマンドでは、あいまいさがなければドットは正しく解析されますが、警告は表示されます。
.RE
.PP
@seeタグの検索順序
.PP
\fBjavadoc\fRコマンドは、ソース・ファイル、パッケージ・ファイル、概要ファイルに表示される\fB@see\fRタグを処理します。後者の2つのファイルでは、完全修飾の名前を\fB@see\fRタグに指定する必要があります。ソース・ファイルでは、完全修飾の名前、または部分修飾の名前を指定できます。
.PP
次に、\fB@see\fRタグの検索順序を示します。
.sp
.RS 4
.ie n \{\
\h'-04' 1.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  1." 4.2
.\}
現在のクラスまたはインタフェース。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 2.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  2." 4.2
.\}
外側を囲んでいるクラスとインタフェース(最も近いものから検索)。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 3.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  3." 4.2
.\}
スーパークラスとスーパーインタフェース(最も近いものから検索)。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 4.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  4." 4.2
.\}
現在のパッケージ。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 5.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  5." 4.2
.\}
インポートされているパッケージ、クラス、およびインタフェース(\fBimport\fR文の順序に従って検索)。
.RE
.PP
\fBjavadoc\fRコマンドは、各クラスについて項目1\-3を再帰的に適用しながら、一致する名前が見つかるまで検索を続けます。つまり、まず現在のクラスを検索し、次にその外側を囲んでいるクラスEを検索した後、Eのスーパークラスを検索してから、Eを囲んでいるクラスを検索します。項目4と5では、\fBjavadoc\fRコマンドが1つのパッケージ内のクラスまたはインタフェースを検索する順序は決まっていません(その順序は、個々のコンパイラによって異なります)。項目5では、\fBjavadoc\fRコマンドは、\fIjava\&.lang\fRを検索します。このパッケージは、すべてのプログラムに自動的にインポートされるからです。
.PP
\fBjavadoc\fRコマンドは、完全修飾でないソース・ファイルで\fB@see\fRタグを見つけると、Javaコンパイラと同じ順序で指定された名前を検索します(ただし、\fBjavadoc\fRコマンドは、特定の名前空間のあいまいさを検出しません。これは、ソース・コードにこれらのエラーが存在していないことを前提としているためです)。この検索順序は、Java言語仕様で正式に定義されています。\fBjavadoc\fRコマンドは、関連するクラスとパッケージ、およびインポートされたクラスとパッケージのすべてからその名前を検索します。具体的には、次の順序で検索します。
.sp
.RS 4
.ie n \{\
\h'-04' 1.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  1." 4.2
.\}
現在のクラスまたはインタフェース。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 2.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  2." 4.2
.\}
外側を囲んでいるクラスとインタフェース(最も近いものから検索)。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 3.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  3." 4.2
.\}
スーパークラスとスーパーインタフェース(最も近いものから検索)。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 4.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  4." 4.2
.\}
現在のパッケージ。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 5.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  5." 4.2
.\}
インポートされているパッケージ、クラス、およびインタフェース(\fBimport\fR文の順序に従って検索)。
.RE
.PP
\fBjavadoc\fRコマンドは、必ずしもサブクラスを検索するとは限りません。また、実行中に他のパッケージのドキュメントが生成される場合でも、他のパッケージを検索しません。たとえば、\fB@see\fRタグが\fBjava\&.awt\&.event\&.KeyEvent\fRクラス内に含まれていて、\fBjava\&.awt package\fR内のある名前を参照していても、そのクラスがインポートしないかぎり\fBjavadoc\fRコマンドはそのパッケージを検索しません。
.PP
名前が表示される方法
.PP
\fBlabel\fRを省略すると、\fBpackage\&.class\&.member\fRが表示されます。一般に、これは現在のクラスおよびパッケージに応じて適切に短縮されます。短縮されるとは、\fBjavadoc\fRコマンドにより必要最小限の名前のみが表示されるということです。たとえば、\fBString\&.toUpperCase()\fRメソッドに、同じクラスのメンバーへの参照と他のクラスのメンバーへの参照が含まれている場合、クラス名が表示されるのは後者のケースのみです(次のリストを参照)。パッケージ名を全体的に削除するには、\fB\-noqualifier\fRオプションを使用します。
.RS 4
\fB参照のタイプ\fR: \fB@see\fRタグは同じクラス、同じパッケージのメンバーを参照します
.RE
.RS 4
\fB例\fR: \fB@see String#toLowerCase()\fR
.RE
.RS 4
\fB表示\fR: \fBtoLowerCase()\fR \- パッケージおよびクラス名を省略します
.RE
.RS 4
.RE
.RS 4
\fB参照のタイプ\fR: \fB@see\fRタグは別のクラス、同じパッケージのメンバーを参照します
.RE
.RS 4
\fB例\fR: \fB@see Character#toLowerCase(char)\fR
.RE
.RS 4
\fB表示\fR: \fBCharacter\&.toLowerCase(char)\fR \- パッケージ名を省略し、クラス名を含みます
.RE
.RS 4
.RE
.RS 4
\fB参照のタイプ\fR: \fB@see\fRタグは異なるクラス、異なるパッケージのメンバーを参照します
.RE
.RS 4
\fB例\fR: \fB@see java\&.io\&.File#exists()\fR
.RE
.RS 4
\fB表示\fR: \fBjava\&.io\&.File\&.exists()\fR \- パッケージおよびクラス名を含みます
.RE
.RS 4
.RE
.PP
@seeタグの例
.PP
右側のコメントは、\fB@see\fRタグが\fBjava\&.applet\&.Applet\fRなどの別のパッケージのクラス内にある場合に、名前がどのように表示されるかを示しています。JavadocツールでのDocコメントの記述方法の@seeに関する項
(http://www\&.oracle\&.com/technetwork/java/javase/documentation/index\-137868\&.html#@see)を参照してください。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB                                            See also:\fR
\fB@see java\&.lang\&.String                   //  String                           \fR
\fB@see java\&.lang\&.String The String class  //  The String class                 \fR
\fB@see String                             //  String                           \fR
\fB@see String#equals(Object)              //  String\&.equals(Object)            \fR
\fB@see String#equals                      //  String\&.equals(java\&.lang\&.Object)   \fR
\fB@see java\&.lang\&.Object#wait(long)        //  java\&.lang\&.Object\&.wait(long)      \fR
\fB@see Character#MAX_RADIX                //  Character\&.MAX_RADIX              \fR
\fB@see <a href="spec\&.html">Java Spec</a>  //  Java Spec            \fR
\fB@see "The Java Programming Language"    //  "The Java Programming Language" \fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fB注意:\fR
\fB@se\fR\fBe\fRタグを拡張してドキュメント化されないクラスにリンクするには、\fB\-link\fRオプションを使用します。
.PP
@serial \fIfield\-description\fR | include | exclude
.RS 4
JDK 1\&.2で導入
.sp
デフォルトの直列化可能フィールドのドキュメンテーション・コメントで使用します。クラスの直列化可能なフィールドおよびデータの文書化
(http://docs\&.oracle\&.com/javase/8/docs/platform/serialization/spec/serial\-arch\&.html#5251)を参照してください
.sp
Oracleの直列化された形式の仕様にクラスを含める基準
(http://www\&.oracle\&.com/technetwork/java/javase/documentation/serialized\-criteria\-137781\&.html)も参照してください
.sp
\fBfield\-description\fR(省略可能)では、フィールドの意味を説明し、取り得る値のリストを示す必要があります。必要な場合は、複数の行に渡って説明を記述できます。標準ドックレットは、この情報を、直列化された形式ページに追加します。相互参照ページを参照してください。
.sp
クラスを直列化した後に直列化可能フィールドをクラスに追加した場合、主説明に、追加したバージョンを識別する文を追加する必要があります。
.sp
\fBinclude\fRおよび\fBexclude\fR引数は、直列化された形式ページにクラスまたはパッケージを含めるか除外するかを示します。次のように機能します。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBSerializable\fRを実装しているpublicまたはprotectedクラスは、そのクラス(またはそのクラスが属するパッケージ)が\fB@serial exclude\fRタグでマークされていないかぎり、含められます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBSerializable\fRを実装しているprivateまたはpackage\-privateクラスは、そのクラス(またはそのクラスが属するパッケージ)が\fB@serial include\fRタグでマークされていないかぎり、除外されます。
.RE
.sp
たとえば、\fBjavax\&.swing\fRパッケージはpackage\&.htmlまたはpackage\-info\&.java内で\fB@serial\fR
\fBexclude\fRタグでマークされています。publicクラス\fBjava\&.security\&.BasicPermission\fRは\fB@serial exclude\fRタグでマークされています。package\-privateクラス\fBjava\&.util\&.PropertyPermissionCollection\fRは\fB@serial include\fRタグでマークされています。
.sp
クラス・レベルの\fB@serial\fRタグはパッケージ・レベルの\fB@serial\fRタグをオーバーライドします。
.RE
.PP
@serialData \fIdata\-description\fR
.RS 4
JDK 1\&.2で導入
.sp
データの説明値を使用して、直列化された形式でのデータの型と順序をドキュメント化します。このデータには、\fBwriteObject\fRメソッドによって書き込まれる省略可能なデータ、および\fBExternalizable\&.writeExternal\fRメソッドによって書き込まれるすべてのデータ(ベース・クラスを含む)が含まれます。
.sp
\fB@serialData\fRタグは、\fBwriteObject\fR、\fBreadObject\fR、\fBwriteExternal\fR、\fBreadExternal\fR、\fBwriteReplace\fRおよび\fBreadResolve\fRメソッドのドキュメンテーション・コメントで使用できます。
.RE
.PP
@serialField \fIfield\-name\fR \fIfield\-type\fR \fIfield\-description\fR
.RS 4
JDK 1\&.2で導入
.sp
\fBSerializable\fRクラスの\fBserialPersistentFields\fRメンバーの\fBObjectStreamField\fRコンポーネントをドキュメント化します。\fBObjectStreamField\fRコンポーネントごとに1つの\fB@serialField\fRタグを使用します。
.RE
.PP
@since \fIsince\-text\fR
.RS 4
JDK 1\&.1で導入
.sp
生成ドキュメントに、指定された\fBsince\-text\fRの値の\fI「導入されたバージョン」\fR見出しを追加します。このテキストには、特別な内部構造はありません。このタグは、すべてのドキュメンテーション・コメント、つまり概要、パッケージ、クラス、インタフェース、コンストラクタ、メソッド、またはフィールドで有効です。このタグは、特定の変更または機能が、\fBsince\-text\fRの値によって指定されたソフトウェア・リリース以降、存在していることを意味します。たとえば、\fB@since 1\&.5\fRです。
.sp
Javaプラットフォームのソース・コードの場合、\fB@since\fRタグは、JavaプラットフォームAPI仕様のバージョンを示します。ソース・コードがリファレンス実装に追加された時期を示すとは限りません。複数の\fB@since\fRタグを使用でき、複数の\fB@author\fRタグのように扱われます。プログラム要素が複数のAPIで使用される場合、複数のタグを使用できます。
.RE
.PP
@throws \fIclass\-name\fR \fIdescription\fR
.RS 4
JDK 1\&.2で導入
.sp
\fB@exception\fRタグと同じ動作をします。JavadocツールでのDocコメントの記述方法の@throwsに関する項
(http://www\&.oracle\&.com/technetwork/java/javase/documentation/index\-137868\&.html#@exception)を参照してください
.sp
\fB@throws\fRタグは、生成ドキュメントに\fIThrows\fR小見出しを追加して、\fBclass\-name\fRおよび\fBdescription\fRテキストを書き込みます。\fIclass\-name\fRは、そのメソッドからスローされる可能性のある例外の名前です。このタグは、メソッド、コンストラクタのドキュメンテーション・コメント内でのみ有効です。このクラスが完全指定の名前で記述されていない場合、\fBjavadoc\fRコマンドは、検索順序に従ってクラスを探します。複数の\fB@throws\fRタグを、同じ例外または違う例外の指定したドキュメンテーション・コメントで使用できます。「@seeタグの検索順序」を参照してください。
.sp
すべてのチェック済例外がドキュメント化されるようにするために、\fB@throws\fRタグがthrows節内の例外用に存在しない場合は、\fB@throws\fRタグでドキュメント化されたかのように、\fBjavadoc\fRコマンドによって例外がHTML出力に説明なしで追加されます。
.sp
オーバーライドされるメソッド内で例外が明示的に宣言されている場合のみ、\fB@throws\fRのドキュメントがそのメソッドからサブクラスにコピーされます。インタフェース・メソッドから実装メソッドにコピーされる場合も同様です。\fB{@inheritDoc}\fRタグを使用して、\fB@throws\fRタグがドキュメンテーションを継承するように強制できます。
.RE
.PP
{@value \fIpackage\&.class#field\fR}
.RS 4
JDK 1\&.4で導入
.sp
定数の値を表示します。\fB{@value}\fRタグが静的フィールドのドキュメンテーション・コメントで引数なしで使用されている場合、その定数の値を表示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * The value of this constant is {@value}\&.\fR
\fB */\fR
\fBpublic static final String SCRIPT_START = "<script>"\fR
 
.fi
.if n \{\
.RE
.\}
任意のドキュメンテーション・コメント内で引数\fBpackage\&.class#field\fRありで使用された場合、\fB{@value}\fRタグは指定された定数の値を表示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * Evaluates the script starting with {@value #SCRIPT_START}\&.\fR
\fB */\fR
\fBpublic String evalScript(String script) {}\fR
 
.fi
.if n \{\
.RE
.\}
引数\fBpackage\&.class#field\fRは、\fB@see\fRタグ引数と同一の形式になります。ただし、メンバーは静的フィールドである必要があります。
.sp
これらの定数の値は「定数フィールド値」
(http://docs\&.oracle\&.com/javase/8/docs/api/constant\-values\&.html)にも表示されます
.RE
.PP
@version \fIversion\-text\fR
.RS 4
JDK 1\&.0で導入
.sp
\fB\-version\fRオプションが使用されている場合、生成ドキュメントに\fI「バージョン」\fR小見出しを追加して、指定された\fBversion\-text\fRの値を書き込みます。このタグはこのコードが含まれるソフトウェアの現在のリリース番号を保持するためのものであるのに対し、\fB@since\fRタグは、このコードが導入されたリリース番号を保持します。\fBversion\-text\fRの値には、特別な内部構造はありません。JavadocツールでのDocコメントの記述方法の@versionに関する項
(http://www\&.oracle\&.com/technetwork/java/javase/documentation/index\-137868\&.html#@version)を参照してください
.sp
1つのドキュメンテーション・コメントに複数の\fB@version\fRタグを含めることができます。必要に応じて、1つの\fB@version\fRタグに1つのリリース番号を指定することも、複数のリリース番号を指定することもできます。前者の場合は、\fBjavadoc\fRコマンドによって名前と名前の間にカンマ(,)と空白文字が挿入されます。後者の場合は、テキスト全体が解析されることなく、生成ドキュメントにコピーされます。したがって、カンマではなく、各言語に対応した名前区切り文字を使用する必要があるときに、1行に複数の名前を指定できます。
.RE
.SH "タグを使用できる場所"
.PP
ここでは、タグを使用できる場所について説明します。次のタグがすべてのドキュメンテーション・コメントで使用できます。\fB@see\fR、\fB@since\fR、\fB@deprecated\fR、\fB{@link}\fR、\fB{@linkplain}\fRおよび\fB{@docroot}\fR。
.SS "概要タグ"
.PP
概要タグは、概要ページのドキュメンテーション・コメントで使用できるタグです(このドキュメンテーション・コメントは、通常overview\&.htmlという名前のソース・ファイル内にあります)。他のドキュメンテーション・コメントの場合と同様に、これらのタグは、主説明の後で使用する必要があります。
.PP
\fB注意:\fR
Java SE 1\&.2では、概要ドキュメント内の\fB{@link}\fRタグにbugがあります。テキストは正しく表示されますが、リンクが設定されません。現在のところ、\fB{@docRoot}\fRタグは、概要ドキュメント内では機能しません。
.PP
概要タグは、次のとおりです。
.PP
@see reference || @since since\-text || @serialField field\-name field\-type field\-description || <AUTHOR> || @version version\-text || {@link package\&.class#member label} || {@linkplain package\&.class#member label} || {@docRoot} ||
.SS "パッケージ・タグ"
.PP
パッケージ・タグは、パッケージのドキュメンテーション・コメントで使用できるタグで、ドキュメンテーション・コメントはpackage\&.htmlまたはpackage\-info\&.javaという名前のソース・ファイル内にあります。ここで使用できる\fB@serial\fRタグは、\fBinclude\fRまたは\fBexclude\fR引数を指定したもののみです。
.PP
パッケージ・タグは、次のとおりです。
.PP
@see reference || @since since\-text || @serial field\-description | include | exclude || <AUTHOR> || @version version\-text || {@linkplain package\&.class#member label} || {@linkplain package\&.class#member label} || {@docRoot} ||
.SS "クラスおよびインタフェース・タグ"
.PP
次に、クラスまたはインタフェースのドキュメンテーション・コメントで使用できるタグを示します。\fB@serial\fRタグは、\fBinclude\fRまたは\fBexclude\fR引数を指定して、クラスまたはインタフェースのドキュメンテーション内でのみ使用できます。
.PP
@see reference || @since since\-text || @deprecated deprecated\-text || @serial field\-description | include | exclude || <AUTHOR> || @version version\-text || {@link package\&.class#member label} || {@linkplain package\&.class#member label} || {@docRoot} ||
.PP
クラス・コメントの例:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * A class representing a window on the screen\&.\fR
\fB * For example:\fR
\fB * <pre>\fR
\fB *    Window win = new Window(parent);\fR
\fB *    win\&.show();\fR
\fB * </pre>\fR
\fB *\fR
\fB * <AUTHOR> Shaio\fR
\fB * @version 1\&.13, 06/08/06\fR
\fB * @see     java\&.awt\&.BaseWindow\fR
\fB * @see     java\&.awt\&.Button\fR
\fB */\fR
\fBclass Window extends BaseWindow {\fR
\fB   \&.\&.\&.\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
.SS "フィールド・タグ"
.PP
これらのタグは、フィールドに表示できます。
.PP
@see reference || @since since\-text || @deprecated deprecated\-text || @serial field\-description | include | exclude || @serialField field\-name field\-type field\-description || {@link package\&.class#member label} || {@linkplain package\&.class#member label} || {@docRoot} || {@value package\&.class#field}
.PP
フィールド・コメントの例:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB    /**\fR
\fB     * The X\-coordinate of the component\&.\fR
\fB     *\fR
\fB     * @see #getLocation()\fR
\fB     */\fR
\fB    int x = 1263732;\fR
 
.fi
.if n \{\
.RE
.\}
.SS "コンストラクタとメソッド・タグ"
.PP
次に、コンストラクタまたはメソッドのドキュメンテーション・コメントで使用できるタグを示します。ただし、\fB@return\fRはコンストラクタでは使用できず、
\fB{@inheritDoc}\fRには制限があります。
.PP
@see reference || @since since\-text || @deprecated deprecated\-text || @param parameter\-name description || @return description || @throws class\-name description || @exception class\-name description || @serialData data\-description || {@link package\&.class#member label} || {@linkplain package\&.class#member label} || {@inheritDoc} || {@docRoot}
.PP
\fB注意:\fR
\fB@serialData\fRタグは、\fBwriteObject\fR、\fBreadObject\fR、\fBwriteExternal\fR、\fBreadExternal\fR、\fBwriteReplace\fRおよび\fBreadResolve\fRメソッドのドキュメンテーション・コメントでのみ使用できます。
.PP
メソッド・コメントの例:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB     * Returns the character at the specified index\&. An index \fR
\fB     * ranges from <code>0</code> to <code>length() \- 1</code>\fR
\fB     *\fR
\fB     * @param     index the index of the desired character\&.\fR
\fB     * @return    the desired character\&.\fR
\fB     * @exception StringIndexOutOfRangeException \fR
\fB     *              if the index is not in the range <code>0</code> \fR
\fB     *              to <code>length()\-1</code>\fR
\fB     * @see       java\&.lang\&.Character#charValue()\fR
\fB     */\fR
\fB    public char charAt(int index) {\fR
\fB       \&.\&.\&.\fR
\fB    }\fR
 
.fi
.if n \{\
.RE
.\}
.SH "オプション"
.PP
\fBjavadoc\fRコマンドは、ドックレットを使用して出力を決定します。\fBjavadoc\fRコマンドは、\fB\-doclet\fRオプションでカスタム・ドックレットが指定されている場合以外は、デフォルトの標準ドックレットを使用します。\fBjavadoc\fRコマンドには、任意のドックレットとともに使用できるコマンド行オプションがあります。これらのオプションについては、Javadocオプションで説明します。標準ドックレットでは、この他に、いくつかの追加のコマンド行オプションが提供されます。これらのオプションについては、標準ドックレットのオプションで説明します。どのオプション名も、大文字と小文字が区別されません。ただし、オプションの引数では、大文字と小文字が区別されます。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Javadocオプションも参照してください
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
標準ドックレットのオプションも参照してください
.RE
.PP
オプションは次のとおりです。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-1\&.1
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-author
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-bootclasspath classpathlist
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-bottom text
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-breakiterator
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-charset name
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-classpath classpathlist
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-d directory
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-docencoding name
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-docfilesubdirs
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-doclet class
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-docletpath classpathlist
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-doctitle title
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-encoding
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-exclude packagename1:packagename2:\&.\&.\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-excludedocfilessubdir name1:name2
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-extdirs dirist
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-footer footer
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-group groupheading packagepattern:packagepattern
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-header header
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-help
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-helpfile path\efilename
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-Jflag
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-javafx
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-keywords
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-link extdocURL
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-linkoffline extdocURL packagelistLoc
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-linksource
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-locale language_country_variant
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-nocomment
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-nodeprecated
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-nodeprecatedlist
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-nohelp
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-noindex
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-nonavbar
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-noqualifier all | packagename1:packagename2\&.\&.\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-nosince
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-notimestamp
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-notree
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-overview path/filename
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-package
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-private
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-protected
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-public
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-quiet
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-serialwarn
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-source release
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-sourcepath sourcepathlist
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-sourcetab tablength
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-splitindex
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-stylesheet path/filename
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-tag tagname:Xaoptcmf:"taghead"
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-subpackages package1:package2:\&.\&.\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-taglet class
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-tagletpath tagletpathlist
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-title title
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-top
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-use
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-verbose
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-version
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\-windowtitle title
.RE
.PP
次のオプションは、すべてのドックレットに使用可能なコアのJavadocオプションです。標準ドックレットでは、ドックレットの他の部分を提供します。\fB\-bootclasspath\fR、\fB\-breakiterator\fR、\fB\-classpath\fR、\fB\-doclet\fR、\fB\-docletpath\fR、\fB\-encoding\fR、\-\fBexclude\fR、\fB\-extdirs\fR、\fB\-help\fR、\fB\-locale\fR、\fB\-\fR\fBoverview\fR、\fB\-package\fR、\fB\-private\fR、\fB\-protected\fR、\fB\-public\fR、\fB\-quiet\fR、\fB\-source\fR、\fB\-sourcepath\fR、\fB\-subpackages\fRおよび\fB\-verbose\fR。
.SS "Javadocオプション"
.PP
\-overview \fIpath/filename \fR
.RS 4

\fBjavadoc\fRコマンドに対して、\fIpath/filename \fRで指定されたソース・ファイルから概要ドキュメント用のテキストを取得し、そのテキストを概要ページ(overview\-summary\&.html)に配置するように指定します。\fIpath/filename\fRは、現在のディレクトリからの相対パスです。
.sp
\fBfilename\fRの値で任意の名前を使用し、pathで任意の配置先を指定できますが、通常はoverview\&.htmlという名前を付け、ソース・ツリー内の最上位パッケージ・ディレクトリを含むディレクトリに配置します。この場所に配置すると、パッケージをドキュメント化するときにpathを指定する必要がなくなります。これは、\fB\-sourcepath\fRオプションによってこのファイルが指し示されるからです。
.sp
たとえば、\fBjava\&.lang\fRパッケージのソース・ツリーが/src/classes/java/lang/の場合、概要ファイルを/src/classes/overview\&.htmlに配置できます
.sp
実際の例を参照してください。
.sp
\fIpath/filename\fRで指定するファイルについては、概要コメント・ファイルを参照してください。
.sp
「概要」ページが作成されるのは、\fBjavadoc\fRコマンドに複数のパッケージ名を渡した場合のみです。詳細は、HTMLフレームを参照してください。「概要」ページのタイトルは、\fB\-doctitle\fRによって設定されます。
.RE
.PP
\-Xdoclint:(all|none|[\-]\fI<group>\fR)
.RS 4
不正な参照、アクセシビリティの欠落およびJavadocコメントの不足の警告をレポートし、無効なJavadoc構文および不足しているHTMLタグのエラーをレポートします。
.sp
このオプションにより、\fBjavadoc\fRコマンドは生成された出力に含まれるすべてのドキュメント・コメントをチェックします。通常どおり、標準オプション\fB\-public\fR、\fB\-protected\fR、\fB\-package\fRおよび\fB\-private\fRで生成された出力に含む項目を選択できます。
.sp
\fB\-Xdoclint\fRが有効になっている場合は、\fBjavac\fRコマンドと同様にメッセージで問題がレポートされます。\fBjavadoc\fRコマンドは、メッセージ、ソース・ファイルのコピーおよびエラーが検出された正確な位置を指すキャレットを出力します。メッセージは、重大度、および生成されたドキュメントがバリデータを使用して実行された場合にエラーが発生する可能性に応じて、警告またはエラーになります。たとえば、不正な参照またはJavadocコメントの欠落は、\fBjavadoc\fRコマンドが無効なHTMLを生成する原因にならないため、これらの問題は警告としてレポートされます。構文エラーまたはHTML終了タグの欠落は、\fBjavadoc\fRコマンドが無効なHTMLを生成する原因になるため、これらの問題はエラーとしてレポートされます。
.sp
デフォルトでは、\fB\-Xdoclint\fRオプションは有効になっています。オプション\fB\-Xdoclint:none\fRで無効にします。
.sp
\fB\-Xdoclint\fRオプションでレポートされる内容は次のオプションで変更します。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-Xdoclint none\fR:
\fB\-Xdoclint\fRオプションを無効にします。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-Xdoclint \fR\fIgroup\fR:
\fIgroup\fRチェックを有効にします。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-Xdoclint all\fR: すべてのチェック・グループを有効にします。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-Xdoclint all,\fR\fI\-group\fR:
\fIgroup\fRチェック以外のすべてを有効にします。
.RE
.sp
変数\fIgroup\fRは次のいずれかの値を持ちます。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBaccessibility\fR: アクセシビリティ・チェッカで検出する問題をチェックします(たとえば、\fB<table>\fRタグで指定されるno captionまたはsummary属性)。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBhtml\fR: インライン要素へのブロック要素の挿入や終了タグを必要とする要素を終了しないなど、上位レベルHTMLの問題を検出します。ルールは、HTML 4\&.01仕様から導出されます。このタイプのチェックは、\fBjavadoc\fRコマンドを有効にして、ブラウザが受け入れる可能性のあるHTMLの問題を検出します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBmissing\fR: 欠落しているJavadocコメントまたはタグをチェックします(たとえば、欠落しているコメントやクラス、または欠落している\fB@return\fRタグやメソッド上の同様のタグ)。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBreference\fR: JavadocタグのJava API要素の参照に関連する問題をチェックします(たとえば、\fB@see\fRで見つからない項目、または\fB@param\fRの後の不正な名前)。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBsyntax\fR: エスケープされていない山カッコ(\fB<\fRおよび\fB>\fR)やアンパサンド(\fB&\fR)、無効なJavadocタグなどの下位レベルの問題を確認します。
.RE
.sp
\fB\-Xdoclint\fRオプションを複数回指定して、複数のカテゴリのエラーと警告をチェックするオプションを有効にできます。または、前のオプションを使用して、複数のエラーおよび警告カテゴリを指定できます。たとえば、次のコマンドのいずれかを使用して、\fIfilename\fRファイル内のHTML、構文およびアクセシビリティの問題をチェックします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-Xdoclint:html \-Xdoclint:syntax \-Xdoclint:accessibility \fR\fB\fIfilename\fR\fR
\fBjavadoc \-Xdoclint:html,syntax,accessibility \fR\fB\fIfilename\fR\fR
 
.fi
.if n \{\
.RE
.\}
\fB注意:\fR
\fBjavadoc\fRコマンドでは、これらのチェックの完全性は保証されません。具体的には、完全なHTMLコンプライアンス・チェッカではありません。\-\fBXdoclint\fRオプションの目的は、\fBjavadoc\fRコマンドを有効にして一般的なエラーの大半をレポートすることです。
.sp
\fBjavadoc\fRコマンドは、無効な入力の修正を試行せず、レポートのみ行います。
.RE
.PP
\-public
.RS 4
publicクラスおよびメンバーのみ表示します。
.RE
.PP
\-protected
.RS 4
protectedおよびpublicのクラスとメンバーのみを表示します。これがデフォルトです。
.RE
.PP
\-package
.RS 4
package、protected、およびpublicのクラスとメンバーのみ表示します。
.RE
.PP
\-private
.RS 4
すべてのクラスとメンバーを表示します。
.RE
.PP
\-help
.RS 4
オンライン・ヘルプを表示します。\fBjavadoc\fRと\fBドックレット\fRのコマンド行オプションがリストされます。
.RE
.PP
\-doclet \fIclass\fR
.RS 4
ドキュメントの生成に使用するドックレットを起動するためのクラス・ファイルを指定します。完全修飾名を使用します。このドックレットにより、出力の内容と形式が定義されます。\fB\-doclet\fRオプションが使用されていない場合、\fBjavadoc\fRコマンドは、標準ドックレットを使用してデフォルトのHTML形式を生成します。このクラスには\fBstart(Root)\fRメソッドが含まれている必要があります。この起動クラスへのパスは\fB\-docletpath\fRオプションによって定義されます。ドックレットの概要
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javadoc/doclet/overview\&.html)を参照してください
.RE
.PP
\-docletpath \fIclasspathlist\fR
.RS 4
\fB\-doclet\fRオプションで指定されたドックレット開始クラス・ファイル、およびそのクラスが依存するすべてのJARファイルへのパスを指定します。開始クラス・ファイルがJARファイル内にある場合、このオプションでJARファイルへのパスを指定します。絶対パスまたは現在のディレクトリからの相対パスを指定できます。\fBclasspathlist\fRに複数のパスやJARファイルが含まれる場合には、それらをSolarisの場合はコロン(:)で、Windowsの場合はセミコロン(;)でそれぞれ区切ります。目的のドックレット開始クラスがすでに検索パス内にある場合は、このオプションは不要です。ドックレットの概要
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javadoc/doclet/overview\&.html)を参照してください
.RE
.PP
\-1\&.1
.RS 4
Javadoc 1\&.4から削除され、代替はありません。このオプションは、Javadoc 1\&.1によって生成されるのと同じ外見と機能を持つドキュメントを作成するためのものでした(ネストされたクラスはサポートされていません)。このオプションが必要な場合は、Javadoc 1\&.2または1\&.3をかわりに使用してください。
.RE
.PP
\-source \fIrelease\fR
.RS 4
受け付けるソース・コードのリリースを指定します。\fBrelease\fRパラメータには次の値を指定できます。\fBjavac\fRコマンドでコードをコンパイルするときに使用する値に対応する\fBリリース\fRの値を使用します。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBリリース値: 1\&.5\fR。\fBjavadoc\fRコマンドは、JDK 1\&.5で導入された総称および他の言語機能を含むコードを受け付けます。\fB\-source\fRオプションが使用されなかった場合のコンパイラのデフォルト動作は、1\&.5のものになります。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBリリース値: 1\&.4\fR。\fBjavadoc\fRコマンドは、JDK 1\&.4で導入されたアサーションを含むコードを受け付けます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBリリース値: 1\&.3\fR。\fBjavadoc\fRコマンドは、JDK 1\&.3以降に導入されたアサーション、総称、または他の言語機能をサポートしません。
.RE
.RE
.PP
\-sourcepath \fIsourcepathlist\fR
.RS 4
パッケージ名または\fB\-subpackages\fRオプションを\fBjavadoc\fRコマンドに渡すときに、ソース・ファイルを見つけるための検索パスを指定します。
.sp
複数のパスはコロン(:)で区切ります。
.sp
\fBjavadoc\fRコマンドは、指定されたパス以下のすべてのサブディレクトリを検索します。このオプションを使用して、ドキュメント化されるソース・ファイルの位置のみでなく、それ自体はドキュメント化されないがドキュメント化されるソース・ファイルから継承されたコメントを持つソース・ファイルの位置も確認できます。
.sp
\fB\-sourcepath\fRオプションを使用できるのは、\fBjavadoc\fRコマンドにパッケージ名を渡す場合のみです。\fBjavadoc\fRコマンドに渡されるソース・ファイルは検索されません。ソース・ファイルを特定するには、そのディレクトリに移動するか、「1つ以上のクラスのドキュメント化」に示すように各ファイルの前にパスを含めます。\fB\-sourcepath\fRが省略された場合、\fBjavadoc\fRコマンドは、クラス・パスを使用してソース・ファイルを検索します(\fB\-classpath\fRを参照)。デフォルトの\fB\-sourcepath\fRは、クラス・パスの値です。\fB\-classpath\fRを省略してパッケージ名を\fBjavadoc\fRコマンドに渡すと、\fBjavadoc\fRコマンドは現在のディレクトリ(およびそのサブディレクトリ)からソース・ファイルを検索します。
.sp
\fBsourcepathlist\fRには、ドキュメント化するパッケージのソース・ツリーのルート・ディレクトリを設定します。
.sp
たとえば、\fBcom\&.mypackage\fRという名前のパッケージをドキュメント化する場合に、そのソース・ファイルが/home/<USER>/src/com/mypackage/*\&.javaにあるとします。ソース・パスをcom\emypackageが含まれるディレクトリ/home/<USER>/srcに指定してから、次のように、パッケージ名を指定します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-sourcepath /home/<USER>/src/ com\&.mypackage\fR
 
.fi
.if n \{\
.RE
.\}
ソース・パスの値とパッケージ名を連結して、ドットをスラッシュ(/)に変更すると、次のように、パッケージのフルパスになります。
.sp
/home/<USER>/src/com/mypackage
.sp
2つのソース・パスを設定するには、次のようにします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-sourcepath /home/<USER>/src:/home/<USER>/src com\&.mypackage\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-classpath \fIclasspathlist\fR
.RS 4
\fBjavadoc\fRコマンドが参照クラスの検索を行うときに使用するパスを指定します。参照クラスとは、ドキュメント化されるクラスと、それらのクラスによって参照されるすべてのクラスのことです。
.sp
複数のパスはコロン(:)で区切ります。
.sp
\fBjavadoc\fRコマンドは、指定されたパス以下のすべてのサブディレクトリを検索します。\fBclasspathlist\fRの値を指定するときは、クラス・パスのドキュメントにある指示に従ってください。
.sp
\fB\-sourcepath\fRが省略された場合、\fBjavadoc\fRコマンドは\fB\-classpath\fRを使用して、ソース・ファイルおよびクラス・ファイルを検索します(後方互換性のため)。ソース・ファイルとクラス・ファイルを別々のパスから検索する必要がある場合は、\fB\-sourcepath\fRと\fB\-classpath\fRの両方を使用します。
.sp
たとえば、\fBcom\&.mypackage\fRをドキュメント化する場合に、そのソース・ファイルがディレクトリ/home/<USER>/src/com/mypackageにあり、このパッケージが/home/<USER>/lib内のライブラリに依存しているとき、次のように指定します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-sourcepath /home/<USER>/lib \-classpath /home/<USER>/src com\&.mypackage\fR
 
.fi
.if n \{\
.RE
.\}
他のツールと同様に、\fB\-classpath\fRが指定されていない場合、\fBCLASSPATH\fR環境変数が設定されていれば、\fBjavadoc\fRコマンドはその環境変数を使用します。どちらも設定されていない場合、\fBjavadoc\fRコマンドは現在のディレクトリからクラスを検索します。
.sp
\fBjavadoc\fRコマンドが\fB\-classpath\fRを使用してユーザー・クラスを検索する方法についての、拡張機能クラスやブートストラップ・クラスに関連した詳細は、クラスの検索方法
(http://docs\&.oracle\&.com/javase/8/docs/technotes/tools/findingclasses\&.html)を参照してください。
.sp
*のベース名を含むクラス・パス要素は、\fB\&.jar\fRまたは\fB\&.JAR\fRを拡張子に持つディレクトリ内のすべてのファイルのリストを指定するのと同等とみなされます。
.sp
たとえば、ディレクトリ\fBmydir\fRに\fBa\&.jar\fRと\fBb\&.JA\fRRが含まれている場合、クラス・パス要素\fBfoo/*\fRは\fBA\&.jar:b\&.JAR\fRに展開されますが、JARファイルの順番は未指定となります。非表示のファイルを含む、指定したディレクトリ内のすべてのJARファイルがリストに含まれます。*からなるクラス・パス・エントリは、現在のディレクトリ内のすべてのJARファイルのリストに展開されます。\fBCLASSPATH\fR環境変数も同様に展開されます。クラス・パスのワイルドカードの展開は、Java Virtual Machine (JVM)の開始前に行われます。Javaプログラムは、System\&.getenv(\fB"CLASSPATH"\fR)の呼び出しによってなど、環境を問い合せる場合を除き、展開されていないワイルドカードを参照しません。
.RE
.PP
\-subpackages \fIpackage1:package2:\&.\&.\&.\fR
.RS 4
ソース・ファイルから指定されたパッケージおよびそのサブパッケージ内に再帰的にドキュメントを生成します。このオプションは、ソース・コードに新しいサブパッケージを追加する際に便利です。新しいサブパッケージが自動的に組み込まれるからです。各package引数は、任意の最上位サブパッケージ(\fBjava\fRなど)または完全修飾パッケージ(\fBjavax\&.swing\fRなど)になります。ソース・ファイルを含める必要はありません。引数は、すべてのオペレーティング・システムで、コロンで区切られます。ワイルドカードは使用できません。パッケージの検索場所を指定するには、\fB\-sourcepath\fRを使用します。このオプションでは、ソース・ツリー内に存在するがパッケージに含まれないソース・ファイルを処理しません。ソース・ファイルの処理を参照してください。
.sp
たとえば、次のコマンドは、\fBjava\fRおよび\fBjavax\&.swing\fRという名前のパッケージとこれらのサブパッケージ全部のドキュメントを生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-d docs \-sourcepath /home/<USER>/src  \-subpackages java:javax\&.swing \fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-exclude \fIpackagename1:packagename2:\&.\&.\&.\fR
.RS 4
指定されたパッケージとそのサブパッケージを\fB\-subpackages\fRによって作成されたリストから無条件に除外します。過去または将来の\fB\-subpackages\fRオプションの指定によって組み込まれるパッケージも除外の対象となります。
.sp
次の例では、\fBjava\&.io\fR、\fBjava\&.util\fR、\fBjava\&.math\fRなどは組み込まれますが、\fBjava\&.net\fRと\fBjava\&.lang\fRをルートに持つパッケージは除外されます。\fBjava\&.lang\fRのサブパッケージである\fBjava\&.lang\&.ref\fRが除外される点に注意してください。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-sourcepath /home/<USER>/src \-subpackages java \-exclude \fR
\fB    java\&.net:java\&.lang\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-bootclasspath \fIclasspathlist\fR
.RS 4
ブート・クラスが存在するパスを指定します。ブート・クラスとは、通常、Javaプラットフォーム・クラスのことです。\fBbootclasspath\fRは、\fBjavadoc\fRコマンドがソース・ファイルとクラス・ファイルを探すときに使用する検索パスの一部です。詳細は、クラスの検出方法
(http://docs\&.oracle\&.com/javase/8/docs/technotes/tools/findingclasses\&.html)を参照してください
.sp
\fBclasspathlist\fRパラメータ内のディレクトリは、セミコロン(;)で区切る(Windowsの場合)か、コロン(:)で区切ります(Oracle Solarisの場合)。
.RE
.PP
\-extdirs \fIdirist\fR
.RS 4
拡張機能クラスが存在するディレクトリを指定します。拡張機能クラスとは、Java拡張機能機構を使用するすべてのクラスです。\fBextdirs\fRオプションは、\fBjavadoc\fRコマンドがソース・ファイルとクラス・ファイルを探すときに使用する検索パスの一部です。詳細は、\fB\-classpath\fRオプションを参照してください。\fBdirlist\fR内のディレクトリは、セミコロン(;)で区切る(Windowsの場合)か、コロン(:)で区切ります(Oracle Solarisの場合)。
.RE
.PP
\-verbose
.RS 4
\fBjavadoc\fRコマンドの実行中に詳細なメッセージを表示します。\fBverbose\fRオプションを指定しないと、ソース・ファイルのロード時、ドキュメントの生成時(ソース・ファイルごとに1つのメッセージ)、およびソート時にメッセージが表示されます。verboseオプションを指定すると、各Javaソース・ファイルの解析に要した時間(ミリ秒単位)を示す追加のメッセージが表示されます。
.RE
.PP
\-quiet
.RS 4
メッセージを抑制し、警告とエラーのみが表示されるようにして、これらを確認しやすくします。\fBversion\fR文字列も抑止します。
.RE
.PP
\-breakiterator
.RS 4
英語の場合、パッケージ、クラスまたはメンバーの主説明の最初の文の終わりを判断する際に、\fBjava\&.text\&.BreakIterator\fRの国際化された文境界を使用します。他のすべてのロケールは、英語言語というロケール固有のアルゴリズムではなく、すでに\fBBreakIterator\fRクラスを使用しています。最初の文は、パッケージ、クラス、またはメンバーのサマリーにコピーされ、アルファベット順の索引にコピーされます。JDK 1\&.2以降、\fBBreakIterator\fRクラスは、英語を除くすべての言語の文の終わりを判断するために、すでに使用されています。したがって、\fB\-breakiterator\fRオプションは、1\&.2以降では英文以外には効果がありません。英文には、次のような独自のデフォルトのアルゴリズムがあります。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
英文のデフォルトの文区切りアルゴリズム。空白文字またはHTMLブロック・タグ(\fB<P>\fRなど)が続くピリオドで停止します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
breakiterator文区切りアルゴリズム。次の語が大文字で始まる場合、空白文字が続くピリオド、疑問符、または感嘆符で停止します。このアルゴリズムでは「The serial no\&. is valid」など、ほとんどの省略表記が処理されますが、「Mr\&. Smith」は処理されません。\fB\-breakiterator\fRオプションでは、HTMLタグや、数字または記号で始まる文では停止しません。HTMLタグに埋め込まれている場合でも、アルゴリズムは「\&.\&./filename」の最後のピリオドで停止します。
.RE
.sp
Java SE 1\&.5では\fB\-breakiterator\fR警告メッセージが削除され、デフォルトの文区切りアルゴリズムは変更されていません。ソース・コードを変更せず、SE 1\&.4\&.xでの\fB\-breakiterator\fRオプションの警告を除去していない場合でも、何もする必要はありません。Java SE 1\&.5\&.0からは警告は消滅しています。
.RE
.PP
\-locale \fIlanguage_country_variant\fR
.RS 4
\fBjavadoc\fRコマンドがドキュメントを生成するときに使用するロケールを指定します。この引数は、\fBj\fR\fBava\&.util\&.Locale\fRドキュメントで説明しているように、\fBen_US\fR
(英語、米国)または\fBen_US_WIN\fR
(Windowsバリアント)などのロケールの名前です。
.sp
\fB注意:\fR
\fB\-locale\fRオプションは、標準ドックレットが提供するすべてのオプション、またはその他の任意のドックレットが提供するすべてのオプションより前(左側)に指定する必要があります。そうしないと、ナビゲーション・バーが英語で表示されます。このコマンド行オプションのみ、指定する順序に依存します。標準ドックレットのオプションを参照してください。
.sp
ロケールを指定すると、指定したロケールのリソース・ファイルが\fBjavadoc\fRコマンドによって選択されて、メッセージ(ナビゲーション・バー、リストと表の見出し、ヘルプ・ファイルの目次、stylesheet\&.cssのコメントなどの文字列)のために使用されます。また、アルファベット順にソートされるリストのソート順、および最初の文の終わりを判断するための文の区切り文字も、指定したロケールによって決まります。\fB\-locale\fRオプションは、ドキュメント化されるクラスのソース・ファイル内で指定されているドキュメンテーション・コメントのテキストのロケールを決定するものではありません。
.RE
.PP
\-encoding
.RS 4
ソース・ファイルのエンコーディングの名前(\fBEUCJIS/SJIS\fRなど)を指定します。このオプションが指定されていない場合は、プラットフォームのデフォルト・コンバータが使用されます。およびオプションも参照してください。
.RE
.PP
\-J\fIflag\fR
.RS 4
\fBjavadoc\fRコマンドを実行するJava Runtime Environment (JRE)に、\fBflag\fRを直接渡します。たとえば、生成ドキュメントを処理するためにシステムで32MBのメモリーを確保しておく必要がある場合は、\fB\-Xmx\fRオプションを次のように呼び出します。\fBjavadoc \-J\-Xmx32m \-J\-Xms32m com\&.mypackage\fR。\fB\-Xms\fRは省略可能で、これは初期メモリーのサイズを設定するのみのオプションで、必要なメモリーの最小量がわかっている場合に便利です。
.sp
\fBJ\fRと\fBflag\fRの間に空白文字はありません。
.sp
使用している\fBjavadoc\fRコマンドのバージョンを確認するには\fB\-version\fRオプションを使用します。出力ストリームには標準ドックレットのバージョン番号が含まれます。Javadocコマンドの実行を参照してください。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-J\-version\fR
\fBjava version "1\&.7\&.0_09"\fR
\fBJava(TM) SE Runtime Environment (build 1\&.7\&.0_09\-b05)\fR
\fBJava HotSpot(TM) 64\-Bit Server VM (build 23\&.5\-b02, mixed mode)\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-javafx
.RS 4
標準ドックレットに対してJavaFX拡張機能を使用して、HTMLドキュメントを生成します。生成されたドキュメントには、標準Javaドックレットで生成された他のサマリー・セクションに加えて「プロパティのサマリー」セクションが含まれています。リストされたプロパティは、各プロパティのgetterおよびsetterメソッドのセクションにリンクされます。
.sp
getterおよびsetterメソッドに対して明示的に記載されているドキュメント・コメントがない場合、プロパティ・メソッドのドキュメント・コメントがこれらのメソッドに対して生成されたドキュメントに自動的にコピーされます。このオプションは、プロパティのデフォルト値を記述できる新しい\fB@defaultValue\fRタグも追加します。
.sp
例:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-javafx MyClass\&.java \-d testdir\fR
.fi
.if n \{\
.RE
.\}
.RE
.SS "標準ドックレットのオプション"
.PP
\-d \fIdirectory\fR
.RS 4
\fBjavadoc\fRコマンドが生成されたHTMLファイルを保存する生成先ディレクトリを指定します。\fB\-d\fRオプションを省略すると、ファイルは現在のディレクトリに保存されます。\fBdirectory\fRの値には、絶対ディレクトリ、または現在の作業ディレクトリからの相対ディレクトリを指定できます。Java SE 1\&.4では、\fBjavadoc\fRコマンドを実行すると生成先ディレクトリが自動的に作成されます。
.sp
たとえば、次の例では、\fBcom\&.mypackage\fRパッケージのドキュメントが生成され、その結果が/user/doc/ディレクトリに保存されます。\fBjavadoc \-d \fR\fB/user/doc/ \fR\fBcom\&.mypackage\fR
.RE
.PP
\-use
.RS 4
ドキュメント化されるクラスおよびパッケージごとに1つの「使用」ページを組み込みます。このページには、その特定のクラスまたはパッケージのAPIを使用しているパッケージ、クラス、メソッド、コンストラクタ、およびフィールドが記述されます。たとえば、クラスCを例にとると、クラスCを使用しているものとしては、Cのサブクラス、Cとして宣言されているフィールド、Cを返すメソッド、および型Cのパラメータを持つメソッドとコンストラクタがあります。たとえば、\fBString\fR型用の「使用」ページを表示できます。\fBjava\&.awt\&.Font\fRクラスの\fBgetName\fRメソッドは\fBString\fR型を戻すので、\fBgetName\fRメソッドは\fBString\fRを使用し、\fBgetName\fRメソッドが\fBString\fR用の「使用」ページに表示されます。これは実装ではなくAPIの使用のみをドキュメント化します。メソッドがその実装で\fBString\fRを使用するが、引数として文字列を取らない、または文字列を返さない場合、それは\fBString\fRの使用とはみなされません。生成された「使用」ページにアクセスするには、クラスまたはパッケージに移動し、ナビゲーション・バーの\fBリンクの使用\fRをクリックします。
.RE
.PP
\-version
.RS 4
生成ドキュメントに、@versionのテキストを組み込みます。このテキストは、デフォルトでは省略されます。使用している\fBjavadoc\fRコマンドのバージョンを確認するには\fB\-J\-version\fRオプションを使用します。
.RE
.PP
\-author
.RS 4
生成ドキュメントに、\fB@author\fRのテキストを組み込みます。
.RE
.PP
\-splitindex
.RS 4
索引ファイルをアルファベットごとに複数のファイルに分割し、文字ごとに1つのファイルと、アルファベット以外の記号で始まる索引エントリ用に1つのファイルを作成します。
.RE
.PP
\-windowtitle \fItitle\fR
.RS 4
HTMLの\fB<title>\fRタグに配置するタイトルを指定します。\fBtitle\fRタグに指定したテキストは、ウィンドウのタイトルや、このページに対して作成されたブラウザのブックマーク(お気に入り)に表示されます。このタイトルにはHTMLタグを含めないでください。タイトルにHTMLタグが含まれていると、ブラウザがタグを正しく解釈できません。\fBtitle\fRタグ内の内部の二重引用符はエスケープ文字を使用してマークします。\fB\-windowtitle\fRオプションを省略すると、\fBjavadoc\fRコマンドは、\fB\-windowtitle\fRオプションのかわりに、\fB\-doctitle\fRオプションの値を使用します。たとえば、\fBjavadoc \-windowtitle "Java SE Platform" com\&.mypackage\fRです。
.RE
.PP
\-doctitle \fItitle\fR
.RS 4
概要サマリー・ファイルの最上部の近くに配置するタイトルを指定します。\fBtitle\fRタグに指定したテキストは中央揃えになり、レベル1の見出しとして、上部ナビゲーション・バーのすぐ下に置かれます。\fBtitle\fRタグにはHTMLタグおよび空白文字を含めることができますが、その場合、タイトルを引用符で囲む必要があります。\fBtitle\fRタグの内部で引用符を使用する場合は、エスケープする必要があります。たとえば、\fBjavadoc \-header "<b>Java Platform </b><br>v1\&.4" com\&.mypackage\&.\fRとなります。
.RE
.PP
\-title \fItitle\fR
.RS 4
すでに存在しません。Javadoc 1\&.2のベータ版にしか存在していませんでした。このオプションは、\fB\-doctitle\fRという名前に変更されました。名前を変更した理由は、このオプションが、ウィンドウのタイトルではなくドキュメントのタイトルを定義することを明確にするためです。
.RE
.PP
\-header \fIheader\fR
.RS 4
各出力ファイルの最上部に配置するヘッダー・テキストを指定します。ヘッダーは、ナビゲーション・バーの右上に配置されます。\fBheader\fRにはHTMLタグおよび空白文字を含めることができますが、その場合、\fBheader\fRを引用符で囲む必要があります。ヘッダー内部の引用符にはエスケープ文字を使用します。たとえば、\fBjavadoc \-header "<b>Java Platform </b><br>v1\&.4" com\&.mypackage\&.\fRとなります。
.RE
.PP
\-footer \fIfooter\fR
.RS 4
各出力ファイルの最下部に配置するフッター・テキストを指定します。\fIfooter\fRの値は、ナビゲーション・バーの右下に配置されます。\fBfooter\fRにはHTMLタグおよび空白文字を含めることができますが、その場合、\fBfooter\fRを引用符で囲む必要があります。フッター内部の引用符にはエスケープ文字を使用します。
.RE
.PP
\-top
.RS 4
各出力ファイルの最上部に配置するテキストを指定します。
.RE
.PP
\-bottom \fItext\fR
.RS 4
各出力ファイルの最下部に配置するテキストを指定します。このテキストは、下部ナビゲーション・バーより下の、ページの最下部に配置されます。テキストにはHTMLタグおよび空白文字を含めることができますが、その場合、テキストを引用符で囲む必要があります。テキスト内部の引用符にはエスケープ文字を使用します。
.RE
.PP
\-link \fIextdocURL\fR
.RS 4
既存のJavadocにより生成された外部参照クラスのドキュメントへのリンクを作成します。\fIextdocURL\fR引数は、リンク先として指定する、Javadocにより生成された外部ドキュメントを含むディレクトリの絶対URLまたは相対URLです。指定した\fBjavadoc\fRコマンドの実行で、複数の\fB\-link\fRオプションを指定して複数のドキュメントへのリンクを作成できます。
.sp
このディレクトリ内にpackage\-listファイルが存在する必要があります(存在しない場合は、\fB\-linkoffline\fRオプションを使用します)。\fBjavadoc\fRコマンドは、package\-listファイルからパッケージ名を読み取った後、そのURLでこれらのパッケージにリンクします。\fBjavadoc\fRコマンドの実行時に、\fBextdocURL\fRの値が、作成された\fB<A HREF>\fRリンク内にコピーされます。したがって、\fBextdocURL\fRはファイルではなくディレクトリへのURLである必要があります。\fIextdocURL\fRに絶対リンクを使用すると、ユーザーのドキュメントを任意のWebサイト上のドキュメントにリンクできます。相対位置へリンクするのみの場合は相対リンクを使用できます。相対リンクを使用する場合、渡す値は宛先ディレクトリから、リンクされているパッケージを含むディレクトリへである必要があります(\fB\-d\fRオプションで指定)。絶対リンクを指定する場合、通常、HTTPリンクを使用します。ただし、Webサーバーを持たないファイル・システムにリンクする場合は、ファイル・リンクを使用できます。生成されたドキュメンテーションにアクセスする全員が同じファイル・システムを共有する場合にのみファイル・リンクを使用します。どの場合も、どのオペレーティング・システムでも、URLが絶対または相対のいずれでも、また\fBh\fR\fBttp:\fRまたは\fBf\fR\fBile:\fRのいずれでも、URLメモ: Uniform Resource Locators
(http://www\&.ietf\&.org/rfc/rfc1738\&.txt)に指定されているとおり、区切り文字としてスラッシュを使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-link  http://<host>/<directory>/<directory>/\&.\&.\&./<name>\fR
\fB\-link file://<host>/<directory>/<directory>/\&.\&.\&./<name>\fR
\fB\-link <directory>/<directory>/\&.\&.\&./<name>\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-linkofflineおよび\-linkオプションの違い
.PP
次の場合に、\fB\-link\fRオプションを使用します。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
外部APIドキュメントへの相対パスを使用する場合。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
外部APIドキュメントへの絶対URLを使用する場合(そのURLに接続し、読取りを行うことがシェルによって許可されている場合)。
.RE
.PP
外部APIドキュメントへの絶対URLを使用する場合(そのURLに接続し、読取りを行うことがシェルによって許可されていない場合)は\fB\-linkoffline\fRオプションを使用します。このような状況は、ファイアウォールの内側からファイアウォールの外側にあるドキュメントにリンクしようとする場合に発生します。
.PP
\fB例 1 \fR外部ドキュメントへの絶対リンク
.RS 4
http://docs\&.oracle\&.com/javase/8/docs/api/index\&.htmlに示すような、\fBjava\&.lang\fR、\fBjava\&.io\fRおよびその他のJavaプラットフォーム・パッケージにリンクする場合、次のコマンドを使用します
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-link http://docs\&.oracle\&.com/javase/8/docs/api/ com\&.mypackage\fR
 
.fi
.if n \{\
.RE
.\}
このコマンドは、Java SEプラットフォーム・パッケージへのリンク持つ\fBcom\&.mypackage\fRパッケージのドキュメントを生成します。生成ドキュメントには、たとえばクラス\fBtrees\fR内の\fBObject\fRクラスへのリンクが含まれています。\fB\-sourcepath\fRや\fB\-d\fRなどの他のオプションは表示されません。
.RE
.PP
\fB例 2 \fR外部ドキュメントへの相対リンク
.RS 4
この例では、2つのパッケージがあり、そのドキュメントは\fBjavadoc\fRコマンドを複数回実行した結果生成されたものです。さらに、これらのドキュメントは相対パスで分割されています。パッケージは、APIである\fBcom\&.apipackage\fRと、SPI(サービス・プロバイダ・インタフェース)であるc\fBom\&.spipackage\fRです。ドキュメントの格納先は、docs/api/com/apipackageとdocs/spi/com/spipackageです。APIパッケージのドキュメントはすでに生成されていて、docsが現在のディレクトリである場合、APIドキュメントへのリンクを持つSPIパッケージをドキュメント化するには、次のコマンドを実行します。\fBjavadoc \-d \&./spi \-link \&.\&./api com\&.spipackage\fR
.sp
\fB\-link\fRオプションは、宛先ディレクトリ(docs/spi)からの相対パスです。
.RE
.PP
注意
.PP
\fB\-link\fRオプションを使用すると、コードからは参照されていても、今回の\fBjavadoc\fRの実行ではドキュメント化されないクラスにリンクできるようになります。リンクから有効なページに移動できるようにするには、それらのHTMLページがある場所を調べ、その場所を\fBextdocURL\fRに指定する必要があります。これにより、サードパーティのドキュメンテーションがjava\&.*ドキュメンテーション(
http://docs\&.oracle\&.com)へリンクすることができます。\fBjavadoc\fRコマンドで、現在の実行で生成しているドキュメンテーション内のAPIへのリンクのみを作成する場合には、\fB\-link\fRオプションを省略します。\fB\-link\fRオプションを指定しないと、\fBjavadoc\fRコマンドは外部参照のためのドキュメンテーションへのリンクを作成しません。ドキュメンテーションが存在するのかどうか、またはどこに存在するのかがわからないからです。\fB\-link\fRオプションでは、生成ドキュメンテーション内の複数の場所にリンクを作成できます。ソース・ファイルの処理を参照してください。もう1つの用途は、パッケージ・セットの間にクロスリンクを作成することです。一方のパッケージ・セットに対して\fBjavadoc\fRコマンドを実行した後、他方のパッケージ・セットに対して\fBjavadoc\fRコマンドを再度実行すると、両セット間に双方向のリンクを作成できます。
.PP
クラスの参照方法
.PP
表示される外部参照クラスへのリンクの場合(およびそのテキスト・ラベルだけではなく)、クラスは次の方法で参照される必要があります。メソッドの本体でクラスを参照するのみでは十分ではありません。\fBimport\fR文、宣言のいずれかで参照する必要があります。次に、クラス\fBjava\&.io\&.File\fRを参照する方法の例を示します。
.PP
すべてのタイプのimport文の場合。ワイルドカードによるインポート、名前による明示的なインポート、または\fBjava\&.lang\&.*\fRに対する自動インポート。
.PP
Java SE 1\&.3\&.\fIn\fRおよび1\&.2\&.\fIn\fRでは、名前による明示的なインポートのみ機能します。ワイルドカードによる\fBimport\fR文も、\fBimport java\&.lang\&.*\fRの自動インポートも機能しません。
.PP
宣言の場合:
\fBvoid mymethod(File f) {}\fR
.PP
参照は、メソッド、コンストラクタ、フィールド、クラスまたはインタフェースの戻り型またはパラメータ・タイプ、あるいは実装、拡張またはスロー文にあります。
.PP
重要な結果として、\fB\-link\fRオプションを使用しても、この制限のために誤って表示されないリンクが多数発生する可能性があります。テキストはハイパーテキスト・リンクが付けられずに表示されます。リンクが表示する警告から、これらのリンクを認識できます。クラスを正しく参照し、それによってリンクを追加するための最も簡単な方法はそのクラスをインポートすることです。
.PP
パッケージ・リスト
.PP
\fB\-link\fRオプションには、\fBjavadoc\fRコマンドによって生成されるpackage\-listという名前のファイルが、\fB\-link\fRオプションに指定したURLに存在していることが必要です。package\-listファイルは、その場所にあるドキュメント化されたパッケージの名前のリストが入った単純なテキスト・ファイルです。前述の例では、\fBjavadoc\fRコマンドは、指定したURLでpackage\-listという名前のファイルを検索し、パッケージ名を読み取って、そのURLでこれらのパッケージにリンクします。
.PP
たとえば、Java SE APIのパッケージ・リストは
http://docs\&.oracle\&.com/javase/8/docs/api/package\-listにあります。
.PP
このパッケージ・リストは次のような内容で始まっています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava\&.applet\fR
\fBjava\&.awt\fR
\fBjava\&.awt\&.color\fR
\fBjava\&.awt\&.datatransfer\fR
\fBjava\&.awt\&.dnd\fR
\fBjava\&.awt\&.event\fR
\fBjava\&.awt\&.font\fR
\fBand so on \&.\&.\&.\&.\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fB\-link\fRオプションを指定せずに\fBjavadoc\fRを実行した場合、外部参照クラスに属する名前を見つけると、その名前をリンクなしで出力します。一方、\fB\-link\fRオプションを指定した場合、\fBjavadoc\fRコマンドは、指定された\fIextdocURL\fRの場所にあるpackage\-listファイルでそのパッケージ名を検索します。パッケージ名が見つかると、\fIextdocURL\fRが名前の前に付加されます。
.PP
すべてのリンクが正しく機能するためには、外部参照のすべてのドキュメントが、指定したURLに存在する必要があります。\fBjavadoc\fRコマンドは、指定されたpackage\-listが存在するかどうかのみをチェックし、これらのページが存在するかどうかはチェックしません。
.PP
複数のリンク
.PP
複数の\fB\-link\fRオプションを指定すると、任意の数の外部生成ドキュメントへのリンクを作成できます。Javadoc 1\&.2には、複数の\fB\-link\fRオプションを指定できないという既知の不具合があります。これはJavadoc 1\&.2\&.2で修正されました。リンクする外部ドキュメントごとに、次のように別々のリンク・オプションを指定します。\fBjavadoc \-link extdocURL1 \-link extdocURL2 \&.\&.\&. \-link extdocURLn com\&.mypackage\fR
\fIextdocURL1\fR、\fIextdocURL2\fR、\&.\fB\&.\&. extdocURLn\fRは、それぞれ外部ドキュメントのルートを指し、各ルートには、package\-listという名前のファイルが入っています。
.PP
クロスリンク
.PP
以前に作成された複数のドキュメントをクロスリンクする場合、ブートストラップが必要になることがあります。どのドキュメントについてもpackage\-listが存在していない場合は、最初のドキュメントに対して\fBjavadoc\fRコマンドを実行する時点で、2番目のドキュメントのpackage\-listはまだ存在していません。したがって、外部リンクを作成するには、2番目のドキュメントを生成した後で、最初のドキュメントを生成しなおす必要があります。
.PP
この場合、最初のドキュメント生成の目的は、package\-listを作成することです(パッケージ名を把握している場合は手動で作成してもかまいません)。次に、2番目のドキュメントとその外部リンクを生成します。必要な外部のpackage\-listファイルが存在しない場合は、\fBjavadoc\fRコマンドから警告が出力されます。
.PP
\-linkoffline \fIextdocURL packagelistLoc\fR
.RS 4
このオプションは\fB\-link\fRオプションのバリエーションです。両方とも、Javadocにより生成された外部参照クラスのドキュメントへのリンクを作成します。\fBjavadoc\fRコマンドがWeb接続を使用してドキュメントにアクセスできないとき、Web上のドキュメントにリンクするには、\fB\-link\fRo\fBffline\fRオプションを使用します。外部ドキュメントのpackage\-listファイルにアクセスできないとき、またはこのファイルが\fBextdocURL\fRで指定された場所には存在せず、\fBpackageListLoc\fRで指定できる別の場所(通常ローカル)に存在するとき、\fB\-linkoffline\fRオプションを使用します。\fBextdocURL\fRにWorld Wide Web上でしかアクセスできない場合は、\fB\-linkoffline\fRオプションを指定することにより、ドキュメントの生成時に\fBjavadoc\fRコマンドがWebに接続する必要があるという制約がなくなります。もう1つの用途は、ドキュメントを更新するための回避策として使用することです。パッケージのセット全体に対して\fBjavadoc\fRコマンドを実行した後、変更した一部のパッケージに対してのみ\fBjavadoc\fRコマンドを再度実行して、更新されたファイルを、オリジナルのセットに挿入できるようにします。次に例を示します。\fB\-linkoffline\fRオプションは引数を2つ取ります。第1引数は\fB<a href>\fRリンクに組み込まれる文字列を指定し、第2引数はpackage\-listの検索場所を\fB\-linkoffline\fRに伝えます。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBextdocURL\fRの値は、リンク先として指定する、Javadocにより生成された外部ドキュメントを含むディレクトリの絶対URLまたは相対URLです。相対URLの場合、値は、生成先ディレクトリ(\fB\-d\fRオプションで指定)からリンク先となるパッケージのルートへの相対パスにする必要があります。詳細は、\fB\-link\fRオプションの\fIextdocURL\fRを参照してください。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBpackagelistLoc\fRの値は、外部ドキュメントのpackage\-listファイルを含むディレクトリへのパスまたはURLです。これは、URL (http:またはfile:)でもファイル・パスでもかまいません。また、絶対パスと相対パスのどちらでもかまいません。相対パスの場合は、\fBjavadoc\fRコマンドが実行される現在のディレクトリからの相対パスとして指定します。package\-listファイル名を含めないでください。
.sp
指定した\fBjavadoc\fRコマンドの実行で、複数の\fB\-link\fRオプションを指定できます。Javadoc 1\&.2\&.2より前では、\fB\-linkfile\fRオプションは1回しか指定できませんでした。
.RE
.RE
.PP
外部ドキュメントへの絶対リンク
.PP
http://docs\&.oracle\&.com/javase/8/docs/api/index\&.htmlに示すような、\fBjava\&.lang\fR、
\fBjava\&.io\fRおよびその他のJava SEパッケージにリンクする必要がある場合があります。
.PP
ただし、シェルにはWebアクセス権がありません。この場合、次を行います。
.sp
.RS 4
.ie n \{\
\h'-04' 1.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  1." 4.2
.\}
ブラウザでpackage\-listファイルを開きます(http://docs\&.oracle\&.com/javase/8/docs/api/package\-list)
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 2.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  2." 4.2
.\}
ファイルをローカル・ディレクトリに保存し、このローカル・コピーを第2引数\fBpackagelistLoc\fRで指定します。この例では、パッケージ・リスト・ファイルはカレント・ディレクトリ(\&.)に保存されています。
.RE
.PP
次のコマンドは、Java SEプラットフォーム・パッケージへのリンクを持つc\fBom\&.mypackage\fRパッケージのドキュメントを生成します。生成ドキュメントには、たとえばクラス\fBtrees\fR内の\fBObject\fRクラスへのリンクが含まれています。\fB\-sourcepath\fRなど、他の必要なオプションは表示されません。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-linkoffline http://docs\&.oracle\&.com/javase/8/docs/api/ \&.  com\&.mypackage \fR
 
.fi
.if n \{\
.RE
.\}
.PP
外部ドキュメントへの相対リンク
.PP
\fB\-linkoffline\fRを相対パスとともに使用することはあまりありません。理由は単純で、通常は\fB\-link\fRで間に合うからです。\fB\-linkoffline\fRオプションを使用する場合、通常、package\-listファイルはローカルで、相対リンクを使用する場合はリンク先のファイルもローカルなので、通常は\fB\-linkoffline\fRオプションの2つの引数に、異なるパスを指定する必要はありません。2つの引数が同一の場合、\fB\-link\fRオプションを使用できます。
.PP
package\-listファイルの手動での作成
.PP
package\-listファイルがまだ存在しなくても、ドキュメントのリンク先のパッケージ名がわかっている場合は、このファイルのコピーを手動で作成し、\fBpackagelistLoc\fRでそのパスを指定することができます。\fBcom\&.apipackage\fRが最初に生成された時点で\fBcom\&.spipackage\fRのパッケージ・リストが存在しないという前出のケースが一例として挙げられます。この方法は、パッケージ名はわかっているものの、まだ公開されていない、新しい外部ドキュメントにリンクするドキュメントを生成する必要がある場合に便利です。また、package\-listファイルが生成されないJavadoc 1\&.0または1\&.1で生成されたパッケージ用にpackage\-listファイルを作成する場合にも、この方法が使用できます。同様に、2つの企業が未公開のpackage\-listファイルを共有できるため、クロスリンクを設定したドキュメントを同時にリリースすることも可能になります。
.PP
複数ドキュメントへのリンク
.PP
参照先の生成ドキュメントごとに1回、\fB\-linkoffline\fRオプションを含めることができます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-linkoffline extdocURL1 packagelistLoc1 \-linkoffline extdocURL2\fR
\fBpackagelistLoc2 \&.\&.\&.\fR
 
.fi
.if n \{\
.RE
.\}
.PP
ドキュメントの更新
.PP
プロジェクトに何十または何百のパッケージが含まれる場合にも、\fB\-linkoffline\fRオプションを使用できます。ソース・ツリー全体ですでに\fBjavadoc\fRコマンドを実行したことがある場合、ドキュメンテーション・コメントにわずかな変更を迅速に加え、ソース・ツリーの一部で\fBjavadoc\fRコマンドを再実行することができます。2回目の実行は、ドキュメンテーション・コメントを変更し、宣言は変更しない場合にのみ正しく処理されることに注意してください。ソース・コードに対して宣言を追加、削除、または変更した場合は、索引、パッケージ・ツリー、継承されるメンバーのリスト、使用ページなどの場所で、リンクが壊れることがあります。
.PP
まず、この新しい小規模な実行で使用する、新しい生成先ディレクトリ(updateなど)を作成します。この例では、元の生成先ディレクトリの名前はhtmlです。最も単純な例では、htmlディレクトリの親ディレクトリに移動します。\fB\-linkoffline\fRオプションの第1引数にカレント・ディレクトリ(\&.)を設定し、第2引数にpackage\-listが検索されるhtmlへの相対パスを設定し、更新するパッケージのパッケージ名のみを渡します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-d update \-linkoffline \&. html com\&.mypackage\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBjavadoc\fRコマンドの終了後、update/com/package内の生成されたクラスのページをコピーし(概要や索引は除く)、html/com/package内の元のファイルに上書きします。
.PP
\-linksource
.RS 4
各ソース・ファイル(行番号付き)のHTMLバージョンを作成し、標準HTMLドキュメントからソース・ファイルへのリンクを追加します。リンクは、ソース・ファイル内に宣言されているクラス、インタフェース、コンストラクタ、メソッド、フィールドに対して作成されます。デフォルト・コンストラクタ、生成されたクラスなどに対しては作成されません。
.sp
このオプションは、\fB\-public\fR、\fB\-package\fR、\fB\-protected\fRおよび\fB\-private\fRの各オプションとは関係なく、非公開のクラス、フィールド、非公開のメソッドの本体をはじめとする組み込まれたソース・ファイル内のすべての非公開実装の詳細を公開します。\fB\-private\fRオプションもあわせて指定しないかぎり、非公開のクラスやインタフェースの一部には、リンクを介してアクセスできないことがあります。
.sp
各リンクは、その宣言内の識別子名の上に作成されます。たとえば、\fBButton\fRクラスのソース・コードへのリンクは、\fBButton\fRという語の上に作成されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpublic class Button extends Component implements Accessible\fR
 
.fi
.if n \{\
.RE
.\}
\fBButton\fRクラスの\fBgetLabel\fRメソッドのソース・コードへのリンクは、\fBgetLabel\fRという語の上に作成されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpublic String getLabel()\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-group groupheading \fIpackagepattern:packagepattern\fR
.RS 4
概要ページの複数のパッケージを、指定したグループに分けて、グループごとに表を作成します。各グループは、それぞれ別の\fB\-group\fRオプションで指定します。グループは、コマンド行で指定された順序でページに表示されます。パッケージは、グループ内でアルファベット順になっています。指定した\fB\-group\fRオプションでは、\fBpackagepattern\fR式のリストに一致するパッケージが、見出しとして\fIgroupheading\fRを持つ1つの表に表示されます。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBgroupheading\fRには、任意のテキストを指定でき、空白を含めることができます。指定したテキストは、グループの表見出しになります。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBpackagepattern\fRの値には、任意のパッケージ名の先頭部分とそれに続く1つのアスタリスク(*)で任意のパッケージ名を指定できます。アスタリスクは使用できる唯一のワイルドカードで、任意の文字に一致する、という意味です。1つのグループには、コロン(:)で区切って複数のパターンを含めることができます。パターンまたはパターン・リストでアスタリスクを使用する場合、パターン・リストは\fB"java\&.lang*:java\&.util"\fRのように引用符で囲む必要があります。
.RE
.sp
\fB\-group\fRオプションを指定しない場合は、見出し\fIPackages\fRおよび適切な小見出しを持つ1つのグループに配置されます。小見出しにすべてのドキュメント化されるパッケージ(すべてのグループ)が含まれるわけではない場合、残りのパッケージは「その他のパッケージ」というサブ見出しを持つ独立したグループに入れられます。
.sp
たとえば、次の\fBjavadoc\fRコマンドでは、3つのドキュメント化されたパッケージが\fI「コア」\fR、\fI「拡張」\fRおよび\fI「その他のパッケージ」\fRに分けられます。\fBjava\&.lang*\fRでは、最後のドット(\&.)を指定していません。\fBjava\&.lang\&.*\fRのようにドットを入れると、\fB java\&.lang\fRパッケージは除外されることになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-group "Core Packages" "java\&.lang*:java\&.util"\fR
\fB        \-group "Extension Packages" "javax\&.*"\fR
\fB        java\&.lang java\&.lang\&.reflect java\&.util javax\&.servlet java\&.new\fR
 
.fi
.if n \{\
.RE
.\}
\fBコア・パッケージ\fR
.sp
\fBjava\&.lang\fR
.sp
\fBjava\&.lang\&.reflect\fR
.sp
\fBjava\&.util\fR
.sp
\fB拡張機能パッケージ\fR
.sp
\fBjavax\&.servlet\fR
.sp
\fBOther Packages\fR
.sp
\fBjava\&.new\fR
.RE
.PP
\-nodeprecated
.RS 4
推奨されないAPIをドキュメントに生成しないようにします。このオプションを指定すると、\fB\-nodeprecatedlist\fRオプションを指定した場合と同じ効果があり、ドキュメントの他の部分全体でも、推奨されないAPIが生成されません。このオプションは、コードを記述しているとき、推奨されないコードによって気を散らされたくない場合に便利です。
.RE
.PP
\-nodeprecatedlist
.RS 4
推奨されないAPIのリストを含むファイル(deprecated\-list\&.html)、およびナビゲーション・バーのそのページへのリンクが生成されないようにします。\fBjavadoc\fRコマンドでは、引き続き、ドキュメントの他の部分では、推奨されないAPIが生成されます。このオプションは、推奨されないAPIがソース・コードに含まれておらず、ナビゲーション・バーをすっきりと見せる場合に便利です。
.RE
.PP
\-nosince
.RS 4
生成ドキュメントから、\fB@since\fRタグに関連付けられた\fB「導入されたバージョン」\fRセクションを省略します。
.RE
.PP
\-notree
.RS 4
生成ドキュメントから、クラスおよびインタフェースの階層ページを省略します。これらのページには、ナビゲーション・バーの「階層ツリー」ボタンからアクセスできます。デフォルトでは、階層が生成されます。
.RE
.PP
\-noindex
.RS 4
生成ドキュメントから、索引を省略します。デフォルトでは、索引が生成されます。
.RE
.PP
\-nohelp
.RS 4
出力の各ページの最上部と最下部にあるナビゲーション・バーから「ヘルプ」リンクを省略します。
.RE
.PP
\-nonavbar
.RS 4
通常、生成されるページの最上部と最下部に表示されるナビゲーション・バー、ヘッダー、およびフッターを生成しないようにします。\fB\-nonavbar\fRオプションは\fB\-bottom\fRオプションに影響を与えません。\fB\-nonavbar\fRオプションは、印刷するためにのみファイルをPostScriptやPDFに変換する場合など、内容のみが重要で、ナビゲーションの必要がない場合に便利です。
.RE
.PP
\-helpfile \fIpath\efilename\fR
.RS 4
最上部および最下部のナビゲーション・バーの「ヘルプ」リンクのリンク先となる代替ヘルプ・ファイルpath\efilenameのパスを指定します。このオプションが指定されていないと、\fBjavadoc\fRコマンドは、\fBjavadoc\fRコマンド内でハードコードされているヘルプ・ファイルhelp\-doc\&.htmlを自動作成します。このオプションを使用すると、デフォルトをオーバーライドできます。ファイル名にはどんな名前でも指定でき、help\-doc\&.htmlに限定されません。\fBjavadoc\fRコマンドは、次の例のように、ナビゲーション・バー内のリンクを必要に応じて調整します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-helpfile /home/<USER>/myhelp\&.html java\&.awt\&.\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-stylesheet \fIpath/filename \fR
.RS 4
代替HTMLスタイルシート・ファイルのパスを指定します。このオプションが指定されていないと、\fBjavadoc\fRコマンドは、\fBjavadoc\fRコマンド内でハードコードされているスタイルシート・ファイルstylesheet\&.cssを自動作成します。このオプションを使用すると、デフォルトをオーバーライドできます。ファイル名にはどんな名前でも指定でき、stylesheet\&.cssに限定されません。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-stylesheet file /home/<USER>/mystylesheet\&.css com\&.mypackage\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-serialwarn
.RS 4
\fB@serial\fRタグがない場合は、コンパイル時に警告を生成します。デフォルトでは、Javadoc 1\&.2\&.2以降では、直列化の警告は生成されません。以前のリリースとは逆の動作です。このオプションを使用すると、直列化の警告が表示されるので、デフォルトの直列化可能フィールドと\fBwriteExternal\fRメソッドを適切にドキュメント化するのに役立ちます。
.RE
.PP
\-charset \fIname\fR
.RS 4
このドキュメント用のHTML文字セットを指定します。この名前は、IANA RegistryのCharacter Sets
(http://www\&.iana\&.org/assignments/character\-sets)に示された、優先MIME名である必要があります。
.sp
たとえば、\fBjavadoc \-charset "iso\-8859\-1" mypackage\fRは次の行を生成された各ページのヘッダーに挿入します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB<META http\-equiv="Content\-Type" content="text/html; charset=ISO\-8859\-1">\fR
 
.fi
.if n \{\
.RE
.\}
この\fBMETA\fRタグは、HTML標準(4197265および4137321)のHTML Document Representation
(http://www\&.w3\&.org/TR/REC\-html40/charset\&.html#h\-5\&.2\&.2)に記載されています。
.sp
およびオプションも参照してください。
.RE
.PP
\-docencoding \fIname\fR
.RS 4
生成されるHTMLファイルのエンコーディングを指定します。この名前は、IANA RegistryのCharacter Sets
(http://www\&.iana\&.org/assignments/character\-sets)に示された、優先MIME名である必要があります。
.sp
\fB\-docencoding\fRオプションを省略し、\fB\-encoding\fRオプションを使用すると、生成されたHTMLファイルの暗号化は\fB\-encoding\fRオプションで特定されます。例:
\fBjavadoc \-docencoding "iso\-8859\-1" mypackage\fRおよびオプションも参照してください。
.RE
.PP
\-keywords
.RS 4
HTMLキーワード<META>タグを、クラスごとに生成されるファイルに追加します。これらのタグは、<META>タグを検索するサーチ・エンジンがページを見つける場合に役立ちます。インターネット全体を検索する検索エンジンのほとんどは<META>タグを参照しません。ページが誤用している可能性があるからです。自身のWebサイトへの検索を制限する、企業により提供される検索エンジンは、<META>タグを参照することで恩恵を受けることができます。<META>タグには、クラスの完全修飾名と、フィールドおよびメソッドの修飾されていない名前が含まれます。コンストラクタは、クラス名と同じであるため含まれません。たとえば、クラス\fBString\fRは次のキーワードで開始します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB<META NAME="keywords" CONTENT="java\&.lang\&.String class">\fR
\fB<META NAME="keywords" CONTENT="CASE_INSENSITIVE_ORDER">\fR
\fB<META NAME="keywords" CONTENT="length()">\fR
\fB<META NAME="keywords" CONTENT="charAt()">\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-tag \fItagname\fR:Xaoptcmf:"\fItaghead\fR"
.RS 4
\fBjavadoc\fRコマンドがドキュメンテーション・コメント内の引数を1つ取る単純なカスタム・ブロック・タグ\fB@tagname\fRを解釈できるようにします。これにより、\fBjavadoc\fRコマンドはタグ名のスペルチェックを行うことができるので、ソース・コード内に存在するすべてのカスタム・タグについて、\fB\-tag\fRオプションを組み込むことが重要です。今回の実行では出力されないタグは、\fBX\fRを付けて無効にします。\fB\-tag\fRオプションは、タグの見出し\fItaghead\fRを太字で出力します。その次の行には、このオプションの1つの引数で指定したテキストが続きます。ブロック・タグと同様、この引数のテキストにはインライン・タグを含めることができます。このインライン・タグも解釈されます。出力は、引数を1つ取る標準のタグ(\fB@return\fRや\fB@author\fRなど)の出力とよく似ています。\fItaghead\fRの値を省略すると、\fBtagname\fRが見出しとして表示されます。
.sp
\fBタグの配置\fR:
\fBXaoptcmf\fR引数により、ソース・コード内でタグを配置できる場所が決まり、タグを無効にできるかどうか(\fBX\fRを使用して)が決まります。タグの配置位置を制限しない場合は\fBa\fRを指定します。それ以外の文字の組合せも可能です。
.sp
\fBX\fR
(タグの無効化)
.sp
\fBa\fR
(すべて)
.sp
\fBo\fR
(概要)
.sp
\fBp\fR
(パッケージ)
.sp
\fBt\fR
(タイプ、つまりクラスとインタフェース)
.sp
\fBc\fR
(コンストラクタ)
.sp
\fBm\fR
(メソッド)
.sp
\fBf\fR
(フィールド)
.sp
\fBシングル・タグの例\fR: ソース・コード内の任意の位置で使用できるタグのタグ・オプションの例を示します。\fB\-tag todo:a:"To Do:"\fR
.sp
\fB@todo\fRタグをコンストラクタ、メソッドおよびフィールドとのみ使用する場合、\fB\-tag todo:cmf:"To Do:"\fRを使用します。
.sp
最後のコロン(:)は、パラメータ区切り文字ではなく、見出しテキストの一部になっています。\fB@todo\fRタグを含む、ソース・コード用のいずれかのタグ・オプションを使用します。たとえば、\fB@todo The documentation for this method needs work\fRです。
.sp
\fBタグ名内のコロン\fR: タグ名内でコロンを使用する場合はバックスラッシュを使用してエスケープします。次のドキュメンテーション・コメントには、\fB\-tag ejb\e\e:bean:a:"EJB Bean:"\fRオプションを使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB * @ejb:bean\fR
\fB */\fR
 
.fi
.if n \{\
.RE
.\}
\fBタグ名のスペルチェック\fR: 一部の開発者が必ずしも出力しないカスタム・タグをソース・コード内に配置することがあります。この場合、ソース・コード内のすべてのタグをリストし、出力するタグを有効にし、出力しないタグを無効にする必要があります。\fBX\fRを指定するとタグは無効になります。指定しないと、タグは有効になります。これにより、\fBjavadoc\fRコマンドは、検出したタグが入力ミスなどによる不明タグであるかどうかを特定できます。このような場合に、\fBjavadoc\fRコマンドから警告が出力されます。すでに配置されている値に\fBX\fRを追加できます。こうしておけば、\fBX\fRを削除するのみでタグを有効にすることができます。たとえば、\fB@todo\fRタグを出力で抑制する場合、\fB\-tag todo:Xcmf:"To Do:"\fRを使用します。さらに簡単にする場合、\fB\-tag todo:X\fRを使用します。構文\fB\-tag todo:X\fRは、\fB@todo\fRタグがタグレットで定義されていても機能します。
.sp
\fBタグの順序\fR:
\fB\-ta\fR\fBg\fRおよび\fB\-taglet\fRオプションの順序によって、タグの出力順が決まります。カスタム・タグと標準タグを組み合せて使用することもできます。標準タグのタグ・オプションは、順序を決定するためだけのプレースホルダです。標準タグの名前のみを取ります。標準タグの小見出しは変更できません。これを次の例に示します。\fB\-tag\fRオプションを指定しないと、\fB\-tagle\fR\fBt\fRオプションの位置により、順序が決まります。タグが両方とも存在する場合、コマンドラインの最後にある方がその順序を決定します。これは、タグやタグレットがコマンドラインに指定された順番に処理されるためです。たとえば、\fB\-taglet\fRおよび\fB\-tag\fRオプションが名前\fBtodo\fR値を持つ場合、コマンドラインに最後に指定されたものが順序を決定します。
.sp
\fBタグの完全セットの例\fR: この例では、出力のParametersとThrowsの間にTo Doを挿入します。\fBX\fRを使用して、\fB@example\fRタグが、ソース・コード内の今回の実行では出力されないタグであることも指定します。\fB@argfile\fRタグを使用する場合は、次のように、引数ファイル内の別々の行にタグを配置できます(行の継続を示す文字は不要)。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-tag param\fR
\fB\-tag return\fR
\fB\-tag todo:a:"To Do:"\fR
\fB\-tag throws\fR
\fB\-tag see\fR
\fB\-tag example:X\fR
 
.fi
.if n \{\
.RE
.\}
\fBjavadoc\fRコマンドがドキュメンテーション・コメントを解析する際に検索されたタグのうち、標準タグでも、\fB\-tag\fRや\fB\-taglet\fRオプションで渡されたタグでもないものはすべて不明タグとみなされ、警告がスローされます。
.sp
標準タグは、最初、デフォルトの順序でリスト内に内部的に格納されます。\fB\-tag\fRオプションを使用すると、それらのタグはこのリストに追加されます。標準タグは、デフォルトの位置から移動されます。そのため、標準タグの\fB\-tag\fRオプションを省略すると、それはデフォルトの位置に配置されたままになります。
.sp
\fB競合の回避\fR: 固有の名前空間を作成するには、パッケージに使用されている\fBcom\&.mycompany\&.todo\fRという名前のように、ドットで区切られた名前を使用します。Oracleは、今後も名前にドットを含まない標準タグを作成します。ユーザーが作成したタグは、Oracleが定義する同じ名前のタグの動作をオーバーライドします。\fB@todo\fRという名前のタグまたはタグレットをユーザーが作成した場合、その後にOracleが同じ名前の標準タグを作成しても、そのタグまたはタグレットは常にユーザーが定義したのと同じ動作を保持します。
.sp
\fB注釈vs\&. Javadocタグ\fR: 一般に、追加する必要のあるマークアップが、ドキュメントに影響を与えたりドキュメントを生成したりするためのものである場合、そのマークアップはJavadocタグにします。それ以外の場合は注釈にします。JavadocツールでのDocコメントの記述方法のカスタム・タグと注釈に関する項
(http://www\&.oracle\&.com/technetwork/java/javase/documentation/index\-137868\&.html#annotations)を参照してください。
.sp
\fB\-taglet\fRオプションを使用して、より複雑なブロック・タグやカスタム・インライン・タグも作成できます。
.RE
.PP
\-taglet \fIclass\fR
.RS 4
そのタグのドキュメントの生成に使用するドックレットを起動するためのクラス・ファイルを指定します。\fBclass\fR値の完全修飾名を使用します。このタグレットは、カスタム・タグのテキスト引数の数も定義します。タグレットは、これらの引数を受け付け、処理し、出力を生成します。タグレットの例を使用した豊富なドキュメントについては、タグレットの概要
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javadoc/taglet/overview\&.html)を参照してください。
.sp
タグレットは、ブロックタグまたはインライン・タグで便利です。タグレットは任意の数の引数をとることができます。また、テキストを太字にする、箇条書きを作成する、テキストをファイルに書き出す、その他のプロセスを開始するなどのカスタム動作を実装できます。タグレットで指定できるのは、タグの配置場所と配置形式のみです。その他のすべての決定は、ドックレットによって行われます。タグレットを使用しても、包含クラスのリストからクラス名を削除するなどの処理は実行できません。ただし、タグのテキストをファイルに出力したり、別のプロセスをトリガーするなどの副作用は得られます。タグレットへのパスを指定するには、\fB\-tagletpath\fRオプションを使用します。次に、生成されるページのParametersとThrowsの間にTo Doタグレットを挿入する例を示します。または、\fB\-taglet\fRオプションをその\fB\-tag\fRオプションのかわりに使用することができますが、読み取りが困難になる可能性があります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-taglet com\&.sun\&.tools\&.doclets\&.ToDoTaglet\fR
\fB\-tagletpath /home/<USER>
\fB\-tag return\fR
\fB\-tag param\fR
\fB\-tag todo\fR
\fB\-tag throws\fR
\fB\-tag see\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-tagletpath \fItagletpathlist\fR
.RS 4
tagletクラス・ファイルを検索するための検索パスを指定します。\fBtagletpathlist\fRには、コロン(:)で区切って複数のパスを含めることができます。\fBjavadoc\fRコマンドは、指定されたパス以下のすべてのサブディレクトリを検索します。
.RE
.PP
\-docfilesubdirs
.RS 4
doc\-filesディレクトリのディープ・コピーを有効にします。宛先には、サブディレクトリとそのすべて内容が再帰的にコピーされます。たとえば、ディレクトリoc\-files/example/imagesとその内容がすべてコピーされます。ここでも、サブディレクトリを除外する指定が可能です。
.RE
.PP
\-excludedocfilessubdir \fIname1:name2\fR
.RS 4
指定された名前のdoc\-filesサブディレクトリをすべて除外します。これにより、SCCSとその他のソース・コード制御サブディレクトリのコピーを防ぎます。
.RE
.PP
\-noqualifier all | \fIpackagename1\fR:\fIpackagename2\&.\&.\&.\fR
.RS 4
出力されるクラス名から修飾パッケージ名を省略します。\fB\-noqualifier\fRオプションの引数は、\fBall\fR(すべてのパッケージ修飾子を省略)、または修飾子として削除するパッケージのコロン区切りリスト(ワイルドカードも可)、のいずれかとなります。クラスまたはインタフェース名が表示される位置からパッケージ名が削除されます。ソース・ファイルの処理を参照してください。
.sp
次の例では、すべてのパッケージ修飾子を省略します。\fB\-noqualifier all\fR
.sp
次の例では、\fBjava\&.lang\fRおよび\fBjava\&.io\fRパッケージ修飾子を省略します:
\fB\-noqualifier java\&.lang:java\&.io\fR。
.sp
次の例では、\fBjava\fRで始まるパッケージ修飾子および\fBcom\&.sun\fRサブパッケージを省略しますが、\fBjavax\fRは省略しません。\fB\-noqualifier java\&.*:com\&.sun\&.*\fR
.sp
パッケージ修飾子が前述の動作に従って表示される場合、名前は適切に短縮されます。「名前が表示される方法」を参照してください。このルールは、\fB\-noqualifier\fRオプションを使用するかどうかにかかわらず有効です。
.RE
.PP
\-notimestamp
.RS 4
タイムスタンプが抑制されます。各ページの先頭近くにある、生成されたHTML内のHTMLコメントでタイムスタンプが隠されます。\fB\-notimestamp\fRオプションは、\fBjavadoc\fRコマンドを2つのソース・ベースで実行し、それらの間の差分\fBdiff\fRを取得する場合に役立ちます。タイムスタンプによる\fBdiff\fRの発生を防ぐからです(そうでないとすべてのページで\fBdiff\fRになります)。タイムスタンプには\fBjavadoc\fRコマンドのリリース番号が含まれ、現在では、\fB<!\-\- Generated by javadoc (build 1\&.5\&.0_01) on Thu Apr 02 14:04:52 IST 2009 \-\->\fRのように表示されます。
.RE
.PP
\-nocomment
.RS 4
主説明およびすべてのタグを含むコメント本文全体を抑制し、宣言のみを生成します。このオプションにより、元は異なる目的のためだったソース・ファイルを再利用し、新しいプロジェクトの早い段階でスケルトンHTMLドキュメントを作成できるようになります。
.RE
.PP
\-sourcetab \fItablength\fR
.RS 4
ソース内で各タブが使用する空白文字の数を指定します。
.RE
.SH "コマンドライン引数ファイル"
.PP
\fBjavadoc\fRコマンドを短くしたり簡潔にしたりするために、\fBjavadoc\fRコマンドに対する引数(\fB\-J\fRオプションを除く)が入った1つ以上のファイルを指定することができます。このことを利用すれば、どのオペレーティング・システム上でも、任意の長さの\fBjavadoc\fRコマンドを作成できます。
.PP
引数ファイルには、\fBjavac\fRのオプションとソース・ファイル名を自由に組み合せて記述できます。ファイル内の各引数は、スペースまたは改行で区切ります。ファイル名に埋め込まれた空白がある場合、ファイル名全体を二重引用符で囲みます。
.PP
引数ファイル内のファイル名は、引数ファイルの位置ではなく、現在のディレクトリに相対的となります。これらのリストでは、ワイルドカード(\fB*\fR)は使用できません。たとえば、*\&.javaとは指定できません。アットマーク(@)を使用して、ファイルを再帰的に解釈することはできません。また、\fB\-J\fRオプションもサポートされていません。このオプションは起動ツールに渡されますが、起動ツールでは引数ファイルをサポートしていないからです。
.PP
\fBjavadoc\fRを実行するときに、各引数ファイルのパスとファイル名の先頭に@文字を付けて渡します。\fBjavadoc\fRコマンドは、アットマーク(@)文字で始まる引数を見つけると、そのファイルの内容を展開して引数リストに挿入します。
.PP
\fB例 1 \fR単一の引数ファイル
.RS 4
\fBargfile\fRという名前の1つの引数ファイルを使用して、すべての\fBjavadoc\fRコマンド引数を保持できます。\fBjavadoc @argfile\fR次の例に示すように、この引数ファイルには両方のファイルの内容が含まれています。
.RE
.PP
\fB例 2 \fR2つの引数ファイル
.RS 4
次のように、2つの引数ファイルを作成できます。\fBjavadoc\fRコマンドのオプション用に1つ、パッケージ名またはソース・ファイル名用に1つです。次のリストでは行継続文字を使用していません。
.sp
次の内容を含む、optionsという名前のファイルを作成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-d docs\-filelist \fR
\fB\-use \fR
\fB\-splitindex\fR
\fB\-windowtitle \*(AqJava SE 7 API Specification\*(Aq\fR
\fB\-doctitle \*(AqJava SE 7 API Specification\*(Aq\fR
\fB\-header \*(Aq<b>Java(TM) SE 7</b>\*(Aq\fR
\fB\-bottom \*(AqCopyright &copy; 1993\-2011 Oracle and/or its affiliates\&. All rights reserved\&.\*(Aq\fR
\fB\-group "Core Packages" "java\&.*"\fR
\fB\-overview /java/pubs/ws/1\&.7\&.0/src/share/classes/overview\-core\&.html\fR
\fB\-sourcepath /java/pubs/ws/1\&.7\&.0/src/share/classes\fR
 
.fi
.if n \{\
.RE
.\}
次の内容を含む、packagesという名前のファイルを作成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBcom\&.mypackage1\fR
\fBcom\&.mypackage2\fR
\fBcom\&.mypackage3\fR
 
.fi
.if n \{\
.RE
.\}
次のように、\fBjavadoc\fRコマンドを実行します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc @options @packages\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fB例 3 \fRパスを使用した引数ファイル
.RS 4
引数ファイルはパスを指定できますが、ファイル内のすべてのファイル名は、(\fBpath1\fRや\fBpath2\fRではなく)次のように現在の作業ディレクトリに相対的となります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc @path1/options @path2/packages\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fB例 4 \fRオプション引数
.RS 4
次に、\fBjavadoc\fRコマンドのオプションに対する引数を引数ファイルに格納する例を示します。長い引数を指定できるので、\fB\-bottom\fRオプションを使用します。次のようなテキスト引数を含む、bottomという名前のファイルを作成できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB<font size="\-1">\fR
\fB    <a href="http://bugreport\&.sun\&.com/bugreport/">Submit a bug or feature</a><br/>\fR
\fB    Copyright &copy; 1993, 2011, Oracle and/or its affiliates\&. All rights reserved\&. <br/>\fR
\fB    Oracle is a registered trademark of Oracle Corporation and/or its affiliates\&.\fR
\fB    Other names may be trademarks of their respective owners\&.</font>\fR
 
.fi
.if n \{\
.RE
.\}
\fBjavadoc\fRコマンドを次のように実行します。\fB javadoc \-bottom @bottom @packages\fR
.sp
\fB\-bottom\fRオプションを引数ファイルの最初に含めて、次のように\fBjavadoc\fRコマンドを実行することもできます。\fBjavadoc @bottom @packages\fR
.RE
.SH "JAVADOCコマンドの実行"
.PP
\fBjavadoc\fRコマンドのリリース番号は\fBjavadoc \-J\-version\fRオプションで特定できます。出力ストリームには標準ドックレットのリリース番号が含まれます。\fB\-quiet\fRオプションで無効にできます。
.PP
Java言語で記述されたプログラムから\fBjavadoc\fRコマンドを起動するには公開プログラマティック・インタフェースを使用します。このインタフェースは\fBcom\&.sun\&.tools\&.javadoc\&.Main\fRにあります(また\fBjavadoc\fRコマンドは再入可能です)。詳細は、標準ドックレット
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javadoc/standard\-doclet\&.html#runningprogrammatically)を参照してください。
.PP
次の手順では、標準HTMLドックレットを呼び出します。カスタム・ドックレットを呼び出すには、\fB\-doclet\fRおよび\fB\-docletpath\fRオプションを使用しますドックレットの概要
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javadoc/doclet/overview\&.html)を参照してください
.SS "簡単な例"
.PP
\fBjavadoc\fRコマンドは、パッケージ全体に対して実行することも、個々のソース・ファイルに対して実行することもできます。各パッケージ名は、それぞれのパッケージ名に対応するディレクトリ名を持ちます。
.PP
次の例では、ソース・ファイルは/home/<USER>/java/awt/*\&.javaにあります。生成先ディレクトリは/home/<USER>
.PP
1つ以上のパッケージのドキュメント化
.PP
パッケージをドキュメント化するには、そのパッケージのソース・ファイルを、そのパッケージと同じ名前のディレクトリ内に格納する必要があります。
.PP
パッケージ名が(\fBjava\&.awt\&.color\fRのようにドットで区切られた)複数の識別子から構成されている場合、後続の各識別子が下位のサブディレクトリ(ava/awt/colorなど)に対応している必要があります。
.PP
1つのパッケージのための複数のソース・ファイルを、異なる場所にあるそのような2つのディレクトリ・ツリーに分けて格納することもできます。ただし、その場合は\fB\-sourcepath\fRによってその両方の場所を指定する必要があります。たとえば、src1/java/awt/colorとsrc2/java/awt/color。
.PP
ディレクトリの変更(\fBcd\fRコマンドを使用)または\fB\-sourcepath\fRオプションにより、\fBjavadoc\fRコマンドを実行できます。次の例で両方の選択肢を示します。
.PP
\fB例 1 \fR1つ以上のパッケージから再帰的に実行
.RS 4
この例では\fBjavadoc\fRコマンドが任意のディレクトリから実行できるように、\fB\-sourcepath\fRを使用し、再帰的処理のために\fB\-subpackages\fR(1\&.4の新オプション)を使用します。これは、javaディレクトリのサブパッケージをたどりますが、\fBjava\&.net\fRと\fBjava\&.lang\fRをルートに持つパッケージは除外されます。\fBjava\&.lang\fRのサブパッケージである\fBjava\&.lang\&.ref\fRが除外される点に注意してください。また、他のパッケージ・ツリーを下方にたどるには、\fBjava:javax:org\&.xml\&.sax\fRのように、それらのパッケージの名前を\fB\-subpackages\fRの引数に追加します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-d /home/<USER>/home/<USER>
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fB例 2 \fRルートへの移動および明示的なパッケージの実行
.RS 4
完全修飾されたパッケージの親ディレクトリに移動します。次に、ドキュメント化する1つ以上のパッケージの名前を指定して\fBjavadoc\fRコマンドを実行します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBcd /home/<USER>/\fR
\fBjavadoc \-d /home/<USER>
 
.fi
.if n \{\
.RE
.\}
また、他のパッケージ・ツリーを下方にたどるには、j\fBava:javax:org\&.xml\&.sax\fRのように、それらのパッケージの名前を\fB\-subpackages\fRの引数に追加します。
.RE
.PP
\fB例 3 \fR1つのツリーの明示的なパッケージの任意のディレクトリから実行
.RS 4
この場合、現在のディレクトリがどこかは問題ではありません。\fBjavadoc\fRコマンドを実行し、最上位パッケージの親ディレクトリを指定して\fB\-sourcepath\fRオプションを使用します。ドキュメント化する1つ以上のパッケージの名前を指定します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-d /home/<USER>/home/<USER>
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fB例 4 \fR複数のツリーの明示的なパッケージの任意のディレクトリから実行
.RS 4
\fBjavadoc\fRコマンドを実行し、各ツリーのルートへのパスのコロン区切りリストを指定して\fB\-sourcepath\fRオプションを使用します。ドキュメント化する1つ以上のパッケージの名前を指定します。指定したパッケージのすべてのソース・ファイルが、1つのルート・ディレクトリの下に存在する必要はありませんが、ソース・パスで指定された場所のどこかで見つかる必要があります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-d /home/<USER>/home/<USER>/home/<USER>
 
.fi
.if n \{\
.RE
.\}
すべてのケースでj\fBava\&.awt\fRおよび\fBjava\&.awt\&.even\fRtパッケージ内の\fBpublic\fRおよび\fBprotected\fRクラスとインタフェースについて、HTML形式のドキュメントが生成され、指定された生成先ディレクトリにHTMLファイルが保存されます。2つ以上のパッケージが生成されているので、ドキュメントは、パッケージのリスト、クラスのリスト、およびメインのクラス・ページという3つのHTMLフレームを持つことになります。
.RE
.PP
1つ以上のクラスのドキュメント化
.PP
また、1つ以上のソース・ファイルを渡して、\fBjavadoc\fRコマンドを実行することもできます。\fBjavadoc\fRは、次の2つの方法のいずれかで実行できます。1つはディレクトリを変更する方法(\fBcd\fRを使用)、もう1つはソース・ファイルへのパスを完全に指定する方法です。相対パスは、現在のディレクトリを起点とします。ソース・ファイルを渡すときは、\fB\-sourcepath\fRオプションは無視されます。アスタリスク(*)のようなコマンドライン・ワイルドカードを使用すると、クラスのグループを指定できます。
.PP
\fB例 1 \fRソース・ディレクトリに変更
.RS 4
ソースを保持するディレクトリに変更します。次に、ドキュメント化する1つ以上のソース・ファイルの名前を指定して\fBjavadoc\fRコマンドを実行します。
.sp
この例では、\fBButton\fRクラスと\fBCanvas\fRクラス、および名前が\fBGraphics\fRで始まるクラスについて、HTML形式のドキュメントが生成されます。パッケージ名ではなくソース・ファイルが\fBjavadoc\fRコマンドに引数として渡されているので、ドキュメントは、クラスのリストとメイン・ページという2つのフレームを持つことになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBcd /home/<USER>/java/awt\fR
\fBjavadoc \-d /home/<USER>
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fB例 2 \fRパッケージのルート・ディレクトリに変更
.RS 4
これは、同じルートからの別のサブパッケージの個々のソース・ファイルをドキュメント化するのに役立ちます。パッケージのルート・ディレクトリに移り、各ソース・ファイルを、ルートからのパスとともに指定します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBcd /home/<USER>/\fR
\fBjavadoc \-d /home/<USER>/awt/Button\&.java java/applet/Applet\&.java\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fB例 3 \fR任意のディレクトリからのファイルのドキュメント化
.RS 4
この場合、現在のディレクトリがどこかは問題ではありません。ドキュメント化するソース・ファイルへの絶対パス(または現在のディレクトリからの相対パス)を指定して\fBjavadoc\fRコマンドを実行します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-d /home/<USER>/home/<USER>/java/awt/Button\&.java\fR
\fB/home/<USER>/java/awt/Graphics*\&.java\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
パッケージおよびクラスのドキュメント化
.PP
パッケージ全体と個々のクラスを同時に指定してドキュメント化することもできます。次に、前述の2つの例を組み合せた例を示します。\fB\-sourcepath\fRオプションは、パッケージへのパスに対しては使用できますが、個々のクラスへのパスに対しては使用できません。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-d /home/<USER>/home/<USER>
\fB/home/<USER>/java/applet/Applet\&.java\fR
 
.fi
.if n \{\
.RE
.\}
.SS "実際の例"
.PP
次のコマンドラインおよび\fBmakefile\fRバージョンの\fBjavadoc\fRコマンドをJavaプラットフォームAPIで実行します。Java SE 1\&.2で約1500個のpublicおよびprotectedクラスのドキュメントを生成するには、180MBのメモリーを使用します。どちらの例もオプションの引数で絶対パスが使用されているため、任意のディレクトリから同じ\fBjavadoc\fRコマンドを実行できます。
.PP
コマンドラインの例
.PP
次のコマンドは、一部のシェルに対して長すぎる可能性があります。この制限を回避するには、コマンドライン引数ファイルを使用します。または、シェル・スクリプトを記述します。
.PP
この例では、\fBpackages\fRは処理するパッケージを含む名前で、\fBjava\&.applet\fR
\fBjava\&.lang\fRなどです。各オプションの、一重引用符で囲まれた引数の内側には、改行文字を挿入できません。たとえば、この例をコピー・アンド・ペーストする場合は、\fB\-bottom\fRオプションから改行文字を削除してください。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-sourcepath /java/jdk/src/share/classes \e\fR
\fB\-overview /java/jdk/src/share/classes/overview\&.html \e\fR
\fB\-d /java/jdk/build/api \e\fR
\fB\-use \e\fR
\fB\-splitIndex \e\fR
\fB\-windowtitle \*(AqJava Platform, Standard Edition 7 API Specification\*(Aq \e\fR
\fB\-doctitle \*(AqJava Platform, Standard Edition 7 API Specification\*(Aq \e\fR
\fB\-header \*(Aq<b>Java(TM) SE 7</b>\*(Aq \e\fR
\fB\-bottom \*(Aq<font size="\-1">\fR
\fB<a href="http://bugreport\&.sun\&.com/bugreport/">Submit a bug or feature</a><br/>\fR
\fBCopyright &copy; 1993, 2011, Oracle and/or its affiliates\&. All rights reserved\&.<br/>\fR
\fBOracle is a registered trademark of Oracle Corporation and/or its affiliates\&.\fR
\fBOther names may be trademarks of their respective owners\&.</font>\*(Aq \e\fR
\fB\-group "Core Packages" "java\&.*:com\&.sun\&.java\&.*:org\&.omg\&.*" \e\fR
\fB\-group "Extension Packages" "javax\&.*" \e\fR
\fB\-J\-Xmx180m \e  \fR
\fB@packages\fR
 
.fi
.if n \{\
.RE
.\}
.PP
プログラマティック・インタフェース
.PP
Javadoc Access APIでは、新しいプロセスを実行しなくても、JavadocツールをJavaアプリケーションから直接起動できます。
.PP
たとえば、次の文はコマンド\fBjavadoc \-d /home/<USER>/home/<USER>
.sp
.if n \{\
.RS 4
.\}
.nf
\fBimport javax\&.tools\&.DocumentationTool;\fR
\fBimport javax\&.tools\&.ToolProvider;\fR
 
\fBpublic class JavaAccessSample{\fR
\fB    public static void main(String[] args){\fR
\fB        DocumentationTool javadoc = ToolProvider\&.getSystemDocumentationTool();\fR
\fB        int rc = javadoc\&.run( null, null, null,\fR
\fB                 "\-d", "/home/<USER>",\fR
\fB                 "\-sourcepath", "home/src",\fR
\fB                 "\-subpackages", "java",\fR
\fB                 "\-exclude", "java\&.net:java\&.lang",\fR
\fB                 "com\&.example");\fR
\fB     }\fR
\fB }\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBrun\fRメソッドの最初の3つの引数は、入力、標準出力、および標準エラー・ストリームを指定します。\fBNull\fRは\fBSystem\&.in\fR、\fBSystem\&.out\fRおよび\fBSystem\&.err\fRそれぞれのデフォルト値です。
.SS "makefileの例"
.PP
ここでは、GNU
\fBmakefile\fRの例を示します。\fBmakefile\fRの引数は、一重引用符で囲みます。Windows
\fBmakefile\fRの例については、Javadoc FAQの\fBmakefiles\fRのセクション
(http://www\&.oracle\&.com/technetwork/java/javase/documentation/index\-137483\&.html#makefiles)を参照してください
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavadoc \-sourcepath $(SRCDIR)              \e   /* Sets path for source files   */\fR
\fB        \-overview $(SRCDIR)/overview\&.html  \e   /* Sets file for overview text  */\fR
\fB        \-d /java/jdk/build/api             \e   /* Sets destination directory   */\fR
\fB        \-use                               \e   /* Adds "Use" files             */\fR
\fB        \-splitIndex                        \e   /* Splits index A\-Z             */\fR
\fB        \-windowtitle $(WINDOWTITLE)        \e   /* Adds a window title          */\fR
\fB        \-doctitle $(DOCTITLE)              \e   /* Adds a doc title             */\fR
\fB        \-header $(HEADER)                  \e   /* Adds running header text     */\fR
\fB        \-bottom $(BOTTOM)                  \e   /* Adds text at bottom          */\fR
\fB        \-group $(GROUPCORE)                \e   /* 1st subhead on overview page */\fR
\fB        \-group $(GROUPEXT)                 \e   /* 2nd subhead on overview page */\fR
\fB        \-J\-Xmx180m                         \e   /* Sets memory to 180MB         */\fR
\fB        java\&.lang java\&.lang\&.reflect        \e   /* Sets packages to document    */\fR
\fB        java\&.util java\&.io java\&.net         \e\fR
\fB        java\&.applet\fR
\fB        \fR
\fBWINDOWTITLE = \*(AqJava(TM) SE 7 API Specification\*(Aq\fR
\fBDOCTITLE = \*(AqJava(TM) Platform Standard Edition 7 API Specification\*(Aq\fR
\fBHEADER = \*(Aq<b>Java(TM) SE 7</font>\*(Aq\fR
\fBBOTTOM = \*(Aq<font size="\-1">\fR
\fB      <a href="http://bugreport\&.sun\&.com/bugreport/">Submit a bug or feature</a><br/>\fR
\fB      Copyright &copy; 1993, 2011, Oracle and/or its affiliates\&. All rights reserved\&.<br/>\fR
\fB      Oracle is a registered trademark of Oracle Corporation and/or its affiliates\&.\fR
\fB      Other names may be trademarks of their respective owners\&.</font>\*(Aq\fR
\fBGROUPCORE = \*(Aq"Core Packages" "java\&.*:com\&.sun\&.java\&.*:org\&.omg\&.*"\*(Aq\fR
\fBGROUPEXT  = \*(Aq"Extension Packages" "javax\&.*"\*(Aq\fR
\fBSRCDIR = \*(Aq/java/jdk/1\&.7\&.0/src/share/classes\*(Aq\fR
 
.fi
.if n \{\
.RE
.\}
.SS "注意"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-windowtitle\fRオプションを省略すると、\fBjavadoc\fRコマンドによってドキュメント・タイトルがウィンドウ・タイトルにコピーされます。\fB\-windowtitle\fRオプションのテキストは、\fB\-doctitle\fRオプションと同じですが、HTMLタグは含まれません。これは、HTMLタグが、ウィンドウ・タイトル内にそのままのテキストとして表示されるのを防ぐためです。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-footer\fRオプションを省略すると、\fBjavadoc\fRコマンドによってヘッダー・テキストがフッターにコピーされます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
この例では必要ありませんが、\fB\-classpath\fRと\fB\-link\fRも重要なオプションです。
.RE
.SH "一般的なトラブルシューティング"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBjavadoc\fRコマンドは有効なクラス名を含むファイルのみを読み取ります。\fBjavadoc\fRコマンドがファイルの内容を正しく読み取っていない場合は、クラス名が有効であることを確認します。ソース・ファイルの処理を参照してください。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
一般的なbugおよびトラブルシューティングのヒントについては、Javadoc FAQ
(http://www\&.oracle\&.com/technetwork/java/javase/documentation/index\-137483\&.html)を参照してください。
.RE
.SH "エラーと警告"
.PP
エラーおよび警告メッセージには、ファイル名と宣言行(ドキュメンテーション・コメント内の特定の行ではない)の行番号が含まれます。
.PP
たとえば、メッセージ\fB「エラー: Class1\&.javaを読み込めません」\fRは、\fBjavadoc\fRコマンドが\fBClass1\&.jav\fR\fBa\fRを現在のディレクトリにロードしようとしていることを意味します。クラス名はそのパス(絶対または相対)で表示されます。
.SH "環境"
.PP
CLASSPATH
.RS 4
\fBCLASSPATH\fRは、\fBjavadoc\fRコマンドがユーザー・クラス・ファイルの検出に使用するパスを提供する環境変数です。この環境変数は、\fB\-classpath\fRオプションによってオーバーライドされます。ディレクトリはセミコロン(Windowsの場合)またはコロン(Oracle Solarisの場合)で区切ります。
.sp
\fBWindowsの例\fR:
\fB\&.;C:\eclasses;C:\ehome\ejava\eclasses\fR
.sp
\fBOracle Solarisの例\fR:
\fB\&.:/home/<USER>/usr/local/java/classes\fR
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javac(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
java(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jdb(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javah(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javap(1)
.RE
.SH "関連ドキュメント"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Javadocテクノロジ
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javadoc/index\&.html)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
クラスの検出方法
(http://docs\&.oracle\&.com/javase/8/docs/technotes/tools/findingclasses\&.html)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
JavadocツールでのDocコメントの記述方法
(http://www\&.oracle\&.com/technetwork/java/javase/documentation/index\-137868\&.html)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
URLメモ、Uniform Resource Locators
(http://www\&.ietf\&.org/rfc/rfc1738\&.txt)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
HTML標準、HTML Document Representation (4197265および4137321)
(http://www\&.w3\&.org/TR/REC\-html40/charset\&.html#h\-5\&.2\&.2)
.RE
.br
'pl 8.5i
'bp
