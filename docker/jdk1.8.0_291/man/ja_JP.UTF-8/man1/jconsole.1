'\" t
.\" Copyright (c) 2004, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jconsole
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Javaトラブルシューティング、プロファイリング、モニタリングおよび管理ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jconsole" "1" "2013年11月21日" "JDK 8" "Javaトラブルシューティング、プロファイリング、モニタリン"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jconsole \- Javaアプリケーションを監視および管理するためのグラフィカル・コンソールを開始します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjconsole\fR [ \fIoptions\fR ] [ connection \&.\&.\&. ]
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
connection = \fIpid\fR | \fIhost\fR:\fIport\fR | \fIjmxURL\fR
.RS 4
\fBpid\fR値はローカルのJava Virtual Machine (JVM)のプロセスIDです。JVMは\fBjconsole\fRコマンドを実行しているユーザーIDと同じユーザーIDで実行する必要があります。\fBhost:port\fR値はJVMが動作しているホスト・システムの名前と、JVMが開始したときにシステム・プロパティ\fBcom\&.sun\&.management\&.jmxremote\&.port\fRで指定したポート番号です。\fBjmxUrl\fR値は、JMXServiceURLで記述されている、接続されるJMXエージェントのアドレスです。
.sp
\fBconnection\fRパラメータの詳細は、JMXテクノロジを使用したモニタリングおよび管理
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/management/agent\&.html)を参照してください
.sp
\fBJMXServiceURL\fRクラスの説明(
http://docs\&.oracle\&.com/javase/8/docs/api/javax/management/remote/JMXServiceURL\&.html)も参照してください
.RE
.SH "説明"
.PP
\fBjconsole\fRコマンドは、ローカル・マシンまたはリモート・マシン上のJavaアプリケーションと仮想マシンの監視と管理を行うグラフィカル・コンソール・ツールを起動します。
.PP
Windows上では、\fBjconsole\fRコマンドはコンソール・ウィンドウと関連付けられていません。ただし、\fBjconsole\fRコマンドが失敗すると、エラー情報を示すダイアログ・ボックスが表示されます。
.SH "オプション"
.PP
\-interval\fI=n\fR
.RS 4
更新間隔を\fIn\fR秒に設定します(デフォルトは4秒)。
.RE
.PP
\-notile
.RS 4
最初にウィンドウをタイリングしません(複数接続の場合)。
.RE
.PP
\-pluginpath \fIplugins\fR
.RS 4
\fBJConsole\fRプラグインを検索するディレクトリまたはJARファイルのリストを指定します。\fIplugins\fRパスには\fBMETA\-INF/services/com\&.sun\&.tools\&.jconsole\&.JConsolePlugin\fRという名前のプロバイダ構成ファイルを含んでいる必要があり、これにはプラグインごとに1行が含まれています。その行は\fBcom\&.sun\&.tools\&.jconsole\&.JConsolePlugin\fRクラスを実装しているクラスの完全修飾クラス名を指定します。
.RE
.PP
\-version
.RS 4
リリース情報を表示して終了します。
.RE
.PP
\-help
.RS 4
ヘルプ・メッセージを表示します。
.RE
.PP
\-J\fIflag\fR
.RS 4
\fBjconsole\fRコマンドを実行したJVMに\fBflag\fRを渡します。
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
JConsoleの使用
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/management/jconsole\&.html)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
JMXテクノロジを使用したモニタリングおよび管理

(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/management/agent\&.html)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBJMXServiceURL\fRクラスの説明(
http://docs\&.oracle\&.com/javase/8/docs/api/javax/management/remote/JMXServiceURL\&.html)
.RE
.br
'pl 8.5i
'bp
