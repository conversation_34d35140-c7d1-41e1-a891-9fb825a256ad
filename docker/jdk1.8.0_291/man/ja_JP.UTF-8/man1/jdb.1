'\" t
.\" Copyright (c) 1995, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jdb
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: 基本ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jdb" "1" "2013年11月21日" "JDK 8" "基本ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jdb \- Javaプラットフォーム・プログラムのbugを検出および修正します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjdb\fR [\fIoptions\fR] [\fIclassname\fR] [\fIarguments\fR]
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIclass\fRname
.RS 4
デバッグするメイン・クラスの名前。
.RE
.PP
\fIarguments\fR
.RS 4
classの\fBmain()\fRメソッドに渡す引数。
.RE
.SH "説明"
.PP
Javaデバッガ(JDB)は、Javaクラス用の簡単なコマンド行デバッガです。\fBjdb\fRコマンドとそのオプションはJDBを呼び出します。\fBjdb\fRコマンドは、JDBA (Java Platform Debugger Architecture)を視覚的に実行し、ローカルまたはリモートのJava Virtual Machine (JVM)の検査とデバッグを行うことができます。Java Platform Debugger Architecture (JDBA)
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/jpda/index\&.html)を参照してください。
.SS "JDBセッションの開始"
.PP
JDBセッションを開始するには様々な方法があります。最も頻繁に使用されるのは、デバッグするアプリケーションのメイン・クラスを使用して、JDBから新しいJVMを起動する方法です。コマンド行で、\fBjava\fRコマンドのかわりに\fBjdb\fRコマンドを入力します。たとえば、アプリケーションのメイン・クラスが\fBMyClass\fRの場合は、JDB環境でデバッグするときに次のコマンドを使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjdb MyClass\fR
 
.fi
.if n \{\
.RE
.\}
.PP
この方法で起動すると、\fBjdb\fRコマンドは、指定されたパラメータを使用して2つ目のJVMを呼び出します。次に、指定されたクラスをロードして、クラスの最初の命令を実行する前にJVMを停止させます。
.PP
\fBjdb\fRコマンドのもう1つの使用方法は、すでに実行中のJVMにjdbを接続することです。\fBjdb\fRコマンドが接続するVMを、その実行中に起動するための構文を次に示します。これは、インプロセス・デバッグ用ライブラリをロードし、接続の種類を指定します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava \-agentlib:jdwp=transport=dt_socket,server=y,suspend=n MyClass\fR
 
.fi
.if n \{\
.RE
.\}
.PP
次のコマンドを使用して、\fBjdb\fRコマンドをJVMに接続できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjdb \-attach 8000\fR
 
.fi
.if n \{\
.RE
.\}
.PP
この場合、\fBjdb\fRコマンドは新しいJVMを起動するかわりに既存のJVMに接続されるため、\fBjdb\fRコマンド行に\fBMyClass\fR引数は指定しません。
.PP
デバッガをJVMに接続するには他にも様々な方法があり、すべて\fBjdb\fRコマンドでサポートされています。接続オプションについては、Java Platform Debugger Architectureのドキュメントを参照してください。
.SS "基本jdbコマンド"
.PP
基本的な\fBjdb\fRコマンドの一覧を示します。JDBがサポートするコマンドはこれ以外にもあり、それらは\fB\-help\fRオプションを使用して表示できます。
.PP
helpまたは?
.RS 4
\fBhelp\fRまたは\fB?\fRコマンドは、認識されたコマンドのリストに簡潔な説明を付けて表示します。
.RE
.PP
run
.RS 4
JDBを起動してブレークポイントを設定したあとに、\fBrun\fRコマンドを使用して、デバッグするアプリケーションの実行を開始できます。\fBrun\fRコマンドは、既存のVMに接続している場合とは異なり、デバッグするアプリケーションが\fBjdb\fRから起動したときにのみ使用できます。
.RE
.PP
cont
.RS 4
ブレークポイント、例外、またはステップ実行の後で、デバッグするアプリケーションの実行を継続します。
.RE
.PP
print
.RS 4
Javaオブジェクトおよびプリミティブ値を表示します。プリミティブ型の変数またはフィールドの場合には、実際の値が出力されます。オブジェクトの場合には、短い説明が出力されます。オブジェクトの詳細を取得する方法を探すには、dumpコマンドを参照してください。
.sp
\fB注意:\fR
ローカル変数を表示するには、含んでいるクラスが\fBjavac \-g\fRオプションを使用してコンパイルされている必要があります。
.sp
\fBprint\fRコマンドは、次に示すような、メソッド呼出しを使用したものなど、多くの簡単なJava式をサポートします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBprint MyClass\&.myStaticField\fR
\fBprint myObj\&.myInstanceField\fR
\fBprint i + j + k (i, j, k are primities and either fields or local variables)\fR
\fBprint myObj\&.myMethod() (if myMethod returns a non\-null)\fR
\fBprint new java\&.lang\&.String("Hello")\&.length()\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
dump
.RS 4
プリミティブ値の場合、\fBdump\fRコマンドは\fBprint\fRコマンドと同一です。オブジェクトの場合、\fBdump\fRコマンドでは、オブジェクト内に定義されている各フィールドの現在の値が出力されます。staticフィールドとinstanceフィールドが出力されます。\fBdump\fRコマンドでは、\fBprint\fRコマンドと同じ式がサポートされます。
.RE
.PP
threads
.RS 4
現在実行中のスレッドを一覧表示します。スレッドごとに、名前と現在の状態、および他のコマンドに使用できるインデックスが出力されます。この例では、スレッド・インデックスは4であり、スレッドは\fBjava\&.lang\&.Thread\fRのインスタンスで、スレッドの名前は\fBmain\fRであり、現在実行中です。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB4\&. (java\&.lang\&.Thread)0x1 main      running\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
thread
.RS 4
現在のスレッドにするスレッドを選択します。多くの\fBjdb\fRコマンドは、現在のスレッドの設定に基づいて実行されます。スレッドは、threadsコマンドで説明したスレッド・インデックスとともに指定します。
.RE
.PP
where
.RS 4
引数を指定しないで\fBwhere\fRコマンドを実行すると、現在のスレッドのスタックがダンプされます。\fBwhere\fR
\fBall\fRコマンドは、現在のスレッド・グループにあるスレッドのスタックをすべてダンプします。\fBwhere\fR
\fBthreadindex\fRコマンドは、指定されたスレッドのスタックをダンプします。
.sp
現在のスレッドが(ブレークポイントか\fBsuspend\fRコマンドによって)中断している場合は、ローカル変数とフィールドは\fBprint\fRコマンドと\fBdump\fRコマンドで表示できます。\fBup\fRコマンドと\fBdown\fRコマンドで、どのスタック・フレームを現在のスタック・フレームにするかを選ぶことができます。
.RE
.SS "ブレークポイント"
.PP
ブレークポイントは、行番号またはメソッドの最初の命令でJDBに設定できます。次に例を示します。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
コマンド\fBstop at MyClass:22\fRは、\fBMyClass\fRが含まれるソース・ファイルの22行目の最初の命令にブレークポイントを設定します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
コマンド\fBstop in java\&.lang\&.String\&.length\fRは、メソッド\fBjava\&.lang\&.String\&.length\fRの先頭にブレークポイントを設定します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
コマンド\fBstop in MyClass\&.<clinit>\fRは、\fB<clinit>\fRを使用して、\fBMyClass\fRの静的初期化コードを特定します。
.RE
.PP
メソッドがオーバーロードされている場合には、メソッドの引数の型も指定して、ブレークポイントに対して適切なメソッドが選択されるようにする必要があります。たとえば、\fBMyClass\&.myMethod(int,java\&.lang\&.String)\fRまたは\fBMyClass\&.myMethod()\fRと指定します。
.PP
\fBclear\fRコマンドは、\fBclear MyClass:45\fRのような構文を使用してブレークポイントを削除します。\fBclear\fRを使用するか、引数を指定しないで\fBstop\fRコマンドを使用すると、現在設定されているすべてのブレークポイントが表示されます。\fBcont\fRコマンドは実行を継続します。
.SS "ステップ実行"
.PP
\fBstep\fRコマンドは、現在のスタック・フレームまたは呼び出されたメソッド内で、次の行を実行します。\fBnext\fRコマンドは、現在のスタック・フレームの次の行を実行します。
.SS "例外"
.PP
スローしているスレッドの呼出しスタック上のどこにも\fBcatch\fR文がない場合に例外が発生すると、JVMは通常、例外トレースを出力して終了します。ただし、JDB環境で実行している場合は、違反のスロー時にJDBに制御が戻ります。次に、\fBjdb\fRコマンドを使用して例外の原因を診断します。
.PP
たとえば、\fBcatch java\&.io\&.FileNotFoundException\fRまたは\fBcatch\fR
\fBmypackage\&.BigTroubleException\fRのように\fBcatch\fRコマンドを使用すると、デバッグされたアプリケーションは、他の例外がスローされたときに停止します。例外が特定のクラスまたはサブクラスのインスタンスの場合は、アプリケーションは例外がスローされた場所で停止します。
.PP
\fBignore\fRコマンドを使用すると、以前の\fBcatch\fRコマンドの効果が無効になります。\fBignore\fRコマンドでは、デバッグされるJVMは特定の例外を無視せず、デバッガのみを無視します。
.SH "オプション"
.PP
コマンド行で\fBjava\fRコマンドのかわりに\fBjdb\fRコマンドを使用する場合、\fBjdb\fRコマンドは、\fB\-D\fR、\fB\-classpath\fR、\fB\-X\fRなど、\fBjava\fRコマンドと同じ数のオプションを受け入れます。\fBjdb\fRコマンドは、その他に次のリストにあるオプションを受け入れます。
.PP
デバッグを行うJVMにデバッガを接続するための代替機構を提供するために、その他のオプションがサポートされています。これらの接続の代替に関する詳細なドキュメントは、Java Platform Debugger Architecture (JPDA)
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/jpda/index\&.html)を参照してください
.PP
\-help
.RS 4
ヘルプ・メッセージを表示します。
.RE
.PP
\-sourcepath \fIdir1:dir2: \&. \&. \&.\fR
.RS 4
指定されたパスを使用して、ソース・ファイルを検索します。このオプションが指定されていない場合は、デフォルト・パスのドット(\&.)が使用されます。
.RE
.PP
\-attach \fIaddress\fR
.RS 4
デフォルトの接続機構を使用して、実行中のJVMにデバッガを接続します。
.RE
.PP
\-listen \fIaddress\fR
.RS 4
実行中のJVMが標準のコネクタを使用して指定されたアドレスに接続するのを待機します。
.RE
.PP
\-launch
.RS 4
デバッグするアプリケーションをJDBの起動後ただちに起動します。\fB\-launch\fRオプションにより、\fBrun\fRコマンドが必要なくなります。デバッグするアプリケーションは、起動後、初期アプリケーション・クラスがロードされる直前に停止します。その時点で、必要なブレークポイントを設定し、\fBcont\fRコマンドを使用して実行を継続できます。
.RE
.PP
\-listconnectors
.RS 4
このJVMで利用できるコネクタを一覧表示します。
.RE
.PP
\-connect connector\-name:\fIname1=value1\fR
.RS 4
一覧表示された引数の値と指定のコネクタを使用してターゲットJVMに接続します。
.RE
.PP
\-dbgtrace [\fIflags\fR]
.RS 4
\fBjdb\fRコマンドのデバッグの情報を出力します。
.RE
.PP
\-tclient
.RS 4
Java HotSpot VMクライアント内でアプリケーションを実行します。
.RE
.PP
\-tserver
.RS 4
Java HotSpot VMサーバー内でアプリケーションを実行します。
.RE
.PP
\-J\fIoption\fR
.RS 4
JVMに\fBoption\fRを渡します。optionには、Javaアプリケーション起動ツールのリファレンス・ページに記載されているオプションを1つ指定します。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。java(1)を参照してください。
.RE
.SH "デバッグ対象のプロセスに転送されるオプション"
.PP
\-v \-verbose[:\fIclass\fR|gc|jni]
.RS 4
冗長モードにします。
.RE
.PP
\-D\fIname\fR=\fIvalue\fR
.RS 4
システム・プロパティを設定します。
.RE
.PP
\-classpath \fIdir\fR
.RS 4
クラスを検索するための、コロンで区切って指定されたディレクトリのリスト。
.RE
.PP
\-X\fIoption\fR
.RS 4
非標準ターゲットJVMオプションです。
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javac(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
java(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javah(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javap(1)
.RE
.br
'pl 8.5i
'bp
