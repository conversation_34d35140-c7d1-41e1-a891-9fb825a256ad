'\" t
.\" Copyright (c) 1994, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jjs
.\" Language: Japanese
.\" Date: 2015年3月3日
.\" SectDesc: 基本ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jjs" "1" "2015年3月3日" "JDK 8" "基本ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jjs \- Nashornエンジンを呼び出します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\fBjjs\fR\fR\fB [\fR\fB\fIoptions\fR\fR\fB] [\fR\fB\fIscript\-files\fR\fR\fB] [\-\- \fR\fB\fIarguments\fR\fR\fB]\fR
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
空白文字で区切られた、\fBjjs\fRコマンドの1つ以上のオプション。詳細は、オプションを参照してください。
.RE
.PP
\fIscript\-files\fR
.RS 4
空白文字で区切られた、Nashornを使用して解釈する1つ以上のスクリプト・ファイル。ファイルが指定されない場合は、対話型シェルが起動されます。
.RE
.PP
\fIarguments\fR
.RS 4
二重ハイフン・マーカー(\fB\-\-\fR)の後のすべての値が、引数としてスクリプトまたは対話型シェルに渡されます。これらの値には\fBarguments\fRプロパティを使用してアクセスできます(Example 3を参照してください)。
.RE
.SH "説明"
.PP
\fBjjs\fRコマンド行ツールを使用してNashornエンジンを呼び出します。これを使用して、1つまたは複数のスクリプト・ファイルを解釈したり、対話型シェルを実行することができます。
.SH "オプション"
.PP
\fBjjs\fRコマンドのオプションはスクリプトがNashornによって解釈される条件を制御します。
.PP
\-ccs=\fIsize\fR
.br
\-\-class\-cache\-size=\fIsize\fR
.RS 4
クラス・キャッシュ・サイズをバイト単位で設定します。キロバイト(KB)を示すために\fBk\fRまたは\fBK\fRの文字を追加し、メガバイト(MB)を示すために\fBm\fRまたは\fBM\fRの文字を追加し、ギガバイト(GB)を示すために\fBg\fRまたは\fBG\fRを追加します。デフォルトでは、クラス・キャッシュ・サイズは50バイトに設定されます。次の例は、1024バイト(1 KB)に設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-css=100\fR
\fB\-css=1k\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-co
.br
\-\-compile\-only
.RS 4
スクリプトを実行せずにコンパイルします。
.RE
.PP
\-cp \fIpath\fR
.br
\-classpath \fIpath\fR
.RS 4
サポートするクラスへのパスを指定します。複数のパスを設定するには、このオプションを繰り返すか、または各パスをコロン(:)で区切ります。
.RE
.PP
\-D\fIname\fR=\fIvalue\fR
.RS 4
プロパティ名に値を割り当てることで、スクリプトに渡すシステム・プロパティを設定します。次の例で、対話型モードでNashornを呼び出して、\fBmyValue\fRを\fBmyKey\fRという名前のプロパティに割り当てる方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB>> \fR\fB\fBjjs \-DmyKey=myValue\fR\fR
\fBjjs> \fR\fB\fBjava\&.lang\&.System\&.getProperty("myKey")\fR\fR
\fBmyValue\fR
\fBjjs>\fR
 
.fi
.if n \{\
.RE
.\}
このオプションを繰り返し使用すると、複数のプロパティを設定できます。
.RE
.PP
\-d=\fIpath\fR
.br
\-\-dump\-debug\-dir=\fIpath\fR
.RS 4
クラス・ファイルをダンプするディレクトリへのパスを指定します。
.RE
.PP
\-\-debug\-lines
.RS 4
クラス・ファイル内の行番号表を生成します。デフォルトでは、このオプションは有効になっています。無効にするには\fB\-\-debug\-lines=false\fRを指定します。
.RE
.PP
\-\-debug\-locals
.RS 4
クラス・ファイル内のローカル変数を生成します。
.RE
.PP
\-doe
.br
\-\-dump\-on\-error
.RS 4
エラーが発生したときに、フル・スタック・トレースを提供します。デフォルトでは、簡単なエラー・メッセージのみが出力されます。
.RE
.PP
\-\-early\-lvalue\-error
.RS 4
無効な左辺値式が早期エラーとして(つまり、コードが解析されるときに)報告されます。デフォルトでは、このオプションは有効になっています。無効にするには\fB\-\-early\-lvalue\-error=false\fRを指定します。無効な場合、無効な左辺値式はコードが実行されるまで報告されません。
.RE
.PP
\-\-empty\-statements
.RS 4
空の文をJavaの抽象構文ツリーに保存します。
.RE
.PP
\-fv
.br
\-\-fullversion
.RS 4
完全なNashornバージョン文字列を出力します。
.RE
.PP
\-\-function\-statement\-error
.RS 4
関数の宣言が文として使用されるとエラー・メッセージが出力されます。
.RE
.PP
\-\-function\-statement\-warning
.RS 4
関数の宣言が文として使用されると警告メッセージが出力されます。
.RE
.PP
\-fx
.RS 4
スクリプトをJavaFXアプリケーションとして起動します。
.RE
.PP
\-h
.br
\-help
.RS 4
オプションのリストとその説明を出力します。
.RE
.PP
\-J\fIoption\fR
.RS 4
指定した\fBjava\fR起動オプションをJVMに渡します。次の例で、対話型モードでNashornを呼び出して、JVMによって使用される最大メモリーを4 GBに設定する方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB>> \fR\fB\fBjjs \-J\-Xmx4g\fR\fR
\fBjjs> \fR\fB\fBjava\&.lang\&.Runtime\&.getRuntime()\&.maxMemory()\fR\fR
\fB3817799680\fR
\fBjjs>\fR
 
.fi
.if n \{\
.RE
.\}
このオプションを繰り返し使用すると、複数の\fBjava\fRコマンド・オプションを渡すことができます。
.RE
.PP
\-\-language=[es5]
.RS 4
ECMAScript言語バージョンを指定します。デフォルトのバージョンはES5です。
.RE
.PP
\-\-lazy\-compilation
.RS 4
レイジー・コード生成戦略(つまり、スクリプト全体が一度にコンパイルされない)を有効にします。このオプションは試験的なものです。
.RE
.PP
\-\-loader\-per\-compile
.RS 4
コンパイルごとに新しいクラス・ローダーを作成します。デフォルトでは、このオプションは有効になっています。無効にするには\fB\-\-loader\-per\-compile=false\fRを指定します。
.RE
.PP
\-\-log=\fIsubsystem\fR:\fIlevel\fR
.RS 4
指定されたサブシステムに対して、特定のレベルでロギングを実行します。カンマで区切って複数のサブシステムのロギング・レベルを指定できます。次に例を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-\-log=fields:finest,codegen:info\fR
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-\-optimistic\-types=[true|false]
.RS 4
再コンパイルの最適化を解除してオプティミスティック・タイプ仮定を有効または無効にします。オプティミスティック・タイプを使用した実行によって最終的な速度が向上しますが、ウォームアップ時間が増える場合があります。
.RE
.PP
\-\-package=\fIname\fR
.RS 4
生成されたクラス・ファイルを追加するパッケージを指定します。
.RE
.PP
\-\-parse\-only
.RS 4
コンパイルせずにコードを解析します。
.RE
.PP
\-\-print\-ast
.RS 4
抽象構文ツリーを出力します。
.RE
.PP
\-\-print\-code
.RS 4
バイトコードを出力します。
.RE
.PP
\-\-print\-lower\-ast
.RS 4
掘り下げた抽象構文ツリーを出力します。
.RE
.PP
\-\-print\-lower\-parse
.RS 4
掘り下げた解析ツリーを出力します。
.RE
.PP
\-\-print\-no\-newline
.RS 4
その他の\fB\-\-print*\fRオプションで強制的に1行で出力します。
.RE
.PP
\-\-print\-parse
.RS 4
解析ツリーを出力します。
.RE
.PP
\-\-print\-symbols
.RS 4
記号表を出力します。
.RE
.PP
\-pcs
.br
\-\-profile\-callsites
.RS 4
呼び出しサイトのプロファイル・データをダンプします。
.RE
.PP
\-scripting
.RS 4
シェルのスクリプト機能を有効にします。
.RE
.PP
\-\-stderr=\fIfilename\fR|\fIstream\fR|\fItty\fR
.RS 4
標準エラー・ストリームを指定したファイル、ストリーム(たとえば\fBstdout\fR)に、またはテキスト端末にリダイレクトします。
.RE
.PP
\-\-stdout=\fIfilename\fR|\fIstream\fR|\fItty\fR
.RS 4
標準出力ストリームを指定したファイル、ストリーム(たとえば\fBstderr\fR)に、またはテキスト端末にリダイレクトします。
.RE
.PP
\-strict
.RS 4
標準(ECMAScript Edition 5\&.1)への準拠を強化するstrictモードを有効にし、これにより共通のコーディング・エラーを簡単に検出できるようになります。
.RE
.PP
\-t=\fIzone\fR
.br
\-timezone=\fIzone\fR
.RS 4
スクリプトの実行に対し指定したタイムゾーンを設定します。OSで設定されたタイムゾーンをオーバーライドし、\fBDate\fRオブジェクトで使用されます。
.RE
.PP
\-tcs=\fIparameter\fR
.br
\-\-trace\-callsites=\fIparameter\fR
.RS 4
呼出しサイトのトレースのモードを有効にします。使用可能なパラメータは、次のとおりです。
.PP
miss
.RS 4
呼出しサイトのミスをトレースします。
.RE
.PP
enterexit
.RS 4
呼出しサイトへの出入りをトレースします。
.RE
.PP
objects
.RS 4
オブジェクトのプロパティを出力します。
.RE
.RE
.PP
\-\-verify\-code
.RS 4
バイトコードを実行する前に検証します。
.RE
.PP
\-v
.br
\-version
.RS 4
Nashornバージョン文字列を出力します。
.RE
.PP
\-xhelp
.RS 4
コマンドライン・オプションの拡張ヘルプを出力します。
.RE
.SH "例"
.PP
\fB例 1 \fRNashornを使用したスクリプトの実行
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjjs script\&.js\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fB例 2 \fR対話型モードでのNashornの実行
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB>> \fR\fB\fBjjs\fR\fR
\fBjjs> \fR\fB\fBprintln("Hello, World!")\fR\fR
\fBHello, World!\fR
\fBjjs> \fR\fB\fBquit()\fR\fR
\fB>>\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fB例 3 \fRNashornへの引数の渡し
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB>> \fR\fB\fBjjs \-\- a b c\fR\fR
\fBjjs> \fR\fB\fBarguments\&.join(", ")\fR\fR
\fBa, b, c\fR
\fBjjs>\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.SH "関連項目"
.PP
\fBjrunscript\fR
.br
'pl 8.5i
'bp
