'\" t
.\" Copyright (c) 2004, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jmap
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: トラブルシューティング・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jmap" "1" "2013年11月21日" "JDK 8" "トラブルシューティング・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jmap \- プロセスやコア・ファイルまたはリモート・デバッグ・サーバーの、共用オブジェクト・メモリー・マップまたはヒープ・メモリーの詳細を出力します。このコマンドは試験的なもので、サポートされていません。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjmap\fR [ \fIoptions\fR ] \fIpid\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjmap\fR [ \fIoptions\fR ] \fIexecutable\fR \fIcore\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjmap\fR [ \fIoptions\fR ] [ \fIpid\fR ] \fIserver\-id\fR@ ] \fIremote\-hostname\-or\-IP\fR
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIpid\fR
.RS 4
出力するメモリー・マップのプロセスID。プロセスはJavaプロセスである必要があります。マシン上で実行しているJavaプロセスの一覧を取得するには、jps(1)コマンドを使用します。
.RE
.PP
\fI実行可能ファイル\fR
.RS 4
コア・ダンプの作成元のJava実行可能ファイル。
.RE
.PP
\fIコア\fR
.RS 4
出力するメモリー・マップのコア・ファイル。
.RE
.PP
\fIremote\-hostname\-or\-IP\fR
.RS 4
リモート・デバッグ・サーバーの\fBホスト名\fRまたは\fBIP\fRアドレス。jsadebugd(1)を参照してください。
.RE
.PP
\fIserver\-id\fR
.RS 4
複数のデバッグ・サーバーが同一のリモート・ホストで実行している場合の、オプション固有のID。
.RE
.SH "説明"
.PP
\fBjmap\fRコマンドは、指定されたプロセスやコア・ファイルまたはリモート・デバッグ・サーバーの、共用オブジェクト・メモリー・マップまたはヒープ・メモリーの詳細を出力します。指定されたプロセスが64ビットJava Virtual Machine (JVM)上で実行されている場合、\fB\-J\-d64\fRオプションを指定する必要がある場合があります。次に例を示します。\fBjmap\fR
\fB\-J\-d64 \-heap pid\fR。
.PP
\fB注意:\fR
このユーティリティはサポート対象外であり、将来のJDKのリリースでは利用できなくなる可能性があります。\fBdbgeng\&.dll\fRファイルが存在していないWindowsシステムでは、Debugging Tools For Windowsをインストールしないとこれらのツールが正常に動作しません。\fBPATH\fR環境変数には、ターゲット・プロセスによって使用される\fBjvm\&.dll\fRの場所、またはクラッシュ・ダンプ・ファイルが生成された場所が含まれるようにしてください。次に例を示します。\fBset PATH=%JDK_HOME%\ejre\ebin\eclient;%PATH%\fR。
.SH "オプション"
.PP
<no option>
.RS 4
オプションを使用しない場合、\fBjmap\fRコマンドは共用オブジェクト・マッピングを出力します。ターゲットJVMにロードされた共用オブジェクトごとに、開始アドレス、マッピングのサイズおよび共用オブジェクト・ファイルのフルパスが出力されます。この動作は、Oracle Solaris
\fBpmap\fRユーティリティと類似しています。
.RE
.PP
\-dump:[live,] format=b, file=\fIfilename\fR
.RS 4
Javaヒープを\fBhprof\fRバイナリ形式で\fBfilename\fRにダンプします。\fBlive\fRサブオプションはオプションですが、指定した場合、ヒープ内のアクティブなオブジェクトのみがダンプされます。ヒープ・ダンプを参照するには、jhat(1)コマンドを使用して生成されたファイルを読み取ります。
.RE
.PP
\-finalizerinfo
.RS 4
ファイナライズを待っているオブジェクトに関する情報を出力します。
.RE
.PP
\-heap
.RS 4
使用されているガベージ・コレクションのヒープ・サマリー、ヘッダー構成、および世代関連のヒープ使用状況を出力します。さらに、internされた文字列の数とサイズも出力されます。
.RE
.PP
\-histo[:live]
.RS 4
ヒープのヒストグラムを出力します。Javaクラスごとに、オブジェクトの数、バイト単位でのメモリー・サイズ、および完全修飾クラス名が出力されます。JVMの内部クラス名はアスタリスク(*)の接頭辞を付けて出力されます。\fBlive\fRサブオプションが指定された場合、アクティブなオブジェクトのみがカウントされます。
.RE
.PP
\-clstats
.RS 4
Javaヒープの、クラス・ローダー関連の統計データを出力します。クラス・ローダーごとに、その名前、状態、アドレス、親クラス・ローダー、およびクラス・ローダーがロードしたクラスの数とサイズが出力されます。
.RE
.PP
\-F
.RS 4
強制します。PIDが応答しないときに、このオプションを\fBjmap \-dump\fRまたは\fBjmap \-histo\fRオプションとともに使用します。このモードでは、\fBlive\fRサブオプションはサポートされません。
.RE
.PP
\-h
.RS 4
ヘルプ・メッセージが出力されます。
.RE
.PP
\-help
.RS 4
ヘルプ・メッセージが出力されます。
.RE
.PP
\-J\fIflag\fR
.RS 4
\fBjmap\fRコマンドを実行しているJava Virtual Machineに\fBflag\fRを渡します。
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jhat(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jps(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jsadebugd(1)
.RE
.br
'pl 8.5i
'bp
