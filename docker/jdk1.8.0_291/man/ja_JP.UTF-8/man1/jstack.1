'\" t
.\" Copyright (c) 2004, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jstack
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: トラブルシューティング・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jstack" "1" "2013年11月21日" "JDK 8" "トラブルシューティング・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jstack \- Javaプロセス、コア・ファイルまたはリモート・デバッグ・サーバーに対するJavaスレッドのスタック・トレースを出力します。このコマンドは試験的なもので、サポートされていません。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjstack\fR [ \fIoptions\fR ] \fIpid\fR 
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjstack\fR [ \fIoptions\fR ] \fIexecutable\fR \fIcore\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjstack\fR [ \fIoptions\fR ] [ \fIserver\-id\fR@ ] \fIremote\-hostname\-or\-IP\fR
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIpid\fR
.RS 4
出力するスタック・トレースのプロセスIDです。プロセスはJavaプロセスである必要があります。マシン上で実行しているJavaプロセスの一覧を取得するには、jps(1)コマンドを使用します。
.RE
.PP
\fI実行可能ファイル\fR
.RS 4
コア・ダンプの作成元のJava実行可能ファイル。
.RE
.PP
\fIコア\fR
.RS 4
出力するスタック・トレースのコア・ファイルです。
.RE
.PP
\fIremote\-hostname\-or\-IP\fR
.RS 4
リモート・デバッグ・サーバーの\fBホスト名\fRまたは\fBIP\fRアドレス。jsadebugd(1)を参照してください。
.RE
.PP
\fIserver\-id\fR
.RS 4
複数のデバッグ・サーバーが同一のリモート・ホストで実行している場合の、オプション固有のID。
.RE
.SH "説明"
.PP
\fBjstack\fRコマンドは、指定されたJavaプロセス、コア・ファイルまたはリモート・デバッグ・サーバーに対するJavaスレッドのJavaスタック・トレースを出力します。Javaフレームごとに、フルクラス名、メソッド名、バイトコード・インデックス(bci)、および行番号(利用可能な場合)が出力されます。\fB\-m\fRオプションを使用すると、\fBjstack\fRコマンドは、すべてのスレッドのJavaフレームとネイティブ・フレームの両方を、プログラム・カウンタ(PC)とともに出力します。ネイティブ・フレームごとに、PCに最も近いネイティブ・シンボル(利用可能な場合)が出力されます。C++分解名は分解解除されません。C++名を分解解除するには、このコマンドの出力を\fBc++filt\fRにパイプします。指定されたプロセスが64ビットJava仮想マシン上で実行されている場合は、\fB\-J\-d64\fRオプションを指定する必要があります(例:
\fBjstack \-J\-d64 \-m pid\fR)。
.PP
\fB注意\fR
このユーティリティはサポート対象外であり、将来のJDKのリリースでは利用できなくなる可能性があります。dbgeng\&.dllファイルが存在していないWindowsシステムでは、Debugging Tools For Windowsをインストールしないとこれらのツールが正常に動作しません。また、\fBPATH\fR環境変数には、ターゲット・プロセスによって使用されるjvm\&.dllの場所、またはクラッシュ・ダンプ・ファイルが生成された場所が含まれるようにしてください。次に例を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBset PATH=<jdk>\ejre\ebin\eclient;%PATH%\fR
 
.fi
.if n \{\
.RE
.\}
.SH "オプション"
.PP
\-F
.RS 4
\fBjstack\fR
[\fB\-l\fR]
\fBpid\fRが応答しない場合にスタック・ダンプを強制します。
.RE
.PP
\-l
.RS 4
長形式のリスト。所有\fBjava\&.util\&.concurrent\fRの所有できるシンクロナイザの一覧など、ロックについての追加情報を印刷します。http://docs\&.oracle\&.com/javase/8/docs/api/java/util/concurrent/locks/AbstractOwnableSynchronizer\&.htmlにある
\fBAbstractOwnableSynchronizer\fRクラス記述を参照してください
.RE
.PP
\-m
.RS 4
JavaおよびネイティブC/C++フレームの両方を持つ混合モードのスタック・トレースを出力します。
.RE
.PP
\-h
.RS 4
ヘルプ・メッセージが出力されます。
.RE
.PP
\-help
.RS 4
ヘルプ・メッセージが出力されます。
.RE
.SH "既知の不具合"
.PP
混合モードのスタック・トレースでは、\fB\-m\fRオプションはリモート・デバッグ・サーバーでは機能しません。
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
pstack(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
C++filt(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jps(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jsadebugd(1)
.RE
.br
'pl 8.5i
'bp
