'\" t
.\" Copyright (c) 2004, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: pack200
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Javaデプロイメント・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "pack200" "1" "2013年11月21日" "JDK 8" "Javaデプロイメント・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
pack200 \- WebデプロイメントのためにJARファイルをpack200圧縮ファイルにパッケージします。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpack200\fR [\fIoptions\fR] \fIoutput\-file\fR \fIJAR\-file\fR
.fi
.if n \{\
.RE
.\}
.PP
オプションは任意の順序で指定できます。コマンド行またはpropertiesファイルに指定された最後のオプションが、それ以前に指定されたすべてのオプションより優先されます。
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIoutput\-file\fR
.RS 4
出力ファイルの名前。
.RE
.PP
\fIJAR\-file\fR
.RS 4
入力ファイルの名前。
.RE
.SH "説明"
.PP
\fBpack200\fRコマンドは、Java gzipコンプレッサを使用してJARファイルをpack200圧縮ファイルに変換するJavaアプリケーションです。pack200ファイルは高圧縮のファイルで、直接デプロイでき、帯域幅の節約やダウンロード時間の短縮が可能です。
.PP
\fBpack200\fRコマンドには、圧縮エンジンの設定や微調整を行うオプションがあります。一般的な使用方法を次の例に示します。\fBmyarchive\&.pack\&.gz\fRがデフォルトの\fBpack200\fRコマンド設定で作成されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpack200 myarchive\&.pack\&.gz myarchive\&.jar\fR
 
.fi
.if n \{\
.RE
.\}
.SH "オプション"
.PP
\-r
.br
\-\-repack
.RS 4
JARファイルをパックした後アンパックして、JARファイルを生成します。生成されたファイルは\fBjarsigner\fR(1)ツールの入力ファイルとして使用できます。次の例では、myarchive\&.jarファイルをパックした後、アンパックします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpack200 \-\-repack myarchive\-packer\&.jar myarchive\&.jar\fR
\fBpack200 \-\-repack myarchive\&.jar\fR
 
.fi
.if n \{\
.RE
.\}
次の例では、入力ファイル内のファイルの順序を保持します。
.RE
.PP
\-g
.br
\-\-no\-gzip
.RS 4
\fBpack200\fRファイルを生成します。このオプションを指定するときは、適切な圧縮ツールを使用する必要があります。また、ターゲット・システムでは、対応する圧縮解除ツールを使用する必要があります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpack200 \-\-no\-gzip myarchive\&.pack myarchive\&.jar\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-G
.br
\-\-strip\-debug
.RS 4
出力からデバッグ属性を削除します。これには、\fBSourceFile\fR、\fBLineNumberTable\fR、\fBLocalVariableTable\fR、\fBLocalVariableTypeTable\fRが含まれます。これらの属性を削除すれば、ダウンロードとインストールのサイズは小さくなりますが、デバッガの機能は制限されます。
.RE
.PP
\-\-keep\-file\-order
.RS 4
入力ファイル内のファイルの順序を保持します。これは、デフォルトの動作です。
.RE
.PP
\-O
.br
\-\-no\-keep\-file\-order
.RS 4
パック・ツールは、すべての要素を並べ替えて送信します。パック・ツールは、JARディレクトリ名を削除してダウンロード・サイズを削減することもできます。ただし、インデックスなど、特定のJARファイルの最適化機能が正常に動作しなくなることがあります。
.RE
.PP
\-S\fIvalue\fR
.br
\-\-segment\-limit=\fIvalue\fR
.RS 4
この値は、各アーカイブ・セグメントの予想ターゲット・サイズ\fIN\fR
(バイト単位)です。単一の入力ファイルの必要サイズが\fIN\fRバイトを超えると、独立したセグメントが割り当てられます。特殊なケースとして、値が\fB\-1\fRの場合は、すべての入力ファイルを含む大きな単一のセグメントが生成され、値が0の場合は、クラスごとにセグメントが1つずつ生成されます。アーカイブ・セグメントが大きくなると、断片化が少なくなり圧縮率が高くなりますが、その処理には多くのメモリーが必要です。
.sp
各セグメントのサイズは、セグメントに変換されるそれぞれの入力ファイルのサイズのほか、その名前と他の送信されるプロパティのサイズを計算して推測されます。
.sp
デフォルトは\-1です。つまり、パック・ツールは単一のセグメント出力ファイルを作成します。極端に大きな出力ファイルが生成される場合には、入力ファイルをセグメント化(分割)してより小さなJARにすることを強くお薦めします。
.sp
この制限が課されていない10 MBのJARパック・ファイルは通常、約10%小さくパックされます。しかし、パック・ツールでより大きなJavaヒープ(セグメントの制限の約10倍)を必要とする場合もあります。
.RE
.PP
\-E\fIvalue\fR
.br
\-\-effort=\fIvalue\fR
.RS 4
単一の10進数値を指定した場合、パック・ツールは、指定された圧縮率でアーカイブを圧縮します。レベル1の場合は、比較的短い圧縮時間で多少大きめのファイルが生成されますが、レベル9の場合は、非常に長い時間がかかるものの、より圧縮率の高いファイルが生成されます。特殊な値0を指定した場合は、\fBpack200\fRコマンドは元のJARファイルを圧縮なしで直接コピーします。JSR 200標準では、すべての解凍プログラムが、この特別な場合をアーカイブ全体のパススルーと解釈するように規定しています。
.sp
デフォルトは5です。この場合、標準的な時間で適切な圧縮が行われます。
.RE
.PP
\-H\fIvalue\fR
.br
\-\-deflate\-hint=\fIvalue\fR
.RS 4
入力情報を保存するというデフォルト値をオーバーライドします。転送されるアーカイブのサイズは大きくなる場合があります。指定可能な値は、\fBtrue\fR、\fBfalse\fRまたは\fBkeep\fRです。
.sp
\fBvalue\fRが\fBtrue\fRまたはfalseの場合、\fBpacker200\fRコマンドは指定に従ってデフレーション・ヒントを出力アーカイブに設定します。アーカイブ要素の個々のデフレーション・ヒントは転送されません。
.sp
\fBkeep\fR値は、入力JARで確認されたデフレーション・ヒントを保持します。これがデフォルトです。
.RE
.PP
\-m\fIvalue\fR
.br
\-\-modification\-time=\fIvalue\fR
.RS 4
指定可能な値は\fBlatest\fRと\fBkeep\fRです。
.sp
値が最新の場合、パック・ツールは、元のアーカイブの使用可能なすべてのエントリのうちの最終更新時刻か、そのセグメントの使用可能なすべてのエントリの最終更新時刻を特定しようとします。この単一の値はセグメントの一部として転送され、各セグメントの全エントリに適用されます。この場合、すべてのインストール・ファイルに単一の日付が設定されるという問題はありますが、アーカイブの転送サイズを少し小さくすることができます。
.sp
値が\fBkeep\fRの場合、入力JARで確認された変更時間が保持されます。これがデフォルトです。
.RE
.PP
\-P\fIfile\fR
.br
\-\-pass\-file=\fIfile\fR
.RS 4
ファイルを圧縮せず、バイト単位で転送するように指定します。このオプションを繰返し使用して、複数のファイルを指定できます。システム・ファイル・セパレータがJARファイル・セパレータのスラッシュ(/)に置き換えられる点を除き、パス名の変換は行われません。結果として得られるファイル名は、文字列として正確にJARファイルでの出現と一致している必要があります。\fBfile\fRにディレクトリ名を指定した場合、そのディレクトリ内のすべてのファイルが転送されます。
.RE
.PP
\-U\fIaction\fR
.br
\-\-unknown\-attribute=\fIaction\fR
.RS 4
デフォルトの動作をオーバーライドします。つまり、不明な属性を含むクラス・ファイルが、指定した\fBaction\fRによって渡されます。アクションとして指定可能な値は、\fBerror\fR、\fBstrip\fRまたは\fBpass\fRです。
.sp
値が\fBerror\fRの場合、\fBpack200\fRコマンド操作全体が失敗して適切な説明が表示されます。
.sp
値が\fBstrip\fRの場合、属性は削除されます。Java Virtual Machine (JVM)必須属性を削除すると、クラス・ローダーの障害が発生することがあります。
.sp
値が\fBpass\fRの場合、クラス全体が1つのリソースとして転送されます。
.RE
.PP
\-C\fIattribute\-name\fR=\fIlayout\fR
.br
\-\-class\-attribute=\fIattribute\-name\fR=\fIaction\fR
.RS 4
次のオプションを参照してください。
.RE
.PP
\-F\fIattribute\-name\fR=\fIlayout\fR
.br
\-\-field\-attribute=\fIattribute\-name\fR=\fIaction\fR
.RS 4
次のオプションを参照してください。
.RE
.PP
\-M\fIattribute\-name\fR=\fIlayout\fR
.br
\-\-method\-attribute=\fIattribute\-name\fR=\fIaction\fR
.RS 4
次のオプションを参照してください。
.RE
.PP
\-D\fIattribute\-name\fR=\fIlayout\fR
.br
\-\-code\-attribute=\fIattribute\-name\fR=\fIaction\fR
.RS 4
前述の4つのオプションでは、クラス・エンティティに\fBclass\-attribute\fR、\fBfield\-attribute\fR、\fBmethod\-attribute\fRおよび\fBcode\-attribute\fRなどの属性のレイアウトを指定できます。\fIattribute\-name\fRには、これからレイアウトまたはアクションを定義する属性の名前を指定します。\fIaction\fRとして指定可能な値は、\fBsome\-layout\-string\fR、\fBerror\fR、\fBstrip\fR、\fBpass\fRです。
.sp
\fBsome\-layout\-string\fR: レイアウト言語はJSR 200仕様で定義されています。例:
\fB\-\-class\-attribute=SourceFile=RUH\fR。
.sp
値が\fBerror\fRの場合、\fBpack200\fR操作が失敗して説明が表示されます。
.sp
値が\fBstrip\fRの場合、属性が出力から削除されます。VM必須属性を削除するとクラス・ローダーの障害が発生することがあります。たとえば、\fB\-\-class\-attribute=CompilationID=pass\fRというこの属性を含むクラス・ファイルを転送します。パック・ツールは、その他のアクションを行いません。
.sp
値が\fBpass\fRの場合、クラス全体が1つのリソースとして転送されます。
.RE
.PP
\-f \fIpack\&.properties\fR
.br
\-\-config\-file=\fIpack\&.properties\fR
.RS 4
コマンド行に、パック・ツールを初期化するためのJavaプロパティが含まれている構成ファイルを指定できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpack200 \-f pack\&.properties myarchive\&.pack\&.gz myarchive\&.jar\fR
\fBmore pack\&.properties\fR
\fB# Generic properties for the packer\&.\fR
\fBmodification\&.time=latest\fR
\fBdeflate\&.hint=false\fR
\fBkeep\&.file\&.order=false\fR
\fB# This option will cause the files bearing new attributes to\fR
\fB# be reported as an error rather than passed uncompressed\&.\fR
\fBunknown\&.attribute=error\fR
\fB# Change the segment limit to be unlimited\&.\fR
\fBsegment\&.limit=\-1\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-v
.br
\-\-verbose
.RS 4
最小限のメッセージを出力します。このオプションを複数指定すると、より長いメッセージが作成されます。
.RE
.PP
\-q
.br
\-\-quiet
.RS 4
メッセージを表示せずに動作するように指定します。
.RE
.PP
\-l\fIfilename\fR
.br
\-\-log\-file=\fIfilename\fR
.RS 4
出力メッセージのログ・ファイルを指定します。
.RE
.PP
\-?
.br
\-h
.br
\-\-help
.RS 4
このコマンドに関するヘルプ情報を出力します。
.RE
.PP
\-V
.br
\-\-version
.RS 4
このコマンドに関するバージョン情報を出力します。
.RE
.PP
\-J\fIoption\fR
.RS 4
指定されたオプションをJava Virtual Machineに渡します。詳細は、java(1)コマンドのリファレンス・ページを参照してください。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。
.RE
.SH "終了ステータス"
.PP
次の終了値が返されます: 正常終了の場合は0、エラーが発生した場合は0より大きい値。
.SH "注意"
.PP
このコマンドと\fBpack\fR(1)を混同しないでください。\fBpack\fRおよび\fBpack200\fRコマンドは、別々の製品です。
.PP
JDKに付属するJava SE API仕様との相違が見つかった場合には、仕様を優先してください。
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
unpack200(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jar(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jarsigner(1)
.RE
.br
'pl 8.5i
'bp
