'\" t
.\" Copyright (c) 2005, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: schemagen
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Java Webサービス・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "schemagen" "1" "2013年11月21日" "JDK 8" "Java Webサービス・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
schemagen \- Javaクラス内で参照されているネームスペースごとにスキーマを生成します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBschemagen\fR [ \fIoptions\fR ] \fIjava\-files\fR
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIjava\-files\fR
.RS 4
処理するJavaクラス・ファイル。
.RE
.SH "説明"
.PP
スキーマ・ジェネレータは、Javaクラス内で参照されているネームスペースごとに1つのスキーマ・ファイルを作成します。現在、生成されるスキーマ・ファイルの名前は制御できません。スキーマ・ファイル名を制御する場合は、http://jaxb\&.java\&.net/nonav/2\&.2\&.3u1/docs/schemagenTask\&.htmlにある
「Using SchemaGen with Ant」を参照してください
.PP
プラットフォームのbinディレクトリにある適切な\fBschemagen\fRシェル・スクリプトを使用して、スキーマ・ジェネレータを起動します。現在のスキーマ・ジェネレータは、Javaソース・ファイルとクラス・ファイルのいずれも処理できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBschemagen\&.sh Foo\&.java Bar\&.java \&.\&.\&.\fR
\fBNote: Writing schema1\&.xsd\fR
 
.fi
.if n \{\
.RE
.\}
.PP
javaファイルが他のクラスを参照している場合、それらのクラスにシステム\fBCLASSPATH\fR環境変数でアクセスできる必要があります。または、クラス・パス・オプションを指定して\fBschemagen\fRコマンド行で指定する必要があります。オプションを参照してください。参照されるファイルにアクセスできない、または参照されるファイルが指定されていない場合、スキーマの生成時にエラーが表示されます。
.SH "オプション"
.PP
\-d \fIpath\fR
.RS 4
\fBschemagen\fRコマンドがプロセッサ生成および\fBjavac\fR生成クラス・ファイルを配置する場所。
.RE
.PP
\-cp \fIpath\fR
.RS 4
\fBschemagen\fRコマンドがユーザー指定ファイルを配置する場所。
.RE
.PP
\-classpath \fIpath\fR
.RS 4
\fBschemagen\fRコマンドがユーザー指定ファイルを配置する場所。
.RE
.PP
\-encoding \fIencoding\fR
.RS 4
\fBapt\fRまたは\fBjavac\fRコマンドの呼出しに使用するエンコーディングを指定します。
.RE
.PP
\-episode \fIfile\fR
.RS 4
コンパイルごとにエピソード・ファイルを生成します。
.RE
.PP
\-version
.RS 4
リリース情報を表示します。
.RE
.PP
\-help
.RS 4
ヘルプ・メッセージを表示します。
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Using SchemaGen with Ant

(http://jaxb\&.java\&.net/nonav/2\&.2\&.3u1/docs/schemagenTask\&.html)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Java Architecture for XML Binding (JAXB)

(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/xml/jaxb/index\&.html)
.RE
.br
'pl 8.5i
'bp
