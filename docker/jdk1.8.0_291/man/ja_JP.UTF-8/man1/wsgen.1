'\" t
.\" Copyright (c) 2005, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: wsgen
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Java Webサービス・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "wsgen" "1" "2013年11月21日" "JDK 8" "Java Webサービス・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
wsgen \- Webサービスのエンドポイント実装(SEI)クラスを読取り、Webサービスのデプロイメントと呼出しに必要なすべてのアーティファクトを生成します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBwsgen\fR [ \fIoptions\fR ] \fISEI\fR
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fISEI\fR
.RS 4
読み取るWebサービスのエンドポイント実装クラス(SEI)です。
.RE
.SH "説明"
.PP
\fBwsgen\fRコマンドは、JAX\-WS Webサービスで使用されるJAX\-WSポータブル・アーティファクトを生成します。このツールは、Webサービスのエンドポイント・クラスを読取り、Webサービスのデプロイメントと呼出しに必要なすべてのアーティファクトを生成します。JAXWS 2\&.1\&.1 RIでは\fBwsgen\fR
Antタスクも提供されます。
http://jax\-ws\&.java\&.net/nonav/2\&.1\&.1/docs/wsgenant\&.htmlのJAX\-WS (wsgen)ページのToolsタブを参照してください。
.PP
\fBwsgen\fRコマンドを起動するには、次を行います。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBexport JAXWS_HOME=/pathto/jaxws\-ri\fR
\fB$JAXWS_HOME/bin/wsgen\&.sh \-help\fR
 
.fi
.if n \{\
.RE
.\}
.SH "オプション"
.PP
\-classpath \fIpath\fR
.RS 4
入力クラス・ファイルの場所。
.RE
.PP
\-cp \fIpath\fR
.RS 4
入力クラス・ファイルの場所。
.RE
.PP
\-d \fIdirectory\fR
.RS 4
生成された出力ファイルを格納する場所。
.RE
.PP
\-extension
.RS 4
ベンダー拡張を使用可能にします。拡張を使用すると、アプリケーションの移植性が失われたり、他の実装と連携できなくなる可能性があります。
.RE
.PP
\-help
.RS 4
\fBwsgen\fRコマンドに関するヘルプ・メッセージを表示します。
.RE
.PP
\-keep
.RS 4
生成されたファイルを保存します。
.RE
.PP
\-r \fIdirectory\fR
.RS 4
このオプションを\fB\-wsdl\fRオプションとともに使用して、WSDLなど生成されたリソース・ファイルを配置する場所を指定します。
.RE
.PP
\-s \fIdirectory\fR
.RS 4
生成されたソース・ファイルを格納する場所。
.RE
.PP
\-verbose
.RS 4
コンパイラ・メッセージを表示します。
.RE
.PP
\-version
.RS 4
リリース情報を出力します。
.RE
.PP
\-wsdl [ :protocol ]
.RS 4
エンドポイントをデプロイする前に確認するWSDLファイルを生成するオプション・コマンド。WSDLファイルには、サービスを呼び出す方法、パラメータに必要な値、返されるデータ構造についてのコンピュータで読取り可能な記述が含まれます。
.sp
デフォルトでは\fBwsgen\fRコマンドはWSDLファイルを生成しません。\fBprotocol\fR値は省略可能であり、WSDLバインディング(\fBwsdl:binding\fR)で使用するプロトコルを指定するために使用されます。有効なプロトコルは、\fBsoap1\&.1\fRと\fBXsoap1\&.2\fRです。デフォルトは\fBsoap1\&.1\fRです。\fBXsoap1\&.2\fRプロトコルは標準ではないため、\fB\-extension\fRオプションとともにのみ使用可能です。
.RE
.PP
\-servicename \fIname\fR
.RS 4
\fB\-wsdl\fRオプションとともにのみ使用して、WSDLで生成される特定のWSDLサービス(\fBwsdl:service\fR)名を指定します。例:
\fB\-servicename "{http://mynamespace/}MyService"\fR。
.RE
.PP
\-portname \fIname\fR
.RS 4
\fB\-wsdl\fRオプションとともにのみ使用して、WSDLで生成される特定のWSDLポート(\fBwsdl:port\fR)名を指定します。例:
\fB\-portname "{http://mynamespace/}MyPort"\fR。
.RE
.SH "例"
.PP
次の例では、Stockディレクトリ内に\fB@WebService\fR注釈を持つ\fBStockService\fRのラッパー・クラスを生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBwsgen \-d stock \-cp myclasspath stock\&.StockService\fR
 
.fi
.if n \{\
.RE
.\}
.PP
次の例では、SOAP 1\&.1 WSDLおよび\fB@WebService\fR注釈を持つ\fBstock\&.StockService\fRクラスのスキーマを生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBwsgen \-wsdl \-d stock \-cp myclasspath stock\&.StockService\fR
 
.fi
.if n \{\
.RE
.\}
.PP
次の例では、SOAP 1\&.2 WSDLを生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBwsgen \-wsdl:Xsoap1\&.2 \-d stock \-cp myclasspath stock\&.StockService \fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fB注意:\fR
サービスのデプロイ時にJAXWS実行時環境でWSDLが生成されるため、開発時にWSDLを生成する必要はありません。
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
wsimport(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
http://jax\-ws\&.java\&.net/nonav/2\&.1\&.1/docs/wsgenant\&.htmlの
JAX\-WS (wsgen)ページのToolsタブを参照してください。
.RE
.br
'pl 8.5i
'bp
