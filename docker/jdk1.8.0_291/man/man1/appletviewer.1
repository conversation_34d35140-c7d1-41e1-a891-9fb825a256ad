'\" t
.\"  Copyright (c) 1995, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Basic Tools
.\"     Title: appletviewer.1
.\"
.if n .pl 99999
.TH appletviewer 1 "21 November 2013" "JDK 8" "Basic Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
appletviewer \- Runs applets outside of a web browser\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBappletviewer\fR [\fIoptions\fR] \fIurl\fR\&.\&.\&.
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options separated by spaces\&. See Options\&.
.TP     
\fIurl\fR
The location of the documents or resources to be displayed\&. You can specify multiple URLs separated by spaces\&.
.SH DESCRIPTION    
The \f3appletviewer\fR command connects to the documents or resources designated by \fIurls\fR and displays each applet referenced by the documents in its own window\&. If the documents referred to by urls do not reference any applets with the \f3OBJECT\fR, \f3EMBED\fR, or \f3APPLET\fR tag, then the \f3appletviewer\fR command does nothing\&. For details about the HTML tags that the \f3appletviewer\fR command supports, see AppletViewer Tags at http://docs\&.oracle\&.com/javase/8/docs/technotes/tools/appletviewertags\&.html
.PP
The \f3appletviewer\fR command requires encoded URLs according to the escaping mechanism defined in RFC2396\&. Only encoded URLs are supported\&. However, file names must be unencoded, as specified in RFC2396\&.
.PP
\fINote:\fR The \f3appletviewer\fR command is intended for development purposes only\&. For more information, see About Sample/Test Applications and Code at http://docs\&.oracle\&.com/javase/8/docs/technotes/samples/aboutCodeSamples\&.html
.SH OPTIONS    
.TP
-debug
.br
Starts the Applet Viewer in the Java debugger with the \f3jdb\fR command to debug the applets in the document\&.
.TP
-encoding \fIencoding-name\fR
.br
Specifies the input HTML file encoding name\&.
.TP
-J\fIjavaoption\fR
.br
Passes the string \f3javaoption\fR as a single argument to the Java interpreter, which runs the Applet Viewer\&. The argument should not contain spaces\&. Multiple argument words must all begin with the prefix \f3-J\fR\&. This is useful for adjusting the compiler\&'s execution environment or memory usage\&.
.PP
.RE
.br
'pl 8.5i
'bp
