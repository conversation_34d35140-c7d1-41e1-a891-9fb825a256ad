'\" t
.\"  Copyright (c) 1997, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Basic Tools
.\"     Title: jar.1
.\"
.if n .pl 99999
.TH jar 1 "21 November 2013" "JDK 8" "Basic Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
jar \- Manipulates Java Archive (JAR) files\&.
.SH SYNOPSIS    
Create JAR file
.sp     
.nf     

\fBjar c\fR[\fBefmMnv0\fR] [\fIentrypoint\fR] [\fIjarfile\fR] [\fImanifest\fR] [\fB\-C\fR \fIdir\fR] \fIfile\fR \&.\&.\&. [\-J\fIoption\fR \&.\&.\&.] [@\fIarg\-file\fR \&.\&.\&.]
.fi     
.sp     

Update JAR file
.sp     
.nf     

\fBjar u\fR[\fBefmMnv0\fR] [\fIentrypoint\fR] [\fIjarfile\fR] [\fImanifest\fR] [\fB\-C\fR \fIdir\fR] \fIfile\fR \&.\&.\&. [\-J\fIoption\fR \&.\&.\&.] [@\fIarg\-file\fR \&.\&.\&.]
.fi     
.sp     

Extract JAR file
.sp     
.nf     

\fBjar\fR \fBx\fR[\fBvf\fR] [\fIjarfile\fR] \fIfile\fR \&.\&.\&. [\-J\fIoption\fR \&.\&.\&.] [@\fIarg\-file\fR \&.\&.\&.]
.fi     
.sp     

List Contents of JAR file
.sp     
.nf     

\fBjar\fR \fBt\fR[\fBvf\fR] [\fIjarfile\fR] \fIfile\fR \&.\&.\&. [\-J\fIoption\fR \&.\&.\&.] [@\fIarg\-file\fR \&.\&.\&.]
.fi     
.sp     

Add Index to JAR file
.sp     
.nf     

\fBjar\fR \fBi\fR \fIjarfile\fR [\-J\fIoption\fR \&.\&.\&.] [@\fIarg\-file\fR \&.\&.\&.]
.fi     
.sp     
.SH DESCRIPTION    
The \f3jar\fR command is a general-purpose archiving and compression tool, based on ZIP and the ZLIB compression format\&. However, the \f3jar\fR command was designed mainly to package Java applets or applications into a single archive\&. When the components of an applet or application (files, images and sounds) are combined into a single archive, they can be downloaded by a Java agent (such as a browser) in a single HTTP transaction, rather than requiring a new connection for each piece\&. This dramatically improves download times\&. The \f3jar\fR command also compresses files, which further improves download time\&. The \f3jar\fR command also allows individual entries in a file to be signed by the applet author so that their origin can be authenticated\&. A JAR file can be used as a class path entry, whether or not it is compressed\&.
.PP
The syntax for the \f3jar\fR command resembles the syntax for the \f3tar\fR command\&. It has several operation modes, defined by one of the mandatory \fIoperation arguments\fR\&. Other arguments are either \fIoptions\fR that modify the behavior of the operation, or \fIoperands\fR required to perform the operation\&.
.SH OPERATION\ ARGUMENTS    
When using the \f3jar\fR command, you have to select an operation to be performed by specifying one of the following operation arguments\&. You can mix them up with other one-letter options on the command line, but usually the operation argument is the first argument specified\&.
.TP     
c
Create a new JAR archive\&.
.TP     
i
Generate index information for a JAR archive\&.
.TP     
t
List the contents of a JAR archive\&.
.TP     
u
Update a JAR archive\&.
.TP     
x
Extract files from a JAR archive\&.
.SH OPTIONS    
Use the following options to customize how the JAR file is created, updated, extracted, or viewed:
.TP     
e
Sets the class specified by the \fIentrypoint\fR operand to be the entry point\f3\fR for a standalone Java application bundled into an executable JAR file\&. The use of this option creates or overrides the \f3Main-Class\fR attribute value in the manifest file\&. The \f3e\fR option can be used when creating (\f3c\fR) or updating (\f3u\fR) the JAR file\&.

For example, the following command creates the \f3Main\&.jar\fR archive with the \f3Main\&.class\fR file where the \f3Main-Clas\fRs attribute value in the manifest is set to \f3Main\fR:
.sp     
.nf     
\f3jar cfe Main\&.jar Main Main\&.class\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The Java Runtime Environment (JRE) can directly call this application by running the following command:
.sp     
.nf     
\f3java \-jar Main\&.jar\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


If the entry point class name is in a package, then it could use either the dot (\&.) or slash (/) as the delimiter\&. For example, if \f3Main\&.class\fR is in a package called \f3mydir\fR, then the entry point can be specified in one of the following ways:
.sp     
.nf     
\f3jar \-cfe Main\&.jar mydir/Main mydir/Main\&.class\fP
.fi     
.nf     
\f3jar \-cfe Main\&.jar mydir\&.Main mydir/Main\&.class\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


Note

Specifying both \f3m\fR and \f3e\fR options together when a particular manifest also contains the \f3Main-Class\fR attribute results in an ambiguous \f3Main-Class\fR specification\&. The ambiguity leads to an error and the \f3jar\fR command creation or update operation is terminated\&.
.TP     
f
Sets the file specified by the \fI\fR\fIjarfile\fR operand to be the name of the JAR file that is created (\f3c\fR), updated (\f3u\fR), extracted (\f3x\fR) from, or viewed (\f3t\fR)\&. Omitting the \f3f\fR option and the \fIjarfile\fR operand instructs the \f3jar\fR command to accept the JAR file name from \f3stdin\fR (for \f3x\fR and \f3t\fR) or send the JAR \f3\fRfile to \f3stdout\fR (for \f3c\fR and \f3u\fR)\&.
.TP     
m
Includes names and values of attributes from the file specified by the \f3manifest\fR operand in the manifest file of the \f3jar\fR command (located in the archive at \f3META-INF/MANIFEST\&.MF\fR)\&. The \f3jar\fR command adds the attribute\(cqs name and value to the JAR file unless an entry already exists with the same name, in which case the \f3jar\fR command updates the value of the attribute\&. The \f3m\fR option can be used when creating (\f3c\fR) or updating (\f3u\fR) the JAR file\&.

You can add special-purpose name-value attribute pairs to the manifest that are not contained in the default manifest file\&. For example, you can add attributes that specify vendor information, release information, package sealing, or to make JAR-bundled applications executable\&. For examples of using the \f3m\fR option, see Packaging Programs at http://docs\&.oracle\&.com/javase/tutorial/deployment/jar/index\&.html
.TP     
M
Does not create a manifest file entry (for \f3c\fR and \f3u\fR), or delete a manifest file entry when one exists (for \f3u\fR)\&. The \f3M\fR option can be used when creating (\f3c\fR) or updating (\f3u\fR) the JAR file\&.
.TP     
n
When creating (\f3c\fR) a JAR file, this option normalizes the archive so that the content is not affected by the packing and unpacking operations of the pack200(1) command\&. Without this normalization, the signature of a signed JAR can become invalid\&.
.TP     
v
Generates verbose output to standard output\&. See Examples\&.
.TP     
0
(Zero) Creates (\f3c\fR) or updates (\f3u\fR) the JAR file without using ZIP compression\&.
.TP
-C \fIdir\fR
.br
When creating (\f3c\fR) or updating (\f3u\fR) a JAR file, this option temporarily changes the directory while processing files specified by the \fIfile\fR operands\&. Its operation is intended to be similar to the \f3-C\fR option of the UNIX \f3tar\fR utility\&.For example, the following command changes to the \f3classes\fR directory and adds the \f3Bar\&.class\fR file from that directory to \f3my\&.jar\fR:
.sp     
.nf     
\f3jar uf my\&.jar \-C classes Bar\&.class\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The following command changes to the \f3classes\fR directory and adds to \f3my\&.jar\fR all files within the classes directory (without creating a \f3classes\fR directory in the JAR file), then changes back to the original directory before changing to the \f3bin\fR directory to add \f3Xyz\&.class\fR to \f3my\&.jar\fR\&.
.sp     
.nf     
\f3jar uf my\&.jar \-C classes \&. \-C bin Xyz\&.class\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


If \f3classes\fR contained files \f3bar1\fR and \f3bar2\fR, then the JAR file will contain the following after running the previous command:
.sp     
.nf     
\f3% \fIjar tf my\&.jar\fR\fP
.fi     
.nf     
\f3META\-INF/\fP
.fi     
.nf     
\f3META\-INF/MANIFEST\&.MF\fP
.fi     
.nf     
\f3bar1\fP
.fi     
.nf     
\f3bar2\fP
.fi     
.nf     
\f3Xyz\&.class\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP     
\fI\fR-J\fIoption\fR
Sets the specified JVM option to be used when the JRE runs the JAR file\&. JVM options are described on the reference page for the java(1) command\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&.
.SH OPERANDS    
The following operands are recognized by the \f3jar\fR command\&.
.TP     
\fIfile\fR
When creating (\f3c\fR) or updating (\f3u\fR) a JAR file, the \fIfile\fR operand defines the path and name of the file or directory that should be added to the archive\&. When extracting (\f3x\fR) or listing the contents (\f3t\fR) of a JAR file, the \fIfile\fR operand defines the path and name of the file to be extrated or listed\&. At least one valid file or directory must be specified\&. Separate multiple \fIfile\fR operands with spaces\&. If the \fIentrypoint\fR, \fIjarfile\fR, or \fImanifest\fR operands are used, the \fIfile\fR operands must be specified after them\&.
.TP     
\fIentrypoint\fR
When creating (\f3c\fR) or updating (\f3u\fR) a JAR file, the \fIentrypoint\fR operand defines the name of the class that should be the entry point\f3\fR for a standalone Java application bundled into an executable JAR file\&. The \fIentrypoint\fR operand must be specified if the \f3e\fR option is present\&.
.TP     
\fIjarfile\fR
Defines the name of the file to be created (\f3c\fR), updated (\f3u\fR), extracted (\f3x\fR), or viewed (\f3t\fR)\&. The \fIjarfile\fR operand must be specified if the \f3f\fR option is present\&. Omitting the \f3f\fR option and the \fIjarfile\fR operand instructs the \f3jar\fR command to accept the JAR file name from \f3stdin\fR (for \f3x\fR and \f3t\fR) or send the JAR \f3\fRfile to \f3stdout\fR (for \f3c\fR and \f3u\fR)\&.

When indexing (\f3i\fR) a JAR file, specify the \fIjarfile\fR operand without the \f3f\fR option\&.
.TP     
\fImanifest\fR
When creating (\f3c\fR) or updating (\f3u\fR) a JAR file, the \fImanifest\fR operand defines the preexisting manifest files with names and values of attributes to be included in \f3MANIFEST\&.MF\fR in the JAR file\&. The \fImanifest\fR operand must be specified if the \f3f\fR option is present\&.
.TP     
\fI@arg-file\fR
To shorten or simplify the \f3jar\fR command, you can specify arguments in a separate text file and pass it to the \f3jar\fR command with the at sign (@) as a prefix\&. When the \f3jar\fR command encounters an argument beginning with the at sign, it expands the contents of that file into the argument list\&.

An argument file can include options and arguments of the \f3jar\fR command (except the \f3-J\fR options, because they are passed to the launcher, which does not support argument files)\&. The arguments within a file can be separated by spaces or newline characters\&. File names within an argument file are relative to the current directory from which you run the \f3jar\fR command, not relative to the location of the argument file\&. Wild cards, such as the asterisk (*), that might otherwise be expanded by the operating system shell, are not expanded\&.

The following example, shows how to create a \f3classes\&.list\fR file with names of files from the current directory output by the \f3find\fR command:
.sp     
.nf     
\f3find \&. \-name \&'*\&.class\&' \-print > classes\&.list\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


You can then execute the \f3jar\fR command and pass the \f3classes\&.list\fR file to it using the \fI@arg-file\fR syntax:
.sp     
.nf     
\f3jar cf my\&.jar @classes\&.list\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


An argument file can be specified with a path, but any file names inside the argument file that have relative paths are relative to the current working directory of the \f3jar\fR command, not to the path passed in, for example:
.sp     
.nf     
\f3jar @dir/classes\&.list\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.SH NOTES    
The \f3e\fR, \f3f\fR, and \f3m\fR options must appear in the same order on the command line as the \fIentrypoint\fR, \fIjarfile\fR, and \fImanifest\fR operands, for example:
.sp     
.nf     
\f3jar cmef myManifestFile MyMainClass myFile\&.jar *\&.class\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH EXAMPLES    
\f3Example 1 Adding All Files From the Current Directory With Verbose Output\fR
.sp     
.nf     
\f3% ls\fP
.fi     
.nf     
\f31\&.au          Animator\&.class    monkey\&.jpg\fP
.fi     
.nf     
\f32\&.au          Wave\&.class        spacemusic\&.au\fP
.fi     
.nf     
\f33\&.au          at_work\&.gif\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3% jar cvf bundle\&.jar *\fP
.fi     
.nf     
\f3added manifest\fP
.fi     
.nf     
\f3adding: 1\&.au(in = 2324) (out= 67)(deflated 97%)\fP
.fi     
.nf     
\f3adding: 2\&.au(in = 6970) (out= 90)(deflated 98%)\fP
.fi     
.nf     
\f3adding: 3\&.au(in = 11616) (out= 108)(deflated 99%)\fP
.fi     
.nf     
\f3adding: Animator\&.class(in = 2266) (out= 66)(deflated 97%)\fP
.fi     
.nf     
\f3adding: Wave\&.class(in = 3778) (out= 81)(deflated 97%)\fP
.fi     
.nf     
\f3adding: at_work\&.gif(in = 6621) (out= 89)(deflated 98%)\fP
.fi     
.nf     
\f3adding: monkey\&.jpg(in = 7667) (out= 91)(deflated 98%)\fP
.fi     
.nf     
\f3adding: spacemusic\&.au(in = 3079) (out= 73)(deflated 97%)\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3Example 2 Adding Files From Subdirectories\fR
.sp     
.nf     
\f3% ls \-F\fP
.fi     
.nf     
\f3audio/ classes/ images/\fP
.fi     
.nf     
\f3% jar cvf bundle\&.jar audio classes images\fP
.fi     
.nf     
\f3added manifest\fP
.fi     
.nf     
\f3adding: audio/(in = 0) (out= 0)(stored 0%)\fP
.fi     
.nf     
\f3adding: audio/1\&.au(in = 2324) (out= 67)(deflated 97%)\fP
.fi     
.nf     
\f3adding: audio/2\&.au(in = 6970) (out= 90)(deflated 98%)\fP
.fi     
.nf     
\f3adding: audio/3\&.au(in = 11616) (out= 108)(deflated 99%)\fP
.fi     
.nf     
\f3adding: audio/spacemusic\&.au(in = 3079) (out= 73)(deflated 97%)\fP
.fi     
.nf     
\f3adding: classes/(in = 0) (out= 0)(stored 0%)\fP
.fi     
.nf     
\f3adding: classes/Animator\&.class(in = 2266) (out= 66)(deflated 97%)\fP
.fi     
.nf     
\f3adding: classes/Wave\&.class(in = 3778) (out= 81)(deflated 97%)\fP
.fi     
.nf     
\f3adding: images/(in = 0) (out= 0)(stored 0%)\fP
.fi     
.nf     
\f3adding: images/monkey\&.jpg(in = 7667) (out= 91)(deflated 98%)\fP
.fi     
.nf     
\f3adding: images/at_work\&.gif(in = 6621) (out= 89)(deflated 98%)\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3% ls \-F\fP
.fi     
.nf     
\f3audio/ bundle\&.jar classes/ images/\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3Example 3 Listing the Contents of JAR\fR
.sp     
.nf     
\f3% jar tf bundle\&.jar\fP
.fi     
.sp     
.sp     
.nf     
\f3META\-INF/\fP
.fi     
.nf     
\f3META\-INF/MANIFEST\&.MF\fP
.fi     
.nf     
\f3audio/1\&.au\fP
.fi     
.nf     
\f3audio/2\&.au\fP
.fi     
.nf     
\f3audio/3\&.au\fP
.fi     
.nf     
\f3audio/spacemusic\&.au\fP
.fi     
.nf     
\f3classes/Animator\&.class\fP
.fi     
.nf     
\f3classes/Wave\&.class\fP
.fi     
.nf     
\f3images/monkey\&.jpg\fP
.fi     
.nf     
\f3images/at_work\&.gif\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3Example 4 Adding an Index\fR
.PP
Use the \f3i\fR option when you split the interdependent classes for a stock trade application into three JAR files: \f3main\&.jar\fR, \f3buy\&.jar\fR, and \f3sell\&.jar\fR\&. If you specify the \f3Class-Path\fR attribute in the \f3main\&.jar\fR manifest, then you can use the \f3i\fR option to speed up the class loading time for your application:
.sp     
.nf     
\f3Class\-Path: buy\&.jar sell\&.jar\fP
.fi     
.nf     
\f3jar i main\&.jar\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
An \f3INDEX\&.LIST\fR file is inserted to the \f3META-INF\fR directory\&. This enables the application class loader to download the specified JAR files when it is searching for classes or resources\&.
.PP
The application class loader uses the information stored in this file for efficient class loading\&. To copy directories, first compress files in \f3dir1\fR to \f3stdout\fR, then pipeline and extract from \f3stdin\fR to \f3dir2\fR (omitting the \f3-f\fR option from both \f3jar\fR commands):
.sp     
.nf     
\f3(cd dir1; jar c \&.) | (cd dir2; jar x)\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
pack200(1)\&.
.TP 0.2i    
\(bu
The JAR section of The Java Tutorials at http://docs\&.oracle\&.com/javase/tutorial/deployment/jar/index\&.html
.RE
.br
'pl 8.5i
'bp
