'\" t
.\" Copyright (c) 1994, 2021, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: java
.\" Language: English
.\" Date: 19 January 2021
.\" SectDesc: Basic Tools
.\" Software: JDK 8
.\" Arch: generic
.\" Part Number: E38209-04
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "java" "1" "19 January 2021" "JDK 8" "Basic Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
java \- Launches a Java application\&.
.SH "SYNOPSIS"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava\fR [\fIoptions\fR] \fIclassname\fR [\fIargs\fR]
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava\fR [\fIoptions\fR] \fB\-jar\fR \fIfilename\fR [\fIargs\fR]
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
Command\-line options separated by spaces\&. See Options\&.
.RE
.PP
\fIclassname\fR
.RS 4
The name of the class to be launched\&.
.RE
.PP
\fIfilename\fR
.RS 4
The name of the Java Archive (JAR) file to be called\&. Used only with the
\fB\-jar\fR
option\&.
.RE
.PP
\fIargs\fR
.RS 4
The arguments passed to the
\fBmain()\fR
method separated by spaces\&.
.RE
.SH "DESCRIPTION"
.PP
The
\fBjava\fR
command starts a Java application\&. It does this by starting the Java Runtime Environment (JRE), loading the specified class, and calling that class\*(Aqs
\fBmain()\fR
method\&. The method must be declared
\fIpublic\fR
and
\fIstatic\fR, it must not return any value, and it must accept a
\fBString\fR
array as a parameter\&. The method declaration has the following form:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpublic static void main(String[] args)\fR
 
.fi
.if n \{\
.RE
.\}
.PP
The
\fBjava\fR
command can be used to launch a JavaFX application by loading a class that either has a
\fBmain()\fR
method or that extends
\fBjavafx\&.application\&.Application\fR\&. In the latter case, the launcher constructs an instance of the
\fBApplication\fR
class, calls its
\fBinit()\fR
method, and then calls the
\fBstart(javafx\&.stage\&.Stage)\fR
method\&.
.PP
By default, the first argument that is not an option of the
\fBjava\fR
command is the fully qualified name of the class to be called\&. If the
\fB\-jar\fR
option is specified, its argument is the name of the JAR file containing class and resource files for the application\&. The startup class must be indicated by the
\fBMain\-Class\fR
manifest header in its source code\&.
.PP
The JRE searches for the startup class (and other classes used by the application) in three sets of locations: the bootstrap class path, the installed extensions, and the user\(cqs class path\&.
.PP
Arguments after the class file name or the JAR file name are passed to the
\fBmain()\fR
method\&.
.SH "OPTIONS"
.PP
The
\fBjava\fR
command supports a wide range of options that can be divided into the following categories:
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Standard Options
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Non\-Standard Options
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Advanced Runtime Options
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Advanced JIT Compiler Options
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Advanced Serviceability Options
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Advanced Garbage Collection Options
.RE
.PP
Standard options are guaranteed to be supported by all implementations of the Java Virtual Machine (JVM)\&. They are used for common actions, such as checking the version of the JRE, setting the class path, enabling verbose output, and so on\&.
.PP
Non\-standard options are general purpose options that are specific to the Java HotSpot Virtual Machine, so they are not guaranteed to be supported by all JVM implementations, and are subject to change\&. These options start with
\fB\-X\fR\&.
.PP
Advanced options are not recommended for casual use\&. These are developer options used for tuning specific areas of the Java HotSpot Virtual Machine operation that often have specific system requirements and may require privileged access to system configuration parameters\&. They are also not guaranteed to be supported by all JVM implementations, and are subject to change\&. Advanced options start with
\fB\-XX\fR\&.
.PP
To keep track of the options that were deprecated or removed in the latest release, there is a section named Deprecated and Removed Options at the end of the document\&.
.PP
Boolean options are used to either enable a feature that is disabled by default or disable a feature that is enabled by default\&. Such options do not require a parameter\&. Boolean
\fB\-XX\fR
options are enabled using the plus sign (\fB\-XX:+\fR\fIOptionName\fR) and disabled using the minus sign (\fB\-XX:\-\fR\fIOptionName\fR)\&.
.PP
For options that require an argument, the argument may be separated from the option name by a space, a colon (:), or an equal sign (=), or the argument may directly follow the option (the exact syntax differs for each option)\&. If you are expected to specify the size in bytes, you can use no suffix, or use the suffix
\fBk\fR
or
\fBK\fR
for kilobytes (KB),
\fBm\fR
or
\fBM\fR
for megabytes (MB),
\fBg\fR
or
\fBG\fR
for gigabytes (GB)\&. For example, to set the size to 8 GB, you can specify either
\fB8g\fR,
\fB8192m\fR,
\fB8388608k\fR, or
\fB8589934592\fR
as the argument\&. If you are expected to specify the percentage, use a number from 0 to 1 (for example, specify
\fB0\&.25\fR
for 25%)\&.
.SS "Standard Options"
.PP
These are the most commonly used options that are supported by all implementations of the JVM\&.
.PP
\-agentlib:\fIlibname\fR[=\fIoptions\fR]
.RS 4
Loads the specified native agent library\&. After the library name, a comma\-separated list of options specific to the library can be used\&.
.sp
If the option
\fB\-agentlib:foo\fR
is specified, then the JVM attempts to load the library named
\fBlibfoo\&.so\fR
in the location specified by the
\fBLD_LIBRARY_PATH\fR
system variable (on OS X this variable is
\fBDYLD_LIBRARY_PATH\fR)\&.
.sp
The following example shows how to load the heap profiling tool (HPROF) library and get sample CPU information every 20 ms, with a stack depth of 3:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-agentlib:hprof=cpu=samples,interval=20,depth=3\fR
 
.fi
.if n \{\
.RE
.\}
The following example shows how to load the Java Debug Wire Protocol (JDWP) library and listen for the socket connection on port 8000, suspending the JVM before the main class loads:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-agentlib:jdwp=transport=dt_socket,server=y,address=8000\fR
 
.fi
.if n \{\
.RE
.\}
For more information about the native agent libraries, refer to the following:
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
The
\fBjava\&.lang\&.instrument\fR
package description at http://docs\&.oracle\&.com/javase/8/docs/api/java/lang/instrument/package\-summary\&.html
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Agent Command Line Options in the JVM Tools Interface guide at http://docs\&.oracle\&.com/javase/8/docs/platform/jvmti/jvmti\&.html#starting
.RE
.RE
.PP
\-agentpath:\fIpathname\fR[=\fIoptions\fR]
.RS 4
Loads the native agent library specified by the absolute path name\&. This option is equivalent to
\fB\-agentlib\fR
but uses the full path and file name of the library\&.
.RE
.PP
\-client
.RS 4
Selects the Java HotSpot Client VM\&. The 64\-bit version of the Java SE Development Kit (JDK) currently ignores this option and instead uses the Server JVM\&.
.sp
For default JVM selection, see Server\-Class Machine Detection at
http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/vm/server\-class\&.html
.RE
.PP
\-D\fIproperty\fR=\fIvalue\fR
.RS 4
Sets a system property value\&. The
\fIproperty\fR
variable is a string with no spaces that represents the name of the property\&. The
\fIvalue\fR
variable is a string that represents the value of the property\&. If
\fIvalue\fR
is a string with spaces, then enclose it in quotation marks (for example
\fB\-Dfoo="foo bar"\fR)\&.
.RE
.PP
\-d32
.RS 4
Runs the application in a 32\-bit environment\&. If a 32\-bit environment is not installed or is not supported, then an error will be reported\&. By default, the application is run in a 32\-bit environment unless a 64\-bit system is used\&.
.RE
.PP
\-d64
.RS 4
Runs the application in a 64\-bit environment\&. If a 64\-bit environment is not installed or is not supported, then an error will be reported\&. By default, the application is run in a 32\-bit environment unless a 64\-bit system is used\&.
.sp
Currently only the Java HotSpot Server VM supports 64\-bit operation, and the
\fB\-server\fR
option is implicit with the use of
\fB\-d64\fR\&. The
\fB\-client\fR
option is ignored with the use of
\fB\-d64\fR\&. This is subject to change in a future release\&.
.RE
.PP
\-disableassertions[:[\fIpackagename\fR]\&.\&.\&.|:\fIclassname\fR]
.br
\-da[:[\fIpackagename\fR]\&.\&.\&.|:\fIclassname\fR]
.RS 4
Disables assertions\&. By default, assertions are disabled in all packages and classes\&.
.sp
With no arguments,
\fB\-disableassertions\fR
(\fB\-da\fR) disables assertions in all packages and classes\&. With the
\fIpackagename\fR
argument ending in
\fB\&.\&.\&.\fR, the switch disables assertions in the specified package and any subpackages\&. If the argument is simply
\fB\&.\&.\&.\fR, then the switch disables assertions in the unnamed package in the current working directory\&. With the
\fIclassname\fR
argument, the switch disables assertions in the specified class\&.
.sp
The
\fB\-disableassertions\fR
(\fB\-da\fR) option applies to all class loaders and to system classes (which do not have a class loader)\&. There is one exception to this rule: if the option is provided with no arguments, then it does not apply to system classes\&. This makes it easy to disable assertions in all classes except for system classes\&. The
\fB\-disablesystemassertions\fR
option enables you to disable assertions in all system classes\&.
.sp
To explicitly enable assertions in specific packages or classes, use the
\fB\-enableassertions\fR
(\fB\-ea\fR) option\&. Both options can be used at the same time\&. For example, to run the
\fBMyClass\fR
application with assertions enabled in package
\fBcom\&.wombat\&.fruitbat\fR
(and any subpackages) but disabled in class
\fBcom\&.wombat\&.fruitbat\&.Brickbat\fR, use the following command:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava \-ea:com\&.wombat\&.fruitbat\&.\&.\&. \-da:com\&.wombat\&.fruitbat\&.Brickbat MyClass\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-disablesystemassertions
.br
\-dsa
.RS 4
Disables assertions in all system classes\&.
.RE
.PP
\-enableassertions[:[\fIpackagename\fR]\&.\&.\&.|:\fIclassname\fR]
.br
\-ea[:[\fIpackagename\fR]\&.\&.\&.|:\fIclassname\fR]
.RS 4
Enables assertions\&. By default, assertions are disabled in all packages and classes\&.
.sp
With no arguments,
\fB\-enableassertions\fR
(\fB\-ea\fR) enables assertions in all packages and classes\&. With the
\fIpackagename\fR
argument ending in
\fB\&.\&.\&.\fR, the switch enables assertions in the specified package and any subpackages\&. If the argument is simply
\fB\&.\&.\&.\fR, then the switch enables assertions in the unnamed package in the current working directory\&. With the
\fIclassname\fR
argument, the switch enables assertions in the specified class\&.
.sp
The
\fB\-enableassertions\fR
(\fB\-ea\fR) option applies to all class loaders and to system classes (which do not have a class loader)\&. There is one exception to this rule: if the option is provided with no arguments, then it does not apply to system classes\&. This makes it easy to enable assertions in all classes except for system classes\&. The
\fB\-enablesystemassertions\fR
option provides a separate switch to enable assertions in all system classes\&.
.sp
To explicitly disable assertions in specific packages or classes, use the
\fB\-disableassertions\fR
(\fB\-da\fR) option\&. If a single command contains multiple instances of these switches, then they are processed in order before loading any classes\&. For example, to run the
\fBMyClass\fR
application with assertions enabled only in package
\fBcom\&.wombat\&.fruitbat\fR
(and any subpackages) but disabled in class
\fBcom\&.wombat\&.fruitbat\&.Brickbat\fR, use the following command:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava \-ea:com\&.wombat\&.fruitbat\&.\&.\&. \-da:com\&.wombat\&.fruitbat\&.Brickbat MyClass\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-enablesystemassertions
.br
\-esa
.RS 4
Enables assertions in all system classes\&.
.RE
.PP
\-help
.br
\-?
.RS 4
Displays usage information for the
\fBjava\fR
command without actually running the JVM\&.
.RE
.PP
\-jar \fIfilename\fR
.RS 4
Executes a program encapsulated in a JAR file\&. The
\fIfilename\fR
argument is the name of a JAR file with a manifest that contains a line in the form
\fBMain\-Class:\fR\fIclassname\fR
that defines the class with the
\fBpublic static void main(String[] args)\fR
method that serves as your application\*(Aqs starting point\&.
.sp
When you use the
\fB\-jar\fR
option, the specified JAR file is the source of all user classes, and other class path settings are ignored\&.
.sp
For more information about JAR files, see the following resources:
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jar(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
The Java Archive (JAR) Files guide at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/jar/index\&.html
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Lesson: Packaging Programs in JAR Files at

http://docs\&.oracle\&.com/javase/tutorial/deployment/jar/index\&.html
.RE
.RE
.PP
\-javaagent:\fIjarpath\fR[=\fIoptions\fR]
.RS 4
Loads the specified Java programming language agent\&. For more information about instrumenting Java applications, see the
\fBjava\&.lang\&.instrument\fR
package description in the Java API documentation at http://docs\&.oracle\&.com/javase/8/docs/api/java/lang/instrument/package\-summary\&.html
.RE
.PP
\-jre\-restrict\-search
.RS 4
Includes user\-private JREs in the version search\&.
.RE
.PP
\-no\-jre\-restrict\-search
.RS 4
Excludes user\-private JREs from the version search\&.
.RE
.PP
\-server
.RS 4
Selects the Java HotSpot Server VM\&. The 64\-bit version of the JDK supports only the Server VM, so in that case the option is implicit\&.
.sp
For default JVM selection, see Server\-Class Machine Detection at
http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/vm/server\-class\&.html
.RE
.PP
\-showversion
.RS 4
Displays version information and continues execution of the application\&. This option is equivalent to the
\fB\-version\fR
option except that the latter instructs the JVM to exit after displaying version information\&.
.RE
.PP
\-splash:\fIimgname\fR
.RS 4
Shows the splash screen with the image specified by
\fIimgname\fR\&. For example, to show the
\fBsplash\&.gif\fR
file from the
\fBimages\fR
directory when starting your application, use the following option:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-splash:images/splash\&.gif\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-verbose:class
.RS 4
Displays information about each loaded class\&.
.RE
.PP
\-verbose:gc
.RS 4
Displays information about each garbage collection (GC) event\&.
.RE
.PP
\-verbose:jni
.RS 4
Displays information about the use of native methods and other Java Native Interface (JNI) activity\&.
.RE
.PP
\-version
.RS 4
Displays version information and then exits\&. This option is equivalent to the
\fB\-showversion\fR
option except that the latter does not instruct the JVM to exit after displaying version information\&.
.RE
.PP
\-version:\fIrelease\fR
.RS 4
Specifies the release version to be used for running the application\&. If the version of the
\fBjava\fR
command called does not meet this specification and an appropriate implementation is found on the system, then the appropriate implementation will be used\&.
.sp
The
\fIrelease\fR
argument specifies either the exact version string, or a list of version strings and ranges separated by spaces\&. A
\fIversion string\fR
is the developer designation of the version number in the following form:
\fB1\&.\fR\fIx\fR\fB\&.0_\fR\fIu\fR
(where
\fIx\fR
is the major version number, and
\fIu\fR
is the update version number)\&. A
\fIversion range\fR
is made up of a version string followed by a plus sign (\fB+\fR) to designate this version or later, or a part of a version string followed by an asterisk (\fB*\fR) to designate any version string with a matching prefix\&. Version strings and ranges can be combined using a space for a logical
\fIOR\fR
combination, or an ampersand (\fB&\fR) for a logical
\fIAND\fR
combination of two version strings/ranges\&. For example, if running the class or JAR file requires either JRE 6u13 (1\&.6\&.0_13), or any JRE 6 starting from 6u10 (1\&.6\&.0_10), specify the following:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-version:"1\&.6\&.0_13 1\&.6* & 1\&.6\&.0_10+"\fR
 
.fi
.if n \{\
.RE
.\}
Quotation marks are necessary only if there are spaces in the
\fIrelease\fR
parameter\&.
.sp
For JAR files, the preference is to specify version requirements in the JAR file manifest rather than on the command line\&.
.RE
.SS "Non\-Standard Options"
.PP
These options are general purpose options that are specific to the Java HotSpot Virtual Machine\&.
.PP
\-X
.RS 4
Displays help for all available
\fB\-X\fR
options\&.
.RE
.PP
\-Xbatch
.RS 4
Disables background compilation\&. By default, the JVM compiles the method as a background task, running the method in interpreter mode until the background compilation is finished\&. The
\fB\-Xbatch\fR
flag disables background compilation so that compilation of all methods proceeds as a foreground task until completed\&.
.sp
This option is equivalent to
\fB\-XX:\-BackgroundCompilation\fR\&.
.RE
.PP
\-Xbootclasspath:\fIpath\fR
.RS 4
Specifies a list of directories, JAR files, and ZIP archives separated by colons (:) to search for boot class files\&. These are used in place of the boot class files included in the JDK\&.
.sp
Do not deploy applications that use this option to override a class in
\fBrt\&.jar\fR, because this violates the JRE binary code license\&.
.RE
.PP
\-Xbootclasspath/a:\fIpath\fR
.RS 4
Specifies a list of directories, JAR files, and ZIP archives separated by colons (:) to append to the end of the default bootstrap class path\&.
.sp
Do not deploy applications that use this option to override a class in
\fBrt\&.jar\fR, because this violates the JRE binary code license\&.
.RE
.PP
\-Xbootclasspath/p:\fIpath\fR
.RS 4
Specifies a list of directories, JAR files, and ZIP archives separated by colons (:) to prepend to the front of the default bootstrap class path\&.
.sp
Do not deploy applications that use this option to override a class in
\fBrt\&.jar\fR, because this violates the JRE binary code license\&.
.RE
.PP
\-Xcheck:jni
.RS 4
Performs additional checks for Java Native Interface (JNI) functions\&. Specifically, it validates the parameters passed to the JNI function and the runtime environment data before processing the JNI request\&. Any invalid data encountered indicates a problem in the native code, and the JVM will terminate with an irrecoverable error in such cases\&. Expect a performance degradation when this option is used\&.
.RE
.PP
\-Xcomp
.RS 4
Forces compilation of methods on first invocation\&. By default, the Client VM (\fB\-client\fR) performs 1,000 interpreted method invocations and the Server VM (\fB\-server\fR) performs 10,000 interpreted method invocations to gather information for efficient compilation\&. Specifying the
\fB\-Xcomp\fR
option disables interpreted method invocations to increase compilation performance at the expense of efficiency\&.
.sp
You can also change the number of interpreted method invocations before compilation using the
\fB\-XX:CompileThreshold\fR
option\&.
.RE
.PP
\-Xdebug
.RS 4
Does nothing\&. Provided for backward compatibility\&.
.RE
.PP
\-Xdiag
.RS 4
Shows additional diagnostic messages\&.
.RE
.PP
\-Xfuture
.RS 4
Enables strict class\-file format checks that enforce close conformance to the class\-file format specification\&. Developers are encouraged to use this flag when developing new code because the stricter checks will become the default in future releases\&.
.RE
.PP
\-Xint
.RS 4
Runs the application in interpreted\-only mode\&. Compilation to native code is disabled, and all bytecode is executed by the interpreter\&. The performance benefits offered by the just in time (JIT) compiler are not present in this mode\&.
.RE
.PP
\-Xinternalversion
.RS 4
Displays more detailed JVM version information than the
\fB\-version\fR
option, and then exits\&.
.RE
.PP
\-Xloggc:\fIfilename\fR
.RS 4
Sets the file to which verbose GC events information should be redirected for logging\&. The information written to this file is similar to the output of
\fB\-verbose:gc\fR
with the time elapsed since the first GC event preceding each logged event\&. The
\fB\-Xloggc\fR
option overrides
\fB\-verbose:gc\fR
if both are given with the same
\fBjava\fR
command\&.
.sp
Example:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Xloggc:garbage\-collection\&.log\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-Xmaxjitcodesize=\fIsize\fR
.RS 4
Specifies the maximum code cache size (in bytes) for JIT\-compiled code\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. The default maximum code cache size is 240 MB; if you disable tiered compilation with the option
\fB\-XX:\-TieredCompilation\fR, then the default size is 48 MB:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Xmaxjitcodesize=240m\fR
 
.fi
.if n \{\
.RE
.\}
This option is equivalent to
\fB\-XX:ReservedCodeCacheSize\fR\&.
.RE
.PP
\-Xmixed
.RS 4
Executes all bytecode by the interpreter except for hot methods, which are compiled to native code\&.
.RE
.PP
\-Xmn\fIsize\fR
.RS 4
Sets the initial and maximum size (in bytes) of the heap for the young generation (nursery)\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&.
.sp
The young generation region of the heap is used for new objects\&. GC is performed in this region more often than in other regions\&. If the size for the young generation is too small, then a lot of minor garbage collections will be performed\&. If the size is too large, then only full garbage collections will be performed, which can take a long time to complete\&. Oracle recommends that you keep the size for the young generation between a half and a quarter of the overall heap size\&.
.sp
The following examples show how to set the initial and maximum size of young generation to 256 MB using various units:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Xmn256m\fR
\fB\-Xmn262144k\fR
\fB\-Xmn268435456\fR
 
.fi
.if n \{\
.RE
.\}
Instead of the
\fB\-Xmn\fR
option to set both the initial and maximum size of the heap for the young generation, you can use
\fB\-XX:NewSize\fR
to set the initial size and
\fB\-XX:MaxNewSize\fR
to set the maximum size\&.
.RE
.PP
\-Xms\fIsize\fR
.RS 4
Sets the minimum and the initial size (in bytes) of the heap\&. This value must be a multiple of 1024 and greater than 1 MB\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&.
.sp
The following examples show how to set the size of allocated memory to 6 MB using various units:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Xms6291456\fR
\fB\-Xms6144k\fR
\fB\-Xms6m\fR
 
.fi
.if n \{\
.RE
.\}
If you do not set this option, then the initial size will be set as the sum of the sizes allocated for the old generation and the young generation\&. The initial size of the heap for the young generation can be set using the
\fB\-Xmn\fR
option or the
\fB\-XX:NewSize\fR
option\&.

Note that the \fB\-XX:InitalHeapSize\fR option can also be used to set the initial heap size. If it appears after \fB\-Xms\fR on the command line, then the initial heap size gets set to the value specified with \fB\-XX:InitalHeapSize\fR.
.RE
.PP
\-Xmx\fIsize\fR
.RS 4
Specifies the maximum size (in bytes) of the memory allocation pool in bytes\&. This value must be a multiple of 1024 and greater than 2 MB\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. The default value is chosen at runtime based on system configuration\&. For server deployments,
\fB\-Xms\fR
and
\fB\-Xmx\fR
are often set to the same value\&. See the section "Ergonomics" in
\fIJava SE HotSpot Virtual Machine Garbage Collection Tuning Guide\fR
at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/vm/gctuning/index\&.html\&.
.sp
The following examples show how to set the maximum allowed size of allocated memory to 80 MB using various units:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Xmx83886080\fR
\fB\-Xmx81920k\fR
\fB\-Xmx80m\fR
 
.fi
.if n \{\
.RE
.\}
The
\fB\-Xmx\fR
option is equivalent to
\fB\-XX:MaxHeapSize\fR\&.
.RE
.PP
\-Xnoclassgc
.RS 4
Disables garbage collection (GC) of classes\&. This can save some GC time, which shortens interruptions during the application run\&.
.sp
When you specify
\fB\-Xnoclassgc\fR
at startup, the class objects in the application will be left untouched during GC and will always be considered live\&. This can result in more memory being permanently occupied which, if not used carefully, will throw an out of memory exception\&.
.RE
.PP
\-Xprof
.RS 4
Profiles the running program and sends profiling data to standard output\&. This option is provided as a utility that is useful in program development and is not intended to be used in production systems\&.
.RE
.PP
\-Xrs
.RS 4
Reduces the use of operating system signals by the JVM\&.
.sp
Shutdown hooks enable orderly shutdown of a Java application by running user cleanup code (such as closing database connections) at shutdown, even if the JVM terminates abruptly\&.
.sp
The JVM catches signals to implement shutdown hooks for unexpected termination\&. The JVM uses
\fBSIGHUP\fR,
\fBSIGINT\fR, and
\fBSIGTERM\fR
to initiate the running of shutdown hooks\&.
.sp
The JVM uses a similar mechanism to implement the feature of dumping thread stacks for debugging purposes\&. The JVM uses
\fBSIGQUIT\fR
to perform thread dumps\&.
.sp
Applications embedding the JVM frequently need to trap signals such as
\fBSIGINT\fR
or
\fBSIGTERM\fR, which can lead to interference with the JVM signal handlers\&. The
\fB\-Xrs\fR
option is available to address this issue\&. When
\fB\-Xrs\fR
is used, the signal masks for
\fBSIGINT\fR,
\fBSIGTERM\fR,
\fBSIGHUP\fR, and
\fBSIGQUIT\fR
are not changed by the JVM, and signal handlers for these signals are not installed\&.
.sp
There are two consequences of specifying
\fB\-Xrs\fR:
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBSIGQUIT\fR
thread dumps are not available\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
User code is responsible for causing shutdown hooks to run, for example, by calling
\fBSystem\&.exit()\fR
when the JVM is to be terminated\&.
.RE
.RE
.PP
\-Xshare:\fImode\fR
.RS 4
Sets the class data sharing (CDS) mode\&. Possible
\fImode\fR
arguments for this option include the following:
.PP
auto
.RS 4
Use CDS if possible\&. This is the default value for Java HotSpot 32\-Bit Client VM\&.
.RE
.PP
on
.RS 4
Require the use of CDS\&. Print an error message and exit if class data sharing cannot be used\&.
.RE
.PP
off
.RS 4
Do not use CDS\&. This is the default value for Java HotSpot 32\-Bit Server VM, Java HotSpot 64\-Bit Client VM, and Java HotSpot 64\-Bit Server VM\&.
.RE
.PP
dump
.RS 4
Manually generate the CDS archive\&. Specify the application class path as described in "Setting the Class Path "\&.
.sp
You should regenerate the CDS archive with each new JDK release\&.
.RE
.RE
.PP
\-XshowSettings:\fIcategory\fR
.RS 4
Shows settings and continues\&. Possible
\fIcategory\fR
arguments for this option include the following:
.PP
all
.RS 4
Shows all categories of settings\&. This is the default value\&.
.RE
.PP
locale
.RS 4
Shows settings related to locale\&.
.RE
.PP
properties
.RS 4
Shows settings related to system properties\&.
.RE
.PP
vm
.RS 4
Shows the settings of the JVM\&.
.RE
.RE
.PP
\-Xss\fIsize\fR
.RS 4
Sets the thread stack size (in bytes)\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate KB,
\fBm\fR
or
\fBM\fR
to indicate MB,
\fBg\fR
or
\fBG\fR
to indicate GB\&. The default value depends on the platform:
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Linux/ARM (32\-bit): 320 KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Linux/i386 (32\-bit): 320 KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Linux/x64 (64\-bit): 1024 KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
OS X (64\-bit): 1024 KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Oracle Solaris/i386 (32\-bit): 320 KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Oracle Solaris/x64 (64\-bit): 1024 KB
.RE
.sp
The following examples set the thread stack size to 1024 KB in different units:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Xss1m\fR
\fB\-Xss1024k\fR
\fB\-Xss1048576\fR
 
.fi
.if n \{\
.RE
.\}
This option is equivalent to
\fB\-XX:ThreadStackSize\fR\&.
.RE
.PP
\-Xusealtsigs
.RS 4
Use alternative signals instead of
\fBSIGUSR1\fR
and
\fBSIGUSR2\fR
for JVM internal signals\&. This option is equivalent to
\fB\-XX:+UseAltSigs\fR\&.
.RE

.PP
\-Xverify:\fImode\fR
.RS 4
Sets the mode of the bytecode verifier\&. Bytecode verification ensures that class files are properly formed and satisfy the constraints listed in section 4.10, "Verification of class Files" in the \fIThe Java Virtual Machine Specification\fR:
.sp
https://docs\&.oracle.com/javase/specs/jvms/se8/html/jvms-4\&.html#jvms-4\&.10
.sp
Do not turn off verification as this reduces the protection provided by Java and could cause problems due to ill-formed class files\&.
.sp
Possible
\fImode\fR
arguments for this option include the following:
.PP
remote
.RS 4
Verifies all bytecodes not loaded by the bootstrap class loader\&. This is the default behavior if you do not specify the
\fB\-Xverify\fR
option\&.
.RE
.PP
all
.RS 4
Enables verification of all bytecodes\&.
.RE
.PP
none
.RS 4
Disables verification of all bytecodes\&. Use of
\fB\-Xverify:none\fR
is unsupported\&.
.RE
.RE
.SS "Advanced Runtime Options"
.PP
These options control the runtime behavior of the Java HotSpot VM\&.
.PP
\-XX:+CheckEndorsedAndExtDirs
.RS 4
Enables the option to prevent the
\fBjava\fR
command from running a Java application if it uses the endorsed\-standards override mechanism or the extension mechanism\&. This option checks if an application is using one of these mechanisms by checking the following:
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
The
\fBjava\&.ext\&.dirs\fR
or
\fBjava\&.endorsed\&.dirs\fR
system property is set\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
The
\fBlib/endorsed\fR
directory exists and is not empty\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
The
\fBlib/ext\fR
directory contains any JAR files other than those of the JDK\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
The system\-wide platform\-specific extension directory contains any JAR files\&.
.RE
.RE
.PP
\-XX:+DisableAttachMechanism
.RS 4
Enables the option that disables the mechanism that lets tools attach to the JVM\&. By default, this option is disabled, meaning that the attach mechanism is enabled and you can use tools such as
\fBjcmd\fR,
\fBjstack\fR,
\fBjmap\fR, and
\fBjinfo\fR\&.
.RE
.PP
\-XX:ErrorFile=\fIfilename\fR
.RS 4
Specifies the path and file name to which error data is written when an irrecoverable error occurs\&. By default, this file is created in the current working directory and named
\fBhs_err_pid\fR\fIpid\fR\fB\&.log\fR
where
\fIpid\fR
is the identifier of the process that caused the error\&. The following example shows how to set the default log file (note that the identifier of the process is specified as
\fB%p\fR):
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:ErrorFile=\&./hs_err_pid%p\&.log\fR
 
.fi
.if n \{\
.RE
.\}
The following example shows how to set the error log to
\fB/var/log/java/java_error\&.log\fR:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:ErrorFile=/var/log/java/java_error\&.log\fR
 
.fi
.if n \{\
.RE
.\}
If the file cannot be created in the specified directory (due to insufficient space, permission problem, or another issue), then the file is created in the temporary directory for the operating system\&. The temporary directory is
\fB/tmp\fR\&.
.RE
.PP
\-XX:+FailOverToOldVerifier
.RS 4
Enables automatic failover to the old verifier when the new type checker fails\&. By default, this option is disabled and it is ignored (that is, treated as disabled) for classes with a recent bytecode version\&. You can enable it for classes with older versions of the bytecode\&.
.RE
.PP
\-XX:+FlightRecorder
.RS 4
Enables the use of the Java Flight Recorder (JFR) during the runtime of the application\&. This is a commercial feature that works in conjunction with the
\fB\-XX:+UnlockCommercialFeatures\fR
option as follows:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava \-XX:+UnlockCommercialFeatures \-XX:+FlightRecorder\fR
 
.fi
.if n \{\
.RE
.\}
If this option is not provided, Java Flight Recorder can still be enabled in a running JVM by providing the appropriate
\fBjcmd\fR
diagnostic commands\&.
.RE
.PP
\-XX:\-FlightRecorder
.RS 4
Disables the use of the Java Flight Recorder (JFR) during the runtime of the application\&. This is a commercial feature that works in conjunction with the
\fB\-XX:+UnlockCommercialFeatures\fR
option as follows:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava \-XX:+UnlockCommercialFeatures \-XX:\-FlightRecorder\fR
 
.fi
.if n \{\
.RE
.\}
If this option is provided, Java Flight Recorder cannot be enabled in a running JVM\&.
.RE
.PP
\-XX:FlightRecorderOptions=\fIparameter\fR=\fIvalue\fR
.RS 4
Sets the parameters that control the behavior of JFR\&. This is a commercial feature that works in conjunction with the
\fB\-XX:+UnlockCommercialFeatures\fR
option\&. This option can be used only when JFR is enabled (that is, the
\fB\-XX:+FlightRecorder\fR
option is specified)\&.
.sp
The following list contains all available JFR parameters:
.PP
defaultrecording={true|false}
.RS 4
Specifies whether the recording is a continuous background recording or if it runs for a limited time\&. By default, this parameter is set to
\fBfalse\fR
(recording runs for a limited time)\&. To make the recording run continuously, set the parameter to
\fBtrue\fR\&.
.RE
.PP
disk={true|false}
.RS 4
Specifies whether JFR should write a continuous recording to disk\&. By default, this parameter is set to
\fBfalse\fR
(continuous recording to disk is disabled)\&. To enable it, set the parameter to
\fBtrue\fR, and also set
\fBdefaultrecording=true\fR\&.
.RE
.PP
dumponexit={true|false}
.RS 4
Specifies whether a dump file of JFR data should be generated when the JVM terminates in a controlled manner\&. By default, this parameter is set to
\fBfalse\fR
(dump file on exit is not generated)\&. To enable it, set the parameter to
\fBtrue\fR, and also set
\fBdefaultrecording=true\fR\&.
.sp
The dump file is written to the location defined by the
\fBdumponexitpath\fR
parameter\&.
.RE
.PP
dumponexitpath=\fIpath\fR
.RS 4
Specifies the path and name of the dump file with JFR data that is created when the JVM exits in a controlled manner if you set the
\fBdumponexit=true\fR
parameter\&. Setting the path makes sense only if you also set
\fBdefaultrecording=true\fR\&.
.sp
If the specified path is a directory, the JVM assigns a file name that shows the creation date and time\&. If the specified path includes a file name and if that file already exists, the JVM creates a new file by appending the date and time stamp to the specified file name\&.
.RE
.PP
globalbuffersize=\fIsize\fR
.RS 4
Specifies the total amount of primary memory (in bytes) used for data retention\&. Append
\fBk\fR
or
\fBK\fR, to specify the size in KB,
\fBm\fR
or
\fBM\fR
to specify the size in MB,
\fBg\fR
or
\fBG\fR
to specify the size in GB\&. By default, the size is set to 462848 bytes\&.
.RE
.PP
loglevel={quiet|error|warning|info|debug|trace}
.RS 4
Specify the amount of data written to the log file by JFR\&. By default, it is set to
\fBinfo\fR\&.
.RE
.PP
maxage=\fItime\fR
.RS 4
Specifies the maximum age of disk data to keep for the default recording\&. Append
\fBs\fR
to specify the time in seconds,
\fBm\fR
for minutes,
\fBh\fR
for hours, or
\fBd\fR
for days (for example, specifying
\fB30s\fR
means 30 seconds)\&. By default, the maximum age is set to 15 minutes (\fB15m\fR)\&.
.sp
This parameter is valid only if you set the
\fBdisk=true\fR
parameter\&.
.RE
.PP
maxchunksize=\fIsize\fR
.RS 4
Specifies the maximum size (in bytes) of the data chunks in a recording\&. Append
\fBk\fR
or
\fBK\fR, to specify the size in KB,
\fBm\fR
or
\fBM\fR
to specify the size in MB,
\fBg\fR
or
\fBG\fR
to specify the size in GB\&. By default, the maximum size of data chunks is set to 12 MB\&.
.RE
.PP
maxsize=\fIsize\fR
.RS 4
Specifies the maximum size (in bytes) of disk data to keep for the default recording\&. Append
\fBk\fR
or
\fBK\fR, to specify the size in KB,
\fBm\fR
or
\fBM\fR
to specify the size in MB,
\fBg\fR
or
\fBG\fR
to specify the size in GB\&. By default, the maximum size of disk data is not limited, and this parameter is set to 0\&.
.sp
This parameter is valid only if you set the
\fBdisk=true\fR
parameter\&.
.RE
.PP
repository=\fIpath\fR
.RS 4
Specifies the repository (a directory) for temporary disk storage\&. By default, the system\(cqs temporary directory is used\&.
.RE
.PP
samplethreads={true|false}
.RS 4
Specifies whether thread sampling is enabled\&. Thread sampling occurs only if the sampling event is enabled along with this parameter\&. By default, this parameter is enabled\&.
.RE
.PP
settings=\fIpath\fR
.RS 4
Specifies the path and name of the event settings file (of type JFC)\&. By default, the
\fBdefault\&.jfc\fR
file is used, which is located in
\fBJAVA_HOME/jre/lib/jfr\fR\&.
.RE
.PP
stackdepth=\fIdepth\fR
.RS 4
Stack depth for stack traces by JFR\&. By default, the depth is set to 64 method calls\&. The maximum is 2048, minimum is 1\&.
.RE
.PP
threadbuffersize=\fIsize\fR
.RS 4
Specifies the per\-thread local buffer size (in bytes)\&. Append
\fBk\fR
or
\fBK\fR, to specify the size in KB,
\fBm\fR
or
\fBM\fR
to specify the size in MB,
\fBg\fR
or
\fBG\fR
to specify the size in GB\&. Higher values for this parameter allow more data gathering without contention to flush it to the global storage\&. It can increase application footprint in a thread\-rich environment\&. By default, the local buffer size is set to 5 KB\&.
.RE
.sp
You can specify values for multiple parameters by separating them with a comma\&. For example, to instruct JFR to write a continuous recording to disk, and set the maximum size of data chunks to 10 MB, specify the following:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:FlightRecorderOptions=defaultrecording=true,disk=true,maxchunksize=10M\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:LargePageSizeInBytes=\fIsize\fR
.RS 4
On Solaris, sets the maximum size (in bytes) for large pages used for Java heap\&. The
\fIsize\fR
argument must be a power of 2 (2, 4, 8, 16, \&.\&.\&.)\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. By default, the size is set to 0, meaning that the JVM chooses the size for large pages automatically\&.
.sp
The following example illustrates how to set the large page size to 4 megabytes (MB):
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:LargePageSizeInBytes=4m\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MaxDirectMemorySize=\fIsize\fR
.RS 4
Sets the maximum total size (in bytes) of the New I/O (the
\fBjava\&.nio\fR
package) direct\-buffer allocations\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. By default, the size is set to 0, meaning that the JVM chooses the size for NIO direct\-buffer allocations automatically\&.
.sp
The following examples illustrate how to set the NIO size to 1024 KB in different units:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxDirectMemorySize=1m\fR
\fB\-XX:MaxDirectMemorySize=1024k\fR
\fB\-XX:MaxDirectMemorySize=1048576\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:NativeMemoryTracking=\fImode\fR
.RS 4
Specifies the mode for tracking JVM native memory usage\&. Possible
\fImode\fR
arguments for this option include the following:
.PP
off
.RS 4
Do not track JVM native memory usage\&. This is the default behavior if you do not specify the
\fB\-XX:NativeMemoryTracking\fR
option\&.
.RE
.PP
summary
.RS 4
Only track memory usage by JVM subsystems, such as Java heap, class, code, and thread\&.
.RE
.PP
detail
.RS 4
In addition to tracking memory usage by JVM subsystems, track memory usage by individual
\fBCallSite\fR, individual virtual memory region and its committed regions\&.
.RE
.RE
.PP
\-XX:ObjectAlignmentInBytes=\fIalignment\fR
.RS 4
Sets the memory alignment of Java objects (in bytes)\&. By default, the value is set to 8 bytes\&. The specified value should be a power of two, and must be within the range of 8 and 256 (inclusive)\&. This option makes it possible to use compressed pointers with large Java heap sizes\&.
.sp
The heap size limit in bytes is calculated as:
.sp
\fB4GB * ObjectAlignmentInBytes\fR
.sp
Note: As the alignment value increases, the unused space between objects will also increase\&. As a result, you may not realize any benefits from using compressed pointers with large Java heap sizes\&.
.RE
.PP
\-XX:OnError=\fIstring\fR
.RS 4
Sets a custom command or a series of semicolon\-separated commands to run when an irrecoverable error occurs\&. If the string contains spaces, then it must be enclosed in quotation marks\&.
.sp
The following example shows how the
\fB\-XX:OnError\fR
option can be used to run the
\fBgcore\fR
command to create the core image, and the debugger is started to attach to the process in case of an irrecoverable error (the
\fB%p\fR
designates the current process):
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:OnError="gcore %p;dbx \- %p"\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:OnOutOfMemoryError=\fIstring\fR
.RS 4
Sets a custom command or a series of semicolon\-separated commands to run when an
\fBOutOfMemoryError\fR
exception is first thrown\&. If the string contains spaces, then it must be enclosed in quotation marks\&. For an example of a command string, see the description of the
\fB\-XX:OnError\fR
option\&.
.RE
.PP
\-XX:+PerfDataSaveToFile
.RS 4
If enabled, saves
jstat(1) binary data when the Java application exits\&. This binary data is saved in a file named
\fBhsperfdata_\fR\fI<pid>\fR, where
\fI<pid>\fR
is the process identifier of the Java application you ran\&. Use
\fBjstat\fR
to display the performance data contained in this file as follows:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjstat \-class file:///\fR\fB\fI<path>\fR\fR\fB/hsperfdata_\fR\fB\fI<pid>\fR\fR
\fBjstat \-gc file:///\fR\fB\fI<path>\fR\fR\fB/hsperfdata_\fR\fB\fI<pid>\fR\fR
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+PrintCommandLineFlags
.RS 4
Enables printing of ergonomically selected JVM flags that appeared on the command line\&. It can be useful to know the ergonomic values set by the JVM, such as the heap space size and the selected garbage collector\&. By default, this option is disabled and flags are not printed\&.
.RE
.PP
\-XX:+PrintNMTStatistics
.RS 4
Enables printing of collected native memory tracking data at JVM exit when native memory tracking is enabled (see
\fB\-XX:NativeMemoryTracking\fR)\&. By default, this option is disabled and native memory tracking data is not printed\&.
.RE
.PP
\-XX:+RelaxAccessControlCheck
.RS 4
Decreases the amount of access control checks in the verifier\&. By default, this option is disabled, and it is ignored (that is, treated as disabled) for classes with a recent bytecode version\&. You can enable it for classes with older versions of the bytecode\&.
.RE
.PP
\-XX:+ResourceManagement
.RS 4
Enables the use of Resource Management during the runtime of the application\&.
.sp
This is a commercial feature that requires you to also specify the
\fB\-XX:+UnlockCommercialFeatures\fR
option as follows:
.sp
\fBjava \-XX:+UnlockCommercialFeatures \-XX:+ResourceManagement\fR
.RE
.PP
\-XX:ResourceManagementSampleInterval=\fIvalue\fR (milliseconds)
.RS 4
Sets the parameter that controls the sampling interval for Resource Management measurements, in milliseconds\&.
.sp
This option can be used only when Resource Management is enabled (that is, the
\fB\-XX:+ResourceManagement\fR
option is specified)\&.
.RE
.PP
\-XX:SharedArchiveFile=\fIpath\fR
.RS 4
Specifies the path and name of the class data sharing (CDS) archive file
.RE
.PP
\-XX:SharedClassListFile=\fIfile_name\fR
.RS 4
Specifies the text file that contains the names of the class files to store in the class data sharing (CDS) archive\&. This file contains the full name of one class file per line, except slashes (\fB/\fR) replace dots (\fB\&.\fR)\&. For example, to specify the classes
\fBjava\&.lang\&.Object\fR
and
\fBhello\&.Main\fR, create a text file that contains the following two lines:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava/lang/Object\fR
\fBhello/Main\fR
 
.fi
.if n \{\
.RE
.\}
The class files that you specify in this text file should include the classes that are commonly used by the application\&. They may include any classes from the application, extension, or bootstrap class paths\&.
.RE
.PP
\-XX:+ShowMessageBoxOnError
.RS 4
Enables displaying of a dialog box when the JVM experiences an irrecoverable error\&. This prevents the JVM from exiting and keeps the process active so that you can attach a debugger to it to investigate the cause of the error\&. By default, this option is disabled\&.
.RE
.PP
\-XX:StartFlightRecording=\fIparameter\fR=\fIvalue\fR
.RS 4
Starts a JFR recording for the Java application\&. This is a commercial feature that works in conjunction with the
\fB\-XX:+UnlockCommercialFeatures\fR
option\&. This option is equivalent to the
\fBJFR\&.start\fR
diagnostic command that starts a recording during runtime\&. You can set the following parameters when starting a JFR recording:
.PP
compress={true|false}
.RS 4
Specifies whether to compress the JFR recording log file (of type JFR) on the disk using the
\fBgzip\fR
file compression utility\&. This parameter is valid only if the
\fBfilename\fR
parameter is specified\&. By default it is set to
\fBfalse\fR
(recording is not compressed)\&. To enable compression, set the parameter to
\fBtrue\fR\&.
.RE
.PP
defaultrecording={true|false}
.RS 4
Specifies whether the recording is a continuous background recording or if it runs for a limited time\&. By default, this parameter is set to
\fBfalse\fR
(recording runs for a limited time)\&. To make the recording run continuously, set the parameter to
\fBtrue\fR\&.
.RE
.PP
delay=\fItime\fR
.RS 4
Specifies the delay between the Java application launch time and the start of the recording\&. Append
\fBs\fR
to specify the time in seconds,
\fBm\fR
for minutes,
\fBh\fR
for hours, or
\fBd\fR
for days (for example, specifying
\fB10m\fR
means 10 minutes)\&. By default, there is no delay, and this parameter is set to 0\&.
.RE
.PP
dumponexit={true|false}
.RS 4
Specifies whether a dump file of JFR data should be generated when the JVM terminates in a controlled manner\&. By default, this parameter is set to
\fBfalse\fR
(dump file on exit is not generated)\&. To enable it, set the parameter to
\fBtrue\fR\&.
.sp
The dump file is written to the location defined by the
\fBfilename\fR
parameter\&.
.sp
Example:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:StartFlightRecording=name=test,filename=D:\etest\&.jfr,dumponexit=true\fR
.fi
.if n \{\
.RE
.\}
.RE
.PP
duration=\fItime\fR
.RS 4
Specifies the duration of the recording\&. Append
\fBs\fR
to specify the time in seconds,
\fBm\fR
for minutes,
\fBh\fR
for hours, or
\fBd\fR
for days (for example, specifying
\fB5h\fR
means 5 hours)\&. By default, the duration is not limited, and this parameter is set to 0\&.
.RE
.PP
filename=\fIpath\fR
.RS 4
Specifies the path and name of the JFR recording log file\&.
.RE
.PP
name=\fIidentifier\fR
.RS 4
Specifies the identifier for the JFR recording\&. By default, it is set to
\fBRecording x\fR\&.
.RE
.PP
maxage=\fItime\fR
.RS 4
Specifies the maximum age of disk data to keep for the default recording\&. Append
\fBs\fR
to specify the time in seconds,
\fBm\fR
for minutes,
\fBh\fR
for hours, or
\fBd\fR
for days (for example, specifying
\fB30s\fR
means 30 seconds)\&. By default, the maximum age is set to 15 minutes (\fB15m\fR)\&.
.RE
.PP
maxsize=\fIsize\fR
.RS 4
Specifies the maximum size (in bytes) of disk data to keep for the default recording\&. Append
\fBk\fR
or
\fBK\fR, to specify the size in KB,
\fBm\fR
or
\fBM\fR
to specify the size in MB,
\fBg\fR
or
\fBG\fR
to specify the size in GB\&. By default, the maximum size of disk data is not limited, and this parameter is set to 0\&.
.RE
.PP
settings=\fIpath\fR
.RS 4
Specifies the path and name of the event settings file (of type JFC)\&. By default, the
\fBdefault\&.jfc\fR
file is used, which is located in
\fBJAVA_HOME/jre/lib/jfr\fR\&.
.RE
.sp
You can specify values for multiple parameters by separating them with a comma\&. For example, to save the recording to test\&.jfr in the current working directory, and instruct JFR to compress the log file, specify the following:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:StartFlightRecording=filename=test\&.jfr,compress=true\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:ThreadStackSize=\fIsize\fR
.RS 4
Sets the thread stack size (in bytes)\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. The default value depends on the platform:
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Linux/ARM (32\-bit): 320 KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Linux/i386 (32\-bit): 320 KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Linux/x64 (64\-bit): 1024 KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
OS X (64\-bit): 1024 KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Oracle Solaris/i386 (32\-bit): 320 KB
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Oracle Solaris/x64 (64\-bit): 1024 KB
.RE
.sp
The following examples show how to set the thread stack size to 1024 KB in different units:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:ThreadStackSize=1m\fR
\fB\-XX:ThreadStackSize=1024k\fR
\fB\-XX:ThreadStackSize=1048576\fR
 
.fi
.if n \{\
.RE
.\}
This option is equivalent to
\fB\-Xss\fR\&.
.RE
.PP
\-XX:+TraceClassLoading
.RS 4
Enables tracing of classes as they are loaded\&. By default, this option is disabled and classes are not traced\&.
.RE
.PP
\-XX:+TraceClassLoadingPreorder
.RS 4
Enables tracing of all loaded classes in the order in which they are referenced\&. By default, this option is disabled and classes are not traced\&.
.RE
.PP
\-XX:+TraceClassResolution
.RS 4
Enables tracing of constant pool resolutions\&. By default, this option is disabled and constant pool resolutions are not traced\&.
.RE
.PP
\-XX:+TraceClassUnloading
.RS 4
Enables tracing of classes as they are unloaded\&. By default, this option is disabled and classes are not traced\&.
.RE
.PP
\-XX:+TraceLoaderConstraints
.RS 4
Enables tracing of the loader constraints recording\&. By default, this option is disabled and loader constraints recording is not traced\&.
.RE
.PP
\-XX:+UnlockCommercialFeatures
.RS 4
Enables the use of commercial features\&. Commercial features are included with Oracle Java SE Advanced or Oracle Java SE Suite packages, as defined on the
\fIJava SE Products\fR
page at http://www\&.oracle\&.com/technetwork/java/javase/terms/products/index\&.html
.sp
By default, this option is disabled and the JVM runs without the commercial features\&. Once they were enabled for a JVM process, it is not possible to disable their use for that process\&.
.sp
If this option is not provided, commercial features can still be unlocked in a running JVM by using the appropriate
\fBjcmd\fR
diagnostic commands\&.
.RE
.PP
\-XX:+UseAltSigs
.RS 4
Enables the use of alternative signals instead of
\fBSIGUSR1\fR
and
\fBSIGUSR2\fR
for JVM internal signals\&. By default, this option is disabled and alternative signals are not used\&. This option is equivalent to
\fB\-Xusealtsigs\fR\&.
.RE
.PP
\-XX:+UseAppCDS
.RS 4
Enables application class data sharing (AppCDS)\&. To use AppCDS, you must also specify values for the options
\fB\-XX:SharedClassListFile\fR
and
\fB\-XX:SharedArchiveFile\fR
during both CDS dump time (see the option
\fB\-Xshare:dump\fR) and application run time\&.
.sp
This is a commercial feature that requires you to also specify the
\fB\-XX:+UnlockCommercialFeatures\fR
option\&. This is also an experimental feature; it may change in future releases\&.
.sp
See "Application Class Data Sharing"\&.
.RE
.PP
\-XX:\-UseBiasedLocking
.RS 4
Disables the use of biased locking\&. Some applications with significant amounts of uncontended synchronization may attain significant speedups with this flag enabled, whereas applications with certain patterns of locking may see slowdowns\&. For more information about the biased locking technique, see the example in Java Tuning White Paper at http://www\&.oracle\&.com/technetwork/java/tuning\-139912\&.html#section4\&.2\&.5
.sp
By default, this option is enabled\&.
.RE
.PP
\-XX:\-UseCompressedOops
.RS 4
Disables the use of compressed pointers\&. By default, this option is enabled, and compressed pointers are used when Java heap sizes are less than 32 GB\&. When this option is enabled, object references are represented as 32\-bit offsets instead of 64\-bit pointers, which typically increases performance when running the application with Java heap sizes less than 32 GB\&. This option works only for 64\-bit JVMs\&.
.sp
It is also possible to use compressed pointers when Java heap sizes are greater than 32GB\&. See the
\fB\-XX:ObjectAlignmentInBytes\fR
option\&.
.RE
.PP
\-XX:+UseHugeTLBFS
.RS 4
This option for Linux is the equivalent of specifying
\fB\-XX:+UseLargePages\fR\&. This option is disabled by default\&. This option pre\-allocates all large pages up\-front, when memory is reserved; consequently the JVM cannot dynamically grow or shrink large pages memory areas; see
\fB\-XX:UseTransparentHugePages\fR
if you want this behavior\&.
.sp
For more information, see "Large Pages"\&.
.RE
.PP
\-XX:+UseLargePages
.RS 4
Enables the use of large page memory\&. By default, this option is disabled and large page memory is not used\&.
.sp
For more information, see "Large Pages"\&.
.RE
.PP
\-XX:+UseMembar
.RS 4
Enables issuing of membars on thread state transitions\&. This option is disabled by default on all platforms except ARM servers, where it is enabled\&. (It is recommended that you do not disable this option on ARM servers\&.)
.RE
.PP
\-XX:+UsePerfData
.RS 4
Enables the
\fBperfdata\fR
feature\&. This option is enabled by default to allow JVM monitoring and performance testing\&. Disabling it suppresses the creation of the
\fBhsperfdata_userid\fR
directories\&. To disable the
\fBperfdata\fR
feature, specify
\fB\-XX:\-UsePerfData\fR\&.
.RE
.PP
\-XX:+UseTransparentHugePages
.RS 4
On Linux, enables the use of large pages that can dynamically grow or shrink\&. This option is disabled by default\&. You may encounter performance problems with transparent huge pages as the OS moves other pages around to create huge pages; this option is made available for experimentation\&.
.sp
For more information, see "Large Pages"\&.
.RE
.PP
\-XX:+AllowUserSignalHandlers
.RS 4
Enables installation of signal handlers by the application\&. By default, this option is disabled and the application is not allowed to install signal handlers\&.
.RE
.SS "Advanced JIT Compiler Options"
.PP
These options control the dynamic just\-in\-time (JIT) compilation performed by the Java HotSpot VM\&.
.PP
\-XX:+AggressiveOpts
.RS 4
Enables the use of aggressive performance optimization features, which are expected to become default in upcoming releases\&. By default, this option is disabled and experimental performance features are not used\&.
.RE
.PP
\-XX:AllocateInstancePrefetchLines=\fIlines\fR
.RS 4
Sets the number of lines to prefetch ahead of the instance allocation pointer\&. By default, the number of lines to prefetch is set to 1:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:AllocateInstancePrefetchLines=1\fR
 
.fi
.if n \{\
.RE
.\}
Only the Java HotSpot Server VM supports this option\&.
.RE
.PP
\-XX:AllocatePrefetchDistance=\fIsize\fR
.RS 4
Sets the size (in bytes) of the prefetch distance for object allocation\&. Memory about to be written with the value of new objects is prefetched up to this distance starting from the address of the last allocated object\&. Each Java thread has its own allocation point\&.
.sp
Negative values denote that prefetch distance is chosen based on the platform\&. Positive values are bytes to prefetch\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. The default value is set to \-1\&.
.sp
The following example shows how to set the prefetch distance to 1024 bytes:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:AllocatePrefetchDistance=1024\fR
 
.fi
.if n \{\
.RE
.\}
Only the Java HotSpot Server VM supports this option\&.
.RE
.PP
\-XX:AllocatePrefetchInstr=\fIinstruction\fR
.RS 4
Sets the prefetch instruction to prefetch ahead of the allocation pointer\&. Only the Java HotSpot Server VM supports this option\&. Possible values are from 0 to 3\&. The actual instructions behind the values depend on the platform\&. By default, the prefetch instruction is set to 0:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:AllocatePrefetchInstr=0\fR
 
.fi
.if n \{\
.RE
.\}
Only the Java HotSpot Server VM supports this option\&.
.RE
.PP
\-XX:AllocatePrefetchLines=\fIlines\fR
.RS 4
Sets the number of cache lines to load after the last object allocation by using the prefetch instructions generated in compiled code\&. The default value is 1 if the last allocated object was an instance, and 3 if it was an array\&.
.sp
The following example shows how to set the number of loaded cache lines to 5:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:AllocatePrefetchLines=5\fR
 
.fi
.if n \{\
.RE
.\}
Only the Java HotSpot Server VM supports this option\&.
.RE
.PP
\-XX:AllocatePrefetchStepSize=\fIsize\fR
.RS 4
Sets the step size (in bytes) for sequential prefetch instructions\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. By default, the step size is set to 16 bytes:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:AllocatePrefetchStepSize=16\fR
 
.fi
.if n \{\
.RE
.\}
Only the Java HotSpot Server VM supports this option\&.
.RE
.PP
\-XX:AllocatePrefetchStyle=\fIstyle\fR
.RS 4
Sets the generated code style for prefetch instructions\&. The
\fIstyle\fR
argument is an integer from 0 to 3:
.PP
0
.RS 4
Do not generate prefetch instructions\&.
.RE
.PP
1
.RS 4
Execute prefetch instructions after each allocation\&. This is the default parameter\&.
.RE
.PP
2
.RS 4
Use the thread\-local allocation block (TLAB) watermark pointer to determine when prefetch instructions are executed\&.
.RE
.PP
3
.RS 4
Use BIS instruction on SPARC for allocation prefetch\&.
.RE
.sp
Only the Java HotSpot Server VM supports this option\&.
.RE
.PP
\-XX:+BackgroundCompilation
.RS 4
Enables background compilation\&. This option is enabled by default\&. To disable background compilation, specify
\fB\-XX:\-BackgroundCompilation\fR
(this is equivalent to specifying
\fB\-Xbatch\fR)\&.
.RE
.PP
\-XX:CICompilerCount=\fIthreads\fR
.RS 4
Sets the number of compiler threads to use for compilation\&. By default, the number of threads is set to 2 for the server JVM, to 1 for the client JVM, and it scales to the number of cores if tiered compilation is used\&. The following example shows how to set the number of threads to 2:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CICompilerCount=2\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:CodeCacheMinimumFreeSpace=\fIsize\fR
.RS 4
Sets the minimum free space (in bytes) required for compilation\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. When less than the minimum free space remains, compiling stops\&. By default, this option is set to 500 KB\&. The following example shows how to set the minimum free space to 1024 MB:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CodeCacheMinimumFreeSpace=1024m\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:CompileCommand=\fIcommand\fR,\fImethod\fR[,\fIoption\fR]
.RS 4
Specifies a command to perform on a method\&. For example, to exclude the
\fBindexOf()\fR
method of the
\fBString\fR
class from being compiled, use the following:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileCommand=exclude,java/lang/String\&.indexOf\fR
 
.fi
.if n \{\
.RE
.\}
Note that the full class name is specified, including all packages and subpackages separated by a slash (\fB/\fR)\&. For easier cut and paste operations, it is also possible to use the method name format produced by the
\fB\-XX:+PrintCompilation\fR
and
\fB\-XX:+LogCompilation\fR
options:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileCommand=exclude,java\&.lang\&.String::indexOf\fR
 
.fi
.if n \{\
.RE
.\}
If the method is specified without the signature, the command will be applied to all methods with the specified name\&. However, you can also specify the signature of the method in the class file format\&. In this case, you should enclose the arguments in quotation marks, because otherwise the shell treats the semicolon as command end\&. For example, if you want to exclude only the
\fBindexOf(String)\fR
method of the
\fBString\fR
class from being compiled, use the following:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileCommand="exclude,java/lang/String\&.indexOf,(Ljava/lang/String;)I"\fR
 
.fi
.if n \{\
.RE
.\}
You can also use the asterisk (*) as a wildcard for class and method names\&. For example, to exclude all
\fBindexOf()\fR
methods in all classes from being compiled, use the following:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileCommand=exclude,*\&.indexOf\fR
 
.fi
.if n \{\
.RE
.\}
The commas and periods are aliases for spaces, making it easier to pass compiler commands through a shell\&. You can pass arguments to
\fB\-XX:CompileCommand\fR
using spaces as separators by enclosing the argument in quotation marks:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileCommand="exclude java/lang/String indexOf"\fR
 
.fi
.if n \{\
.RE
.\}
Note that after parsing the commands passed on the command line using the
\fB\-XX:CompileCommand\fR
options, the JIT compiler then reads commands from the
\fB\&.hotspot_compiler\fR
file\&. You can add commands to this file or specify a different file using the
\fB\-XX:CompileCommandFile\fR
option\&.
.sp
To add several commands, either specify the
\fB\-XX:CompileCommand\fR
option multiple times, or separate each argument with the newline separator (\fB\en\fR)\&. The following commands are available:
.PP
break
.RS 4
Set a breakpoint when debugging the JVM to stop at the beginning of compilation of the specified method\&.
.RE
.PP
compileonly
.RS 4
Exclude all methods from compilation except for the specified method\&. As an alternative, you can use the
\fB\-XX:CompileOnly\fR
option, which allows to specify several methods\&.
.RE
.PP
dontinline
.RS 4
Prevent inlining of the specified method\&.
.RE
.PP
exclude
.RS 4
Exclude the specified method from compilation\&.
.RE
.PP
help
.RS 4
Print a help message for the
\fB\-XX:CompileCommand\fR
option\&.
.RE
.PP
inline
.RS 4
Attempt to inline the specified method\&.
.RE
.PP
log
.RS 4
Exclude compilation logging (with the
\fB\-XX:+LogCompilation\fR
option) for all methods except for the specified method\&. By default, logging is performed for all compiled methods\&.
.RE
.PP
option
.RS 4
This command can be used to pass a JIT compilation option to the specified method in place of the last argument (\fIoption\fR)\&. The compilation option is set at the end, after the method name\&. For example, to enable the
\fBBlockLayoutByFrequency\fR
option for the
\fBappend()\fR
method of the
\fBStringBuffer\fR
class, use the following:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileCommand=option,java/lang/StringBuffer\&.append,BlockLayoutByFrequency\fR
 
.fi
.if n \{\
.RE
.\}
You can specify multiple compilation options, separated by commas or spaces\&.
.RE
.PP
print
.RS 4
Print generated assembler code after compilation of the specified method\&.
.RE
.PP
quiet
.RS 4
Do not print the compile commands\&. By default, the commands that you specify with the \-\fBXX:CompileCommand\fR
option are printed; for example, if you exclude from compilation the
\fBindexOf()\fR
method of the
\fBString\fR
class, then the following will be printed to standard output:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBCompilerOracle: exclude java/lang/String\&.indexOf\fR
 
.fi
.if n \{\
.RE
.\}
You can suppress this by specifying the
\fB\-XX:CompileCommand=quiet\fR
option before other
\fB\-XX:CompileCommand\fR
options\&.
.RE
.RE
.PP
\-XX:CompileCommandFile=\fIfilename\fR
.RS 4
Sets the file from which JIT compiler commands are read\&. By default, the
\fB\&.hotspot_compiler\fR
file is used to store commands performed by the JIT compiler\&.
.sp
Each line in the command file represents a command, a class name, and a method name for which the command is used\&. For example, this line prints assembly code for the
\fBtoString()\fR
method of the
\fBString\fR
class:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBprint java/lang/String toString\fR
 
.fi
.if n \{\
.RE
.\}
For more information about specifying the commands for the JIT compiler to perform on methods, see the
\fB\-XX:CompileCommand\fR
option\&.
.RE
.PP
\-XX:CompileOnly=\fImethods\fR
.RS 4
Sets the list of methods (separated by commas) to which compilation should be restricted\&. Only the specified methods will be compiled\&. Specify each method with the full class name (including the packages and subpackages)\&. For example, to compile only the
\fBlength()\fR
method of the
\fBString\fR
class and the
\fBsize()\fR
method of the
\fBList\fR
class, use the following:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileOnly=java/lang/String\&.length,java/util/List\&.size\fR
 
.fi
.if n \{\
.RE
.\}
Note that the full class name is specified, including all packages and subpackages separated by a slash (\fB/\fR)\&. For easier cut and paste operations, it is also possible to use the method name format produced by the
\fB\-XX:+PrintCompilation\fR
and
\fB\-XX:+LogCompilation\fR
options:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileOnly=java\&.lang\&.String::length,java\&.util\&.List::size\fR
 
.fi
.if n \{\
.RE
.\}
Although wildcards are not supported, you can specify only the class or package name to compile all methods in that class or package, as well as specify just the method to compile methods with this name in any class:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileOnly=java/lang/String\fR
\fB\-XX:CompileOnly=java/lang\fR
\fB\-XX:CompileOnly=\&.length\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:CompileThreshold=\fIinvocations\fR
.RS 4
Sets the number of interpreted method invocations before compilation\&. By default, in the server JVM, the JIT compiler performs 10,000 interpreted method invocations to gather information for efficient compilation\&. For the client JVM, the default setting is 1,500 invocations\&. This option is ignored when tiered compilation is enabled; see the option
\fB\-XX:+TieredCompilation\fR\&. The following example shows how to set the number of interpreted method invocations to 5,000:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CompileThreshold=5000\fR
 
.fi
.if n \{\
.RE
.\}
You can completely disable interpretation of Java methods before compilation by specifying the
\fB\-Xcomp\fR
option\&.
.RE
.PP
\-XX:+DoEscapeAnalysis
.RS 4
Enables the use of escape analysis\&. This option is enabled by default\&. To disable the use of escape analysis, specify
\fB\-XX:\-DoEscapeAnalysis\fR\&. Only the Java HotSpot Server VM supports this option\&.
.RE
.PP
\-XX:InitialCodeCacheSize=\fIsize\fR
.RS 4
Sets the initial code cache size (in bytes)\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. The default value is set to 500 KB\&. The initial code cache size should be not less than the system\*(Aqs minimal memory page size\&. The following example shows how to set the initial code cache size to 32 KB:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:InitialCodeCacheSize=32k\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+Inline
.RS 4
Enables method inlining\&. This option is enabled by default to increase performance\&. To disable method inlining, specify
\fB\-XX:\-Inline\fR\&.
.RE
.PP
\-XX:InlineSmallCode=\fIsize\fR
.RS 4
Sets the maximum code size (in bytes) for compiled methods that should be inlined\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. Only compiled methods with the size smaller than the specified size will be inlined\&. By default, the maximum code size is set to 1000 bytes:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:InlineSmallCode=1000\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+LogCompilation
.RS 4
Enables logging of compilation activity to a file named
\fBhotspot\&.log\fR
in the current working directory\&. You can specify a different log file path and name using the
\fB\-XX:LogFile\fR
option\&.
.sp
By default, this option is disabled and compilation activity is not logged\&. The
\fB\-XX:+LogCompilation\fR
option has to be used together with the
\fB\-XX:UnlockDiagnosticVMOptions\fR
option that unlocks diagnostic JVM options\&.
.sp
You can enable verbose diagnostic output with a message printed to the console every time a method is compiled by using the
\fB\-XX:+PrintCompilation\fR
option\&.
.RE
.PP
\-XX:MaxInlineSize=\fIsize\fR
.RS 4
Sets the maximum bytecode size (in bytes) of a method to be inlined\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. By default, the maximum bytecode size is set to 35 bytes:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxInlineSize=35\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MaxNodeLimit=\fInodes\fR
.RS 4
Sets the maximum number of nodes to be used during single method compilation\&. By default, the maximum number of nodes is set to 65,000:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxNodeLimit=65000\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MaxTrivialSize=\fIsize\fR
.RS 4
Sets the maximum bytecode size (in bytes) of a trivial method to be inlined\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. By default, the maximum bytecode size of a trivial method is set to 6 bytes:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxTrivialSize=6\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+OptimizeStringConcat
.RS 4
Enables the optimization of
\fBString\fR
concatenation operations\&. This option is enabled by default\&. To disable the optimization of
\fBString\fR
concatenation operations, specify
\fB\-XX:\-OptimizeStringConcat\fR\&. Only the Java HotSpot Server VM supports this option\&.
.RE
.PP
\-XX:+PrintAssembly
.RS 4
Enables printing of assembly code for bytecoded and native methods by using the external
\fBdisassembler\&.so\fR
library\&. This enables you to see the generated code, which may help you to diagnose performance issues\&.
.sp
By default, this option is disabled and assembly code is not printed\&. The
\fB\-XX:+PrintAssembly\fR
option has to be used together with the
\fB\-XX:UnlockDiagnosticVMOptions\fR
option that unlocks diagnostic JVM options\&.
.RE
.PP
\-XX:+PrintCompilation
.RS 4
Enables verbose diagnostic output from the JVM by printing a message to the console every time a method is compiled\&. This enables you to see which methods actually get compiled\&. By default, this option is disabled and diagnostic output is not printed\&.
.sp
You can also log compilation activity to a file by using the
\fB\-XX:+LogCompilation\fR
option\&.
.RE
.PP
\-XX:+PrintInlining
.RS 4
Enables printing of inlining decisions\&. This enables you to see which methods are getting inlined\&.
.sp
By default, this option is disabled and inlining information is not printed\&. The
\fB\-XX:+PrintInlining\fR
option has to be used together with the
\fB\-XX:+UnlockDiagnosticVMOptions\fR
option that unlocks diagnostic JVM options\&.
.RE
.PP
\-XX:ReservedCodeCacheSize=\fIsize\fR
.RS 4
Sets the maximum code cache size (in bytes) for JIT\-compiled code\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. The default maximum code cache size is 240 MB; if you disable tiered compilation with the option
\fB\-XX:\-TieredCompilation\fR, then the default size is 48 MB\&. This option has a limit of 2 GB; otherwise, an error is generated\&. The maximum code cache size should not be less than the initial code cache size; see the option
\fB\-XX:InitialCodeCacheSize\fR\&. This option is equivalent to
\fB\-Xmaxjitcodesize\fR\&.
.RE
.PP
\-XX:RTMAbortRatio=\fIabort_ratio\fR
.RS 4
The RTM abort ratio is specified as a percentage (%) of all executed RTM transactions\&. If a number of aborted transactions becomes greater than this ratio, then the compiled code will be deoptimized\&. This ratio is used when the
\fB\-XX:+UseRTMDeopt\fR
option is enabled\&. The default value of this option is 50\&. This means that the compiled code will be deoptimized if 50% of all transactions are aborted\&.
.RE
.PP
\-XX:RTMRetryCount=\fInumber_of_retries\fR
.RS 4
RTM locking code will be retried, when it is aborted or busy, the number of times specified by this option before falling back to the normal locking mechanism\&. The default value for this option is 5\&. The
\fB\-XX:UseRTMLocking\fR
option must be enabled\&.
.RE
.PP
\-XX:\-TieredCompilation
.RS 4
Disables the use of tiered compilation\&. By default, this option is enabled\&. Only the Java HotSpot Server VM supports this option\&.
.RE
.PP
\-XX:+UseAES
.RS 4
Enables hardware\-based AES intrinsics for Intel, AMD, and SPARC hardware\&. Intel Westmere (2010 and newer), AMD Bulldozer (2011 and newer), and SPARC (T4 and newer) are the supported hardware\&. UseAES is used in conjunction with UseAESIntrinsics\&.
.RE
.PP
\-XX:+UseAESIntrinsics
.RS 4
UseAES and UseAESIntrinsics flags are enabled by default and are supported only for Java HotSpot Server VM 32\-bit and 64\-bit\&. To disable hardware\-based AES intrinsics, specify
\fB\-XX:\-UseAES \-XX:\-UseAESIntrinsics\fR\&. For example, to enable hardware AES, use the following flags:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:+UseAES \-XX:+UseAESIntrinsics\fR
 
.fi
.if n \{\
.RE
.\}
To support UseAES and UseAESIntrinsics flags for 32\-bit and 64\-bit use
\fB\-server\fR
option to choose Java HotSpot Server VM\&. These flags are not supported on Client VM\&.
.RE
.PP
\-XX:+UseCodeCacheFlushing
.RS 4
Enables flushing of the code cache before shutting down the compiler\&. This option is enabled by default\&. To disable flushing of the code cache before shutting down the compiler, specify
\fB\-XX:\-UseCodeCacheFlushing\fR\&.
.RE
.PP
\-XX:+UseCondCardMark
.RS 4
Enables checking of whether the card is already marked before updating the card table\&. This option is disabled by default and should only be used on machines with multiple sockets, where it will increase performance of Java applications that rely heavily on concurrent operations\&. Only the Java HotSpot Server VM supports this option\&.
.RE
.PP
\-XX:+UseRTMDeopt
.RS 4
Auto\-tunes RTM locking depending on the abort ratio\&. This ratio is specified by
\fB\-XX:RTMAbortRatio\fR
option\&. If the number of aborted transactions exceeds the abort ratio, then the method containing the lock will be deoptimized and recompiled with all locks as normal locks\&. This option is disabled by default\&. The
\fB\-XX:+UseRTMLocking\fR
option must be enabled\&.
.RE
.PP
\-XX:+UseRTMLocking
.RS 4
Generate Restricted Transactional Memory (RTM) locking code for all inflated locks, with the normal locking mechanism as the fallback handler\&. This option is disabled by default\&. Options related to RTM are only available for the Java HotSpot Server VM on x86 CPUs that support Transactional Synchronization Extensions (TSX)\&.
.sp
RTM is part of Intel\*(Aqs TSX, which is an x86 instruction set extension and facilitates the creation of multithreaded applications\&. RTM introduces the new instructions
\fBXBEGIN\fR,
\fBXABORT\fR,
\fBXEND\fR, and
\fBXTEST\fR\&. The
\fBXBEGIN\fR
and
\fBXEND\fR
instructions enclose a set of instructions to run as a transaction\&. If no conflict is found when running the transaction, the memory and register modifications are committed together at the
\fBXEND\fR
instruction\&. The
\fBXABORT\fR
instruction can be used to explicitly abort a transaction and the
\fBXEND\fR
instruction to check if a set of instructions are being run in a transaction\&.
.sp
A lock on a transaction is inflated when another thread tries to access the same transaction, thereby blocking the thread that did not originally request access to the transaction\&. RTM requires that a fallback set of operations be specified in case a transaction aborts or fails\&. An RTM lock is a lock that has been delegated to the TSX\*(Aqs system\&.
.sp
RTM improves performance for highly contended locks with low conflict in a critical region (which is code that must not be accessed by more than one thread concurrently)\&. RTM also improves the performance of coarse\-grain locking, which typically does not perform well in multithreaded applications\&. (Coarse\-grain locking is the strategy of holding locks for long periods to minimize the overhead of taking and releasing locks, while fine\-grained locking is the strategy of trying to achieve maximum parallelism by locking only when necessary and unlocking as soon as possible\&.) Also, for lightly contended locks that are used by different threads, RTM can reduce false cache line sharing, also known as cache line ping\-pong\&. This occurs when multiple threads from different processors are accessing different resources, but the resources share the same cache line\&. As a result, the processors repeatedly invalidate the cache lines of other processors, which forces them to read from main memory instead of their cache\&.
.RE
.PP
\-XX:+UseSHA
.RS 4
Enables hardware\-based intrinsics for SHA crypto hash functions for SPARC hardware\&.
\fBUseSHA\fR
is used in conjunction with the
\fBUseSHA1Intrinsics\fR,
\fBUseSHA256Intrinsics\fR, and
\fBUseSHA512Intrinsics\fR
options\&.
.sp
The
\fBUseSHA\fR
and
\fBUseSHA*Intrinsics\fR
flags are enabled by default, and are supported only for Java HotSpot Server VM 64\-bit on SPARC T4 and newer\&.
.sp
This feature is only applicable when using the
\fBsun\&.security\&.provider\&.Sun\fR
provider for SHA operations\&.
.sp
To disable all hardware\-based SHA intrinsics, specify
\fB\-XX:\-UseSHA\fR\&. To disable only a particular SHA intrinsic, use the appropriate corresponding option\&. For example:
\fB\-XX:\-UseSHA256Intrinsics\fR\&.
.RE
.PP
\-XX:+UseSHA1Intrinsics
.RS 4
Enables intrinsics for SHA\-1 crypto hash function\&.
.RE
.PP
\-XX:+UseSHA256Intrinsics
.RS 4
Enables intrinsics for SHA\-224 and SHA\-256 crypto hash functions\&.
.RE
.PP
\-XX:+UseSHA512Intrinsics
.RS 4
Enables intrinsics for SHA\-384 and SHA\-512 crypto hash functions\&.
.RE
.PP
\-XX:+UseSuperWord
.RS 4
Enables the transformation of scalar operations into superword operations\&. This option is enabled by default\&. To disable the transformation of scalar operations into superword operations, specify
\fB\-XX:\-UseSuperWord\fR\&. Only the Java HotSpot Server VM supports this option\&.
.RE
.SS "Advanced Serviceability Options"
.PP
These options provide the ability to gather system information and perform extensive debugging\&.
.PP
\-XX:+ExtendedDTraceProbes
.RS 4
Enables additional
\fBdtrace\fR
tool probes that impact the performance\&. By default, this option is disabled and
\fBdtrace\fR
performs only standard probes\&.
.RE
.PP
\-XX:+HeapDumpOnOutOfMemory
.RS 4
Enables the dumping of the Java heap to a file in the current directory by using the heap profiler (HPROF) when a
\fBjava\&.lang\&.OutOfMemoryError\fR
exception is thrown\&. You can explicitly set the heap dump file path and name using the
\fB\-XX:HeapDumpPath\fR
option\&. By default, this option is disabled and the heap is not dumped when an
\fBOutOfMemoryError\fR
exception is thrown\&.
.RE
.PP
\-XX:HeapDumpPath=\fIpath\fR
.RS 4
Sets the path and file name for writing the heap dump provided by the heap profiler (HPROF) when the
\fB\-XX:+HeapDumpOnOutOfMemoryError\fR
option is set\&. By default, the file is created in the current working directory, and it is named
\fBjava_pid\fR\fIpid\fR\fB\&.hprof\fR
where
\fIpid\fR
is the identifier of the process that caused the error\&. The following example shows how to set the default file explicitly (\fB%p\fR
represents the current process identificator):
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:HeapDumpPath=\&./java_pid%p\&.hprof\fR
 
.fi
.if n \{\
.RE
.\}
The following example shows how to set the heap dump file to
\fB/var/log/java/java_heapdump\&.hprof\fR:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:HeapDumpPath=/var/log/java/java_heapdump\&.hprof\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:LogFile=\fIpath\fR
.RS 4
Sets the path and file name where log data is written\&. By default, the file is created in the current working directory, and it is named
\fBhotspot\&.log\fR\&.
.sp
The following example shows how to set the log file to
\fB/var/log/java/hotspot\&.log\fR:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:LogFile=/var/log/java/hotspot\&.log\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+PrintClassHistogram
.RS 4
Enables printing of a class instance histogram after a
\fBControl+C\fR
event (\fBSIGTERM\fR)\&. By default, this option is disabled\&.
.sp
Setting this option is equivalent to running the
\fBjmap \-histo\fR
command, or the
\fBjcmd \fR\fIpid\fR\fB GC\&.class_histogram\fR
command, where
\fIpid\fR
is the current Java process identifier\&.
.RE
.PP
\-XX:+PrintConcurrentLocks
.RS 4
Enables printing of locks after a event\&. By default, this option is disabled\&.
.sp
Enables printing of
\fBjava\&.util\&.concurrent\fR
locks after a
\fBControl+C\fR
event (\fBSIGTERM\fR)\&. By default, this option is disabled\&.
.sp
Setting this option is equivalent to running the
\fBjstack \-l\fR
command or the
\fBjcmd \fR\fIpid\fR\fB Thread\&.print \-l\fR
command, where
\fIpid\fR
is the current Java process identifier\&.
.RE
.PP
\-XX:+UnlockDiagnosticVMOptions
.RS 4
Unlocks the options intended for diagnosing the JVM\&. By default, this option is disabled and diagnostic options are not available\&.
.RE
.SS "Advanced Garbage Collection Options"
.PP
These options control how garbage collection (GC) is performed by the Java HotSpot VM\&.
.PP
\-XX:+AggressiveHeap
.RS 4
Enables Java heap optimization\&. This sets various parameters to be optimal for long\-running jobs with intensive memory allocation, based on the configuration of the computer (RAM and CPU)\&. By default, the option is disabled and the heap is not optimized\&.
.RE
.PP
\-XX:+AlwaysPreTouch
.RS 4
Enables touching of every page on the Java heap during JVM initialization\&. This gets all pages into the memory before entering the
\fBmain()\fR
method\&. The option can be used in testing to simulate a long\-running system with all virtual memory mapped to physical memory\&. By default, this option is disabled and all pages are committed as JVM heap space fills\&.
.RE
.PP
\-XX:+CMSClassUnloadingEnabled
.RS 4
Enables class unloading when using the concurrent mark\-sweep (CMS) garbage collector\&. This option is enabled by default\&. To disable class unloading for the CMS garbage collector, specify
\fB\-XX:\-CMSClassUnloadingEnabled\fR\&.
.RE
.PP
\-XX:CMSExpAvgFactor=\fIpercent\fR
.RS 4
Sets the percentage of time (0 to 100) used to weight the current sample when computing exponential averages for the concurrent collection statistics\&. By default, the exponential averages factor is set to 25%\&. The following example shows how to set the factor to 15%:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CMSExpAvgFactor=15\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:CMSInitiatingOccupancyFraction=\fIpercent\fR
.RS 4
Sets the percentage of the old generation occupancy (0 to 100) at which to start a CMS collection cycle\&. The default value is set to \-1\&. Any negative value (including the default) implies that
\fB\-XX:CMSTriggerRatio\fR
is used to define the value of the initiating occupancy fraction\&.
.sp
The following example shows how to set the occupancy fraction to 20%:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CMSInitiatingOccupancyFraction=20\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+CMSScavengeBeforeRemark
.RS 4
Enables scavenging attempts before the CMS remark step\&. By default, this option is disabled\&.
.RE
.PP
\-XX:CMSTriggerRatio=\fIpercent\fR
.RS 4
Sets the percentage (0 to 100) of the value specified by
\fB\-XX:MinHeapFreeRatio\fR
that is allocated before a CMS collection cycle commences\&. The default value is set to 80%\&.
.sp
The following example shows how to set the occupancy fraction to 75%:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:CMSTriggerRatio=75\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:ConcGCThreads=\fIthreads\fR
.RS 4
Sets the number of threads used for concurrent GC\&. The default value depends on the number of CPUs available to the JVM\&.
.sp
For example, to set the number of threads for concurrent GC to 2, specify the following option:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:ConcGCThreads=2\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+DisableExplicitGC
.RS 4
Enables the option that disables processing of calls to
\fBSystem\&.gc()\fR\&. This option is disabled by default, meaning that calls to
\fBSystem\&.gc()\fR
are processed\&. If processing of calls to
\fBSystem\&.gc()\fR
is disabled, the JVM still performs GC when necessary\&.
.RE
.PP
\-XX:+ExplicitGCInvokesConcurrent
.RS 4
Enables invoking of concurrent GC by using the
\fBSystem\&.gc()\fR
request\&. This option is disabled by default and can be enabled only together with the
\fB\-XX:+UseConcMarkSweepGC\fR
option\&.
.RE
.PP
\-XX:+ExplicitGCInvokesConcurrentAndUnloadsClasses
.RS 4
Enables invoking of concurrent GC by using the
\fBSystem\&.gc()\fR
request and unloading of classes during the concurrent GC cycle\&. This option is disabled by default and can be enabled only together with the
\fB\-XX:+UseConcMarkSweepGC\fR
option\&.
.RE
.PP
\-XX:G1HeapRegionSize=\fIsize\fR
.RS 4
Sets the size of the regions into which the Java heap is subdivided when using the garbage\-first (G1) collector\&. The value can be between 1 MB and 32 MB\&. The default region size is determined ergonomically based on the heap size\&.
.sp
The following example shows how to set the size of the subdivisions to 16 MB:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:G1HeapRegionSize=16m\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+G1PrintHeapRegions
.RS 4
Enables the printing of information about which regions are allocated and which are reclaimed by the G1 collector\&. By default, this option is disabled\&.
.RE
.PP
\-XX:G1ReservePercent=\fIpercent\fR
.RS 4
Sets the percentage of the heap (0 to 50) that is reserved as a false ceiling to reduce the possibility of promotion failure for the G1 collector\&. By default, this option is set to 10%\&.
.sp
The following example shows how to set the reserved heap to 20%:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:G1ReservePercent=20\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:InitialHeapSize=\fIsize\fR
.RS 4
Sets the initial size (in bytes) of the memory allocation pool\&. This value must be either 0, or a multiple of 1024 and greater than 1 MB\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. The default value is chosen at runtime based on system configuration\&. See the section "Ergonomics" in
\fIJava SE HotSpot Virtual Machine Garbage Collection Tuning Guide\fR
at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/vm/gctuning/index\&.html\&.
.sp
The following examples show how to set the size of allocated memory to 6 MB using various units:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:InitialHeapSize=6291456\fR
\fB\-XX:InitialHeapSize=6144k\fR
\fB\-XX:InitialHeapSize=6m\fR
 
.fi
.if n \{\
.RE
.\}
If you set this option to 0, then the initial size will be set as the sum of the sizes allocated for the old generation and the young generation\&. The size of the heap for the young generation can be set using the
\fB\-XX:NewSize\fR
option\&.

Note that the \fB\-Xms\fR option sets both the minimum and the initial heap size of the heap. If \fB\-Xms\fR appears after \fB\-XX:InitialHeapSize\fR on the command line, then the initial heap size gets set to the value specified with \fB\-Xms\fR.
.RE
.PP
\-XX:InitialSurvivorRatio=\fIratio\fR
.RS 4
Sets the initial survivor space ratio used by the throughput garbage collector (which is enabled by the
\fB\-XX:+UseParallelGC\fR
and/or \-\fBXX:+UseParallelOldGC\fR
options)\&. Adaptive sizing is enabled by default with the throughput garbage collector by using the
\fB\-XX:+UseParallelGC\fR
and
\fB\-XX:+UseParallelOldGC\fR
options, and survivor space is resized according to the application behavior, starting with the initial value\&. If adaptive sizing is disabled (using the
\fB\-XX:\-UseAdaptiveSizePolicy\fR
option), then the
\fB\-XX:SurvivorRatio\fR
option should be used to set the size of the survivor space for the entire execution of the application\&.
.sp
The following formula can be used to calculate the initial size of survivor space (S) based on the size of the young generation (Y), and the initial survivor space ratio (R):
.sp
.if n \{\
.RS 4
.\}
.nf
\fBS=Y/(R+2)\fR
 
.fi
.if n \{\
.RE
.\}
The 2 in the equation denotes two survivor spaces\&. The larger the value specified as the initial survivor space ratio, the smaller the initial survivor space size\&.
.sp
By default, the initial survivor space ratio is set to 8\&. If the default value for the young generation space size is used (2 MB), the initial size of the survivor space will be 0\&.2 MB\&.
.sp
The following example shows how to set the initial survivor space ratio to 4:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:InitialSurvivorRatio=4\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:InitiatingHeapOccupancyPercent=\fIpercent\fR
.RS 4
Sets the percentage of the heap occupancy (0 to 100) at which to start a concurrent GC cycle\&. It is used by garbage collectors that trigger a concurrent GC cycle based on the occupancy of the entire heap, not just one of the generations (for example, the G1 garbage collector)\&.
.sp
By default, the initiating value is set to 45%\&. A value of 0 implies nonstop GC cycles\&. The following example shows how to set the initiating heap occupancy to 75%:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:InitiatingHeapOccupancyPercent=75\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MaxGCPauseMillis=\fItime\fR
.RS 4
Sets a target for the maximum GC pause time (in milliseconds)\&. This is a soft goal, and the JVM will make its best effort to achieve it\&. By default, there is no maximum pause time value\&.
.sp
The following example shows how to set the maximum target pause time to 500 ms:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxGCPauseMillis=500\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MaxHeapSize=\fIsize\fR
.RS 4
Sets the maximum size (in byes) of the memory allocation pool\&. This value must be a multiple of 1024 and greater than 2 MB\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. The default value is chosen at runtime based on system configuration\&. For server deployments,
\fB\-XX:InitialHeapSize\fR
and
\fB\-XX:MaxHeapSize\fR
are often set to the same value\&. See the section "Ergonomics" in
\fIJava SE HotSpot Virtual Machine Garbage Collection Tuning Guide\fR
at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/vm/gctuning/index\&.html\&.
.sp
The following examples show how to set the maximum allowed size of allocated memory to 80 MB using various units:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxHeapSize=83886080\fR
\fB\-XX:MaxHeapSize=81920k\fR
\fB\-XX:MaxHeapSize=80m\fR
 
.fi
.if n \{\
.RE
.\}
On Oracle Solaris 7 and Oracle Solaris 8 SPARC platforms, the upper limit for this value is approximately 4,000 MB minus overhead amounts\&. On Oracle Solaris 2\&.6 and x86 platforms, the upper limit is approximately 2,000 MB minus overhead amounts\&. On Linux platforms, the upper limit is approximately 2,000 MB minus overhead amounts\&.
.sp
The
\fB\-XX:MaxHeapSize\fR
option is equivalent to
\fB\-Xmx\fR\&.
.RE
.PP
\-XX:MaxHeapFreeRatio=\fIpercent\fR
.RS 4
Sets the maximum allowed percentage of free heap space (0 to 100) after a GC event\&. If free heap space expands above this value, then the heap will be shrunk\&. By default, this value is set to 70%\&.
.sp
The following example shows how to set the maximum free heap ratio to 75%:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxHeapFreeRatio=75\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MaxMetaspaceSize=\fIsize\fR
.RS 4
Sets the maximum amount of native memory that can be allocated for class metadata\&. By default, the size is not limited\&. The amount of metadata for an application depends on the application itself, other running applications, and the amount of memory available on the system\&.
.sp
The following example shows how to set the maximum class metadata size to 256 MB:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxMetaspaceSize=256m\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MaxNewSize=\fIsize\fR
.RS 4
Sets the maximum size (in bytes) of the heap for the young generation (nursery)\&. The default value is set ergonomically\&.
.RE
.PP
\-XX:MaxTenuringThreshold=\fIthreshold\fR
.RS 4
Sets the maximum tenuring threshold for use in adaptive GC sizing\&. The largest value is 15\&. The default value is 15 for the parallel (throughput) collector, and 6 for the CMS collector\&.
.sp
The following example shows how to set the maximum tenuring threshold to 10:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MaxTenuringThreshold=10\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:MetaspaceSize=\fIsize\fR
.RS 4
Sets the size of the allocated class metadata space that will trigger a garbage collection the first time it is exceeded\&. This threshold for a garbage collection is increased or decreased depending on the amount of metadata used\&. The default size depends on the platform\&.
.RE
.PP
\-XX:MinHeapFreeRatio=\fIpercent\fR
.RS 4
Sets the minimum allowed percentage of free heap space (0 to 100) after a GC event\&. If free heap space falls below this value, then the heap will be expanded\&. By default, this value is set to 40%\&.
.sp
The following example shows how to set the minimum free heap ratio to 25%:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:MinHeapFreeRatio=25\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:NewRatio=\fIratio\fR
.RS 4
Sets the ratio between young and old generation sizes\&. By default, this option is set to 2\&. The following example shows how to set the young/old ratio to 1:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:NewRatio=1\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:NewSize=\fIsize\fR
.RS 4
Sets the initial size (in bytes) of the heap for the young generation (nursery)\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&.
.sp
The young generation region of the heap is used for new objects\&. GC is performed in this region more often than in other regions\&. If the size for the young generation is too low, then a large number of minor GCs will be performed\&. If the size is too high, then only full GCs will be performed, which can take a long time to complete\&. Oracle recommends that you keep the size for the young generation between a half and a quarter of the overall heap size\&.
.sp
The following examples show how to set the initial size of young generation to 256 MB using various units:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:NewSize=256m\fR
\fB\-XX:NewSize=262144k\fR
\fB\-XX:NewSize=268435456\fR
 
.fi
.if n \{\
.RE
.\}
The
\fB\-XX:NewSize\fR
option is equivalent to
\fB\-Xmn\fR\&.
.RE
.PP
\-XX:ParallelGCThreads=\fIthreads\fR
.RS 4
Sets the number of threads used for parallel garbage collection in the young and old generations\&. The default value depends on the number of CPUs available to the JVM\&.
.sp
For example, to set the number of threads for parallel GC to 2, specify the following option:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:ParallelGCThreads=2\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+ParallelRefProcEnabled
.RS 4
Enables parallel reference processing\&. By default, this option is disabled\&.
.RE
.PP
\-XX:+PrintAdaptiveSizePolicy
.RS 4
Enables printing of information about adaptive generation sizing\&. By default, this option is disabled\&.
.RE
.PP
\-XX:+PrintGC
.RS 4
Enables printing of messages at every GC\&. By default, this option is disabled\&.
.RE
.PP
\-XX:+PrintGCApplicationConcurrentTime
.RS 4
Enables printing of how much time elapsed since the last pause (for example, a GC pause)\&. By default, this option is disabled\&.
.RE
.PP
\-XX:+PrintGCApplicationStoppedTime
.RS 4
Enables printing of how much time the pause (for example, a GC pause) lasted\&. By default, this option is disabled\&.
.RE
.PP
\-XX:+PrintGCDateStamps
.RS 4
Enables printing of a date stamp at every GC\&. By default, this option is disabled\&.
.RE
.PP
\-XX:+PrintGCDetails
.RS 4
Enables printing of detailed messages at every GC\&. By default, this option is disabled\&.
.RE
.PP
\-XX:+PrintGCTaskTimeStamps
.RS 4
Enables printing of time stamps for every individual GC worker thread task\&. By default, this option is disabled\&.
.RE
.PP
\-XX:+PrintGCTimeStamps
.RS 4
Enables printing of time stamps at every GC\&. By default, this option is disabled\&.
.RE
.PP
\-XX:+PrintStringDeduplicationStatistics
.RS 4
Prints detailed deduplication statistics\&. By default, this option is disabled\&. See the
\fB\-XX:+UseStringDeduplication\fR
option\&.
.RE
.PP
\-XX:+PrintTenuringDistribution
.RS 4
Enables printing of tenuring age information\&. The following is an example of the output:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBDesired survivor size 48286924 bytes, new threshold 10 (max 10)\fR
\fB\- age 1: 28992024 bytes, 28992024 total\fR
\fB\- age 2: 1366864 bytes, 30358888 total\fR
\fB\- age 3: 1425912 bytes, 31784800 total\fR
\fB\&.\&.\&.\fR
 
.fi
.if n \{\
.RE
.\}
Age 1 objects are the youngest survivors (they were created after the previous scavenge, survived the latest scavenge, and moved from eden to survivor space)\&. Age 2 objects have survived two scavenges (during the second scavenge they were copied from one survivor space to the next)\&. And so on\&.
.sp
In the preceding example, 28 992 024 bytes survived one scavenge and were copied from eden to survivor space, 1 366 864 bytes are occupied by age 2 objects, etc\&. The third value in each row is the cumulative size of objects of age n or less\&.
.sp
By default, this option is disabled\&.
.RE
.PP
\-XX:+ScavengeBeforeFullGC
.RS 4
Enables GC of the young generation before each full GC\&. This option is enabled by default\&. Oracle recommends that you
\fIdo not\fR
disable it, because scavenging the young generation before a full GC can reduce the number of objects reachable from the old generation space into the young generation space\&. To disable GC of the young generation before each full GC, specify
\fB\-XX:\-ScavengeBeforeFullGC\fR\&.
.RE
.PP
\-XX:SoftRefLRUPolicyMSPerMB=\fItime\fR
.RS 4
Sets the amount of time (in milliseconds) a softly reachable object is kept active on the heap after the last time it was referenced\&. The default value is one second of lifetime per free megabyte in the heap\&. The
\fB\-XX:SoftRefLRUPolicyMSPerMB\fR
option accepts integer values representing milliseconds per one megabyte of the current heap size (for Java HotSpot Client VM) or the maximum possible heap size (for Java HotSpot Server VM)\&. This difference means that the Client VM tends to flush soft references rather than grow the heap, whereas the Server VM tends to grow the heap rather than flush soft references\&. In the latter case, the value of the
\fB\-Xmx\fR
option has a significant effect on how quickly soft references are garbage collected\&.
.sp
The following example shows how to set the value to 2\&.5 seconds:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:SoftRefLRUPolicyMSPerMB=2500\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:StringDeduplicationAgeThreshold=\fIthreshold\fR
.RS 4
\fBString\fR
objects reaching the specified age are considered candidates for deduplication\&. An object\*(Aqs age is a measure of how many times it has survived garbage collection\&. This is sometimes referred to as tenuring; see the
\fB\-XX:+PrintTenuringDistribution\fR
option\&. Note that
\fBString\fR
objects that are promoted to an old heap region before this age has been reached are always considered candidates for deduplication\&. The default value for this option is
\fB3\fR\&. See the
\fB\-XX:+UseStringDeduplication\fR
option\&.
.RE
.PP
\-XX:SurvivorRatio=\fIratio\fR
.RS 4
Sets the ratio between eden space size and survivor space size\&. By default, this option is set to 8\&. The following example shows how to set the eden/survivor space ratio to 4:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:SurvivorRatio=4\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:TargetSurvivorRatio=\fIpercent\fR
.RS 4
Sets the desired percentage of survivor space (0 to 100) used after young garbage collection\&. By default, this option is set to 50%\&.
.sp
The following example shows how to set the target survivor space ratio to 30%:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:TargetSurvivorRatio=30\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:TLABSize=\fIsize\fR
.RS 4
Sets the initial size (in bytes) of a thread\-local allocation buffer (TLAB)\&. Append the letter
\fBk\fR
or
\fBK\fR
to indicate kilobytes,
\fBm\fR
or
\fBM\fR
to indicate megabytes,
\fBg\fR
or
\fBG\fR
to indicate gigabytes\&. If this option is set to 0, then the JVM chooses the initial size automatically\&.
.sp
The following example shows how to set the initial TLAB size to 512 KB:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-XX:TLABSize=512k\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-XX:+UseAdaptiveSizePolicy
.RS 4
Enables the use of adaptive generation sizing\&. This option is enabled by default\&. To disable adaptive generation sizing, specify
\fB\-XX:\-UseAdaptiveSizePolicy\fR
and set the size of the memory allocation pool explicitly (see the
\fB\-XX:SurvivorRatio\fR
option)\&.
.RE
.PP
\-XX:+UseCMSInitiatingOccupancyOnly
.RS 4
Enables the use of the occupancy value as the only criterion for initiating the CMS collector\&. By default, this option is disabled and other criteria may be used\&.
.RE
.PP
\-XX:+UseConcMarkSweepGC
.RS 4
Enables the use of the CMS garbage collector for the old generation\&. Oracle recommends that you use the CMS garbage collector when application latency requirements cannot be met by the throughput (\fB\-XX:+UseParallelGC\fR) garbage collector\&. The G1 garbage collector (\fB\-XX:+UseG1GC\fR) is another alternative\&.
.sp
By default, this option is disabled and the collector is chosen automatically based on the configuration of the machine and type of the JVM\&. When this option is enabled, the
\fB\-XX:+UseParNewGC\fR
option is automatically set and you should not disable it, because the following combination of options has been deprecated in JDK 8:
\fB\-XX:+UseConcMarkSweepGC \-XX:\-UseParNewGC\fR\&.
.RE
.PP
\-XX:+UseG1GC
.RS 4
Enables the use of the garbage\-first (G1) garbage collector\&. It is a server\-style garbage collector, targeted for multiprocessor machines with a large amount of RAM\&. It meets GC pause time goals with high probability, while maintaining good throughput\&. The G1 collector is recommended for applications requiring large heaps (sizes of around 6 GB or larger) with limited GC latency requirements (stable and predictable pause time below 0\&.5 seconds)\&.
.sp
By default, this option is disabled and the collector is chosen automatically based on the configuration of the machine and type of the JVM\&.
.RE
.PP
\-XX:+UseGCOverheadLimit
.RS 4
Enables the use of a policy that limits the proportion of time spent by the JVM on GC before an
\fBOutOfMemoryError\fR
exception is thrown\&. This option is enabled, by default and the parallel GC will throw an
\fBOutOfMemoryError\fR
if more than 98% of the total time is spent on garbage collection and less than 2% of the heap is recovered\&. When the heap is small, this feature can be used to prevent applications from running for long periods of time with little or no progress\&. To disable this option, specify
\fB\-XX:\-UseGCOverheadLimit\fR\&.
.RE
.PP
\-XX:+UseNUMA
.RS 4
Enables performance optimization of an application on a machine with nonuniform memory architecture (NUMA) by increasing the application\*(Aqs use of lower latency memory\&. By default, this option is disabled and no optimization for NUMA is made\&. The option is only available when the parallel garbage collector is used (\fB\-XX:+UseParallelGC\fR)\&.
.RE
.PP
\-XX:+UseParallelGC
.RS 4
Enables the use of the parallel scavenge garbage collector (also known as the throughput collector) to improve the performance of your application by leveraging multiple processors\&.
.sp
By default, this option is disabled and the collector is chosen automatically based on the configuration of the machine and type of the JVM\&. If it is enabled, then the
\fB\-XX:+UseParallelOldGC\fR
option is automatically enabled, unless you explicitly disable it\&.
.RE
.PP
\-XX:+UseParallelOldGC
.RS 4
Enables the use of the parallel garbage collector for full GCs\&. By default, this option is disabled\&. Enabling it automatically enables the
\fB\-XX:+UseParallelGC\fR
option\&.
.RE
.PP
\-XX:+UseParNewGC
.RS 4
Enables the use of parallel threads for collection in the young generation\&. By default, this option is disabled\&. It is automatically enabled when you set the
\fB\-XX:+UseConcMarkSweepGC\fR
option\&. Using the
\fB\-XX:+UseParNewGC\fR
option without the
\fB\-XX:+UseConcMarkSweepGC\fR
option was deprecated in JDK 8\&.
.RE
.PP
\-XX:+UseSerialGC
.RS 4
Enables the use of the serial garbage collector\&. This is generally the best choice for small and simple applications that do not require any special functionality from garbage collection\&. By default, this option is disabled and the collector is chosen automatically based on the configuration of the machine and type of the JVM\&.
.RE
.PP
\-XX:+UseSHM
.RS 4
On Linux, enables the JVM to use shared memory to setup large pages\&.
.sp
For more information, see "Large Pages"\&.
.RE
.PP
\-XX:+UseStringDeduplication
.RS 4
Enables string deduplication\&. By default, this option is disabled\&. To use this option, you must enable the garbage\-first (G1) garbage collector\&. See the
\fB\-XX:+UseG1GC\fR
option\&.
.sp
\fIString deduplication\fR
reduces the memory footprint of
\fBString\fR
objects on the Java heap by taking advantage of the fact that many
\fBString\fR
objects are identical\&. Instead of each
\fBString\fR
object pointing to its own character array, identical
\fBString\fR
objects can point to and share the same character array\&.
.RE
.PP
\-XX:+UseTLAB
.RS 4
Enables the use of thread\-local allocation blocks (TLABs) in the young generation space\&. This option is enabled by default\&. To disable the use of TLABs, specify
\fB\-XX:\-UseTLAB\fR\&.
.RE
.SS "Deprecated and Removed Options"
.PP
These options were included in the previous release, but have since been considered unnecessary\&.
.PP
\-Xincgc
.RS 4
Enables incremental garbage collection\&. This option was deprecated in JDK 8 with no replacement\&.
.RE
.PP
\-Xrun\fIlibname\fR
.RS 4
Loads the specified debugging/profiling library\&. This option was superseded by the
\fB\-agentlib\fR
option\&.
.RE
.PP
\-XX:CMSIncrementalDutyCycle=\fIpercent\fR
.RS 4
Sets the percentage of time (0 to 100) between minor collections that the concurrent collector is allowed to run\&. This option was deprecated in JDK 8 with no replacement, following the deprecation of the
\fB\-XX:+CMSIncrementalMode\fR
option\&.
.RE
.PP
\-XX:CMSIncrementalDutyCycleMin=\fIpercent\fR
.RS 4
Sets the percentage of time (0 to 100) between minor collections that is the lower bound for the duty cycle when
\fB\-XX:+CMSIncrementalPacing\fR
is enabled\&. This option was deprecated in JDK 8 with no replacement, following the deprecation of the
\fB\-XX:+CMSIncrementalMode\fR
option\&.
.RE
.PP
\-XX:+CMSIncrementalMode
.RS 4
Enables the incremental mode for the CMS collector\&. This option was deprecated in JDK 8 with no replacement, along with other options that start with
\fBCMSIncremental\fR\&.
.RE
.PP
\-XX:CMSIncrementalOffset=\fIpercent\fR
.RS 4
Sets the percentage of time (0 to 100) by which the incremental mode duty cycle is shifted to the right within the period between minor collections\&. This option was deprecated in JDK 8 with no replacement, following the deprecation of the
\fB\-XX:+CMSIncrementalMode\fR
option\&.
.RE
.PP
\-XX:+CMSIncrementalPacing
.RS 4
Enables automatic adjustment of the incremental mode duty cycle based on statistics collected while the JVM is running\&. This option was deprecated in JDK 8 with no replacement, following the deprecation of the
\fB\-XX:+CMSIncrementalMode\fR
option\&.
.RE
.PP
\-XX:CMSIncrementalSafetyFactor=\fIpercent\fR
.RS 4
Sets the percentage of time (0 to 100) used to add conservatism when computing the duty cycle\&. This option was deprecated in JDK 8 with no replacement, following the deprecation of the
\fB\-XX:+CMSIncrementalMode\fR
option\&.
.RE
.PP
\-XX:CMSInitiatingPermOccupancyFraction=\fIpercent\fR
.RS 4
Sets the percentage of the permanent generation occupancy (0 to 100) at which to start a GC\&. This option was deprecated in JDK 8 with no replacement\&.
.RE
.PP
\-XX:MaxPermSize=\fIsize\fR
.RS 4
Sets the maximum permanent generation space size (in bytes)\&. This option was deprecated in JDK 8, and superseded by the
\fB\-XX:MaxMetaspaceSize\fR
option\&.
.RE
.PP
\-XX:PermSize=\fIsize\fR
.RS 4
Sets the space (in bytes) allocated to the permanent generation that triggers a garbage collection if it is exceeded\&. This option was deprecated un JDK 8, and superseded by the
\fB\-XX:MetaspaceSize\fR
option\&.
.RE
.PP
\-XX:+UseSplitVerifier
.RS 4
Enables splitting of the verification process\&. By default, this option was enabled in the previous releases, and verification was split into two phases: type referencing (performed by the compiler) and type checking (performed by the JVM runtime)\&. This option was deprecated in JDK 8, and verification is now split by default without a way to disable it\&.
.RE
.PP
\-XX:+UseStringCache
.RS 4
Enables caching of commonly allocated strings\&. This option was removed from JDK 8 with no replacement\&.
.RE
.SH "PERFORMANCE TUNING EXAMPLES"
.PP
The following examples show how to use experimental tuning flags to either optimize throughput or to provide lower response time\&.
.PP
\fBExample 1 \fRTuning for Higher Throughput
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava \-d64 \-server \-XX:+AggressiveOpts \-XX:+UseLargePages \-Xmn10g  \-Xms26g \-Xmx26g\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fBExample 2 \fRTuning for Lower Response Time
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava \-d64 \-XX:+UseG1GC \-Xms26g Xmx26g \-XX:MaxGCPauseMillis=500 \-XX:+PrintGCTimeStamp\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.SH "LARGE PAGES"
.PP
Also known as huge pages, large pages are memory pages that are significantly larger than the standard memory page size (which varies depending on the processor and operating system)\&. Large pages optimize processor Translation\-Lookaside Buffers\&.
.PP
A Translation\-Lookaside Buffer (TLB) is a page translation cache that holds the most\-recently used virtual\-to\-physical address translations\&. TLB is a scarce system resource\&. A TLB miss can be costly as the processor must then read from the hierarchical page table, which may require multiple memory accesses\&. By using a larger memory page size, a single TLB entry can represent a larger memory range\&. There will be less pressure on TLB, and memory\-intensive applications may have better performance\&.
.PP
However, large pages page memory can negatively affect system performance\&. For example, when a large mount of memory is pinned by an application, it may create a shortage of regular memory and cause excessive paging in other applications and slow down the entire system\&. Also, a system that has been up for a long time could produce excessive fragmentation, which could make it impossible to reserve enough large page memory\&. When this happens, either the OS or JVM reverts to using regular pages\&.
.SS "Large Pages Support"
.PP
Solaris and Linux support large pages\&.
.sp
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
.br
.ps +1
\fBSolaris\fR
.RS 4
.PP
Solaris 9 and later include Multiple Page Size Support (MPSS); no additional configuration is necessary\&. See http://www\&.oracle\&.com/technetwork/server\-storage/solaris10/overview/solaris9\-features\-scalability\-135663\&.html\&.
.RE
.sp
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
.br
.ps +1
\fBLinux\fR
.RS 4
.PP
The 2\&.6 kernel supports large pages\&. Some vendors have backported the code to their 2\&.4\-based releases\&. To check if your system can support large page memory, try the following:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB# cat /proc/meminfo | grep Huge\fR
\fBHugePages_Total: 0\fR
\fBHugePages_Free: 0\fR
\fBHugepagesize: 2048 kB\fR
 
.fi
.if n \{\
.RE
.\}
.PP
If the output shows the three "Huge" variables, then your system can support large page memory but it needs to be configured\&. If the command prints nothing, then your system does not support large pages\&. To configure the system to use large page memory, login as
\fBroot\fR, and then follow these steps:
.sp
.RS 4
.ie n \{\
\h'-04' 1.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  1." 4.2
.\}
If you are using the option
\fB\-XX:+UseSHM\fR
(instead of
\fB\-XX:+UseHugeTLBFS\fR), then increase the
\fBSHMMAX\fR
value\&. It must be larger than the Java heap size\&. On a system with 4 GB of physical RAM (or less), the following will make all the memory sharable:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB# echo 4294967295 > /proc/sys/kernel/shmmax\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 2.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  2." 4.2
.\}
If you are using the option
\fB\-XX:+UseSHM\fR
or
\fB\-XX:+UseHugeTLBFS\fR, then specify the number of large pages\&. In the following example, 3 GB of a 4 GB system are reserved for large pages (assuming a large page size of 2048kB, then 3 GB = 3 * 1024 MB = 3072 MB = 3072 * 1024 kB = 3145728 kB and 3145728 kB / 2048 kB = 1536):
.sp
.if n \{\
.RS 4
.\}
.nf
\fB# echo 1536 > /proc/sys/vm/nr_hugepages\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.if n \{\
.sp
.\}
.RS 4
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
.br
.ps +1
\fBNote\fR
.ps -1
.br
.TS
allbox tab(:);
l.
T{
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Note that the values contained in
\fB/proc\fR
will reset after you reboot your system, so may want to set them in an initialization script (for example,
\fBrc\&.local\fR
or
\fBsysctl\&.conf\fR)\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
If you configure (or resize) the OS kernel parameters
\fB/proc/sys/kernel/shmmax\fR
or
\fB/proc/sys/vm/nr_hugepages\fR, Java processes may allocate large pages for areas in addition to the Java heap\&. These steps can allocate large pages for the following areas:
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Java heap
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Code cache
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
The marking bitmap data structure for the parallel GC
.RE
.sp
Consequently, if you configure the
\fBnr_hugepages\fR
parameter to the size of the Java heap, then the JVM can fail in allocating the code cache areas on large pages because these areas are quite large in size\&.
.RE
T}
.TE
.sp 1
.sp .5v
.RE
.RE
.SH "APPLICATION CLASS DATA SHARING"
.PP
Application Class Data Sharing (AppCDS) extends CDS (see https://docs\&.oracle\&.com/javase/8/docs/technotes/guides/vm/class\-data\-sharing\&.html) to enable classes from the standard extensions directories (specified by the system property
\fBjava\&.ext\&.dirs\fR; see https://docs\&.oracle\&.com/javase/8/docs/technotes/guides/extensions/spec\&.html) and the application class path (see "Setting the Class Path ") to be placed in the shared archive\&. AppCDS reduces the footprint and decreases start\-up time of your applications provided that a substantial number of classes are loaded from the application class path\&.
.PP
This is a commercial feature that requires you to also specify the
\fB\-XX:+UnlockCommercialFeatures\fR
option\&. This is also an experimental feature; it may change in future releases\&.
.SS "Creating a Shared Archive File, and Running an Application with It"
.PP
The following steps create a shared archive file that contains all the classes used by the
\fBtest\&.Hello\fR
application\&. The last step runs the application with the shared archive file\&.
.sp
.RS 4
.ie n \{\
\h'-04' 1.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  1." 4.2
.\}
Create a list of all classes used by the
\fBtest\&.Hello\fR
application\&. The following command creates a file named
\fBhello\&.classlist\fR
that contains a list of all classes used by this application:
.sp
\fBjava \-Xshare:off \-XX:+UnlockCommercialFeatures \-XX:DumpLoadedClassList=hello\&.classlist \-XX:+UseAppCDS \-cp hello\&.jar test\&.Hello\fR
.sp
Note that the
\fB\-cp\fR
parameter must contain only JAR files; the
\fB\-XX:+UseAppCDS\fR
option does not support class paths that contain directory names\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 2.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  2." 4.2
.\}
Create a shared archive, named
\fBhello\&.jsa\fR, that contains all the classes in
\fBhello\&.classlist\fR:
.sp
\fBjava \-XX:+UnlockCommercialFeatures \-Xshare:dump \-XX:+UseAppCDS \-XX:SharedArchiveFile=hello\&.jsa \-XX:SharedClassListFile=hello\&.classlist \-cp hello\&.jar\fR
.sp
Note that the
\fB\-cp\fR
parameter used at archive creation time must be the same as (or a prefix of) the
\fB\-cp\fR
used at run time\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 3.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  3." 4.2
.\}
Run the application
\fBtest\&.Hello\fR
with the shared archive
\fBhello\&.jsa\fR:
.sp
\fBjava \-XX:+UnlockCommercialFeatures \-Xshare:on \-XX:+UseAppCDS \-XX:SharedArchiveFile=hello\&.jsa \-cp hello\&.jar test\&.Hello\fR
.sp
Ensure that you have specified the option
\fB\-Xshare:on\fR
or \-\fBXshare:auto\fR\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 4.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  4." 4.2
.\}
Verify that the
\fBtest\&.Hello\fR
application is using the class contained in the
\fBhello\&.jsa\fR
shared archive:
.sp
\fBjava \-XX:+UnlockCommercialFeatures \-Xshare:on \-XX:+UseAppCDS \-XX:SharedArchiveFile=hello\&.jsa \-cp hello\&.jar \-verbose:class test\&.Hello\fR
.sp
The output of this command should contain the following text:
.sp
\fBLoaded test\&.Hello from shared objects file by sun/misc/Launcher$AppClassLoader\fR
.RE
.SS "Sharing a Shared Archive across Multiple Application Processes"
.PP
You can share the same archive file across multiple applications processes that have the exact same class path or share a common class path prefix\&. This reduces memory usage as the archive is memory\-mapped into the address space of the processes\&. The operating system automatically shares the read\-only pages across these processes\&.
.PP
The following steps create a shared archive that both applications
\fBHello\fR
and
\fBHi\fR
can use\&.
.sp
.RS 4
.ie n \{\
\h'-04' 1.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  1." 4.2
.\}
Create a list of all classes used by the
\fBHello\fR
application and another list for the
\fBHi\fR
application:
.sp
\fBjava \-XX:+UnlockCommercialFeatures \-XX:DumpLoadedClassList=hello\&.classlist \-XX:+UseAppCDS \-cp common\&.jar:hello\&.jar Hello\fR
.sp
\fBjava \-XX:+UnlockCommercialFeatures \-XX:DumpLoadedClassList=hi\&.classlist \-XX:+UseAppCDS \-cp common\&.jar:hi\&.jar Hi\fR
.sp
Note that because the
\fBHello\fR
and
\fBHi\fR
applications share a common class path prefix (both of their class paths start with
\fBcommon\&.jar\fR), these two applications can share a shared archive file\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 2.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  2." 4.2
.\}
Create a single list of classes used by all the applications that will share the shared archive file\&.
.sp
The following commands combine the files
\fBhello\&.classlist\fR
and
\fBhi\&.classlist\fR
to one file,
\fBcommon\&.classlist\fR:
.sp
\fBcat hello\&.classlist hi\&.classlist > common\&.classlist\fR
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 3.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  3." 4.2
.\}
Create a shared archive, named
\fBcommon\&.jsa\fR, that contains all the classes in
\fBcommon\&.classlist\fR:
.sp
\fBjava \-XX:+UnlockCommercialFeatures \-Xshare:dump \-XX:SharedArchiveFile=common\&.jsa \-XX:+UseAppCDS \-XX:SharedClassListFile=common\&.classlist \-cp common\&.jar\fR
.sp
The value of the
\fB\-cp\fR
parameter is the common class path prefix shared by the
\fBHello\fR
and
\fBHi\fR
applications\&.
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 4.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  4." 4.2
.\}
Run the
\fBHello\fR
and
\fBHi\fR
applications with the same shared archive:
.sp
\fBjava \-XX:+UnlockCommercialFeatures \-Xshare:on \-XX:SharedArchiveFile=common\&.jsa \-XX:+UseAppCDS \-cp common\&.jar:hello\&.jar Hello\fR
.sp
\fBjava \-XX:+UnlockCommercialFeatures \-Xshare:on \-XX:SharedArchiveFile=common\&.jsa \-XX:+UseAppCDS \-cp common\&.jar:hi\&.jar Hi\fR
.RE
.SH "EXIT STATUS"
.PP
The following exit values are typically returned by the launcher when the launcher is called with the wrong arguments, serious errors, or exceptions thrown by the JVM\&. However, a Java application may choose to return any value by using the API call
\fBSystem\&.exit(exitValue)\fR\&. The values are:
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB0\fR: Successful completion
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB>0\fR: An error occurred
.RE
.SH "SEE ALSO"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javac(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jdb(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javah(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jar(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jstat(1)
.RE
.br
'pl 8.5i
'bp
