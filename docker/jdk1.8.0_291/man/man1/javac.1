'\" t
.\"  Copyright (c) 1994, 2015, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 03 March 2015
.\"     SectDesc: Basic Tools
.\"     Title: javac.1
.\"
.if n .pl 99999
.TH javac 1 "03 March 2015" "JDK 8" "Basic Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
javac \- Reads Java class and interface definitions and compiles them into bytecode and class files\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjavac\fR [ \fIoptions\fR ] [ \fIsourcefiles\fR ] [ \fIclasses\fR] [ \fI@argfiles\fR ]
.fi     
.sp     
Arguments can be in any order:
.TP     
\fIoptions\fR
Command-line options\&. See Options\&.
.TP     
\fIsourcefiles\fR
One or more source files to be compiled (such as \f3MyClass\&.java\fR)\&.
.TP     
\fIclasses\fR
One or more classes to be processed for annotations (such as \f3MyPackage\&.MyClass\fR)\&.
.TP     
\fI@argfiles\fR
One or more files that list options and source files\&. The \f3-J\fR options are not allowed in these files\&. See Command-Line Argument Files\&.
.SH DESCRIPTION    
The \f3javac\fR command reads class and interface definitions, written in the Java programming language, and compiles them into bytecode class files\&. The \f3javac\fR command can also process annotations in Java source files and classes\&.
.PP
There are two ways to pass source code file names to \f3javac\fR\&.
.TP 0.2i    
\(bu
For a small number of source files, list the file names on the command line\&.
.TP 0.2i    
\(bu
For a large number of source files, list the file names in a file that is separated by blanks or line breaks\&. Use the list file name preceded by an at sign (@) with the \f3javac\fR command\&.
.PP
Source code file names must have \&.java suffixes, class file names must have \&.class suffixes, and both source and class files must have root names that identify the class\&. For example, a class called \f3MyClass\fR would be written in a source file called \f3MyClass\&.java\fR and compiled into a bytecode class file called \f3MyClass\&.class\fR\&.
.PP
Inner class definitions produce additional class files\&. These class files have names that combine the inner and outer class names, such as \f3MyClass$MyInnerClass\&.class\fR\&.
.PP
Arrange source files in a directory tree that reflects their package tree\&. For example, if all of your source files are in \f3/workspace\fR, then put the source code for \f3com\&.mysoft\&.mypack\&.MyClass\fR in \f3/workspace/com/mysoft/mypack/MyClass\&.java\fR\&.
.PP
By default, the compiler puts each class file in the same directory as its source file\&. You can specify a separate destination directory with the \f3-d\fR option\&.
.SH OPTIONS    
The compiler has a set of standard options that are supported on the current development environment\&. An additional set of nonstandard options are specific to the current virtual machine and compiler implementations and are subject to change in the future\&. Nonstandard options begin with the \f3-X\fR option\&.
.TP 0.2i    
\(bu
See also Cross-Compilation Options
.TP 0.2i    
\(bu
See also Nonstandard Options
.SS STANDARD\ OPTIONS    
.TP
-A\fIkey\fR[\fI=value\fR]
.br
Specifies options to pass to annotation processors\&. These options are not interpreted by \f3javac\fR directly, but are made available for use by individual processors\&. The \f3key\fR value should be one or more identifiers separated by a dot (\&.)\&.
.TP
-cp \fIpath\fR or -classpath \fIpath\fR
.br
Specifies where to find user class files, and (optionally) annotation processors and source files\&. This class path overrides the user class path in the \f3CLASSPATH\fR environment variable\&. If neither \f3CLASSPATH\fR, \f3-cp\fR nor \f3-classpath\fR is specified, then the user \fIclass path\fR is the current directory\&. See Setting the Class Path\&.

If the \f3-sourcepath\fR option is not specified, then the user class path is also searched for source files\&.

If the \f3-processorpath\fR option is not specified, then the class path is also searched for annotation processors\&.
.TP
-Djava\&.ext\&.dirs=\fIdirectories\fR
.br
Overrides the location of installed extensions\&.
.TP
-Djava\&.endorsed\&.dirs=\fIdirectories\fR
.br
Overrides the location of the endorsed standards path\&.
.TP
-d \fIdirectory\fR
.br
Sets the destination directory for class files\&. The directory must already exist because \f3javac\fR does not create it\&. If a class is part of a package, then \f3javac\fR puts the class file in a subdirectory that reflects the package name and creates directories as needed\&.

If you specify \f3-d\fR\f3/home/<USER>/home/<USER>/com/mypackage/MyClass\&.class\fR\&.

If the \fI-d\fR option is not specified, then \f3javac\fR puts each class file in the same directory as the source file from which it was generated\&.

\fINote:\fR The directory specified by the \fI-d\fR option is not automatically added to your user class path\&.
.TP
-deprecation
.br
Shows a description of each use or override of a deprecated member or class\&. Without the \f3-deprecation\fR option, \f3javac\fR shows a summary of the source files that use or override deprecated members or classes\&. The \f3-deprecation\fR option is shorthand for \f3-Xlint:deprecation\fR\&.
.TP
-encoding \fIencoding\fR
.br
Sets the source file encoding name, such as EUC-JP and UTF-8\&. If the \f3-encoding\fR option is not specified, then the platform default converter is used\&.
.TP
-endorseddirs \fIdirectories\fR
.br
Overrides the location of the endorsed standards path\&.
.TP
-extdirs \fIdirectories\fR
.br
Overrides the location of the \f3ext\fR directory\&. The directories variable is a colon-separated list of directories\&. Each JAR file in the specified directories is searched for class files\&. All JAR files found become part of the class path\&.

If you are cross-compiling (compiling classes against bootstrap and extension classes of a different Java platform implementation), then this option specifies the directories that contain the extension classes\&. See Cross-Compilation Options for more information\&.
.TP
-g
.br
Generates all debugging information, including local variables\&. By default, only line number and source file information is generated\&.
.TP
-g:none
.br
Does not generate any debugging information\&.
.TP
-g:[\fIkeyword list\fR]
.br
Generates only some kinds of debugging information, specified by a comma separated list of keywords\&. Valid keywords are:
.RS     
.TP     
source
Source file debugging information\&.
.TP     
lines
Line number debugging information\&.
.TP     
vars
Local variable debugging information\&.
.RE     

.TP
-help
.br
Prints a synopsis of standard options\&.
.TP
-implicit:[\fIclass, none\fR]
.br
Controls the generation of class files for implicitly loaded source files\&. To automatically generate class files, use \f3-implicit:class\fR\&. To suppress class file generation, use \f3-implicit:none\fR\&. If this option is not specified, then the default is to automatically generate class files\&. In this case, the compiler issues a warning if any such class files are generated when also doing annotation processing\&. The warning is not issued when the \f3-implicit\fR option is set explicitly\&. See Searching for Types\&.
.TP
-J\fIoption\fR
.br
Passes \f3option\fR to the Java Virtual Machine (JVM), where option is one of the options described on the reference page for the Java launcher\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&. See java(1)\&.

\fINote:\fR The \fICLASSPATH\fR, \f3-classpath\fR, \f3-bootclasspath\fR, and \f3-extdirs\fR options do not specify the classes used to run \f3javac\fR\&. Trying to customize the compiler implementation with these options and variables is risky and often does not accomplish what you want\&. If you must customize the complier implementation, then use the \f3-J\fR option to pass options through to the underlying \f3\fRJava launcher\&.
.TP
-nowarn
.br
Disables warning messages\&. This option operates the same as the \f3-Xlint:none\fR option\&.
.TP
-parameters
.br
Stores formal parameter names of constructors and methods in the generated class file so that the method \f3java\&.lang\&.reflect\&.Executable\&.getParameters\fR from the Reflection API can retrieve them\&.
.TP
-proc: [\fInone\fR, \fIonly\fR]
.br
Controls whether annotation processing and compilation are done\&. \f3-proc:none\fR means that compilation takes place without annotation processing\&. \f3-proc:only\fR means that only annotation processing is done, without any subsequent compilation\&.
.TP
-processor \fIclass1\fR [,\fIclass2\fR,\fIclass3\fR\&.\&.\&.]
.br
Names of the annotation processors to run\&. This bypasses the default discovery process\&.
.TP
-processorpath \fIpath\fR
.br
Specifies where to find annotation processors\&. If this option is not used, then the class path is searched for processors\&.
.TP
-s \fIdir\fR
.br
Specifies the directory where to place the generated source files\&. The directory must already exist because \f3javac\fR does not create it\&. If a class is part of a package, then the compiler puts the source file in a subdirectory that reflects the package name and creates directories as needed\&.

If you specify \f3-s /home/<USER>/home/<USER>/com/mypackage/MyClass\&.java\fR\&.
.TP
-source \fIrelease\fR
.br
Specifies the version of source code accepted\&. The following values for \f3release\fR are allowed:
.RS     
.TP     
1\&.3
The compiler does not support assertions, generics, or other language features introduced after Java SE 1\&.3\&.
.TP     
1\&.4
The compiler accepts code containing assertions, which were introduced in Java SE 1\&.4\&.
.TP     
1\&.5
The compiler accepts code containing generics and other language features introduced in Java SE 5\&.
.TP     
5
Synonym for 1\&.5\&.
.TP     
1\&.6
No language changes were introduced in Java SE 6\&. However, encoding errors in source files are now reported as errors instead of warnings as in earlier releases of Java Platform, Standard Edition\&.
.TP     
6
Synonym for 1\&.6\&.
.TP     
1\&.7
The compiler accepts code with features introduced in Java SE 7\&.
.TP     
7
Synonym for 1\&.7\&.
.TP     
1\&.8
This is the default value\&. The compiler accepts code with features introduced in Java SE 8\&.
.TP     
8
Synonym for 1\&.8\&.
.RE     

.TP
-sourcepath \fIsourcepath\fR
.br
Specifies the source code path to search for class or interface definitions\&. As with the user class path, source path entries are separated by colons (:) on Oracle Solaris and semicolons on Windows and can be directories, JAR archives, or ZIP archives\&. If packages are used, then the local path name within the directory or archive must reflect the package name\&.

\fINote:\fR Classes found through the class path might be recompiled when their source files are also found\&. See Searching for Types\&.
.TP
-verbose
.br
Uses verbose output, which includes information about each class loaded and each source file compiled\&.
.TP
-version
.br
Prints release information\&.
.TP
-werror
.br
Terminates compilation when warnings occur\&.
.TP
-X
.br
Displays information about nonstandard options and exits\&.
.SS CROSS-COMPILATION\ OPTIONS    
By default, classes are compiled against the bootstrap and extension classes of the platform that \f3javac\fR shipped with\&. But \f3javac\fR also supports cross-compiling, where classes are compiled against a bootstrap and extension classes of a different Java platform implementation\&. It is important to use the \f3-bootclasspath\fR and \f3-extdirs\fR options when cross-compiling\&.
.TP
-target \fIversion\fR
.br
Generates class files that target a specified release of the virtual machine\&. Class files will run on the specified target and on later releases, but not on earlier releases of the JVM\&. Valid targets are 1\&.1, 1\&.2, 1\&.3, 1\&.4, 1\&.5 (also 5), 1\&.6 (also 6), 1\&.7 (also 7), and 1\&.8 (also 8)\&.

The default for the \f3-target\fR option depends on the value of the \f3-source\fR option:
.RS     
.TP 0.2i    
\(bu
If the \f3-source\fR option is not specified, then the value of the \f3-target\fR option is 1\&.8
.TP 0.2i    
\(bu
If the \f3-source\fR option is 1\&.2, then the value of the \f3-target\fR option is 1\&.4
.TP 0.2i    
\(bu
If the \f3-source\fR option is 1\&.3, then the value of the \f3-target\fR option is 1\&.4
.TP 0.2i    
\(bu
If the \f3-source\fR option is 1\&.5, then the value of the \f3-target\fR option is 1\&.8
.TP 0.2i    
\(bu
If the \f3-source\fR option is 1\&.6, then the value of the \f3-target\fR is option 1\&.8
.TP 0.2i    
\(bu
If the \f3-source\fR option is 1\&.7, then the value of the \f3-target\fR is option 1\&.8
.TP 0.2i    
\(bu
For all other values of the \f3-source\fR option, the value of the \f3-target\fR option is the value of the \f3-source\fR option\&.
.RE     

.TP
-bootclasspath \fIbootclasspath\fR
.br
Cross-compiles against the specified set of boot classes\&. As with the user class path, boot class path entries are separated by colons (:) and can be directories, JAR archives, or ZIP archives\&.
.SS COMPACT\ PROFILE\ OPTION    
Beginning with JDK 8, the \f3javac\fR compiler supports compact profiles\&. With compact profiles, applications that do not require the entire Java platform can be deployed and run with a smaller footprint\&. The compact profiles feature could be used to shorten the download time for applications from app stores\&. This feature makes for more compact deployment of Java applications that bundle the JRE\&. This feature is also useful in small devices\&.
.PP
The supported profile values are \f3compact1\fR, \f3compact2\fR, and \f3compact3\fR\&. These are additive layers\&. Each higher-numbered compact profile contains all of the APIs in profiles with smaller number names\&.
.TP
-profile
.br
When using compact profiles, this option specifies the profile name when compiling\&. For example:
.sp     
.nf     
\f3javac \-profile compact1 Hello\&.java\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


javac does not compile source code that uses any Java SE APIs that is not in the specified profile\&. Here is an example of the error message that results from attempting to compile such source code:
.sp     
.nf     
\f3cd jdk1\&.8\&.0/bin\fP
.fi     
.nf     
\f3\&./javac \-profile compact1 Paint\&.java\fP
.fi     
.nf     
\f3Paint\&.java:5: error: Applet is not available in profile \&'compact1\&'\fP
.fi     
.nf     
\f3import java\&.applet\&.Applet;\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


In this example, you can correct the error by modifying the source to not use the \f3Applet\fR class\&. You could also correct the error by compiling without the -profile option\&. Then the compilation would be run against the full set of Java SE APIs\&. (None of the compact profiles include the \f3Applet\fR class\&.)

An alternative way to compile with compact profiles is to use the \f3-bootclasspath\fR option to specify a path to an \f3rt\&.jar\fR file that specifies a profile\&'s image\&. Using the \f3-profile\fR option instead does not require a profile image to be present on the system at compile time\&. This is useful when cross-compiling\&.
.SS NONSTANDARD\ OPTIONS    
.TP
-Xbootclasspath/p:\fIpath\fR
.br
Adds a suffix to the bootstrap class path\&.
.TP
-Xbootclasspath/a:\fIpath\fR
.br
Adds a prefix to the bootstrap class path\&.
.TP
-Xbootclasspath/:\fIpath\fR
.br
Overrides the location of the bootstrap class files\&.
.TP
-Xdoclint:[-]\fIgroup\fR [\fI/access\fR]
.br
Enables or disables specific groups of checks, where \fIgroup\fR is one of the following values: \f3accessibility\fR, \f3syntax\fR, \f3reference\fR, \f3html\fR or \f3missing\fR\&. For more information about these groups of checks see the \f3-Xdoclint\fR option of the \f3javadoc\fR command\&. The \f3-Xdoclint\fR option is disabled by default in the \f3javac\fR command\&.

The variable \fIaccess\fR specifies the minimum visibility level of classes and members that the \f3-Xdoclint\fR option checks\&. It can have one of the following values (in order of most to least visible) : \f3public\fR, \f3protected\fR, \f3package\fR and \f3private\fR\&. For example, the following option checks classes and members (with all groups of checks) that have the access level protected and higher (which includes protected, package and public):
.sp     
.nf     
\f3\-Xdoclint:all/protected\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The following option enables all groups of checks for all access levels, except it will not check for HTML errors for classes and members that have access level package and higher (which includes package and public):
.sp     
.nf     
\f3\-Xdoclint:all,\-html/package\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-Xdoclint:none
.br
Disables all groups of checks\&.
.TP
-Xdoclint:all[\fI/access\fR]
.br
Enables all groups of checks\&.
.TP
-Xlint
.br
\fI\fREnables all recommended warnings\&. In this release, enabling all available warnings is recommended\&.
.TP
-Xlint:all
.br
\fI\fREnables all recommended warnings\&. In this release, enabling all available warnings is recommended\&.
.TP
-Xlint:none
.br
Disables all warnings\&.
.TP
-Xlint:\fIname\fR
.br
Disables warning name\&. See Enable or Disable Warnings with the -Xlint Option for a list of warnings you can disable with this option\&.
.TP
-Xlint:\fI-name\fR
.br
Disables warning name\&. See Enable or Disable Warnings with the -Xlint Option with the \f3-Xlint\fR option to get a list of warnings that you can disable with this option\&.
.TP
-Xmaxerrs \fInumber\fR
.br
Sets the maximum number of errors to print\&.
.TP
-Xmaxwarns \fInumber\fR
.br
Sets the maximum number of warnings to print\&.
.TP
-Xstdout \fIfilename\fR
.br
Sends compiler messages to the named file\&. By default, compiler messages go to \f3System\&.err\fR\&.
.TP
-Xprefer:[\fInewer,source\fR]
.br
Specifies which file to read when both a source file and class file are found for a type\&. (See Searching for Types)\&. If the \f3-Xprefer:newer\fR option is used, then it reads the newer of the source or class file for a type (default)\&. If the \f3-Xprefer:source\fR option is used, then it reads the source file\&. Use -\f3Xprefer:source\fR when you want to be sure that any annotation processors can access annotations declared with a retention policy of \f3SOURCE\fR\&.
.TP
-Xpkginfo:[\fIalways\fR,\fIlegacy\fR,\fInonempty\fR]
.br
Control whether javac generates \f3package-info\&.class\fR files from package-info\&.java files\&. Possible mode arguments for this option include the following\&.
.RS     
.TP     
always
Always generate a \f3package-info\&.class\fR file for every \f3package-info\&.java\fR file\&. This option may be useful if you use a build system such as Ant, which checks that each \f3\&.java\fR file has a corresponding \f3\&.class\fR file\&.
.TP     
legacy
Generate a \f3package-info\&.class\fR file only if package-info\&.java contains annotations\&. Don\&'t generate a \f3package-info\&.class\fR file if package-info\&.java only contains comments\&.

\fINote:\fR A \f3package-info\&.class\fR file might be generated but be empty if all the annotations in the package-info\&.java file have \f3RetentionPolicy\&.SOURCE\fR\&.
.TP     
nonempty
Generate a \f3package-info\&.class\fR file only if package-info\&.java contains annotations with \f3RetentionPolicy\&.CLASS\fR or \f3RetentionPolicy\&.RUNTIME\fR\&.
.RE     

.TP
-Xprint
.br
Prints a textual representation of specified types for debugging purposes\&. Perform neither annotation processing nor compilation\&. The format of the output could change\&.
.TP
-XprintProcessorInfo
.br
Prints information about which annotations a processor is asked to process\&.
.TP
-XprintRounds
.br
Prints information about initial and subsequent annotation processing rounds\&.
.SH ENABLE\ OR\ DISABLE\ WARNINGS\ WITH\ THE\ -XLINT\ OPTION    
Enable warning \fIname\fR with the \f3-Xlint:name\fR option, where \f3name\fR is one of the following warning names\&. Note that you can disable a warning with the \f3-Xlint:-name:\fR option\&.
.TP     
cast
Warns about unnecessary and redundant casts, for example:
.sp     
.nf     
\f3String s = (String) "Hello!"\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP     
classfile
Warns about issues related to class file contents\&.
.TP     
deprecation
Warns about the use of deprecated items, for example:
.sp     
.nf     
\f3java\&.util\&.Date myDate = new java\&.util\&.Date();\fP
.fi     
.nf     
\f3int currentDay = myDate\&.getDay();\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The method \f3java\&.util\&.Date\&.getDay\fR has been deprecated since JDK 1\&.1
.TP     
dep-ann
Warns about items that are documented with an \f3@deprecated\fR Javadoc comment, but do not have a \f3@Deprecated\fR annotation, for example:
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3  * @deprecated As of Java SE 7, replaced by {@link #newMethod()}\fP
.fi     
.nf     
\f3  */\fP
.fi     
.nf     
\f3public static void deprecatedMethood() { }\fP
.fi     
.nf     
\f3public static void newMethod() { }\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP     
divzero
Warns about division by the constant integer 0, for example:
.sp     
.nf     
\f3int divideByZero = 42 / 0;\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP     
empty
Warns about empty statements after \f3if\fRstatements, for example:
.sp     
.nf     
\f3class E {\fP
.fi     
.nf     
\f3    void m() {\fP
.fi     
.nf     
\f3         if (true) ;\fP
.fi     
.nf     
\f3    }\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP     
fallthrough
Checks the switch blocks for fall-through cases and provides a warning message for any that are found\&. Fall-through cases are cases in a switch block, other than the last case in the block, whose code does not include a break statement, allowing code execution to fall through from that case to the next case\&. For example, the code following the case 1 label in this switch block does not end with a break statement:
.sp     
.nf     
\f3switch (x) {\fP
.fi     
.nf     
\f3case 1:\fP
.fi     
.nf     
\f3  System\&.out\&.println("1");\fP
.fi     
.nf     
\f3  // No break statement here\&.\fP
.fi     
.nf     
\f3case 2:\fP
.fi     
.nf     
\f3  System\&.out\&.println("2");\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


If the \f3-Xlint:fallthrough\fR option was used when compiling this code, then the compiler emits a warning about possible fall-through into case, with the line number of the case in question\&.
.TP     
finally
Warns about \f3finally\fR clauses that cannot complete normally, for example:
.sp     
.nf     
\f3public static int m() {\fP
.fi     
.nf     
\f3  try {\fP
.fi     
.nf     
\f3     throw new NullPointerException();\fP
.fi     
.nf     
\f3  }  catch (NullPointerException(); {\fP
.fi     
.nf     
\f3     System\&.err\&.println("Caught NullPointerException\&.");\fP
.fi     
.nf     
\f3     return 1;\fP
.fi     
.nf     
\f3   } finally {\fP
.fi     
.nf     
\f3     return 0;\fP
.fi     
.nf     
\f3   }\fP
.fi     
.nf     
\f3  }\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The compiler generates a warning for the \f3finally\fR block in this example\&. When the \f3int\fR method is called, it returns a value of 0\&. A \f3finally\fR block executes when the \f3try\fR block exits\&. In this example, when control is transferred to the \f3catch\fR block, the \f3int\fR method exits\&. However, the \f3finally\fR block must execute, so it is executed, even though control was transferred outside the method\&.
.TP     
options
Warns about issues that related to the use of command-line options\&. See Cross-Compilation Options\&.
.TP     
overrides
Warns about issues regarding method overrides\&. For example, consider the following two classes:
.sp     
.nf     
\f3public class ClassWithVarargsMethod {\fP
.fi     
.nf     
\f3  void varargsMethod(String\&.\&.\&. s) { }\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3public class ClassWithOverridingMethod extends ClassWithVarargsMethod {\fP
.fi     
.nf     
\f3   @Override\fP
.fi     
.nf     
\f3   void varargsMethod(String[] s) { }\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The compiler generates a warning similar to the following:\&.
.sp     
.nf     
\f3warning: [override] varargsMethod(String[]) in ClassWithOverridingMethod \fP
.fi     
.nf     
\f3overrides varargsMethod(String\&.\&.\&.) in ClassWithVarargsMethod; overriding\fP
.fi     
.nf     
\f3method is missing \&'\&.\&.\&.\&'\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


When the compiler encounters a \f3varargs\fR method, it translates the \f3varargs\fR formal parameter into an array\&. In the method \f3ClassWithVarargsMethod\&.varargsMethod\fR, the compiler translates the \f3varargs\fR formal parameter \f3String\&.\&.\&. s\fR to the formal parameter \f3String[] s\fR, an array, which matches the formal parameter of the method \f3ClassWithOverridingMethod\&.varargsMethod\fR\&. Consequently, this example compiles\&.
.TP     
path
Warns about invalid path elements and nonexistent path directories on the command line (with regard to the class path, the source path, and other paths)\&. Such warnings cannot be suppressed with the \f3@SuppressWarnings\fR annotation, for example:
.sp     
.nf     
\f3javac \-Xlint:path \-classpath /nonexistentpath Example\&.java\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP     
processing
Warn about issues regarding annotation processing\&. The compiler generates this warning when you have a class that has an annotation, and you use an annotation processor that cannot handle that type of exception\&. For example, the following is a simple annotation processor:

\fISource file AnnocProc\&.java\fR:
.sp     
.nf     
\f3import java\&.util\&.*;\fP
.fi     
.nf     
\f3import javax\&.annotation\&.processing\&.*;\fP
.fi     
.nf     
\f3import javax\&.lang\&.model\&.*;\fP
.fi     
.nf     
\f3import\&.javaz\&.lang\&.model\&.element\&.*;\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3@SupportedAnnotationTypes("NotAnno")\fP
.fi     
.nf     
\f3public class AnnoProc extends AbstractProcessor {\fP
.fi     
.nf     
\f3  public boolean process(Set<? extends TypeElement> elems, RoundEnvironment renv){\fP
.fi     
.nf     
\f3     return true;\fP
.fi     
.nf     
\f3  }\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3  public SourceVersion getSupportedSourceVersion() {\fP
.fi     
.nf     
\f3     return SourceVersion\&.latest();\fP
.fi     
.nf     
\f3   }\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


\fISource file AnnosWithoutProcessors\&.java\fR:
.sp     
.nf     
\f3@interface Anno { }\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3@Anno\fP
.fi     
.nf     
\f3class AnnosWithoutProcessors { }\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The following commands compile the annotation processor \f3AnnoProc\fR, then run this annotation processor against the source file \f3AnnosWithoutProcessors\&.java\fR:
.sp     
.nf     
\f3javac AnnoProc\&.java\fP
.fi     
.nf     
\f3javac \-cp \&. \-Xlint:processing \-processor AnnoProc \-proc:only AnnosWithoutProcessors\&.java\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


When the compiler runs the annotation processor against the source file \f3AnnosWithoutProcessors\&.java\fR, it generates the following warning:
.sp     
.nf     
\f3warning: [processing] No processor claimed any of these annotations: Anno\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


To resolve this issue, you can rename the annotation defined and used in the class \f3AnnosWithoutProcessors\fR from \f3Anno\fR to \f3NotAnno\fR\&.
.TP     
rawtypes
Warns about unchecked operations on raw types\&. The following statement generates a \f3rawtypes\fR warning:
.sp     
.nf     
\f3void countElements(List l) { \&.\&.\&. }\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The following example does not generate a \f3rawtypes\fR warning
.sp     
.nf     
\f3void countElements(List<?> l) { \&.\&.\&. }\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


\f3List\fR is a raw type\&. However, \f3List<?>\fR is an unbounded wildcard parameterized type\&. Because \f3List\fR is a parameterized interface, always specify its type argument\&. In this example, the \f3List\fR formal argument is specified with an unbounded wildcard (\f3?\fR) as its formal type parameter, which means that the \f3countElements\fR method can accept any instantiation of the \f3List\fR interface\&.
.TP     
Serial
Warns about missing \f3serialVersionUID\fR definitions on serializable classes, for example:
.sp     
.nf     
\f3public class PersistentTime implements Serializable\fP
.fi     
.nf     
\f3{\fP
.fi     
.nf     
\f3  private Date time;\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3   public PersistentTime() {\fP
.fi     
.nf     
\f3     time = Calendar\&.getInstance()\&.getTime();\fP
.fi     
.nf     
\f3   }\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3   public Date getTime() {\fP
.fi     
.nf     
\f3     return time;\fP
.fi     
.nf     
\f3   }\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The compiler generates the following warning:
.sp     
.nf     
\f3warning: [serial] serializable class PersistentTime has no definition of\fP
.fi     
.nf     
\f3serialVersionUID\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


If a serializable class does not explicitly declare a field named \f3serialVersionUID\fR, then the serialization runtime environment calculates a default \f3serialVersionUID\fR value for that class based on various aspects of the class, as described in the Java Object Serialization Specification\&. However, it is strongly recommended that all serializable classes explicitly declare \f3serialVersionUID\fR values because the default process of computing \f3serialVersionUID\fR vales is highly sensitive to class details that can vary depending on compiler implementations, and as a result, might cause an unexpected \f3InvalidClassExceptions\fR during deserialization\&. To guarantee a consistent \f3serialVersionUID\fR value across different Java compiler implementations, a serializable class must declare an explicit \f3serialVersionUID\fR value\&.
.TP     
static
Warns about issues relating to the use of statics, for example:
.sp     
.nf     
\f3class XLintStatic {\fP
.fi     
.nf     
\f3    static void m1() { }\fP
.fi     
.nf     
\f3    void m2() { this\&.m1(); }\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The compiler generates the following warning:
.sp     
.nf     
\f3warning: [static] static method should be qualified by type name, \fP
.fi     
.nf     
\f3XLintStatic, instead of by an expression\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


To resolve this issue, you can call the \f3static\fR method \f3m1\fR as follows:
.sp     
.nf     
\f3XLintStatic\&.m1();\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


Alternately, you can remove the \f3static\fR keyword from the declaration of the method \f3m1\fR\&.
.TP     
try
Warns about issues relating to use of \f3try\fR blocks, including try-with-resources statements\&. For example, a warning is generated for the following statement because the resource \f3ac\fR declared in the \f3try\fR block is not used:
.sp     
.nf     
\f3try ( AutoCloseable ac = getResource() ) {    // do nothing}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP     
unchecked
Gives more detail for unchecked conversion warnings that are mandated by the Java Language Specification, for example:
.sp     
.nf     
\f3List l = new ArrayList<Number>();\fP
.fi     
.nf     
\f3List<String> ls = l;       // unchecked warning\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


During type erasure, the types \f3ArrayList<Number>\fR and \f3List<String>\fR become \f3ArrayList\fR and \f3List\fR, respectively\&.

The \f3ls\fR command has the parameterized type \f3List<String>\fR\&. When the \f3List\fR referenced by \f3l\fR is assigned to \f3ls\fR, the compiler generates an unchecked warning\&. At compile time, the compiler and JVM cannot determine whether \f3l\fR refers to a \f3List<String>\fR type\&. In this case, \f3l\fR does not refer to a \f3List<String>\fR type\&. As a result, heap pollution occurs\&.

A heap pollution situation occurs when the \f3List\fR object \f3l\fR, whose static type is \f3List<Number>\fR, is assigned to another \f3List\fR object, \f3ls\fR, that has a different static type, \f3List<String>\fR\&. However, the compiler still allows this assignment\&. It must allow this assignment to preserve backward compatibility with releases of Java SE that do not support generics\&. Because of type erasure, \f3List<Number>\fR and \f3List<String>\fR both become \f3List\fR\&. Consequently, the compiler allows the assignment of the object \f3l\fR\f3,\fR which has a raw type of \f3List\fR, to the object \f3ls\fR\&.
.TP     
varargs
Warns about unsafe usages of variable arguments (\f3varargs\fR) methods, in particular, those that contain non-reifiable arguments, for example:
.sp     
.nf     
\f3public class ArrayBuilder {\fP
.fi     
.nf     
\f3  public static <T> void addToList (List<T> listArg, T\&.\&.\&. elements) {\fP
.fi     
.nf     
\f3    for (T x : elements) {\fP
.fi     
.nf     
\f3      listArg\&.add(x);\fP
.fi     
.nf     
\f3    }\fP
.fi     
.nf     
\f3  }\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


\fINote:\fR A non-reifiable type is a type whose type information is not fully available at runtime\&.

The compiler generates the following warning for the definition of the method \f3ArrayBuilder\&.addToList\fR
.sp     
.nf     
\f3warning: [varargs] Possible heap pollution from parameterized vararg type T\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


When the compiler encounters a varargs method, it translates the \f3varargs\fR formal parameter into an array\&. However, the Java programming language does not permit the creation of arrays of parameterized types\&. In the method \f3ArrayBuilder\&.addToList\fR, the compiler translates the \f3varargs\fR formal parameter \f3T\&.\&.\&.\fR elements to the formal parameter \f3T[]\fR elements, an array\&. However, because of type erasure, the compiler converts the \f3varargs\fR formal parameter to \f3Object[]\fR elements\&. Consequently, there is a possibility of heap pollution\&.
.SH COMMAND-LINE\ ARGUMENT\ FILES    
To shorten or simplify the \f3javac\fR command, you can specify one or more files that contain arguments to the \f3javac\fR command (except \f3-J\fR options)\&. This enables you to create \f3javac\fR commands of any length on any operating system\&.
.PP
An argument file can include \f3javac\fR options and source file names in any combination\&. The arguments within a file can be separated by spaces or new line characters\&. If a file name contains embedded spaces, then put the whole file name in double quotation marks\&.
.PP
File Names within an argument file are relative to the current directory, not the location of the argument file\&. Wild cards (*) are not allowed in these lists (such as for specifying \f3*\&.java\fR)\&. Use of the at sign (@) to recursively interpret files is not supported\&. The \f3-J\fR options are not supported because they are passed to the launcher, which does not support argument files\&.
.PP
When executing the \f3javac\fR command, pass in the path and name of each argument file with the at sign (@) leading character\&. When the \f3javac\fR command encounters an argument beginning with the at sign (@), it expands the contents of that file into the argument list\&.
.PP
\f3Example 1 Single Argument File\fR
.PP
You could use a single argument file named \f3argfile\fR to hold all \f3javac\fR arguments:
.sp     
.nf     
\f3javac @argfile\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
This argument file could contain the contents of both files shown in Example 2
.PP
\f3Example 2 Two Argument Files\fR
.PP
You can create two argument files: one for the \f3javac\fR options and the other for the source file names\&. Note that the following lists have no line-continuation characters\&.
.PP
Create a file named options that contains the following:
.sp     
.nf     
\f3\-d classes\fP
.fi     
.nf     
\f3\-g\fP
.fi     
.nf     
\f3\-sourcepath /java/pubs/ws/1\&.3/src/share/classes\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Create a file named classes that contains the following:
.sp     
.nf     
\f3MyClass1\&.java\fP
.fi     
.nf     
\f3MyClass2\&.java\fP
.fi     
.nf     
\f3MyClass3\&.java\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Then, run the \f3javac\fR command as follows:
.sp     
.nf     
\f3javac @options @classes\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3Example 3 Argument Files with Paths\fR
.PP
The argument files can have paths, but any file names inside the files are relative to the current working directory (not \f3path1\fR or \f3path2\fR):
.sp     
.nf     
\f3javac @path1/options @path2/classes\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH ANNOTATION\ PROCESSING    
The \f3javac\fR command provides direct support for annotation processing, superseding the need for the separate annotation processing command, \f3apt\fR\&.
.PP
The API for annotation processors is defined in the \f3javax\&.annotation\&.processing\fR and j\f3avax\&.lang\&.model\fR packages and subpackages\&.
.SS HOW\ ANNOTATION\ PROCESSING\ WORKS    
Unless annotation processing is disabled with the \f3-proc:none\fR option, the compiler searches for any annotation processors that are available\&. The search path can be specified with the \f3-processorpath\fR option\&. If no path is specified, then the user class path is used\&. Processors are located by means of service provider-configuration files named \f3META-INF/services/javax\&.annotation\&.processing\fR\&.Processor on the search path\&. Such files should contain the names of any annotation processors to be used, listed one per line\&. Alternatively, processors can be specified explicitly, using the \f3-processor\fR option\&.
.PP
After scanning the source files and classes on the command line to determine what annotations are present, the compiler queries the processors to determine what annotations they process\&. When a match is found, the processor is called\&. A processor can claim the annotations it processes, in which case no further attempt is made to find any processors for those annotations\&. After all of the annotations are claimed, the compiler does not search for additional processors\&.
.PP
If any processors generate new source files, then another round of annotation processing occurs: Any newly generated source files are scanned, and the annotations processed as before\&. Any processors called on previous rounds are also called on all subsequent rounds\&. This continues until no new source files are generated\&.
.PP
After a round occurs where no new source files are generated, the annotation processors are called one last time, to give them a chance to complete any remaining work\&. Finally, unless the \f3-proc:only\fR option is used, the compiler compiles the original and all generated source files\&.
.SS IMPLICITLY\ LOADED\ SOURCE\ FILES    
To compile a set of source files, the compiler might need to implicitly load additional source files\&. See Searching for Types\&. Such files are currently not subject to annotation processing\&. By default, the compiler gives a warning when annotation processing occurred and any implicitly loaded source files are compiled\&. The \f3-implicit\fR option provides a way to suppress the warning\&.
.SH SEARCHING\ FOR\ TYPES    
To compile a source file, the compiler often needs information about a type, but the type definition is not in the source files specified on the command line\&. The compiler needs type information for every class or interface used, extended, or implemented in the source file\&. This includes classes and interfaces not explicitly mentioned in the source file, but that provide information through inheritance\&.
.PP
For example, when you create a subclass \f3java\&.applet\&.Applet\fR, you are also using the ancestor classes of \f3Applet\fR: \f3java\&.awt\&.Panel\fR, \f3java\&.awt\&.Container\fR, \f3java\&.awt\&.Component\fR, and \f3java\&.lang\&.Object\fR\&.
.PP
When the compiler needs type information, it searches for a source file or class file that defines the type\&. The compiler searches for class files first in the bootstrap and extension classes, then in the user class path (which by default is the current directory)\&. The user class path is defined by setting the \f3CLASSPATH\fR environment variable or by using the \f3-classpath\fR option\&.
.PP
If you set the \f3-sourcepath\fR option, then the compiler searches the indicated path for source files\&. Otherwise, the compiler searches the user class path for both class files and source files\&.
.PP
You can specify different bootstrap or extension classes with the \f3-bootclasspath\fR and the \f3-extdirs\fR options\&. See Cross-Compilation Options\&.
.PP
A successful type search may produce a class file, a source file, or both\&. If both are found, then you can use the \f3-Xprefer\fR option to instruct the compiler which to use\&. If \f3newer\fR is specified, then the compiler uses the newer of the two files\&. If \f3source\fR is specified, the compiler uses the source file\&. The default is \f3newer\fR\&.
.PP
If a type search finds a source file for a required type, either by itself, or as a result of the setting for the \f3-Xprefer\fR option, then the compiler reads the source file to get the information it needs\&. By default the compiler also compiles the source file\&. You can use the \f3-implicit\fR option to specify the behavior\&. If \f3none\fR is specified, then no class files are generated for the source file\&. If \f3class\fR is specified, then class files are generated for the source file\&.
.PP
The compiler might not discover the need for some type information until after annotation processing completes\&. When the type information is found in a source file and no \f3-implicit\fR option is specified, the compiler gives a warning that the file is being compiled without being subject to annotation processing\&. To disable the warning, either specify the file on the command line (so that it will be subject to annotation processing) or use the \f3-implicit\fR option to specify whether or not class files should be generated for such source files\&.
.SH PROGRAMMATIC\ INTERFACE    
The \f3javac\fR command supports the new Java Compiler API defined by the classes and interfaces in the \f3javax\&.tools\fR package\&.
.SS EXAMPLE    
To compile as though providing command-line arguments, use the following syntax:
.sp     
.nf     
\f3JavaCompiler javac = ToolProvider\&.getSystemJavaCompiler();\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The example writes diagnostics to the standard output stream and returns the exit code that \f3javac\fR would give when called from the command line\&.
.PP
You can use other methods in the \f3javax\&.tools\&.JavaCompiler\fR interface to handle diagnostics, control where files are read from and written to, and more\&.
.SS OLD\ INTERFACE    
\fINote:\fR This API is retained for backward compatibility only\&. All new code should use the newer Java Compiler API\&.
.PP
The \f3com\&.sun\&.tools\&.javac\&.Main\fR class provides two static methods to call the compiler from a program:
.sp     
.nf     
\f3public static int compile(String[] args);\fP
.fi     
.nf     
\f3public static int compile(String[] args, PrintWriter out);\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The \f3args\fR parameter represents any of the command-line arguments that would typically be passed to the compiler\&.
.PP
The \f3out\fR parameter indicates where the compiler diagnostic output is directed\&.
.PP
The \f3return\fR value is equivalent to the \f3exit\fR value from \f3javac\fR\&.
.PP
\fINote:\fR All other classes and methods found in a package with names that start with \f3com\&.sun\&.tools\&.javac\fR (subpackages of \f3com\&.sun\&.tools\&.javac\fR) are strictly internal and subject to change at any time\&.
.SH EXAMPLES    
\f3Example 1 Compile a Simple Program\fR
.PP
This example shows how to compile the \f3Hello\&.java\fR source file in the greetings directory\&. The class defined in \f3Hello\&.java\fR is called \f3greetings\&.Hello\fR\&. The greetings directory is the package directory both for the source file and the class file and is underneath the current directory\&. This makes it possible to use the default user class path\&. It also makes it unnecessary to specify a separate destination directory with the \f3-d\fR option\&.
.PP
The source code in \f3Hello\&.java\fR:
.sp     
.nf     
\f3package greetings;\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3public class Hello {\fP
.fi     
.nf     
\f3    public static void main(String[] args) {\fP
.fi     
.nf     
\f3        for (int i=0; i < args\&.length; i++) {\fP
.fi     
.nf     
\f3            System\&.out\&.println("Hello " + args[i]);\fP
.fi     
.nf     
\f3        }\fP
.fi     
.nf     
\f3    }\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Compile greetings\&.Hello:
.sp     
.nf     
\f3javac greetings/Hello\&.java\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Run \f3greetings\&.Hello\fR:
.sp     
.nf     
\f3java greetings\&.Hello World Universe Everyone\fP
.fi     
.nf     
\f3Hello World\fP
.fi     
.nf     
\f3Hello Universe\fP
.fi     
.nf     
\f3Hello Everyone\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3Example 2 Compile Multiple Source Files\fR
.PP
This example compiles the \f3Aloha\&.java\fR, \f3GutenTag\&.java\fR, \f3Hello\&.java\fR, and \f3Hi\&.java\fR source files in the \f3greetings\fR package\&.
.sp     
.nf     
\f3% javac greetings/*\&.java\fP
.fi     
.nf     
\f3% ls greetings\fP
.fi     
.nf     
\f3Aloha\&.class         GutenTag\&.class      Hello\&.class         Hi\&.class\fP
.fi     
.nf     
\f3Aloha\&.java          GutenTag\&.java       Hello\&.java          Hi\&.java\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3Example 3 Specify a User Class Path\fR
.PP
After changing one of the source files in the previous example, recompile it:
.sp     
.nf     
\f3pwd\fP
.fi     
.nf     
\f3/examples\fP
.fi     
.nf     
\f3javac greetings/Hi\&.java\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Because \f3greetings\&.Hi\fR refers to other classes in the \f3greetings\fR package, the compiler needs to find these other classes\&. The previous example works because the default user class path is the directory that contains the package directory\&. If you want to recompile this file without concern for which directory you are in, then add the examples directory to the user class path by setting \f3CLASSPATH\fR\&. This example uses the \f3-classpath\fR option\&.
.sp     
.nf     
\f3javac \-classpath /examples /examples/greetings/Hi\&.java\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
If you change \f3greetings\&.Hi\fR to use a banner utility, then that utility also needs to be accessible through the user class path\&.
.sp     
.nf     
\f3javac \-classpath /examples:/lib/Banners\&.jar \e\fP
.fi     
.nf     
\f3            /examples/greetings/Hi\&.java\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
To execute a class in the \f3greetings\fR package, the program needs access to the \f3greetings\fR package, and to the classes that the \f3greetings\fR classes use\&.
.sp     
.nf     
\f3java \-classpath /examples:/lib/Banners\&.jar greetings\&.Hi\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3Example 4 Separate Source Files and Class Files\fR
.PP
The following example uses \f3javac\fR to compile code that runs on JVM 1\&.7\&.
.sp     
.nf     
\f3javac \-source 1\&.7 \-target 1\&.7 \-bootclasspath jdk1\&.7\&.0/lib/rt\&.jar \e \fP
.fi     
.nf     
\f3\-extdirs "" OldCode\&.java\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The \f3-source 1\&.7\fR option specifies that release 1\&.7 (or 7) of the Java programming language be used to compile \f3OldCode\&.java\fR\&. The option \f3-target 1\&.7\fR option ensures that the generated class files are compatible with JVM 1\&.7\&. Note that in most cases, the value of the \f3-target\fR option is the value of the \f3-source\fR option; in this example, you can omit the \f3-target\fR option\&.
.PP
You must specify the \f3-bootclasspath\fR option to specify the correct version of the bootstrap classes (the \f3rt\&.jar\fR library)\&. If not, then the compiler generates a warning:
.sp     
.nf     
\f3javac \-source 1\&.7 OldCode\&.java\fP
.fi     
.nf     
\f3warning: [options] bootstrap class path not set in conjunction with \fP
.fi     
.nf     
\f3\-source 1\&.7\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
If you do not specify the correct version of bootstrap classes, then the compiler uses the old language rules (in this example, it uses version 1\&.7 of the Java programming language) combined with the new bootstrap classes, which can result in class files that do not work on the older platform (in this case, Java SE 7) because reference to nonexistent methods can get included\&.
.PP
\f3Example 5 Cross Compile\fR
.PP
This example uses \f3javac\fR to compile code that runs on JVM 1\&.7\&.
.sp     
.nf     
\f3javac \-source 1\&.7 \-target 1\&.7 \-bootclasspath jdk1\&.7\&.0/lib/rt\&.jar \e\fP
.fi     
.nf     
\f3            \-extdirs "" OldCode\&.java\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The\f3-source 1\&.7\fR option specifies that release 1\&.7 (or 7) of the Java programming language to be used to compile OldCode\&.java\&. The \f3-target 1\&.7\fR option ensures that the generated class files are compatible with JVM 1\&.7\&.
.PP
You must specify the \f3-bootclasspath\fR option to specify the correct version of the bootstrap classes (the \f3rt\&.jar\fR library)\&. If not, then the compiler generates a warning:
.sp     
.nf     
\f3javac \-source 1\&.7 OldCode\&.java\fP
.fi     
.nf     
\f3warning: [options] bootstrap class path not set in conjunction with \-source 1\&.7\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
If you do not specify the correct version of bootstrap classes, then the compiler uses the old language rules combined with the new bootstrap classes\&. This combination can result in class files that do not work on the older platform (in this case, Java SE 7) because reference to nonexistent methods can get included\&. In this example, the compiler uses release 1\&.7 of the Java programming language\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
java(1)
.TP 0.2i    
\(bu
jdb(1)
.TP 0.2i    
\(bu
javah(1)
.TP 0.2i    
\(bu
javadoc(1)
.TP 0.2i    
\(bu
jar(1)
.TP 0.2i    
\(bu
jdb(1)
.RE
.br
'pl 8.5i
'bp
