'\" t
.\"  Copyright (c) 1994, 2015, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 03 March 2015
.\"     SectDesc: Basic Tools
.\"     Title: javadoc.1
.\"
.if n .pl 99999
.TH javadoc 1 "03 March 2015" "JDK 8" "Basic Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
javadoc \- Generates HTML pages of API documentation from Java source files\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjavadoc\fR {\fIpackages\fR|\fIsource\-files\fR} [\fIoptions\fR] [\fI@argfiles\fR]
.fi     
.sp     
.TP     
\fIpackages\fR
Names of packages that you want to document, separated by spaces, for example \f3java\&.lang java\&.lang\&.reflect java\&.awt\fR\&. If you want to also document the subpackages, use the \f3-subpackages\fR option to specify the packages\&.

By default, \f3javadoc\fR looks for the specified packages in the current directory and subdirectories\&. Use the \f3-sourcepath\fR option to specify the list of directories where to look for packages\&.
.TP     
\fIsource-files\fR
Names of Java source files that you want to document, separated by spaces, for example \f3Class\&.java Object\&.java Button\&.java\fR\&. By default, \f3javadoc\fR looks for the specified classes in the current directory\&. However, you can specify the full path to the class file and use wildcard characters, for example \f3/home/<USER>/java/awt/Graphics*\&.java\fR\&. You can also specify the path relative to the current directory\&.
.TP     
\fIoptions\fR
Command-line options, separated by spaces\&. See Options\&.
.TP     
\fI@argfiles\fR
Names of files that contain a list of \f3javadoc\fR command options, package names and source file names in any order\&.
.SH DESCRIPTION    
The \f3javadoc\fR command parses the declarations and documentation comments in a set of Java source files and produces a corresponding set of HTML pages that describe (by default) the public and protected classes, nested classes (but not anonymous inner classes), interfaces, constructors, methods, and fields\&. You can use the \f3javadoc\fR command to generate the API documentation or the implementation documentation for a set of source files\&.
.PP
You can run the \f3javadoc\fR command on entire packages, individual source files, or both\&. When documenting entire packages, you can either use the \f3-subpackages\fR option to recursively traverse a directory and its subdirectories, or to pass in an explicit list of package names\&. When you document individual source files, pass in a list of Java source file names\&. See Simple Examples\&.
.SS PROCESS\ SOURCE\ FILES    
The \f3javadoc\fR command processes files that end in source and other files described in Source Files\&. If you run the \f3javadoc\fR command by passing in individual source file names, then you can determine exactly which source files are processed\&. However, that is not how most developers want to work, because it is simpler to pass in package names\&. The \f3javadoc\fR command can be run three ways without explicitly specifying the source file names\&. You can pass in package names, use the \f3-subpackages\fR option, or use wild cards with source file names\&. In these cases, the \f3javadoc\fR command processes a source file only when the file fulfills all of the following requirements:
.TP 0.2i    
\(bu
The file name prefix (with \f3\&.java\fR removed) is a valid class name\&.
.TP 0.2i    
\(bu
The path name relative to the root of the source tree is a valid package name after the separators are converted to dots\&.
.TP 0.2i    
\(bu
The package statement contains the valid package name\&.
.PP
Processing Links

During a run, the \f3javadoc\fR command adds cross-reference links to package, class, and member names that are being documented as part of that run\&. Links appear in the following places\&. See Javadoc Tags for a description of the @ tags\&.
.TP 0.2i    
\(bu
Declarations (return types, argument types, and field types)\&.
.TP 0.2i    
\(bu
\fISee Also\fR sections that are generated from \f3@see\fR tags\&.
.TP 0.2i    
\(bu
Inline text generated from \f3{@link}\fR tags\&.
.TP 0.2i    
\(bu
Exception names generated from \f3@throws\fR tags\&.
.TP 0.2i    
\(bu
\fISpecified by\fR links to interface members and \fIOverrides\fR links to class members\&. See Method Comment Inheritance\&.
.TP 0.2i    
\(bu
Summary tables listing packages, classes and members\&.
.TP 0.2i    
\(bu
Package and class inheritance trees\&.
.TP 0.2i    
\(bu
The index\&.
.PP
You can add links to existing text for classes not included on the command line (but generated separately) by way of the \f3-link\fR and \f3-linkoffline\fR options\&.
.PP
Processing Details

The \f3javadoc\fR command produces one complete document every time it runs\&. It does not do incremental builds that modify or directly incorporate the results from earlier runs\&. However, the \f3javadoc\fR command can link to results from other runs\&.
.PP
The \f3javadoc\fR command implementation requires and relies on the Java compiler\&. The \f3javadoc\fR command calls part of the \f3javac\fR command to compile the declarations and ignore the member implementations\&. The \f3javadoc\fR command builds a rich internal representation of the classes that includes the class hierarchy and use relationships to generate the HTML\&. The \f3javadoc\fR command also picks up user-supplied documentation from documentation comments in the source code\&. See Documentation Comments\&.
.PP
The \f3javadoc\fR command runs on source files that are pure stub files with no method bodies\&. This means you can write documentation comments and run the \f3javadoc\fR command in the early stages of design before API implementation\&.
.PP
Relying on the compiler ensures that the HTML output corresponds exactly with the actual implementation, which may rely on implicit, rather than explicit, source code\&. For example, the \f3javadoc\fR command documents default constructors that are present in the compiled class files but not in the source code\&.
.PP
In many cases, the \f3javadoc\fR command lets you generate documentation for source files with incomplete or erroneous code\&. You can generate documentation before all debugging and troubleshooting is done\&. The \f3javadoc\fR command does primitive checking of documentation comments\&.
.PP
When the \f3javadoc\fR command builds its internal structure for the documentation, it loads all referenced classes\&. Because of this, the \f3javadoc\fR command must be able to find all referenced classes, whether bootstrap classes, extensions, or user classes\&. See How Classes Are Found at http://docs\&.oracle\&.com/javase/8/docs/technotes/tools/findingclasses\&.html
.PP
Typically, classes you create must either be loaded as an extension or in the \f3javadoc\fR command class path\&.
.SS JAVADOC\ DOCLETS    
You can customize the content and format of the \f3javadoc\fR command output with doclets\&. The \f3javadoc\fR command has a default built-in doclet, called the standard doclet, that generates HTML-formatted API documentation\&. You can modify or make a subclass of the standard doclet, or write your own doclet to generate HTML, XML, MIF, RTF or whatever output format you want\&.
.PP
When a custom doclet is not specified with the \f3-doclet\fR option, the \f3javadoc\fR command uses the default standard doclet\&. The \f3javadoc\fR command has several options that are available regardless of which doclet is being used\&. The standard doclet adds a supplementary set of command-line options\&. See Options\&.
.SH SOURCE\ FILES    
The \f3javadoc\fR command generates output that originates from the following types of source files: Java language source files for classes (\f3\&.java\fR), package comment files, overview comment files, and miscellaneous unprocessed files\&. This section also describes test files and template files that can also be in the source tree, but that you want to be sure not to document\&.
.SS CLASS\ SOURCE\ FILES    
Each class or interface and its members can have their own documentation comments contained in a source file\&. See Documentation Comments\&.
.SS PACKAGE\ COMMENT\ FILES    
Each package can have its own documentation comment, contained in its own source file, that the \f3javadoc\fR command merges into the generated package summary page\&. You typically include in this comment any documentation that applies to the entire package\&.
.PP
To create a package comment file, you can place your comments in one of the following files:
.TP 0.2i    
\(bu
The \f3package-info\&.java\fR file can contain the package declaration, package annotations, package comments, and Javadoc tags\&. This file is preferred\&.
.TP 0.2i    
\(bu
The \f3package\&.html\fR file contains only package comments and Javadoc tags\&. No package annotations\&.
.PP
A package can have a single \f3package\&.html\fR file or a single \f3package-info\&.java\fR file, but not both\&. Place either file in the package directory in the source tree with your source files\&.
.PP
The package-info\&.java File

The \f3package-info\&.java\fR file can contain a package comment of the following structure\&. The comment is placed before the package declaration\&.
.PP
\fINote:\fR The comment separators \f3/**\fR and \f3*/\fR must be present, but the leading asterisks on the intermediate lines can be left off\&.
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * Provides the classes necessary to create an  \fP
.fi     
.nf     
\f3 * applet and the classes an applet uses \fP
.fi     
.nf     
\f3 * to communicate with its applet context\&.\fP
.fi     
.nf     
\f3 * <p>\fP
.fi     
.nf     
\f3 * The applet framework involves two entities:\fP
.fi     
.nf     
\f3 * the applet and the applet context\&.\fP
.fi     
.nf     
\f3 * An applet is an embeddable window (see the\fP
.fi     
.nf     
\f3 * {@link java\&.awt\&.Panel} class) with a few extra\fP
.fi     
.nf     
\f3 * methods that the applet context can use to \fP
.fi     
.nf     
\f3 * initialize, start, and stop the applet\&.\fP
.fi     
.nf     
\f3 *\fP
.fi     
.nf     
\f3 * @since 1\&.0\fP
.fi     
.nf     
\f3 * @see java\&.awt\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3package java\&.lang\&.applet;\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.PP
The package\&.html File

The \f3package\&.html\fR file can contain a package comment of the following structure\&. The comment is placed in the \f3<body>\fR element\&.
.PP
File: \f3java/applet/package\&.html\fR
.sp     
.nf     
\f3<HTML>\fP
.fi     
.nf     
\f3<BODY>\fP
.fi     
.nf     
\f3Provides the classes necessary to create an applet and the \fP
.fi     
.nf     
\f3classes an applet uses to communicate with its applet context\&.\fP
.fi     
.nf     
\f3<p>\fP
.fi     
.nf     
\f3The applet framework involves two entities: the applet\fP
.fi     
.nf     
\f3and the applet context\&. An applet is an embeddable\fP
.fi     
.nf     
\f3window (see the {@link java\&.awt\&.Panel} class) with a\fP
.fi     
.nf     
\f3few extra methods that the applet context can use to\fP
.fi     
.nf     
\f3initialize, start, and stop the applet\&. \fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3@since 1\&.0 \fP
.fi     
.nf     
\f3@see java\&.awt\fP
.fi     
.nf     
\f3</BODY>\fP
.fi     
.nf     
\f3</HTML>\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The \f3package\&.html\fR file is a typical HTML file and does not include a package declaration\&. The content of the package comment file is written in HTML with one exception\&. The documentation comment should not include the comment separators \f3/**\fR and \f3*/\fR or leading asterisks\&. When writing the comment, make the first sentence a summary about the package, and do not put a title or any other text between the \f3<body>\fR tag and the first sentence\&. You can include package tags\&. All block tags must appear after the main description\&. If you add an \f3@see\fR tag in a package comment file, then it must have a fully qualified name\&.
.PP
Processing the Comment File

When the \f3javadoc\fR command runs, it searches for the package comment file\&. If the package comment file is found, then the \f3javadoc\fR command does the following:
.TP 0.2i    
\(bu
Copies the comment for processing\&. For package\&.html, the \f3javadoc\fR command copies all content between the \f3<body>\fR and \f3</body>\fR HTML tags\&. You can include a \f3<head>\fR section to put a \f3<title>\fR tag, source file copyright statement, or other information, but none of these appear in the generated documentation\&.
.TP 0.2i    
\(bu
Processes the package tags\&. See Package Tags\&.
.TP 0.2i    
\(bu
Inserts the processed text at the bottom of the generated package summary page\&. See Java Platform, Standard Edition API Specification Overview at http://docs\&.oracle\&.com/javase/8/docs/api/overview-summary\&.html
.TP 0.2i    
\(bu
Copies the first sentence of the package comment to the top of the package summary page\&. The \f3javadoc\fR command also adds the package name and this first sentence to the list of packages on the overview page\&. See Java Platform, Standard Edition API Specification Overview at http://docs\&.oracle\&.com/javase/8/docs/api/overview-summary\&.html

The end of the sentence is determined by the same rules used for the end of the first sentence of class and member main descriptions\&.
.SS OVERVIEW\ COMMENT\ FILES    
Each application or set of packages that you are documenting can have its own overview documentation comment that is kept in its own source file, that the \f3javadoc\fR command merges into the generated overview page\&. You typically include in this comment any documentation that applies to the entire application or set of packages\&.
.PP
You can name the file anything you want such as overview\&.html and place it anywhere\&. A typical location is at the top of the source tree\&.
.PP
For example, if the source files for the \f3java\&.applet\fR package are contained in the /home/<USER>/src/java/applet directory, then you could create an overview comment file at /home/<USER>/src/overview\&.html\&.
.PP
You can have multiple overview comment files for the same set of source files in case you want to run the \f3javadoc\fR command multiple times on different sets of packages\&. For example, you could run the \f3javadoc\fR command once with \f3-private\fR for internal documentation and again without that option for public documentation\&. In this case, you could describe the documentation as public or internal in the first sentence of each overview comment file\&.
.PP
The content of the overview comment file is one big documentation comment that is written in HTML\&. Make the first sentence a summary about the application or set of packages\&. Do not put a title or any other text between the \f3<body>\fR tag and the first sentence\&. All tags except inline tags, such as an {\f3@link}\fR tag, must appear after the main description\&. If you add an \f3@see\fR tag, then it must have a fully qualified name\&.
.PP
When you run the \f3javadoc\fR command, specify the overview comment file name with the \f3-overview\fR option\&. The file is then processed similarly to that of a package comment file\&. The \f3javadoc\fR command does the following:
.TP 0.2i    
\(bu
Copies all content between the \f3<body>\fR and \f3</body>\fR tags for processing\&.
.TP 0.2i    
\(bu
Processes the overview tags that are present\&. See Overview Tags\&.
.TP 0.2i    
\(bu
Inserts the processed text at the bottom of the generated overview page\&. See Java Platform Standard Edition API Specification Overview at http://docs\&.oracle\&.com/javase/8/docs/api/overview-summary\&.html
.TP 0.2i    
\(bu
Copies the first sentence of the overview comment to the top of the overview summary page\&.
.SS UNPROCESSED\ FILES    
Your source files can include any files that you want the \f3javadoc\fR command to copy to the destination directory\&. These files usually include graphic files, example Java source and class files, and self-standing HTML files with a lot of content that would overwhelm the documentation comment of a typical Java source file\&.
.PP
To include unprocessed files, put them in a directory called doc-files\&. The doc-files directory can be a subdirectory of any package directory that contains source files\&. You can have one doc-files subdirectory for each package\&.
.PP
For example, if you want to include the image of a button in the \f3java\&.awt\&.Button\fR class documentation, then place the image file in the /home/<USER>/src/java/awt/doc-files/ directory\&. Do not place the doc-files directory at /home/<USER>/src/java/doc-files, because java is not a package\&. It does not contain any source files\&.
.PP
All links to the unprocessed files must be included in the code because the \f3javadoc\fR command does not look at the files\&. The \f3javadoc\fR command copies the directory and all of its contents to the destination\&. The following example shows how the link in the Button\&.java documentation comment might look:
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * This button looks like this: \fP
.fi     
.nf     
\f3 * <img src="doc\-files/Button\&.gif">\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS TEST\ AND\ TEMPLATE\ FILES    
You can store test and template files in the source tree in the same directory with or in a subdirectory of the directory where the source files reside\&. To prevent test and template files from being processed, run the \f3javadoc\fR command and explicitly pass in individual source file names\&.
.PP
Test files are valid, compilable source files\&. Template files are not valid, compatible source files, but they often have the \f3\&.java\fR suffix\&.
.PP
Test Files

If you want your test files to belong to either an unnamed package or to a package other than the package that the source files are in, then put the test files in a subdirectory underneath the source files and give the directory an invalid name\&. If you put the test files in the same directory with the source and call the \f3javadoc\fR command with a command-line argument that indicates its package name, then the test files cause warnings or errors\&. If the files are in a subdirectory with an invalid name, then the test file directory is skipped and no errors or warnings are issued\&. For example, to add test files for source files in com\&.package1, put them in a subdirectory in an invalid package name\&. The following directory name is invalid because it contains a hyphen:
.sp     
.nf     
\f3com/package1/test\-files/\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
If your test files contain documentation comments, then you can set up a separate run of the \f3javadoc\fR command to produce test file documentation by passing in their test source file names with wild cards, such as \f3com/package1/test-files/*\&.java\fR\&.
.PP
Template Files

If you want a template file to be in the source directory, but not generate errors when you execute the \f3javadoc\fR command, then give it an invalid file name such as \f3Buffer-Template\&.java\fR to prevent it from being processed\&. The \f3javadoc\fR command only processes source files with names, when stripped of the \f3\&.java\fR suffix, that are valid class names\&.
.SH GENERATED\ FILES    
By default, the \f3javadoc\fR command uses a standard doclet that generates HTML-formatted documentation\&. The standard doclet generates basic content, cross-reference, and support pages described here\&. Each HTML page corresponds to a separate file\&. The \f3javadoc\fR command generates two types of files\&. The first type is named after classes and interfaces\&. The second type contain hyphens (such as package-summary\&.html) to prevent conflicts with the first type of file\&.
.SS BASIC\ CONTENT\ PAGES    
.TP 0.2i    
\(bu
One class or interface page (classname\&.html) for each class or interface being documented\&.
.TP 0.2i    
\(bu
One package page (package-summary\&.html) for each package being documented\&. The \f3javadoc\fR command includes any HTML text provided in a file with the name package\&.html or package-info\&.java in the package directory of the source tree\&.
.TP 0.2i    
\(bu
One overview page (overview-summary\&.html) for the entire set of packages\&. The overview page is the front page of the generated document\&. The \f3javadoc\fR command includes any HTML text provided in a file specified by the \f3-overview\fR option\&. The Overview page is created only when you pass two or more package names into the \f3javadoc\fR command\&. See HTML Frames and Options\&.
.SS CROSS-REFERENCE\ PAGES    
.TP 0.2i    
\(bu
One class hierarchy page for the entire set of packages (overview-tree\&.html)\&. To view the hierarchy page, click \fIOverview\fR in the navigation bar and click \fITree\fR\&.
.TP 0.2i    
\(bu
One class hierarchy page for each package (package-tree\&.html) To view the hierarchy page, go to a particular package, class, or interface page, and click \fITree\fR to display the hierarchy for that package\&.
.TP 0.2i    
\(bu
One use page for each package (package-use\&.html) and a separate use page for each class and interface (class-use/classname\&.html)\&. The use page describes what packages, classes, methods, constructors and fields use any part of the specified class, interface, or package\&. For example, given a class or interface A, its use page includes subclasses of A, fields declared as A, methods that return A, and methods and constructors with parameters of type A\&. To view the use page, go to the package, class, or interface and click the \fIUse\fR link in the navigation bar\&.
.TP 0.2i    
\(bu
A deprecated API page (deprecated-list\&.html) that lists all deprecated APIs and their suggested replacements\&. Avoid deprecated APIs because they can be removed in future implementations\&.
.TP 0.2i    
\(bu
A constant field values page (constant-values\&.html) for the values of static fields\&.
.TP 0.2i    
\(bu
A serialized form page (serialized-form\&.html) that provides information about serializable and externalizable classes with field and method descriptions\&. The information on this page is of interest to reimplementors, and not to developers who want to use the API\&. To access the serialized form page, go to any serialized class and click \fISerialized Form\fR in the See Also section of the class comment\&. The standard doclet generates a serialized form page that lists any class (public or non-public) that implements Serializable with its \f3readObject\fR and \f3writeObject\fR methods, the fields that are serialized, and the documentation comments from the \f3@serial\fR, \f3@serialField\fR, and \f3@serialData\fR tags\&. Public serializable classes can be excluded by marking them (or their package) with \f3@serial\fR exclude, and package-private serializable classes can be included by marking them (or their package) with an \f3@serial\fR include\&. As of Release 1\&.4, you can generate the complete serialized form for public and private classes by running the \f3javadoc\fR command without specifying the \f3-private\fR option\&. See Options\&.
.TP 0.2i    
\(bu
An index page (\f3index-*\&.html\fR) of all class, interface, constructor, field and method names, in alphabetical order\&. The index page is internationalized for Unicode and can be generated as a single file or as a separate file for each starting character (such as A\(enZ for English)\&.
.SS SUPPORT\ PAGES    
.TP 0.2i    
\(bu
A help page (help-doc\&.html) that describes the navigation bar and the previous pages\&. Use \f3-helpfile\fR to override the default help file with your own custom help file\&.
.TP 0.2i    
\(bu
One index\&.html file that creates the HTML frames for display\&. Load this file to display the front page with frames\&. The index\&.html file contains no text content\&.
.TP 0.2i    
\(bu
Several frame files (\f3*-frame\&.html\fR) that contains lists of packages, classes, and interfaces\&. The frame files display the HTML frames\&.
.TP 0.2i    
\(bu
A package list file (package-list) that is used by the \f3-link\fR and \f3-linkoffline\fR options\&. The package list file is a text file that is not reachable through links\&.
.TP 0.2i    
\(bu
A style sheet file (stylesheet\&.css) that controls a limited amount of color, font family, font size, font style, and positioning information on the generated pages\&.
.TP 0.2i    
\(bu
A doc-files directory that holds image, example, source code, or other files that you want copied to the destination directory\&. These files are not processed by the \f3javadoc\fR command\&. This directory is not processed unless it exists in the source tree\&.
.PP
See Options\&.
.SS HTML\ FRAMES    
The \f3javadoc\fR command generates the minimum number of frames (two or three) necessary based on the values passed to the command\&. It omits the list of packages when you pass a single package name or source files that belong to a single package as an argument to the \f3javadoc\fR command\&. Instead, the \f3javadoc\fR command creates one frame in the left-hand column that displays the list of classes\&. When you pass two or more package names, the \f3javadoc\fR command creates a third frame that lists all packages and an overview page (overview-summary\&.html)\&. To bypass frames, click the \fINo Frames\fR link or enter the page set from the overview-summary\&.html page\&.
.SS GENERATED\ FILE\ STRUCTURE    
The generated class and interface files are organized in the same directory hierarchy that Java source files and class files are organized\&. This structure is one directory per subpackage\&.
.PP
For example, the document generated for the \f3java\&.applet\&.Applet\fR class would be located at java/applet/Applet\&.html\&.
.PP
The file structure for the \f3java\&.applet\fR package follows, assuming that the destination directory is named \f3apidocs\fR\&. All files that contain the word \fIframe\fR appear in the upper-left or lower-left frames, as noted\&. All other HTML files appear in the right-hand frame\&.
.PP
Directories are bold\&. The asterisks (*) indicate the files and directories that are omitted when the arguments to the \f3javadoc\fR command are source file names rather than package names\&. When arguments are source file names, an empty package list is created\&. The doc-files directory is not created in the destination unless it exists in the source tree\&. See Generated Files\&.
.TP 0.2i    
\(bu
\fIapidocs\fR: Top-level directory
.RS     
.TP 0.2i    
\(bu
index\&.html: Initial Page that sets up HTML frames
.TP 0.2i    
\(bu
*overview-summary\&.html: Package list with summaries
.TP 0.2i    
\(bu
overview-tree\&.html: Class hierarchy for all packages
.TP 0.2i    
\(bu
deprecated-list\&.html: Deprecated APIs for all packages
.TP 0.2i    
\(bu
constant-values\&.html: Static field values for all packages
.TP 0.2i    
\(bu
serialized-form\&.html: Serialized forms for all packages
.TP 0.2i    
\(bu
*overview-frame\&.html: All packages for display in upper-left frame
.TP 0.2i    
\(bu
allclasses-frame\&.html: All classes for display in lower-left frame
.TP 0.2i    
\(bu
help-doc\&.html: Help about Javadoc page organization
.TP 0.2i    
\(bu
index-all\&.html: Default index created without \f3-splitindex\fR option
.TP 0.2i    
\(bu
\fIindex-files\fR: Directory created with \f3-splitindex\fR option
.RS     
.TP 0.2i    
\(bu
index-<number>\&.html: Index files created with \f3-splitindex\fR option
.RE     

.TP 0.2i    
\(bu
package-list: Package names for resolving external references
.TP 0.2i    
\(bu
stylesheet\&.css: Defines fonts, colors, positions, and so on
.RE     

.TP 0.2i    
\(bu
\fIjava\fR: Package directory
.RS     
.TP 0.2i    
\(bu
\fIapplet\fR: Subpackage directory
.RS     
.TP 0.2i    
\(bu
Applet\&.html: \f3Applet\fR class page
.TP 0.2i    
\(bu
AppletContext\&.html: \f3AppletContext\fR interface
.TP 0.2i    
\(bu
AppletStub\&.html: \f3AppletStub\fR interface
.TP 0.2i    
\(bu
AudioClip\&.html: \f3AudioClip\fR interface
.TP 0.2i    
\(bu
package-summary\&.html: Classes with summaries
.TP 0.2i    
\(bu
package-frame\&.html: Package classes for display in lower-left frame
.TP 0.2i    
\(bu
package-tree\&.html: Class hierarchy for this package
.TP 0.2i    
\(bu
package-use\&.html: Where this package is used
.TP 0.2i    
\(bu
\fIdoc-files\fR: Image and example files directory
.TP 0.2i    
\(bu
\fIclass-use\fR: Image and examples file location

- Applet\&.html: Uses of the Applet class

- AppletContext\&.html: Uses of the \f3AppletContext\fR interface

- AppletStub\&.html: Uses of the \f3AppletStub\fR interface

- AudioClip\&.html: Uses of the \f3AudioClip\fR interface
.RE     

.RE     

.TP 0.2i    
\(bu
\fIsrc-html\fR: Source code directory
.RS     
.TP 0.2i    
\(bu
\fIjava\fR: Package directory
.RS     
.TP 0.2i    
\(bu
\fIapplet\fR: Subpackage directory

- Applet\&.html: Applet source code

- AppletContext\&.html: \f3AppletContext\fR source code

- AppletStub\&.html: \f3AppletStub\fR source code

- AudioClip\&.html: \f3AudioClip\fR source code
.RE     

.RE     

.SS GENERATED\ API\ DECLARATIONS    
The \f3javadoc\fR command generates a declaration at the start of each class, interface, field, constructor, and method description for that API item\&. For example, the declaration for the \f3Boolean\fR class is:
.sp     
.nf     
\f3public final class Boolean\fP
.fi     
.nf     
\f3extends Object\fP
.fi     
.nf     
\f3implements Serializable\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The declaration for the \f3Boolean\&.valueOf\fR method is:
.sp     
.nf     
\f3public static Boolean valueOf(String s)\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The \f3javadoc\fR command can include the modifiers \f3public\fR, \f3protected\fR, \f3private\fR, \f3abstract\fR, \f3final\fR, \f3static\fR, \f3transient\fR, and \f3volatile\fR, but not \f3synchronized\fR or \f3native\fR\&. The \f3synchronized\fR and \f3native\fR modifiers are considered implementation detail and not part of the API specification\&.
.PP
Rather than relying on the keyword \f3synchronized\fR, APIs should document their concurrency semantics in the main description of the comment\&. For example, a description might be: A single enumeration cannot be used by multiple threads concurrently\&. The document should not describe how to achieve these semantics\&. As another example, while the \f3Hashtable\fR option should be thread-safe, there is no reason to specify that it is achieved by synchronizing all of its exported methods\&. It is better to reserve the right to synchronize internally at the bucket level for higher concurrency\&.
.SH DOCUMENTATION\ COMMENTS    
This section describes source code comments and comment inheritance\&.
.SS SOURCE\ CODE\ COMMENTS    
You can include documentation comments in the source code, ahead of declarations for any class, interface, method, constructor, or field\&. You can also create documentation comments for each package and another one for the overview, though their syntax is slightly different\&. A documentation comment consists of the characters between \f3/**\fR and \f3*/\fR that end it\&. Leading asterisks are allowed on each line and are described further in the following section\&. The text in a comment can continue onto multiple lines\&.
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * This is the typical format of a simple documentation comment\fP
.fi     
.nf     
\f3 * that spans two lines\&.\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
To save space you can put a comment on one line:
.sp     
.nf     
\f3/** This comment takes up only one line\&. */\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.PP
Placement of Comments

Documentation comments are recognized only when placed immediately before class, interface, constructor, method, or field declarations\&. Documentation comments placed in the body of a method are ignored\&. The \f3javadoc\fR command recognizes only one documentation comment per declaration statement\&. See Where Tags Can Be Used\&.
.PP
A common mistake is to put an \f3import\fR statement between the class comment and the class declaration\&. Do not put an \f3import\fR statement at this location because the \f3javadoc\fR command ignores the class comment\&.
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * This is the class comment for the class Whatever\&.\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3import com\&.example;   // MISTAKE \- Important not to put import statement here\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3public class Whatever{ }\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.PP
Parts of Comments

A documentation comment has a main description followed by a tag section\&. The main description begins after the starting delimiter \f3/**\fR and continues until the tag section\&. The tag section starts with the first block tag, which is defined by the first \f3@\fR character that begins a line (ignoring leading asterisks, white space, and leading separator \f3/**\fR)\&. It is possible to have a comment with only a tag section and no main description\&. The main description cannot continue after the tag section begins\&. The argument to a tag can span multiple lines\&. There can be any number of tags, and some types of tags can be repeated while others cannot\&. For example, this \f3@see\fR tag starts the tag section:
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * This sentence holds the main description for this documentation comment\&.\fP
.fi     
.nf     
\f3 * @see java\&.lang\&.Object\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.PP
Block and inline Tags

A tag is a special keyword within a documentation comment that the \f3javadoc\fR command processes\&. There are two kinds of tags: block tags, which appear as an \f3@tag\fR tag (also known as standalone tags), and inline tags, which appear within braces, as an \f3{@tag}\fR tag\&. To be interpreted, a block tag must appear at the beginning of a line, ignoring leading asterisks, white space, and the separator (\f3/**\fR)\&. This means you can use the \f3@\fR character elsewhere in the text and it will not be interpreted as the start of a tag\&. If you want to start a line with the \f3@\fR character and not have it be interpreted, then use the HTML entity \f3&#064;\fR\&. Each block tag has associated text, which includes any text following the tag up to, but not including, either the next tag, or the end of the documentation comment\&. This associated text can span multiple lines\&. An inline tag is allowed and interpreted anywhere that text is allowed\&. The following example contains the \f3@deprecated\fR block tag and the \f3{@link}\fR inline tag\&. See Javadoc Tags\&.
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * @deprecated  As of JDK 1\&.1, replaced by {@link #setBounds(int,int,int,int)}\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.PP
Write Comments in HTML

The text must be written in HTML with HTML entities and HTML tags\&. You can use whichever version of HTML your browser supports\&. The standard doclet generates HTML 3\&.2-compliant code elsewhere (outside of the documentation comments) with the inclusion of cascading style sheets and frames\&. HTML 4\&.0 is preferred for generated files because of the frame sets\&.
.PP
For example, entities for the less than symbol (<) and the greater than symbol (>) should be written as \f3&lt;\fR and \f3&gt;\fR\&. Similarly, the ampersand (&) should be written as \f3&amp;\fR\&. The bold HTML tag \f3<b>\fR is shown in the following example\&.
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * This is a <b>doc</b> comment\&.\fP
.fi     
.nf     
\f3 * @see java\&.lang\&.Object\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.PP
Leading Asterisks

When the \f3javadoc\fR command parses a documentation comment, leading asterisks (*) on each line are discarded, and blanks and tabs that precede the initial asterisks (*) are also discarded\&. If you omit the leading asterisk on a line, then the leading white space is no longer removed so that you can paste code examples directly into a documentation comment inside a \f3<PRE>\fR tag with its indentation preserved\&. Spaces are interpreted by browsers more uniformly than tabs\&. Indentation is relative to the left margin (rather than the separator \f3/**\fR or \f3<PRE>\fR tag)\&.
.PP
First Sentence

The first sentence of each documentation comment should be a summary sentence that contains a concise but complete description of the declared entity\&. This sentence ends at the first period that is followed by a blank, tab, or line terminator, or at the first block tag\&. The \f3javadoc\fR command copies this first sentence to the member summary at the top of the HTML page\&.
.PP
Multiple-Field Declarations

The Java platform lets you declare multiple fields in a single statement, but this statement can have only one documentation comment that is copied for all fields\&. If you want individual documentation comments for each field, then declare each field in a separate statement\&. For example, the following documentation comment does not make sense written as a single declaration and would be better handled as two declarations:
.sp     
.nf     
\f3/** \fP
.fi     
.nf     
\f3 * The horizontal and vertical distances of point (x,y)\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3public int x, y;      // Avoid this \fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The \f3javadoc\fR command generates the following documentation from the previous code:
.sp     
.nf     
\f3public int x\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The horizontal and vertical distances of point (x, y)\&.
.sp     
.nf     
\f3public int y\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The horizontal and vertical distances of point (x, y)\&.
.PP
Use of Header Tags

When writing documentation comments for members, it is best not to use HTML heading tags such as \f3<H1>\fR and \f3<H2>\fR, because the \f3javadoc\fR command creates an entire structured document, and these structural tags might interfere with the formatting of the generated document\&. However, you can use these headings in class and package comments to provide your own structure\&.
.SS METHOD\ COMMENT\ INHERITANCE    
The \f3javadoc\fR command allows method comment inheritance in classes and interfaces to fill in missing text or to explicitly inherit method comments\&. Constructors, fields, and nested classes do not inherit documentation comments\&.
.PP
\fINote:\fR The source file for an inherited method must be on the path specified by the \f3-sourcepath\fR option for the documentation comment to be available to copy\&. Neither the class nor its package needs to be passed in on the command line\&. This contrasts with Release 1\&.3\&.\fIn\fR and earlier releases, where the class had to be a documented class\&.
.PP
Fill in Missing Text

When a main description, or \f3@return\fR, \f3@param\fR, or \f3@throws\fR tag is missing from a method comment, the \f3javadoc\fR command copies the corresponding main description or tag comment from the method it overrides or implements (if any)\&. See Method Comment Inheritance\&.
.PP
When an \f3@param\fR tag for a particular parameter is missing, the comment for that parameter is copied from the method further up the inheritance hierarchy\&. When an \f3@throws\fR tag for a particular exception is missing, the \f3@throws\fR tag is copied only when that exception is declared\&.
.PP
This behavior contrasts with Release 1\&.3 and earlier, where the presence of any main description or tag would prevent all comments from being inherited\&.
.PP
See Javadoc Tags and Options\&.
.PP
Explicit Inheritance

Insert the \f3{@inheritDoc}\fR inline tag in a method main description or \f3@return\fR, \f3@param\fR, or \f3@throws\fR tag comment\&. The corresponding inherited main description or tag comment is copied into that spot\&.
.SS CLASS\ AND\ INTERFACE\ INHERITANCE    
Comment inheritance occurs in all possible cases of inheritance from classes and interfaces:
.TP 0.2i    
\(bu
When a method in a class overrides a method in a superclass
.TP 0.2i    
\(bu
When a method in an interface overrides a method in a superinterface
.TP 0.2i    
\(bu
When a method in a class implements a method in an interface
.PP
In the first two cases, the \f3javadoc\fR command generates the subheading \fIOverrides\fR in the documentation for the overriding method\&. A link to the method being overridden is included, whether or not the comment is inherited\&.
.PP
In the third case, when a method in a specified class implements a method in an interface, the \f3javadoc\fR command generates the subheading \fISpecified by\fR in the documentation for the overriding method\&. A link to the method being implemented is included, whether or not the comment is inherited\&.
.SS METHOD\ COMMENTS\ ALGORITHM    
If a method does not have a documentation comment, or has an \f3{@inheritDoc}\fR tag, then the \f3javadoc\fR command uses the following algorithm to search for an applicable comment\&. The algorithm is designed to find the most specific applicable documentation comment, and to give preference to interfaces over superclasses:
.TP 0.4i    
1\&.
Look in each directly implemented (or extended) interface in the order they appear following the word \f3implements\fR (or \f3extends\fR) in the method declaration\&. Use the first documentation comment found for this method\&.
.TP 0.4i    
2\&.
If Step 1 failed to find a documentation comment, then recursively apply this entire algorithm to each directly implemented (or extended) interface in the same order they were examined in Step 1\&.
.TP 0.4i    
3\&.
When Step 2 fails to find a documentation comment and this is a class other than the \f3Object\fR class, but not an interface:
.RS     
.TP 0.4i    
1\&.
If the superclass has a documentation comment for this method, then use it\&.
.TP 0.4i    
2\&.
If Step 3a failed to find a documentation comment, then recursively apply this entire algorithm to the superclass\&.
.RE     

.SH JAVADOC\ TAGS    
The \f3javadoc\fR command parses special tags when they are embedded within a Java documentation comment\&. The \f3javadoc\fR tags let you autogenerate a complete, well-formatted API from your source code\&. The tags start with an at sign (\f3@\fR) and are case-sensitive\&. They must be typed with the uppercase and lowercase letters as shown\&. A tag must start at the beginning of a line (after any leading spaces and an optional asterisk), or it is treated as text\&. By convention, tags with the same name are grouped together\&. For example, put all \f3@see\fR tags together\&. For more information, see Where Tags Can Be Used\&.
.PP
Tags have the following types:
.TP 0.2i    
\(bu
Bock tags: Place block tags only in the tag section that follows the description\&. Block tags have the form: \fI@tag\fR\&.
.TP 0.2i    
\(bu
Inline tags: Place inline tags anywhere in the main description or in the comments for block tags\&. Inline tags are enclosed within braces: \fI{@tag}\fR\&.
.PP
For custom tags, see -tag tagname:Xaoptcmf:"taghead"\&. See also Where Tags Can Be Used\&.
.SS TAG\ DESCRIPTIONS    
.TP     
<AUTHOR>
Introduced in JDK 1\&.0

Adds an Author entry with the specified name text to the generated documents when the \f3-author\fR option is used\&. A documentation comment can contain multiple \f3@author\fR tags\&. You can specify one name per \f3@author\fR tag or multiple names per tag\&. In the former case, the \f3javadoc\fR command inserts a comma (,) and space between names\&. In the latter case, the entire text is copied to the generated document without being parsed\&. Therefore, you can use multiple names per line if you want a localized name separator other than a comma\&. See <AUTHOR> How to Write Doc Comments for the Javadoc Tool at http://www\&.oracle\&.com/technetwork/java/javase/documentation/index-137868\&.html#<AUTHOR>     
{@code \fItext\fR}
Introduced in JDK 1\&.5

Equivalent to \f3<code>{@literal}</code>\fR\&.

Displays text in code font without interpreting the text as HTML markup or nested Javadoc tags\&. This enables you to use regular angle brackets (< and >) instead of the HTML entities (\f3&lt;\fR and \f3&gt;\fR) in documentation comments, such as in parameter types (\f3<Object>\fR), inequalities (\f33 < 4\fR), or arrows (\f3<-\fR)\&. For example, the documentation comment text \f3{@code A<B>C}\fR displayed in the generated HTML page unchanged as \f3A<B>C\fR\&. This means that the \f3<B>\fR is not interpreted as bold and is in code font\&. If you want the same functionality without the code font, then use the \f3{@literal}\fR tag\&.
.TP     
@deprecated \fIdeprecated-text\fR
Introduced in JDK 1\&.0

Adds a comment indicating that this API should no longer be used (even though it may continue to work)\&. The \f3javadoc\fR command moves \f3deprecated-text\fRahead of the main description, placing it in italics and preceding it with a bold warning: Deprecated\&. This tag is valid in all documentation comments: overview, package, class, interface, constructor, method and field\&.

The first sentence of deprecated text should tell the user when the API was deprecated and what to use as a replacement\&. The \f3javadoc\fR command copies the first sentence to the summary section and index\&. Subsequent sentences can also explain why it was deprecated\&. You should include an \f3{@link}\fR tag (for Javadoc 1\&.2 or later) that points to the replacement API\&.

Use the \fI@deprecated annotation\fR tag to deprecate a program element\&. See How and When to Deprecate APIs at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javadoc/deprecation/deprecation\&.html

See also @deprecated in How to Write Doc Comments for the Javadoc Tool at http://www\&.oracle\&.com/technetwork/java/javase/documentation/index-137868\&.html#@deprecated
.TP     
{@docRoot}
Introduced in JDK 1\&.3

Represents the relative path to the generated document\&'s (destination) root directory from any generated page\&. This tag is useful when you want to include a file, such as a copyright page or company logo, that you want to reference from all generated pages\&. Linking to the copyright page from the bottom of each page is common\&.

This \f3{@docRoot}\fR tag can be used both on the command line and in a documentation comment\&. This tag is valid in all documentation comments: overview, package, class, interface, constructor, method and field, and includes the text portion of any tag (such as the \f3@return\fR, \f3@param\fR and \f3@deprecated\fR tags)\&.
.RS     
.TP 0.2i    
\(bu
On the command line, where the header, footer, or bottom are defined: \f3javadoc -bottom \&'<a href="{@docRoot}/copyright\&.html">Copyright</a>\&'\fR\&.

When you use the \f3{@docRoot}\fR tag this way in a make file, some \f3makefile\fR programs require a special way to escape for the brace \f3{}\fR characters\&. For example, the Inprise MAKE version 5\&.2 running on Windows requires double braces: \f3{{@docRoot}}\fR\&. It also requires double (rather than single) quotation marks to enclose arguments to options such as the \f3-bottom\fR option (with the quotation marks around the \f3href\fR argument omitted)\&.
.TP 0.2i    
\(bu
In a documentation comment:
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * See the <a href="{@docRoot}/copyright\&.html">Copyright</a>\&.\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


This tag is needed because the generated documents are in hierarchical directories, as deep as the number of subpackages\&. The expression: \f3<a href="{@docRoot}/copyright\&.html">\fR resolves to \f3<a href="\&.\&./\&.\&./copyright\&.html">\fR for \f3java/lang/Object\&.java\fR and \f3<a href="\&.\&./\&.\&./\&.\&./copyright\&.html">\fR for \f3java/lang/ref/Reference\&.java\fR\&.
.RE     

.TP     
@exception \fIclass-name description\fR
Introduced in JDK 1\&.0

Identical to the \f3@throws\fR tag\&. See @throws class-name description\&.
.TP     
{@inheritDoc}
Introduced in JDK 1\&.4

Inherits (copies) documentation from the nearest inheritable class or implementable interface into the current documentation comment at this tag\&'s location\&. This enables you to write more general comments higher up the inheritance tree and to write around the copied text\&.

This tag is valid only in these places in a documentation comment:
.RS     
.TP 0.2i    
\(bu
In the main description block of a method\&. In this case, the main description is copied from a class or interface up the hierarchy\&.
.TP 0.2i    
\(bu
In the text arguments of the \f3@return\fR, \f3@param,\fR and \f3@throws\fR tags of a method\&. In this case, the tag text is copied from the corresponding tag up the hierarchy\&.
.RE     


See Method Comment Inheritance for a description of how comments are found in the inheritance hierarchy\&. Note that if this tag is missing, then the comment is or is not automatically inherited according to rules described in that section\&.
.TP     
{@link \fIpackage\&.class#member label\fR}
Introduced in JDK 1\&.2

Inserts an inline link with a visible text label that points to the documentation for the specified package, class, or member name of a referenced class\&. This tag is valid in all documentation comments: overview, package, class, interface, constructor, method and field, including the text portion of any tag, such as the \f3@return\fR, \f3@param\fR and \f3@deprecated\fR tags\&. See @link in How to Write Doc Comments for the Javadoc Tool at http://www\&.oracle\&.com/technetwork/java/javase/documentation/index-137868\&.html#{@link

This tag is similar to the \f3@see\fR tag\&. Both tags require the same references and accept the same syntax for \f3package\&.class#member\fR and \f3label\fR\&. The main difference is that the \f3{@link}\fR tag generates an inline link rather than placing the link in the See Also section\&. The \f3{@link}\fR tag begins and ends with braces to separate it from the rest of the inline text\&. If you need to use the right brace (\f3}\fR) inside the label, then use the HTML entity notation \f3&#125;\fR\&.

There is no limit to the number of \f3{@link}\fR tags allowed in a sentence\&. You can use this tag in the main description part of any documentation comment or in the text portion of any tag, such as the \f3@deprecated\fR, \f3@return\fR or \f3@param\fR tags\&.

For example, here is a comment that refers to the \f3getComponentAt(int, int)\fR method:
.sp     
.nf     
\f3Use the {@link #getComponentAt(int, int) getComponentAt} method\&.\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


From this code, the standard doclet generates the following HTML (assuming it refers to another class in the same package):
.sp     
.nf     
\f3Use the <a href="Component\&.html#getComponentAt(int, int)">getComponentAt</a> method\&.\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The previous line appears on the web page as:
.sp     
.nf     
\f3Use the getComponentAt method\&.\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP     
{@linkplain \fIpackage\&.class#member label\fR}
Introduced in JDK 1\&.4

Behaves the same as the \f3{@link}\fR tag, except the link label is displayed in plain text rather than code font\&. Useful when the label is plain text\&. For example, \f3Refer to {@linkplain add() the overridden method}\fR\&. displays as: Refer to the overridden method\&.
.TP     
{@literal \fItext\fR}
Introduced in JDK 1\&.5

Displays text without interpreting the text as HTML markup or nested Javadoc tags\&. This enables you to use angle brackets (\f3< and >\fR) instead of the HTML entities (\f3&lt;\fR and \f3&gt;\fR) in documentation comments, such as in parameter types (\f3<Object>\fR), inequalities (\f33 < 4\fR), or arrows (<-)\&. For example, the documentation comment text \f3{@literal A<B>C}\fR displays unchanged in the generated HTML page in your browser, as \f3A<B>C\fR\&. The \f3<B>\fR is not interpreted as bold (and it is not in code font)\&. If you want the same functionality with the text in code font, then use the \f3{@code}\fR tag\&.
.TP     
@param \fIparameter-name description\fR
Introduced in JDK 1\&.0

Adds a parameter with the specified \f3parameter-name\fR followed by the specified description to the Parameters section\&. When writing the documentation comment, you can continue the description onto multiple lines\&. This tag is valid only in a documentation comment for a method, constructor, or class\&. See @param in How to Write Doc Comments for the Javadoc Tool at http://www\&.oracle\&.com/technetwork/java/javase/documentation/index-137868\&.html#@param

The \f3parameter-name\fR can be the name of a parameter in a method or constructor, or the name of a type parameter of a class, method, or constructor\&. Use angle brackets around this parameter name to specify the use of a type parameter\&.

Example of a type parameter of a class:
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * @param <E> Type of element stored in a list\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3public interface List<E> extends Collection<E> {\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


Example of a type parameter of a method:
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * @param string  the string to be converted\fP
.fi     
.nf     
\f3 * @param type    the type to convert the string to\fP
.fi     
.nf     
\f3 * @param <T>     the type of the element\fP
.fi     
.nf     
\f3 * @param <V>     the value of the element\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3<T, V extends T> V convert(String string, Class<T> type) {\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP     
@return \fIdescription\fR
Introduced in JDK 1\&.0

Adds a Returns section with the description text\&. This text should describe the return type and permissible range of values\&. This tag is valid only in a documentation comment for a method\&. See @return in How to Write Doc Comments for the Javadoc Tool at http://www\&.oracle\&.com/technetwork/java/javase/documentation/index-137868\&.html#@return
.TP     
@see \fIreference\fR
Introduced in JDK 1\&.0

Adds a \fISee Also\fR heading with a link or text entry that points to a reference\&. A documentation comment can contain any number of \f3@see\fR tags, which are all grouped under the same heading\&. The \f3@see\fR tag has three variations\&. The form is the most common\&. This tag is valid in any documentation comment: overview, package, class, interface, constructor, method, or field\&. For inserting an inline link within a sentence to a package, class, or member, see \f3{@link}\fR\&.

\fIForm 1\fR\&. The @see \f3string\fR tag form adds a text entry for \fIstring\fR\&. No link is generated\&. The string is a book or other reference to information not available by URL\&. The \f3javadoc\fR command distinguishes this from the previous cases by searching for a double quotation mark (") as the first character\&. For example, \f3@see "The Java Programming Language"\fR that generates the following text:

\fISee Also\fR:

"The Java Programming Language"

\fIForm 2\fR\&. The \f3@see <a href="URL#value">label</a>\fR form adds a link as defined by \f3URL#value\fR\&. The \f3URL#value\fR parameter is a relative or absolute URL\&. The \f3javadoc\fR command distinguishes this from other cases by searching for a less-than symbol (\f3<\fR) as the first character\&. For example, \f3@see <a href="spec\&.html#section">Java Spec</a>\fR generates the following link:

\fISee Also\fR:

Java Spec

\fIForm 3\fR\&. The \f3@see package\&.class#member label\fR form adds a link with a visible text label that points to the documentation for the specified name in the Java Language that is referenced\&. The label is optional\&. If the label is omitted, then the name appears instead as visible text, suitably shortened\&. Use the \f3-noqualifier\fR option to globally remove the package name from this visible text\&. Use the label when you want the visible text to be different from the autogenerated visible text\&. See How a Name Appears\&.

In Java SE 1\&.2 only, the name but not the label automatically appears in \f3<code>\fR HTML tags\&. Starting with Java SE 1\&.2\&.2, the \f3<code>\fR tag is always included around the visible text, whether or not a label is used\&.
.RS     
.TP 0.2i    
\(bu
\f3package\&.class#member\fR is any valid program element name that is referenced, such as a package, class, interface, constructor, method or field name, except that the character ahead of the member name should be a number sign (\f3#\fR)\&. The class represents any top-level or nested class or interface\&. The member represents any constructor, method, or field (not a nested class or interface)\&. If this name is in the documented classes, then the \f3javadoc\fR command create a link to it\&. To create links to external referenced classes, use the \f3-link\fR option\&. Use either of the other two \f3@see\fR tag forms to refer to the documentation of a name that does not belong to a referenced class\&. See Specify a Name\&.

\fINote:\fR External referenced classes are classes that are not passed into the \f3javadoc\fR command on the command line\&. Links in the generated documentation to external referenced classes are called external references or external links\&. For example, if you run the \f3javadoc\fR command on only the \f3java\&.awt package\fR, then any class in \f3java\&.lang\fR, such as \f3Object\fR, is an external referenced class\&. Use the \f3-link\fR and \f3-linkoffline\fR options to link to external referenced classes\&. The source comments of external referenced classes are not available to the \f3javadoc\fR command run\&.
.TP 0.2i    
\(bu
\f3label\fR is optional text that is visible as the link label\&. The label can contain white space\&. If \f3label\fR is omitted, then \f3package\&.class\&.member\fR appears, suitably shortened relative to the current class and package\&. See How a Name Appears\&.
.TP 0.2i    
\(bu
A space is the delimiter between \f3package\&.class#member\fR and \f3label\fR\&. A space inside parentheses does not indicate the start of a label, so spaces can be used between parameters in a method\&.
.RE     


\fI\fRIn the following example, an \f3@see\fR tag (in the \f3Character\fR class) refers to the equals method in the \f3String\fR class\&. The tag includes both arguments: the name \f3String#equals(Object)\fR and the label \f3equals\fR\&.
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * @see String#equals(Object) equals\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The standard doclet produces HTML that is similar to:
.sp     
.nf     
\f3<dl>\fP
.fi     
.nf     
\f3<dt><b>See Also:</b>\fP
.fi     
.nf     
\f3<dd><a href="\&.\&./\&.\&./java/lang/String#equals(java\&.lang\&.Object)"><code>equals<code></a>\fP
.fi     
.nf     
\f3</dl>\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The previous code looks similar to the following in a browser, where the label is the visible link text:

\fISee Also\fR:

equals
.PP
Specify a Name

\fI\fRThis \f3package\&.class#member\fR name can be either fully qualified, such as \f3java\&.lang\&.String#toUpperCase()\fR or not, such as \f3String#toUpperCase()\fR or \f3#toUpperCase()\fR\&. If the name is less than fully qualified, then the \f3javadoc\fR command uses the standard Java compiler search order to find it\&. See Search Order for the @see Tag\&. The name can contain white space within parentheses, such as between method arguments\&.The advantage to providing shorter, partially qualified names is that they are shorter to type and there is less clutter in the source code\&. The following listing shows the different forms of the name, where \f3Class\fR can be a class or interface; Type can be a class, interface, array, or primitive; and method can be a method or constructor\&.
.sp     
.nf     
\f3\fITypical forms for\fR\fI @see package\&.class#member\fR\fP
.fi     
.nf     
\f3\fIReferencing a member of the current class\fR\fP
.fi     
.nf     
\f3@see #field\fP
.fi     
.nf     
\f3@see #method(Type, Type,\&.\&.\&.)\fP
.fi     
.nf     
\f3@see #method(Type argname, Type argname,\&.\&.\&.)\fP
.fi     
.nf     
\f3@see #constructor(Type, Type,\&.\&.\&.)\fP
.fi     
.nf     
\f3@see #constructor(Type argname, Type argname,\&.\&.\&.) \fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3\fIReferencing another class in the current or imported packages\fR\fP
.fi     
.nf     
\f3@see Class#field\fP
.fi     
.nf     
\f3@see Class#method(Type, Type,\&.\&.\&.)\fP
.fi     
.nf     
\f3@see Class#method(Type argname, Type argname,\&.\&.\&.)\fP
.fi     
.nf     
\f3@see Class#constructor(Type, Type,\&.\&.\&.)\fP
.fi     
.nf     
\f3@see Class#constructor(Type argname, Type argname,\&.\&.\&.)\fP
.fi     
.nf     
\f3@see Class\&.NestedClass\fP
.fi     
.nf     
\f3@see Class \fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3\fIReferencing an element in another package (fully qualified)\fR\fP
.fi     
.nf     
\f3@see package\&.Class#field\fP
.fi     
.nf     
\f3@see package\&.Class#method(Type, Type,\&.\&.\&.)\fP
.fi     
.nf     
\f3@see package\&.Class#method(Type argname, Type argname,\&.\&.\&.)\fP
.fi     
.nf     
\f3@see package\&.Class#constructor(Type, Type,\&.\&.\&.)\fP
.fi     
.nf     
\f3@see package\&.Class#constructor(Type argname, Type argname,\&.\&.\&.)\fP
.fi     
.nf     
\f3@see package\&.Class\&.NestedClass\fP
.fi     
.nf     
\f3@see package\&.Class\fP
.fi     
.nf     
\f3@see package\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3\fRNotes about the previous listing:
.TP 0.2i    
\(bu
The first set of forms with no class or package causes the \f3javadoc\fR command to search only through the current class hierarchy\&. It finds a member of the current class or interface, one of its superclasses or superinterfaces, or one of its enclosing classes or interfaces (search Items 1\(en3)\&. It does not search the rest of the current package or other packages (search Items 4\(en5)\&. See Search Order for the @see Tag\&.
.TP 0.2i    
\(bu
If any method or constructor is entered as a name with no parentheses, such as \f3getValue\fR, and if there is no field with the same name, then the \f3javadoc\fR command still creates a link to the method\&. If this method is overloaded, then the \f3javadoc\fR command links to the first method its search encounters, which is unspecified\&.
.TP 0.2i    
\(bu
Nested classes must be specified as \f3outer\&.inner\fR, not simply \f3inner\fR, for all forms\&.
.TP 0.2i    
\(bu
As stated, the number sign (\f3#\fR), rather than a dot (\f3\&.\fR) separates a member from its class\&. This enables the \f3javadoc\fR command to resolve ambiguities, because the dot also separates classes, nested classes, packages, and subpackages\&. However, the \f3javadoc\fR command properly parses a dot when there is no ambiguity, but prints a warning to alert you\&.
.PP
Search Order for the @see Tag

\fI\fRThe \f3javadoc\fR command processes an \f3@see\fR tag that appears in a source file, package file, or overview file\&. In the latter two files, you must fully qualify the name you supply with the \f3@see\fR tag\&. In a source file, you can specify a name that is fully qualified or partially qualified\&.
.PP
The following is the search order for the \f3@see\fR tag\&.
.TP 0.4i    
1\&.
The current class or interface\&.
.TP 0.4i    
2\&.
Any enclosing classes and interfaces searching the closest first\&.
.TP 0.4i    
3\&.
Any superclasses and superinterfaces, searching the closest first\&.
.TP 0.4i    
4\&.
The current package\&.
.TP 0.4i    
5\&.
Any imported packages, classes, and interfaces, searching in the order of the \f3import\fR statement\&.
.PP
The \f3javadoc\fR command continues to search recursively through Items 1-3 for each class it encounters until it finds a match\&. That is, after it searches through the current class and its enclosing class E, it searches through the superclasses of E before the enclosing classes of E\&. In Items 4 and 5, the \f3javadoc\fR command does not search classes or interfaces within a package in any specified order (that order depends on the particular compiler)\&. In Item 5, the \f3javadoc\fR command searches in \fIjava\&.lang\fR because that is imported by all programs\&.
.PP
When the \f3javadoc\fR command encounters an \f3@see\fR tag in a source file that is not fully qualified, it searches for the specified name in the same order as the Java compiler would, except the \f3javadoc\fR command does not detect certain name space ambiguities because it assumes the source code is free of these errors\&. This search order is formally defined in the Java Language Specification\&. The \f3javadoc\fR command searches for that name through all related and imported classes and packages\&. In particular, it searches in this order:
.TP 0.4i    
1\&.
The current class or interface\&.
.TP 0.4i    
2\&.
Any enclosing classes and interfaces, searching the closest first\&.
.TP 0.4i    
3\&.
Any superclasses and superinterfaces, searching the closest first\&.
.TP 0.4i    
4\&.
The current package\&.
.TP 0.4i    
5\&.
Any imported packages, classes, and interfaces, searching in the order of the \f3import\fR statements\&.
.PP
The \f3javadoc\fR command does not necessarily look in subclasses, nor will it look in other packages even when their documentation is being generated in the same run\&. For example, if the \f3@see\fR tag is in the \f3java\&.awt\&.event\&.KeyEvent\fR class and refers to a name in the \f3java\&.awt package\fR, then the \f3javadoc\fR command does not look in that package unless that class imports it\&.
.PP
How a Name Appears

\fI\fRIf \f3label\fR is omitted, then \f3package\&.class\&.member\fR appears\&. In general, it is suitably shortened relative to the current class and package\&. Shortened means the \f3javadoc\fR command displays only the minimum name necessary\&. For example, if the \f3String\&.toUpperCase()\fR method contains references to a member of the same class and to a member of a different class, then the class name is displayed only in the latter case, as shown in the following listing\&. Use the \f3-noqualifier\fR option to globally remove the package names\&.
.PP
\fIType of reference\fR: The \f3@see\fR tag refers to a member of the same class, same package
.br     
\fIExample in\fR: \f3@see String#toLowerCase()\fR
.br     
\fIAppears as\fR: \f3toLowerCase()\fR - omits the package and class names
.br     

.PP
\fIType of reference\fR: The \f3@see\fR tag refers to a member of a different class, same package
.br     
\fIExample in\fR: \f3@see Character#toLowerCase(char)\fR
.br     
\fIAppears as\fR: \f3Character\&.toLowerCase(char)\fR - omits the package name, includes the class name
.br     

.PP
\fIType of reference\fR: The \f3@see\fR tag refers to a member of a different class, different package
.br     
\fIExample in\fR: \f3@see java\&.io\&.File#exists()\fR
.br     
\fIAppears as\fR: \f3java\&.io\&.File\&.exists()\fR - includes the package and class names
.br     

.PP
Examples of the @see Tag

The comment to the right shows how the name appears when the \f3@see\fR tag is in a class in another package, such as \f3java\&.applet\&.Applet\fR\&. See @see in How to Write Doc Comments for the Javadoc Tool at http://www\&.oracle\&.com/technetwork/java/javase/documentation/index-137868\&.html#@see
.sp     
.nf     
\f3                                            See also:\fP
.fi     
.nf     
\f3@see java\&.lang\&.String                   //  String                           \fP
.fi     
.nf     
\f3@see java\&.lang\&.String The String class  //  The String class                 \fP
.fi     
.nf     
\f3@see String                             //  String                           \fP
.fi     
.nf     
\f3@see String#equals(Object)              //  String\&.equals(Object)            \fP
.fi     
.nf     
\f3@see String#equals                      //  String\&.equals(java\&.lang\&.Object)   \fP
.fi     
.nf     
\f3@see java\&.lang\&.Object#wait(long)        //  java\&.lang\&.Object\&.wait(long)      \fP
.fi     
.nf     
\f3@see Character#MAX_RADIX                //  Character\&.MAX_RADIX              \fP
.fi     
.nf     
\f3@see <a href="spec\&.html">Java Spec</a>  //  Java Spec            \fP
.fi     
.nf     
\f3@see "The Java Programming Language"    //  "The Java Programming Language" \fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\fINote:\fR You can extend the \f3@se\fR\f3e\fR tag to link to classes not being documented with the \f3-link\fR option\&.
.TP     
@serial \fIfield-description\fR | include | exclude
Introduced in JDK 1\&.2

Used in the documentation comment for a default serializable field\&. See Documenting Serializable Fields and Data for a Class at http://docs\&.oracle\&.com/javase/8/docs/platform/serialization/spec/serial-arch\&.html#5251

See also Oracle\(cqs Criteria for Including Classes in the Serialized Form Specification at http://www\&.oracle\&.com/technetwork/java/javase/documentation/serialized-criteria-137781\&.html

An optional \f3field-description\fR should explain the meaning of the field and list the acceptable values\&. When needed, the description can span multiple lines\&. The standard doclet adds this information to the serialized form page\&. See Cross-Reference Pages\&.

If a serializable field was added to a class after the class was made serializable, then a statement should be added to its main description to identify at which version it was added\&.

The \f3include\fR and \f3exclude\fR arguments identify whether a class or package should be included or excluded from the serialized form page\&. They work as follows:
.RS     
.TP 0.2i    
\(bu
A public or protected class that implements \f3Serializable\fR is included unless that class (or its package) is marked with the \f3@serial exclude\fR tag\&.
.TP 0.2i    
\(bu
A private or package-private class that implements \f3Serializable\fR is excluded unless that class (or its package) is marked with the \f3@serial include\fR tag\&.
.RE     


For example, the \f3javax\&.swing\fR package is marked with the \f3@serial\fR\f3exclude\fR tag in package\&.html or package-info\&.java\&. The public class \f3java\&.security\&.BasicPermission\fR is marked with the \f3@serial exclude\fR tag\&. The package-private class \f3java\&.util\&.PropertyPermissionCollection\fR is marked with the \f3@serial include\fR tag\&.

The \f3@serial\fR tag at the class level overrides the \f3@serial\fR tag at the package level\&.
.TP     
@serialData \fIdata-description\fR
Introduced in JDK 1\&.2

Uses the data description value to document the types and order of data in the serialized form\&. This data includes the optional data written by the \f3writeObject\fR method and all data (including base classes) written by the \f3Externalizable\&.writeExternal\fR method\&.

The \f3@serialData\fR tag can be used in the documentation comment for the \f3writeObject\fR, \f3readObject\fR, \f3writeExternal\fR, \f3readExternal\fR, \f3writeReplace\fR, and \f3readResolve\fR methods\&.
.TP     
@serialField \fIfield-name\fR\fIfield-type\fR\fIfield-description\fR
Introduced in JDK 1\&.2

Documents an \f3ObjectStreamField\fR component of the \f3serialPersistentFields\fR member of a \f3Serializable\fR class\&. Use one \f3@serialField\fR tag for each \f3ObjectStreamField\fR component\&.
.TP     
@since \fIsince-text\fR
Introduced in JDK 1\&.1

Adds a \fISince\fR heading with the specified \f3since-text\fR value to the generated documentation\&. The text has no special internal structure\&. This tag is valid in any documentation comment: overview, package, class, interface, constructor, method, or field\&. This tag means that this change or feature has existed since the software release specified by the \f3since-text\fR value, for example: \f3@since 1\&.5\fR\&.

For Java platform source code, the \f3@since\fR tag indicates the version of the Java platform API specification, which is not necessarily when the source code was added to the reference implementation\&. Multiple \f3@since\fR tags are allowed and are treated like multiple \f3@author\fR tags\&. You could use multiple tags when the program element is used by more than one API\&.
.TP     
@throws \fIclass-name\fR\fIdescription\fR
Introduced in JDK 1\&.2

Behaves the same as the \f3@exception\fR tag\&. See @throws in How to Write Doc Comments for the Javadoc Tool at http://www\&.oracle\&.com/technetwork/java/javase/documentation/index-137868\&.html#@exception

The \f3@throws\fR tag adds a \fIThrows\fR subheading to the generated documentation, with the \f3class-name\fR and \f3description\fR text\&. The \fIclass-name\fR is the name of the exception that might be thrown by the method\&. This tag is valid only in the documentation comment for a method or constructor\&. If this class is not fully specified, then the \f3javadoc\fR command uses the search order to look up this class\&. Multiple \f3@throws\fR tags can be used in a specified documentation comment for the same or different exceptions\&. See Search Order for the @see Tag\&.

To ensure that all checked exceptions are documented, when an \f3@throws\fR tag does not exist for an exception in the throws clause, the \f3javadoc\fR command adds that exception to the HTML output (with no description) as though it were documented with the \f3@throws\fR tag\&.

The \f3@throws\fR documentation is copied from an overridden method to a subclass only when the exception is explicitly declared in the overridden method\&. The same is true for copying from an interface method to an implementing method\&. You can use the \f3{@inheritDoc}\fR tag to force the \f3@throws\fR tag to inherit documentation\&.
.TP     
{@value \fIpackage\&.class#field\fR}
Introduced in JDK 1\&.4

Displays constant values\&. When the \f3{@value}\fR tag is used without an argument in the documentation comment of a static field, it displays the value of that constant:
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * The value of this constant is {@value}\&.\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3public static final String SCRIPT_START = "<script>"\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


When used with the argument \f3package\&.class#field\fR in any documentation comment, he \f3{@value}\fR tag displays the value of the specified constant:
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * Evaluates the script starting with {@value #SCRIPT_START}\&.\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3public String evalScript(String script) {}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The argument \f3package\&.class#field\fR takes a form similar to that of the \f3@see\fR tag argument, except that the member must be a static field\&.

The values of these constants are also displayed in Constant Field Values at http://docs\&.oracle\&.com/javase/8/docs/api/constant-values\&.html
.TP     
@version \fIversion-text\fR
Introduced in JDK 1\&.0

Adds a \fIVersion\fR subheading with the specified \f3version-text\fR value to the generated documents when the \f3-version\fR option is used\&. This tag is intended to hold the current release number of the software that this code is part of, as opposed to the\f3@since\fR tag, which holds the release number where this code was introduced\&. The \f3version-text\fR value has no special internal structure\&. See @version in How to Write Doc Comments for the Javadoc Tool at http://www\&.oracle\&.com/technetwork/java/javase/documentation/index-137868\&.html#@version

A documentation comment can contain multiple \f3@version\fR tags\&. When it makes sense, you can specify one release number per \f3@version\fR tag or multiple release numbers per tag\&. In the former case, the \f3javadoc\fR command inserts a comma (,) and a space between the names\&. In the latter case, the entire text is copied to the generated document without being parsed\&. Therefore, you can use multiple names per line when you want a localized name separator other than a comma\&.
.SH WHERE\ TAGS\ CAN\ BE\ USED    
The following sections describe where tags can be used\&. Note that the following tags can be used in all documentation comments: \f3@see\fR, \f3@since\fR, \f3@deprecated\fR, \f3{@link}\fR, \f3{@linkplain}\fR, and \f3{@docroot}\fR\&.
.SS OVERVIEW\ TAGS    
Overview tags are tags that can appear in the documentation comment for the overview page (which resides in the source file typically named overview\&.html)\&. Similar to any other documentation comments, these tags must appear after the main description
.PP
\fINote:\fR The \f3{@link}\fR tag has a bug in overview documents in Java SE 1\&.2\&. The text appears correctly but has no link\&. The \f3{@docRoot}\fR tag does not currently work in overview documents\&.
.PP
The overview tags are the following:
.PP
@see reference || @since since-text || @serialField field-name field-type field-description || <AUTHOR> || @version version-text || {@link package\&.class#member label} || {@linkplain package\&.class#member label} || {@docRoot} ||
.SS PACKAGE\ TAGS    
Package tags are tags that can appear in the documentation comment for a package, that resides in the source file named package\&.html or package-info\&.java\&. The \f3@serial\fR tag can only be used here with the \f3include\fR or \f3exclude\fR argument\&.
.PP
The package tags are the following:
.PP
@see reference || @since since-text || @serial field-description | include | exclude || <AUTHOR> || @version version-text || {@linkplain package\&.class#member label} || {@linkplain package\&.class#member label} || {@docRoot} ||
.SS CLASS\ AND\ INTERFACE\ TAGS    
The following are tags that can appear in the documentation comment for a class or interface\&. The \f3@serial\fR tag can only be used within the documentation for a class or interface with an \f3include\fR or \f3exclude\fR argument\&.
.PP
@see reference || @since since-text || @deprecated deprecated-text || @serial field-description | include | exclude || <AUTHOR> || @version version-text || {@link package\&.class#member label} || {@linkplain package\&.class#member label} || {@docRoot} ||
.PP
Class comment example:
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * A class representing a window on the screen\&.\fP
.fi     
.nf     
\f3 * For example:\fP
.fi     
.nf     
\f3 * <pre>\fP
.fi     
.nf     
\f3 *    Window win = new Window(parent);\fP
.fi     
.nf     
\f3 *    win\&.show();\fP
.fi     
.nf     
\f3 * </pre>\fP
.fi     
.nf     
\f3 *\fP
.fi     
.nf     
\f3 * <AUTHOR> Shaio\fP
.fi     
.nf     
\f3 * @version 1\&.13, 06/08/06\fP
.fi     
.nf     
\f3 * @see     java\&.awt\&.BaseWindow\fP
.fi     
.nf     
\f3 * @see     java\&.awt\&.Button\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3class Window extends BaseWindow {\fP
.fi     
.nf     
\f3   \&.\&.\&.\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS FIELD\ TAGS    
These tags can appear in fields:
.PP
@see reference || @since since-text || @deprecated deprecated-text || @serial field-description | include | exclude || @serialField field-name field-type field-description || {@link package\&.class#member label} || {@linkplain package\&.class#member label} || {@docRoot} || {@value package\&.class#field}
.PP
Field comment example:
.sp     
.nf     
\f3    /**\fP
.fi     
.nf     
\f3     * The X\-coordinate of the component\&.\fP
.fi     
.nf     
\f3     *\fP
.fi     
.nf     
\f3     * @see #getLocation()\fP
.fi     
.nf     
\f3     */\fP
.fi     
.nf     
\f3    int x = 1263732;\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS CONSTRUCTOR\ AND\ METHOD\ TAGS    
The following tags can appear in the documentation comment for a constructor or a method, except for the \f3@return\fR tag, which cannot appear in a constructor, and the \f3{@inheritDoc}\fR tag, which has restrictions\&.
.PP
@see reference || @since since-text || @deprecated deprecated-text || @param parameter-name description || @return description || @throws class-name description || @exception class-name description || @serialData data-description || {@link package\&.class#member label} || {@linkplain package\&.class#member label} || {@inheritDoc} || {@docRoot}
.PP
\fINote:\fR The \f3@serialData\fR tag can only be used in the documentation comment for the \f3writeObject\fR, \f3readObject\fR, \f3writeExternal\fR, \f3readExternal\fR, \f3writeReplace\fR, and \f3readResolve\fR methods\&.
.PP
Method comment example:
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3     * Returns the character at the specified index\&. An index \fP
.fi     
.nf     
\f3     * ranges from <code>0</code> to <code>length() \- 1</code>\fP
.fi     
.nf     
\f3     *\fP
.fi     
.nf     
\f3     * @param     index the index of the desired character\&.\fP
.fi     
.nf     
\f3     * @return    the desired character\&.\fP
.fi     
.nf     
\f3     * @exception StringIndexOutOfRangeException \fP
.fi     
.nf     
\f3     *              if the index is not in the range <code>0</code> \fP
.fi     
.nf     
\f3     *              to <code>length()\-1</code>\fP
.fi     
.nf     
\f3     * @see       java\&.lang\&.Character#charValue()\fP
.fi     
.nf     
\f3     */\fP
.fi     
.nf     
\f3    public char charAt(int index) {\fP
.fi     
.nf     
\f3       \&.\&.\&.\fP
.fi     
.nf     
\f3    }\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH OPTIONS    
The \f3javadoc\fR command uses doclets to determine its output\&. The \f3javadoc\fR command uses the default standard doclet unless a custom doclet is specified with the \f3-doclet\fR option\&. The \f3javadoc\fR command provides a set of command-line options that can be used with any doclet\&. These options are described in Javadoc Options\&. The standard doclet provides an additional set of command-line options that are described in Standard Doclet Options\&. All option names are not case-sensitive, but their arguments are case-sensitive\&.
.TP 0.2i    
\(bu
See also Javadoc Options
.TP 0.2i    
\(bu
See also Standard Doclet Options
.PP
The options are:
.PP
-1\&.1 || -author || -bootclasspath classpathlist || -bottom text || -breakiterator || -charset name || -classpath classpathlist || -d directory || -docencoding name || -docfilesubdirs || -doclet class || -docletpath classpathlist || -doctitle title || -encoding || -exclude packagename1:packagename2:\&.\&.\&. || -excludedocfilessubdir name1:name2 || -extdirs dirist || -footer footer || -group groupheading packagepattern:packagepattern || -header header || -help || -helpfile path\efilename || -Jflag || -javafx ||-keywords || -link extdocURL || -linkoffline extdocURL packagelistLoc || -linksource || -locale language_country_variant || -nocomment || -nodeprecated || -nodeprecatedlist || -nohelp || -noindex || -nonavbar || -noqualifier all | packagename1:packagename2\&.\&.\&. || -nosince || -notimestamp || -notree || -overview path/filename || -package || -private || -protected || -public || -quiet || -serialwarn || -source release || -sourcepath sourcepathlist || -sourcetab tablength || -splitindex || -stylesheet path/filename || -subpackages package1:package2:\&.\&.\&. || -tag tagname:Xaoptcmf:"taghead" || -taglet class || -tagletpath tagletpathlist || -title title || -top || -use || -verbose || -version || -windowtitle title
.PP
The following options are the core Javadoc options that are available to all doclets\&. The standard doclet provides the rest of the doclets: \f3-bootclasspath\fR, \f3-breakiterator\fR, \f3-classpath\fR, \f3-doclet\fR, \f3-docletpath\fR, \f3-encoding\fR, -\f3exclude\fR, \f3-extdirs\fR, \f3-help\fR, \f3-locale\fR, \f3-\fR\f3overview\fR, \f3-package\fR, \f3-private\fR, \f3-protected\fR, \f3-public\fR, \f3-quiet\fR, \f3-source\fR, \f3-sourcepath\fR, \f3-subpackages\fR, and \f3-verbose\fR\&.
.SS JAVADOC\ OPTIONS    
.TP
-overview \fIpath/filename\fR
.br
Specifies that the \f3javadoc\fR command should retrieve the text for the overview documentation from the source file specified by the \fIpath/filename\fRand place it on the Overview page (overview-summary\&.html)\&. The \fIpath/filename\fRis relative to the current directory\&.

While you can use any name you want for the \f3filename\fR value and place it anywhere you want for the path, it is typical to name it overview\&.html and place it in the source tree at the directory that contains the topmost package directories\&. In this location, no path is needed when documenting packages, because the \f3-sourcepath\fR option points to this file\&.

For example, if the source tree for the \f3java\&.lang\fR package is /src/classes/java/lang/, then you could place the overview file at /src/classes/overview\&.html

See Real-World Examples\&.

For information about the file specified by \fIpath/filename,\fRsee Overview Comment Files\&.

The overview page is created only when you pass two or more package names to the \f3javadoc\fR command\&. For a further explanation, see HTML Frames\&. The title on the overview page is set by \f3-doctitle\fR\&.
.TP
-Xdoclint:(all|none|[-]\fI<group>\fR)
.br
Reports warnings for bad references, lack of accessibility and missing Javadoc comments, and reports errors for invalid Javadoc syntax and missing HTML tags\&.

This option enables the \f3javadoc\fR command to check for all documentation comments included in the generated output\&. As always, you can select which items to include in the generated output with the standard options \f3-public\fR, \f3-protected\fR, \f3-package\fR and \f3-private\fR\&.

When the \f3-Xdoclint\fR is enabled, it reports issues with messages similar to the \f3javac\fR command\&. The \f3javadoc\fR command prints a message, a copy of the source line, and a caret pointing at the exact position where the error was detected\&. Messages may be either warnings or errors, depending on their severity and the likelihood to cause an error if the generated documentation were run through a validator\&. For example, bad references or missing Javadoc comments do not cause the \f3javadoc\fR command to generate invalid HTML, so these issues are reported as warnings\&. Syntax errors or missing HTML end tags cause the \f3javadoc\fR command to generate invalid output, so these issues are reported as errors\&.

By default, the \f3-Xdoclint\fR option is enabled\&. Disable it with the option \f3-Xdoclint:none\fR\&.

Change what the \f3-Xdoclint\fR option reports with the following options:
.RS     
.TP 0.2i    
\(bu
\f3-Xdoclint none\fR : disable the \f3-Xdoclint\fR option
.TP 0.2i    
\(bu
\f3-Xdoclint\fR\fIgroup\fR : enable \fIgroup\fR checks
.TP 0.2i    
\(bu
\f3-Xdoclint all\fR : enable all groups of checks
.TP 0.2i    
\(bu
\f3-Xdoclint all,\fR\fI-group\fR : enable all except \fIgroup\fR checks
.RE     


The variable \fIgroup\fR has one of the following values:
.RS     
.TP 0.2i    
\(bu
\f3accessibility\fR : Checks for the issues to be detected by an accessibility checker (for example, no caption or summary attributes specified in a \f3<table>\fR tag)\&.
.TP 0.2i    
\(bu
\f3html\fR : Detects high-level HTML issues, like putting block elements inside inline elements, or not closing elements that require an end tag\&. The rules are derived from theHTML 4\&.01 Specification\&. This type of check enables the \f3javadoc\fR command to detect HTML issues that many browsers might accept\&.
.TP 0.2i    
\(bu
\f3missing\fR : Checks for missing Javadoc comments or tags (for example, a missing comment or class, or a missing \f3@return\fR tag or similar tag on a method)\&.
.TP 0.2i    
\(bu
\f3reference\fR : Checks for issues relating to the references to Java API elements from Javadoc tags (for example, item not found in \f3@see\fR , or a bad name after \f3@param)\fR\&.
.TP 0.2i    
\(bu
\f3syntax\fR : Checks for low level issues like unescaped angle brackets (\f3<\fR and \f3>\fR) and ampersands (\f3&\fR) and invalid Javadoc tags\&.
.RE     


You can specify the \f3-Xdoclint\fR option multiple times to enable the option to check errors and warnings in multiple categories\&. Alternatively, you can specify multiple error and warning categories by using the preceding options\&. For example, use either of the following commands to check for the HTML, syntax, and accessibility issues in the file \fIfilename\fR\&.
.sp     
.nf     
\f3javadoc \-Xdoclint:html \-Xdoclint:syntax \-Xdoclint:accessibility \fIfilename\fR\fP
.fi     
.nf     
\f3javadoc \-Xdoclint:html,syntax,accessibility \fIfilename\fR\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


\fINote:\fR The \f3javadoc\fR command does not guarantee the completeness of these checks\&. In particular, it is not a full HTML compliance checker\&. The goal of the -\f3Xdoclint\fR option is to enable the \f3javadoc\fR command to report majority of common errors\&.

The \f3javadoc\fR command does not attempt to fix invalid input, it just reports it\&.
.TP
-public
.br
Shows only public classes and members\&.
.TP
-protected
.br
Shows only protected and public classes and members\&. This is the default\&.
.TP
-package
.br
Shows only package, protected, and public classes and members\&.
.TP
-private
.br
Shows all classes and members\&.
.TP
-help
.br
Displays the online help, which lists all of the \f3javadoc\fR and \f3doclet\fR command-line options\&.
.TP
-doclet \fIclass\fR
.br
Specifies the class file that starts the doclet used in generating the documentation\&. Use the fully qualified name\&. This doclet defines the content and formats the output\&. If the \f3-doclet\fR option is not used, then the \f3javadoc\fR command uses the standard doclet for generating the default HTML format\&. This class must contain the \f3start(Root)\fR method\&. The path to this starting class is defined by the \f3-docletpath\fR option\&. See Doclet Overview at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javadoc/doclet/overview\&.html
.TP
-docletpath \fIclasspathlist\fR
.br
Specifies the path to the doclet starting class file (specified with the \f3-doclet\fR option) and any JAR files it depends on\&. If the starting class file is in a JAR file, then this option specifies the path to that JAR file\&. You can specify an absolute path or a path relative to the current directory\&. If \f3classpathlist\fR contains multiple paths or JAR files, then they should be separated with a colon (:) on Oracle Solaris and a semi-colon (;) on Windows\&. This option is not necessary when the doclet starting class is already in the search path\&. See Doclet Overview at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javadoc/doclet/overview\&.html
.TP
-1\&.1
.br
Removed from Javadoc 1\&.4 with no replacement\&. This option created documentation with the appearance and functionality of documentation generated by Javadoc 1\&.1 (it never supported nested classes)\&. If you need this option, then use Javadoc 1\&.2 or 1\&.3 instead\&.
.TP
-source \fIrelease\fR
.br
Specifies the release of source code accepted\&. The following values for the \f3release\fR parameter are allowed\&. Use the value of \f3release\fR that corresponds to the value used when you compile code with the \f3javac\fR command\&.
.RS     
.TP 0.2i    
\(bu
\fIRelease Value: 1\&.5\fR\&. The \f3javadoc\fR command accepts code containing generics and other language features introduced in JDK 1\&.5\&. The compiler defaults to the 1\&.5 behavior when the \f3-source\fR option is not used\&.
.TP 0.2i    
\(bu
\fIRelease Value: 1\&.4\fR\&. The \f3javadoc\fR command accepts code containing assertions, which were introduced in JDK 1\&.4\&.
.TP 0.2i    
\(bu
\fIRelease Value: 1\&.3\fR\&. The \f3javadoc\fR command does not support assertions, generics, or other language features introduced after JDK 1\&.3\&.
.RE     

.TP
-sourcepath \fIsourcepathlist\fR
.br
Specifies the search paths for finding source files when passing package names or the \f3-subpackages\fR option into the \f3javadoc\fR command\&. Separate multiple paths with a colon (:)\&. The \f3javadoc\fR command searches all subdirectories of the specified paths\&. Note that this option is not only used to locate the source files being documented, but also to find source files that are not being documented, but whose comments are inherited by the source files being documented\&.

You can use the \f3-sourcepath\fR option only when passing package names into the \f3javadoc\fR command\&. This will not locate source files passed into the \f3javadoc\fR command\&. To locate source files, \f3\fRchange to that directory or include the path ahead of each file, as shown at Document One or More Classes\&. If you omit \f3-sourcepath\fR, then the \f3javadoc\fR command uses the class path to find the source files (see \f3-classpath\fR)\&. The default \f3-sourcepath\fR is the value of class path\&. If \f3-classpath\fR is omitted and you pass package names into the \f3javadoc\fR command, then the \f3javadoc\fR command searches in the current directory and subdirectories for the source files\&.

Set \f3sourcepathlist\fR to the root directory of the source tree for the package you are documenting\&.

For example, suppose you want to document a package called \f3com\&.mypackage\fR, whose source files are located at:/home/<USER>/src/com/mypackage/*\&.java\&. Specify the sourcepath to /home/<USER>/src, the directory that contains com\emypackage, and then supply the package name, as follows:
.sp     
.nf     
\f3javadoc \-sourcepath /home/<USER>/src/ com\&.mypackage\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


Notice that if you concatenate the value of sourcepath and the package name together and change the dot to a slash (/), then you have the full path to the package:

/home/<USER>/src/com/mypackage

To point to two source paths:
.sp     
.nf     
\f3javadoc \-sourcepath /home/<USER>/src:/home/<USER>/src com\&.mypackage\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-classpath \fIclasspathlist\fR
.br
Specifies the paths where the \f3javadoc\fR command searches for referenced classes These are the documented classes plus any classes referenced by those classes\&. Separate multiple paths with a colon (:)\&. The \f3javadoc\fR command searches all subdirectories of the specified paths\&. Follow the instructions in the class path documentation for specifying the \f3classpathlist\fR value\&.

If you omit \f3-sourcepath\fR, then the \f3javadoc\fR command uses \f3-classpath\fR to find the source files and class files (for backward compatibility)\&. If you want to search for source and class files in separate paths, then use both \f3-sourcepath\fR and \f3-classpath\fR\&.

For example, if you want to document \f3com\&.mypackage\fR, whose source files reside in the directory /home/<USER>/src/com/mypackage, and if this package relies on a library in /home/<USER>/lib, then you would use the following command:
.sp     
.nf     
\f3javadoc \-sourcepath /home/<USER>/lib \-classpath /home/<USER>/src com\&.mypackage\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


Similar to other tools, if you do not specify \f3-classpath\fR, then the \f3javadoc\fR command uses the \f3CLASSPATH\fR environment variable when it is set\&. If both are not set, then the \f3javadoc\fR command searches for classes from the current directory\&.

For an in-depth description of how the \f3javadoc\fR command uses \f3-classpath\fR to find user classes as it relates to extension classes and bootstrap classes, see How Classes Are Found at http://docs\&.oracle\&.com/javase/8/docs/technotes/tools/findingclasses\&.html

A class path element that contains a base name of * is considered equivalent to specifying a list of all the files in the directory with the extension \f3\&.jar\fR or \f3\&.JAR\fR\&.

For example, if directory \f3mydir\fR contains \f3a\&.jar\fR and \f3b\&.JA\fRR, then the class path element \f3foo/*\fR is expanded to a \f3A\&.jar:b\&.JAR\fR, except that the order of JAR files is unspecified\&. All JAR files in the specified directory including hidden files are included in the list\&. A class path entry that consists of * expands to a list of all the jar files in the current directory\&. The \f3CLASSPATH\fR environment variable is similarly expanded\&. Any class path wildcard expansion occurs before the Java Virtual Machine (JVM) starts\&. No Java program ever sees unexpanded wild cards except by querying the environment, for example, by calling System\&.getenv(\f3"CLASSPATH"\fR)\&.
.TP
-subpackages \fIpackage1:package2:\&.\&.\&.\fR
.br
Generates documentation from source files in the specified packages and recursively in their subpackages\&. This option is useful when adding new subpackages to the source code because they are automatically included\&. Each package argument is any top-level subpackage (such as \f3java\fR) or fully qualified package (such as \f3javax\&.swing\fR) that does not need to contain source files\&. Arguments are separated by colons on all operating systems\&. Wild cards are not allowed\&. Use \f3-sourcepath\fR to specify where to find the packages\&. This option does not process source files that are in the source tree but do not belong to the packages\&. See Process Source Files\&.

For example, the following command generates documentation for packages named \f3java\fR and \f3javax\&.swing\fR and all of their subpackages\&.
.sp     
.nf     
\f3javadoc \-d docs \-sourcepath /home/<USER>/src  \-subpackages java:javax\&.swing \fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-exclude \fIpackagename1:packagename2:\&.\&.\&.\fR
.br
Unconditionally excludes the specified packages and their subpackages from the list formed by \f3-subpackages\fR\&. It excludes those packages even when they would otherwise be included by some earlier or later \f3-subpackages\fR option\&.

The following example would include \f3java\&.io\fR, \f3java\&.util\fR, and \f3java\&.math\fR (among others), but would exclude packages rooted at \f3java\&.net\fR and \f3java\&.lang\fR\&. Notice that this example excludes \f3java\&.lang\&.ref\fR, which is a subpackage of \f3java\&.lang\fR\&.
.sp     
.nf     
\f3javadoc \-sourcepath /home/<USER>/src \-subpackages java \-exclude \fP
.fi     
.nf     
\f3    java\&.net:java\&.lang\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-bootclasspath \fIclasspathlist\fR
.br
Specifies the paths where the boot classes reside\&. These are typically the Java platform classes\&. The \f3bootclasspath\fR is part of the search path the \f3javadoc\fR command uses to look up source and class files\&. For more information, see How Classes Are Found at http://docs\&.oracle\&.com/javase/8/docs/technotes/tools/findingclasses\&.html

Separate directories in the \f3classpathlist\fR parameters with semicolons (;) for Windows and colons (:) for Oracle Solaris\&.
.TP
-extdirs \fIdirist\fR
.br
Specifies the directories where extension classes reside\&. These are any classes that use the Java Extension mechanism\&. The \f3extdirs\fR option is part of the search path the \f3javadoc\fR command uses to look up source and class files\&. See the \f3-classpath\fR option for more information\&. Separate directories in \f3dirlist\fR with semicolons (;) for Windows and colons (:) for Oracle Solaris\&.
.TP
-verbose
.br
Provides more detailed messages while the \f3javadoc\fR command runs\&. Without the \f3verbose\fR option, messages appear for loading the source files, generating the documentation (one message per source file), and sorting\&. The verbose option causes the printing of additional messages that specify the number of milliseconds to parse each Java source file\&.
.TP
-quiet
.br
Shuts off messages so that only the warnings and errors appear to make them easier to view\&. It also suppresses the \f3version\fR string\&.
.TP
-breakiterator
.br
Uses the internationalized sentence boundary of \f3java\&.text\&.BreakIterator\fR to determine the end of the first sentence in the main description of a package, class, or member for English\&. All other locales already use the \f3BreakIterator\fR class, rather than an English language, locale-specific algorithm\&. The first sentence is copied to the package, class, or member summary and to the alphabetic index\&. From JDK 1\&.2 and later, the \f3BreakIterator\fR class is used to determine the end of a sentence for all languages except for English\&. Therefore, the \f3-breakiterator\fR option has no effect except for English from 1\&.2 and later\&. English has its own default algorithm:
.RS     
.TP 0.2i    
\(bu
English default sentence-break algorithm\&. Stops at a period followed by a space or an HTML block tag, such as \f3<P>\fR\&.
.TP 0.2i    
\(bu
Breakiterator sentence-break algorithm\&. Stops at a period, question mark, or exclamation point followed by a space when the next word starts with a capital letter\&. This is meant to handle most abbreviations (such as "The serial no\&. is valid", but will not handle "Mr\&. Smith")\&. The \f3-breakiterator\fR option does not stop at HTML tags or sentences that begin with numbers or symbols\&. The algorithm stops at the last period in \&.\&./filename, even when embedded in an HTML tag\&.
.RE     


In Java SE 1\&.5 the \f3-breakiterator\fR option warning messages are removed, and the default sentence-break algorithm is unchanged\&. If you have not modified your source code to eliminate the \f3-breakiterator\fR option warnings in Java SE 1\&.4\&.x, then you do not have to do anything\&. The warnings go away starting with Java SE 1\&.5\&.0\&.
.TP
-locale \fIlanguage_country_variant\fR
.br
Specifies the locale that the \f3javadoc\fR command uses when it generates documentation\&. The argument is the name of the locale, as described in \f3j\fR\f3ava\&.util\&.Locale\fR documentation, such as \f3en_US\fR (English, United States) or \f3en_US_WIN\fR (Windows variant)\&.

\fINote:\fR The \f3-locale\fR option must be placed ahead (to the left) of any options provided by the standard doclet or any other doclet\&. Otherwise, the navigation bars appear in English\&. This is the only command-line option that depends on order\&. See Standard Doclet Options\&.

Specifying a locale causes the \f3javadoc\fR command to choose the resource files of that locale for messages such as strings in the navigation bar, headings for lists and tables, help file contents, comments in the stylesheet\&.css file, and so on\&. It also specifies the sorting order for lists sorted alphabetically, and the sentence separator to determine the end of the first sentence\&. The \f3-locale\fR option does not determine the locale of the documentation comment text specified in the source files of the documented classes\&.
.TP
-encoding
.br
Specifies the encoding name of the source files, such as \f3EUCJIS/SJIS\fR\&. If this option is not specified, then the platform default converter is used\&. See also the\f3-docencoding name\fR and \f3-charset name\fR options\&.
.TP
-J\fIflag\fR
.br
Passes \f3flag\fR directly to the Java Runtime Environment (JRE) that runs the \f3javadoc\fR command\&. For example, if you must ensure that the system sets aside 32 MB of memory in which to process the generated documentation, then you would call the \f3-Xmx\fR option as follows: \f3javadoc -J-Xmx32m -J-Xms32m com\&.mypackage\fR\&. Be aware that \f3-Xms\fR is optional because it only sets the size of initial memory, which is useful when you know the minimum amount of memory required\&.

There is no space between the \f3J\fR and the \f3flag\fR\&.

Use the \f3-version\fR option to find out what version of the \f3javadoc\fR command you are using\&. The version number of the standard doclet appears in its output stream\&. See Running the Javadoc Command\&.
.sp     
.nf     
\f3javadoc \-J\-version\fP
.fi     
.nf     
\f3java version "1\&.7\&.0_09"\fP
.fi     
.nf     
\f3Java(TM) SE Runtime Environment (build 1\&.7\&.0_09\-b05)\fP
.fi     
.nf     
\f3Java HotSpot(TM) 64\-Bit Server VM (build 23\&.5\-b02, mixed mode)\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-javafx
.br
Generates HTML documentation using the JavaFX extensions to the standard doclet\&. The generated documentation includes a Property Summary section in addition to the other summary sections generated by the standard Java doclet\&. The listed properties are linked to the sections for the getter and setter methods of each property\&.

If there are no documentation comments written explicitly for getter and setter methods, the documentation comments from the property method are automatically copied to the generated documentation for these methods\&. This option also adds a new \f3@defaultValue\fR tag that allows documenting the default value for a property\&.

Example:
.sp     
.nf     
\f3javadoc \-javafx MyClass\&.java \-d testdir\fP
.fi     
.sp     

.SS STANDARD\ DOCLET\ OPTIONS    
.TP
-d \fIdirectory\fR
.br
Specifies the destination directory where the \f3javadoc\fR command saves the generated HTML files\&. If you omit the \f3-d\fR option, then the files are saved to the current directory\&. The \f3directory\fR value can be absolute or relative to the current working directory\&. As of Java SE 1\&.4, the destination directory is automatically created when the \f3javadoc\fR command runs\&.

For example, the following command generates the documentation for the package \f3com\&.mypackage\fR and saves the results in the /user/doc/ directory: \f3javadoc -d\fR\f3/user/doc/\fR\f3com\&.mypackage\fR\&.
.TP
-use
.br
Includes one Use page for each documented class and package\&. The page describes what packages, classes, methods, constructors and fields use any API of the specified class or package\&. Given class C, things that use class C would include subclasses of C, fields declared as C, methods that return C, and methods and constructors with parameters of type C\&. For example, you can look at the Use page for the \f3String\fR type\&. Because the \f3getName\fR method in the \f3java\&.awt\&.Font\fR class returns type \f3String\fR, the \f3getName\fR method uses \f3String\fR and so the \f3getName\fR method appears on the Use page for \f3String\fR\&.This documents only uses of the API, not the implementation\&. When a method uses \f3String\fR in its implementation, but does not take a string as an argument or return a string, that is not considered a use of \f3String\fR\&.To access the generated Use page, go to the class or package and click the \fIUse link\fR in the navigation bar\&.
.TP
-version
.br
Includes the @version text in the generated docs\&. This text is omitted by default\&. To find out what version of the \f3javadoc\fR command you are using, use the \f3-J-version\fR option\&.
.TP
-author
.br
Includes the \f3@author\fR text in the generated docs\&.
.TP
-splitindex
.br
Splits the index file into multiple files, alphabetically, one file per letter, plus a file for any index entries that start with non-alphabetical symbols\&.
.TP
-windowtitle \fItitle\fR
.br
Specifies the title to be placed in the HTML \f3<title>\fR tag\&. The text specified in the \f3title\fR tag appears in the window title and in any browser bookmarks (favorite places) that someone creates for this page\&. This title should not contain any HTML tags because the browser does not interpret them correctly\&. Use escape characters on any internal quotation marks within the \f3title\fR tag\&. If the \f3-windowtitle\fR option is omitted, then the \f3javadoc\fR command uses the value of the \f3-doctitle\fR option for the \f3-windowtitle\fR option\&. For example, \f3javadoc -windowtitle "Java SE Platform" com\&.mypackage\fR\&.
.TP
-doctitle \fItitle\fR
.br
Specifies the title to place near the top of the overview summary file\&. The text specified in the \f3title\fR tag is placed as a centered, level-one heading directly beneath the top navigation bar\&. The \f3title\fR tag can contain HTML tags and white space, but when it does, you must enclose the title in quotation marks\&. Internal quotation marks within the \f3title\fR tag must be escaped\&. For example, \f3javadoc -header "<b>Java Platform </b><br>v1\&.4" com\&.mypackage\&.\fR
.TP
-title \fItitle\fR
.br
No longer exists\&. It existed only in Beta releases of Javadoc 1\&.2\&. It was renamed to \f3-doctitle\fR\&. This option was renamed to make it clear that it defines the document title, rather than the window title\&.
.TP
-header \fIheader\fR
.br
Specifies the header text to be placed at the top of each output file\&. The header is placed to the right of the upper navigation bar\&. The \f3header\fR can contain HTML tags and white space, but when it does, the \f3header\fR must be enclosed in quotation marks\&. Use escape characters for internal quotation marks within a header\&. For example, \f3javadoc -header "<b>Java Platform </b><br>v1\&.4" com\&.mypackage\&.\fR
.TP
-footer \fIfooter\fR
.br
Specifies the footer text to be placed at the bottom of each output file\&. The \fIfooter\fR value is placed to the right of the lower navigation bar\&. The \f3footer\fR value can contain HTML tags and white space, but when it does, the \f3footer\fR value must be enclosed in quotation marks\&. Use escape characters for any internal quotation marks within a footer\&.
.TP
-top
.br
Specifies the text to be placed at the top of each output file\&.
.TP
-bottom \fItext\fR
.br
Specifies the text to be placed at the bottom of each output file\&. The text is placed at the bottom of the page, underneath the lower navigation bar\&. The text can contain HTML tags and white space, but when it does, the text must be enclosed in quotation marks\&. Use escape characters for any internal quotation marks within text\&.
.TP
-link \fIextdocURL\fR
.br
Creates links to existing Javadoc-generated documentation of externally referenced classes\&. The \fIextdocURL\fR argument is the absolute or relative URL of the directory that contains the external Javadoc-generated documentation you want to link to\&. You can specify multiple \f3-link\fR options in a specified \f3javadoc\fR command run to link to multiple documents\&.

The package-list file must be found in this directory (otherwise, use the \f3-linkoffline\fR option)\&. The \f3javadoc\fR command reads the package names from the package-list file and links to those packages at that URL\&. When the \f3javadoc\fR command runs, the \f3extdocURL\fR value is copied into the \f3<A HREF>\fR links that are created\&. Therefore, \f3extdocURL\fR must be the URL to the directory, and not to a file\&. You can use an absolute link for \fIextdocURL\fR to enable your documents to link to a document on any web site, or you can use a relative link to link only to a relative location\&. If you use a relative link, then the value you pass in should be the relative path from the destination directory (specified with the \f3-d\fR option) to the directory containing the packages being linked to\&.When you specify an absolute link, you usually use an HTTP link\&. However, if you want to link to a file system that has no web server, then you can use a file link\&. Use a file link only when everyone who wants to access the generated documentation shares the same file system\&.In all cases, and on all operating systems, use a slash as the separator, whether the URL is absolute or relative, and \f3h\fR\f3ttp:\fR or \f3f\fR\f3ile:\fR as specified in the URL Memo: Uniform Resource Locators at http://www\&.ietf\&.org/rfc/rfc1738\&.txt
.sp     
.nf     
\f3\-link  http://<host>/<directory>/<directory>/\&.\&.\&./<name>\fP
.fi     
.nf     
\f3\-link file://<host>/<directory>/<directory>/\&.\&.\&./<name>\fP
.fi     
.nf     
\f3\-link <directory>/<directory>/\&.\&.\&./<name>\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.PP
Differences between the -linkoffline and -link options

Use the \f3-link\fR option in the following cases:
.TP 0.2i    
\(bu
When you use a relative path to the external API document\&.
.TP 0.2i    
\(bu
When you use an absolute URL to the external API document if your shell lets you open a connection to that URL for reading\&.
.PP
Use the \f3-linkoffline\fR option when you use an absolute URL to the external API document, if your shell does not allow a program to open a connection to that URL for reading\&. This can occur when you are behind a firewall and the document you want to link to is on the other side\&.
.PP
\f3Example 1 Absolute Link to External Documents\fR
.PP
Use the following command if you want to link to the \f3java\&.lang\fR, \f3java\&.io\fR and other Java platform packages, shown at http://docs\&.oracle\&.com/javase/8/docs/api/index\&.html
.sp     
.nf     
\f3javadoc \-link http://docs\&.oracle\&.com/javase/8/docs/api/ com\&.mypackage\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The command generates documentation for the package \f3com\&.mypackage\fR with links to the Java SE packages\&. The generated documentation contains links to the \f3Object\fR class, for example, in the class \f3trees\fR\&. Other options, such as the \f3-sourcepath\fR and \f3-d\fR options, are not shown\&.
.PP
\f3Example 2 Relative Link to External Documents\fR
.PP
In this example, there are two packages with documents that are generated in different runs of the \f3javadoc\fR command, and those documents are separated by a relative path\&. The packages are \f3com\&.apipackage\fR, an API, and c\f3om\&.spipackage\fR, an Service Provide Interface (SPI)\&. You want the documentation to reside in docs/api/com/apipackage and docs/spi/com/spipackage\&. Assuming that the API package documentation is already generated, and that docs is the current directory, you document the SPI package with links to the API documentation by running: \f3javadoc -d \&./spi -link \&.\&./api com\&.spipackage\fR\&.
.PP
Notice the \f3-link\fR option is relative to the destination directory (docs/spi)\&.
.PP
Notes

The \f3-link\fR option lets you link to classes referenced to by your code, but not documented in the current \f3javadoc\fR command run\&. For these links to go to valid pages, you must know where those HTML pages are located and specify that location with \f3extdocURL\fR\&. This allows third-party documentation to link to java\&.* documentation at http://docs\&.oracle\&.com\&.Omit the \f3-link\fR option when you want the \f3javadoc\fR command to create links only to APIs within the documentation it is generating in the current run\&. Without the \f3-link\fR option, the \f3javadoc\fR command does not create links to documentation for external references because it does not know whether or where that documentation exists\&.The \f3-link\fR option can create links in several places in the generated documentation\&. See Process Source Files\&. Another use is for cross-links between sets of packages: Execute the \f3javadoc\fR command on one set of packages, then run the \f3javadoc\fR command again on another set of packages, creating links both ways between both sets\&.
.PP
How to Reference a Class

For a link to an externally referenced class to appear (and not just its text label), the class must be referenced in the following way\&. It is not sufficient for it to be referenced in the body of a method\&. It must be referenced in either an \f3import\fR statement or in a declaration\&. Here are examples of how the class \f3java\&.io\&.File\fR can be referenced:
.PP
\fI\fRIn any kind of import statement\&. By wildcard import, import explicitly by name, or automatically import for \f3java\&.lang\&.*\fR\&.
.PP
In Java SE 1\&.3\&.\fIn\fR and 1\&.2\&.\fIn\fR, only an explicit import by name works\&. A wildcard \f3import\fR statement does not work, nor does the automatic \f3import java\&.lang\&.*\fR\&.
.PP
\fI\fRIn a declaration: \f3void mymethod(File f) {}\fR
.PP
The reference can be in the return type or parameter type of a method, constructor, field, class, or interface, or in an implements, extends, or throws statement\&.
.PP
An important corollary is that when you use the \f3-link\fR option, there can be many links that unintentionally do not appear due to this constraint\&. The text would appear without being a link\&. You can detect these by the warnings they emit\&. The simplest way to properly reference a class and add the link would be to import that class\&.
.PP
Package List

The \f3-link\fR option requires that a file named package-list, which is generated by the \f3javadoc\fR command, exists at the URL you specify with the \f3-link\fR option\&. The package-list file is a simple text file that lists the names of packages documented at that location\&. In the earlier example, the \f3javadoc\fR command searches for a file named package-list at the specified URL, reads in the package names, and links to those packages at that URL\&.
.PP
For example, the package list for the Java SE API is located at http://docs\&.oracle\&.com/javase/8/docs/api/package-list
.PP
The package list starts as follows:
.sp     
.nf     
\f3java\&.applet\fP
.fi     
.nf     
\f3java\&.awt\fP
.fi     
.nf     
\f3java\&.awt\&.color\fP
.fi     
.nf     
\f3java\&.awt\&.datatransfer\fP
.fi     
.nf     
\f3java\&.awt\&.dnd\fP
.fi     
.nf     
\f3java\&.awt\&.event\fP
.fi     
.nf     
\f3java\&.awt\&.font\fP
.fi     
.nf     
\f3and so on \&.\&.\&.\&.\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
When \f3javadoc\fR is run without the \f3-link\fR option and encounters a name that belongs to an externally referenced class, it prints the name with no link\&. However, when the \f3-link\fR option is used, the \f3javadoc\fR command searches the package-list file at the specified \fIextdocURL\fR location for that package name\&. When it finds the package name, it prefixes the name with \fIextdocURL\fR\&.
.PP
For there to be no broken links, all of the documentation for the external references must exist at the specified URLs\&. The \f3javadoc\fR command does not check that these pages exist, but only that the package-list exists\&.
.PP
Multiple Links

You can supply multiple \f3-link\fR options to link to any number of externally generated documents\&. Javadoc 1\&.2 has a known bug that prevents you from supplying more than one \f3-link\fR options\&. This was fixed in Javadoc 1\&.2\&.2\&. Specify a different link option for each external document to link to \f3javadoc -link extdocURL1 -link extdocURL2 \&.\&.\&. -link extdocURLn com\&.mypackage\fR where \fIextdocURL1\fR, \fIextdocURL2\fR, \&.\f3\&.\&. extdocURLn\fR point respectively to the roots of external documents, each of which contains a file named package-list\&.
.PP
Cross Links

Note that bootstrapping might be required when cross-linking two or more documents that were previously generated\&. If package-list does not exist for either document when you run the \f3javadoc\fR command on the first document, then the package-list does not yet exist for the second document\&. Therefore, to create the external links, you must regenerate the first document after you generate the second document\&.
.PP
In this case, the purpose of first generating a document is to create its package-list (or you can create it by hand if you are certain of the package names)\&. Then, generate the second document with its external links\&. The \f3javadoc\fR command prints a warning when a needed external package-list file does not exist\&.
.TP
-linkoffline \fIextdocURL packagelistLoc\fR
.br
This option is a variation of the \f3-link\fR option\&. They both create links to Javadoc-generated documentation for externally referenced classes\&. Use the \f3-link\fRo\f3ffline\fR option when linking to a document on the web when the \f3javadoc\fR command cannot access the document through a web connection\&. Use the \f3-linkoffline\fR option when package-list file of the external document is not accessible or does not exist at the \f3extdocURL\fR location, but does exist at a different location that can be specified by \f3packageListLoc\fR (typically local)\&. If \f3extdocURL\fR is accessible only on the World Wide Web, then the \f3-linkoffline\fR option removes the constraint that the \f3javadoc\fR command must have a web connection to generate documentation\&. Another use is as a work-around to update documents: After you have run the \f3javadoc\fR command on a full set of packages, you can run the \f3javadoc\fR command again on a smaller set of changed packages, so that the updated files can be inserted back into the original set\&. Examples follow\&. The \f3-linkoffline\fR option takes two arguments\&. The first is for the string to be embedded in the \f3<a href>\fR links, and the second tells the \f3-linkoffline\fR option where to find package-list:
.RS     
.TP 0.2i    
\(bu
The \f3extdocURL\fR value is the absolute or relative URL of the directory that contains the external Javadoc-generated documentation you want to link to\&. When relative, the value should be the relative path from the destination directory (specified with the \f3-d\fR option) to the root of the packages being linked to\&. For more information, see \fIextdocURL\fR in the \f3-link\fR option\&.
.TP 0.2i    
\(bu
The \f3packagelistLoc\fR value is the path or URL to the directory that contains the package-list file for the external documentation\&. This can be a URL (http: or file:) or file path, and can be absolute or relative\&. When relative, make it relative to the current directory from where the \f3javadoc\fR command was run\&. Do not include the package-list file name\&.

You can specify multiple \f3-linkoffline\fR options in a specified \f3javadoc\fR command run\&. Before Javadoc 1\&.2\&.2, the \f3-linkfile\fR options could be specified once\&.
.RE     

.PP
Absolute Links to External Documents

You might have a situation where you want to link to the \f3java\&.lang\fR, \f3java\&.io\fR and other Java SE packages at http://docs\&.oracle\&.com/javase/8/docs/api/index\&.html
.PP
However, your shell does not have web access\&. In this case, do the following:
.TP 0.4i    
1\&.
Open the package-list file in a browser at http://docs\&.oracle\&.com/javase/8/docs/api/package-list
.TP 0.4i    
2\&.
Save the file to a local directory, and point to this local copy with the second argument, \f3packagelistLoc\fR\&. In this example, the package list file was saved to the current directory (\&.)\&.
.PP
The following command generates documentation for the package c\f3om\&.mypackage\fR with links to the Java SE packages\&. The generated documentation will contain links to the \f3Object\fR class, for example, in the class \f3trees\fR\&. Other necessary options, such as \f3-sourcepath\fR, are not shown\&.
.sp     
.nf     
\f3javadoc \-linkoffline http://docs\&.oracle\&.com/javase/8/docs/api/ \&.  com\&.mypackage \fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.PP
Relative Links to External Documents

It is not very common to use \f3-linkoffline\fR with relative paths, for the simple reason that the \f3-link\fR option is usually enough\&. When you use the \f3-linkoffline\fR option, the package-list file is usually local, and when you use relative links, the file you are linking to is also local, so it is usually unnecessary to give a different path for the two arguments to the \f3-linkoffline\fR option When the two arguments are identical, you can use the \f3-link\fR option\&.
.PP
Create a package-list File Manually

If a package-list file does not exist yet, but you know what package names your document will link to, then you can manually create your own copy of this file and specify its path with \f3packagelistLoc\fR\&. An example would be the previous case where the package list for \f3com\&.spipackage\fR did not exist when \f3com\&.apipackage\fR was first generated\&. This technique is useful when you need to generate documentation that links to new external documentation whose package names you know, but which is not yet published\&. This is also a way of creating package-list files for packages generated with Javadoc 1\&.0 or 1\&.1, where package-list files were not generated\&. Similarly, two companies can share their unpublished package-list files so they can release their cross-linked documentation simultaneously\&.
.PP
Link to Multiple Documents

You can include the \f3-linkoffline\fR option once for each generated document you want to refer to:
.sp     
.nf     
\f3javadoc \-linkoffline extdocURL1 packagelistLoc1 \-linkoffline extdocURL2\fP
.fi     
.nf     
\f3packagelistLoc2 \&.\&.\&.\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.PP
Update Documents

You can also use the \f3-linkoffline\fR option when your project has dozens or hundreds of packages\&. If you have already run the \f3javadoc\fR command on the entire source tree, then you can quickly make small changes to documentation comments and rerun the \f3javadoc\fR command on a portion of the source tree\&. Be aware that the second run works properly only when your changes are to documentation comments and not to declarations\&. If you were to add, remove, or change any declarations from the source code, then broken links could show up in the index, package tree, inherited member lists, Use page, and other places\&.
.PP
First, create a new destination directory, such as update, for this new small run\&. In this example, the original destination directory is named html\&. In the simplest example, change directory to the parent of html\&. Set the first argument of the \f3-linkoffline\fR option to the current directory (\&.) and set the second argument to the relative path to html, where it can find package-list and pass in only the package names of the packages you want to update:
.sp     
.nf     
\f3javadoc \-d update \-linkoffline \&. html com\&.mypackage\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
When the \f3javadoc\fR command completes, copy these generated class pages in update/com/package (not the overview or index) to the original files in html/com/package\&.
.TP
-linksource
.br
Creates an HTML version of each source file (with line numbers) and adds links to them from the standard HTML documentation\&. Links are created for classes, interfaces, constructors, methods, and fields whose declarations are in a source file\&. Otherwise, links are not created, such as for default constructors and generated classes\&.

This option exposes all private implementation details in the included source files, including private classes, private fields, and the bodies of private methods, regardless of the \f3-public\fR, \f3-package\fR, \f3-protected\fR, and \f3-private\fR options\&. Unless you also use the \f3-private\fR option, not all private classes or interfaces are accessible through links\&.

Each link appears on the name of the identifier in its declaration\&. For example, the link to the source code of the \f3Button\fR class would be on the word \f3Button\fR:
.sp     
.nf     
\f3public class Button extends Component implements Accessible\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The link to the source code of the \f3getLabel\fR method in the \f3Button\fR class is on the word \f3getLabel\fR:
.sp     
.nf     
\f3public String getLabel()\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-group groupheading \fIpackagepattern:packagepattern\fR
.br
Separates packages on the overview page into whatever groups you specify, one group per table\&. You specify each group with a different \f3-group\fR option\&. The groups appear on the page in the order specified on the command line\&. Packages are alphabetized within a group\&. For a specified \f3-group\fR option, the packages matching the list of \f3packagepattern\fR expressions appear in a table with the heading \fIgroupheading\fR\&.
.RS     
.TP 0.2i    
\(bu
The \f3groupheading\fR can be any text and can include white space\&. This text is placed in the table heading for the group\&.
.TP 0.2i    
\(bu
The \f3packagepattern\fR value can be any package name at the start of any package name followed by an asterisk (*)\&. The asterisk is the only wildcard allowed and means match any characters\&. Multiple patterns can be included in a group by separating them with colons (:)\&. If you use an asterisk in a pattern or pattern list, then the pattern list must be inside quotation marks, such as \f3"java\&.lang*:java\&.util"\fR\&.
.RE     


When you do not supply a \f3-group\fR option, all packages are placed in one group with the heading \fIPackages\fR and appropriate subheadings\&. If the subheadings do not include all documented packages (all groups), then the remaining packages appear in a separate group with the subheading Other Packages\&.

For example, the following \f3javadoc\fR command separates the three documented packages into \fICore\fR, \fIExtension\fR, and \fIOther Packages\fR\&. The trailing dot (\&.) does not appear in \f3java\&.lang*\fR\&. Including the dot, such as \f3java\&.lang\&.*\fR omits the\f3java\&.lang\fR package\&.
.sp     
.nf     
\f3javadoc \-group "Core Packages" "java\&.lang*:java\&.util"\fP
.fi     
.nf     
\f3        \-group "Extension Packages" "javax\&.*"\fP
.fi     
.nf     
\f3        java\&.lang java\&.lang\&.reflect java\&.util javax\&.servlet java\&.new\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


\fICore Packages\fR

\f3java\&.lang\fR

\f3java\&.lang\&.reflect\fR

\f3java\&.util\fR

\fIExtension Packages\fR

\f3javax\&.servlet\fR

\fIOther Packages\fR

\f3java\&.new\fR
.TP
-nodeprecated
.br
Prevents the generation of any deprecated API in the documentation\&. This does what the \f3-nodeprecatedlist\fR option does, and it does not generate any deprecated API throughout the rest of the documentation\&. This is useful when writing code when you do not want to be distracted by the deprecated code\&.
.TP
-nodeprecatedlist
.br
Prevents the generation of the file that contains the list of deprecated APIs (deprecated-list\&.html) and the link in the navigation bar to that page\&. The \f3javadoc\fR command continues to generate the deprecated API throughout the rest of the document\&. This is useful when your source code contains no deprecated APIs, and you want to make the navigation bar cleaner\&.
.TP
-nosince
.br
Omits from the generated documents the \f3Since\fR sections associated with the \f3@since\fR tags\&.
.TP
-notree
.br
Omits the class/interface hierarchy pages from the generated documents\&. These are the pages you reach using the Tree button in the navigation bar\&. The hierarchy is produced by default\&.
.TP
-noindex
.br
Omits the index from the generated documents\&. The index is produced by default\&.
.TP
-nohelp
.br
Omits the HELP link in the navigation bars at the top and bottom of each page of output\&.
.TP
-nonavbar
.br
Prevents the generation of the navigation bar, header, and footer, that are usually found at the top and bottom of the generated pages\&. The \f3-nonavbar\fR option has no affect on the \f3-bottom\fR option\&. The \f3-nonavbar\fR option is useful when you are interested only in the content and have no need for navigation, such as when you are converting the files to PostScript or PDF for printing only\&.
.TP
-helpfile \fIpath\efilename\fR
.br
Specifies the path of an alternate help file path\efilename that the HELP link in the top and bottom navigation bars link to\&. Without this option, the \f3javadoc\fR command creates a help file help-doc\&.html that is hard-coded in the \f3javadoc\fR command\&. This option lets you override the default\&. The file name can be any name and is not restricted to help-doc\&.html\&. The \f3javadoc\fR command adjusts the links in the navigation bar accordingly, for example:
.sp     
.nf     
\f3javadoc \-helpfile /home/<USER>/myhelp\&.html java\&.awt\&.\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-stylesheet \fIpath/filename\fR
.br
Specifies the path of an alternate HTML stylesheet file\&. Without this option, the \f3javadoc\fR command automatically creates a stylesheet file stylesheet\&.css that is hard-coded in the \f3javadoc\fR command\&. This option lets you override the default\&. The file name can be any name and is not restricted to stylesheet\&.css, for example:
.sp     
.nf     
\f3javadoc \-stylesheet file /home/<USER>/mystylesheet\&.css com\&.mypackage\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-serialwarn
.br
Generates compile-time warnings for missing \f3@serial\fR tags\&. By default, Javadoc 1\&.2\&.2 and later versions generate no serial warnings\&. This is a reversal from earlier releases\&. Use this option to display the serial warnings, which helps to properly document default serializable fields and \f3writeExternal\fR methods\&.
.TP
-charset \fIname\fR
.br
Specifies the HTML character set for this document\&. The name should be a preferred MIME name as specified in the IANA Registry, Character Sets at http://www\&.iana\&.org/assignments/character-sets

For example, \f3javadoc -charset "iso-8859-1" mypackage\fR inserts the following line in the head of every generated page:
.sp     
.nf     
\f3<META http\-equiv="Content\-Type" content="text/html; charset=ISO\-8859\-1">\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


This \f3META\fR tag is described in the HTML standard (4197265 and 4137321), HTML Document Representation, at http://www\&.w3\&.org/TR/REC-html40/charset\&.html#h-5\&.2\&.2

See also the \f3-encoding\fR and \f3-docencoding name\fR options\&.
.TP
-docencoding \fIname\fR
.br
Specifies the encoding of the generated HTML files\&. The name should be a preferred MIME name as specified in the IANA Registry, Character Sets at http://www\&.iana\&.org/assignments/character-sets

If you omit the \f3-docencoding\fR option but use the \f3-encoding\fR option, then the encoding of the generated HTML files is determined by the \f3-encoding\fR option, for example: \f3javadoc -docencoding "iso-8859-1" mypackage\fR\&. See also the \f3-encoding\fR and \f3-docencoding name\fR options\&.
.TP
-keywords
.br
Adds HTML keyword <META> tags to the generated file for each class\&. These tags can help search engines that look for <META> tags find the pages\&. Most search engines that search the entire Internet do not look at <META> tags, because pages can misuse them\&. Search engines offered by companies that confine their searches to their own website can benefit by looking at <META> tags\&. The <META> tags include the fully qualified name of the class and the unqualified names of the fields and methods\&. Constructors are not included because they are identical to the class name\&. For example, the class \f3String\fR starts with these keywords:
.sp     
.nf     
\f3<META NAME="keywords" CONTENT="java\&.lang\&.String class">\fP
.fi     
.nf     
\f3<META NAME="keywords" CONTENT="CASE_INSENSITIVE_ORDER">\fP
.fi     
.nf     
\f3<META NAME="keywords" CONTENT="length()">\fP
.fi     
.nf     
\f3<META NAME="keywords" CONTENT="charAt()">\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-tag \fItagname\fR:Xaoptcmf:"\fItaghead\fR"
.br
Enables the \f3javadoc\fR command to interpret a simple, one-argument \f3@tagname\fR custom block tag in documentation comments\&. For the \f3javadoc\fR command to spell-check tag names, it is important to include a \f3-tag\fR option for every custom tag that is present in the source code, disabling (with \f3X\fR) those that are not being output in the current run\&.The colon (:) is always the separator\&. The \f3-tag\fR option outputs the tag heading \fItaghead\fR in bold, followed on the next line by the text from its single argument\&. Similar to any block tag, the argument text can contain inline tags, which are also interpreted\&. The output is similar to standard one-argument tags, such as the \f3@return\fR and \f3@author\fR tags\&. Omitting a value for \fItaghead\fR causes \f3tagname\fR to be the heading\&.

\fIPlacement of tags\fR: The \f3Xaoptcmf\fR arguments determine where in the source code the tag is allowed to be placed, and whether the tag can be disabled (using \f3X\fR)\&. You can supply either \f3a\fR, to allow the tag in all places, or any combination of the other letters:

\f3X\fR (disable tag)

\f3a\fR (all)

\f3o\fR (overview)

\f3p\fR (packages)

\f3t\fR (types, that is classes and interfaces)

\f3c\fR (constructors)

\f3m\fR (methods)

\f3f\fR (fields)

\fIExamples of single tags\fR: An example of a tag option for a tag that can be used anywhere in the source code is: \f3-tag todo:a:"To Do:"\fR\&.

If you want the \f3@todo\fR tag to be used only with constructors, methods, and fields, then you use: \f3-tag todo:cmf:"To Do:"\fR\&.

Notice the last colon (:) is not a parameter separator, but is part of the heading text\&. You would use either tag option for source code that contains the \f3@todo\fR tag, such as: \f3@todo The documentation for this method needs work\fR\&.

\fIColons in tag names\fR: Use a backslash to escape a colon that you want to use in a tag name\&. Use the \f3-tag ejb\e\e:bean:a:"EJB Bean:"\fR option for the following documentation comment:
.sp     
.nf     
\f3/**\fP
.fi     
.nf     
\f3 * @ejb:bean\fP
.fi     
.nf     
\f3 */\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


\fISpell-checking tag names\fR: Some developers put custom tags in the source code that they do not always want to output\&. In these cases, it is important to list all tags that are in the source code, enabling the ones you want to output and disabling the ones you do not want to output\&. The presence of \f3X\fR disables the tag, while its absence enables the tag\&. This gives the \f3javadoc\fR command enough information to know whether a tag it encounters is unknown, which is probably the results of a typographical error or a misspelling\&. The \f3javadoc\fR command prints a warning in these cases\&. You can add \f3X\fR to the placement values already present, so that when you want to enable the tag, you can simply delete the \f3X\fR\&. For example, if the \f3@todo\fR tag is a tag that you want to suppress on output, then you would use: \f3-tag todo:Xcmf:"To Do:"\fR\&. If you would rather keep it simple, then use this: \f3-tag todo:X\fR\&. The syntax \f3-tag todo:X\fR works even when the \f3@todo\fR tag is defined by a taglet\&.

\fIOrder of tags\fR: The order of the \f3-ta\fR\f3g\fR and \f3-taglet\fR options determines the order the tags are output\&. You can mix the custom tags with the standard tags to intersperse them\&. The tag options for standard tags are placeholders only for determining the order\&. They take only the standard tag\&'s name\&. Subheadings for standard tags cannot be altered\&. This is illustrated in the following example\&.If the \f3-tag\fR option is missing, then the position of the \f3-tagle\fR\f3t\fR option determines its order\&. If they are both present, then whichever appears last on the command line determines its order\&. This happens because the tags and taglets are processed in the order that they appear on the command line\&. For example, if the \f3-taglet\fR and \f3-tag\fR options have the name \f3todo\fR value, then the one that appears last on the command line determines the order\&.

\fIExample of a complete set of tags\fR: This example inserts To Do after Parameters and before Throws in the output\&. By using \f3X\fR, it also specifies that the \f3@example\fR tag might be encountered in the source code that should not be output during this run\&. If you use the \f3@argfile\fR tag, then you can put the tags on separate lines in an argument file similar to this (no line continuation characters needed):
.sp     
.nf     
\f3\-tag param\fP
.fi     
.nf     
\f3\-tag return\fP
.fi     
.nf     
\f3\-tag todo:a:"To Do:"\fP
.fi     
.nf     
\f3\-tag throws\fP
.fi     
.nf     
\f3\-tag see\fP
.fi     
.nf     
\f3\-tag example:X\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


When the \f3javadoc\fR command parses the documentation comments, any tag encountered that is neither a standard tag nor passed in with the \f3-tag\fR or \f3-taglet\fR options is considered unknown, and a warning is thrown\&.

The standard tags are initially stored internally in a list in their default order\&. Whenever the \f3-tag\fR options are used, those tags get appended to this list\&. Standard tags are moved from their default position\&. Therefore, if a \f3-tag\fR option is omitted for a standard tag, then it remains in its default position\&.

\fIAvoiding conflicts\fR: If you want to create your own namespace, then you can use a dot-separated naming convention similar to that used for packages: \f3com\&.mycompany\&.todo\fR\&. Oracle will continue to create standard tags whose names do not contain dots\&. Any tag you create will override the behavior of a tag by the same name defined by Oracle\&. If you create a \f3@todo\fR tag or taglet, then it always has the same behavior you define, even when Oracle later creates a standard tag of the same name\&.

\fIAnnotations vs\&. Javadoc tags\fR: In general, if the markup you want to add is intended to affect or produce documentation, then it should be a Javadoc tag\&. Otherwise, it should be an annotation\&. See Custom Tags and Annotations in How to Write Doc Comments for the Javadoc Tool at http://www\&.oracle\&.com/technetwork/java/javase/documentation/index-137868\&.html#annotations

You can also create more complex block tags or custom inline tags with the \f3-taglet\fR option\&.
.TP
-taglet \fIclass\fR
.br
Specifies the class file that starts the taglet used in generating the documentation for that tag\&. Use the fully qualified name for the \f3class\fR value\&. This taglet also defines the number of text arguments that the custom tag has\&. The taglet accepts those arguments, processes them, and generates the output\&. For extensive documentation with example taglets, see: Taglet Overview at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javadoc/taglet/overview\&.html

Taglets are useful for block or inline tags\&. They can have any number of arguments and implement custom behavior, such as making text bold, formatting bullets, writing out the text to a file, or starting other processes\&. Taglets can only determine where a tag should appear and in what form\&. All other decisions are made by the doclet\&. A taglet cannot do things such as remove a class name from the list of included classes\&. However, it can execute side effects, such as printing the tag\&'s text to a file or triggering another process\&. Use the \f3-tagletpath\fR option to specify the path to the taglet\&. The following example inserts the To Do taglet after Parameters and ahead of Throws in the generated pages\&. Alternately, you can use the \f3-taglet\fR option in place of its \f3-tag\fR option, but that might be difficult to read\&.
.sp     
.nf     
\f3\-taglet com\&.sun\&.tools\&.doclets\&.ToDoTaglet\fP
.fi     
.nf     
\f3\-tagletpath /home/<USER>
.fi     
.nf     
\f3\-tag return\fP
.fi     
.nf     
\f3\-tag param\fP
.fi     
.nf     
\f3\-tag todo\fP
.fi     
.nf     
\f3\-tag throws\fP
.fi     
.nf     
\f3\-tag see\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-tagletpath \fItagletpathlist\fR
.br
Specifies the search paths for finding taglet class files\&. The \f3tagletpathlist\fR can contain multiple paths by separating them with a colon (:)\&. The \f3javadoc\fR command searches all subdirectories of the specified paths\&.
.TP
-docfilesubdirs
.br
Enables deep copying of doc-files directories\&. Subdirectories and all contents are recursively copied to the destination\&. For example, the directory doc-files/example/images and all of its contents would be copied\&. There is also an option to exclude subdirectories\&.
.TP
-excludedocfilessubdir \fIname1:name2\fR
.br
Excludes any doc-files subdirectories with the specified names\&. This prevents the copying of SCCS and other source-code-control subdirectories\&.
.TP
-noqualifier all | \fIpackagename1\fR:\fIpackagename2\&.\&.\&.\fR
.br
Omits qualifying package names from class names in output\&. The argument to the \f3-noqualifier\fR option is either \f3all\fR (all package qualifiers are omitted) or a colon-separate list of packages, with wild cards, to be removed as qualifiers\&. The package name is removed from places where class or interface names appear\&. See Process Source Files\&.

The following example omits all package qualifiers: \f3-noqualifier all\fR\&.

The following example omits \f3java\&.lang\fR and \f3java\&.io\fR package qualifiers: \f3-noqualifier java\&.lang:java\&.io\fR\&.

The following example omits package qualifiers starting with \f3java\fR, and \f3com\&.sun\fR subpackages, but not \f3javax\fR: \f3-noqualifier java\&.*:com\&.sun\&.*\fR\&.

Where a package qualifier would appear due to the previous behavior, the name can be suitably shortened\&. See How a Name Appears\&. This rule is in effect whether or not the \f3-noqualifier\fR option is used\&.
.TP
-notimestamp
.br
Suppresses the time stamp, which is hidden in an HTML comment in the generated HTML near the top of each page\&. The \f3-notimestamp\fR option is useful when you want to run the \f3javadoc\fR command on two source bases and get the differences between \f3diff\fR them, because it prevents time stamps from causing a \f3diff\fR (which would otherwise be a \f3diff\fR on every page)\&. The time stamp includes the \f3javadoc\fR command release number, and currently appears similar to this: \f3<!-- Generated by javadoc (build 1\&.5\&.0_01) on Thu Apr 02 14:04:52 IST 2009 -->\fR\&.
.TP
-nocomment
.br
Suppresses the entire comment body, including the main description and all tags, and generate only declarations\&. This option lets you reuse source files that were originally intended for a different purpose so that you can produce skeleton HTML documentation at the early stages of a new project\&.
.TP
-sourcetab \fItablength\fR
.br
Specifies the number of spaces each tab uses in the source\&.
.SH COMMAND-LINE\ ARGUMENT\ FILES    
To shorten or simplify the \f3javadoc\fR command, you can specify one or more files that contain arguments to the \f3javadoc\fR command (except \f3-J\fR options)\&. This enables you to create \f3javadoc\fR commands of any length on any operating system\&.
.PP
An argument file can include \f3javac\fR options and source file names in any combination\&. The arguments within a file can be space-separated or newline-separated\&. If a file name contains embedded spaces, then put the whole file name in double quotation marks\&.
.PP
File Names within an argument file are relative to the current directory, not the location of the argument file\&. Wild cards (\f3*\fR) are not allowed in these lists (such as for specifying *\&.java)\&. Using the at sign (@) to recursively interpret files is not supported\&. The \f3-J\fR options are not supported because they are passed to the launcher, which does not support argument files\&.
.PP
When you run the \f3javadoc\fR command, pass in the path and name of each argument file with the @ leading character\&. When the \f3javadoc\fR command encounters an argument beginning with the at sign (@), it expands the contents of that file into the argument list\&.
.PP
\f3Example 1 Single Argument File\fR
.PP
You could use a single argument file named \f3argfile\fR to hold all \f3javadoc\fR command arguments: \f3javadoc @argfile\fR\&. The argument file \f3\fRcontains the contents of both files, as shown in the next example\&.
.PP
\f3Example 2 Two Argument Files\fR
.PP
You can create two argument files: One for the \f3javadoc\fR command options and the other for the package names or source file names\&. Notice the following lists have no line-continuation characters\&.
.PP
Create a file named options that contains:
.sp     
.nf     
\f3\-d docs\-filelist \fP
.fi     
.nf     
\f3\-use \fP
.fi     
.nf     
\f3\-splitindex\fP
.fi     
.nf     
\f3\-windowtitle \&'Java SE 7 API Specification\&'\fP
.fi     
.nf     
\f3\-doctitle \&'Java SE 7 API Specification\&'\fP
.fi     
.nf     
\f3\-header \&'<b>Java\(tm SE 7</b>\&'\fP
.fi     
.nf     
\f3\-bottom \&'Copyright &copy; 1993\-2011 Oracle and/or its affiliates\&. All rights reserved\&.\&'\fP
.fi     
.nf     
\f3\-group "Core Packages" "java\&.*"\fP
.fi     
.nf     
\f3\-overview /java/pubs/ws/1\&.7\&.0/src/share/classes/overview\-core\&.html\fP
.fi     
.nf     
\f3\-sourcepath /java/pubs/ws/1\&.7\&.0/src/share/classes\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Create a file named packages that contains:
.sp     
.nf     
\f3com\&.mypackage1\fP
.fi     
.nf     
\f3com\&.mypackage2\fP
.fi     
.nf     
\f3com\&.mypackage3\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Run the \f3javadoc\fR command as follows:
.sp     
.nf     
\f3javadoc @options @packages\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3Example 3 Argument Files with Paths\fR
.PP
The argument files can have paths, but any file names inside the files are relative to the current working directory (not \f3path1\fR or \f3path2\fR):
.sp     
.nf     
\f3javadoc @path1/options @path2/packages\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3Example 4 Option Arguments\fR
.PP
The following example saves an argument to a \f3javadoc\fR command option in an argument file\&. The \f3-bottom\fR option is used because it can have a lengthy argument\&. You could create a file named bottom to contain the text argument:
.sp     
.nf     
\f3<font size="\-1">\fP
.fi     
.nf     
\f3    <a href="http://bugreport\&.sun\&.com/bugreport/">Submit a bug or feature</a><br/>\fP
.fi     
.nf     
\f3    Copyright &copy; 1993, 2011, Oracle and/or its affiliates\&. All rights reserved\&. <br/>\fP
.fi     
.nf     
\f3    Oracle is a registered trademark of Oracle Corporation and/or its affiliates\&.\fP
.fi     
.nf     
\f3    Other names may be trademarks of their respective owners\&.</font>\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Run the \f3javadoc\fR command as follows:\f3javadoc -bottom @bottom @packages\fR\&.
.PP
You can also include the \f3-bottom\fR option at the start of the argument file and run the \f3javadoc\fR command as follows: \f3javadoc @bottom @packages\fR\&.
.SH RUNNING\ THE\ JAVADOC\ COMMAND    
The release number of the \f3javadoc\fR command can be determined with the \f3javadoc -J-version\fR option\&. The release number of the standard doclet appears in the output stream\&. It can be turned off with the \f3-quiet\fR option\&.
.PP
Use the public programmatic interface to call the \f3javadoc\fR command from within programs written in the Java language\&. This interface is in \f3com\&.sun\&.tools\&.javadoc\&.Main\fR (and the \f3javadoc\fR command is reentrant)\&. For more information, see The Standard Doclet at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javadoc/standard-doclet\&.html#runningprogrammatically
.PP
The following instructions call the standard HTML doclet\&. To call a custom doclet, use the \f3-doclet\fR and \f3-docletpath\fR options\&. See Doclet Overview at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javadoc/doclet/overview\&.html
.SS SIMPLE\ EXAMPLES    
You can run the \f3javadoc\fR command on entire packages or individual source files\&. Each package name has a corresponding directory name\&.
.PP
In the following examples, the source files are located at /home/<USER>/java/awt/*\&.java\&. The destination directory is /home/<USER>
.PP
Document One or More Packages

To document a package, the source files for that package must be located in a directory that has the same name as the package\&.
.PP
If a package name has several identifiers (separated by dots, such as \f3java\&.awt\&.color\fR), then each subsequent identifier must correspond to a deeper subdirectory (such as java/awt/color)\&.
.PP
You can split the source files for a single package among two such directory trees located at different places, as long as \f3-sourcepath\fR points to them both\&. For example, src1/java/awt/color and src2/java/awt/color\&.
.PP
You can run the \f3javadoc\fR command either by changing directories (with the \f3cd\fR command) or by using the \f3-sourcepath\fR option\&. The following examples illustrate both alternatives\&.
.PP
\f3Example 1 Recursive Run from One or More Packages\fR
.PP
This example uses \f3-sourcepath\fR so the \f3javadoc\fR command can be run from any directory and \f3-subpackages\fR (a new 1\&.4 option) for recursion\&. It traverses the subpackages of the java directory excluding packages rooted at \f3java\&.net\fR and \f3java\&.lang\fR\&. Notice this excludes \f3java\&.lang\&.ref\fR, a subpackage of \f3java\&.lang\fR\&. To also traverse down other package trees, append their names to the \f3-subpackages\fR argument, such as \f3java:javax:org\&.xml\&.sax\fR\&.
.sp     
.nf     
\f3javadoc \-d /home/<USER>/home/<USER>
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3Example 2 Change to Root and Run Explicit Packages\fR
.PP
Change to the parent directory of the fully qualified package\&. Then, run the \f3javadoc\fR command with the names of one or more packages that you want to document:
.sp     
.nf     
\f3cd /home/<USER>/\fP
.fi     
.nf     
\f3javadoc \-d /home/<USER>
.fi     
.nf     
\f3\fP
.fi     
.sp     
To also traverse down other package trees, append their names to the \f3-subpackages\fR argument, such as j\f3ava:javax:org\&.xml\&.sax\fR\&.
.PP
\f3Example 3 Run from Any Directory on Explicit Packages in One Tree\fR
.PP
In this case, it does not matter what the current directory is\&. Run the \f3javadoc\fR command and use the \f3-sourcepath\fR option with the parent directory of the top-level package\&. Provide the names of one or more packages that you want to document:
.sp     
.nf     
\f3javadoc \-d /home/<USER>/home/<USER>
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3Example 4 Run from Any Directory on Explicit Packages in Multiple Trees\fR
.PP
Run the \f3javadoc\fR command and use the \f3-sourcepath\fR option with a colon-separated list of the paths to each tree\&'s root\&. Provide the names of one or more packages that you want to document\&. All source files for a specified package do not need to be located under a single root directory, but they must be found somewhere along the source path\&.
.sp     
.nf     
\f3javadoc \-d /home/<USER>/home/<USER>/home/<USER>
.fi     
.nf     
\f3\fP
.fi     
.sp     
The result is that all cases generate HTML-formatted documentation for the \f3public\fR and \f3protected\fR classes and interfaces in packages j\f3ava\&.awt\fR and \f3java\&.awt\&.even\fRt and save the HTML files in the specified destination directory\&. Because two or more packages are being generated, the document has three HTML frames: one for the list of packages, another for the list of classes, and the third for the main class pages\&.
.PP
Document One or More Classes

The second way to run the \f3javadoc\fR command is to pass one or more source files\&. You can run \f3javadoc\fR either of the following two ways: by changing directories (with the \f3cd\fR command) or by fully specifying the path to the source files\&. Relative paths are relative to the current directory\&. The \f3-sourcepath\fR option is ignored when passing source files\&. You can use command-line wild cards, such as an asterisk (*), to specify groups of classes\&.
.PP
\f3Example 1 Change to the Source Directory\fR
.PP
Change to the directory that holds the source files\&. Then run the \f3javadoc\fR command with the names of one or more source files you want to document\&.
.PP
This example generates HTML-formatted documentation for the classes \f3Button\fR, \f3Canvas,\fR and classes that begin with \f3Graphics\fR\&. Because source files rather than package names were passed in as arguments to the \f3javadoc\fR command, the document has two frames: one for the list of classes and the other for the main page\&.
.sp     
.nf     
\f3cd /home/<USER>/java/awt\fP
.fi     
.nf     
\f3javadoc \-d /home/<USER>
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3Example 2 Change to the Root Directory of the Package\fR
.PP
This is useful for documenting individual source files from different subpackages off of the same root\&. Change to the package root directory, and supply the source files with paths from the root\&.
.sp     
.nf     
\f3cd /home/<USER>/\fP
.fi     
.nf     
\f3javadoc \-d /home/<USER>/awt/Button\&.java java/applet/Applet\&.java\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3Example 3 Document Files from Any Directory\fR
.PP
In this case, it does not matter what the current directory is\&. Run the \f3javadoc\fR command with the absolute path (or path relative to the current directory) to the source files you want to document\&.
.sp     
.nf     
\f3javadoc \-d /home/<USER>/home/<USER>/java/awt/Button\&.java\fP
.fi     
.nf     
\f3/home/<USER>/java/awt/Graphics*\&.java\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.PP
Document Packages and Classes

You can document entire packages and individual classes at the same time\&. Here is an example that mixes two of the previous examples\&. You can use the \f3-sourcepath\fR option for the path to the packages but not for the path to the individual classes\&.
.sp     
.nf     
\f3javadoc \-d /home/<USER>/home/<USER>
.fi     
.nf     
\f3/home/<USER>/java/applet/Applet\&.java\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS REAL-WORLD\ EXAMPLES    
The following command-line and \f3makefile\fR versions of the \f3javadoc\fR command run on the Java platform APIs\&. It uses 180 MB of memory to generate the documentation for the 1500 (approximately) public and protected classes in the Java SE 1\&.2\&. Both examples use absolute paths in the option arguments, so that the same \f3javadoc\fR command can be run from any directory\&.
.PP
Command-Line Example

The following command might be too long for some shells\&. You can use a command-line argument file (or write a shell script) to overcome this limitation\&.
.PP
In the example, \f3packages\fR is the name of a file that contains the packages to process, such as \f3java\&.applet\fR\f3java\&.lang\fR\&. None of the options should contain any newline characters between the single quotation marks\&. For example, if you copy and paste this example, then delete the newline characters from the \f3-bottom\fR option\&.
.sp     
.nf     
\f3javadoc \-sourcepath /java/jdk/src/share/classes \e\fP
.fi     
.nf     
\f3\-overview /java/jdk/src/share/classes/overview\&.html \e\fP
.fi     
.nf     
\f3\-d /java/jdk/build/api \e\fP
.fi     
.nf     
\f3\-use \e\fP
.fi     
.nf     
\f3\-splitIndex \e\fP
.fi     
.nf     
\f3\-windowtitle \&'Java Platform, Standard Edition 7 API Specification\&' \e\fP
.fi     
.nf     
\f3\-doctitle \&'Java Platform, Standard Edition 7 API Specification\&' \e\fP
.fi     
.nf     
\f3\-header \&'<b>Java\(tm SE 7</b>\&' \e\fP
.fi     
.nf     
\f3\-bottom \&'<font size="\-1">\fP
.fi     
.nf     
\f3<a href="http://bugreport\&.sun\&.com/bugreport/">Submit a bug or feature</a><br/>\fP
.fi     
.nf     
\f3Copyright &copy; 1993, 2011, Oracle and/or its affiliates\&. All rights reserved\&.<br/>\fP
.fi     
.nf     
\f3Oracle is a registered trademark of Oracle Corporation and/or its affiliates\&.\fP
.fi     
.nf     
\f3Other names may be trademarks of their respective owners\&.</font>\&' \e\fP
.fi     
.nf     
\f3\-group "Core Packages" "java\&.*:com\&.sun\&.java\&.*:org\&.omg\&.*" \e\fP
.fi     
.nf     
\f3\-group "Extension Packages" "javax\&.*" \e\fP
.fi     
.nf     
\f3\-J\-Xmx180m \e  \fP
.fi     
.nf     
\f3@packages\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.PP
Programmatic Interface

The Javadoc Access API enables the user to invoke the Javadoc tool directly from a Java application without executing a new process\&.
.PP
For example, the following statements are equivalent to the command \f3javadoc -d /home/<USER>/home/<USER>
.sp     
.nf     
\f3import javax\&.tools\&.DocumentationTool;\fP
.fi     
.nf     
\f3import javax\&.tools\&.ToolProvider;\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3public class JavaAccessSample{\fP
.fi     
.nf     
\f3    public static void main(String[] args){\fP
.fi     
.nf     
\f3        DocumentationTool javadoc = ToolProvider\&.getSystemDocumentationTool();\fP
.fi     
.nf     
\f3        int rc = javadoc\&.run( null, null, null,\fP
.fi     
.nf     
\f3                 "\-d", "/home/<USER>",\fP
.fi     
.nf     
\f3                 "\-sourcepath", "home/src",\fP
.fi     
.nf     
\f3                 "\-subpackages", "java",\fP
.fi     
.nf     
\f3                 "\-exclude", "java\&.net:java\&.lang",\fP
.fi     
.nf     
\f3                 "com\&.example");\fP
.fi     
.nf     
\f3     }\fP
.fi     
.nf     
\f3 }\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The first three arguments of the \f3run\fR method specify input, standard output, and standard error streams\&. \f3Null\fR is the default value for \f3System\&.in\fR, \f3System\&.out\fR, and \f3System\&.err\fR, respectively\&.
.SS THE\ MAKEFILE\ EXAMPLE    
This is an example of a GNU \f3makefile\fR\&. Single quotation marks surround \f3makefile\fR arguments\&. For an example of a Windows \f3makefile\fR, see the \f3makefiles\fR section of the Javadoc FAQ at http://www\&.oracle\&.com/technetwork/java/javase/documentation/index-137483\&.html#makefiles
.sp     
.nf     
\f3javadoc \-sourcepath $(SRCDIR)              \e   /* Sets path for source files   */\fP
.fi     
.nf     
\f3        \-overview $(SRCDIR)/overview\&.html  \e   /* Sets file for overview text  */\fP
.fi     
.nf     
\f3        \-d /java/jdk/build/api             \e   /* Sets destination directory   */\fP
.fi     
.nf     
\f3        \-use                               \e   /* Adds "Use" files             */\fP
.fi     
.nf     
\f3        \-splitIndex                        \e   /* Splits index A\-Z             */\fP
.fi     
.nf     
\f3        \-windowtitle $(WINDOWTITLE)        \e   /* Adds a window title          */\fP
.fi     
.nf     
\f3        \-doctitle $(DOCTITLE)              \e   /* Adds a doc title             */\fP
.fi     
.nf     
\f3        \-header $(HEADER)                  \e   /* Adds running header text     */\fP
.fi     
.nf     
\f3        \-bottom $(BOTTOM)                  \e   /* Adds text at bottom          */\fP
.fi     
.nf     
\f3        \-group $(GROUPCORE)                \e   /* 1st subhead on overview page */\fP
.fi     
.nf     
\f3        \-group $(GROUPEXT)                 \e   /* 2nd subhead on overview page */\fP
.fi     
.nf     
\f3        \-J\-Xmx180m                         \e   /* Sets memory to 180MB         */\fP
.fi     
.nf     
\f3        java\&.lang java\&.lang\&.reflect        \e   /* Sets packages to document    */\fP
.fi     
.nf     
\f3        java\&.util java\&.io java\&.net         \e\fP
.fi     
.nf     
\f3        java\&.applet\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3WINDOWTITLE = \&'Java\(tm SE 7 API Specification\&'\fP
.fi     
.nf     
\f3DOCTITLE = \&'Java\(tm Platform Standard Edition 7 API Specification\&'\fP
.fi     
.nf     
\f3HEADER = \&'<b>Java\(tm SE 7</font>\&'\fP
.fi     
.nf     
\f3BOTTOM = \&'<font size="\-1">\fP
.fi     
.nf     
\f3      <a href="http://bugreport\&.sun\&.com/bugreport/">Submit a bug or feature</a><br/>\fP
.fi     
.nf     
\f3      Copyright &copy; 1993, 2011, Oracle and/or its affiliates\&. All rights reserved\&.<br/>\fP
.fi     
.nf     
\f3      Oracle is a registered trademark of Oracle Corporation and/or its affiliates\&.\fP
.fi     
.nf     
\f3      Other names may be trademarks of their respective owners\&.</font>\&'\fP
.fi     
.nf     
\f3GROUPCORE = \&'"Core Packages" "java\&.*:com\&.sun\&.java\&.*:org\&.omg\&.*"\&'\fP
.fi     
.nf     
\f3GROUPEXT  = \&'"Extension Packages" "javax\&.*"\&'\fP
.fi     
.nf     
\f3SRCDIR = \&'/java/jdk/1\&.7\&.0/src/share/classes\&'\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS NOTES    
.TP 0.2i    
\(bu
If you omit the \f3-windowtitle\fR option, then the \f3javadoc\fR command copies the document title to the window title\&. The \f3-windowtitle\fR option text is similar to the the \f3-doctitle\fR option, but without HTML tags to prevent those tags from appearing as raw text in the window title\&.
.TP 0.2i    
\(bu
If you omit the \f3-footer\fR option, then the \f3javadoc\fR command copies the header text to the footer\&.
.TP 0.2i    
\(bu
Other important options you might want to use, but were not needed in the previous example, are the \f3-classpath\fR and \f3-link\fR options\&.
.SH GENERAL\ TROUBLESHOOTING    
.TP 0.2i    
\(bu
The \f3javadoc\fR command reads only files that contain valid class names\&. If the \f3javadoc\fR command is not correctly reading the contents of a file, then verify that the class names are valid\&. See Process Source Files\&.
.TP 0.2i    
\(bu
See the Javadoc FAQ for information about common bugs and for troubleshooting tips at http://www\&.oracle\&.com/technetwork/java/javase/documentation/index-137483\&.html
.SH ERRORS\ AND\ WARNINGS    
Error and warning messages contain the file name and line number to the declaration line rather than to the particular line in the documentation comment\&.
.PP
For example, this message \f3error: cannot read: Class1\&.java\fR means that the \f3javadoc\fR command is trying to load \f3Class1\&.jav\fR\f3a\fR in the current directory\&. The class name is shown with its path (absolute or relative)\&.
.SH ENVIRONMENT    
.TP     
CLASSPATH
\f3CLASSPATH\fR is the environment variable that provides the path that the \f3javadoc\fR command uses to find user class files\&. This environment variable is overridden by the \f3-classpath\fR option\&. Separate directories with a semicolon for Windows or a colon for Oracle Solaris\&.

\fIWindows example\fR: \f3\&.;C:\eclasses;C:\ehome\ejava\eclasses\fR

\fIOracle Solaris example\fR: \f3\&.:/home/<USER>/usr/local/java/classes\fR\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
javac(1)
.TP 0.2i    
\(bu
java(1)
.TP 0.2i    
\(bu
jdb(1)
.TP 0.2i    
\(bu
javah(1)
.TP 0.2i    
\(bu
javap(1)
.SH RELATED\ DOCUMENTS    
.TP 0.2i    
\(bu
Javadoc Technology at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javadoc/index\&.html
.TP 0.2i    
\(bu
How Classes Are Found http://docs\&.oracle\&.com/javase/8/docs/technotes/tools/findingclasses\&.html
.TP 0.2i    
\(bu
How to Write Doc Comments for the Javadoc Tool http://www\&.oracle\&.com/technetwork/java/javase/documentation/index-137868\&.html
.TP 0.2i    
\(bu
URL Memo, Uniform Resource Locators http://www\&.ietf\&.org/rfc/rfc1738\&.txt
.TP 0.2i    
\(bu
HTML standard, HTML Document Representation (4197265 and 4137321) http://www\&.w3\&.org/TR/REC-html40/charset\&.html#h-5\&.2\&.2
.RE
.br
'pl 8.5i
'bp
