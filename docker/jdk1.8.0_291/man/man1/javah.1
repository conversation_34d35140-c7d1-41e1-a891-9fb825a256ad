'\" t
.\"  Copyright (c) 1994, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Basic Tools
.\"     Title: javah.1
.\"
.if n .pl 99999
.TH javah 1 "21 November 2013" "JDK 8" "Basic Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
javah \- Generates C header and source files from a Java class\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjavah\fR [ \fIoptions\fR ] f\fIully\-qualified\-class\-name \&.\&.\&.\fR
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
\fIfully-qualified-class-name\fR
The fully qualified location of the classes to be converted to C header and source files\&.
.SH DESCRIPTION    
The \f3javah\fR command generates C header and source files that are needed to implement native methods\&. The generated header and source files are used by C programs to reference an object\&'s instance variables from native source code\&. The \f3\&.h\fR file contains a \f3struct\fR definition with a layout that parallels the layout of the corresponding class\&. The fields in the \f3struct\fR correspond to instance variables in the class\&.
.PP
The name of the header file and the structure declared within it are derived from the name of the class\&. When the class passed to the \f3javah\fR command is inside a package, the package name is added to the beginning of both the header file name and the structure name\&. Underscores (_) are used as name delimiters\&.
.PP
By default the \f3javah\fR command creates a header file for each class listed on the command line and puts the files in the current directory\&. Use the \f3-stubs\fR option to create source files\&. Use the \f3-o\fR option to concatenate the results for all listed classes into a single file\&.
.PP
The Java Native Interface (JNI) does not require header information or stub files\&. The \f3javah\fR command can still be used to generate native method function prototypes needed for JNI-style native methods\&. The \f3javah\fR command produces JNI-style output by default and places the result in the \f3\&.h\fR file\&.
.SH OPTIONS    
.TP
-o \fIoutputfile\fR
.br
Concatenates the resulting header or source files for all the classes listed on the command line into an output file\&. Only one of \f3-o\fR or \f3-d\fR can be used\&.
.TP
-d \fIdirectory\fR
.br
Sets the directory where the \f3javah\fR command saves the header files or the stub files\&. Only one of \f3-d\fR or \f3-o\fR can be used\&.
.TP
-stubs
.br
Causes the \f3javah\fR command to generate C declarations from the Java object file\&.
.TP
-verbose
.br
Indicates verbose output and causes the \f3javah\fR command to print a message to \f3stdout\fR about the status of the generated files\&.
.TP
-help
.br
Prints a help message for \f3javah\fR usage\&.
.TP
-version
.br
Prints \f3javah\fR command release information\&.
.TP
-jni
.br
Causes the \f3javah\fR command to create an output file containing JNI-style native method function prototypes\&. This is the default output; use of \f3-jni\fR is optional\&.
.TP
-classpath \fIpath\fR
.br
Specifies the path the \f3javah\fR command uses to look up classes\&. Overrides the default or the \f3CLASSPATH\fR environment variable when it is set\&. Directories are separated by colons on Oracle Solaris and semicolons on Windows\&. The general format for path is:

\fIOracle Solaris\fR:

\&.:\fIyour-path\fR

Example: \f3\&.:/home/<USER>/classes:/usr/local/java/classes\fR

\fIWindows\fR:

\&.;\fIyour-path\fR

Example: \f3\&.;C:\eusers\edac\eclasses;C:\etools\ejava\eclasses\fR

As a special convenience, a class path element that contains a base name of * is considered equivalent to specifying a list of all the files in the directory with the extension \f3\&.jar\fR or \f3\&.JAR\fR\&.

For example, if directory \f3mydir\fR contains \f3a\&.jar\fR and \f3b\&.JAR\fR, then the class path element \f3mydir/*\fR is expanded to a \f3A\fR\f3\&.jar:b\&.JAR\fR, except that the order of jar files is unspecified\&. All JAR files in the specified directory, including hidden ones, are included in the list\&. A class path entry that consists of * expands to a list of all the JAR files in the current directory\&. The \f3CLASSPATH\fR environment variable, where defined, is similarly expanded\&. Any class path wild card expansion occurs before the Java Virtual Machine (JVM) is started\&. A Java program will never see unexpanded wild cards except by querying the environment\&. For example, by calling \f3System\&.getenv("CLASSPATH")\fR\&.
.TP
-bootclasspath \fIpath\fR
.br
Specifies the path from which to load bootstrap classes\&. By default, the bootstrap classes are the classes that implement the core Java platform located in \f3jre\elib\ert\&.jar\fR and several other JAR files\&.
.TP
-old
.br
Specifies that old JDK 1\&.0-style header files should be generated\&.
.TP
-force
.br
Specifies that output files should always be written\&.
.TP
-J\fIoption\fR
.br
Passes \f3option\fR to the Java Virtual Machine, where \f3option\fR is one of the options described on the reference page for the Java application launcher\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&. See java(1)\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
javah(1)
.TP 0.2i    
\(bu
java(1)
.TP 0.2i    
\(bu
jdb(1)
.TP 0.2i    
\(bu
javap(1)
.TP 0.2i    
\(bu
javadoc(1)
.RE
.br
'pl 8.5i
'bp
