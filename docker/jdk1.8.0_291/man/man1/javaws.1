'\" t
.\"  Copyright (c) 2003, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Java Web Start Tools
.\"     Title: javaws.1
.\"
.if n .pl 99999
.TH javaws 1 "21 November 2013" "JDK 8" "Java Web Start Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
javaws \- Starts Java Web Start\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjavaws\fR [ \fIrun\-options\fR ] \fIjnlp\fR
.fi     
.nf     

\fBjavaws\fR [ \fIcontrol\-options\fR ]
.fi     
.sp     
.TP     
\fIrun-options\fR
Command-line \f3run-options\fR\&. The \f3run-options\fR can be in any order\&. See Run-Options\&.
.TP     
\fIjnlp\fR
Either the path of or the Uniform Resource Locator (URL) of the Java Network Launching Protocol (JNLP) file\&.
.TP     
\fIcontrol-options\fR
Command-line \f3control-options\fR\&. The \f3control-options\fR can be in any order\&. See Control-Options\&.
.SH DESCRIPTION    
\fINote:\fR The \f3javaws\fR command is not available on Oracle Solaris\&.
.PP
The \f3javaws\fR command starts Java Web Start, which is the reference implementation of the JNLP\&. Java Web Start starts Java applications and applets hosted on a network\&.
.PP
If a JNLP file is specified, then the \f3javaws\fR command starts the Java application or applet specified in the JNLP file\&.
.PP
The \f3javaws\fR launcher has a set of options that are supported in the current release\&. However, the options may be removed in a future release\&.
.SH RUN-OPTIONS    
.TP
-offline
.br
Runs Java Web Start in offline mode\&.
.TP
-Xnosplash
.br
Does not display the initial splash screen\&.
.TP
-open \fIarguments\fR
.br
When specified, this option replaces the arguments in the JNLP file with \f3-open\fR\f3arguments\fR\&.
.TP
-print \fIarguments\fR
.br
When specified, this option replaces the arguments in the JNLP file with \f3-print\fR\f3arguments\fR\&.
.TP
-online
.br
Uses online mode\&. This is the default behavior\&.
.TP
-wait
.br
The \f3javaws\fR process does not exit until the application exits\&. This option does not function as described on Windows platforms\&.
.TP
-verbose
.br
Displays additional output\&.
.TP
-J\fIoption\fR
.br
Passes option to the Java Virtual Machine, where \f3option\fR is one of the options described on the reference page for the Java application launcher\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&. See java(1)\&.
.TP
-system
.br
Runs the application from the system cache only\&.
.SH CONTROL-OPTIONS    
.TP
-viewer
.br
Shows the cache viewer in the Java Control Panel\&.
.TP
-clearcache
.br
Removes all non-installed applications from the cache\&.
.TP
-userConfig \fIproperty-name\fR
.br
Clears the specified deployment property\&.
.TP
-userConfig \fIproperty-name property-value\fR
.br
Sets the specified deployment property to the specified value\&.
.TP
-uninstall
.br
Removes all applications from the cache\&.
.TP
-uninstall \fIjnlp\fR
.br
Removes the application from the cache\&.
.TP
-print \fIimport-options\fRjnlp
.br
Imports the application to the cache\&.
.SH IMPORT-OPTIONS    
.TP
-silent
.br
Imports silently (with no user interface)\&.
.TP
-system
.br
Imports application to the system cache\&.
.TP
-codebase \fIurl\fR
.br
Retrieves resources from the specified codebase\&.
.TP
-shortcut
.br
Installs shortcuts if the user allows a prompt\&. This option has no effect unless the \f3-silent\fR option is also used\&.
.TP
-association
.br
Installs associations if the user allows a prompt\&. This option has no effect unless the \f3-silent\fR option is also used\&.
.SH FILES    
For information about the user and system cache and deployment\&.properties files, see Deployment Configuration File and Properties at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/deployment/deployment-guide/properties\&.html
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
Java Web Start at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javaws/index\&.html
.RE
.br
'pl 8.5i
'bp
