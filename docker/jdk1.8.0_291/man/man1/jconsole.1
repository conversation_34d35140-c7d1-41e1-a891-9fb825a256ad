'\" t
.\"  Copyright (c) 2004, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Java Troubleshooting, Profiling, Monitoring and Management Tools
.\"     Title: jconsole.1
.\"
.if n .pl 99999
.TH jconsole 1 "21 November 2013" "JDK 8" "Java Troubleshooting, Profiling, Monitoring and Management Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
jconsole \- Starts a graphical console that lets you monitor and manage Java applications\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjconsole\fR [ \fIoptions\fR ] [ connection \&.\&.\&. ]
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
connection = \fIpid\fR | \fIhost\fR:\fIport\fR | \fIjmxURL\fR
The \f3pid\fR value is the process ID of a local Java Virtual Machine (JVM)\&. The JVM must be running with the same user ID as the user ID running the \f3jconsole\fR command\&.The \f3host:port\fR values are the name of the host system on which the JVM is running, and the port number specified by the system property \f3com\&.sun\&.management\&.jmxremote\&.port\fR when the JVM was started\&.The \f3jmxUrl\fR value is the address of the JMX agent to be connected to as described in JMXServiceURL\&.

For more information about the \f3connection\fR parameter, see Monitoring and Management Using JMX Technology at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/management/agent\&.html

See also the \f3JMXServiceURL\fR class description at http://docs\&.oracle\&.com/javase/8/docs/api/javax/management/remote/JMXServiceURL\&.html
.SH DESCRIPTION    
The \f3jconsole\fR command starts a graphical console tool that lets you monitor and manage Java applications and virtual machines on a local or remote machine\&.
.PP
On Windows, the \f3jconsole\fR command does not associate with a console window\&. It does, however, display a dialog box with error information when the \f3jconsole\fR command fails\&.
.SH OPTIONS    
.TP
-interval\fI=n\fR
.br
Sets the update interval to \fIn\fR seconds (default is 4 seconds)\&.
.TP
-notile
.br
Does not tile windows initially (for two or more connections)\&.
.TP
-pluginpath \fIplugins\fR
.br
Specifies a list of directories or JAR files to be searched for \f3JConsole\fR plug-ins\&. The \fIplugins\fR path should contain a provider-configuration file named \f3META-INF/services/com\&.sun\&.tools\&.jconsole\&.JConsolePlugin\fR that contains one line for each plug-in\&. The line specifies the fully qualified class name of the class implementing the \f3com\&.sun\&.tools\&.jconsole\&.JConsolePlugin\fR class\&.
.TP
-version
.br
Displays release information and exits\&.
.TP
-help
.br
Displays a help message\&.
.TP
-J\fIflag\fR
.br
Passes \f3flag\fR to the JVM on which the \f3jconsole\fR command is run\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
Using JConsole at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/management/jconsole\&.html
.TP 0.2i    
\(bu
Monitoring and Management Using JMX Technology at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/management/agent\&.html
.TP 0.2i    
\(bu
The \f3JMXServiceURL\fR class description at http://docs\&.oracle\&.com/javase/8/docs/api/javax/management/remote/JMXServiceURL\&.html
.RE
.br
'pl 8.5i
'bp
