'\" t
.\"  Copyright (c) 1995, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Basic Tools
.\"     Title: jdb.1
.\"
.if n .pl 99999
.TH jdb 1 "21 November 2013" "JDK 8" "Basic Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
jdb \- Finds and fixes bugs in Java platform programs\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjdb\fR [\fIoptions\fR] [\fIclassname\fR]  [\fIarguments\fR]
.fi     
.sp     
.TP     
\fIoptions\fR
Command-line options\&. See Options\&.
.TP     
\fIclass\fRname
Name of the main class to debug\&.
.TP     
\fIarguments\fR
Arguments passed to the \f3main()\fR method of the class\&.
.SH DESCRIPTION    
The Java Debugger (JDB) is a simple command-line debugger for Java classes\&. The \f3jdb\fR command and its options call the JDB\&. The \f3jdb\fR command demonstrates the Java Platform Debugger Architecture (JDBA) and provides inspection and debugging of a local or remote Java Virtual Machine (JVM)\&. See Java Platform Debugger Architecture (JDBA) at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/jpda/index\&.html
.SS START\ A\ JDB\ SESSION    
There are many ways to start a JDB session\&. The most frequently used way is to have JDB launch a new JVM with the main class of the application to be debugged\&. Do this by substituting the \f3jdb\fR command for the \f3java\fR command in the command line\&. For example, if your application\&'s main class is \f3MyClass\fR, then use the following command to debug it under JDB:
.sp     
.nf     
\f3jdb MyClass\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
When started this way, the \f3jdb\fR command calls a second JVM with the specified parameters, loads the specified class, and stops the JVM before executing that class\&'s first instruction\&.
.PP
Another way to use the \f3jdb\fR command is by attaching it to a JVM that is already running\&. Syntax for starting a JVM to which the \f3jdb\fR command attaches when the JVM is running is as follows\&. This loads in-process debugging libraries and specifies the kind of connection to be made\&.
.sp     
.nf     
\f3java \-agentlib:jdwp=transport=dt_socket,server=y,suspend=n MyClass\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
You can then attach the \f3jdb\fR command to the JVM with the following command:
.sp     
.nf     
\f3jdb \-attach 8000\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The \f3MyClass\fR argument is not specified in the \f3jdb\fR command line in this case because the \f3jdb\fR command is connecting to an existing JVM instead of launching a new JVM\&.
.PP
There are many other ways to connect the debugger to a JVM, and all of them are supported by the \f3jdb\fR command\&. The Java Platform Debugger Architecture has additional documentation on these connection options\&.
.SS BASIC\ JDB\ COMMANDS    
The following is a list of the basic \f3jdb\fR commands\&. The JDB supports other commands that you can list with the \f3-help\fR option\&.
.TP     
help or ?
The \f3help\fR or \f3?\fR commands display the list of recognized commands with a brief description\&.
.TP     
run
After you start JDB and set breakpoints, you can use the \f3run\fR command to execute the debugged application\&. The \f3run\fR command is available only when the \f3jdb\fR command starts the debugged application as opposed to attaching to an existing JVM\&.
.TP     
cont
Continues execution of the debugged application after a breakpoint, exception, or step\&.
.TP     
print
Displays Java objects and primitive values\&. For variables or fields of primitive types, the actual value is printed\&. For objects, a short description is printed\&. See the dump command to find out how to get more information about an object\&.

\fINote:\fR To display local variables, the containing class must have been compiled with the \f3javac -g\fR option\&.

The \f3print\fR command supports many simple Java expressions including those with method invocations, for example:
.sp     
.nf     
\f3print MyClass\&.myStaticField\fP
.fi     
.nf     
\f3print myObj\&.myInstanceField\fP
.fi     
.nf     
\f3print i + j + k (i, j, k are primities and either fields or local variables)\fP
.fi     
.nf     
\f3print myObj\&.myMethod() (if myMethod returns a non\-null)\fP
.fi     
.nf     
\f3print new java\&.lang\&.String("Hello")\&.length()\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP     
dump
For primitive values, the \f3dump\fR command is identical to the \f3print\fR command\&. For objects, the \f3dump\fR command prints the current value of each field defined in the object\&. Static and instance fields are included\&. The \f3dump\fR command supports the same set of expressions as the \f3print\fR command\&.
.TP     
threads
List the threads that are currently running\&. For each thread, its name and current status are printed and an index that can be used in other commands\&. In this example, the thread index is 4, the thread is an instance of \f3java\&.lang\&.Thread\fR, the thread name is \f3main\fR, and it is currently running\&.
.sp     
.nf     
\f34\&. (java\&.lang\&.Thread)0x1 main      running\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP     
thread
Select a thread to be the current thread\&. Many \f3jdb\fR commands are based on the setting of the current thread\&. The thread is specified with the thread index described in the threads command\&.
.TP     
where
The \f3where\fR command with no arguments dumps the stack of the current thread\&. The \f3where\fR\f3all\fR command dumps the stack of all threads in the current thread group\&. The \f3where\fR\f3threadindex\fR command dumps the stack of the specified thread\&.

If the current thread is suspended either through an event such as a breakpoint or through the \f3suspend\fR command, then local variables and fields can be displayed with the \f3print\fR and \f3dump\fR commands\&. The \f3up\fR and \f3down\fR commands select which stack frame is the current stack frame\&.
.SS BREAKPOINTS    
Breakpoints can be set in JDB at line numbers or at the first instruction of a method, for example:
.TP 0.2i    
\(bu
The command \f3stop at MyClass:22\fR sets a breakpoint at the first instruction for line 22 of the source file containing \f3MyClass\fR\&.
.TP 0.2i    
\(bu
The command \f3stop in java\&.lang\&.String\&.length\fR sets a breakpoint at the beginning of the method \f3java\&.lang\&.String\&.length\fR\&.
.TP 0.2i    
\(bu
The command \f3stop in MyClass\&.<clinit>\fR uses \f3<clinit>\fR to identify the static initialization code for \f3MyClass\fR\&.
.PP
When a method is overloaded, you must also specify its argument types so that the proper method can be selected for a breakpoint\&. For example, \f3MyClass\&.myMethod(int,java\&.lang\&.String)\fR or \f3MyClass\&.myMethod()\fR\&.
.PP
The \f3clear\fR command removes breakpoints using the following syntax: \f3clear MyClass:45\fR\&. Using the \f3clear\fR or \f3stop\fR command with no argument displays a list of all breakpoints currently set\&. The \f3cont\fR command continues execution\&.
.SS STEPPING    
The \f3step\fR command advances execution to the next line whether it is in the current stack frame or a called method\&. The \f3next\fR command advances execution to the next line in the current stack frame\&.
.SS EXCEPTIONS    
When an exception occurs for which there is not a \f3catch\fR statement anywhere in the throwing thread\&'s call stack, the JVM typically prints an exception trace and exits\&. When running under JDB, however, control returns to JDB at the offending throw\&. You can then use the \f3jdb\fR command to diagnose the cause of the exception\&.
.PP
Use the \f3catch\fR command to cause the debugged application to stop at other thrown exceptions, for example: \f3catch java\&.io\&.FileNotFoundException\fR or \f3catch\fR\f3mypackage\&.BigTroubleException\fR\&. Any exception that is an instance of the specified class or subclass stops the application at the point where it is thrown\&.
.PP
The \f3ignore\fR command negates the effect of an earlier \f3catch\fR command\&. The \f3ignore\fR command does not cause the debugged JVM to ignore specific exceptions, but only to ignore the debugger\&.
.SH OPTIONS    
When you use the \f3jdb\fR command instead of the \f3java\fR command on the command line, the \f3jdb\fR command accepts many of the same options as the \f3java\fR command, including \f3-D\fR, \f3-classpath\fR, and \f3-X\fR options\&. The following list contains additional options that are accepted by the \f3jdb\fR command\&.
.PP
Other options are supported to provide alternate mechanisms for connecting the debugger to the JVM it is to debug\&. For additional documentation about these connection alternatives, see Java Platform Debugger Architecture (JPDA) at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/jpda/index\&.html
.TP
-help
.br
Displays a help message\&.
.TP
-sourcepath \fIdir1:dir2: \&. \&. \&.\fR
.br
Uses the specified path to search for source files in the specified path\&. If this option is not specified, then use the default path of dot (\&.)\&.
.TP
-attach \fIaddress\fR
.br
Attaches the debugger to a running JVM with the default connection mechanism\&.
.TP
-listen \fIaddress\fR
.br
Waits for a running JVM to connect to the specified address with a standard connector\&.
.TP
-launch
.br
Starts the debugged application immediately upon startup of JDB\&. The \f3-launch\fR option removes the need for the \f3run\fR command\&. The debugged application is launched and then stopped just before the initial application class is loaded\&. At that point, you can set any necessary breakpoints and use the \f3cont\fR command to continue execution\&.
.TP
-listconnectors
.br
List the connectors available in this JVM\&.
.TP
-connect connector-name:\fIname1=value1\fR
.br
Connects to the target JVM with the named connector and listed argument values\&.
.TP
-dbgtrace [\fIflags\fR]
.br
Prints information for debugging the \f3jdb\fR command\&.
.TP
-tclient
.br
Runs the application in the Java HotSpot VM client\&.
.TP
-tserver
.br
Runs the application in the Java HotSpot VM server\&.
.TP
-J\fIoption\fR
.br
Passes \f3option\fR to the JVM, where option is one of the options described on the reference page for the Java application launcher\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&. See java(1)\&.
.SH OPTIONS\ FORWARDED\ TO\ THE\ DEBUGGER\ PROCESS    
.TP
-v -verbose[:\fIclass\fR|gc|jni]
.br
Turns on verbose mode\&.
.TP
-D\fIname\fR=\fIvalue\fR
.br
Sets a system property\&.
.TP
-classpath \fIdir\fR
.br
Lists directories separated by colons in which to look for classes\&.
.TP
-X\fIoption\fR
.br
Nonstandard target JVM option\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
javac(1)
.TP 0.2i    
\(bu
java(1)
.TP 0.2i    
\(bu
javah(1)
.TP 0.2i    
\(bu
javap(1)
.RE
.br
'pl 8.5i
'bp
