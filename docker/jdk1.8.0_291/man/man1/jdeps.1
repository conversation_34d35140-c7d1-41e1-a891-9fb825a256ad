'\" t
.\"  Copyright (c) 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Basic Tools
.\"     Title: jdeps.1
.\"
.if n .pl 99999
.TH jdeps 1 "21 November 2013" "JDK 8" "Basic Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
jdeps \- Java class dependency analyzer\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjdeps\fR [\fIoptions\fR] \fIclasses\fR \&.\&.\&.
.fi     
.sp     
.TP     
\fIoptions\fR
Command-line options\&. See Options\&.
.TP     
\fIclasses\fR
Name of the classes to analyze\&. You can specify a class that can be found in the class path, by its file name, a directory, or a JAR file\&.
.SH DESCRIPTION    
The \fI\fR\f3jdeps\fR command shows the package-level or class-level dependencies of Java class files\&. The input class can be a path name to a \f3\&.class\fR file, a directory, a JAR file, or it can be a fully qualified class name to analyze all class files\&. The options determine the output\&. By default, \f3jdeps\fR outputs the dependencies to the system output\&. It can generate the dependencies in DOT language (see the \f3-dotoutput\fR option)\&.
.SH OPTIONS    
.TP
-dotoutput <\fIdir\fR>
.br
Destination directory for DOT file output\&. If specified, \f3jdeps\fR will generate one dot file per each analyzed archive named <\fIarchive-file-name\fR>\&.dot listing the dependencies, and also a summary file named summary\&.dot listing the dependencies among the archives\&.
.TP
-s, -summary
.br
Prints dependency summary only\&.
.TP
-v, -verbose
.br
Prints all class-level dependencies\&.
.TP
-verbose:package
.br
Prints package-level dependencies excluding dependencies within the same archive\&.
.TP
-verbose:class
.br
Prints class-level dependencies excluding dependencies within the same archive\&.
.TP
-cp <\fIpath\fR>, -classpath <\fIpath\fR>
.br
Specifies where to find class files\&.

See also Setting the Class Path\&.
.TP
-p <\fIpkg name\fR>, -package <\fIpkg name\fR>
.br
Finds dependencies in the specified package\&. You can specify this option multiple times for different packages\&. The \f3-p\fR and \f3-e\fR options are mutually exclusive\&.
.TP
-e <\fIregex\fR>, -regex <\fIregex\fR>
.br
Finds dependencies in packages matching the specified regular expression pattern\&. The \f3-p\fR and \f3-e\fR options are mutually exclusive\&.
.TP
-include <\fIregex\fR>
.br
Restricts analysis to classes matching pattern\&. This option filters the list of classes to be analyzed\&. It can be used together with \f3-p\fR and \f3-e\fR which apply pattern to the dependencies\&.
.TP
-jdkinternals
.br
Finds class-level dependences in JDK internal APIs\&. By default, it analyzes all classes specified in the \f3-classpath\fR option and in input files unless you specified the \f3-include\fR option\&. You cannot use this option with the \f3-p\fR, \f3-e\fR, and \f3-s\fR options\&.

\fIWarning\fR: JDK internal APIs may not be accessible in upcoming releases\&.
.TP
-P, -profile
.br
Shows profile or the file containing a package\&.
.TP
-apionly
.br
Restricts analysis to APIs, for example, dependences from the signature of \f3public\fR and \f3protected\fR members of public classes including field type, method parameter types, returned type, and checked exception types\&.
.TP
-R, -recursive
.br
Recursively traverses all dependencies\&.
.TP
-version
.br
Prints version information\&.
.TP
-h, -?, -help
.br
Prints help message for \f3jdeps\fR\&.
.SH EXAMPLES    
Analyzing the dependencies of Notepad\&.jar\&.
.sp     
.nf     
\f3$ jdeps demo/jfc/Notepad/Notepad\&.jar\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3demo/jfc/Notepad/Notepad\&.jar \-> /usr/java/jre/lib/rt\&.jar\fP
.fi     
.nf     
\f3   <unnamed> (Notepad\&.jar)\fP
.fi     
.nf     
\f3      \-> java\&.awt                                           \fP
.fi     
.nf     
\f3      \-> java\&.awt\&.event                                     \fP
.fi     
.nf     
\f3      \-> java\&.beans                                         \fP
.fi     
.nf     
\f3      \-> java\&.io                                            \fP
.fi     
.nf     
\f3      \-> java\&.lang                                          \fP
.fi     
.nf     
\f3      \-> java\&.net                                           \fP
.fi     
.nf     
\f3      \-> java\&.util                                          \fP
.fi     
.nf     
\f3      \-> java\&.util\&.logging                                  \fP
.fi     
.nf     
\f3      \-> javax\&.swing                                        \fP
.fi     
.nf     
\f3      \-> javax\&.swing\&.border                                 \fP
.fi     
.nf     
\f3      \-> javax\&.swing\&.event                                  \fP
.fi     
.nf     
\f3      \-> javax\&.swing\&.text                                   \fP
.fi     
.nf     
\f3      \-> javax\&.swing\&.tree                                   \fP
.fi     
.nf     
\f3      \-> javax\&.swing\&.undo  \fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Use -P or -profile option to show on which profile that Notepad depends\&.
.sp     
.nf     
\f3$ jdeps \-profile demo/jfc/Notepad/Notepad\&.jar \fP
.fi     
.nf     
\f3demo/jfc/Notepad/Notepad\&.jar \-> /usr/java/jre/lib/rt\&.jar (Full JRE)\fP
.fi     
.nf     
\f3   <unnamed> (Notepad\&.jar)\fP
.fi     
.nf     
\f3      \-> java\&.awt                                           Full JRE\fP
.fi     
.nf     
\f3      \-> java\&.awt\&.event                                     Full JRE\fP
.fi     
.nf     
\f3      \-> java\&.beans                                         Full JRE\fP
.fi     
.nf     
\f3      \-> java\&.io                                            compact1\fP
.fi     
.nf     
\f3      \-> java\&.lang                                          compact1\fP
.fi     
.nf     
\f3      \-> java\&.net                                           compact1\fP
.fi     
.nf     
\f3      \-> java\&.util                                          compact1\fP
.fi     
.nf     
\f3      \-> java\&.util\&.logging                                  compact1\fP
.fi     
.nf     
\f3      \-> javax\&.swing                                        Full JRE\fP
.fi     
.nf     
\f3      \-> javax\&.swing\&.border                                 Full JRE\fP
.fi     
.nf     
\f3      \-> javax\&.swing\&.event                                  Full JRE\fP
.fi     
.nf     
\f3      \-> javax\&.swing\&.text                                   Full JRE\fP
.fi     
.nf     
\f3      \-> javax\&.swing\&.tree                                   Full JRE\fP
.fi     
.nf     
\f3      \-> javax\&.swing\&.undo                                   Full JRE\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Analyzing the immediate dependencies of a specific class in a given classpath, for example the \f3com\&.sun\&.tools\&.jdeps\&.Main\fR class in the tools\&.jar file\&.
.sp     
.nf     
\f3$ jdeps \-cp lib/tools\&.jar com\&.sun\&.tools\&.jdeps\&.Main\fP
.fi     
.nf     
\f3lib/tools\&.jar \-> /usr/java/jre/lib/rt\&.jar\fP
.fi     
.nf     
\f3   com\&.sun\&.tools\&.jdeps (tools\&.jar)\fP
.fi     
.nf     
\f3      \-> java\&.io                                            \fP
.fi     
.nf     
\f3      \-> java\&.lang \fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Use the \f3-verbose:class\fR option to find class-level dependencies or use the \f3-v\fR or \f3-verbose\fR option to include dependencies from the same JAR file\&.
.sp     
.nf     
\f3$ jdeps \-verbose:class \-cp lib/tools\&.jar com\&.sun\&.tools\&.jdeps\&.Main\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3lib/tools\&.jar \-> /usr/java/jre/lib/rt\&.jar\fP
.fi     
.nf     
\f3   com\&.sun\&.tools\&.jdeps\&.Main (tools\&.jar)\fP
.fi     
.nf     
\f3      \-> java\&.io\&.PrintWriter                                \fP
.fi     
.nf     
\f3      \-> java\&.lang\&.Exception                                \fP
.fi     
.nf     
\f3      \-> java\&.lang\&.Object                                   \fP
.fi     
.nf     
\f3      \-> java\&.lang\&.String                                   \fP
.fi     
.nf     
\f3      \-> java\&.lang\&.System \fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Use the \f3-R\fR or \f3-recursive\fR option to analyze the transitive dependencies of the \f3com\&.sun\&.tools\&.jdeps\&.Main\fR class\&.
.sp     
.nf     
\f3$ jdeps \-R \-cp lib/tools\&.jar com\&.sun\&.tools\&.jdeps\&.Main\fP
.fi     
.nf     
\f3lib/tools\&.jar \-> /usr/java/jre/lib/rt\&.jar\fP
.fi     
.nf     
\f3   com\&.sun\&.tools\&.classfile (tools\&.jar)\fP
.fi     
.nf     
\f3      \-> java\&.io                                            \fP
.fi     
.nf     
\f3      \-> java\&.lang                                          \fP
.fi     
.nf     
\f3      \-> java\&.lang\&.reflect                                  \fP
.fi     
.nf     
\f3      \-> java\&.nio\&.charset                                   \fP
.fi     
.nf     
\f3      \-> java\&.nio\&.file                                      \fP
.fi     
.nf     
\f3      \-> java\&.util                                          \fP
.fi     
.nf     
\f3      \-> java\&.util\&.regex                                    \fP
.fi     
.nf     
\f3   com\&.sun\&.tools\&.jdeps (tools\&.jar)\fP
.fi     
.nf     
\f3      \-> java\&.io                                            \fP
.fi     
.nf     
\f3      \-> java\&.lang                                          \fP
.fi     
.nf     
\f3      \-> java\&.nio\&.file                                      \fP
.fi     
.nf     
\f3      \-> java\&.nio\&.file\&.attribute                            \fP
.fi     
.nf     
\f3      \-> java\&.text                                          \fP
.fi     
.nf     
\f3      \-> java\&.util                                          \fP
.fi     
.nf     
\f3      \-> java\&.util\&.jar                                      \fP
.fi     
.nf     
\f3      \-> java\&.util\&.regex                                    \fP
.fi     
.nf     
\f3      \-> java\&.util\&.zip                                      \fP
.fi     
.nf     
\f3/usr/java/jre/lib/jce\&.jar \-> /usr/java/jre/lib/rt\&.jar\fP
.fi     
.nf     
\f3   javax\&.crypto (jce\&.jar)\fP
.fi     
.nf     
\f3      \-> java\&.io                                            \fP
.fi     
.nf     
\f3      \-> java\&.lang                                          \fP
.fi     
.nf     
\f3      \-> java\&.lang\&.reflect                                  \fP
.fi     
.nf     
\f3      \-> java\&.net                                           \fP
.fi     
.nf     
\f3      \-> java\&.nio                                           \fP
.fi     
.nf     
\f3      \-> java\&.security                                      \fP
.fi     
.nf     
\f3      \-> java\&.security\&.cert                                 \fP
.fi     
.nf     
\f3      \-> java\&.security\&.spec                                 \fP
.fi     
.nf     
\f3      \-> java\&.util                                          \fP
.fi     
.nf     
\f3      \-> java\&.util\&.concurrent                               \fP
.fi     
.nf     
\f3      \-> java\&.util\&.jar                                      \fP
.fi     
.nf     
\f3      \-> java\&.util\&.regex                                    \fP
.fi     
.nf     
\f3      \-> java\&.util\&.zip                                      \fP
.fi     
.nf     
\f3      \-> javax\&.security\&.auth                                \fP
.fi     
.nf     
\f3      \-> sun\&.security\&.jca                                   JDK internal API (rt\&.jar)\fP
.fi     
.nf     
\f3      \-> sun\&.security\&.util                                  JDK internal API (rt\&.jar)\fP
.fi     
.nf     
\f3   javax\&.crypto\&.spec (jce\&.jar)\fP
.fi     
.nf     
\f3      \-> java\&.lang                                          \fP
.fi     
.nf     
\f3      \-> java\&.security\&.spec                                 \fP
.fi     
.nf     
\f3      \-> java\&.util                                          \fP
.fi     
.nf     
\f3/usr/java/jre/lib/rt\&.jar \-> /usr/java/jre/lib/jce\&.jar\fP
.fi     
.nf     
\f3   java\&.security (rt\&.jar)\fP
.fi     
.nf     
\f3      \-> javax\&.crypto\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Generate dot files of the dependencies of Notepad demo\&.
.sp     
.nf     
\f3$ jdeps \-dotoutput dot demo/jfc/Notepad/Notepad\&.jar\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\f3jdeps\fR will create one dot file for each given JAR file named <\fIfilename\fR>\&.dot in the dot directory specified in the \f3-dotoutput\fR option, and also a summary file named summary\&.dot that will list the dependencies among the JAR files
.sp     
.nf     
\f3$ cat dot/Notepad\&.jar\&.dot \fP
.fi     
.nf     
\f3digraph "Notepad\&.jar" {\fP
.fi     
.nf     
\f3    // Path: demo/jfc/Notepad/Notepad\&.jar\fP
.fi     
.nf     
\f3   "<unnamed>"                                        \-> "java\&.awt";\fP
.fi     
.nf     
\f3   "<unnamed>"                                        \-> "java\&.awt\&.event";\fP
.fi     
.nf     
\f3   "<unnamed>"                                        \-> "java\&.beans";\fP
.fi     
.nf     
\f3   "<unnamed>"                                        \-> "java\&.io";\fP
.fi     
.nf     
\f3   "<unnamed>"                                        \-> "java\&.lang";\fP
.fi     
.nf     
\f3   "<unnamed>"                                        \-> "java\&.net";\fP
.fi     
.nf     
\f3   "<unnamed>"                                        \-> "java\&.util";\fP
.fi     
.nf     
\f3   "<unnamed>"                                        \-> "java\&.util\&.logging";\fP
.fi     
.nf     
\f3   "<unnamed>"                                        \-> "javax\&.swing";\fP
.fi     
.nf     
\f3   "<unnamed>"                                        \-> "javax\&.swing\&.border";\fP
.fi     
.nf     
\f3   "<unnamed>"                                        \-> "javax\&.swing\&.event";\fP
.fi     
.nf     
\f3   "<unnamed>"                                        \-> "javax\&.swing\&.text";\fP
.fi     
.nf     
\f3   "<unnamed>"                                        \-> "javax\&.swing\&.tree";\fP
.fi     
.nf     
\f3   "<unnamed>"                                        \-> "javax\&.swing\&.undo";\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3$ cat dot/summary\&.dot\fP
.fi     
.nf     
\f3digraph "summary" {\fP
.fi     
.nf     
\f3   "Notepad\&.jar"                  \-> "rt\&.jar";\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
javap(1)
.RE
.br
'pl 8.5i
'bp
