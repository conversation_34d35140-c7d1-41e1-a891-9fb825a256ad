'\" t
.\"  Copyright (c) 2006, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Troubleshooting Tools
.\"     Title: jhat.1
.\"
.if n .pl 99999
.TH jhat 1 "21 November 2013" "JDK 8" "Troubleshooting Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
jhat \- Analyzes the Java heap\&. This command is experimental and unsupported\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjhat\fR [ \fIoptions\fR ] \fIheap\-dump\-file\fR 
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
\fIheap-dump-file\fR
Java binary heap dump file to be browsed\&. For a dump file that contains multiple heap dumps, you can specify which dump in the file by appending \f3#<number>\fR to the file name, for example, \f3myfile\&.hprof#3\fR\&.
.SH DESCRIPTION    
The \f3jhat\fR command parses a Java heap dump file and starts a web server\&. The \f3jhat\fR command lets you to browse heap dumps with your favorite web browser\&. The \f3jhat\fR command supports predesigned queries such as show all instances of a known class \f3MyClass\fR, and Object Query Language (OQL)\&. OQL is similar to SQL, except for querying heap dumps\&. Help on OQL is available from the OQL help page shown by the \f3jhat\fR command\&. With the default port, OQL help is available at http://localhost:7000/oqlhelp/
.PP
There are several ways to generate a Java heap dump:
.TP 0.2i    
\(bu
Use the \f3jmap -dump\fR option to obtain a heap dump at runtime\&. See jmap(1)\&.
.TP 0.2i    
\(bu
Use the \f3jconsole\fR option to obtain a heap dump through \f3HotSpotDiagnosticMXBean\fR at runtime\&. See jconsole(1) and the \f3HotSpotDiagnosticMXBean\fR interface description at http://docs\&.oracle\&.com/javase/8/docs/jre/api/management/extension/com/sun/management/HotSpotDiagnosticMXBean\&.html
.TP 0.2i    
\(bu
Heap dump is generated when an \f3OutOfMemoryError\fR is thrown by specifying the \f3-XX:+HeapDumpOnOutOfMemoryError\fR Java Virtual Machine (JVM) option\&.
.TP 0.2i    
\(bu
Use the \f3hprof\fR command\&. See the HPROF: A Heap/CPU Profiling Tool at http://docs\&.oracle\&.com/javase/8/docs/technotes/samples/hprof\&.html
.SH OPTIONS    
.TP
-stack false|true
.br
Turns off tracking object allocation call stack\&. If allocation site information is not available in the heap dump, then you have to set this flag to \f3false\fR\&. The default is \f3true\fR\&.
.TP
-refs false|true
.br
Turns off tracking of references to objects\&. Default is \f3true\fR\&. By default, back pointers, which are objects that point to a specified object such as referrers or incoming references, are calculated for all objects in the heap\&.
.TP
-port \fIport-number\fR
.br
Sets the port for the \f3jhat\fR HTTP server\&. Default is 7000\&.
.TP
-exclude \fIexclude-file\fR
.br
Specifies a file that lists data members that should be excluded from the reachable objects query\&. For example, if the file lists \f3java\&.lang\&.String\&.value\fR, then, then whenever the list of objects that are reachable from a specific object \f3o\fR are calculated, reference paths that involve \f3java\&.lang\&.String\&.value\fR field are not considered\&.
.TP
-baseline \fIexclude-file\fR
.br
Specifies a baseline heap dump\&. Objects in both heap dumps with the same object ID are marked as not being new\&. Other objects are marked as new\&. This is useful for comparing two different heap dumps\&.
.TP
-debug \fIint\fR
.br
Sets the debug level for this tool\&. A level of 0 means no debug output\&. Set higher values for more verbose modes\&.
.TP
-version
.br
Reports the release number and exits
.TP
-h
.br
Dsiplays a help message and exits\&.
.TP
-help
.br
Displays a help message and exits\&.
.TP
-J\fIflag\fR
.br
Passes \f3flag\fR to the Java Virtual Machine on which the \f3jhat\fR command is running\&. For example, \f3-J-Xmx512m\fR to use a maximum heap size of 512 MB\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
jmap(1)
.TP 0.2i    
\(bu
jconsole(1)
.TP 0.2i    
\(bu
HPROF: A Heap/CPU Profiling Tool at http://docs\&.oracle\&.com/javase/8/docs/technotes/samples/hprof\&.html
.RE
.br
'pl 8.5i
'bp
