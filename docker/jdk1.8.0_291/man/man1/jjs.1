'\" t
.\" Copyright (c) 1994, 2015, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jjs
.\" Language: English
.\" Date: 03 March 2015
.\" SectDesc: Basic Tools
.\" Software: JDK 8
.\" Arch: generic
.\" Part Number: E38209-04
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jjs" "1" "03 March 2015" "JDK 8" "Basic Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
jjs \- Invokes the Nashorn engine\&.
.SH "SYNOPSIS"
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\fBjjs\fR\fR\fB [\fR\fB\fIoptions\fR\fR\fB] [\fR\fB\fIscript\-files\fR\fR\fB] [\-\- \fR\fB\fIarguments\fR\fR\fB]\fR
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
One or more options of the
\fBjjs\fR
command, separated by spaces\&. For more information, see Options\&.
.RE
.PP
\fIscript\-files\fR
.RS 4
One or more script files which you want to interpret using Nashorn, separated by spaces\&. If no files are specified, an interactive shell is started\&.
.RE
.PP
\fIarguments\fR
.RS 4
All values after the double hyphen marker (\fB\-\-\fR) are passed through to the script or the interactive shell as arguments\&. These values can be accessed by using the
\fBarguments\fR
property (see Example 3)\&.
.RE
.SH "DESCRIPTION"
.PP
The
\fBjjs\fR
command\-line tool is used to invoke the Nashorn engine\&. You can use it to interpret one or several script files, or to run an interactive shell\&.
.SH "OPTIONS"
.PP
The options of the
\fBjjs\fR
command control the conditions under which scripts are interpreted by Nashorn\&.
.PP
\-cp \fIpath\fR
.br
\-classpath \fIpath\fR
.RS 4
Specifies the path to the supporting class files To set multiple paths, the option can be repeated, or you can separate each path with a colon (:)\&.
.RE
.PP
\-D\fIname\fR=\fIvalue\fR
.RS 4
Sets a system property to be passed to the script by assigning a value to a property name\&. The following example shows how to invoke Nashorn in interactive mode and assign
\fBmyValue\fR
to the property named
\fBmyKey\fR:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB>> \fR\fB\fBjjs \-DmyKey=myValue\fR\fR
\fBjjs> \fR\fB\fBjava\&.lang\&.System\&.getProperty("myKey")\fR\fR
\fBmyValue\fR
\fBjjs>\fR
 
.fi
.if n \{\
.RE
.\}
This option can be repeated to set multiple properties\&.
.RE
.PP
\-doe
.br
\-\-dump\-on\-error
.RS 4
Provides a full stack trace when an error occurs\&. By default, only a brief error message is printed\&.
.RE
.PP
\-fv
.br
\-\-fullversion
.RS 4
Prints the full Nashorn version string\&.
.RE
.PP
\-fx
.RS 4
Launches the script as a JavaFX application\&.
.RE
.PP
\-h
.br
\-help
.RS 4
Prints the list of options and their descriptions\&.
.RE
.PP
\-\-language=[es5]
.RS 4
Specifies the ECMAScript language version\&. The default version is ES5\&.
.RE
.PP
\-ot
.br
\-\-optimistic\-types=[true|false]
.RS 4
Enables or disables optimistic type assumptions with deoptimizing recompilation\&. Running with optimistic types will yield higher final speed, but may increase warmup time\&.
.RE
.PP
\-scripting
.RS 4
Enables shell scripting features\&.
.RE
.PP
\-strict
.RS 4
Enables strict mode, which enforces stronger adherence to the standard (ECMAScript Edition 5\&.1), making it easier to detect common coding errors\&.
.RE
.PP
\-t=\fIzone\fR
.br
\-timezone=\fIzone\fR
.RS 4
Sets the specified time zone for script execution\&. It overrides the time zone set in the OS and used by the
\fBDate\fR
object\&.
.RE
.PP
\-v
.br
\-version
.RS 4
Prints the Nashorn version string\&.
.RE
.SH "EXAMPLES"
.PP
\fBExample 1 \fRRunning a Script with Nashorn
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjjs script\&.js\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fBExample 2 \fRRunning Nashorn in Interactive Mode
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB>> \fR\fB\fBjjs\fR\fR
\fBjjs> \fR\fB\fBprintln("Hello, World!")\fR\fR
\fBHello, World!\fR
\fBjjs> \fR\fB\fBquit()\fR\fR
\fB>>\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fBExample 3 \fRPassing Arguments to Nashorn
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fB>> \fR\fB\fBjjs \-\- a b c\fR\fR
\fBjjs> \fR\fB\fBarguments\&.join(", ")\fR\fR
\fBa, b, c\fR
\fBjjs>\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.SH "SEE ALSO"
.PP
\fBjrunscript\fR
.br
'pl 8.5i
'bp
