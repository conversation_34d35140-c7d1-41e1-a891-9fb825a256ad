'\" t
.\"  Copyright (c) 2006, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Scripting Tools
.\"     Title: jrunscript.1
.\"
.if n .pl 99999
.TH jrunscript 1 "21 November 2013" "JDK 8" "Scripting Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
jrunscript \- Runs a command-line script shell that supports interactive and batch modes\&. This command is experimental and unsupported\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjrunscript\fR [\fIoptions\fR] [\fIarguments\fR]
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
\fIarguments\fR
Arguments, when used, follow immediately after options or the command name\&. See Arguments\&.
.SH DESCRIPTION    
The \f3jrunscript\fR command is a language-independent command-line script shell\&. The \f3jrunscript\fR command supports both an interactive (read-eval-print) mode and a batch (\f3-f\fR option) mode of script execution\&. By default, JavaScript is the language used, but the \f3-l\fR option can be used to specify a different language\&. By using Java to scripting language communication, the \f3jrunscript\fR command supports an exploratory programming style\&.
.SH OPTIONS    
.TP
-classpath \fIpath\fR
.br
Indicate where any class files are that the script needs to access\&.
.TP
-cp \fIpath\fR
.br
Same as \f3-classpath\fR\f3path\fR\&.
.TP
-D\fIname\fR=\fIvalue\fR
.br
Sets a Java system property\&.
.TP
-J\fIflag\fR
.br
Passes \f3flag\fR directly to the Java Virtual Machine where the \f3jrunscript\fR command is running\&.
.TP
-I \fIlanguage\fR
.br
Uses the specified scripting language\&. By default, JavaScript is used\&. To use other scripting languages, you must specify the corresponding script engine\&'s JAR file with the \f3-cp\fR or \f3-classpath\fR option\&.
.TP
-e \fIscript\fR
.br
Evaluates the specified script\&. This option can be used to run one-line scripts that are specified completely on the command line\&.
.TP
-encoding \fIencoding\fR
.br
Specifies the character encoding used to read script files\&.
.TP
-f \fIscript-file\fR
.br
Evaluates the specified script file (batch mode)\&.
.TP
-f -
.br
Reads and evaluates a script from standard input (interactive mode)\&.
.TP
-help
.br
Displays a help message and exits\&.
.TP
-?
.br
Displays a help message and exits\&.
.TP
-q
.br
Lists all script engines available and exits\&.
.SH ARGUMENTS    
If arguments are present and if no \f3-e\fR or \f3-f\fR option is used, then the first argument is the script file and the rest of the arguments, if any, are passed to the script\&. If arguments and \f3-e\fR or the \f3-f\fR option are used, then all arguments are passed to the script\&. If arguments, \f3-e\fR and \f3-f\fR are missing, then interactive mode is used\&. Script arguments are available to a script in an engine variable named \f3arguments\fR of type \f3String\fR array\&.
.SH EXAMPLES    
.SS EXECUTE\ INLINE\ SCRIPTS    
.sp     
.nf     
\f3jrunscript \-e "print(\&'hello world\&')"\fP
.fi     
.nf     
\f3jrunscript \-e "cat(\&'http://www\&.example\&.com\&')"\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS USE\ SPECIFIED\ LANGUAGE\ AND\ EVALUATE\ THE\ SCRIPT\ FILE    
.sp     
.nf     
\f3jrunscript \-l js \-f test\&.js\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS INTERACTIVE\ MODE    
.sp     
.nf     
\f3jrunscript\fP
.fi     
.nf     
\f3js> print(\&'Hello World\en\&');\fP
.fi     
.nf     
\f3Hello World\fP
.fi     
.nf     
\f3js> 34 + 55\fP
.fi     
.nf     
\f389\&.0\fP
.fi     
.nf     
\f3js> t = new java\&.lang\&.Thread(function() { print(\&'Hello World\en\&'); })\fP
.fi     
.nf     
\f3Thread[Thread\-0,5,main]\fP
.fi     
.nf     
\f3js> t\&.start()\fP
.fi     
.nf     
\f3js> Hello World\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3js>\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS RUN\ SCRIPT\ FILE\ WITH\ SCRIPT\ ARGUMENTS    
The test\&.js file is the script file\&. The \f3arg1\fR, \f3arg2\fR and \f3arg3\fR arguments are passed to the script\&. The script can access these arguments with an arguments array\&.
.sp     
.nf     
\f3jrunscript test\&.js arg1 arg2 arg3\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH SEE\ ALSO    
If JavaScript is used, then before it evaluates a user defined script, the \f3jrunscript\fR command initializes certain built-in functions and objects\&. These JavaScript built-ins are documented in JsDoc-Toolkit at http://code\&.google\&.com/p/jsdoc-toolkit/
.RE
.br
'pl 8.5i
'bp
