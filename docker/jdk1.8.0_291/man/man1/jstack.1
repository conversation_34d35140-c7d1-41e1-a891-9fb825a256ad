'\" t
.\"  Copyright (c) 2004, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Troubleshooting Tools
.\"     Title: jstack.1
.\"
.if n .pl 99999
.TH jstack 1 "21 November 2013" "JDK 8" "Troubleshooting Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
jstack \- Prints Java thread stack traces for a Java process, core file, or remote debug server\&. This command is experimental and unsupported\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjstack\fR [ \fIoptions\fR ] \fIpid\fR 
.fi     
.nf     

\fBjstack\fR [ \fIoptions\fR ] \fIexecutable\fR \fIcore\fR
.fi     
.nf     

\fBjstack\fR [ \fIoptions\fR ] [ \fIserver\-id\fR@ ] \fIremote\-hostname\-or\-IP\fR
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
\fIpid\fR
The process ID for which the stack trace is printed\&. The process must be a Java process\&. To get a list of Java processes running on a machine, use the jps(1) command\&.
.TP     
\fIexecutable\fR
The Java executable from which the core dump was produced\&.
.TP     
\fIcore\fR
The core file for which the stack trace is to be printed\&.
.TP     
\fIremote-hostname-or-IP\fR
The remote debug server \f3hostname\fR or \f3IP\fR address\&. See jsadebugd(1)\&.
.TP     
\fIserver-id\fR
An optional unique ID to use when multiple debug servers are running on the same remote host\&.
.SH DESCRIPTION    
The \f3jstack\fR command prints Java stack traces of Java threads for a specified Java process, core file, or remote debug server\&. For each Java frame, the full class name, method name, byte code index (BCI), and line number, when available, are printed\&. With the \f3-m\fR option, the \f3jstack\fR command prints both Java and native frames of all threads with the program counter (PC)\&. For each native frame, the closest native symbol to PC, when available, is printed\&. C++ mangled names are not demangled\&. To demangle C++ names, the output of this command can be piped to \f3c++filt\fR\&. When the specified process is running on a 64-bit Java Virtual Machine, you might need to specify the \f3-J-d64\fR option, for example: \f3jstack -J-d64 -m pid\fR\&.
.PP
\fINote:\fR This utility is unsupported and might not be available in future release of the JDK\&. In Windows Systems where the dbgeng\&.dll file is not present, Debugging Tools For Windows must be installed so these tools work\&. The \f3PATH\fR environment variable needs to contain the location of the jvm\&.dll that is used by the target process, or the location from which the crash dump file was produced\&. For example:
.sp     
.nf     
\f3set PATH=<jdk>\ejre\ebin\eclient;%PATH%\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH OPTIONS    
.TP
-F
.br
Force a stack dump when \f3jstack\fR [\f3-l\fR] \f3pid\fR does not respond\&.
.TP
-l
.br
Long listing\&. Prints additional information about locks such as a list of owned \f3java\&.util\&.concurrent\fR ownable synchronizers\&. See the \f3AbstractOwnableSynchronizer\fR class description at http://docs\&.oracle\&.com/javase/8/docs/api/java/util/concurrent/locks/AbstractOwnableSynchronizer\&.html
.TP
-m
.br
Prints a mixed mode stack trace that has both Java and native C/C++ frames\&.
.TP
-h
.br
Prints a help message\&.
.TP
-help
.br
Prints a help message\&.
.SH KNOWN\ BUGS    
In mixed mode stack trace, the \f3-m\fR option does not work with the remote debug server\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
pstack(1)
.TP 0.2i    
\(bu
C++filt(1)
.TP 0.2i    
\(bu
jps(1)
.TP 0.2i    
\(bu
jsadebugd(1)
.RE
.br
'pl 8.5i
'bp
