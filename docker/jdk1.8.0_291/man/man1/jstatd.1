'\" t
.\"  Copyright (c) 2004, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Monitoring Tools
.\"     Title: jstatd.1
.\"
.if n .pl 99999
.TH jstatd 1 "21 November 2013" "JDK 8" "Monitoring Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
jstatd \- Monitors Java Virtual Machines (JVMs) and enables remote monitoring tools to attach to JVMs\&. This command is experimental and unsupported\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjstatd\fR [ \fIoptions\fR ]
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.SH DESCRIPTION    
The \f3jstatd\fR command is an RMI server application that monitors for the creation and termination of instrumented Java HotSpot VMs and provides an interface to enable remote monitoring tools to attach to JVMs that are running on the local host\&.
.PP
The \f3jstatd\fR server requires an RMI registry on the local host\&. The \f3jstatd\fR server attempts to attach to the RMI registry on the default port, or on the port you specify with the \f3-p\fR\f3port\fR option\&. If an RMI registry is not found, then one is created within the \f3jstatd\fR application that is bound to the port that is indicated by the \f3-p\fR\f3port\fR option or to the default RMI registry port when the \f3-p\fR\f3port\fR option is omitted\&. You can stop the creation of an internal RMI registry by specifying the \f3-nr\fR option\&.
.SH OPTIONS    
.TP
-nr
.br
Does not attempt to create an internal RMI registry within the \f3jstatd\fR process when an existing RMI registry is not found\&.
.TP
-p \fIport\fR
.br
The port number where the RMI registry is expected to be found, or when not found, created if the \f3-nr\fR option is not specified\&.
.TP
-n \fIrminame\fR
.br
Name to which the remote RMI object is bound in the RMI registry\&. The default name is \f3JStatRemoteHost\fR\&. If multiple \f3jstatd\fR servers are started on the same host, then the name of the exported RMI object for each server can be made unique by specifying this option\&. However, doing so requires that the unique server name be included in the monitoring client\&'s \f3hostid\fR and \f3vmid\fR strings\&.
.TP
-J\fIoption\fR
.br
Passes \f3option\fR to the JVM, where option is one of the \f3options\fR described on the reference page for the Java application launcher\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&. See java(1)\&.
.SH SECURITY    
The \f3jstatd\fR server can only monitor JVMs for which it has the appropriate native access permissions\&. Therefore, the \f3jstatd\fR process must be running with the same user credentials as the target JVMs\&. Some user credentials, such as the root user in UNIX-based systems, have permission to access the instrumentation exported by any JVM on the system\&. A \f3jstatd\fR process running with such credentials can monitor any JVM on the system, but introduces additional security concerns\&.
.PP
The \f3jstatd\fR server does not provide any authentication of remote clients\&. Therefore, running a \f3jstatd\fR server process exposes the instrumentation export by all JVMs for which the \f3jstatd\fR process has access permissions to any user on the network\&. This exposure might be undesirable in your environment, and therefore, local security policies should be considered before you start the \f3jstatd\fR process, particularly in production environments or on networks that are not secure\&.
.PP
The \f3jstatd\fR server installs an instance of \f3RMISecurityPolicy\fR when no other security manager is installed, and therefore, requires a security policy file to be specified\&. The policy file must conform to Default Policy Implementation and Policy File Syntax at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/PolicyFiles\&.html
.PP
The following policy file allows the \f3jstatd\fR server to run without any security exceptions\&. This policy is less liberal than granting all permissions to all code bases, but is more liberal than a policy that grants the minimal permissions to run the \f3jstatd\fR server\&.
.sp     
.nf     
\f3grant codebase "file:${java\&.home}/\&.\&./lib/tools\&.jar" {   \fP
.fi     
.nf     
\f3    permission java\&.security\&.AllPermission;\fP
.fi     
.nf     
\f3};\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
To use this policy setting, copy the text into a file called \f3jstatd\&.all\&.policy\fR and run the \f3jstatd\fR server as follows:
.sp     
.nf     
\f3jstatd \-J\-Djava\&.security\&.policy=jstatd\&.all\&.policy\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
For sites with more restrictive security practices, it is possible to use a custom policy file to limit access to specific trusted hosts or networks, though such techniques are subject to IP address spoofing attacks\&. If your security concerns cannot be addressed with a customized policy file, then the safest action is to not run the \f3jstatd\fR server and use the \f3jstat\fR and \f3jps\fR tools locally\&.
.SH REMOTE\ INTERFACE    
The interface exported by the \f3jstatd\fR process is proprietary and guaranteed to change\&. Users and developers are discouraged from writing to this interface\&.
.SH EXAMPLES    
The following are examples of the \f3jstatd\fR command\&. The \f3jstatd\fR scripts automatically start the server in the background
.SS INTERNAL\ RMI\ REGISTRY    
This example shows hos to start a \f3jstatd\fR session with an internal RMI registry\&. This example assumes that no other server is bound to the default RMI registry port (port 1099)\&.
.sp     
.nf     
\f3jstatd \-J\-Djava\&.security\&.policy=all\&.policy\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS EXTERNAL\ RMI\ REGISTRY    
This example starts a \f3jstatd\fR session with a external RMI registry\&.
.sp     
.nf     
\f3rmiregistry&\fP
.fi     
.nf     
\f3jstatd \-J\-Djava\&.security\&.policy=all\&.policy\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
This example starts a \f3jstatd\fR session with an external RMI registry server on port 2020\&.
.sp     
.nf     
\f3jrmiregistry 2020&\fP
.fi     
.nf     
\f3jstatd \-J\-Djava\&.security\&.policy=all\&.policy \-p 2020\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
This example starts a \f3jstatd\fR session with an external RMI registry on port 2020 that is bound to \f3AlternateJstatdServerName\fR\&.
.sp     
.nf     
\f3rmiregistry 2020&\fP
.fi     
.nf     
\f3jstatd \-J\-Djava\&.security\&.policy=all\&.policy \-p 2020\fP
.fi     
.nf     
\f3    \-n AlternateJstatdServerName\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS STOP\ THE\ CREATION\ OF\ AN\ IN-PROCESS\ RMI\ REGISTRY    
This example starts a \f3jstatd\fR session that does not create an RMI registry when one is not found\&. This example assumes an RMI registry is already running\&. If an RMI registry is not running, then an error message is displayed\&.
.sp     
.nf     
\f3jstatd \-J\-Djava\&.security\&.policy=all\&.policy \-nr\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS ENABLE\ RMI\ LOGGING    
This example starts a \f3jstatd\fR session with RMI logging capabilities enabled\&. This technique is useful as a troubleshooting aid or for monitoring server activities\&.
.sp     
.nf     
\f3jstatd \-J\-Djava\&.security\&.policy=all\&.policy\fP
.fi     
.nf     
\f3    \-J\-Djava\&.rmi\&.server\&.logCalls=true\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
java(1)
.TP 0.2i    
\(bu
jps(1)
.TP 0.2i    
\(bu
jstat(1)
.TP 0.2i    
\(bu
rmiregistry(1)
.RE
.br
'pl 8.5i
'bp
