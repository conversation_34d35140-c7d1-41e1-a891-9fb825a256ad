'\" t
.\"  Copyright (c) 1998, 2020, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 31 August 2020
.\"     SectDesc: Security Tools
.\"     Title: keytool.1
.\"
.if n .pl 99999
.TH keytool 1 "31 August 2020" "JDK 8" "Security Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
keytool \- Manages a keystore (database) of cryptographic keys, X\&.509 certificate chains, and trusted certificates\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBkeytool\fR [\fIcommands\fR]
.fi     
.sp     
.TP     
\fIcommands\fR
See Commands\&. These commands are categorized by task as follows:
.RS     
.TP 0.2i    
\(bu
Create or Add Data to the Keystore
.RS     
.TP 0.2i    
\(bu
-gencert
.TP 0.2i    
\(bu
-genkeypair
.TP 0.2i    
\(bu
-genseckey
.TP 0.2i    
\(bu
-importcert
.TP 0.2i    
\(bu
-importpassword
.RE     

.TP 0.2i    
\(bu
Import Contents From Another Keystore
.RS     
.TP 0.2i    
\(bu
-importkeystore
.RE     

.TP 0.2i    
\(bu
Generate Certificate Request
.RS     
.TP 0.2i    
\(bu
-certreq
.RE     

.TP 0.2i    
\(bu
Export Data
.RS     
.TP 0.2i    
\(bu
-exportcert
.RE     

.TP 0.2i    
\(bu
Display Data
.RS     
.TP 0.2i    
\(bu
-list
.TP 0.2i    
\(bu
-printcert
.TP 0.2i    
\(bu
-printcertreq
.TP 0.2i    
\(bu
-printcrl
.RE     

.TP 0.2i    
\(bu
Manage the Keystore
.RS     
.TP 0.2i    
\(bu
-storepasswd
.TP 0.2i    
\(bu
-keypasswd
.TP 0.2i    
\(bu
-delete
.TP 0.2i    
\(bu
-changealias
.RE     

.TP 0.2i    
\(bu
Get Help
.RS     
.TP 0.2i    
\(bu
-help
.RE     

.RE     

.SH DESCRIPTION    
The \f3keytool\fR command is a key and certificate management utility\&. It enables users to administer their own public/private key pairs and associated certificates for use in self-authentication (where the user authenticates himself or herself to other users and services) or data integrity and authentication services, using digital signatures\&. The \f3keytool\fR command also enables users to cache the public keys (in the form of certificates) of their communicating peers\&.
.PP
A certificate is a digitally signed statement from one entity (person, company, and so on\&.), that says that the public key (and some other information) of some other entity has a particular value\&. (See Certificate\&.) When data is digitally signed, the signature can be verified to check the data integrity and authenticity\&. Integrity means that the data has not been modified or tampered with, and authenticity means the data comes from whoever claims to have created and signed it\&.
.PP
The \f3keytool\fR command also enables users to administer secret keys and passphrases used in symmetric encryption and decryption (DES)\&.
.PP
The \f3keytool\fR command stores the keys and certificates in a keystore\&. See KeyStore aliases\&.
.SH COMMAND\ AND\ OPTION\ NOTES    
See Commands for a listing and description of the various commands\&.
.TP 0.2i    
\(bu
All command and option names are preceded by a minus sign (-)\&.
.TP 0.2i    
\(bu
The options for each command can be provided in any order\&.
.TP 0.2i    
\(bu
All items not italicized or in braces or brackets are required to appear as is\&.
.TP 0.2i    
\(bu
Braces surrounding an option signify that a default value will be used when the option is not specified on the command line\&. See Option Defaults\&. Braces are also used around the \f3-v\fR, \f3-rfc\fR, and \f3-J\fR options, which only have meaning when they appear on the command line\&. They do not have any default values other than not existing\&.
.TP 0.2i    
\(bu
Brackets surrounding an option signify that the user is prompted for the values when the option is not specified on the command line\&. For the \f3-keypass\fR option, if you do not specify the option on the command line, then the \f3keytool\fR command first attempts to use the keystore password to recover the private/secret key\&. If this attempt fails, then the \f3keytool\fR command prompts you for the private/secret key password\&.
.TP 0.2i    
\(bu
Items in italics (option values) represent the actual values that must be supplied\&. For example, here is the format of the \f3-printcert\fR command:
.sp     
.nf     
\f3keytool \-printcert {\-file \fIcert_file\fR} {\-v}\fP
.fi     
.sp     




When you specify a \f3-printcert\fR command, replace \fIcert_file\fR with the actual file name, as follows: \f3keytool -printcert -file VScert\&.cer\fR
.TP 0.2i    
\(bu
Option values must be put in quotation marks when they contain a blank (space)\&.
.TP 0.2i    
\(bu
The \f3-help\fR option is the default\&. The \f3keytool\fR command is the same as \f3keytool -help\fR\&.
.SH OPTION\ DEFAULTS    
The following examples show the defaults for various option values\&.
.sp     
.nf     
\f3\-alias "mykey"\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3\-keyalg\fP
.fi     
.nf     
\f3    "DSA" (when using \-genkeypair)\fP
.fi     
.nf     
\f3    "DES" (when using \-genseckey)\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3\-keysize\fP
.fi     
.nf     
\f3    2048 (when using \-genkeypair and \-keyalg is "RSA")\fP
.fi     
.nf     
\f3    1024 (when using \-genkeypair and \-keyalg is "DSA")\fP
.fi     
.nf     
\f3    256 (when using \-genkeypair and \-keyalg is "EC")\fP
.fi     
.nf     
\f3    56 (when using \-genseckey and \-keyalg is "DES")\fP
.fi     
.nf     
\f3    168 (when using \-genseckey and \-keyalg is "DESede")\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3\-validity 90\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3\-keystore <the file named \&.keystore in the user\&'s home directory>\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3\-storetype <the value of the "keystore\&.type" property in the\fP
.fi     
.nf     
\f3    security properties file, which is returned by the static\fP
.fi     
.nf     
\f3    getDefaultType method in java\&.security\&.KeyStore>\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3\-file\fP
.fi     
.nf     
\f3    stdin (if reading)\fP
.fi     
.nf     
\f3    stdout (if writing)\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3\-protected false\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
In generating a public/private key pair, the signature algorithm (\f3-sigalg\fR option) is derived from the algorithm of the underlying private key:
.TP 0.2i    
\(bu
If the underlying private key is of type DSA, then the \f3-sigalg\fR option defaults to SHA256withDSA\&.
.TP 0.2i    
\(bu
If the underlying private key is of type RSA, then the \f3-sigalg\fR option defaults to SHA256withRSA\&.
.TP 0.2i    
\(bu
If the underlying private key is of type EC, then the \f3-sigalg\fR option defaults to SHA256withECDSA\&.
.PP
For a full list of \f3-keyalg\fR and \f3-sigalg\fR arguments, see Java Cryptography Architecture (JCA) Reference Guide at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/crypto/CryptoSpec\&.html#AppA
.SH COMMON\ OPTIONS    
The \f3-v\fR option can appear for all commands except \f3-help\fR\&. When the \f3-v\fR option appears, it signifies verbose mode, which means that more information is provided in the output\&.
.PP
There is also a \f3-Jjavaoption\fR argument that can appear for any command\&. When the \f3-Jjavaoption\fR appears, the specified \f3javaoption\fR string is passed directly to the Java interpreter\&. This option does not contain any spaces\&. It is useful for adjusting the execution environment or memory usage\&. For a list of possible interpreter options, type \f3java -h\fR or \f3java -X\fR at the command line\&.
.PP
These options can appear for all commands operating on a keystore:
.TP
-storetype \fIstoretype\fR
.br
This qualifier specifies the type of keystore to be instantiated\&.
.TP
-keystore \fIkeystore\fR
.br
The keystore location\&.

If the JKS \f3storetype\fR is used and a keystore file does not yet exist, then certain \f3keytool\fR commands can result in a new keystore file being created\&. For example, if \f3keytool -genkeypair\fR is called and the \f3-keystore\fR option is not specified, the default keystore file named \f3\&.keystore\fR in the user\&'s home directory is created when it does not already exist\&. Similarly, if the \f3-keystore ks_file\fR option is specified but ks_file does not exist, then it is created\&. For more information on the JKS \f3storetype\fR, see the \fIKeyStore Implementation\fR section in KeyStore aliases\&.

Note that the input stream from the \f3-keystore\fR option is passed to the \f3KeyStore\&.load\fR method\&. If \f3NONE\fR is specified as the URL, then a null stream is passed to the \f3KeyStore\&.load\fR method\&. \f3NONE\fR should be specified if the keystore is not file-based\&. For example, when it resides on a hardware token device\&.
.TP
-storepass[:\fIenv\fR| :\fIfile\fR] argument
.br
The password that is used to protect the integrity of the keystore\&.

If the modifier \f3env\fR or \f3file\fR is not specified, then the password has the \f3value\fR argument, which must be at least 6 characters long\&. Otherwise, the password is retrieved as follows:
.RS     
.TP 0.2i    
\(bu
\f3env\fR: Retrieve the password from the environment variable named \f3argument\fR\&.
.TP 0.2i    
\(bu
\f3file\fR: Retrieve the password from the file named argument\&.
.RE     


\fINote:\fR All other options that require passwords, such as \f3-keypass\fR, \f3-srckeypass\fR, -\f3destkeypass\fR, \f3-srcstorepass\fR, and \f3-deststorepass\fR, accept the \fIenv\fR and \fIfile\fR modifiers\&. Remember to separate the password option and the modifier with a colon (:)\&.

The password must be provided to all commands that access the keystore contents\&. For such commands, when the \f3-storepass\fR option is not provided at the command line, the user is prompted for it\&.

When retrieving information from the keystore, the password is optional\&. If no password is specified, then the integrity of the retrieved information cannot be verified and a warning is displayed\&.
.TP
-providerName \fIprovider_name\fR
.br
Used to identify a cryptographic service provider\&'s name when listed in the security properties file\&.
.TP
-providerClass \fIprovider_class_name\fR
.br
Used to specify the name of a cryptographic service provider\&'s master class file when the service provider is not listed in the security properties file\&.
.TP
-providerArg \fIprovider_arg\fR
.br
Used with the \f3-providerClass\fR option to represent an optional string input argument for the constructor of \f3provider_class_name\fR\&.
.TP
-protected
.br
Either \f3true\fR or \f3false\fR\&. This value should be specified as \f3true\fR when a password must be specified by way of a protected authentication path such as a dedicated PIN reader\&.Because there are two keystores involved in the \f3-importkeystore\fR command, the following two options \f3-srcprotected\fR and -\f3destprotected\fR are provided for the source keystore and the destination keystore respectively\&.
.TP
-ext \fI{name{:critical} {=value}}\fR
.br
Denotes an X\&.509 certificate extension\&. The option can be used in \f3-genkeypair\fR and \f3-gencert\fR to embed extensions into the certificate generated, or in \f3-certreq\fR to show what extensions are requested in the certificate request\&. The option can appear multiple times\&. The \f3name\fR argument can be a supported extension name (see Named Extensions) or an arbitrary OID number\&. The \f3value\fR argument, when provided, denotes the argument for the extension\&. When \fIvalue\fR is omitted, that means that the default value of the extension or the extension requires no argument\&. The \f3:critical\fR modifier, when provided, means the extension\&'s \f3isCritical\fR attribute is \f3true\fR; otherwise, it is \f3false\fR\&. You can use \f3:c\fR in place of \f3:critical\fR\&.
.SH NAMED\ EXTENSIONS    
The \f3keytool\fR command supports these named extensions\&. The names are not case-sensitive)\&.
.TP     
BC or BasicContraints
\fIValues\fR: The full form is: \f3ca:{true|false}[,pathlen:<len>]\fR or \f3<len>\fR, which is short for \f3ca:true,pathlen:<len>\fR\&. When <\f3len\fR> is omitted, you have \f3ca:true\fR\&.
.TP     
KU or KeyUsage
\fIValues\fR: \f3usage\fR(,\f3usage\fR)*, where \fIusage\fR can be one of \f3digitalSignature\fR, \f3nonRepudiation\fR (contentCommitment), \f3keyEncipherment\fR, \f3dataEncipherment\fR, \f3keyAgreement\fR, \f3keyCertSign\fR, \f3cRLSign\fR, \f3encipherOnly\fR, \f3decipherOnly\fR\&. The \fIusage\fR argument can be abbreviated with the first few letters (\f3dig\fR for \f3digitalSignature\fR) or in camel-case style (\f3dS\fR for \f3digitalSignature\fR or \f3cRLS\fR for \f3cRLSign\fR), as long as no ambiguity is found\&. The \f3usage\fR values are case-sensitive\&.
.TP     
EKU or ExtendedKeyUsage
\fIValues\fR: \f3usage\fR(,\f3usage\fR)*, where \fIusage\fR can be one of \f3anyExtendedKeyUsage\fR, \f3serverAuth\fR, \f3clientAuth\fR, \f3codeSigning\fR, \f3emailProtection\fR, \f3timeStamping\fR, \f3OCSPSigning\fR, or any \fIOID string\fR\&. The \fIusage\fR argument can be abbreviated with the first few letters or in camel-case style, as long as no ambiguity is found\&. The \f3usage\fR values are case-sensitive\&.
.TP     
SAN or SubjectAlternativeName
\fIValues\fR: \f3type\fR:\f3value\fR(,t\f3ype:value\fR)*, where \f3type\fR can be \f3EMAIL\fR, \f3URI\fR, \f3DNS\fR, \f3IP\fR, or \f3OID\fR\&. The \f3value\fR argument is the string format value for the \f3type\fR\&.
.TP     
IAN or IssuerAlternativeName
\fIValues\fR: Same as \f3SubjectAlternativeName\fR\&.
.TP     
SIA or SubjectInfoAccess
\fIValues\fR: \f3method\fR:\f3location-type\fR:\f3location-value\fR (,\f3method:location-type\fR:\f3location-value\fR)*, where \f3method\fR can be \f3timeStamping\fR, \f3caRepository\fR or any OID\&. The \f3location-type\fR and \f3location-value\fR arguments can be any \f3type\fR:\f3value\fR supported by the \f3SubjectAlternativeName\fR extension\&.
.TP     
AIA or AuthorityInfoAccess
\fIValues\fR: Same as \f3SubjectInfoAccess\fR\&. The \f3method\fR argument can be \f3ocsp\fR,\f3caIssuers\fR, or any OID\&.
.PP
When \f3name\fR is OID, the value is the hexadecimal dumped DER encoding of the \f3extnValue\fR for the extension excluding the OCTET STRING type and length bytes\&. Any extra character other than standard hexadecimal numbers (0-9, a-f, A-F) are ignored in the HEX string\&. Therefore, both 01:02:03:04 and 01020304 are accepted as identical values\&. When there is no value, the extension has an empty value field\&.
.PP
A special name \f3honored\fR, used in \f3-gencert\fR only, denotes how the extensions included in the certificate request should be honored\&. The value for this name is a comma separated list of \f3all\fR (all requested extensions are honored), \f3name{:[critical|non-critical]}\fR (the named extension is honored, but using a different \f3isCritical\fR attribute) and \f3-name\fR (used with \f3all\fR, denotes an exception)\&. Requested extensions are not honored by default\&.
.PP
If, besides the\f3-ext honored\fR option, another named or OID \f3-ext\fR option is provided, this extension is added to those already honored\&. However, if this name (or OID) also appears in the honored value, then its value and criticality overrides the one in the request\&.
.PP
The \f3subjectKeyIdentifier\fR extension is always created\&. For non-self-signed certificates, the \f3authorityKeyIdentifier\fR is created\&.
.PP
\fINote:\fR Users should be aware that some combinations of extensions (and other certificate fields) may not conform to the Internet standard\&. See Certificate Conformance Warning\&.
.SH COMMANDS    
.TP     
-gencert
.sp     
.nf     
\f3{\-rfc} {\-infile \fIinfile\fR} {\-outfile \fIoutfile\fR} {\-alias \fIalias\fR} {\-sigalg \fIsigalg\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-dname \fIdname\fR} {\-startdate \fIstartdate\fR {\-ext \fIext\fR}* {\-validity \fIvalDays\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3[\-keypass \fIkeypass\fR] {\-keystore \fIkeystore\fR} [\-storepass \fIstorepass\fR]\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-storetype \fIstoretype\fR} {\-providername \fIprovider_name\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerClass \fIprovider_class_name\fR {\-providerArg \fIprovider_arg\fR}}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-v} {\-protected} {\-Jjavaoption}\fP
.fi     
.sp     


Generates a certificate as a response to a certificate request file (which can be created by the \f3keytool\fR\f3-certreq\fR command)\&. The command reads the request from \fIinfile\fR (if omitted, from the standard input), signs it using alias\&'s private key, and outputs the X\&.509 certificate into \fIoutfile\fR (if omitted, to the standard output)\&. When\f3-rfc\fR is specified, the output format is Base64-encoded PEM; otherwise, a binary DER is created\&.

The \f3sigalg\fR value specifies the algorithm that should be used to sign the certificate\&. The \f3startdate\fR argument is the start time and date that the certificate is valid\&. The \f3valDays\fR argument tells the number of days for which the certificate should be considered valid\&.

When \f3dname\fR is provided, it is used as the subject of the generated certificate\&. Otherwise, the one from the certificate request is used\&.

The \f3ext\fR value shows what X\&.509 extensions will be embedded in the certificate\&. Read Common Options for the grammar of \f3-ext\fR\&.

The \f3-gencert\fR option enables you to create certificate chains\&. The following example creates a certificate, \f3e1\fR, that contains three certificates in its certificate chain\&.

The following commands creates four key pairs named \f3ca\fR, \f3ca1\fR, \f3ca2\fR, and \f3e1\fR:
.sp     
.nf     
\f3keytool \-alias ca \-dname CN=CA \-genkeypair\fP
.fi     
.nf     
\f3keytool \-alias ca1 \-dname CN=CA \-genkeypair\fP
.fi     
.nf     
\f3keytool \-alias ca2 \-dname CN=CA \-genkeypair\fP
.fi     
.nf     
\f3keytool \-alias e1 \-dname CN=E1 \-genkeypair\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The following two commands create a chain of signed certificates; \f3ca\fR signs \f3ca1\fR and \f3ca1\fR signs \f3ca2\fR, all of which are self-issued:
.sp     
.nf     
\f3keytool \-alias ca1 \-certreq |\fP
.fi     
.nf     
\f3    keytool \-alias ca \-gencert \-ext san=dns:ca1 |\fP
.fi     
.nf     
\f3    keytool \-alias ca1 \-importcert\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3keytool \-alias ca2 \-certreq |\fP
.fi     
.nf     
\f3    $KT \-alias ca1 \-gencert \-ext san=dns:ca2 |\fP
.fi     
.nf     
\f3    $KT \-alias ca2 \-importcert\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The following command creates the certificate \f3e1\fR and stores it in the file \f3e1\&.cert\fR, which is signed by \f3ca2\fR\&. As a result, \f3e1\fR should contain \f3ca\fR, \f3ca1\fR, and \f3ca2\fR in its certificate chain:
.sp     
.nf     
\f3keytool \-alias e1 \-certreq | keytool \-alias ca2 \-gencert > e1\&.cert\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP     
-genkeypair
.sp     
.nf     
\f3{\-alias \fIalias\fR} {\-keyalg \fIkeyalg\fR} {\-keysize \fIkeysize\fR} {\-sigalg \fIsigalg\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3[\-dname \fIdname\fR] [\-keypass \fIkeypass\fR] {\-startdate \fIvalue\fR} {\-ext \fIext\fR}*\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-validity \fIvalDays\fR} {\-storetype \fIstoretype\fR} {\-keystore \fIkeystore\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3[\-storepass \fIstorepass\fR]\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerClass \fIprovider_class_name\fR {\-providerArg \fIprovider_arg\fR}}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-v} {\-protected} {\-Jjavaoption}\fP
.fi     
.sp     


Generates a key pair (a public key and associated private key)\&. Wraps the public key into an X\&.509 v3 self-signed certificate, which is stored as a single-element certificate chain\&. This certificate chain and the private key are stored in a new keystore entry identified by alias\&.

The \f3keyalg\fR value specifies the algorithm to be used to generate the key pair, and the \f3keysize\fR value specifies the size of each key to be generated\&. The \f3sigalg\fR value specifies the algorithm that should be used to sign the self-signed certificate\&. This algorithm must be compatible with the \f3keyalg\fR value\&.

The \f3dname\fR value specifies the X\&.500 Distinguished Name to be associated with the value of \f3alias\fR, and is used as the issuer and subject fields in the self-signed certificate\&. If no distinguished name is provided at the command line, then the user is prompted for one\&.

The value of \f3keypass\fR is a password used to protect the private key of the generated key pair\&. If no password is provided, then the user is prompted for it\&. If you press \fIthe Return key\fR at the prompt, then the key password is set to the same password as the keystore password\&. The \f3keypass\fR value must be at least 6 characters\&.

The value of \f3startdate\fR specifies the issue time of the certificate, also known as the "Not Before" value of the X\&.509 certificate\&'s Validity field\&.

The option value can be set in one of these two forms:

\f3([+-]nnn[ymdHMS])+\fR

\f3[yyyy/mm/dd] [HH:MM:SS]\fR

With the first form, the issue time is shifted by the specified value from the current time\&. The value is a concatenation of a sequence of subvalues\&. Inside each subvalue, the plus sign (+) means shift forward, and the minus sign (-) means shift backward\&. The time to be shifted is \f3nnn\fR units of years, months, days, hours, minutes, or seconds (denoted by a single character of \f3y\fR, \f3m\fR, \f3d\fR, \f3H\fR, \f3M\fR, or \f3S\fR respectively)\&. The exact value of the issue time is calculated using the \f3java\&.util\&.GregorianCalendar\&.add(int field, int amount)\fR method on each subvalue, from left to right\&. For example, by specifying, the issue time will be:
.sp     
.nf     
\f3Calendar c = new GregorianCalendar();\fP
.fi     
.nf     
\f3c\&.add(Calendar\&.YEAR, \-1);\fP
.fi     
.nf     
\f3c\&.add(Calendar\&.MONTH, 1);\fP
.fi     
.nf     
\f3c\&.add(Calendar\&.DATE, \-1);\fP
.fi     
.nf     
\f3return c\&.getTime()\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


With the second form, the user sets the exact issue time in two parts, year/month/day and hour:minute:second (using the local time zone)\&. The user can provide only one part, which means the other part is the same as the current date (or time)\&. The user must provide the exact number of digits as shown in the format definition (padding with 0 when shorter)\&. When both the date and time are provided, there is one (and only one) space character between the two parts\&. The hour should always be provided in 24 hour format\&.

When the option is not provided, the start date is the current time\&. The option can be provided at most once\&.

The value of \f3valDays\fR specifies the number of days (starting at the date specified by \f3-startdate\fR, or the current date when \f3-startdate\fR is not specified) for which the certificate should be considered valid\&.

This command was named \f3-genkey\fR in earlier releases\&. The old name is still supported in this release\&. The new name, \f3-genkeypair\fR, is preferred going forward\&.
.TP     
-genseckey
.sp     
.nf     
\f3{\-alias \fIalias\fR} {\-keyalg \fIkeyalg\fR} {\-keysize \fIkeysize\fR} [\-keypass \fIkeypass\fR]\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-storetype \fIstoretype\fR} {\-keystore \fIkeystore\fR} [\-storepass \fIstorepass\fR]\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerClass \fIprovider_class_name\fR {\-providerArg \fIprovider_arg\fR}} {\-v}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-protected} {\-Jjavaoption}\fP
.fi     
.sp     


Generates a secret key and stores it in a new \f3KeyStore\&.SecretKeyEntry\fR identified by \f3alias\fR\&.

The value of \f3keyalg\fR specifies the algorithm to be used to generate the secret key, and the value of \f3keysize\fR specifies the size of the key to be generated\&. The \f3keypass\fR value is a password that protects the secret key\&. If no password is provided, then the user is prompted for it\&. If you press the Return key at the prompt, then the key password is set to the same password that is used for the \f3keystore\fR\&. The \f3keypass\fR value must be at least 6 characters\&.
.TP     
-importcert
.sp     
.nf     
\f3{\-alias \fIalias\fR} {\-file \fIcert_file\fR} [\-keypass \fIkeypass\fR] {\-noprompt} {\-trustcacerts}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-storetype \fIstoretype\fR} {\-keystore \fIkeystore\fR} [\-storepass \fIstorepass\fR]\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerName \fIprovider_name\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerClass \fIprovider_class_name\fR {\-providerArg \fIprovider_arg\fR}}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-v} {\-protected} {\-Jjavaoption}\fP
.fi     
.sp     


Reads the certificate or certificate chain (where the latter is supplied in a PKCS#7 formatted reply or a sequence of X\&.509 certificates) from the file \f3cert_file\fR, and stores it in the \f3keystore\fR entry identified by \f3alias\fR\&. If no file is specified, then the certificate or certificate chain is read from \f3stdin\fR\&.

The \f3keytool\fR command can import X\&.509 v1, v2, and v3 certificates, and PKCS#7 formatted certificate chains consisting of certificates of that type\&. The data to be imported must be provided either in binary encoding format or in printable encoding format (also known as Base64 encoding) as defined by the Internet RFC 1421 standard\&. In the latter case, the encoding must be bounded at the beginning by a string that starts with \f3-\fR\f3----BEGIN\fR, and bounded at the end by a string that starts with \f3-----END\fR\&.

You import a certificate for two reasons: To add it to the list of trusted certificates, and to import a certificate reply received from a certificate authority (CA) as the result of submitting a Certificate Signing Request to that CA (see the \f3-certreq\fR option in Commands)\&.

Which type of import is intended is indicated by the value of the \f3-alias\fR option\&. If the alias does not point to a key entry, then the \f3keytool\fR command assumes you are adding a trusted certificate entry\&. In this case, the alias should not already exist in the keystore\&. If the alias does already exist, then the \f3keytool\fR command outputs an error because there is already a trusted certificate for that alias, and does not import the certificate\&. If the alias points to a key entry, then the \f3keytool\fR command assumes you are importing a certificate reply\&.
.TP     
-importpassword
.sp     
.nf     
\f3{\-alias \fIalias\fR} [\-keypass \fIkeypass\fR] {\-storetype \fIstoretype\fR} {\-keystore \fIkeystore\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3[\-storepass \fIstorepass\fR]\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerClass \fIprovider_class_name\fR {\-providerArg \fIprovider_arg\fR}}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-v} {\-protected} {\-Jjavaoption}\fP
.fi     
.sp     


Imports a passphrase and stores it in a new \f3KeyStore\&.SecretKeyEntry\fR identified by \f3alias\fR\&. The passphrase may be supplied via the standard input stream; otherwise the user is prompted for it\&. \f3keypass\fR is a password used to protect the imported passphrase\&. If no password is provided, the user is prompted for it\&. If you press the Return key at the prompt, the key password is set to the same password as that used for the \f3keystore\fR\&. \f3keypass\fR must be at least 6 characters long\&.
.TP     
-importkeystore
.sp     
.nf     
\f3{\-srcstoretype \fIsrcstoretype\fR} {\-deststoretype \fIdeststoretype\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3[\-srcstorepass \fIsrcstorepass\fR] [\-deststorepass \fIdeststorepass\fR] {\-srcprotected}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-destprotected} \fP
.fi     
.sp     
.sp     
.nf     
\f3{\-srcalias \fIsrcalias\fR {\-destalias \fIdestalias\fR} [\-srckeypass \fIsrckeypass\fR]} \fP
.fi     
.sp     
.sp     
.nf     
\f3[\-destkeypass \fIdestkeypass\fR] {\-noprompt}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-srcProviderName \fIsrc_provider_name\fR} {\-destProviderName \fIdest_provider_name\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerClass \fIprovider_class_name\fR {\-providerArg \fIprovider_arg\fR}} {\-v}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-protected} {\-Jjavaoption}\fP
.fi     
.sp     


Imports a single entry or all entries from a source keystore to a destination keystore\&.

When the \f3-srcalias\fR option is provided, the command imports the single entry identified by the alias to the destination keystore\&. If a destination alias is not provided with \f3destalias\fR, then \f3srcalias\fR is used as the destination alias\&. If the source entry is protected by a password, then \f3srckeypass\fR is used to recover the entry\&. If \fIsrckeypass\fR is not provided, then the \f3keytool\fR command attempts to use \f3srcstorepass\fR to recover the entry\&. If \f3srcstorepass\fR is either not provided or is incorrect, then the user is prompted for a password\&. The destination entry is protected with \f3destkeypass\fR\&. If \f3destkeypass\fR is not provided, then the destination entry is protected with the source entry password\&. For example, most third-party tools require \f3storepass\fR and \f3keypass\fR in a PKCS #12 keystore to be the same\&. In order to create a PKCS #12 keystore for these tools, always specify a \f3-destkeypass\fR to be the same as \f3-deststorepass\fR\&.

If the \f3-srcalias\fR option is not provided, then all entries in the source keystore are imported into the destination keystore\&. Each destination entry is stored under the alias from the source entry\&. If the source entry is protected by a password, then \f3srcstorepass\fR is used to recover the entry\&. If \f3srcstorepass\fR is either not provided or is incorrect, then the user is prompted for a password\&. If a source keystore entry type is not supported in the destination keystore, or if an error occurs while storing an entry into the destination keystore, then the user is prompted whether to skip the entry and continue or to quit\&. The destination entry is protected with the source entry password\&.

If the destination alias already exists in the destination keystore, then the user is prompted to either overwrite the entry or to create a new entry under a different alias name\&.

If the \f3-noprompt\fR option is provided, then the user is not prompted for a new destination alias\&. Existing entries are overwritten with the destination alias name\&. Entries that cannot be imported are skipped and a warning is displayed\&.
.TP     
-printcertreq
.sp     
.nf     
\f3{\-file \fIfile\fR}\fP
.fi     
.sp     


Prints the content of a PKCS #10 format certificate request, which can be generated by the \f3keytool\fR\f3-certreq\fR command\&. The command reads the request from file\&. If there is no file, then the request is read from the standard input\&.
.TP     
-certreq
.sp     
.nf     
\f3{\-alias \fIalias\fR} {\-dname \fIdname\fR} {\-sigalg \fIsigalg\fR} {\-file \fIcertreq_file\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3[\-keypass \fIkeypass\fR] {\-storetype \fIstoretype\fR} {\-keystore \fIkeystore\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3[\-storepass \fIstorepass\fR] {\-providerName \fIprovider_name\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerClass \fIprovider_class_name\fR {\-providerArg \fIprovider_arg\fR}}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-v} {\-protected} {\-Jjavaoption}\fP
.fi     
.sp     


Generates a Certificate Signing Request (CSR) using the PKCS #10 format\&.

A CSR is intended to be sent to a certificate authority (CA)\&. The CA authenticates the certificate requestor (usually off-line) and will return a certificate or certificate chain, used to replace the existing certificate chain (which initially consists of a self-signed certificate) in the keystore\&.

The private key associated with alias is used to create the PKCS #10 certificate request\&. To access the private key, the correct password must be provided\&. If \f3keypass\fR is not provided at the command line and is different from the password used to protect the integrity of the keystore, then the user is prompted for it\&. If \f3dname\fR is provided, then it is used as the subject in the CSR\&. Otherwise, the X\&.500 Distinguished Name associated with alias is used\&.

The \f3sigalg\fR value specifies the algorithm that should be used to sign the CSR\&.

The CSR is stored in the file certreq_file\&. If no file is specified, then the CSR is output to \f3stdout\fR\&.

Use the \f3importcert\fR command to import the response from the CA\&.
.TP     
-exportcert
.sp     
.nf     
\f3{\-alias \fIalias\fR} {\-file \fIcert_file\fR} {\-storetype \fIstoretype\fR} {\-keystore \fIkeystore\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3[\-storepass \fIstorepass\fR] {\-providerName \fIprovider_name\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerClass \fIprovider_class_name\fR {\-providerArg \fIprovider_arg\fR}}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-rfc} {\-v} {\-protected} {\-Jjavaoption}\fP
.fi     
.sp     


Reads from the keystore the certificate associated with \fIalias\fR and stores it in the cert_file file\&. When no file is specified, the certificate is output to \f3stdout\fR\&.

The certificate is by default output in binary encoding\&. If the \f3-rfc\fR option is specified, then the output in the printable encoding format defined by the Internet RFC 1421 Certificate Encoding Standard\&.

If \f3alias\fR refers to a trusted certificate, then that certificate is output\&. Otherwise, \f3alias\fR refers to a key entry with an associated certificate chain\&. In that case, the first certificate in the chain is returned\&. This certificate authenticates the public key of the entity addressed by \f3alias\fR\&.

This command was named \f3-export\fR in earlier releases\&. The old name is still supported in this release\&. The new name, \f3-exportcert\fR, is preferred going forward\&.
.TP     
-list
.sp     
.nf     
\f3{\-alias \fIalias\fR} {\-storetype \fIstoretype\fR} {\-keystore \fIkeystore\fR} [\-storepass \fIstorepass\fR]\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerName \fIprovider_name\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerClass \fIprovider_class_name\fR {\-providerArg \fIprovider_arg\fR}}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-v | \-rfc} {\-protected} {\-Jjavaoption}\fP
.fi     
.sp     


Prints to \f3stdout\fR the contents of the keystore entry identified by \f3alias\fR\&. If no \f3alias\fR is specified, then the contents of the entire keystore are printed\&.

This command by default prints the SHA256 fingerprint of a certificate\&. If the \f3-v\fR option is specified, then the certificate is printed in human-readable format, with additional information such as the owner, issuer, serial number, and any extensions\&. If the \f3-rfc\fR option is specified, then the certificate contents are printed using the printable encoding format, as defined by the Internet RFC 1421 Certificate Encoding Standard\&.

You cannot specify both \f3-v\fR and \f3-rfc\fR\&.
.TP     
-printcert
.sp     
.nf     
\f3{\-file \fIcert_file\fR | \-sslserver \fIhost\fR[:\fIport\fR]} {\-jarfile \fIJAR_file\fR {\-rfc} {\-v}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-Jjavaoption}\fP
.fi     
.sp     


Reads the certificate from the file cert_file, the SSL server located at host:port, or the signed JAR file \f3JAR_file\fR (with the \f3-jarfile\fR option and prints its contents in a human-readable format\&. When no port is specified, the standard HTTPS port 443 is assumed\&. Note that \f3-sslserver\fR and -file options cannot be provided at the same time\&. Otherwise, an error is reported\&. If neither option is specified, then the certificate is read from \f3stdin\fR\&.

When\f3-rfc\fR is specified, the \f3keytool\fR command prints the certificate in PEM mode as defined by the Internet RFC 1421 Certificate Encoding standard\&. See Internet RFC 1421 Certificate Encoding Standard\&.

If the certificate is read from a file or \f3stdin\fR, then it might be either binary encoded or in printable encoding format, as defined by the RFC 1421 Certificate Encoding standard\&.

If the SSL server is behind a firewall, then the \f3-J-Dhttps\&.proxyHost=proxyhost\fR and \f3-J-Dhttps\&.proxyPort=proxyport\fR options can be specified on the command line for proxy tunneling\&. See Java Secure Socket Extension (JSSE) Reference Guide at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/jsse/JSSERefGuide\&.html

\fINote:\fR This option can be used independently of a keystore\&.
.TP     
-printcrl
.sp     
.nf     
\f3\-file \fIcrl_\fR {\-v}\fP
.fi     
.sp     


Reads the Certificate Revocation List (CRL) from the file \f3crl_\fR\&. A CRL is a list of digital certificates that were revoked by the CA that issued them\&. The CA generates the \f3crl_\fR file\&.

\fINote:\fR This option can be used independently of a keystore\&.
.TP     
-storepasswd
.sp     
.nf     
\f3[\-new \fInew_storepass\fR] {\-storetype \fIstoretype\fR} {\-keystore \fIkeystore\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3[\-storepass \fIstorepass\fR] {\-providerName \fIprovider_name\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerClass \fIprovider_class_name\fR {\-providerArg \fIprovider_arg\fR}}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-v} {\-Jjavaoption}\fP
.fi     
.sp     


Changes the password used to protect the integrity of the keystore contents\&. The new password is \f3new_storepass\fR, which must be at least 6 characters\&.
.TP     
-keypasswd
.sp     
.nf     
\f3{\-alias \fIalias\fR} [\-keypass \fIold_keypass\fR] [\-new \fInew_keypass\fR] {\-storetype \fIstoretype\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-keystore \fIkeystore\fR} [\-storepass \fIstorepass\fR] {\-providerName \fIprovider_name\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerClass \fIprovider_class_name\fR {\-providerArg \fIprovider_arg\fR}} {\-v}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-Jjavaoption}\fP
.fi     
.sp     


Changes the password under which the private/secret key identified by \f3alias\fR is protected, from \f3old_keypass\fR to \f3new_keypass\fR, which must be at least 6 characters\&.

If the \f3-keypass\fR option is not provided at the command line, and the key password is different from the keystore password, then the user is prompted for it\&.

If the \f3-new\fR option is not provided at the command line, then the user is prompted for it
.TP     
-delete
.sp     
.nf     
\f3[\-alias \fIalias\fR] {\-storetype \fIstoretype\fR} {\-keystore \fIkeystore\fR} [\-storepass \fIstorepass\fR]\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerName \fIprovider_name\fR}  \fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerClass \fIprovider_class_name\fR {\-providerArg \fIprovider_arg\fR}}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-v} {\-protected} {\-Jjavaoption}\fP
.fi     
.sp     


Deletes from the keystore the entry identified by \f3alias\fR\&. The user is prompted for the alias, when no alias is provided at the command line\&.
.TP     
-changealias
.sp     
.nf     
\f3{\-alias \fIalias\fR} [\-destalias \fIdestalias\fR] [\-keypass \fIkeypass\fR] {\-storetype \fIstoretype\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-keystore \fIkeystore\fR} [\-storepass \fIstorepass\fR] {\-providerName \fIprovider_name\fR}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-providerClass \fIprovider_class_name\fR {\-providerArg \fIprovider_arg\fR}} {\-v}\fP
.fi     
.sp     
.sp     
.nf     
\f3{\-protected} {\-Jjavaoption}\fP
.fi     
.sp     


Move an existing keystore entry from the specified \f3alias\fR to a new alias, \f3destalias\fR\&. If no destination alias is provided, then the command prompts for one\&. If the original entry is protected with an entry password, then the password can be supplied with the \f3-keypass\fR option\&. If no key password is provided, then the \f3storepass\fR (if provided) is attempted first\&. If the attempt fails, then the user is prompted for a password\&.
.TP
-help
.br
Lists the basic commands and their options\&.

For more information about a specific command, enter the following, where \f3command_name\fR is the name of the command: \f3keytool -command_name -help\fR\&.
.SH EXAMPLES    
This example walks through the sequence of steps to create a keystore for managing public/private key pair and certificates from trusted entities\&.
.SS GENERATE\ THE\ KEY\ PAIR    
First, create a keystore and generate the key pair\&. You can use a command such as the following typed as a single line:
.sp     
.nf     
\f3keytool \-genkeypair \-dname "cn=Mark Jones, ou=Java, o=Oracle, c=US"\fP
.fi     
.nf     
\f3    \-alias business \-keypass <new password for private key>\fP
.fi     
.nf     
\f3    \-keystore /working/mykeystore\fP
.fi     
.nf     
\f3    \-storepass <new password for keystore> \-validity 180\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The command creates the keystore named \f3mykeystore\fR in the working directory (assuming it does not already exist), and assigns it the password specified by \f3<new password for keystore>\fR\&. It generates a public/private key pair for the entity whose distinguished name has a common name of Mark Jones, organizational unit of Java, organization of Oracle and two-letter country code of US\&. It uses the default DSA key generation algorithm to create the keys; both are 1024 bits\&.
.PP
The command uses the default SHA256withDSA signature algorithm to create a self-signed certificate that includes the public key and the distinguished name information\&. The certificate is valid for 180 days, and is associated with the private key in a keystore entry referred to by the alias \f3business\fR\&. The private key is assigned the password specified by \f3<new password for private key>\fR\&.
.PP
The command is significantly shorter when the option defaults are accepted\&. In this case, no options are required, and the defaults are used for unspecified options that have default values\&. You are prompted for any required values\&. You could have the following:
.sp     
.nf     
\f3keytool \-genkeypair\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
In this case, a keystore entry with the alias \f3mykey\fR is created, with a newly generated key pair and a certificate that is valid for 90 days\&. This entry is placed in the keystore named \f3\&.keystore\fR in your home directory\&. The keystore is created when it does not already exist\&. You are prompted for the distinguished name information, the keystore password, and the private key password\&.
.PP
The rest of the examples assume you executed the \f3-genkeypair\fR command without options specified, and that you responded to the prompts with values equal to those specified in the first \f3-genkeypair\fR command\&. For example, a distinguished name of \f3cn=Mark Jones\fR, \f3ou=Java\fR, \f3o=Oracle\fR, \f3c=US\fR)\&.
.SS REQUEST\ A\ SIGNED\ CERTIFICATE\ FROM\ A\ CA    
Generating the key pair created a self-signed certificate\&. A certificate is more likely to be trusted by others when it is signed by a Certification Authority (CA)\&. To get a CA signature, first generate a Certificate Signing Request (CSR), as follows:
.sp     
.nf     
\f3keytool \-certreq \-file MarkJ\&.csr\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
This creates a CSR for the entity identified by the default alias \f3mykey\fR and puts the request in the file named MarkJ\&.csr\&. Submit this file to a CA, such as VeriSign\&. The CA authenticates you, the requestor (usually off-line), and returns a certificate, signed by them, authenticating your public key\&. In some cases, the CA returns a chain of certificates, each one authenticating the public key of the signer of the previous certificate in the chain\&.
.SS IMPORT\ A\ CERTIFICATE\ FOR\ THE\ CA    
You now need to replace the self-signed certificate with a certificate chain, where each certificate in the chain authenticates the public key of the signer of the previous certificate in the chain, up to a root CA\&.
.PP
Before you import the certificate reply from a CA, you need one or more trusted certificates in your keystore or in the \f3cacerts\fR keystore file\&. See \f3-importcert\fR in Commands\&.
.TP 0.2i    
\(bu
If the certificate reply is a certificate chain, then you need the top certificate of the chain\&. The root CA certificate that authenticates the public key of the CA\&.
.TP 0.2i    
\(bu
If the certificate reply is a single certificate, then you need a certificate for the issuing CA (the one that signed it)\&. If that certificate is not self-signed, then you need a certificate for its signer, and so on, up to a self-signed root CA certificate\&.
.PP
The \f3cacerts\fR keystore file ships with several VeriSign root CA certificates, so you probably will not need to import a VeriSign certificate as a trusted certificate in your keystore\&. But if you request a signed certificate from a different CA, and a certificate authenticating that CA\&'s public key was not added to \f3cacerts\fR, then you must import a certificate from the CA as a trusted certificate\&.
.PP
A certificate from a CA is usually either self-signed or signed by another CA, in which case you need a certificate that authenticates that CA\&'s public key\&. Suppose company ABC, Inc\&., is a CA, and you obtain a file named A\f3BCCA\&.cer\fR that is supposed to be a self-signed certificate from ABC, that authenticates that CA\&'s public key\&. Be careful to ensure the certificate is valid before you import it as a trusted certificate\&. View it first with the \f3keytool -printcert\fR command or the \f3keytool -importcert\fR command without the \f3-noprompt\fR option, and make sure that the displayed certificate fingerprints match the expected ones\&. You can call the person who sent the certificate, and compare the fingerprints that you see with the ones that they show or that a secure public key repository shows\&. Only when the fingerprints are equal is it guaranteed that the certificate was not replaced in transit with somebody else\&'s (for example, an attacker\&'s) certificate\&. If such an attack takes place, and you did not check the certificate before you imported it, then you would be trusting anything the attacker has signed\&.
.PP
If you trust that the certificate is valid, then you can add it to your keystore with the following command:
.sp     
.nf     
\f3keytool \-importcert \-alias abc \-file ABCCA\&.cer\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
This command creates a trusted certificate entry in the keystore, with the data from the file ABCCA\&.cer, and assigns the alias \f3abc\fR to the entry\&.
.SS IMPORT\ THE\ CERTIFICATE\ REPLY\ FROM\ THE\ CA    
After you import a certificate that authenticates the public key of the CA you submitted your certificate signing request to (or there is already such a certificate in the cacerts file), you can import the certificate reply and replace your self-signed certificate with a certificate chain\&. This chain is the one returned by the CA in response to your request (when the CA reply is a chain), or one constructed (when the CA reply is a single certificate) using the certificate reply and trusted certificates that are already available in the keystore where you import the reply or in the \f3cacerts\fR keystore file\&.
.PP
For example, if you sent your certificate signing request to VeriSign, then you can import the reply with the following, which assumes the returned certificate is named VSMarkJ\&.cer:
.sp     
.nf     
\f3keytool \-importcert \-trustcacerts \-file VSMarkJ\&.cer\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS EXPORT\ A\ CERTIFICATE\ THAT\ AUTHENTICATES\ THE\ PUBLIC\ KEY    
If you used the \f3jarsigner\fR command to sign a Java Archive (JAR) file, then clients that want to use the file will want to authenticate your signature\&. One way the clients can authenticate you is by first importing your public key certificate into their keystore as a trusted entry\&.
.PP
You can export the certificate and supply it to your clients\&. As an example, you can copy your certificate to a file named MJ\&.cer with the following command that assumes the entry has an alias of \f3mykey\fR:
.sp     
.nf     
\f3keytool \-exportcert \-alias mykey \-file MJ\&.cer\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
With the certificate and the signed JAR file, a client can use the \f3jarsigner\fR command to authenticate your signature\&.
.SS IMPORT\ KEYSTORE    
The command \f3importkeystore\fR is used to import an entire keystore into another keystore, which means all entries from the source keystore, including keys and certificates, are all imported to the destination keystore within a single command\&. You can use this command to import entries from a different type of keystore\&. During the import, all new entries in the destination keystore will have the same alias names and protection passwords (for secret keys and private keys)\&. If the \f3keytool\fR command cannot recover the private keys or secret keys from the source keystore, then it prompts you for a password\&. If it detects alias duplication, then it asks you for a new alias, and you can specify a new alias or simply allow the \f3keytool\fR command to overwrite the existing one\&.
.PP
For example, to import entries from a typical JKS type keystore key\&.jks into a PKCS #11 type hardware-based keystore, use the command:
.sp     
.nf     
\f3keytool \-importkeystore\fP
.fi     
.nf     
\f3    \-srckeystore key\&.jks \-destkeystore NONE\fP
.fi     
.nf     
\f3    \-srcstoretype JKS \-deststoretype PKCS11\fP
.fi     
.nf     
\f3    \-srcstorepass <src keystore password>\fP
.fi     
.nf     
\f3    \-deststorepass <destination keystore pwd>\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The \f3importkeystore\fR command can also be used to import a single entry from a source keystore to a destination keystore\&. In this case, besides the options you see in the previous example, you need to specify the alias you want to import\&. With the \f3-srcalias\fR option specified, you can also specify the destination alias name in the command line, as well as protection password for a secret/private key and the destination protection password you want\&. The following command demonstrates this:
.sp     
.nf     
\f3keytool \-importkeystore\fP
.fi     
.nf     
\f3    \-srckeystore key\&.jks \-destkeystore NONE\fP
.fi     
.nf     
\f3    \-srcstoretype JKS \-deststoretype PKCS11\fP
.fi     
.nf     
\f3    \-srcstorepass <src keystore password>\fP
.fi     
.nf     
\f3    \-deststorepass <destination keystore pwd>\fP
.fi     
.nf     
\f3    \-srcalias myprivatekey \-destalias myoldprivatekey\fP
.fi     
.nf     
\f3    \-srckeypass <source entry password>\fP
.fi     
.nf     
\f3    \-destkeypass <destination entry password>\fP
.fi     
.nf     
\f3    \-noprompt\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS GENERATE\ CERTIFICATES\ FOR\ AN\ SSL\ SERVER    
The following are \f3keytool\fR commands to generate key pairs and certificates for three entities: Root CA (\f3root\fR), Intermediate CA (\f3ca\fR), and SSL server (\f3server\fR)\&. Ensure that you store all the certificates in the same keystore\&. In these examples, RSA is the recommended the key algorithm\&.
.sp     
.nf     
\f3keytool \-genkeypair \-keystore root\&.jks \-alias root \-ext bc:c\fP
.fi     
.nf     
\f3keytool \-genkeypair \-keystore ca\&.jks \-alias ca \-ext bc:c\fP
.fi     
.nf     
\f3keytool \-genkeypair \-keystore server\&.jks \-alias server\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3keytool \-keystore root\&.jks \-alias root \-exportcert \-rfc > root\&.pem\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3keytool \-storepass <storepass> \-keystore ca\&.jks \-certreq \-alias ca |\fP
.fi     
.nf     
\f3    keytool \-storepass <storepass> \-keystore root\&.jks\fP
.fi     
.nf     
\f3    \-gencert \-alias root \-ext BC=0 \-rfc > ca\&.pem\fP
.fi     
.nf     
\f3keytool \-keystore ca\&.jks \-importcert \-alias ca \-file ca\&.pem\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3keytool \-storepass <storepass> \-keystore server\&.jks \-certreq \-alias server |\fP
.fi     
.nf     
\f3    keytool \-storepass <storepass> \-keystore ca\&.jks \-gencert \-alias ca\fP
.fi     
.nf     
\f3    \-ext ku:c=dig,kE \-rfc > server\&.pem\fP
.fi     
.nf     
\f3cat root\&.pem ca\&.pem server\&.pem |\fP
.fi     
.nf     
\f3    keytool \-keystore server\&.jks \-importcert \-alias server\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH TERMS    
.TP     
Keystore
A keystore is a storage facility for cryptographic keys and certificates\&.
.TP     
Keystore entries
Keystores can have different types of entries\&. The two most applicable entry types for the \f3keytool\fR command include the following:

\fIKey entries\fR: Each entry holds very sensitive cryptographic key information, which is stored in a protected format to prevent unauthorized access\&. Typically, a key stored in this type of entry is a secret key, or a private key accompanied by the certificate chain for the corresponding public key\&. See Certificate Chains\&. The \f3keytool\fR command can handle both types of entries, while the \f3jarsigner\fR tool only handles the latter type of entry, that is private keys and their associated certificate chains\&.

\fITrusted certificate entries\fR: Each entry contains a single public key certificate that belongs to another party\&. The entry is called a trusted certificate because the keystore owner trusts that the public key in the certificate belongs to the identity identified by the subject (owner) of the certificate\&. The issuer of the certificate vouches for this, by signing the certificate\&.
.TP     
KeyStore aliases
All keystore entries (key and trusted certificate entries) are accessed by way of unique aliases\&.

An alias is specified when you add an entity to the keystore with the \f3-genseckey\fR command to generate a secret key, the \f3-genkeypair\fR command to generate a key pair (public and private key), or the \f3-importcert\fR command to add a certificate or certificate chain to the list of trusted certificates\&. Subsequent \f3keytool\fR commands must use this same alias to refer to the entity\&.

For example, you can use the alias \f3duke\fR to generate a new public/private key pair and wrap the public key into a self-signed certificate with the following command\&. See Certificate Chains\&.
.sp     
.nf     
\f3keytool \-genkeypair \-alias duke \-keypass dukekeypasswd\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


This example specifies an initial password of \f3dukekeypasswd\fR required by subsequent commands to access the private key associated with the alias \f3duke\fR\&. If you later want to change Duke\&'s private key password, use a command such as the following:
.sp     
.nf     
\f3keytool \-keypasswd \-alias duke \-keypass dukekeypasswd \-new newpass\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


This changes the password from \f3dukekeypasswd\fR to \f3newpass\fR\&. A password should not be specified on a command line or in a script unless it is for testing purposes, or you are on a secure system\&. If you do not specify a required password option on a command line, then you are prompted for it\&.
.TP     
KeyStore implementation
The \f3KeyStore\fR class provided in the \f3java\&.security\fR package supplies well-defined interfaces to access and modify the information in a keystore\&. It is possible for there to be multiple different concrete implementations, where each implementation is that for a particular type of keystore\&.

Currently, two command-line tools (\f3keytool\fR and \f3jarsigner\fR) and a GUI-based tool named Policy Tool make use of keystore implementations\&. Because the \f3KeyStore\fR class is \f3public\fR, users can write additional security applications that use it\&.

There is a built-in default implementation, provided by Oracle\&. It implements the keystore as a file with a proprietary keystore type (format) named JKS\&. It protects each private key with its individual password, and also protects the integrity of the entire keystore with a (possibly different) password\&.

Keystore implementations are provider-based\&. More specifically, the application interfaces supplied by \f3KeyStore\fR are implemented in terms of a Service Provider Interface (SPI)\&. That is, there is a corresponding abstract \f3KeystoreSpi\fR class, also in the \f3java\&.security package\fR, which defines the Service Provider Interface methods that providers must implement\&. The term \fIprovider\fR refers to a package or a set of packages that supply a concrete implementation of a subset of services that can be accessed by the Java Security API\&. To provide a keystore implementation, clients must implement a provider and supply a \f3KeystoreSpi\fR subclass implementation, as described in How to Implement a Provider in the Java Cryptography Architecture at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/crypto/HowToImplAProvider\&.html

Applications can choose different types of keystore implementations from different providers, using the \f3getInstance\fR factory method supplied in the \f3KeyStore\fR class\&. A keystore type defines the storage and data format of the keystore information, and the algorithms used to protect private/secret keys in the keystore and the integrity of the keystore\&. Keystore implementations of different types are not compatible\&.

The \f3keytool\fR command works on any file-based keystore implementation\&. It treats the keystore location that is passed to it at the command line as a file name and converts it to a \f3FileInputStream\fR, from which it loads the keystore information\&.)The \f3jarsigner\fR and \f3policytool\fR commands can read a keystore from any location that can be specified with a URL\&.

For \f3keytool\fR and \f3jarsigner\fR, you can specify a keystore type at the command line, with the \f3-storetype\fR option\&. For Policy Tool, you can specify a keystore type with the \fIKeystore\fR menu\&.

If you do not explicitly specify a keystore type, then the tools choose a keystore implementation based on the value of the \f3keystore\&.type\fR property specified in the security properties file\&. The security properties file is called \f3java\&.security\fR, and resides in the security properties directory, \f3java\&.home\elib\esecurity\fR on Windows and \f3java\&.home/lib/security\fR on Oracle Solaris, where \f3java\&.home\fR is the runtime environment directory\&. The \f3jre\fR directory in the SDK or the top-level directory of the Java Runtime Environment (JRE)\&.

Each tool gets the \f3keystore\&.type\fR value and then examines all the currently installed providers until it finds one that implements a keystores of that type\&. It then uses the keystore implementation from that provider\&.The \f3KeyStore\fR class defines a static method named \f3getDefaultType\fR that lets applications and applets retrieve the value of the \f3keystore\&.type\fR property\&. The following line of code creates an instance of the default keystore type as specified in the \f3keystore\&.type\fR property:
.sp     
.nf     
\f3KeyStore keyStore = KeyStore\&.getInstance(KeyStore\&.getDefaultType());\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The default keystore type is \f3jks\fR, which is the proprietary type of the keystore implementation provided by Oracle\&. This is specified by the following line in the security properties file:
.sp     
.nf     
\f3keystore\&.type=jks\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


To have the tools utilize a keystore implementation other than the default, you can change that line to specify a different keystore type\&. For example, if you have a provider package that supplies a keystore implementation for a keystore type called \f3pkcs12\fR, then change the line to the following:
.sp     
.nf     
\f3keystore\&.type=pkcs12\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


\fINote:\fR Case does not matter in keystore type designations\&. For example, JKS would be considered the same as jks\&.
.TP     
Certificate
A certificate (or public-key certificate) is a digitally signed statement from one entity (the issuer), saying that the public key and some other information of another entity (the subject) has some specific value\&. The following terms are related to certificates:

\fIPublic Keys\fR: These are numbers associated with a particular entity, and are intended to be known to everyone who needs to have trusted interactions with that entity\&. Public keys are used to verify signatures\&.

\fIDigitally Signed\fR: If some data is digitally signed, then it is stored with the identity of an entity and a signature that proves that entity knows about the data\&. The data is rendered unforgeable by signing with the entity\&'s private key\&.

\fIIdentity\fR: A known way of addressing an entity\&. In some systems, the identity is the public key, and in others it can be anything from an Oracle Solaris UID to an email address to an X\&.509 distinguished name\&.

\fISignature\fR: A signature is computed over some data using the private key of an entity\&. The signer, which in the case of a certificate is also known as the issuer\&.

\fIPrivate Keys\fR: These are numbers, each of which is supposed to be known only to the particular entity whose private key it is (that is, it is supposed to be kept secret)\&. Private and public keys exist in pairs in all public key cryptography systems (also referred to as public key crypto systems)\&. In a typical public key crypto system, such as DSA, a private key corresponds to exactly one public key\&. Private keys are used to compute signatures\&.

\fIEntity\fR: An entity is a person, organization, program, computer, business, bank, or something else you are trusting to some degree\&.

Public key cryptography requires access to users\&' public keys\&. In a large-scale networked environment, it is impossible to guarantee that prior relationships between communicating entities were established or that a trusted repository exists with all used public keys\&. Certificates were invented as a solution to this public key distribution problem\&. Now a Certification Authority (CA) can act as a trusted third party\&. CAs are entities such as businesses that are trusted to sign (issue) certificates for other entities\&. It is assumed that CAs only create valid and reliable certificates because they are bound by legal agreements\&. There are many public Certification Authorities, such as VeriSign, Thawte, Entrust, and so on\&.

You can also run your own Certification Authority using products such as Microsoft Certificate Server or the Entrust CA product for your organization\&. With the \f3keytool\fR command, it is possible to display, import, and export certificates\&. It is also possible to generate self-signed certificates\&.

The \f3keytool\fR command currently handles X\&.509 certificates\&.
.TP     
X\&.509 Certificates
The X\&.509 standard defines what information can go into a certificate and describes how to write it down (the data format)\&. All the data in a certificate is encoded with two related standards called ASN\&.1/DER\&. Abstract Syntax Notation 1 describes data\&. The Definite Encoding Rules describe a single way to store and transfer that data\&.

All X\&.509 certificates have the following data, in addition to the signature:

\fIVersion\fR: This identifies which version of the X\&.509 standard applies to this certificate, which affects what information can be specified in it\&. Thus far, three versions are defined\&. The \f3keytool\fR command can import and export v1, v2, and v3 certificates\&. It generates v3 certificates\&.

X\&.509 Version 1 has been available since 1988, is widely deployed, and is the most generic\&.

X\&.509 Version 2 introduced the concept of subject and issuer unique identifiers to handle the possibility of reuse of subject or issuer names over time\&. Most certificate profile documents strongly recommend that names not be reused and that certificates should not make use of unique identifiers\&. Version 2 certificates are not widely used\&.

X\&.509 Version 3 is the most recent (1996) and supports the notion of extensions where anyone can define an extension and include it in the certificate\&. Some common extensions are: KeyUsage (limits the use of the keys to particular purposes such as \f3signing-only\fR) and AlternativeNames (allows other identities to also be associated with this public key, for example\&. DNS names, email addresses, IP addresses)\&. Extensions can be marked critical to indicate that the extension should be checked and enforced or used\&. For example, if a certificate has the KeyUsage extension marked critical and set to \f3keyCertSign\fR, then when this certificate is presented during SSL communication, it should be rejected because the certificate extension indicates that the associated private key should only be used for signing certificates and not for SSL use\&.

\fISerial number\fR: The entity that created the certificate is responsible for assigning it a serial number to distinguish it from other certificates it issues\&. This information is used in numerous ways\&. For example, when a certificate is revoked its serial number is placed in a Certificate Revocation List (CRL)\&.

\fISignature algorithm identifier\fR: This identifies the algorithm used by the CA to sign the certificate\&.

\fIIssuer name\fR: The X\&.500 Distinguished Name of the entity that signed the certificate\&. See X\&.500 Distinguished Names\&. This is typically a CA\&. Using this certificate implies trusting the entity that signed this certificate\&. In some cases, such as root or top-level CA certificates, the issuer signs its own certificate\&.

\fIValidity period\fR: Each certificate is valid only for a limited amount of time\&. This period is described by a start date and time and an end date and time, and can be as short as a few seconds or almost as long as a century\&. The validity period chosen depends on a number of factors, such as the strength of the private key used to sign the certificate, or the amount one is willing to pay for a certificate\&. This is the expected period that entities can rely on the public value, when the associated private key has not been compromised\&.

\fISubject name\fR: The name of the entity whose public key the certificate identifies\&. This name uses the X\&.500 standard, so it is intended to be unique across the Internet\&. This is the X\&.500 Distinguished Name (DN) of the entity\&. See X\&.500 Distinguished Names\&. For example,
.sp     
.nf     
\f3CN=Java Duke, OU=Java Software Division, O=Oracle Corporation, C=US\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


These refer to the subject\&'s common name (CN), organizational unit (OU), organization (O), and country (C)\&.

\fISubject public key information\fR: This is the public key of the entity being named with an algorithm identifier that specifies which public key crypto system this key belongs to and any associated key parameters\&.
.TP     
Certificate Chains
The \f3keytool\fR command can create and manage keystore key entries that each contain a private key and an associated certificate chain\&. The first certificate in the chain contains the public key that corresponds to the private key\&.

When keys are first generated, the chain starts off containing a single element, a self-signed certificate\&. See \f3-genkeypair\fR in Commands\&. A self-signed certificate is one for which the issuer (signer) is the same as the subject\&. The subject is the entity whose public key is being authenticated by the certificate\&. Whenever the \f3-genkeypair\fR command is called to generate a new public/private key pair, it also wraps the public key into a self-signed certificate\&.

Later, after a Certificate Signing Request (CSR) was generated with the \f3-certreq\fR command and sent to a Certification Authority (CA), the response from the CA is imported with \f3-importcert\fR, and the self-signed certificate is replaced by a chain of certificates\&. See the \f3-certreq\fR and \f3-importcert\fR options in Commands\&. At the bottom of the chain is the certificate (reply) issued by the CA authenticating the subject\&'s public key\&. The next certificate in the chain is one that authenticates the CA\&'s public key\&.

In many cases, this is a self-signed certificate, which is a certificate from the CA authenticating its own public key, and the last certificate in the chain\&. In other cases, the CA might return a chain of certificates\&. In this case, the bottom certificate in the chain is the same (a certificate signed by the CA, authenticating the public key of the key entry), but the second certificate in the chain is a certificate signed by a different CA that authenticates the public key of the CA you sent the CSR to\&. The next certificate in the chain is a certificate that authenticates the second CA\&'s key, and so on, until a self-signed root certificate is reached\&. Each certificate in the chain (after the first) authenticates the public key of the signer of the previous certificate in the chain\&.

Many CAs only return the issued certificate, with no supporting chain, especially when there is a flat hierarchy (no intermediates CAs)\&. In this case, the certificate chain must be established from trusted certificate information already stored in the keystore\&.

A different reply format (defined by the PKCS #7 standard) includes the supporting certificate chain in addition to the issued certificate\&. Both reply formats can be handled by the \f3keytool\fR command\&.

The top-level (root) CA certificate is self-signed\&. However, the trust into the root\&'s public key does not come from the root certificate itself, but from other sources such as a newspaper\&. This is because anybody could generate a self-signed certificate with the distinguished name of, for example, the VeriSign root CA\&. The root CA public key is widely known\&. The only reason it is stored in a certificate is because this is the format understood by most tools, so the certificate in this case is only used as a vehicle to transport the root CA\&'s public key\&. Before you add the root CA certificate to your keystore, you should view it with the \f3-printcert\fR option and compare the displayed fingerprint with the well-known fingerprint obtained from a newspaper, the root CA\&'s Web page, and so on\&.
.TP     
The cacerts Certificates File
A certificates file named \f3cacerts\fR resides in the security properties directory, \f3java\&.home\elib\esecurity\fR on Windows and \f3java\&.home/lib/security\fR on Oracle Solaris, where \f3java\&.home\fR is the runtime environment\&'s directory, which would be the \f3jre\fR directory in the SDK or the top-level directory of the JRE\&.

The \f3cacerts\fR file represents a system-wide keystore with CA certificates\&. System administrators can configure and manage that file with the \f3keytool\fR command by specifying \f3jks\fR as the keystore type\&. The \f3cacerts\fR keystore file ships with a default set of root CA certificates\&. You can list the default certificates with the following command:
.sp     
.nf     
\f3keytool \-list \-keystore java\&.home/lib/security/cacerts\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The initial password of the \f3cacerts\fR keystore file is \f3changeit\fR\&. System administrators should change that password and the default access permission of that file upon installing the SDK\&.

\fINote:\fR It is important to verify your \f3cacerts\fR file\&. Because you trust the CAs in the \f3cacerts\fR file as entities for signing and issuing certificates to other entities, you must manage the \f3cacerts\fR file carefully\&. The \f3cacerts\fR file should contain only certificates of the CAs you trust\&. It is your responsibility to verify the trusted root CA certificates bundled in the \f3cacerts\fR file and make your own trust decisions\&.

To remove an untrusted CA certificate from the \f3cacerts\fR file, use the \f3delete\fR option of the \f3keytool\fR command\&. You can find the \f3cacerts\fR file in the JRE installation directory\&. Contact your system administrator if you do not have permission to edit this file
.TP     
Internet RFC 1421 Certificate Encoding Standard
Certificates are often stored using the printable encoding format defined by the Internet RFC 1421 standard, instead of their binary encoding\&. This certificate format, also known as Base64 encoding, makes it easy to export certificates to other applications by email or through some other mechanism\&.

Certificates read by the \f3-importcert\fR and \f3-printcert\fR commands can be in either this format or binary encoded\&. The \f3-exportcert\fR command by default outputs a certificate in binary encoding, but will instead output a certificate in the printable encoding format, when the \f3-rfc\fR option is specified\&.

The \f3-list\fR command by default prints the SHA256 fingerprint of a certificate\&. If the \f3-v\fR option is specified, then the certificate is printed in human-readable format\&. If the \f3-rfc\fR option is specified, then the certificate is output in the printable encoding format\&.

In its printable encoding format, the encoded certificate is bounded at the beginning and end by the following text:
.sp     
.nf     
\f3\-\-\-\-\-BEGIN CERTIFICATE\-\-\-\-\-\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3encoded certificate goes here\&. \fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3\-\-\-\-\-END CERTIFICATE\-\-\-\-\-\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP     
X\&.500 Distinguished Names
X\&.500 Distinguished Names are used to identify entities, such as those that are named by the \f3subject\fR and \f3issuer\fR (signer) fields of X\&.509 certificates\&. The \f3keytool\fR command supports the following subparts:

\fIcommonName\fR: The common name of a person such as Susan Jones\&.

\fIorganizationUnit\fR: The small organization (such as department or division) name\&. For example, Purchasing\&.

\fIlocalityName\fR: The locality (city) name, for example, Palo Alto\&.

\fIstateName\fR: State or province name, for example, California\&.

\fIcountry\fR: Two-letter country code, for example, CH\&.

When you supply a distinguished name string as the value of a \f3-dname\fR option, such as for the \f3-genkeypair\fR command, the string must be in the following format:
.sp     
.nf     
\f3CN=cName, OU=orgUnit, O=org, L=city, S=state, C=countryCode\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


All the italicized items represent actual values and the previous keywords are abbreviations for the following:
.sp     
.nf     
\f3CN=commonName\fP
.fi     
.nf     
\f3OU=organizationUnit\fP
.fi     
.nf     
\f3O=organizationName\fP
.fi     
.nf     
\f3L=localityName\fP
.fi     
.nf     
\f3S=stateName\fP
.fi     
.nf     
\f3C=country\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


A sample distinguished name string is:
.sp     
.nf     
\f3CN=Mark Smith, OU=Java, O=Oracle, L=Cupertino, S=California, C=US\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


A sample command using such a string is:
.sp     
.nf     
\f3keytool \-genkeypair \-dname "CN=Mark Smith, OU=Java, O=Oracle, L=Cupertino,\fP
.fi     
.nf     
\f3S=California, C=US" \-alias mark\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


Case does not matter for the keyword abbreviations\&. For example, CN, cn, and Cn are all treated the same\&.

Order matters; each subcomponent must appear in the designated order\&. However, it is not necessary to have all the subcomponents\&. You can use a subset, for example:
.sp     
.nf     
\f3CN=Steve Meier, OU=Java, O=Oracle, C=US\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


If a distinguished name string value contains a comma, then the comma must be escaped by a backslash (\e) character when you specify the string on a command line, as in:
.sp     
.nf     
\f3cn=Peter Schuster, ou=Java\e, Product Development, o=Oracle, c=US\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


It is never necessary to specify a distinguished name string on a command line\&. When the distinguished name is needed for a command, but not supplied on the command line, the user is prompted for each of the subcomponents\&. In this case, a comma does not need to be escaped by a backslash (\e)\&.
.SH WARNINGS    
.SS IMPORTING\ TRUSTED\ CERTIFICATES\ WARNING    
\fIImportant\fR: Be sure to check a certificate very carefully before importing it as a trusted certificate\&.
.PP
Windows Example:

View the certificate first with the \f3-printcert\fR command or the \f3-importcert\fR command without the \f3-noprompt\fR option\&. Ensure that the displayed certificate fingerprints match the expected ones\&. For example, suppose sends or emails you a certificate that you put it in a file named \f3\etmp\ecert\fR\&. Before you consider adding the certificate to your list of trusted certificates, you can execute a \f3-printcert\fR command to view its fingerprints, as follows:
.sp     
.nf     
\f3  keytool \-printcert \-file \etmp\ecert\fP
.fi     
.nf     
\f3    Owner: CN=ll, OU=ll, O=ll, L=ll, S=ll, C=ll\fP
.fi     
.nf     
\f3    Issuer: CN=ll, OU=ll, O=ll, L=ll, S=ll, C=ll\fP
.fi     
.nf     
\f3    Serial Number: 59092b34\fP
.fi     
.nf     
\f3    Valid from: Thu Sep 25 18:01:13 PDT 1997 until: Wed Dec 24 17:01:13 PST 1997\fP
.fi     
.nf     
\f3    Certificate Fingerprints:\fP
.fi     
.nf     
\f3         SHA1: 20:B6:17:FA:EF:E5:55:8A:D0:71:1F:E8:D6:9D:C0:37:13:0E:5E:FE\fP
.fi     
.nf     
\f3         SHA256: 90:7B:70:0A:EA:DC:16:79:92:99:41:FF:8A:FE:EB:90:\fP
.fi     
.nf     
\f3                 17:75:E0:90:B2:24:4D:3A:2A:16:A6:E4:11:0F:67:A4\fP
.fi     
.sp     

.PP
Oracle Solaris Example:

View the certificate first with the \f3-printcert\fR command or the \f3-importcert\fR command without the \f3-noprompt\fR option\&. Ensure that the displayed certificate fingerprints match the expected ones\&. For example, suppose someone sends or emails you a certificate that you put it in a file named \f3/tmp/cert\fR\&. Before you consider adding the certificate to your list of trusted certificates, you can execute a \f3-printcert\fR command to view its fingerprints, as follows:
.sp     
.nf     
\f3  keytool \-printcert \-file /tmp/cert\fP
.fi     
.nf     
\f3    Owner: CN=ll, OU=ll, O=ll, L=ll, S=ll, C=ll\fP
.fi     
.nf     
\f3    Issuer: CN=ll, OU=ll, O=ll, L=ll, S=ll, C=ll\fP
.fi     
.nf     
\f3    Serial Number: 59092b34\fP
.fi     
.nf     
\f3    Valid from: Thu Sep 25 18:01:13 PDT 1997 until: Wed Dec 24 17:01:13 PST 1997\fP
.fi     
.nf     
\f3    Certificate Fingerprints:\fP
.fi     
.nf     
\f3         SHA1: 20:B6:17:FA:EF:E5:55:8A:D0:71:1F:E8:D6:9D:C0:37:13:0E:5E:FE\fP
.fi     
.nf     
\f3         SHA256: 90:7B:70:0A:EA:DC:16:79:92:99:41:FF:8A:FE:EB:90:\fP
.fi     
.nf     
\f3                 17:75:E0:90:B2:24:4D:3A:2A:16:A6:E4:11:0F:67:A4\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Then call or otherwise contact the person who sent the certificate and compare the fingerprints that you see with the ones that they show\&. Only when the fingerprints are equal is it guaranteed that the certificate was not replaced in transit with somebody else\&'s certificate such as an attacker\&'s certificate\&. If such an attack took place, and you did not check the certificate before you imported it, then you would be trusting anything the attacker signed, for example, a JAR file with malicious class files inside\&.
.PP
\fINote:\fR It is not required that you execute a \f3-printcert\fR command before importing a certificate\&. This is because before you add a certificate to the list of trusted certificates in the keystore, the \f3-importcert\fR command prints out the certificate information and prompts you to verify it\&. You can then stop the import operation\&. However, you can do this only when you call the \f3-importcert\fR command without the \f3-noprompt\fR option\&. If the \f3-noprompt\fR option is specified, then there is no interaction with the user\&.
.SS PASSWORDS\ WARNING    
Most commands that operate on a keystore require the store password\&. Some commands require a private/secret key password\&. Passwords can be specified on the command line in the \f3-storepass\fR and \f3-keypass\fR options\&. However, a password should not be specified on a command line or in a script unless it is for testing, or you are on a secure system\&. When you do not specify a required password option on a command line, you are prompted for it\&.
.SS CERTIFICATE\ CONFORMANCE\ WARNING    
The Internet standard RFC 5280 has defined a profile on conforming X\&.509 certificates, which includes what values and value combinations are valid for certificate fields and extensions\&. See the standard at http://tools\&.ietf\&.org/rfc/rfc5280\&.txt
.PP
The \f3keytool\fR command does not enforce all of these rules so it can generate certificates that do not conform to the standard\&. Certificates that do not conform to the standard might be rejected by JRE or other applications\&. Users should ensure that they provide the correct options for \f3-dname\fR, \f3-ext\fR, and so on\&.
.SH NOTES    
.SS IMPORT\ A\ NEW\ TRUSTED\ CERTIFICATE    
Before you add the certificate to the keystore, the \f3keytool\fR command verifies it by attempting to construct a chain of trust from that certificate to a self-signed certificate (belonging to a root CA), using trusted certificates that are already available in the keystore\&.
.PP
If the \f3-trustcacerts\fR option was specified, then additional certificates are considered for the chain of trust, namely the certificates in a file named \f3cacerts\fR\&.
.PP
If the \f3keytool\fR command fails to establish a trust path from the certificate to be imported up to a self-signed certificate (either from the keystore or the \f3cacerts\fR file), then the certificate information is printed, and the user is prompted to verify it by comparing the displayed certificate fingerprints with the fingerprints obtained from some other (trusted) source of information, which might be the certificate owner\&. Be very careful to ensure the certificate is valid before importing it as a trusted certificate\&. See Importing Trusted Certificates Warning\&. The user then has the option of stopping the import operation\&. If the \f3-noprompt\fR option is specified, then there is no interaction with the user\&.
.SS IMPORT\ A\ CERTIFICATE\ REPLY    
When you import a certificate reply, the certificate reply is validated with trusted certificates from the keystore, and optionally, the certificates configured in the \f3cacerts\fR keystore file when the \f3-trustcacert\fR\f3s\fR option is specified\&. See The cacerts Certificates File\&.
.PP
The methods of determining whether the certificate reply is trusted are as follows:
.TP 0.2i    
\(bu
If the reply is a single X\&.509 certificate, then the \f3keytool\fR command attempts to establish a trust chain, starting at the certificate reply and ending at a self-signed certificate (belonging to a root CA)\&. The certificate reply and the hierarchy of certificates is used to authenticate the certificate reply from the new certificate chain of aliases\&. If a trust chain cannot be established, then the certificate reply is not imported\&. In this case, the \f3keytool\fR command does not print the certificate and prompt the user to verify it, because it is very difficult for a user to determine the authenticity of the certificate reply\&.
.TP 0.2i    
\(bu
If the reply is a PKCS #7 formatted certificate chain or a sequence of X\&.509 certificates, then the chain is ordered with the user certificate first followed by zero or more CA certificates\&. If the chain ends with a self-signed root CA certificate and the\f3-trustcacerts\fR option was specified, the \f3keytool\fR command attempts to match it with any of the trusted certificates in the keystore or the \f3cacerts\fR keystore file\&. If the chain does not end with a self-signed root CA certificate and the \f3-trustcacerts\fR option was specified, the \f3keytool\fR command tries to find one from the trusted certificates in the keystore or the \f3cacerts\fR keystore file and add it to the end of the chain\&. If the certificate is not found and the \f3-noprompt\fR option is not specified, the information of the last certificate in the chain is printed, and the user is prompted to verify it\&.
.PP
If the public key in the certificate reply matches the user\&'s public key already stored with \f3alias\fR, then the old certificate chain is replaced with the new certificate chain in the reply\&. The old chain can only be replaced with a valid \f3keypass\fR, and so the password used to protect the private key of the entry is supplied\&. If no password is provided, and the private key password is different from the keystore password, the user is prompted for it\&.
.PP
This command was named \f3-import\fR in earlier releases\&. This old name is still supported in this release\&. The new name, \f3-importcert\fR, is preferred going forward\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
jar(1)
.TP 0.2i    
\(bu
jarsigner(1)
.TP 0.2i    
\(bu
Trail: Security Features in Java SE at http://docs\&.oracle\&.com/javase/tutorial/security/index\&.html
.RE
.br
'pl 8.5i
'bp
