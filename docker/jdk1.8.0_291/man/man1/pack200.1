'\" t
.\"  Copyright (c) 2004, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Java Deployment Tools
.\"     Title: pack200.1
.\"
.if n .pl 99999
.TH pack200 1 "21 November 2013" "JDK 8" "Java Deployment Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
pack200 \- Packages a JAR file into a compressed pack200 file for web deployment\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBpack200\fR [\fIoptions\fR] \fIoutput\-file\fR \fIJAR\-file\fR
.fi     
.sp     
Options can be in any order\&. The last option on the command line or in a properties file supersedes all previously specified options\&.
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
\fIoutput-file\fR
Name of the output file\&.
.TP     
\fIJAR-file\fR
Name of the input file\&.
.SH DESCRIPTION    
The \f3pack200\fR command is a Java application that transforms a JAR file into a compressed pack200 file with the Java gzip compressor\&. The pack200 files are highly compressed files that can be directly deployed to save bandwidth and reduce download time\&.
.PP
The \f3pack200\fR command has several options to fine-tune and set the compression engine\&. The typical usage is shown in the following example, where \f3myarchive\&.pack\&.gz\fR is produced with the default \f3pack200\fR command settings:
.sp     
.nf     
\f3pack200 myarchive\&.pack\&.gz myarchive\&.jar\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH OPTIONS    
.TP
-r, --repack
.br
Produces a JAR file by packing and unpacking a JAR file\&. The resulting file can be used as an input to the \f3jarsigner\fR(1) tool\&. The following example packs and unpacks the myarchive\&.jar file:
.sp     
.nf     
\f3pack200 \-\-repack myarchive\-packer\&.jar myarchive\&.jar\fP
.fi     
.nf     
\f3pack200 \-\-repack myarchive\&.jar\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The following example preserves the order of files in the input file\&.
.TP
-g, --no-gzip
.br
Produces a \f3pack200\fR file\&. With this option, a suitable compressor must be used, and the target system must use a corresponding decompresser\&.
.sp     
.nf     
\f3pack200 \-\-no\-gzip myarchive\&.pack myarchive\&.jar\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-G, --strip-debug
.br
Strips debugging attributes from the output\&. These include \f3SourceFile\fR, \f3LineNumberTable\fR, \f3LocalVariableTable\fR and \f3LocalVariableTypeTable\fR\&. Removing these attributes reduces the size of both downloads and installations, but reduces the usefulness of debuggers\&.
.TP
--keep-file-order
.br
Preserve the order of files in the input file\&. This is the default behavior\&.
.TP
-O, --no-keep-file-order
.br
The packer reorders and transmits all elements\&. The packer can also remove JAR directory names to reduce the download size\&. However, certain JAR file optimizations, such as indexing, might not work correctly\&.
.TP
-S\fIvalue\fR , --segment-limit=\fIvalue\fR
.br
The value is the estimated target size \fIN\fR (in bytes) of each archive segment\&. If a single input file requires more than \fIN\fR bytes, then its own archive segment is provided\&. As a special case, a value of \f3-1\fR produces a single large segment with all input files, while a value of 0 produces one segment for each class\&. Larger archive segments result in less fragmentation and better compression, but processing them requires more memory\&.

The size of each segment is estimated by counting the size of each input file to be transmitted in the segment with the size of its name and other transmitted properties\&.

The default is -1, which means that the packer creates a single segment output file\&. In cases where extremely large output files are generated, users are strongly encouraged to use segmenting or break up the input file into smaller JARs\&.

A 10 MB JAR packed without this limit typically packs about 10 percent smaller, but the packer might require a larger Java heap (about 10 times the segment limit)\&.
.TP
-E\fIvalue\fR , --effort=\fIvalue\fR
.br
If the value is set to a single decimal digit, then the packer uses the indicated amount of effort in compressing the archive\&. Level 1 might produce somewhat larger size and faster compression speed, while level 9 takes much longer, but can produce better compression\&. The special value 0 instructs the \f3pack200\fR command to copy through the original JAR file directly with no compression\&. The JSR 200 standard requires any unpacker to understand this special case as a pass-through of the entire archive\&.

The default is 5, to invest a modest amount of time to produce reasonable compression\&.
.TP
-H\fIvalue\fR , --deflate-hint=\fIvalue\fR
.br
Overrides the default, which preserves the input information, but can cause the transmitted archive to be larger\&. The possible values are: \f3true\fR, \f3false\fR, or \f3keep\fR\&.

If the \f3value\fR is \f3true\fR or false, then the \f3packer200\fR command sets the deflation hint accordingly in the output archive and does not transmit the individual deflation hints of archive elements\&.

The \f3keep\fR value preserves deflation hints observed in the input JAR\&. This is the default\&.
.TP
-m\fIvalue\fR , --modification-time=\fIvalue\fR
.br
The possible values are \f3latest\fR and \f3keep\fR\&.

If the value is latest, then the packer attempts to determine the latest modification time, among all the available entries in the original archive, or the latest modification time of all the available entries in that segment\&. This single value is transmitted as part of the segment and applied to all the entries in each segment\&. This can marginally decrease the transmitted size of the archive at the expense of setting all installed files to a single date\&.

If the value is \f3keep\fR, then modification times observed in the input JAR are preserved\&. This is the default\&.
.TP
-P\fIfile\fR , --pass-file=\fIfile\fR
.br
Indicates that a file should be passed through bytewise with no compression\&. By repeating the option, multiple files can be specified\&. There is no pathname transformation, except that the system file separator is replaced by the JAR file separator forward slash (/)\&. The resulting file names must match exactly as strings with their occurrences in the JAR file\&. If \f3file\fR is a directory name, then all files under that directory are passed\&.
.TP
-U\fIaction\fR , --unknown-attribute=\fIaction\fR
.br
Overrides the default behavior, which means that the class file that contains the unknown attribute is passed through with the specified \f3action\fR\&. The possible values for actions are \f3error\fR, \f3strip\fR, or \f3pass\fR\&.

If the value is \f3error\fR, then the entire \f3pack200\fR command operation fails with a suitable explanation\&.

If the value is \f3strip\fR, then the attribute is dropped\&. Removing the required Java Virtual Machine (JVM) attributes can cause class loader failures\&.

If the value is \f3pass\fR, then the entire class is transmitted as though it is a resource\&.
.TP
.nf
-C\fIattribute-name\fR=\fIlayout\fR , --class-attribute=\fIattribute-name\fR=\fIaction\fR
.br
.fi
See next option\&.
.TP
.nf
-F\fIattribute-name\fR=\fIlayout\fR , --field-attribute=\fIattribute-name\fR=\fIaction\fR
.br
.fi
See next option\&.
.TP
.nf
-M\fIattribute-name\fR=\fIlayout\fR , --method-attribute=\fIattribute-name\fR=\fIaction\fR
.br
.fi
See next option\&.
.TP
.nf
-D\fIattribute-name\fR=\fIlayout\fR , --code-attribute=\fIattribute-name\fR=\fIaction\fR
.br
.fi
With the previous four options, the attribute layout can be specified for a class entity, such as \f3class-attribute\fR, \f3field-attribute\fR, \f3method-attribute\fR, and \f3code-attribute\fR\&. The \fIattribute-name\fR is the name of the attribute for which the layout or action is being defined\&. The possible values for \fIaction\fR are \f3some-layout-string\fR, \f3error\fR, \f3strip\fR, \f3pass\fR\&.

\f3some-layout-string\fR: The layout language is defined in the JSR 200 specification, for example: \f3--class-attribute=SourceFile=RUH\fR\&.

If the value is \f3error\fR, then the \f3pack200\fR operation fails with an explanation\&.

If the value is \f3strip\fR, then the attribute is removed from the output\&. Removing JVM-required attributes can cause class loader failures\&. For example, \f3--class-attribute=CompilationID=pass\fR causes the class file that contains this attribute to be passed through without further action by the packer\&.

If the value is \f3pass\fR, then the entire class is transmitted as though it is a resource\&.
.TP
-f \fIpack\&.properties\fR , --config-file=\fIpack\&.properties\fR
.br
A configuration file, containing Java properties to initialize the packer, can be specified on the command line\&.
.sp     
.nf     
\f3pack200 \-f pack\&.properties myarchive\&.pack\&.gz myarchive\&.jar\fP
.fi     
.nf     
\f3more pack\&.properties\fP
.fi     
.nf     
\f3# Generic properties for the packer\&.\fP
.fi     
.nf     
\f3modification\&.time=latest\fP
.fi     
.nf     
\f3deflate\&.hint=false\fP
.fi     
.nf     
\f3keep\&.file\&.order=false\fP
.fi     
.nf     
\f3# This option will cause the files bearing new attributes to\fP
.fi     
.nf     
\f3# be reported as an error rather than passed uncompressed\&.\fP
.fi     
.nf     
\f3unknown\&.attribute=error\fP
.fi     
.nf     
\f3# Change the segment limit to be unlimited\&.\fP
.fi     
.nf     
\f3segment\&.limit=\-1\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-v, --verbose
.br
Outputs minimal messages\&. Multiple specification of this option will create more verbose messages\&.
.TP
-q, --quiet
.br
Specifies quiet operation with no messages\&.
.TP
-l\fIfilename\fR , --log-file=\fIfilename\fR
.br
Specifies a log file to output messages\&.
.TP
-?, -h, --help
.br
Prints help information about this command\&.
.TP
-V, --version
.br
Prints version information about this command\&.
.TP
-J\fIoption\fR
.br
Passes the specified option to the Java Virtual Machine\&. For more information, see the reference page for the java(1) command\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&.
.SH EXIT\ STATUS    
The following exit values are returned: 0 for successful completion and a number greater than 0 when an error occurs\&.
.SH NOTES    
This command should not be confused with \f3pack\fR(1)\&. The \f3pack\fR and \f3pack200\fR commands are separate products\&.
.PP
The Java SE API Specification provided with the JDK is the superseding authority, when there are discrepancies\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
unpack200(1)
.TP 0.2i    
\(bu
jar(1)
.TP 0.2i    
\(bu
jarsigner(1)
.RE
.br
'pl 8.5i
'bp
