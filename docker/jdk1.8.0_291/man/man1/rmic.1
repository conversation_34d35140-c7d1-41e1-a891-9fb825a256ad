'\" t
.\"  Copyright (c) 1997, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Remote Method Invocation (RMI) Tools
.\"     Title: rmic.1
.\"
.if n .pl 99999
.TH rmic 1 "21 November 2013" "JDK 8" "Remote Method Invocation (RMI) Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
rmic \- Generates stub, skeleton, and tie classes for remote objects that use the Java Remote Method Protocol (JRMP) or Internet Inter-Orb protocol (IIOP)\&. Also generates Object Management Group (OMG) Interface Definition Language (IDL)
.SH SYNOPSIS    
.sp     
.nf     

\fBrmic\fR [ \fIoptions\fR ] \fIpackage\-qualified\-class\-names\fR
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line \f3options\fR\&. See Options\&.
.TP     
\fIpackage-qualified-class-names\fR
Class names that include their packages, for example, \f3java\&.awt\&.Color\fR\&.
.SH DESCRIPTION    
\fIDeprecation Note:\fR Support for static generation of Java Remote Method Protocol (JRMP) stubs and skeletons has been deprecated\&. Oracle recommends that you use dynamically generated JRMP stubs instead, eliminating the need to use this tool for JRMP-based applications\&. See the \f3java\&.rmi\&.server\&.UnicastRemoteObject\fR specification at http://docs\&.oracle\&.com/javase/8/docs/api/java/rmi/server/UnicastRemoteObject\&.html for further information\&.
.PP
The \f3rmic\fR compiler generates stub and skeleton class files using the Java Remote Method Protocol (JRMP) and stub and tie class files (IIOP protocol) for remote objects\&. These class files are generated from compiled Java programming language classes that are remote object implementation classes\&. A remote implementation class is a class that implements the interface \f3java\&.rmi\&.Remote\fR\&. The class names in the \f3rmic\fR command must be for classes that were compiled successfully with the \f3javac\fR command and must be fully package qualified\&. For example, running the \f3rmic\fR command on the class file name \f3HelloImpl\fR as shown here creates the \f3HelloImpl_Stub\&.class\fRfile in the hello subdirectory (named for the class\&'s package):
.sp     
.nf     
\f3rmic hello\&.HelloImpl\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
A skeleton for a remote object is a JRMP protocol server-side entity that has a method that dispatches calls to the remote object implementation\&.
.PP
A tie for a remote object is a server-side entity similar to a skeleton, but communicates with the client with the IIOP protocol\&.
.PP
A stub is a client-side proxy for a remote object that is responsible for communicating method invocations on remote objects to the server where the actual remote object implementation resides\&. A client\&'s reference to a remote object, therefore, is actually a reference to a local stub\&.
.PP
By default, the \f3rmic\fR command generates stub classes that use the 1\&.2 JRMP stub protocol version only, as though the \f3-v1\&.2\fR option was specified\&. The \f3-vcompat\fR option was the default in releases before 5\&.0\&. Use the \f3-iiop\fR option to generate stub and tie classes for the IIOP protocol\&. See Options\&.
.PP
A stub implements only the remote interfaces, and not any local interfaces that the remote object also implements\&. Because a JRMP stub implements the same set of remote interfaces as the remote object, a client can use the Java programming language built-in operators for casting and type checking\&. For IIOP, the \f3PortableRemoteObject\&.narrow\fR method must be used\&.
.SH OPTIONS    
.TP
-bootclasspath \fIpath\fR
.br
Overrides the location of bootstrap class files\&.
.TP
-classpath path
.br
Specifies the path the \f3rmic\fR command uses to look up classes\&. This option overrides the default or the \f3CLASSPATH\fR environment variable when it is set\&. Directories are separated by colons\&. The general format for path is: \f3\&.:<your_path>\fR, for example: \f3\&.:/usr/local/java/classes\fR\&.
.TP
-d \fIdirectory\fR
.br
Specifies the root destination directory for the generated class hierarchy\&. You can use this option to specify a destination directory for the stub, skeleton, and tie files\&. For example, the following command places the stub and skeleton classes derived from MyClass into the directory /java/classes/exampleclass\&.
.sp     
.nf     
\f3rmic \-d /java/classes exampleclass\&.MyClass\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


If the \f3-d\fR option is not specified, then the default behavior is as if \f3-d \&.\fR was specified\&. The package hierarchy of the target class is created in the current directory, and stub/tie/skeleton files are placed within it\&. In some earlier releases of the \f3rmic\fR command, if the \f3-d\fR option was not specified, then the package hierarchy was not created, and all of the output files were placed directly in the current directory\&.
.TP
-extdirs \fIpath\fR
.br
Overrides the location of installed extensions\&.
.TP
-g
.br
Enables the generation of all debugging information, including local variables\&. By default, only line number information is generated\&.
.TP
-idl
.br
Causes the \f3rmic\fR command to generate OMG IDL for the classes specified and any classes referenced\&. IDL provides a purely declarative, programming language-independent way to specify an API for an object\&. The IDL is used as a specification for methods and data that can be written in and called from any language that provides CORBA bindings\&. This includes Java and C++ among others\&. See Java IDL: IDL to Java Language Mapping at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/mapping/jidlMapping\&.html

When the \f3-idl\fR option is used, other options also include:
.RS     
.TP 0.2i    
\(bu
The \f3-always\fR or \f3-alwaysgenerate\fR options force regeneration even when existing stubs/ties/IDL are newer than the input class\&.
.TP 0.2i    
\(bu
The \f3-factory\fR option uses the \f3factory\fR keyword in generated IDL\&.
.TP 0.2i    
\(bu
The \f3-idlModule\fR from J\f3avaPackage[\&.class]\fR\f3toIDLModule\fR specifies \f3IDLEntity\fR package mapping, for example: \f3-idlModule\fR\f3my\&.module my::real::idlmod\fR\&.
.TP 0.2i    
\(bu
\f3-idlFile\fR\f3fromJavaPackage[\&.class] toIDLFile\fR specifies \f3IDLEntity\fR file mapping, for example: \f3-idlFile test\&.pkg\&.X TEST16\&.idl\fR\&.
.RE     

.TP
-iiop
.br
Causes the \f3rmic\fR command to generate IIOP stub and tie classes, rather than JRMP stub and skeleton classes\&. A stub class is a local proxy for a remote object and is used by clients to send calls to a server\&. Each remote interface requires a stub class, which implements that remote interface\&. A client reference to a remote object is a reference to a stub\&. Tie classes are used on the server side to process incoming calls, and dispatch the calls to the proper implementation class\&. Each implementation class requires a tie class\&.

If you call the \f3rmic\fR command with the \f3-iiop\fR, then it generates stubs and ties that conform to this naming convention:
.sp     
.nf     
\f3_<implementationName>_stub\&.class\fP
.fi     
.nf     
\f3_<interfaceName>_tie\&.class\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.RS     
.TP 0.2i    
\(bu
When you use the \f3-iiop\fR option, other options also include:
.TP 0.2i    
\(bu
The \f3-always\fR or \f3-alwaysgenerate\fR options force regeneration even when existing stubs/ties/IDL are newer than the input class\&.
.TP 0.2i    
\(bu
The \f3-nolocalstubs\fR option means do not create stubs optimized for same-process clients and servers\&.
.TP 0.2i    
\(bu
The \f3-noValueMethods\fR option must be used with the \f3-idl\fR option\&. The \f3-noValueMethods\fR option prevents the addition of \f3valuetype\fR methods and initializers to emitted IDL\&. These methods and initializers are optional for valuetypes, and are generated unless the \f3-noValueMethods\fR option is specified with the \f3-idl\fR option\&.
.TP 0.2i    
\(bu
The \f3-poa\fR option changes the inheritance from \f3org\&.omg\&.CORBA_2_3\&.portable\&.ObjectImpl\fR to \f3org\&.omg\&.PortableServer\&.Servant\fR\&. The \f3PortableServer\fR module for the Portable Object Adapter (POA) defines the native \f3Servant\fR type\&. In the Java programming language, the \f3Servant\fR type is mapped to the \f3Java org\&.omg\&.PortableServer\&.Servant\fR class\&. It serves as the base class for all POA servant implementations and provides a number of methods that can be called by the application programmer, and methods that are called by the POA and that can be overridden by the user to control aspects of servant behavior\&. Based on the OMG IDL to Java Language Mapping Specification, CORBA V 2\&.3\&.1 ptc/00-01-08\&.pdf\&..RE     

.TP
-J
.br
Used with any Java command, the \f3-J\fR option passes the argument that follows the \f3-J\fR (no spaces between the \f3-J\fRand the argument) to the Java interpreter
.TP
-keep or -keepgenerated
.br
Retains the generated \f3\&.java\fR source files for the stub, skeleton, and tie classes and writes them to the same directory as the\f3\&.class\fR files\&.
.TP
-nowarn
.br
Turns off warnings\&. When the \f3-nowarn\fR options is used\&. The compiler does not print out any warnings\&.
.TP
-nowrite
.br
Does not write compiled classes to the file system\&.
.TP
-vcompat (deprecated)
.br
Generates stub and skeleton classes that are compatible with both the 1\&.1 and 1\&.2 JRMP stub protocol versions\&. This option was the default in releases before 5\&.0\&. The generated stub classes use the 1\&.1 stub protocol version when loaded in a JDK 1\&.1 virtual machine and use the 1\&.2 stub protocol version when loaded into a 1\&.2 (or later) virtual machine\&. The generated skeleton classes support both 1\&.1 and 1\&.2 stub protocol versions\&. The generated classes are relatively large to support both modes of operation\&. Note: This option has been deprecated\&. See Description\&.
.TP
-verbose
.br
Causes the compiler and linker to print out messages about what classes are being compiled and what class files are being loaded\&.
.TP
-v1\&.1 (deprecated)
.br
Generates stub and skeleton classes for the 1\&.1 JRMP stub protocol version only\&. The \f3-v1\&.1\fR option is only useful for generating stub classes that are serialization-compatible with preexisting, statically deployed stub classes that were generated by the \f3rmic\fR command from JDK 1\&.1 and that cannot be upgraded (and dynamic class loading is not being used)\&. Note: This option has been deprecated\&. See Description\&.
.TP
-v1\&.2 (deprecated)
.br
(Default) Generates stub classes for the 1\&.2 JRMP stub protocol version only\&. No skeleton classes are generated because skeleton classes are not used with the 1\&.2 stub protocol version\&. The generated stub classes do not work when they are loaded into a JDK 1\&.1 virtual machine\&. Note: This option has been deprecated\&. See Description\&.
.SH ENVIRONMENT\ VARIABLES    
.TP     
CLASSPATH
Used to provide the system a path to user-defined classes\&. Directories are separated by colons, for example: \f3\&.:/usr/local/java/classes\fR\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
javac(1)
.TP 0.2i    
\(bu
java(1)
.TP 0.2i    
\(bu
Setting the Class Path
.RE
.br
'pl 8.5i
'bp
