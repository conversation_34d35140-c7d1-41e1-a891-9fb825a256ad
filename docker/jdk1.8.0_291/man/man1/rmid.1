'\" t
.\"  Copyright (c) 1998, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Remote Method Invocation (RMI) Tools
.\"     Title: rmid.1
.\"
.if n .pl 99999
.TH rmid 1 "21 November 2013" "JDK 8" "Remote Method Invocation (RMI) Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
rmid \- Starts the activation system daemon that enables objects to be registered and activated in a Java Virtual Machine (JVM)\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBrmid\fR [\fIoptions\fR]
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.SH DESCRIPTION    
The \f3rmid\fR command starts the activation system daemon\&. The activation system daemon must be started before activatable objects can be either registered with the activation system or activated in a JVM\&. For details on how to write programs that use activatable objects, the \fIUsing Activation\fR tutorial at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/rmi/activation/overview\&.html
.PP
Start the daemon by executing the \f3rmid\fR command and specifying a security policy file, as follows:
.sp     
.nf     
\f3rmid \-J\-Djava\&.security\&.policy=rmid\&.policy\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
When you run Oracle\(cqs implementation of the \f3rmid\fR command, by default you must specify a security policy file so that the \f3rmid\fR command can verify whether or not the information in each \f3ActivationGroupDesc\fR is allowed to be used to start a JVM for an activation group\&. Specifically, the command and options specified by the \f3CommandEnvironment\fR and any properties passed to an \f3ActivationGroupDesc\fR constructor must now be explicitly allowed in the security policy file for the \f3rmid\fR command\&. The value of the \f3sun\&.rmi\&.activation\&.execPolicy\fR property dictates the policy that the \f3rmid\fR command uses to determine whether or not the information in an \f3ActivationGroupDesc\fR can be used to start a JVM for an activation group\&. For more information see the description of the -J-Dsun\&.rmi\&.activation\&.execPolicy=policy option\&.
.PP
Executing the \f3rmid\fR command starts the Activator and an internal registry on the default port1098 and binds an \f3ActivationSystem\fR to the name \f3java\&.rmi\&.activation\&.ActivationSystem\fR in this internal registry\&.
.PP
To specify an alternate port for the registry, you must specify the \f3-port\fR option when you execute the \f3rmid\fR command\&. For example, the following command starts the activation system daemon and a registry on the registry\&'s default port, 1099\&.
.sp     
.nf     
\f3rmid \-J\-Djava\&.security\&.policy=rmid\&.policy \-port 1099\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH START\ RMID\ ON\ DEMAND    
An alternative to starting \f3rmid\fR from the command line is to configure \f3inetd\fR (Oracle Solaris) or \f3xinetd\fR (Linux) to start \f3rmid\fR on demand\&.
.PP
When RMID starts, it attempts to obtain an inherited channel (inherited from \f3inetd\fR/\f3xinetd\fR) by calling the \f3System\&.inheritedChannel\fR method\&. If the inherited channel is null or not an instance of \f3java\&.nio\&.channels\&.ServerSocketChannel\fR, then RMID assumes that it was not started by \f3inetd\fR/\f3xinetd\fR, and it starts as previously described\&.
.PP
If the inherited channel is a \f3ServerSocketChannel\fR instance, then RMID uses the \f3java\&.net\&.ServerSocket\fR obtained from the \f3ServerSocketChannel\fR as the server socket that accepts requests for the remote objects it exports: The registry in which the \f3java\&.rmi\&.activation\&.ActivationSystem\fR is bound and the \f3java\&.rmi\&.activation\&.Activator\fR remote object\&. In this mode, RMID behaves the same as when it is started from the command line, except in the following cases:
.TP 0.2i    
\(bu
Output printed to \f3System\&.err\fR is redirected to a file\&. This file is located in the directory specified by the \f3java\&.io\&.tmpdir\fR system property (typically \f3/var/tmp\fR or \f3/tmp\fR) with the prefix \f3rmid-err\fR and the suffix \f3tmp\fR\&.
.TP 0.2i    
\(bu
The \f3-port\fR option is not allowed\&. If this option is specified, then RMID exits with an error message\&.
.TP 0.2i    
\(bu
The \f3-log\fR option is required\&. If this option is not specified, then RMID exits with an error message
.PP
See the man pages for \f3inetd\fR (Oracle Solaris) or \f3xinetd\fR (Linux) for details on how to configure services to be started on demand\&.
.SH OPTIONS    
.TP
-C\fIoption\fR
.br
Specifies an option that is passed as a command-line argument to each child process (activation group) of the \f3rmid\fR command when that process is created\&. For example, you could pass a property to each virtual machine spawned by the activation system daemon:
.sp     
.nf     
\f3rmid \-C\-Dsome\&.property=value\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


This ability to pass command-line arguments to child processes can be useful for debugging\&. For example, the following command enables server-call logging in all child JVMs\&.
.sp     
.nf     
\f3rmid \-C\-Djava\&.rmi\&.server\&.logCalls=true\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-J\fIoption\fR
.br
Specifies an option that is passed to the Java interpreter running RMID\&. For example, to specify that the \f3rmid\fR command use a policy file named \f3rmid\&.policy\fR, the \f3-J\fR option can be used to define the \f3java\&.security\&.policy\fR property on the \f3rmid\fR command line, for example:
.sp     
.nf     
\f3rmid \-J\-Djava\&.security\&.policy\-rmid\&.policy\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-J-Dsun\&.rmi\&.activation\&.execPolicy=\fIpolicy\fR
.br
Specifies the policy that RMID employs to check commands and command-line options used to start the JVM in which an activation group runs\&. Please note that this option exists only in Oracle\&'s implementation of the Java RMI activation daemon\&. If this property is not specified on the command line, then the result is the same as though \f3-J-Dsun\&.rmi\&.activation\&.execPolicy=default\fR were specified\&. The possible values of \f3policy\fR can be \f3default\fR, \f3policyClassName\fR, or \f3none\fR\&.
.RS     
.TP 0.2i    
\(bu
default

The \f3default\fR or unspecified value \f3execPolicy\fR allows the \f3rmid\fR command to execute commands with specific command-line options only when the \f3rmid\fR command was granted permission to execute those commands and options in the security policy file that the \f3rmid\fR command uses\&. Only the default activation group implementation can be used with the default execution policy\&.

The \f3rmid\fR command starts a JVM for an activation group with the information in the group\&'s registered activation group descriptor, an \f3ActivationGroupDesc\fR\&. The group descriptor specifies an optional \f3ActivationGroupDesc\&.CommandEnvironment\fR that includes the command to execute to start the activation group and any command-line options to be added to the command line\&. By default, the \f3rmid\fR command uses the \f3java\fR command found in \f3java\&.home\fR\&. The group descriptor also contains properties overrides that are added to the command line as options defined as: \f3-D<property>=<value>\fR\&.The \f3com\&.sun\&.rmi\&.rmid\&.ExecPermission\fR permission grants the \f3rmid\fR command permission to execute a command that is specified in the group descriptor\&'s \f3CommandEnvironment\fR to start an activation group\&. The \f3com\&.sun\&.rmi\&.rmid\&.ExecOptionPermission\fR permission enables the \f3rmid\fR command to use command-line options, specified as properties overrides in the group descriptor or as options in the \f3CommandEnvironment\fR when starting the activation group\&.When granting the \f3rmid\fR command permission to execute various commands and options, the permissions \f3ExecPermission\fR and \f3ExecOptionPermission\fR must be granted to all code sources\&.

\fIExecPermission\fR

The \f3ExecPermission\fR class represents permission for the \f3rmid\fR command to execute a specific command to start an activation group\&.

\fISyntax\fR: The name of an \f3ExecPermission\fR is the path name of a command to grant the \f3rmid\fR command permission to execute\&. A path name that ends in a slash (/) and an asterisk (*) indicates that all of the files contained in that directory where slash is the file-separator character, \f3File\&.separatorChar\fR\&. A path name that ends in a slash (/) and a minus sign (-) indicates all files and subdirectories contained in that directory (recursively)\&. A path name that consists of the special token \f3<<ALL FILES>>\fR matches any file\&.

A path name that consists of an asterisk (*) indicates all the files in the current directory\&. A path name that consists of a minus sign (-) indicates all the files in the current directory and (recursively) all files and subdirectories contained in the current directory\&.

\fIExecOptionPermission\fR

The \f3ExecOptionPermission\fR class represents permission for the \f3rmid\fR command to use a specific command-line option when starting an activation group\&. The name of an \f3ExecOptionPermission\fR is the value of a command-line option\&.

\fISyntax\fR: Options support a limited wild card scheme\&. An asterisk signifies a wild card match, and it can appear as the option name itself (matches any option), or an asterisk (*) can appear at the end of the option name only when the asterisk (*) follows a dot (\&.) or an equals sign (=)\&.

For example: \f3*\fR or \f3-Dmydir\&.*\fR or \f3-Da\&.b\&.c=*\fR is valid, but \f3*mydir\fR or \f3-Da*b\fR or \f3ab*\fR is not\&.

\fIPolicy file for rmid\fR

When you grant the \f3rmid\fR command permission to execute various commands and options, the permissions \f3ExecPermission\fR and \f3ExecOptionPermission\fR must be granted to all code sources (universally)\&. It is safe to grant these permissions universally because only the \f3rmid\fR command checks these permissions\&.

An example policy file that grants various execute permissions to the \f3rmid\fR command is:
.sp     
.nf     
\f3grant {\fP
.fi     
.nf     
\f3    permission com\&.sun\&.rmi\&.rmid\&.ExecPermission\fP
.fi     
.nf     
\f3        "/files/apps/java/jdk1\&.7\&.0/solaris/bin/java";\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3    permission com\&.sun\&.rmi\&.rmid\&.ExecPermission\fP
.fi     
.nf     
\f3        "/files/apps/rmidcmds/*";\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3    permission com\&.sun\&.rmi\&.rmid\&.ExecOptionPermission\fP
.fi     
.nf     
\f3        "\-Djava\&.security\&.policy=/files/policies/group\&.policy";\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3    permission com\&.sun\&.rmi\&.rmid\&.ExecOptionPermission\fP
.fi     
.nf     
\f3        "\-Djava\&.security\&.debug=*";\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3    permission com\&.sun\&.rmi\&.rmid\&.ExecOptionPermission\fP
.fi     
.nf     
\f3        "\-Dsun\&.rmi\&.*";\fP
.fi     
.nf     
\f3};\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


The first permission granted allows the \f3rmid\fR tcommand o execute the 1\&.7\&.0 release of the \f3java\fR command, specified by its explicit path name\&. By default, the version of the \f3java\fR command found in \f3java\&.home\fR is used (the same one that the \f3rmid\fR command uses), and does not need to be specified in the policy file\&. The second permission allows the \f3rmid\fR command to execute any command in the directory \f3/files/apps/rmidcmds\fR\&.

The third permission granted, an \f3ExecOptionPermission\fR, allows the \f3rmid\fR command to start an activation group that defines the security policy file to be \f3/files/policies/group\&.policy\fR\&. The next permission allows the \f3java\&.security\&.debug property\fR to be used by an activation group\&. The last permission allows any property in the \f3sun\&.rmi property\fR name hierarchy to be used by activation groups\&.

To start the \f3rmid\fR command with a policy file, the \f3java\&.security\&.policy\fR property needs to be specified on the \f3rmid\fR command line, for example:

\f3rmid -J-Djava\&.security\&.policy=rmid\&.policy\fR\&.
.TP 0.2i    
\(bu
<policyClassName>

If the default behavior is not flexible enough, then an administrator can provide, when starting the \f3rmid\fR command, the name of a class whose \f3checkExecCommand\fR method is executed to check commands to be executed by the \f3rmid\fR command\&.

The \f3policyClassName\fR specifies a public class with a public, no-argument constructor and an implementation of the following \f3checkExecCommand\fR method:
.sp     
.nf     
\f3 public void checkExecCommand(ActivationGroupDesc desc, String[] command)\fP
.fi     
.nf     
\f3        throws SecurityException;\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     


Before starting an activation group, the \f3rmid\fR command calls the policy\&'s \f3checkExecCommand\fR method and passes to it the activation group descriptor and an array that contains the complete command to start the activation group\&. If the \f3checkExecCommand\fR throws a \f3SecurityException\fR, then the \f3rmid\fR command does not start the activation group and an \f3ActivationException\fR is thrown to the caller attempting to activate the object\&.
.TP 0.2i    
\(bu
none

If the \f3sun\&.rmi\&.activation\&.execPolicy\fR property value is \f3none\fR, then the \f3rmid\fR command does not perform any validation of commands to start activation groups\&.
.RE     

.TP
-log \fIdir\fR
.br
Specifies the name of the directory the activation system daemon uses to write its database and associated information\&. The log directory defaults to creating a log, in the directory in which the \f3rmid\fR command was executed\&.
.TP
-port \fIport\fR
.br
Specifies the port the registry uses\&. The activation system daemon binds the \f3ActivationSystem\fR, with the name \f3java\&.rmi\&.activation\&.ActivationSystem\fR, in this registry\&. The \f3ActivationSystem\fR on the local machine can be obtained using the following \f3Naming\&.lookup\fR method call:
.sp     
.nf     
\f3import java\&.rmi\&.*; \fP
.fi     
.nf     
\f3    import java\&.rmi\&.activation\&.*;\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3    ActivationSystem system; system = (ActivationSystem)\fP
.fi     
.nf     
\f3    Naming\&.lookup("//:port/java\&.rmi\&.activation\&.ActivationSystem");\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     

.TP
-stop
.br
Stops the current invocation of the \f3rmid\fR command for a port specified by the \f3-port\fR option\&. If no port is specified, then this option stops the \f3rmid\fR invocation running on port 1098\&.
.SH ENVIRONMENT\ VARIABLES    
.TP     
CLASSPATH
Used to provide the system a path to user-defined classes\&. Directories are separated by colons, for example: \f3\&.:/usr/local/java/classes\fR\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
java(1)
.TP 0.2i    
\(bu
Setting the Class Path
.RE
.br
'pl 8.5i
'bp
