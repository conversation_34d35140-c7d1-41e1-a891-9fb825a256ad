'\" t
.\"  Copyright (c) 1997, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Remote Method Invocation (RMI) Tools
.\"     Title: rmiregistry.1
.\"
.if n .pl 99999
.TH rmiregistry 1 "21 November 2013" "JDK 8" "Remote Method Invocation (RMI) Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
rmiregistry \- Starts a remote object registry on the specified port on the current host\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBrmiregistry\fR [ \fIport\fR ]
.fi     
.sp     
.TP     
\fIport\fR
The number of a \f3port\fR on the current host at which to start the remote object registry\&.
.SH DESCRIPTION    
The \f3rmiregistry\fR command creates and starts a remote object registry on the specified port on the current host\&. If the port is omitted, then the registry is started on port 1099\&. The \f3rmiregistry\fR command produces no output and is typically run in the background, for example:
.sp     
.nf     
\f3rmiregistry &\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
A remote object registry is a bootstrap naming service that is used by RMI servers on the same host to bind remote objects to names\&. Clients on local and remote hosts can then look up remote objects and make remote method invocations\&.
.PP
The registry is typically used to locate the first remote object on which an application needs to call methods\&. That object then provides application-specific support for finding other objects\&.
.PP
The methods of the \f3java\&.rmi\&.registry\&.LocateRegistry\fR class are used to get a registry operating on the local host or local host and port\&.
.PP
The URL-based methods of the \f3java\&.rmi\&.Naming\fR class operate on a registry and can be used to look up a remote object on any host and on the local host\&. Bind a simple name (string) to a remote object, rebind a new name to a remote object (overriding the old binding), unbind a remote object, and list the URL bound in the registry\&.
.SH OPTIONS    
.TP
-J
.br
Used with any Java option to pass the option following the \f3-J\fR (no spaces between the \f3-J\fR and the option) to the Java interpreter\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
java(1)
.TP 0.2i    
\(bu
\f3java\&.rmi\&.registry\&.LocateRegistry\fR class description at http://docs\&.oracle\&.com/javase/8/docs/api/java/rmi/registry/LocateRegistry\&.html
.TP 0.2i    
\(bu
\f3java\&.rmi\&.Naming class description\fR at http://docs\&.oracle\&.com/javase/8/docs/api/java/rmi/Naming\&.html
.RE
.br
'pl 8.5i
'bp
