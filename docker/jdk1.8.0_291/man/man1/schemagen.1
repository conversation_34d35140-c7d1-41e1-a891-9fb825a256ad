'\" t
.\"  Copyright (c) 2005, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Java Web Services Tools
.\"     Title: schemagen.1
.\"
.if n .pl 99999
.TH schemagen 1 "21 November 2013" "JDK 8" "Java Web Services Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
schemagen \- Generates a schema for every name space that is referenced in your Java classes\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBschemagen\fR [ \fIoptions\fR ] \fIjava\-files\fR
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
\fIjava-files\fR
The Java class files to be processed\&.
.SH DESCRIPTION    
The schema generator creates a schema file for each name space referenced in your Java classes\&. Currently, you cannot control the name of the generated schema files\&. To control the schema file names, see Using SchemaGen with Ant at http://jaxb\&.java\&.net/nonav/2\&.2\&.3u1/docs/schemagenTask\&.html
.PP
Start the schema generator with the appropriate \f3schemagen\fR shell script in the bin directory for your platform\&. The current schema generator can process either Java source files or class files\&.
.sp     
.nf     
\f3schemagen\&.sh Foo\&.java Bar\&.java \&.\&.\&.\fP
.fi     
.nf     
\f3Note: Writing schema1\&.xsd\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
If your java files reference other classes, then those classes must be accessible on your system \f3CLASSPATH\fR environment variable, or they need to be specified in the \f3schemagen\fR command line with the class path options\&. See Options\&. If the referenced files are not accessible or specified, then you get errors when you generate the schema\&.
.SH OPTIONS    
.TP
-d \fIpath\fR
.br
The location where the \f3schemagen\fR command places processor-generated and \f3javac\fR-generated class files\&.
.TP
-cp \fIpath\fR
.br
The location where the \f3schemagen\fR command places user-specified files\&.
.TP
-classpath \fIpath\fR
.br
The location where the \f3schemagen\fR command places user-specified files\&.
.TP
-encoding \fIencoding\fR
.br
Specifies the encoding to use for \f3apt\fR or \f3javac\fR command invocations\&.
.TP
-episode \fIfile\fR
.br
Generates an episode file for separate compilation\&.
.TP
-version
.br
Displays release information\&.
.TP
-help
.br
Displays a help message\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
Using SchemaGen with Ant at http://jaxb\&.java\&.net/nonav/2\&.2\&.3u1/docs/schemagenTask\&.html
.TP 0.2i    
\(bu
Java Architecture for XML Binding (JAXB) at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/xml/jaxb/index\&.html
.RE
.br
'pl 8.5i
'bp
