'\" t
.\"  Copyright (c) 1997, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Remote Method Invocation (RMI) Tools
.\"     Title: serialver.1
.\"
.if n .pl 99999
.TH serialver 1 "21 November 2013" "JDK 8" "Remote Method Invocation (RMI) Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
serialver \- Returns the serial version UID for specified classes\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBserialver\fR [ \fIoptions\fR ] [ \fIclassnames\fR ]
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
\fIclassnames\fR
The classes for which the \f3serialVersionUID\fR is to be returned\&.
.SH DESCRIPTION    
The \f3serialver\fR command returns the \f3serialVersionUID\fR for one or more classes in a form suitable for copying into an evolving class\&. When called with no arguments, the \f3serialver\fR command prints a usage line\&.
.SH OPTIONS    
.TP
-classpath \fIpath-files\fR
.br
Sets the search path for application classes and resources\&. Separate classes and resources with a colon (:)\&.
.TP
-show
.br
Displays a simple user interface\&. Enter the full class name and press either the \fIEnter\fR key or the \fIShow\fR button to display the \f3serialVersionUID\fR\&.
.TP
-J\fIoption\fR
.br
Passes \f3option\fR to the Java Virtual Machine, where option is one of the options described on the reference page for the Java application launcher\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&. See java(1)\&.
.SH NOTES    
The \f3serialver\fR command loads and initializes the specified classes in its virtual machine, and by default, it does not set a security manager\&. If the \f3serialver\fR command is to be run with untrusted classes, then a security manager can be set with the following option:
.sp     
.nf     
\f3\-J\-Djava\&.security\&.manager\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
When necessary, a security policy can be specified with the following option:
.sp     
.nf     
\f3\-J\-Djava\&.security\&.policy=<policy file>\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
policytool(1)
.TP 0.2i    
\(bu
The \f3java\&.io\&.ObjectStream\fR class description at http://docs\&.oracle\&.com/javase/8/docs/api/java/io/ObjectStreamClass\&.html
.RE
.br
'pl 8.5i
'bp
