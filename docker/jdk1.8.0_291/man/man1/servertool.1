'\" t
.\"  Copyright (c) 2001, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Java IDL and RMI-IIOP Tools
.\"     Title: servertool.1
.\"
.if n .pl 99999
.TH servertool 1 "21 November 2013" "JDK 8" "Java IDL and RMI-IIOP Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
servertool \- Provides an easy-to-use interface for developers to register, unregister, start up, and shut down a persistent server\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBservertool\fR \-ORBInitialPort \fInameserverport\fR [ \fIoptions\fR ] [ \fIcommands \fR]
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
commands
The command-line commands\&. See Commands\&.
.SH DESCRIPTION    
The \f3servertool\fR command provides the command-line interface for developers to register, unregister, start up, and shut down a persistent server\&. Command-line commands let you obtain various statistical information about the server\&. See Commands\&.
.SH OPTIONS    
.TP
-ORBInitialHost \fInameserverhost\fR
.br
This options is required\&. It specifies the host machine on which the name server runs and listens for incoming requests\&. The \f3nameserverhost\fR value must specify the port on which the \f3orb\fR is running and listening for requests\&. The value defaults to \f3localhost\fR when this option is not specified\&. If \f3orbd\fR and \f3servertool\fR are running on different machines, then you must specify the name or IP address of the host on which \f3orbd\fR is running\&.

\fINote:\fR On Oracle Solaris, you must become a root user to start a process on a port below 1024\&. Oracle recommends that you use a port number above or equal to 1024 for the \f3nameserverport\fR value\&.
.TP
-J\fIoption\fR
.br
Passes \f3option\fR to the Java Virtual Machine, where \f3option\fR is one of the options described on the reference page for the Java application launcher\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&. See java(1)\&.
.SH COMMANDS    
You can start the \f3servertool\fR command with or without a command-line command\&.
.TP 0.2i    
\(bu
If you did not specify a command when you started \f3servertool\fR, then the command-line tool displays the \f3servertool\fR prompt where you can enter commands: \f3servertool >\fR\&.
.TP 0.2i    
\(bu
If you specify a command when you start \f3servertool\fR, then the Java IDL Server Tool starts, executes the command, and exits\&.
.TP     
.ll 180
register -server \fIserver-class-name\fR -classpath \fIclasspath-to-server\fR [ -applicationName \fIapplication-name\fR -args \fIargs-to-server\fR -vmargs \fIflags-for-JVM\fR ]
Registers a new persistent server with the Object Request Broker Daemon (ORBD)\&. If the server is not already registered, then it is registered and activated\&. This command causes an installation method to be called in the \f3main\fR class of the server identified by the \f3-server\fR option\&. The installation method must be \f3public static void install(org\&.omg\&.CORBA\&.ORB)\fR\&. The install method is optional and lets developers provide their own server installation behavior, such as creating a database schema\&.
.TP     
.ll 180
unregister -serverid \fIserver-id\fR | -applicationName \fIapplication-name\fR
Unregisters a server from the ORBD with either its server ID or its application name\&. This command causes an uninstallation method to be called in the \f3main\fR class of the server identified by the \f3-server\fR option\&. The \f3uninstall\fR method must be \f3public static void uninstall(org\&.omg\&.CORBA\&.ORB)\fR\&. The \f3uninstall\fR method is optional and lets developers provide their own server uninstallation behavior, such as undoing the behavior of the \f3install\fR method\&.
.TP     
getserverid -applicationName \fIapplication-name\fR
Returns the server ID that corresponds to the \f3application-name\fR value\&.
.TP     
list
Lists information about all persistent servers registered with the ORBD\&.
.TP     
listappnames
Lists the application names for all servers currently registered with the ORBD\&.
.TP     
listactive
Lists information about all persistent servers that were started by the ORBD and are currently running\&.
.TP     
.ll 180
locate -serverid \fIserver-id\fR | -applicationName \fIapplication-name\fR [ -endpointType \fIendpointType\fR ]
Locates the endpoints (ports) of a specific type for all ORBs created by a registered server\&. If a server is not already running, then it is activated\&. If an \f3endpointType\fR value is not specified, then the plain/non-protected endpoint associated with each ORB in a server is returned\&.
.TP     
.ll 180
locateperorb -serverid \fIserver-id\fR | -applicationName \fIapplication-name\fR [ -orbid \fIORB-name\fR ]
Locates all the endpoints (ports) registered by a specific Object Request Broker (ORB) of registered server\&. If a server is not already running, then it is activated\&. If an \f3orbid\fR is not specified, then the default value of \f3""\fR is assigned to the \f3orbid\fR\&. If any ORBs are created with an \f3orbid\fR of empty string, then all ports registered by it are returned\&.
.TP     
orblist -serverid \fIserver-id\fR | -applicationName \fIapplication-name\fR
Lists the \f3ORBId\fR of the ORBs defined on a server\&. An \f3ORBId\fR is the string name for the ORB created by the server\&. If the server is not already running, then it is activated\&.
.TP     
shutdown -serverid \fIserver-id\fR | -applicationName application-name
Shut down an active server that is registered with ORBD\&. During execution of this command, the \f3shutdown\fR method defined in the class specified by either the \f3-serverid\fR or \f3-applicationName\fR parameter is also called to shut down the server process\&.
.TP     
startup -serverid \fIserver-id\fR | -applicationName application-name
Starts up or activate a server that is registered with ORBD\&. If the server is not running, then this command starts the server\&. If the server is already running, then an error message is displayed\&.
.TP     
help
Lists all the commands available to the server through the \f3servertool\fR command\&.
.TP     
quit
Exits the \f3servertool\fR command\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
orbd(1)
.RE
.br
'pl 8.5i
'bp
