package cn.bctools.design.data.fields.impl;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.design.data.cache.EchoCacheManager;
import cn.bctools.design.data.fields.DataFieldType;
import cn.bctools.design.data.fields.html.MultipleHtml;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 支持缓存的多选类型Handler基类
 * 继承原有的IMultipleTypeHandler，增加缓存功能
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class CachedMultipleTypeHandler extends IMultipleTypeHandler {
    
    /**
     * 获取缓存管理器
     */
    protected EchoCacheManager getCacheManager() {
        return SpringContextUtil.getBean(EchoCacheManager.class);
    }
    
    /**
     * 带缓存的回显值处理
     * 
     * @param fieldDto 字段配置
     * @param data 原始数据
     * @param lineData 行数据
     * @param fieldType 字段类型
     * @param cacheKeyExtractor 缓存键提取器
     * @param dataSourceLoader 数据源加载器
     * @return 回显值
     */
    protected Object echoValueWithCache(MultipleHtml fieldDto, Object data, Map<String, Object> lineData,
                                      DataFieldType fieldType, 
                                      Function<MultipleHtml, String> cacheKeyExtractor,
                                      Function<String, Map<String, Object>> dataSourceLoader) {
        
        if (ObjectNull.isNull(data)) {
            return null;
        }
        
        String cacheKey = cacheKeyExtractor.apply(fieldDto);
        if (ObjectNull.isNull(cacheKey)) {
            // 如果无法生成缓存键，回退到原始处理方式
            return echoValueFallback(fieldDto, data, lineData);
        }
        
        EchoCacheManager cacheManager = getCacheManager();
        
        // 处理单个值或多个值
        if (data instanceof List) {
            return handleMultipleValues((List<?>) data, fieldType, cacheKey, cacheManager, dataSourceLoader, fieldDto);
        } else {
            return handleSingleValue(data.toString(), fieldType, cacheKey, cacheManager, dataSourceLoader, fieldDto);
        }
    }
    
    /**
     * 处理单个值的回显
     */
    private Object handleSingleValue(String dataValue, DataFieldType fieldType, String cacheKey,
                                   EchoCacheManager cacheManager, 
                                   Function<String, Map<String, Object>> dataSourceLoader,
                                   MultipleHtml fieldDto) {
        
        // 先尝试从缓存获取
        Object cachedValue = cacheManager.getCachedValue(fieldType, cacheKey, dataValue);
        if (ObjectNull.isNotNull(cachedValue)) {
            return cachedValue;
        }
        
        // 缓存未命中，加载数据源
        Map<String, Object> dataMapping = loadDataSourceWithCache(fieldType, cacheKey, cacheManager, dataSourceLoader);
        
        // 从加载的数据中获取值
        Object result = dataMapping.get(dataValue);
        return ObjectNull.isNotNull(result) ? result : dataValue;
    }
    
    /**
     * 处理多个值的回显
     */
    private Object handleMultipleValues(List<?> dataList, DataFieldType fieldType, String cacheKey,
                                      EchoCacheManager cacheManager,
                                      Function<String, Map<String, Object>> dataSourceLoader,
                                      MultipleHtml fieldDto) {
        
        List<String> stringValues = dataList.stream()
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.toList());
        
        if (stringValues.isEmpty()) {
            return "";
        }
        
        // 检查缓存中是否有完整的数据映射
        Map<String, Object> cachedMapping = cacheManager.getCachedMapping(fieldType, cacheKey);
        
        List<String> resultList = new ArrayList<>();
        Set<String> uncachedKeys = new HashSet<>();
        
        // 分离已缓存和未缓存的值
        for (String value : stringValues) {
            if (ObjectNull.isNotNull(cachedMapping) && cachedMapping.containsKey(value)) {
                Object cachedValue = cachedMapping.get(value);
                resultList.add(ObjectNull.isNotNull(cachedValue) ? cachedValue.toString() : value);
            } else {
                uncachedKeys.add(value);
            }
        }
        
        // 处理未缓存的值
        if (!uncachedKeys.isEmpty()) {
            Map<String, Object> dataMapping = loadDataSourceWithCache(fieldType, cacheKey, cacheManager, dataSourceLoader);
            
            for (String value : uncachedKeys) {
                Object mappedValue = dataMapping.get(value);
                resultList.add(ObjectNull.isNotNull(mappedValue) ? mappedValue.toString() : value);
            }
        }
        
        // 根据字段配置决定返回格式
        boolean isMultiple = Boolean.TRUE.equals(fieldDto.getMultiple());
        if (isMultiple) {
            return String.join(",", resultList);
        } else {
            return resultList.isEmpty() ? "" : resultList.get(0);
        }
    }
    
    /**
     * 加载数据源并缓存
     */
    private Map<String, Object> loadDataSourceWithCache(DataFieldType fieldType, String cacheKey,
                                                       EchoCacheManager cacheManager,
                                                       Function<String, Map<String, Object>> dataSourceLoader) {
        
        // 先检查是否已有完整的缓存映射
        Map<String, Object> cachedMapping = cacheManager.getCachedMapping(fieldType, cacheKey);
        if (ObjectNull.isNotNull(cachedMapping)) {
            return cachedMapping;
        }
        
        // 加载数据源
        try {
            Map<String, Object> dataMapping = dataSourceLoader.apply(cacheKey);
            if (ObjectNull.isNotNull(dataMapping) && !dataMapping.isEmpty()) {
                // 缓存整个映射
                cacheManager.cacheMapping(fieldType, cacheKey, dataMapping);
                return dataMapping;
            }
        } catch (Exception e) {
            log.warn("加载数据源失败: fieldType={}, cacheKey={}", fieldType, cacheKey, e);
        }
        
        return new HashMap<>();
    }
    
    /**
     * 批量预加载缓存
     * 在分页查询开始前调用，预加载可能需要的数据
     * 
     * @param fieldType 字段类型
     * @param cacheKey 缓存键
     * @param dataValues 需要回显的数据值集合
     * @param dataSourceLoader 数据源加载器
     */
    public void preloadCache(DataFieldType fieldType, String cacheKey, Set<String> dataValues,
                           Function<String, Map<String, Object>> dataSourceLoader) {
        
        if (ObjectNull.isNull(fieldType) || ObjectNull.isNull(cacheKey) || 
            ObjectNull.isNull(dataValues) || dataValues.isEmpty()) {
            return;
        }
        
        EchoCacheManager cacheManager = getCacheManager();
        
        // 检查哪些值还没有缓存
        Map<String, Object> cachedMapping = cacheManager.getCachedMapping(fieldType, cacheKey);
        Set<String> uncachedValues = dataValues.stream()
                .filter(value -> ObjectNull.isNull(cachedMapping) || !cachedMapping.containsKey(value))
                .collect(Collectors.toSet());
        
        if (!uncachedValues.isEmpty()) {
            log.debug("预加载缓存: fieldType={}, cacheKey={}, uncachedCount={}", 
                     fieldType, cacheKey, uncachedValues.size());
            
            // 加载并缓存数据
            loadDataSourceWithCache(fieldType, cacheKey, cacheManager, dataSourceLoader);
        }
    }
    
    /**
     * 回退到原始处理方式（子类需要实现）
     */
    protected abstract Object echoValueFallback(MultipleHtml fieldDto, Object data, Map<String, Object> lineData);
    
    /**
     * 获取字段类型（子类需要实现）
     */
    protected abstract DataFieldType getFieldType();
}
