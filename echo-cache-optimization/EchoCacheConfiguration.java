package cn.bctools.design.data.config;

import cn.bctools.design.data.cache.EchoCacheManager;
import cn.bctools.design.data.service.impl.OptimizedEchoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 回显缓存配置类
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableScheduling
@ConditionalOnProperty(name = "jvs.echo.cache.enabled", havingValue = "true", matchIfMissing = true)
public class EchoCacheConfiguration {
    
    @Bean
    public EchoCacheManager echoCacheManager() {
        log.info("初始化回显缓存管理器");
        return new EchoCacheManager();
    }
    
    @Bean
    public OptimizedEchoService optimizedEchoService(EchoCacheManager echoCacheManager) {
        log.info("初始化优化回显服务");
        return new OptimizedEchoService(echoCacheManager);
    }
    
    /**
     * 定期清理缓存任务
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @ConditionalOnProperty(name = "jvs.echo.cache.auto-clear", havingValue = "true", matchIfMissing = false)
    public void scheduledCacheClear() {
        try {
            EchoCacheManager cacheManager = echoCacheManager();
            cacheManager.clearAllCache();
            log.info("定期缓存清理任务执行完成");
        } catch (Exception e) {
            log.error("定期缓存清理任务执行失败", e);
        }
    }
    
    /**
     * 缓存性能监控任务
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    @ConditionalOnProperty(name = "jvs.echo.cache.monitor", havingValue = "true", matchIfMissing = false)
    public void scheduledCacheMonitor() {
        try {
            EchoCacheManager cacheManager = echoCacheManager();
            var stats = cacheManager.getCacheStats();
            
            if (!stats.isEmpty()) {
                log.info("=== 缓存性能监控报告 ===");
                stats.forEach((key, stat) -> {
                    if (stat.getHitRate() < 0.7) {
                        log.warn("缓存命中率偏低: {} - {}", key, stat);
                    }
                });
                log.info("========================");
            }
        } catch (Exception e) {
            log.error("缓存性能监控任务执行失败", e);
        }
    }
}
