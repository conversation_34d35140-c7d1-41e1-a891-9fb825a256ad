package cn.bctools.design.data.interceptor;

import cn.bctools.design.data.cache.RequestLevelEchoCache;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 回显缓存拦截器
 * 负责管理请求级缓存的生命周期
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class EchoCacheInterceptor implements HandlerInterceptor {
    
    private final RequestLevelEchoCache requestLevelEchoCache;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 请求开始时，ThreadLocal会自动初始化，无需特殊处理
        log.debug("请求开始: {}", request.getRequestURI());
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        try {
            // 获取缓存统计信息（可选，用于性能监控）
            if (log.isDebugEnabled()) {
                var stats = requestLevelEchoCache.getCurrentRequestStats();
                int cacheSize = requestLevelEchoCache.getCurrentCacheSize();
                
                if (cacheSize > 0) {
                    log.debug("请求完成: {}, 缓存大小: {}, 统计信息: {}", 
                             request.getRequestURI(), cacheSize, stats);
                }
            }
            
            // 清空当前请求的缓存
            requestLevelEchoCache.clearCurrentRequestCache();
            
            // 清理ThreadLocal，防止内存泄漏
            requestLevelEchoCache.removeThreadLocalCache();
            
        } catch (Exception e) {
            log.error("清理请求级缓存失败", e);
        }
    }
}

/**
 * Web配置类
 * 注册回显缓存拦截器
 */
@Configuration
@AllArgsConstructor
public class EchoCacheWebConfig implements WebMvcConfigurer {
    
    private final EchoCacheInterceptor echoCacheInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(echoCacheInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns("/static/**", "/error"); // 排除静态资源和错误页面
    }
}
