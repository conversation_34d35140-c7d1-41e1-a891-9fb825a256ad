package cn.bctools.design.data.cache;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.data.fields.DataFieldType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * 回显数据缓存管理器
 * 用于优化分页查询时的字段回显性能
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class EchoCacheManager {
    
    /**
     * 缓存结构：fieldType -> cacheKey -> cachedValue
     * 第一层：按字段类型分组（如cascader、select、user等）
     * 第二层：按具体的缓存键分组（如dictName、modelId等）
     * 第三层：具体的值映射（如id->name的映射）
     */
    private final Map<DataFieldType, Map<String, Map<String, Object>>> echoCache = new ConcurrentHashMap<>();
    
    /**
     * 缓存统计信息
     */
    private final Map<String, CacheStats> cacheStats = new ConcurrentHashMap<>();
    
    /**
     * 获取缓存数据
     * 
     * @param fieldType 字段类型
     * @param cacheKey 缓存键（如dictName、modelId等）
     * @param dataKey 数据键（如具体的id值）
     * @return 缓存的回显值，如果不存在返回null
     */
    public Object getCachedValue(DataFieldType fieldType, String cacheKey, String dataKey) {
        if (ObjectNull.isNull(fieldType) || ObjectNull.isNull(cacheKey) || ObjectNull.isNull(dataKey)) {
            return null;
        }
        
        Map<String, Map<String, Object>> typeCache = echoCache.get(fieldType);
        if (ObjectNull.isNull(typeCache)) {
            recordCacheMiss(fieldType, cacheKey);
            return null;
        }
        
        Map<String, Object> keyCache = typeCache.get(cacheKey);
        if (ObjectNull.isNull(keyCache)) {
            recordCacheMiss(fieldType, cacheKey);
            return null;
        }
        
        Object value = keyCache.get(dataKey);
        if (ObjectNull.isNotNull(value)) {
            recordCacheHit(fieldType, cacheKey);
        } else {
            recordCacheMiss(fieldType, cacheKey);
        }
        
        return value;
    }
    
    /**
     * 批量获取缓存数据
     * 
     * @param fieldType 字段类型
     * @param cacheKey 缓存键
     * @return 整个缓存映射，如果不存在返回null
     */
    public Map<String, Object> getCachedMapping(DataFieldType fieldType, String cacheKey) {
        if (ObjectNull.isNull(fieldType) || ObjectNull.isNull(cacheKey)) {
            return null;
        }
        
        Map<String, Map<String, Object>> typeCache = echoCache.get(fieldType);
        if (ObjectNull.isNull(typeCache)) {
            return null;
        }
        
        return typeCache.get(cacheKey);
    }
    
    /**
     * 缓存数据
     * 
     * @param fieldType 字段类型
     * @param cacheKey 缓存键
     * @param dataMapping 数据映射（id->name的映射）
     */
    public void cacheMapping(DataFieldType fieldType, String cacheKey, Map<String, Object> dataMapping) {
        if (ObjectNull.isNull(fieldType) || ObjectNull.isNull(cacheKey) || ObjectNull.isNull(dataMapping)) {
            return;
        }
        
        echoCache.computeIfAbsent(fieldType, k -> new ConcurrentHashMap<>())
                 .put(cacheKey, new ConcurrentHashMap<>(dataMapping));
        
        log.debug("缓存数据映射: fieldType={}, cacheKey={}, size={}", 
                 fieldType, cacheKey, dataMapping.size());
    }
    
    /**
     * 缓存单个值
     * 
     * @param fieldType 字段类型
     * @param cacheKey 缓存键
     * @param dataKey 数据键
     * @param value 回显值
     */
    public void cacheValue(DataFieldType fieldType, String cacheKey, String dataKey, Object value) {
        if (ObjectNull.isNull(fieldType) || ObjectNull.isNull(cacheKey) || ObjectNull.isNull(dataKey)) {
            return;
        }
        
        echoCache.computeIfAbsent(fieldType, k -> new ConcurrentHashMap<>())
                 .computeIfAbsent(cacheKey, k -> new ConcurrentHashMap<>())
                 .put(dataKey, value);
    }
    
    /**
     * 批量缓存值
     * 
     * @param fieldType 字段类型
     * @param cacheKey 缓存键
     * @param valueFunction 值获取函数
     * @param dataKeys 需要缓存的数据键集合
     */
    public void batchCacheValues(DataFieldType fieldType, String cacheKey, 
                                Function<String, Object> valueFunction, 
                                java.util.Set<String> dataKeys) {
        if (ObjectNull.isNull(fieldType) || ObjectNull.isNull(cacheKey) || 
            ObjectNull.isNull(valueFunction) || ObjectNull.isNull(dataKeys)) {
            return;
        }
        
        Map<String, Object> keyCache = echoCache.computeIfAbsent(fieldType, k -> new ConcurrentHashMap<>())
                                               .computeIfAbsent(cacheKey, k -> new ConcurrentHashMap<>());
        
        dataKeys.forEach(dataKey -> {
            if (!keyCache.containsKey(dataKey)) {
                try {
                    Object value = valueFunction.apply(dataKey);
                    if (ObjectNull.isNotNull(value)) {
                        keyCache.put(dataKey, value);
                    }
                } catch (Exception e) {
                    log.warn("批量缓存值失败: dataKey={}", dataKey, e);
                }
            }
        });
    }
    
    /**
     * 清除指定类型的缓存
     */
    public void clearCache(DataFieldType fieldType) {
        if (ObjectNull.isNotNull(fieldType)) {
            echoCache.remove(fieldType);
            log.info("清除缓存: fieldType={}", fieldType);
        }
    }
    
    /**
     * 清除指定类型和键的缓存
     */
    public void clearCache(DataFieldType fieldType, String cacheKey) {
        if (ObjectNull.isNotNull(fieldType) && ObjectNull.isNotNull(cacheKey)) {
            Map<String, Map<String, Object>> typeCache = echoCache.get(fieldType);
            if (ObjectNull.isNotNull(typeCache)) {
                typeCache.remove(cacheKey);
                log.info("清除缓存: fieldType={}, cacheKey={}", fieldType, cacheKey);
            }
        }
    }
    
    /**
     * 清除所有缓存
     */
    public void clearAllCache() {
        echoCache.clear();
        cacheStats.clear();
        log.info("清除所有回显缓存");
    }
    
    /**
     * 记录缓存命中
     */
    private void recordCacheHit(DataFieldType fieldType, String cacheKey) {
        String statsKey = fieldType + ":" + cacheKey;
        cacheStats.computeIfAbsent(statsKey, k -> new CacheStats()).hit();
    }
    
    /**
     * 记录缓存未命中
     */
    private void recordCacheMiss(DataFieldType fieldType, String cacheKey) {
        String statsKey = fieldType + ":" + cacheKey;
        cacheStats.computeIfAbsent(statsKey, k -> new CacheStats()).miss();
    }
    
    /**
     * 获取缓存统计信息
     */
    public Map<String, CacheStats> getCacheStats() {
        return new ConcurrentHashMap<>(cacheStats);
    }
    
    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        private long hits = 0;
        private long misses = 0;
        
        public synchronized void hit() {
            hits++;
        }
        
        public synchronized void miss() {
            misses++;
        }
        
        public synchronized long getHits() {
            return hits;
        }
        
        public synchronized long getMisses() {
            return misses;
        }
        
        public synchronized double getHitRate() {
            long total = hits + misses;
            return total == 0 ? 0.0 : (double) hits / total;
        }
        
        @Override
        public String toString() {
            return String.format("CacheStats{hits=%d, misses=%d, hitRate=%.2f%%}", 
                               hits, misses, getHitRate() * 100);
        }
    }
}
