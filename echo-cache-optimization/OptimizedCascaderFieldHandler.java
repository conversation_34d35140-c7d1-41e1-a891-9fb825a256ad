package cn.bctools.design.data.fields.impl.advance;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.design.data.fields.DataFieldType;
import cn.bctools.design.data.fields.html.CascaderItemHtml;
import cn.bctools.design.data.fields.impl.CachedMultipleTypeHandler;
import cn.bctools.design.data.service.DynamicDataService;
import cn.bctools.design.tree.dto.TreeDictDto;
import cn.bctools.design.tree.service.JvsTreeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 优化后的级联选择Handler
 * 使用缓存机制提升性能
 * 
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class OptimizedCascaderFieldHandler extends CachedMultipleTypeHandler {
    
    private final JvsTreeService jvsTreeService;
    
    @Override
    public Object getEchoValue(CascaderItemHtml cascaderItem, Object data, boolean override, 
                              Map<String, Object> lineData, String... paths) {
        
        if (ObjectNull.isNull(data)) {
            return null;
        }
        
        // 根据数据类型选择不同的缓存策略
        switch (cascaderItem.getDatatype()) {
            case system:
                return handleSystemType(cascaderItem, data, lineData);
            case dataModel:
                return handleDataModelType(cascaderItem, data, lineData);
            case option:
                return handleOptionType(cascaderItem, data, lineData);
            default:
                return echoValueFallback(cascaderItem, data, lineData);
        }
    }
    
    /**
     * 处理系统字典类型
     */
    private Object handleSystemType(CascaderItemHtml cascaderItem, Object data, Map<String, Object> lineData) {
        String dictName = cascaderItem.getDictName();
        if (ObjectNull.isNull(dictName)) {
            return data;
        }
        
        return echoValueWithCache(
            cascaderItem, 
            data, 
            lineData,
            DataFieldType.cascader,
            item -> "system:" + item.getDictName(),
            cacheKey -> loadSystemDictData(dictName)
        );
    }
    
    /**
     * 处理数据模型类型
     */
    private Object handleDataModelType(CascaderItemHtml cascaderItem, Object data, Map<String, Object> lineData) {
        String formId = cascaderItem.getFormId();
        if (ObjectNull.isNull(formId)) {
            return data;
        }
        
        return echoValueWithCache(
            cascaderItem,
            data,
            lineData,
            DataFieldType.cascader,
            item -> "dataModel:" + item.getFormId() + ":" + item.getProps().getValue() + ":" + item.getProps().getLabel(),
            cacheKey -> loadDataModelData(cascaderItem)
        );
    }
    
    /**
     * 处理选项类型
     */
    private Object handleOptionType(CascaderItemHtml cascaderItem, Object data, Map<String, Object> lineData) {
        // 选项类型通常是静态配置，可以直接缓存
        return echoValueWithCache(
            cascaderItem,
            data,
            lineData,
            DataFieldType.cascader,
            item -> "option:" + item.hashCode(), // 使用hashCode作为缓存键
            cacheKey -> loadOptionData(cascaderItem)
        );
    }
    
    /**
     * 加载系统字典数据
     */
    private Map<String, Object> loadSystemDictData(String dictName) {
        try {
            List<TreeDictDto> dictList = getTreeDict(dictName);
            if (ObjectNull.isNotNull(dictList)) {
                return dictList.stream()
                    .collect(Collectors.toMap(
                        TreeDictDto::getId, 
                        TreeDictDto::getName, 
                        (existing, replacement) -> existing
                    ));
            }
        } catch (Exception e) {
            log.warn("加载系统字典数据失败: dictName={}", dictName, e);
        }
        return new HashMap<>();
    }
    
    /**
     * 加载数据模型数据
     */
    private Map<String, Object> loadDataModelData(CascaderItemHtml cascaderItem) {
        try {
            DynamicDataService dynamicDataService = SpringContextUtil.getBean(DynamicDataService.class);
            List<Map<String, Object>> dictList = dynamicDataService.queryList(
                cascaderItem.getFormId(), 
                cascaderItem.getProps().getLabel(), 
                cascaderItem.getProps().getSecTitle()
            );
            
            if (ObjectNull.isNotNull(dictList)) {
                return dictList.stream()
                    .filter(e -> ObjectNull.isNotNull(e.get(cascaderItem.getProps().getValue())))
                    .collect(Collectors.toMap(
                        e -> e.get(cascaderItem.getProps().getValue()).toString(),
                        e -> e.get(cascaderItem.getProps().getLabel()),
                        (existing, replacement) -> existing
                    ));
            }
        } catch (Exception e) {
            log.warn("加载数据模型数据失败: formId={}", cascaderItem.getFormId(), e);
        }
        return new HashMap<>();
    }
    
    /**
     * 加载选项数据
     */
    private Map<String, Object> loadOptionData(CascaderItemHtml cascaderItem) {
        try {
            // 这里需要根据实际的选项配置来实现
            // 假设选项配置在cascaderItem中
            Map<String, Object> optionMap = new HashMap<>();
            
            // 示例实现，实际需要根据你的选项配置结构调整
            if (ObjectNull.isNotNull(cascaderItem.getOptions())) {
                // 处理选项配置，转换为id->name的映射
                // 具体实现需要根据你的选项数据结构来调整
            }
            
            return optionMap;
        } catch (Exception e) {
            log.warn("加载选项数据失败", e);
        }
        return new HashMap<>();
    }
    
    /**
     * 获取树形字典数据（复用原有逻辑）
     */
    private List<TreeDictDto> getTreeDict(String dictUniqueName) {
        // 这里可以复用原有的getTreeDict方法逻辑
        // 或者调用原有的CascaderFieldHandler的方法
        try {
            // 复用原有逻辑...
            Map<String, Object> treeDict = jvsTreeService.getByUniqueName(dictUniqueName, 0);
            // ... 其他处理逻辑
            return null; // 实际实现需要返回正确的数据
        } catch (Exception e) {
            log.warn("获取树形字典失败: dictName={}", dictUniqueName, e);
            return null;
        }
    }
    
    @Override
    protected Object echoValueFallback(Object fieldDto, Object data, Map<String, Object> lineData) {
        // 回退到原始处理方式
        // 这里可以调用原有的CascaderFieldHandler的getEchoValue方法
        log.debug("使用回退处理方式处理级联选择回显");
        return data;
    }
    
    @Override
    protected DataFieldType getFieldType() {
        return DataFieldType.cascader;
    }
    
    /**
     * 批量预加载方法
     * 在分页查询开始前调用
     */
    public void batchPreloadCache(List<CascaderItemHtml> cascaderItems, List<Map<String, Object>> dataList) {
        if (ObjectNull.isNull(cascaderItems) || ObjectNull.isNull(dataList)) {
            return;
        }
        
        for (CascaderItemHtml item : cascaderItems) {
            try {
                // 收集需要回显的值
                java.util.Set<String> valuesToPreload = dataList.stream()
                    .map(data -> data.get(item.getFieldKey()))
                    .filter(ObjectNull::isNotNull)
                    .flatMap(value -> {
                        if (value instanceof List) {
                            return ((List<?>) value).stream().map(Object::toString);
                        } else {
                            return java.util.stream.Stream.of(value.toString());
                        }
                    })
                    .collect(Collectors.toSet());
                
                if (!valuesToPreload.isEmpty()) {
                    // 根据类型预加载缓存
                    switch (item.getDatatype()) {
                        case system:
                            preloadCache(DataFieldType.cascader, 
                                       "system:" + item.getDictName(), 
                                       valuesToPreload,
                                       cacheKey -> loadSystemDictData(item.getDictName()));
                            break;
                        case dataModel:
                            preloadCache(DataFieldType.cascader,
                                       "dataModel:" + item.getFormId() + ":" + item.getProps().getValue() + ":" + item.getProps().getLabel(),
                                       valuesToPreload,
                                       cacheKey -> loadDataModelData(item));
                            break;
                        case option:
                            preloadCache(DataFieldType.cascader,
                                       "option:" + item.hashCode(),
                                       valuesToPreload,
                                       cacheKey -> loadOptionData(item));
                            break;
                    }
                }
            } catch (Exception e) {
                log.warn("预加载级联选择缓存失败: fieldKey={}", item.getFieldKey(), e);
            }
        }
    }
}
