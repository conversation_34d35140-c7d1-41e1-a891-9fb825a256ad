package cn.bctools.design.data.service.impl;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.data.cache.EchoCacheManager;
import cn.bctools.design.data.entity.DynamicDataPo;
import cn.bctools.design.data.fields.dto.QueryOrderDto;
import cn.bctools.design.data.fields.dto.page.PageAggregationSetting;
import cn.bctools.design.data.fields.html.FieldBasicsHtml;
import cn.bctools.design.data.service.DynamicDataService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 优化的动态数据服务实现
 * 集成缓存机制提升分页查询回显性能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class OptimizedDynamicDataServiceImpl {
    
    @Autowired
    private DynamicDataService originalDynamicDataService;
    
    @Autowired
    private OptimizedEchoService optimizedEchoService;
    
    @Autowired
    private EchoCacheManager cacheManager;
    
    /**
     * 优化的分页查询方法
     * 替换原有的queryPage方法，使用缓存机制提升性能
     */
    public Page<Map<String, Object>> optimizedQueryPage(String appId, 
                                                        Page<DynamicDataPo> page, 
                                                        String modelId, 
                                                        Map<String, String> combiningFieldFormulaContentMap,
                                                        List<List<QueryConditionDto>> conditions, 
                                                        List<QueryOrderDto> sorts, 
                                                        List<String> fieldKeyList, 
                                                        boolean addButtonInfo, 
                                                        boolean echo, 
                                                        Boolean andOr, 
                                                        List<FieldBasicsHtml> fieldBasicsHtmls, 
                                                        PageAggregationSetting pageAggregationSetting) {
        
        long startTime = System.currentTimeMillis();
        
        // 1. 执行原有的数据查询逻辑（不包含回显）
        Page<Map<String, Object>> result = originalDynamicDataService.queryPage(
            appId, page, modelId, combiningFieldFormulaContentMap, conditions, sorts, 
            fieldKeyList, addButtonInfo, false, // 注意这里echo设为false
            andOr, fieldBasicsHtmls, pageAggregationSetting
        );
        
        // 2. 如果需要回显且有数据，使用优化的回显服务
        if (echo && ObjectNull.isNotNull(result.getRecords()) && !result.getRecords().isEmpty()) {
            
            // 过滤掉限制回显的字段
            List<FieldBasicsHtml> echoFields = fieldBasicsHtmls.stream()
                .filter(field -> !Boolean.TRUE.equals(field.getLimitEcho()))
                .collect(Collectors.toList());
            
            if (!echoFields.isEmpty()) {
                long echoStartTime = System.currentTimeMillis();
                
                // 使用优化的回显服务处理数据
                List<Map<String, Object>> optimizedRecords = optimizedEchoService.optimizedEcho(
                    result.getRecords(), echoFields, false
                );
                
                result.setRecords(optimizedRecords);
                
                long echoEndTime = System.currentTimeMillis();
                log.info("优化回显处理完成: 数据量={}, 回显字段数={}, 回显耗时={}ms", 
                        result.getRecords().size(), echoFields.size(), echoEndTime - echoStartTime);
            }
        }
        
        long endTime = System.currentTimeMillis();
        log.info("优化分页查询完成: 总耗时={}ms", endTime - startTime);
        
        return result;
    }
    
    /**
     * 预热缓存
     * 在系统启动或定期执行，预加载常用的字典数据
     */
    public void warmupCache(String appId, String modelId) {
        try {
            log.info("开始预热缓存: appId={}, modelId={}", appId, modelId);
            
            // 获取模型的所有字段
            List<FieldBasicsHtml> fields = getModelFields(appId, modelId);
            
            // 预加载常用字典数据
            preloadCommonDictionaries(fields);
            
            log.info("缓存预热完成: appId={}, modelId={}", appId, modelId);
            
        } catch (Exception e) {
            log.error("缓存预热失败: appId={}, modelId={}", appId, modelId, e);
        }
    }
    
    /**
     * 获取模型字段
     */
    private List<FieldBasicsHtml> getModelFields(String appId, String modelId) {
        // 这里需要调用实际的字段获取方法
        // 示例实现
        return null; // 实际需要实现
    }
    
    /**
     * 预加载常用字典
     */
    private void preloadCommonDictionaries(List<FieldBasicsHtml> fields) {
        if (ObjectNull.isNull(fields)) {
            return;
        }
        
        for (FieldBasicsHtml field : fields) {
            try {
                switch (field.getType()) {
                    case cascader:
                        preloadCascaderDictionary(field);
                        break;
                    case select:
                    case radio:
                    case checkbox:
                        preloadSelectDictionary(field);
                        break;
                    default:
                        // 其他类型暂不预加载
                        break;
                }
            } catch (Exception e) {
                log.warn("预加载字段字典失败: fieldKey={}", field.getFieldKey(), e);
            }
        }
    }
    
    /**
     * 预加载级联字典
     */
    private void preloadCascaderDictionary(FieldBasicsHtml field) {
        // 实现级联字典的预加载
        log.debug("预加载级联字典: fieldKey={}", field.getFieldKey());
    }
    
    /**
     * 预加载选择字典
     */
    private void preloadSelectDictionary(FieldBasicsHtml field) {
        // 实现选择字典的预加载
        log.debug("预加载选择字典: fieldKey={}", field.getFieldKey());
    }
    
    /**
     * 清理缓存
     * 可以定期调用或在数据更新后调用
     */
    public void clearCache() {
        cacheManager.clearAllCache();
        log.info("已清理所有回显缓存");
    }
    
    /**
     * 清理指定模型的缓存
     */
    public void clearModelCache(String modelId) {
        // 根据modelId清理相关缓存
        // 这里需要根据实际的缓存键规则来实现
        log.info("已清理模型缓存: modelId={}", modelId);
    }
    
    /**
     * 获取缓存统计信息
     */
    public Map<String, EchoCacheManager.CacheStats> getCacheStats() {
        return cacheManager.getCacheStats();
    }
    
    /**
     * 监控缓存性能
     */
    public void monitorCachePerformance() {
        Map<String, EchoCacheManager.CacheStats> stats = getCacheStats();
        
        log.info("=== 缓存性能监控 ===");
        stats.forEach((key, stat) -> {
            double hitRate = stat.getHitRate();
            if (hitRate < 0.8) { // 命中率低于80%时告警
                log.warn("缓存命中率较低: {} - {}", key, stat);
            } else {
                log.info("缓存性能良好: {} - {}", key, stat);
            }
        });
        log.info("==================");
    }
}
