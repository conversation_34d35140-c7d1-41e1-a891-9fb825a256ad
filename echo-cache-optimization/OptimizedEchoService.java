package cn.bctools.design.data.service.impl;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.data.cache.EchoCacheManager;
import cn.bctools.design.data.fields.DataFieldType;
import cn.bctools.design.data.fields.html.FieldBasicsHtml;
import cn.bctools.design.data.fields.impl.CachedMultipleTypeHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 优化的回显服务
 * 提供批量预加载和缓存管理功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class OptimizedEchoService {
    
    private final EchoCacheManager cacheManager;
    
    /**
     * 优化的分页数据回显处理
     * 
     * @param dataList 分页数据列表
     * @param fields 字段配置列表
     * @param override 是否覆盖原字段
     * @return 处理后的数据列表
     */
    public List<Map<String, Object>> optimizedEcho(List<Map<String, Object>> dataList, 
                                                   List<FieldBasicsHtml> fields, 
                                                   boolean override) {
        
        if (ObjectNull.isNull(dataList) || dataList.isEmpty() || 
            ObjectNull.isNull(fields) || fields.isEmpty()) {
            return dataList;
        }
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 分析需要缓存的字段
            List<FieldBasicsHtml> cacheableFields = identifyCacheableFields(fields);
            
            // 2. 批量预加载缓存
            batchPreloadCache(dataList, cacheableFields);
            
            // 3. 执行回显处理
            List<Map<String, Object>> result = dataList.stream()
                .map(data -> processDataEcho(data, fields, override))
                .collect(Collectors.toList());
            
            long endTime = System.currentTimeMillis();
            log.info("优化回显处理完成: 数据量={}, 字段数={}, 耗时={}ms", 
                    dataList.size(), fields.size(), endTime - startTime);
            
            // 4. 输出缓存统计信息
            logCacheStats();
            
            return result;
            
        } catch (Exception e) {
            log.error("优化回显处理失败", e);
            // 回退到原始处理方式
            return fallbackEcho(dataList, fields, override);
        }
    }
    
    /**
     * 识别可缓存的字段
     */
    private List<FieldBasicsHtml> identifyCacheableFields(List<FieldBasicsHtml> fields) {
        return fields.stream()
            .filter(field -> isCacheableFieldType(field.getType()))
            .collect(Collectors.toList());
    }
    
    /**
     * 判断字段类型是否支持缓存
     */
    private boolean isCacheableFieldType(DataFieldType fieldType) {
        if (ObjectNull.isNull(fieldType)) {
            return false;
        }
        
        // 支持缓存的字段类型
        switch (fieldType) {
            case cascader:
            case select:
            case radio:
            case checkbox:
            case user:
            case organization:
            case department:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 批量预加载缓存
     */
    private void batchPreloadCache(List<Map<String, Object>> dataList, List<FieldBasicsHtml> cacheableFields) {
        if (ObjectNull.isNull(cacheableFields) || cacheableFields.isEmpty()) {
            return;
        }
        
        log.debug("开始批量预加载缓存: 可缓存字段数={}", cacheableFields.size());
        
        for (FieldBasicsHtml field : cacheableFields) {
            try {
                preloadFieldCache(dataList, field);
            } catch (Exception e) {
                log.warn("预加载字段缓存失败: fieldKey={}, fieldType={}", 
                        field.getFieldKey(), field.getType(), e);
            }
        }
    }
    
    /**
     * 预加载单个字段的缓存
     */
    private void preloadFieldCache(List<Map<String, Object>> dataList, FieldBasicsHtml field) {
        // 收集该字段的所有值
        Set<String> fieldValues = collectFieldValues(dataList, field.getFieldKey());
        
        if (fieldValues.isEmpty()) {
            return;
        }
        
        log.debug("预加载字段缓存: fieldKey={}, valueCount={}", field.getFieldKey(), fieldValues.size());
        
        // 根据字段类型调用相应的预加载方法
        switch (field.getType()) {
            case cascader:
                preloadCascaderCache(field, fieldValues);
                break;
            case select:
            case radio:
            case checkbox:
                preloadSelectCache(field, fieldValues);
                break;
            case user:
                preloadUserCache(field, fieldValues);
                break;
            case organization:
            case department:
                preloadOrgCache(field, fieldValues);
                break;
            default:
                log.debug("字段类型不支持预加载: {}", field.getType());
        }
    }
    
    /**
     * 收集字段值
     */
    private Set<String> collectFieldValues(List<Map<String, Object>> dataList, String fieldKey) {
        return dataList.stream()
            .map(data -> data.get(fieldKey))
            .filter(ObjectNull::isNotNull)
            .flatMap(value -> {
                if (value instanceof List) {
                    return ((List<?>) value).stream()
                        .filter(ObjectNull::isNotNull)
                        .map(Object::toString);
                } else {
                    return java.util.stream.Stream.of(value.toString());
                }
            })
            .collect(Collectors.toSet());
    }
    
    /**
     * 预加载级联选择缓存
     */
    private void preloadCascaderCache(FieldBasicsHtml field, Set<String> fieldValues) {
        // 这里需要根据具体的CascaderFieldHandler实现来调用预加载方法
        // 示例实现
        try {
            // 假设有一个优化的CascaderFieldHandler
            // OptimizedCascaderFieldHandler handler = getOptimizedCascaderHandler();
            // handler.preloadCache(field, fieldValues);
            
            log.debug("级联选择缓存预加载: fieldKey={}", field.getFieldKey());
        } catch (Exception e) {
            log.warn("级联选择缓存预加载失败: fieldKey={}", field.getFieldKey(), e);
        }
    }
    
    /**
     * 预加载选择框缓存
     */
    private void preloadSelectCache(FieldBasicsHtml field, Set<String> fieldValues) {
        // 实现选择框的缓存预加载
        log.debug("选择框缓存预加载: fieldKey={}", field.getFieldKey());
    }
    
    /**
     * 预加载用户缓存
     */
    private void preloadUserCache(FieldBasicsHtml field, Set<String> fieldValues) {
        // 实现用户字段的缓存预加载
        log.debug("用户字段缓存预加载: fieldKey={}", field.getFieldKey());
    }
    
    /**
     * 预加载组织架构缓存
     */
    private void preloadOrgCache(FieldBasicsHtml field, Set<String> fieldValues) {
        // 实现组织架构字段的缓存预加载
        log.debug("组织架构缓存预加载: fieldKey={}", field.getFieldKey());
    }
    
    /**
     * 处理单条数据的回显
     */
    private Map<String, Object> processDataEcho(Map<String, Object> data, 
                                               List<FieldBasicsHtml> fields, 
                                               boolean override) {
        // 这里可以复用原有的echo逻辑，但会利用已预加载的缓存
        Map<String, Object> result = new HashMap<>(data);
        
        for (FieldBasicsHtml field : fields) {
            try {
                Object fieldValue = data.get(field.getFieldKey());
                if (ObjectNull.isNotNull(fieldValue)) {
                    // 调用相应的Handler进行回显处理
                    // 由于缓存已预加载，这里的处理会很快
                    Object echoValue = processFieldEcho(field, fieldValue, data, override);
                    
                    if (override) {
                        result.put(field.getFieldKey(), echoValue);
                    } else {
                        result.put(field.getFieldKey() + "_1", echoValue);
                    }
                }
            } catch (Exception e) {
                log.warn("字段回显处理失败: fieldKey={}", field.getFieldKey(), e);
            }
        }
        
        return result;
    }
    
    /**
     * 处理单个字段的回显
     */
    private Object processFieldEcho(FieldBasicsHtml field, Object fieldValue, 
                                   Map<String, Object> lineData, boolean override) {
        // 这里需要调用相应的Handler
        // 由于缓存已预加载，Handler的getEchoValue方法会直接从缓存获取数据
        
        // 示例实现，实际需要根据字段类型调用相应的Handler
        return fieldValue; // 临时返回原值
    }
    
    /**
     * 回退到原始处理方式
     */
    private List<Map<String, Object>> fallbackEcho(List<Map<String, Object>> dataList, 
                                                   List<FieldBasicsHtml> fields, 
                                                   boolean override) {
        log.warn("回退到原始回显处理方式");
        // 这里调用原有的echo方法
        return dataList;
    }
    
    /**
     * 输出缓存统计信息
     */
    private void logCacheStats() {
        Map<String, EchoCacheManager.CacheStats> stats = cacheManager.getCacheStats();
        if (!stats.isEmpty()) {
            log.info("缓存统计信息:");
            stats.forEach((key, stat) -> 
                log.info("  {}: {}", key, stat.toString())
            );
        }
    }
    
    /**
     * 清除过期缓存
     */
    public void clearExpiredCache() {
        cacheManager.clearAllCache();
        log.info("已清除所有回显缓存");
    }
}
