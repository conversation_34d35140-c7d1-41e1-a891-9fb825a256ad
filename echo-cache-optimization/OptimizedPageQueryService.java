package cn.bctools.design.data.service.impl;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.data.cache.RequestLevelEchoCache;
import cn.bctools.design.data.fields.DataFieldType;
import cn.bctools.design.data.fields.html.FieldBasicsHtml;
import cn.bctools.design.data.fields.impl.RequestCachedMultipleTypeHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 优化的分页查询服务
 * 使用请求级缓存提升回显性能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class OptimizedPageQueryService {
    
    private final RequestLevelEchoCache requestLevelEchoCache;
    
    /**
     * 优化的分页数据回显处理
     * 
     * @param dataList 分页数据列表
     * @param fields 字段配置列表
     * @param override 是否覆盖原字段
     * @return 处理后的数据列表
     */
    public List<Map<String, Object>> optimizedEcho(List<Map<String, Object>> dataList, 
                                                   List<FieldBasicsHtml> fields, 
                                                   boolean override) {
        
        if (ObjectNull.isNull(dataList) || dataList.isEmpty() || 
            ObjectNull.isNull(fields) || fields.isEmpty()) {
            return dataList;
        }
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 分析并预加载需要缓存的数据源
            preloadDataSources(dataList, fields);
            
            // 2. 执行回显处理（此时会使用已预加载的缓存）
            List<Map<String, Object>> result = dataList.stream()
                .map(data -> processDataEcho(data, fields, override))
                .collect(Collectors.toList());
            
            long endTime = System.currentTimeMillis();
            
            // 3. 输出性能统计
            logPerformanceStats(dataList.size(), fields.size(), endTime - startTime);
            
            return result;
            
        } catch (Exception e) {
            log.error("优化回显处理失败", e);
            // 回退到原始处理方式
            return fallbackEcho(dataList, fields, override);
        }
    }
    
    /**
     * 预加载数据源
     * 分析所有数据中需要的字段值，批量预加载对应的数据源
     */
    private void preloadDataSources(List<Map<String, Object>> dataList, List<FieldBasicsHtml> fields) {
        log.debug("开始预加载数据源: 数据量={}, 字段数={}", dataList.size(), fields.size());
        
        // 按字段类型分组处理
        Map<DataFieldType, List<FieldBasicsHtml>> fieldsByType = fields.stream()
            .filter(field -> isCacheableFieldType(field.getType()))
            .collect(Collectors.groupingBy(FieldBasicsHtml::getType));
        
        fieldsByType.forEach((fieldType, typeFields) -> {
            try {
                preloadFieldTypeDataSources(dataList, typeFields, fieldType);
            } catch (Exception e) {
                log.warn("预加载字段类型数据源失败: fieldType={}", fieldType, e);
            }
        });
    }
    
    /**
     * 预加载特定字段类型的数据源
     */
    private void preloadFieldTypeDataSources(List<Map<String, Object>> dataList, 
                                           List<FieldBasicsHtml> fields, 
                                           DataFieldType fieldType) {
        
        for (FieldBasicsHtml field : fields) {
            try {
                // 收集该字段的所有值
                Set<String> fieldValues = collectFieldValues(dataList, field.getFieldKey());
                
                if (!fieldValues.isEmpty()) {
                    // 获取对应的Handler并预加载数据源
                    preloadFieldDataSource(field, fieldValues, fieldType);
                }
            } catch (Exception e) {
                log.warn("预加载字段数据源失败: fieldKey={}", field.getFieldKey(), e);
            }
        }
    }
    
    /**
     * 收集字段值
     */
    private Set<String> collectFieldValues(List<Map<String, Object>> dataList, String fieldKey) {
        return dataList.stream()
            .map(data -> data.get(fieldKey))
            .filter(ObjectNull::isNotNull)
            .flatMap(value -> {
                if (value instanceof List) {
                    return ((List<?>) value).stream()
                        .filter(ObjectNull::isNotNull)
                        .map(Object::toString);
                } else {
                    return Stream.of(value.toString());
                }
            })
            .collect(Collectors.toSet());
    }
    
    /**
     * 预加载单个字段的数据源
     */
    private void preloadFieldDataSource(FieldBasicsHtml field, Set<String> fieldValues, DataFieldType fieldType) {
        try {
            // 这里需要根据字段类型获取对应的Handler
            // 由于Handler的获取比较复杂，这里提供一个简化的示例
            
            switch (fieldType) {
                case cascader:
                    preloadCascaderDataSource(field, fieldValues);
                    break;
                case select:
                case radio:
                case checkbox:
                    preloadSelectDataSource(field, fieldValues);
                    break;
                case user:
                    preloadUserDataSource(field, fieldValues);
                    break;
                case organization:
                case department:
                    preloadOrgDataSource(field, fieldValues);
                    break;
                default:
                    log.debug("字段类型不支持预加载: {}", fieldType);
            }
        } catch (Exception e) {
            log.warn("预加载字段数据源失败: fieldKey={}, fieldType={}", field.getFieldKey(), fieldType, e);
        }
    }
    
    /**
     * 预加载级联选择数据源
     */
    private void preloadCascaderDataSource(FieldBasicsHtml field, Set<String> fieldValues) {
        // 获取CascaderFieldHandler并调用预加载方法
        // 这里需要根据实际的Handler获取方式来实现
        log.debug("预加载级联选择数据源: fieldKey={}, valueCount={}", field.getFieldKey(), fieldValues.size());
        
        // 示例实现：
        // RequestCachedCascaderFieldHandler handler = getHandler(RequestCachedCascaderFieldHandler.class);
        // CascaderItemHtml cascaderItem = parseFieldConfig(field);
        // String configKey = handler.getConfigKey(cascaderItem);
        // handler.preloadDataSourceIfNeeded(DataFieldType.cascader, configKey, fieldValues, ...);
    }
    
    /**
     * 预加载选择框数据源
     */
    private void preloadSelectDataSource(FieldBasicsHtml field, Set<String> fieldValues) {
        log.debug("预加载选择框数据源: fieldKey={}, valueCount={}", field.getFieldKey(), fieldValues.size());
        // 实现选择框的预加载逻辑
    }
    
    /**
     * 预加载用户数据源
     */
    private void preloadUserDataSource(FieldBasicsHtml field, Set<String> fieldValues) {
        log.debug("预加载用户数据源: fieldKey={}, valueCount={}", field.getFieldKey(), fieldValues.size());
        // 实现用户字段的预加载逻辑
    }
    
    /**
     * 预加载组织架构数据源
     */
    private void preloadOrgDataSource(FieldBasicsHtml field, Set<String> fieldValues) {
        log.debug("预加载组织架构数据源: fieldKey={}, valueCount={}", field.getFieldKey(), fieldValues.size());
        // 实现组织架构字段的预加载逻辑
    }
    
    /**
     * 判断字段类型是否支持缓存
     */
    private boolean isCacheableFieldType(DataFieldType fieldType) {
        if (ObjectNull.isNull(fieldType)) {
            return false;
        }
        
        switch (fieldType) {
            case cascader:
            case select:
            case radio:
            case checkbox:
            case user:
            case organization:
            case department:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * 处理单条数据的回显
     */
    private Map<String, Object> processDataEcho(Map<String, Object> data, 
                                               List<FieldBasicsHtml> fields, 
                                               boolean override) {
        // 这里复用原有的echo逻辑，但会利用已预加载的请求级缓存
        Map<String, Object> result = new HashMap<>(data);
        
        for (FieldBasicsHtml field : fields) {
            try {
                Object fieldValue = data.get(field.getFieldKey());
                if (ObjectNull.isNotNull(fieldValue)) {
                    // 调用相应的Handler进行回显处理
                    // 由于缓存已预加载，这里的处理会很快
                    Object echoValue = processFieldEcho(field, fieldValue, data, override);
                    
                    if (override) {
                        result.put(field.getFieldKey(), echoValue);
                    } else {
                        result.put(field.getFieldKey() + "_1", echoValue);
                    }
                }
            } catch (Exception e) {
                log.warn("字段回显处理失败: fieldKey={}", field.getFieldKey(), e);
            }
        }
        
        return result;
    }
    
    /**
     * 处理单个字段的回显
     */
    private Object processFieldEcho(FieldBasicsHtml field, Object fieldValue, 
                                   Map<String, Object> lineData, boolean override) {
        // 这里需要调用相应的Handler
        // 由于缓存已预加载，Handler的getEchoValue方法会直接从请求级缓存获取数据
        
        // 示例实现，实际需要根据字段类型调用相应的Handler
        return fieldValue; // 临时返回原值
    }
    
    /**
     * 回退到原始处理方式
     */
    private List<Map<String, Object>> fallbackEcho(List<Map<String, Object>> dataList, 
                                                   List<FieldBasicsHtml> fields, 
                                                   boolean override) {
        log.warn("回退到原始回显处理方式");
        // 这里调用原有的echo方法
        return dataList;
    }
    
    /**
     * 输出性能统计信息
     */
    private void logPerformanceStats(int dataSize, int fieldSize, long elapsedTime) {
        var stats = requestLevelEchoCache.getCurrentRequestStats();
        int cacheSize = requestLevelEchoCache.getCurrentCacheSize();
        
        log.info("优化回显处理完成: 数据量={}, 字段数={}, 耗时={}ms, 缓存大小={}", 
                dataSize, fieldSize, elapsedTime, cacheSize);
        
        if (!stats.isEmpty()) {
            log.info("缓存统计: {}", stats);
        }
    }
}
