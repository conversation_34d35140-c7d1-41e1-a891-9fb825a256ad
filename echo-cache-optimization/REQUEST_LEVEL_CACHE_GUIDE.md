# 请求级回显缓存优化方案

## 核心思路

使用ThreadLocal实现**请求级别的缓存**，在单次分页查询请求中：
1. **预加载阶段**：分析所有数据，收集需要回显的字段值，批量加载数据源
2. **回显阶段**：直接从ThreadLocal缓存获取回显值，避免重复查询
3. **清理阶段**：请求结束时自动清空ThreadLocal，避免内存泄漏

## 性能优化效果

- **查询次数**：从O(n*m)降低到O(k)，其中n=数据条数，m=字段数，k=唯一数据源数
- **性能提升**：预期50-80%的性能提升
- **内存安全**：请求结束自动清理，无内存泄漏风险
- **线程安全**：每个请求独立缓存，无并发冲突

## 核心组件

### 1. RequestLevelEchoCache
```java
// 请求级缓存管理器
@Component
public class RequestLevelEchoCache {
    // ThreadLocal存储：Map<cacheKey, Map<dataKey, echoValue>>
    private static final ThreadLocal<Map<String, Map<String, Object>>> REQUEST_CACHE;
    
    // 获取缓存值
    public Object getCachedValue(DataFieldType fieldType, String configKey, String dataKey);
    
    // 缓存数据映射
    public void cacheMapping(DataFieldType fieldType, String configKey, Map<String, Object> dataMapping);
    
    // 清空当前请求缓存
    public void clearCurrentRequestCache();
}
```

### 2. RequestCachedMultipleTypeHandler
```java
// 支持请求级缓存的Handler基类
public abstract class RequestCachedMultipleTypeHandler extends IMultipleTypeHandler {
    
    // 带缓存的回显处理
    protected Object echoValueWithRequestCache(
        MultipleHtml fieldDto, Object data, Map<String, Object> lineData,
        DataFieldType fieldType, 
        Function<MultipleHtml, String> configKeyExtractor,
        Function<String, Map<String, Object>> dataSourceLoader
    );
    
    // 预加载数据源
    public void preloadDataSourceIfNeeded(DataFieldType fieldType, String configKey, 
                                        Set<String> dataValues, Function<String, Map<String, Object>> dataSourceLoader);
}
```

### 3. EchoCacheInterceptor
```java
// 请求拦截器，管理ThreadLocal生命周期
@Component
public class EchoCacheInterceptor implements HandlerInterceptor {
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                               Object handler, Exception ex) {
        // 请求结束时清理ThreadLocal
        requestLevelEchoCache.clearCurrentRequestCache();
        requestLevelEchoCache.removeThreadLocalCache();
    }
}
```

## 集成步骤

### 1. 修改现有Handler

以CascaderFieldHandler为例：

```java
@Component
@DesignField(value = "级联选择", type = DataFieldType.cascader)
public class CascaderFieldHandler extends RequestCachedMultipleTypeHandler 
    implements IDataFieldHandler<CascaderItemHtml> {
    
    @Override
    public Object getEchoValue(CascaderItemHtml cascaderItem, Object data, 
                              boolean override, Map<String, Object> lineData, String... paths) {
        
        // 使用请求级缓存优化
        return echoValueWithRequestCache(
            cascaderItem, data, lineData,
            DataFieldType.cascader,
            this::getConfigKey,           // 配置键生成器
            cacheKey -> loadDataSource(cascaderItem, cacheKey) // 数据源加载器
        );
    }
    
    @Override
    protected String getConfigKey(CascaderItemHtml cascaderItem) {
        // 根据数据类型生成唯一的配置键
        switch (cascaderItem.getDatatype()) {
            case system:
                return "system:" + cascaderItem.getDictName();
            case dataModel:
                return "dataModel:" + cascaderItem.getFormId() + ":" + 
                       cascaderItem.getProps().getValue() + ":" + 
                       cascaderItem.getProps().getLabel();
            default:
                return "unknown:" + cascaderItem.hashCode();
        }
    }
    
    private Map<String, Object> loadDataSource(CascaderItemHtml cascaderItem, String cacheKey) {
        // 原有的数据加载逻辑，只在缓存未命中时调用
        switch (cascaderItem.getDatatype()) {
            case system:
                return loadSystemDictData(cascaderItem.getDictName());
            case dataModel:
                return loadDataModelData(cascaderItem);
            default:
                return new HashMap<>();
        }
    }
}
```

### 2. 修改DynamicDataServiceImpl

```java
@Service
public class DynamicDataServiceImpl {
    
    @Autowired
    private OptimizedPageQueryService optimizedPageQueryService;
    
    @Override
    public Page<Map<String, Object>> queryPage(String appId, Page<DynamicDataPo> page, 
                                              String modelId, /* 其他参数 */) {
        
        // 执行原有查询逻辑（不包含回显）
        Page<Map<String, Object>> result = originalQueryPage(appId, page, modelId, /* 其他参数，echo=false */);
        
        // 如果需要回显
        if (echo && !result.getRecords().isEmpty()) {
            // 使用优化的回显服务
            List<Map<String, Object>> optimizedRecords = optimizedPageQueryService.optimizedEcho(
                result.getRecords(), fieldBasicsHtmls, false
            );
            result.setRecords(optimizedRecords);
        }
        
        return result;
    }
}
```

### 3. 注册拦截器

```java
@Configuration
public class EchoCacheWebConfig implements WebMvcConfigurer {
    
    @Autowired
    private EchoCacheInterceptor echoCacheInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(echoCacheInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/static/**", "/error");
    }
}
```

## 工作流程

### 单次分页查询的执行流程：

1. **请求开始**：拦截器初始化ThreadLocal
2. **数据查询**：执行原有的分页查询逻辑（不包含回显）
3. **预加载阶段**：
   - 分析所有数据，按字段收集需要回显的值
   - 按字段类型和配置分组
   - 批量加载数据源到ThreadLocal缓存
4. **回显阶段**：
   - 遍历每条数据的每个字段
   - 直接从ThreadLocal缓存获取回显值
   - 无缓存命中时返回原值
5. **请求结束**：拦截器清理ThreadLocal

### 缓存键设计：

```
cacheKey = fieldType + ":" + configKey
例如：
- "cascader:system:region"           // 系统字典-行政区划
- "cascader:dataModel:form123:id:name" // 数据模型
- "select:system:gender"             // 选择框-性别字典
- "user:userService"                 // 用户字段
```

## 性能监控

### 1. 请求级统计

```java
// 在Controller中添加性能监控
@PostMapping("/query/page/{modelId}")
public R<Page<Map<String, Object>>> queryPage(/* 参数 */) {
    long startTime = System.currentTimeMillis();
    
    Page<Map<String, Object>> result = dynamicDataService.queryPage(/* 参数 */);
    
    long endTime = System.currentTimeMillis();
    
    // 获取缓存统计
    var cacheStats = requestLevelEchoCache.getCurrentRequestStats();
    int cacheSize = requestLevelEchoCache.getCurrentCacheSize();
    
    log.info("分页查询完成: 耗时={}ms, 缓存大小={}, 统计={}", 
             endTime - startTime, cacheSize, cacheStats);
    
    return R.ok(result);
}
```

### 2. 性能对比测试

```java
@Test
public void performanceTest() {
    // 准备测试数据：1000条数据，10个需要回显的字段
    List<Map<String, Object>> testData = prepareTestData(1000);
    List<FieldBasicsHtml> fields = prepareFields(10);
    
    // 原始方法测试
    long originalTime = testOriginalEcho(testData, fields);
    
    // 优化方法测试
    long optimizedTime = testOptimizedEcho(testData, fields);
    
    // 计算性能提升
    double improvement = (double)(originalTime - optimizedTime) / originalTime * 100;
    
    log.info("性能提升: {:.2f}% ({}ms -> {}ms)", improvement, originalTime, optimizedTime);
    
    // 预期结果：50-80%的性能提升
    assertTrue(improvement > 50);
}
```

## 注意事项

### 1. 内存管理
- ThreadLocal会在请求结束时自动清理
- 拦截器确保即使异常情况下也会清理ThreadLocal
- 单次请求的缓存大小通常很小，不会造成内存压力

### 2. 线程安全
- 每个请求线程有独立的ThreadLocal存储
- 不同请求之间完全隔离，无并发问题
- ConcurrentHashMap确保单个请求内的并发安全

### 3. 降级策略
- 缓存操作异常时自动回退到原始处理方式
- 确保系统稳定性和可用性
- 详细的异常日志便于问题排查

### 4. 扩展性
- 支持所有实现了IMultipleTypeHandler的字段类型
- 新增字段类型只需继承RequestCachedMultipleTypeHandler
- 配置键生成策略可灵活调整

## 预期效果

- **查询性能**：50-80%的性能提升
- **数据库压力**：显著减少重复查询
- **用户体验**：分页查询响应时间明显缩短
- **系统稳定性**：通过降级策略确保稳定运行
