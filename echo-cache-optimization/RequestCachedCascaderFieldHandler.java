package cn.bctools.design.data.fields.impl.advance;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.design.data.fields.DataFieldType;
import cn.bctools.design.data.fields.html.CascaderItemHtml;
import cn.bctools.design.data.fields.impl.RequestCachedMultipleTypeHandler;
import cn.bctools.design.data.service.DynamicDataService;
import cn.bctools.design.tree.dto.TreeDictDto;
import cn.bctools.design.tree.service.JvsTreeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 支持请求级缓存的级联选择Handler
 * 在单次请求中缓存数据源查询结果，避免重复查询
 * 
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class RequestCachedCascaderFieldHandler extends RequestCachedMultipleTypeHandler {
    
    private final JvsTreeService jvsTreeService;
    
    @Override
    public Object getEchoValue(CascaderItemHtml cascaderItem, Object data, boolean override, 
                              Map<String, Object> lineData, String... paths) {
        
        if (ObjectNull.isNull(data)) {
            return null;
        }
        
        // 使用请求级缓存处理回显
        return echoValueWithRequestCache(
            cascaderItem, 
            data, 
            lineData,
            DataFieldType.cascader,
            this::getConfigKey, // 配置键提取器
            cacheKey -> loadDataSource(cascaderItem, cacheKey) // 数据源加载器
        );
    }
    
    @Override
    protected String getConfigKey(CascaderItemHtml cascaderItem) {
        // 根据不同的数据类型生成不同的配置键
        switch (cascaderItem.getDatatype()) {
            case system:
                return "system:" + cascaderItem.getDictName();
            case dataModel:
                return "dataModel:" + cascaderItem.getFormId() + ":" + 
                       cascaderItem.getProps().getValue() + ":" + 
                       cascaderItem.getProps().getLabel();
            case option:
                return "option:" + cascaderItem.hashCode();
            case rule:
                return "rule:" + cascaderItem.getOptionHttp();
            default:
                return "unknown:" + cascaderItem.hashCode();
        }
    }
    
    /**
     * 加载数据源
     * 只在缓存未命中时调用
     */
    private Map<String, Object> loadDataSource(CascaderItemHtml cascaderItem, String cacheKey) {
        try {
            log.debug("加载级联选择数据源: datatype={}, cacheKey={}", 
                     cascaderItem.getDatatype(), cacheKey);
            
            switch (cascaderItem.getDatatype()) {
                case system:
                    return loadSystemDictData(cascaderItem.getDictName());
                case dataModel:
                    return loadDataModelData(cascaderItem);
                case option:
                    return loadOptionData(cascaderItem);
                case rule:
                    return loadRuleData(cascaderItem);
                default:
                    log.warn("不支持的级联选择数据类型: {}", cascaderItem.getDatatype());
                    return new HashMap<>();
            }
        } catch (Exception e) {
            log.error("加载级联选择数据源失败: cacheKey={}", cacheKey, e);
            return new HashMap<>();
        }
    }
    
    /**
     * 加载系统字典数据
     */
    private Map<String, Object> loadSystemDictData(String dictName) {
        try {
            List<TreeDictDto> dictList = getTreeDict(dictName);
            if (ObjectNull.isNotNull(dictList)) {
                return dictList.stream()
                    .collect(Collectors.toMap(
                        TreeDictDto::getId, 
                        TreeDictDto::getName, 
                        (existing, replacement) -> existing
                    ));
            }
        } catch (Exception e) {
            log.warn("加载系统字典数据失败: dictName={}", dictName, e);
        }
        return new HashMap<>();
    }
    
    /**
     * 加载数据模型数据
     */
    private Map<String, Object> loadDataModelData(CascaderItemHtml cascaderItem) {
        try {
            DynamicDataService dynamicDataService = SpringContextUtil.getBean(DynamicDataService.class);
            List<Map<String, Object>> dictList = dynamicDataService.queryList(
                cascaderItem.getFormId(), 
                cascaderItem.getProps().getLabel(), 
                cascaderItem.getProps().getSecTitle()
            );
            
            if (ObjectNull.isNotNull(dictList)) {
                return dictList.stream()
                    .filter(e -> ObjectNull.isNotNull(e.get(cascaderItem.getProps().getValue())))
                    .collect(Collectors.toMap(
                        e -> e.get(cascaderItem.getProps().getValue()).toString(),
                        e -> e.get(cascaderItem.getProps().getLabel()),
                        (existing, replacement) -> existing
                    ));
            }
        } catch (Exception e) {
            log.warn("加载数据模型数据失败: formId={}", cascaderItem.getFormId(), e);
        }
        return new HashMap<>();
    }
    
    /**
     * 加载选项数据
     */
    private Map<String, Object> loadOptionData(CascaderItemHtml cascaderItem) {
        try {
            Map<String, Object> optionMap = new HashMap<>();
            
            // 处理选项配置，转换为id->name的映射
            if (ObjectNull.isNotNull(cascaderItem.getOptions())) {
                // 这里需要根据实际的选项数据结构来实现
                // 示例实现，具体需要根据你的选项配置结构调整
                cascaderItem.getOptions().forEach(option -> {
                    // 假设option有getValue()和getLabel()方法
                    // optionMap.put(option.getValue(), option.getLabel());
                });
            }
            
            return optionMap;
        } catch (Exception e) {
            log.warn("加载选项数据失败", e);
        }
        return new HashMap<>();
    }
    
    /**
     * 加载规则引擎数据
     */
    private Map<String, Object> loadRuleData(CascaderItemHtml cascaderItem) {
        try {
            // 这里需要根据实际的规则引擎实现来加载数据
            // 示例实现
            String optionHttp = cascaderItem.getOptionHttp();
            if (ObjectNull.isNotNull(optionHttp)) {
                // 调用规则引擎获取数据
                // 具体实现需要根据你的规则引擎来调整
            }
            
            return new HashMap<>();
        } catch (Exception e) {
            log.warn("加载规则引擎数据失败: optionHttp={}", cascaderItem.getOptionHttp(), e);
        }
        return new HashMap<>();
    }
    
    /**
     * 获取树形字典数据（复用原有逻辑）
     */
    private List<TreeDictDto> getTreeDict(String dictUniqueName) {
        try {
            // 这里可以复用原有的getTreeDict方法逻辑
            Map<String, Object> treeDict = jvsTreeService.getByUniqueName(dictUniqueName, 0);
            // ... 其他处理逻辑，需要根据实际实现来调整
            return null; // 实际实现需要返回正确的数据
        } catch (Exception e) {
            log.warn("获取树形字典失败: dictName={}", dictUniqueName, e);
            return null;
        }
    }
    
    @Override
    protected Object echoValueFallback(CascaderItemHtml fieldDto, Object data, Map<String, Object> lineData) {
        // 回退到原始处理方式
        log.debug("使用回退处理方式处理级联选择回显");
        // 这里可以调用原有的CascaderFieldHandler的getEchoValue方法
        return data;
    }
    
    @Override
    protected DataFieldType getFieldType() {
        return DataFieldType.cascader;
    }
}
