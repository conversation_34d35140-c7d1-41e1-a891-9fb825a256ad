package cn.bctools.design.data.fields.impl;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.design.data.cache.RequestLevelEchoCache;
import cn.bctools.design.data.fields.DataFieldType;
import cn.bctools.design.data.fields.html.MultipleHtml;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 支持请求级缓存的多选类型Handler基类
 * 继承原有的IMultipleTypeHandler，增加请求级缓存功能
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class RequestCachedMultipleTypeHandler extends IMultipleTypeHandler {
    
    /**
     * 获取请求级缓存管理器
     */
    protected RequestLevelEchoCache getRequestCache() {
        return SpringContextUtil.getBean(RequestLevelEchoCache.class);
    }
    
    /**
     * 带请求级缓存的回显值处理
     * 
     * @param fieldDto 字段配置
     * @param data 原始数据
     * @param lineData 行数据
     * @param fieldType 字段类型
     * @param configKeyExtractor 配置键提取器
     * @param dataSourceLoader 数据源加载器（只在缓存未命中时调用）
     * @return 回显值
     */
    protected Object echoValueWithRequestCache(MultipleHtml fieldDto, Object data, Map<String, Object> lineData,
                                             DataFieldType fieldType, 
                                             Function<MultipleHtml, String> configKeyExtractor,
                                             Function<String, Map<String, Object>> dataSourceLoader) {
        
        if (ObjectNull.isNull(data)) {
            return null;
        }
        
        String configKey = configKeyExtractor.apply(fieldDto);
        if (ObjectNull.isNull(configKey)) {
            // 如果无法生成配置键，回退到原始处理方式
            return echoValueFallback(fieldDto, data, lineData);
        }
        
        RequestLevelEchoCache requestCache = getRequestCache();
        
        // 处理单个值或多个值
        if (data instanceof List) {
            return handleMultipleValues((List<?>) data, fieldType, configKey, requestCache, dataSourceLoader, fieldDto);
        } else {
            return handleSingleValue(data.toString(), fieldType, configKey, requestCache, dataSourceLoader, fieldDto);
        }
    }
    
    /**
     * 处理单个值的回显
     */
    private Object handleSingleValue(String dataValue, DataFieldType fieldType, String configKey,
                                   RequestLevelEchoCache requestCache, 
                                   Function<String, Map<String, Object>> dataSourceLoader,
                                   MultipleHtml fieldDto) {
        
        // 先尝试从请求缓存获取
        Object cachedValue = requestCache.getCachedValue(fieldType, configKey, dataValue);
        if (ObjectNull.isNotNull(cachedValue)) {
            return cachedValue;
        }
        
        // 缓存未命中，检查是否已加载过该配置的数据源
        if (!requestCache.isCached(fieldType, configKey)) {
            // 加载数据源并缓存
            loadAndCacheDataSource(fieldType, configKey, requestCache, dataSourceLoader);
        }
        
        // 再次尝试从缓存获取
        Object result = requestCache.getCachedValue(fieldType, configKey, dataValue);
        return ObjectNull.isNotNull(result) ? result : dataValue;
    }
    
    /**
     * 处理多个值的回显
     */
    private Object handleMultipleValues(List<?> dataList, DataFieldType fieldType, String configKey,
                                      RequestLevelEchoCache requestCache,
                                      Function<String, Map<String, Object>> dataSourceLoader,
                                      MultipleHtml fieldDto) {
        
        List<String> stringValues = dataList.stream()
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.toList());
        
        if (stringValues.isEmpty()) {
            return "";
        }
        
        // 确保数据源已加载
        if (!requestCache.isCached(fieldType, configKey)) {
            loadAndCacheDataSource(fieldType, configKey, requestCache, dataSourceLoader);
        }
        
        // 批量获取回显值
        List<String> resultList = stringValues.stream()
                .map(value -> {
                    Object echoValue = requestCache.getCachedValue(fieldType, configKey, value);
                    return ObjectNull.isNotNull(echoValue) ? echoValue.toString() : value;
                })
                .collect(Collectors.toList());
        
        // 根据字段配置决定返回格式
        boolean isMultiple = Boolean.TRUE.equals(fieldDto.getMultiple());
        if (isMultiple) {
            return String.join(",", resultList);
        } else {
            return resultList.isEmpty() ? "" : resultList.get(0);
        }
    }
    
    /**
     * 加载数据源并缓存到请求级缓存
     */
    private void loadAndCacheDataSource(DataFieldType fieldType, String configKey,
                                      RequestLevelEchoCache requestCache,
                                      Function<String, Map<String, Object>> dataSourceLoader) {
        
        try {
            log.debug("加载数据源: fieldType={}, configKey={}", fieldType, configKey);
            
            Map<String, Object> dataMapping = dataSourceLoader.apply(configKey);
            if (ObjectNull.isNotNull(dataMapping) && !dataMapping.isEmpty()) {
                // 缓存到请求级缓存
                requestCache.cacheMapping(fieldType, configKey, dataMapping);
                log.debug("数据源加载完成: fieldType={}, configKey={}, size={}", 
                         fieldType, configKey, dataMapping.size());
            } else {
                // 即使是空数据也要标记为已缓存，避免重复查询
                requestCache.cacheMapping(fieldType, configKey, new HashMap<>());
                log.debug("数据源为空: fieldType={}, configKey={}", fieldType, configKey);
            }
        } catch (Exception e) {
            log.warn("加载数据源失败: fieldType={}, configKey={}", fieldType, configKey, e);
            // 缓存空映射，避免重复失败查询
            requestCache.cacheMapping(fieldType, configKey, new HashMap<>());
        }
    }
    
    /**
     * 批量预检查需要的数据源
     * 在分页查询开始时调用，分析所有需要的数据源并预加载
     * 
     * @param fieldType 字段类型
     * @param configKey 配置键
     * @param dataValues 需要回显的数据值集合
     * @param dataSourceLoader 数据源加载器
     */
    public void preloadDataSourceIfNeeded(DataFieldType fieldType, String configKey, Set<String> dataValues,
                                        Function<String, Map<String, Object>> dataSourceLoader) {
        
        if (ObjectNull.isNull(fieldType) || ObjectNull.isNull(configKey) || 
            ObjectNull.isNull(dataValues) || dataValues.isEmpty()) {
            return;
        }
        
        RequestLevelEchoCache requestCache = getRequestCache();
        
        // 检查是否已经缓存
        if (!requestCache.isCached(fieldType, configKey)) {
            log.debug("预加载数据源: fieldType={}, configKey={}, valueCount={}", 
                     fieldType, configKey, dataValues.size());
            
            // 加载并缓存数据源
            loadAndCacheDataSource(fieldType, configKey, requestCache, dataSourceLoader);
        } else {
            log.debug("数据源已缓存，跳过预加载: fieldType={}, configKey={}", fieldType, configKey);
        }
    }
    
    /**
     * 回退到原始处理方式（子类需要实现）
     */
    protected abstract Object echoValueFallback(MultipleHtml fieldDto, Object data, Map<String, Object> lineData);
    
    /**
     * 获取字段类型（子类需要实现）
     */
    protected abstract DataFieldType getFieldType();
    
    /**
     * 获取配置键（子类需要实现）
     * 用于标识不同的数据源配置
     */
    protected abstract String getConfigKey(MultipleHtml fieldDto);
}
