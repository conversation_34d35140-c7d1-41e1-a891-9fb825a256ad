<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (c) 1992, 2014, Oracle and/or its affiliates. All rights reserved.
ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
-->

<!DOCTYPE transformations PUBLIC "-//NetBeans//DTD Module Automatic Dependencies 1.0//EN" "http://www.netbeans.org/dtds/module-auto-deps-1_0.dtd">

<transformations version="1.0">

    <transformationgroup>
        <description>Xerces is now available only as an autoload module, not in classpath: http://libs.netbeans.org/#xerces</description>
        <transformation>
            <trigger-dependency type="cancel">
                <package-dependency name="org.apache.xerces"/>
            </trigger-dependency>
            <implies>
                <result>
                    <module-dependency codenamebase="org.netbeans.libs.xerces" major="1" spec="1.2"/>
                </result>
            </implies>
        </transformation>
        <transformation>
            <trigger-dependency type="cancel">
                <package-dependency name="org.apache.xerces.parsers"/>
            </trigger-dependency>
            <implies>
                <result>
                    <module-dependency codenamebase="org.netbeans.libs.xerces" major="1" spec="1.2"/>
                </result>
            </implies>
        </transformation>
        <transformation>
            <trigger-dependency type="cancel">
                <package-dependency name="org.apache.xerces.impl"/>
            </trigger-dependency>
            <implies>
                <result>
                    <module-dependency codenamebase="org.netbeans.libs.xerces" major="1" spec="1.2"/>
                </result>
            </implies>
        </transformation>
        <transformation>
            <trigger-dependency type="cancel">
                <package-dependency name="org.apache.xerces.dom"/>
            </trigger-dependency>
            <implies>
                <result>
                    <module-dependency codenamebase="org.netbeans.libs.xerces" major="1" spec="1.2"/>
                </result>
            </implies>
        </transformation>
        <transformation>
            <trigger-dependency type="cancel">
                <package-dependency name="org.apache.xerces.jaxp"/>
            </trigger-dependency>
            <implies>
                <result>
                    <module-dependency codenamebase="org.netbeans.libs.xerces" major="1" spec="1.2"/>
                </result>
            </implies>
        </transformation>
        <transformation>
            <trigger-dependency type="cancel">
                <package-dependency name="org.apache.xml.serialize"/>
            </trigger-dependency>
            <implies>
                <result>
                    <module-dependency codenamebase="org.netbeans.libs.xerces" major="1" spec="1.2"/>
                </result>
            </implies>
        </transformation>
        <transformation>
            <trigger-dependency type="cancel">
                <package-dependency name="org.w3c.dom.ranges"/>
            </trigger-dependency>
            <implies>
                <result>
                    <module-dependency codenamebase="org.netbeans.libs.xerces" major="1" spec="1.2"/>
                </result>
            </implies>
        </transformation>
    </transformationgroup>

    <transformationgroup>
        <description>Regexp is now available only as an autoload module compiled manually from libs/regexp in the release40 branch, not on classpath: http://libs.netbeans.org/#regexp</description>
        <transformation>
            <trigger-dependency type="cancel">
                <package-dependency name="org.apache.regexp"/>
            </trigger-dependency>
            <implies>
                <result>
                    <module-dependency codenamebase="org.netbeans.libs.regexp" spec="1.2"/>
                </result>
            </implies>
        </transformation>
    </transformationgroup>

</transformations>
