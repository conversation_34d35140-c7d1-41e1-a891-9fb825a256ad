<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (c) 1992, 2014, Oracle and/or its affiliates. All rights reserved.
ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
-->

<!DOCTYPE transformations PUBLIC "-//NetBeans//DTD Module Automatic Dependencies 1.0//EN" "http://www.netbeans.org/dtds/module-auto-deps-1_0.dtd">

<transformations version="1.0">
    <transformationgroup>
        <description>SaveCookie extends Savable. To compile you need to include openide.awt on classpath.</description>
        <transformation>
            <trigger-dependency type="older">
                <module-dependency codenamebase="org.openide.nodes" spec="7.23"/>
            </trigger-dependency>
            <implies>
                <result>
                    <module-dependency codenamebase="org.openide.awt"/>
                </result>
            </implies>
        </transformation>
    </transformationgroup>

</transformations>
