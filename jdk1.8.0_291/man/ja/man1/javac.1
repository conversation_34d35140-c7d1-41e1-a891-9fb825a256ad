'\" t
.\" Copyright (c) 1994, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: javac
.\" Language: Japanese
.\" Date: 2015年3月3日
.\" SectDesc: 基本ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "javac" "1" "2015年3月3日" "JDK 8" "基本ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
javac \- Javaクラスおよびインタフェースの定義を読み取り、バイトコードおよびクラス・ファイルにコンパイルします。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavac\fR [ \fIoptions\fR ] [ \fIsourcefiles\fR ] [ \fIclasses\fR] [ \fI@argfiles\fR ]
.fi
.if n \{\
.RE
.\}
.PP
引数を指定する順序は任意です。
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIsourcefiles\fR
.RS 4
コンパイルされる1つ以上のソース・ファイル(\fBMyClass\&.java\fRなど)。
.RE
.PP
\fIclasses\fR
.RS 4
注釈の処理対象となる1つ以上のクラス(\fBMyPackage\&.MyClass\fRなど)。
.RE
.PP
\fI@argfiles\fR
.RS 4
オプションとソース・ファイルを一覧表示する1つ以上のファイル。このファイルの中では\fB\-J\fRオプションは指定できません。コマンド行引数ファイルを参照してください。
.RE
.SH "説明"
.PP
\fBjavac\fRコマンドは、Javaプログラミング言語で記述されたクラスとインタフェースの定義を読み取り、バイトコードのクラス・ファイルにコンパイルします。\fBjavac\fRコマンドでは、Javaソース・ファイルおよびクラス内の注釈の処理もできます。
.PP
ソース・コードのファイル名を\fBjavac\fRに渡すには、2つの方法があります。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
ソース・ファイルの数が少ない場合は、ファイル名をコマンドラインで指定します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
ソース・ファイルの数が多い場合は、ファイル内のファイル名を空白または改行で区切って指定します。\fBjavac\fRコマンドで、リスト・ファイル名の先頭にアットマーク(@)を使用します。
.RE
.PP
ソース・コードのファイル名は\&.java拡張子を、クラスのファイル名は\&.class拡張子を持っている必要があります。また、ソース・ファイルとクラス・ファイルのどちらも、該当するクラスに対応するルート名を持っている必要があります。たとえば、\fBMyClass\fRという名前のクラスは、\fBMyClass\&.java\fRという名前のソース・ファイルに記述されます。このソース・ファイルは、\fBMyClass\&.class\fRという名前のバイトコード・クラス・ファイルにコンパイルされます。
.PP
内部クラスが定義されていると、追加のクラス・ファイルが生成されます。これらのクラス・ファイルの名前は、\fBMyClass$MyInnerClass\&.class\fRのように、内部クラス名と外部クラス名を組み合せたものになります。
.PP
ソース・ファイルは、パッケージ・ツリーを反映したディレクトリ・ツリーに配置します。たとえば、すべてのソース・ファイルが\fB/workspace\fRにある場合、\fBcom\&.mysoft\&.mypack\&.MyClass\fRのソース・コードを、\fB/workspace/com/mysoft/mypack/MyClass\&.java\fRに格納します。
.PP
デフォルトでは、コンパイラは、各クラス・ファイルを対応するソース・ファイルと同じディレクトリに格納します。\fB\-d\fRオプションを使用して、別の出力先ディレクトリを指定できます。
.SH "オプション"
.PP
コンパイラには、現在の開発環境でサポートされる標準オプションのセットがあります。これ以外の非標準オプションは、現在の仮想マシンおよびコンパイラの実装に固有のオプションで、将来、変更される可能性があります。非標準オプションは、\fB\-X\fRオプションで始まります。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
クロスコンパイル・オプションも参照してください
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
非標準オプションも参照してください
.RE
.SS "標準オプション"
.PP
\-A\fIkey\fR[\fI=value\fR]
.RS 4
注釈プロセッサに渡すオプションを指定します。これらのオプションは、\fBjavac\fRが直接解釈するのではなく、それぞれのプロセッサで使用できるようになります。\fBkey\fRの値は、1つまたは複数の識別子をドット(\&.)で区切る必要があります。
.RE
.PP
\-cp \fIpath\fR or \-classpath \fIpath\fR
.RS 4
ユーザー・クラス・ファイル、および(オプションで)注釈プロセッサとソース・ファイルを検索する場所を指定します。このクラス・パスは\fBCLASSPATH\fR環境変数のユーザー・クラス・パスをオーバーライドします。\fBCLASSPATH\fR、\fB\-cp\fR、\fB\-classpath\fRのいずれも指定されていない場合、ユーザーの\fIクラス・パス\fRは、現在のディレクトリになります。クラス・パスの設定 を参照してください。
.sp
\fB\-sourcepath\fRオプションが指定されていない場合、ソース・ファイルもユーザー・クラス・パスから検索されます。
.sp
\fB\-processorpath\fRオプションが指定されていない場合、注釈プロセッサもクラス・パスから検索されます。
.RE
.PP
\-Djava\&.ext\&.dirs=\fIdirectories\fR
.RS 4
インストール済拡張機能の位置をオーバーライドします。
.RE
.PP
\-Djava\&.endorsed\&.dirs=\fIdirectories\fR
.RS 4
承認された標準パスの位置をオーバーライドします。
.RE
.PP
\-d \fIdirectory\fR
.RS 4
クラス・ファイルの出力先ディレクトリを設定します。そのディレクトリは\fBjavac\fRでは作成されないため、すでに存在している必要があります。クラスがパッケージの一部である場合、\fBjavac\fRは、パッケージ名を反映したサブディレクトリ内にクラス・ファイルを格納し、必要に応じてディレクトリを作成します。
.sp
\fB\-d\fR
\fB/home/<USER>/home/<USER>/com/mypackage/MyClass\&.class\fRになります。
.sp
\fI\-d\fRオプションが指定されなかった場合、\fBjavac\fRは、各クラス・ファイルを、その生成元となるソース・ファイルと同じディレクトリ内に格納します。
.sp
\fB注意:\fR
\fI\-d\fRオプションによって指定されたディレクトリは、ユーザー・クラス・パスに自動的に追加されません。
.RE
.PP
\-deprecation
.RS 4
非推奨のメンバーまたはクラスが使用またはオーバーライドされるたびに、説明を表示します。\fB\-deprecation\fRオプションが指定されていない場合、\fBjavac\fRは、非推奨のメンバーまたはクラスを使用またはオーバーライドしているソース・ファイルのサマリーを表示します。\fB\-deprecation\fRオプションは、\fB\-Xlint:deprecation\fRの省略表記です。
.RE
.PP
\-encoding \fIencoding\fR
.RS 4
ソース・ファイルのエンコーディング名(EUC\-JPやUTF\-8など)を設定します。\fB\-encoding\fRオプションが指定されていない場合は、プラットフォームのデフォルト・コンバータが使用されます。
.RE
.PP
\-endorseddirs \fIdirectories\fR
.RS 4
承認された標準パスの位置をオーバーライドします。
.RE
.PP
\-extdirs \fIdirectories\fR
.RS 4
\fBext\fRディレクトリの位置をオーバーライドします。directories変数には、コロンで区切ったディレクトリのリストを指定します。指定したディレクトリ内の各JARファイルから、クラス・ファイルが検索されます。検出されたすべてのJARファイルは、クラス・パスの一部になります。
.sp
クロスコンパイル(異なるJavaプラットフォームに実装されたブートストラップ・クラスや拡張機能クラスに対してコンパイルを行う)を実行する場合、このオプションには拡張機能クラスを含むディレクトリを指定します。詳細はクロスコンパイル・オプションを参照してください。
.RE
.PP
\-g
.RS 4
ローカル変数を含むすべてのデバッグ情報を生成します。デフォルトでは、行番号およびソース・ファイル情報のみが生成されます。
.RE
.PP
\-g:none
.RS 4
デバッグ情報を生成しません。
.RE
.PP
\-g:[\fIkeyword list\fR]
.RS 4
カンマで区切られたキーワード・リストにより指定された、特定の種類のデバッグ情報のみを生成します。次のキーワードが有効です。
.PP
source
.RS 4
ソース・ファイルのデバッグ情報。
.RE
.PP
lines
.RS 4
行番号のデバッグ情報。
.RE
.PP
vars
.RS 4
ローカル変数のデバッグ情報。
.RE
.RE
.PP
\-help
.RS 4
標準オプションの概要を出力します。
.RE
.PP
\-implicit:[\fIclass, none\fR]
.RS 4
暗黙的にロードされたソース・ファイルに対するクラス・ファイルの生成を制御します。クラス・ファイルを自動生成するには、\fB\-implicit:class\fRを使用します。クラス・ファイルの生成を抑制するには、\fB\-implicit:none\fRを使用します。このオプションが指定されなかった場合のデフォルト動作は、クラス・ファイルの自動生成になります。その場合、そのようなクラス・ファイルが生成された時に注釈処理も実行されると、コンパイラから警告が発行されます。\fB\-implicit\fRオプションが明示的に設定された場合、警告は発行されません。型の検索を参照してください。
.RE
.PP
\-J\fIoption\fR
.RS 4
Java Virtual Machine (JVM)に\fBoption\fRを渡します。optionには、Java起動ツールのリファレンス・ページに記載されているオプションを1つ指定します。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。java(1)を参照してください。
.sp
\fB注意:\fR
\fICLASSPATH\fR、\fB\-classpath\fR、\fB\-bootclasspath\fRおよび\fB\-extdirs\fRオプションは、\fBjavac\fRの実行に使用されるクラスを指定しません。これらのオプションおよび変数を使用してコンパイラの実装をカスタマイズしようとすると、リスクが高く、多くの場合、必要な処理が実行されません。コンパイラの実装をカスタマイズする必要がある場合、\fB\-J\fRオプションを使用して、基礎となるJava起動ツールにオプションを渡します。
.RE
.PP
\-nowarn
.RS 4
警告メッセージを無効にします。このオプションは、\fB\-Xlint:none\fRオプションと同じように動作します。
.RE
.PP
\-parameters
.RS 4
リフレクションAPIのメソッド\fBjava\&.lang\&.reflect\&.Executable\&.getParameters\fRが取得できるように、生成されるクラス・ファイル内のコンストラクタとメソッドの仮パラメータ名を格納します。
.RE
.PP
\-proc: [\fInone\fR, \fIonly\fR]
.RS 4
注釈処理およびコンパイルを実行するかを制御します。\fB\-proc:none\fRは、注釈処理なしでコンパイルが実行されることを意味します。\fB\-proc:only\fRは、注釈処理のみが実行され、後続のコンパイルはまったく実行されないことを意味します。
.RE
.PP
\-processor \fIclass1\fR [,\fIclass2\fR,\fIclass3\fR\&.\&.\&.]
.RS 4
実行する注釈プロセッサの名前。これを指定した場合、デフォルトの検索処理は省略されます。
.RE
.PP
\-processorpath \fIpath\fR
.RS 4
注釈プロセッサを検索する場所を指定します。このオプションが使用されない場合、クラス・パスのプロセッサが検索されます。
.RE
.PP
\-s \fIdir\fR
.RS 4
生成されたソース・ファイルの格納先となるディレクトリを指定します。そのディレクトリは\fBjavac\fRでは作成されないため、すでに存在している必要があります。クラスがパッケージの一部である場合、コンパイラは、パッケージ名を反映したサブディレクトリ内にソース・ファイルを格納し、必要に応じてディレクトリを作成します。
.sp
\fB\-s /home/<USER>/home/<USER>/com/mypackage/MyClass\&.java\fRに格納されます。
.RE
.PP
\-source \fIrelease\fR
.RS 4
受け付けるソース・コードのバージョンを指定します。\fBrelease\fRには次の値を指定できます。
.PP
1\&.3
.RS 4
このコンパイラでは、Java SE 1\&.3以降に導入されたアサーション、総称または他の言語機能をサポートしません。
.RE
.PP
1\&.4
.RS 4
Java SE 1\&.4で導入された、アサーションを含むコードを受け付けます。
.RE
.PP
1\&.5
.RS 4
Java SE 5で導入された総称および他の言語機能を含んだコードを受け付けます。
.RE
.PP
5
.RS 4
1\&.5と同義です。
.RE
.PP
1\&.6
.RS 4
Java SE 6では言語に対する変更は導入されませんでした。しかし、ソース・ファイル内のエンコーディング・エラーが、Java Platform, Standard Editionの以前のリリースような警告ではなく、エラーとして報告されるようになりました。
.RE
.PP
6
.RS 4
1\&.6と同義です。
.RE
.PP
1\&.7
.RS 4
Java SE 7で導入された機能を含むコードを受け付けます。
.RE
.PP
7
.RS 4
1\&.7と同義です。
.RE
.PP
1\&.8
.RS 4
これがデフォルト値です。Java SE 8で導入された機能を含むコードを受け付けます。
.RE
.PP
8
.RS 4
1\&.8と同義です。
.RE
.RE
.PP
\-sourcepath \fIsourcepath\fR
.RS 4
クラスまたはインタフェースの定義を検索するソース・コード・パスを指定します。ユーザー・クラス・パスと同様に、ソース・パスのエントリは、Oracle Solarisではコロン(:)で、Windowsではセミコロンで区切り、ここには、ディレクトリ、JARアーカイブまたはZIPアーカイブを指定できます。パッケージを使用している場合は、ディレクトリまたはアーカイブ内のローカル・パス名がパッケージ名を反映している必要があります。
.sp
\fB注意:\fR
ソース・ファイルも見つかった場合、クラス・パスにより見つかったクラスは再コンパイルされる可能性があります。型の検索を参照してください。
.RE
.PP
\-verbose
.RS 4
ロードされるクラスおよびコンパイルされるソース・ファイルごとの情報が出力される、詳細出力を使用します。
.RE
.PP
\-version
.RS 4
リリース情報を出力します。
.RE
.PP
\-werror
.RS 4
警告が発生した場合にコンパイルを終了します。
.RE
.PP
\-X
.RS 4
非標準オプションに関する情報を表示して終了します。
.RE
.SS "クロスコンパイル・オプション"
.PP
デフォルトでは、クラスのコンパイルは、\fBjavac\fRが添付されているプラットフォームのブートストラップ・クラスおよび拡張機能クラスに対して行われます。ただし、\fBjavac\fRは、異なるJavaプラットフォームに実装されたブートストラップ・クラスおよび拡張機能クラスに対してコンパイルを行うクロスコンパイルもサポートしています。クロスコンパイルを行う場合は、\fB\-bootclasspath\fRおよび\fB\-extdirs\fRオプションを使用することが重要です。
.PP
\-target \fIversion\fR
.RS 4
仮想マシンの指定されたリリースを対象とするクラス・ファイルを生成します。クラス・ファイルは、指定されたターゲット以降のリリースでは動作しますが、それより前のリリースのJVMでは動作しません。有効なターゲットは、1\&.1、1\&.2、1\&.3、1\&.4、1\&.5 (5も可)、1\&.6 (6も可)、1\&.7 (7も可)および1\&.8 (8も可)です。
.sp
\fB\-target\fRオプションのデフォルトは、\fB\-source\fRオプションの値によって異なります。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-source\fRオプションが指定されていない場合、\fB\-target\fRオプションの値は1\&.8です。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-source\fRオプションが1\&.2の場合、\fB\-target\fRオプションの値は1\&.4です。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-source\fRオプションが1\&.3の場合、\fB\-target\fRオプションの値は1\&.4です。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-source\fRオプションが1\&.5の場合、\fB\-target\fRオプションの値は1\&.8です。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-source\fRオプションが1\&.6の場合、\fB\-target\fRオプションの値は1\&.8です。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-source\fRオプションが1\&.7の場合、\fB\-target\fRオプションの値は1\&.8です。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-source\fRオプションの他のすべての値の場合、\fB\-target\fRオプションの値は、\fB\-source\fRオプションの値になります。
.RE
.RE
.PP
\-bootclasspath \fIbootclasspath\fR
.RS 4
指定された一連のブート・クラスに対してクロスコンパイルを行います。ユーザー・クラス・パスと同様に、ブート・クラス・パスのエントリはコロン(:)で区切り、ここには、ディレクトリ、JARアーカイブまたはZIPアーカイブを指定できます。
.RE
.SS "コンパクト・プロファイル・オプション"
.PP
JDK 8以降から、\fBjavac\fRコンパイラはコンパクト・プロファイルをサポートします。コンパクト・プロファイルを使用すると、Javaプラットフォーム全体を必要としないアプリケーションは、デプロイ可能で、小さいフットプリントで実行できます。コンパクト・プロファイル機能は、アプリケーション・ストアからのアプリケーションのダウンロード時間を短縮するのに使用できます。この機能は、JREをバンドルするJavaアプリケーションの、よりコンパクトなデプロイメントに役立ちます。この機能は、小さいデバイスでも役立ちます。
.PP
サポートされているプロファイル値は、\fBcompact1\fR、\fBcompact2\fRおよび\fBcompact3\fRです。これらは、追加のレイヤーです。大きい番号の各コンパクト・プロファイルには、小さい番号の名前のプロファイル内のすべてのAPIが含まれます。
.PP
\-profile
.RS 4
コンパクト・プロファイルを使用する場合、このオプションは、コンパイル時にプロファイル名を指定します。次に例を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavac \-profile compact1 Hello\&.java\fR
 
.fi
.if n \{\
.RE
.\}
javacは、指定されたプロファイルにない任意のJava SE APIを使用するソース・コードをコンパイルしません。これは、そのようなソース・コードをコンパイルしようとすることによって生じるエラー・メッセージの例です。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBcd jdk1\&.8\&.0/bin\fR
\fB\&./javac \-profile compact1 Paint\&.java\fR
\fBPaint\&.java:5: error: Applet is not available in profile \*(Aqcompact1\*(Aq\fR
\fBimport java\&.applet\&.Applet;\fR
 
.fi
.if n \{\
.RE
.\}
この例では、\fBApplet\fRクラスを使用しないようにソースを変更することによって、エラーを修正できます。\-profileオプションを指定せずにコンパイルすることによって、エラーを修正することもできます。コンパイルは、Java SE APIの完全なセットに対して実行されます。(どのコンパクト・プロファイルにも、\fBApplet\fRクラスは含まれていません。)
.sp
コンパクト・プロファイルを使用してコンパイルするための別の方法として、\fB\-bootclasspath\fRオプションを使用して、プロファイルのイメージを指定する\fBrt\&.jar\fRファイルへのパスを指定します。かわりに\fB\-profile\fRオプションを使用すると、プロファイル・イメージは、コンパイル時にシステム上に存在する必要がありません。これは、クロスコンパイル時に役立ちます。
.RE
.SS "非標準オプション"
.PP
\-Xbootclasspath/p:\fIpath\fR
.RS 4
ブートストラップ・クラス・パスに接尾辞を追加します。
.RE
.PP
\-Xbootclasspath/a:\fIpath\fR
.RS 4
ブートストラップ・クラス・パスに接頭辞を追加します。
.RE
.PP
\-Xbootclasspath/:\fIpath\fR
.RS 4
ブートストラップ・クラス・ファイルの位置をオーバーライドします。
.RE
.PP
\-Xdoclint:[\-]\fIgroup\fR [\fI/access\fR]
.RS 4
\fIgroup\fRの値が\fBaccessibility\fR、\fBsyntax\fR、\fBreference\fR、\fBhtml\fRまたは\fBmissing\fRのいずれかである特定のチェック・グループを有効または無効にします。これらのチェック・グループの詳細は、\fBjavadoc\fRコマンドの\fB\-Xdoclint\fRオプションを参照してください。\fB\-Xdoclint\fRオプションは、\fBjavac\fRコマンドではデフォルトで無効になります。
.sp
変数\fIaccess\fRは、\fB\-Xdoclint\fRオプションがチェックするクラスとメンバーの最小の可視性レベルを指定します。\fBpublic\fR、\fBprotected\fR、\fBpackage\fRおよび\fBprivate\fRの値(可視性の高い順)の1つを持つことができます。たとえば、次のオプションは、(protected、package、publicを含む) protected以上のアクセス・レベルを持つクラスおよびメンバーを(すべてのチェック・グループで)チェックします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Xdoclint:all/protected\fR
 
.fi
.if n \{\
.RE
.\}
次のオプションは、package以上のアクセス権(packageおよびpublicを含む)を持つクラスおよびメンバーに対するHTMLエラーをチェックしないことを除き、すべてのアクセス・レベルに対してすべてのチェック・グループを有効にします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Xdoclint:all,\-html/package\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-Xdoclint:none
.RS 4
すべてのチェック・グループを無効にします。
.RE
.PP
\-Xdoclint:all[\fI/access\fR]
.RS 4
すべてのチェック・グループを有効にします。
.RE
.PP
\-Xlint
.RS 4
推奨されるすべての警告を有効にします。このリリースでは、利用可能なすべての警告を有効にすることをお薦めします。
.RE
.PP
\-Xlint:all
.RS 4
推奨されるすべての警告を有効にします。このリリースでは、利用可能なすべての警告を有効にすることをお薦めします。
.RE
.PP
\-Xlint:none
.RS 4
すべての警告を無効にします。
.RE
.PP
\-Xlint:\fIname\fR
.RS 4
警告名を無効にします。このオプションで無効にできる警告のリストは、\-Xlintオプションを使用した警告の有効化または無効化を参照してください。
.RE
.PP
\-Xlint:\fI\-name\fR
.RS 4
警告名を無効にします。このオプションで無効にできる警告のリストを取得するには、\-Xlintオプションを使用した警告の有効化または無効化\-Xlint\fBオプションを使用した\fRを参照してください。
.RE
.PP
\-Xmaxerrs \fInumber\fR
.RS 4
印刷するエラーの最大数を設定します。
.RE
.PP
\-Xmaxwarns \fInumber\fR
.RS 4
印刷する警告の最大数を設定します。
.RE
.PP
\-Xstdout \fIfilename\fR
.RS 4
コンパイラのメッセージを、指定されたファイルに送信します。デフォルトでは、コンパイラのメッセージは\fBSystem\&.err\fRに送られます。
.RE
.PP
\-Xprefer:[\fInewer,source\fR]
.RS 4
ある型に対してソース・ファイルとクラス・ファイルの両方が見つかった場合、そのどちらのファイルを読み取るかを指定します。(型の検索を参照してください)。\fB\-Xprefer:newer\fRオプションを使用した場合、ある型に対するソース・ファイルとクラス・ファイルのうち新しい方が読み取られます(デフォルト)。\fB\-Xprefer:source\fRオプションを使用した場合、ソース・ファイルが読み取られます。\fBSOURCE\fRの保存ポリシーを使用して宣言された注釈に任意の注釈プロセッサがアクセスできるようにする場合は、\fB\-Xprefer:source\fRを使用してください。
.RE
.PP
\-Xpkginfo:[\fIalways\fR,\fIlegacy\fR,\fInonempty\fR]
.RS 4
javacがpackage\-info\&.javaファイルから\fBpackage\-info\&.class\fRファイルを生成するかどうかを制御します。このオプションで使用可能なmode引数は次のとおりです。
.PP
always
.RS 4
すべての\fBpackage\-info\&.java\fRファイルの\fBpackage\-info\&.class\fRファイルを常に生成します。このオプションは、各\fB\&.java\fRファイルに対応する\fB\&.class\fRファイルがあることを確認するAntなどのビルド・システムを使用する場合に役立つことがあります。
.RE
.PP
legacy
.RS 4
package\-info\&.javaに注釈が含まれる場合にのみ\fBpackage\-info\&.class\fRファイルを生成します。package\-info\&.javaにコメントのみ含まれる場合に\fBpackage\-info\&.class\fRファイルを生成しません。
.sp
\fB注意:\fR
\fBpackage\-info\&.class\fRファイルは生成できますが、package\-info\&.javaファイル内のすべての注釈に\fBRetentionPolicy\&.SOURCE\fRがある場合は空になります。
.RE
.PP
nonempty
.RS 4
package\-info\&.javaに\fBRetentionPolicy\&.CLASS\fRまたは\fBRetentionPolicy\&.RUNTIME\fRとともに注釈が含まれる場合にのみ\fBpackage\-info\&.class\fRファイルを生成します。
.RE
.RE
.PP
\-Xprint
.RS 4
デバッグ目的で指定した型のテキスト表示を出力します。注釈処理もコンパイルも実行されません。出力形式は変更される可能性があります。
.RE
.PP
\-XprintProcessorInfo
.RS 4
ある特定のプロセッサが処理を依頼されている注釈に関する情報を出力します。
.RE
.PP
\-XprintRounds
.RS 4
初回および後続の注釈処理ラウンドに関する情報を出力します。
.RE
.SH "-XLINTオプションを使用した警告の有効化または無効化"
.PP
\fB\-Xlint:name\fRオプションを使用して警告\fIname\fRを有効にします。ここで、\fBname\fRは次の警告名のいずれかになります。\fB\-Xlint:\-name:\fRオプションを使用して、警告を無効化できます。
.PP
cast
.RS 4
不要で冗長なキャストについて警告します。たとえば、次のようになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBString s = (String) "Hello!"\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
classfile
.RS 4
クラス・ファイルの内容に関連した問題について警告します。
.RE
.PP
deprecation
.RS 4
非推奨の項目の使用について警告します。たとえば、次のようになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava\&.util\&.Date myDate = new java\&.util\&.Date();\fR
\fBint currentDay = myDate\&.getDay();\fR
 
.fi
.if n \{\
.RE
.\}
メソッド\fBjava\&.util\&.Date\&.getDay\fRはJDK 1\&.1以降は推奨されていません。
.RE
.PP
dep\-ann
.RS 4
\fB@deprecated\fR
Javadocコメントでドキュメント化されているが、\fB@Deprecated\fR注釈が付いていない項目について警告します。たとえば、次のようになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/**\fR
\fB  * @deprecated As of Java SE 7, replaced by {@link #newMethod()}\fR
\fB  */\fR
\fBpublic static void deprecatedMethood() { }\fR
\fBpublic static void newMethod() { }\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
divzero
.RS 4
定整数0で除算されることについて警告します。たとえば、次のようになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBint divideByZero = 42 / 0;\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
empty
.RS 4
\fBif \fR文以降が空の文であることについて警告します。たとえば、次のようになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBclass E {\fR
\fB    void m() {\fR
\fB         if (true) ;\fR
\fB    }\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
fallthrough
.RS 4
fall\-throughケースのswitchブロックをチェックし、検出されたものに対して警告メッセージを表示します。Fall\-throughケースは、switchブロック内の最後のケースを除くケースです。このコードにはbreak文は含まれません。コードの実行をそのケースから次のケースへ移動します。たとえば、このswitchブロック内のcase 1ラベルに続くコードは、break文で終わっていません。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBswitch (x) {\fR
\fBcase 1:\fR
\fB  System\&.out\&.println("1");\fR
\fB  // No break statement here\&.\fR
\fBcase 2:\fR
\fB  System\&.out\&.println("2");\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
このコードのコンパイル時に\fB\-Xlint:fallthrough\fRオプションが使用されていた場合、コンパイラは、問題になっているケースの行番号とともに、caseにfall\-throughする可能性があることを示す警告を発行します。
.RE
.PP
finally
.RS 4
正常に完了できない\fBfinally\fR句について警告します。たとえば、次のようになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpublic static int m() {\fR
\fB  try {\fR
\fB     throw new NullPointerException();\fR
\fB  }  catch (NullPointerException(); {\fR
\fB     System\&.err\&.println("Caught NullPointerException\&.");\fR
\fB     return 1;\fR
\fB   } finally {\fR
\fB     return 0;\fR
\fB   }\fR
\fB  }\fR
 
.fi
.if n \{\
.RE
.\}
この例では、コンパイラは\fBfinally\fRブロックに関する警告を生成します。\fBint\fRメソッドが呼び出されると、値0が返されます。\fBfinally\fRブロックは、\fBtry\fRブロックが終了すると実行されます。この例では、制御が\fBcatch\fRブロックに移された場合、\fBint\fRメソッドは終了します。ただし、\fBfinally\fRブロックは実行される必要があるため、制御がメソッドの外部に移されていても、このブロックは実行されます。
.RE
.PP
options
.RS 4
コマンド行オプションの使用に関する問題について警告します。クロスコンパイル・オプションを参照してください。
.RE
.PP
overrides
.RS 4
メソッドのオーバーライドに関する問題について警告します。たとえば、次の2つのクラスがあるとします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpublic class ClassWithVarargsMethod {\fR
\fB  void varargsMethod(String\&.\&.\&. s) { }\fR
\fB}\fR
 
\fBpublic class ClassWithOverridingMethod extends ClassWithVarargsMethod {\fR
\fB   @Override\fR
\fB   void varargsMethod(String[] s) { }\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
コンパイラは、次のような警告を生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBwarning: [override] varargsMethod(String[]) in ClassWithOverridingMethod \fR
\fBoverrides varargsMethod(String\&.\&.\&.) in ClassWithVarargsMethod; overriding\fR
\fBmethod is missing \*(Aq\&.\&.\&.\*(Aq\fR
 
.fi
.if n \{\
.RE
.\}
コンパイラは、\fBvarargs\fRメソッドを検出すると、\fBvarargs\fRの仮パラメータを配列に変換します。メソッド\fBClassWithVarargsMethod\&.varargsMethod\fRでは、コンパイラは\fBvarargs\fRの仮パラメータ\fBString\&.\&.\&. s\fRを仮パラメータ\fBString[] s\fRに変換します。これは、メソッド\fBClassWithOverridingMethod\&.varargsMethod\fRの仮パラメータに対応する配列です。その結果、この例ではコンパイルが行われます。
.RE
.PP
path
.RS 4
コマンドラインでの無効なパス要素と存在しないパス・ディレクトリについて警告します(クラス・パス、ソース・パスなどのパス関連)。このような警告を\fB@SuppressWarnings\fR注釈で抑制することはできません。たとえば、次のようになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavac \-Xlint:path \-classpath /nonexistentpath Example\&.java\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
processing
.RS 4
注釈処理に関する問題について警告します。コンパイラがこの警告を生成するのは、注釈を含むクラスがあるときに、使用している注釈プロセッサでそのタイプの例外を処理できない場合です。たとえば、単純な注釈プロセッサを次に示します。
.sp
\fBソース・ファイルAnnocProc\&.java\fR:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBimport java\&.util\&.*;\fR
\fBimport javax\&.annotation\&.processing\&.*;\fR
\fBimport javax\&.lang\&.model\&.*;\fR
\fBimport\&.javaz\&.lang\&.model\&.element\&.*;\fR
 
\fB@SupportedAnnotationTypes("NotAnno")\fR
\fBpublic class AnnoProc extends AbstractProcessor {\fR
\fB  public boolean process(Set<? extends TypeElement> elems, RoundEnvironment renv){\fR
\fB     return true;\fR
\fB  }\fR
 
\fB  public SourceVersion getSupportedSourceVersion() {\fR
\fB     return SourceVersion\&.latest();\fR
\fB   }\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
\fBソース・ファイルAnnosWithoutProcessors\&.java\fR:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB@interface Anno { }\fR
\fB \fR
\fB@Anno\fR
\fBclass AnnosWithoutProcessors { }\fR
 
.fi
.if n \{\
.RE
.\}
次のコマンドは、注釈プロセッサ\fBAnnoProc\fRをコンパイルし、この注釈プロセッサをソース・ファイル\fBAnnosWithoutProcessors\&.java\fRに対して実行します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavac AnnoProc\&.java\fR
\fBjavac \-cp \&. \-Xlint:processing \-processor AnnoProc \-proc:only AnnosWithoutProcessors\&.java\fR
 
.fi
.if n \{\
.RE
.\}
コンパイラがソース・ファイル\fBAnnosWithoutProcessors\&.java\fRに対して注釈プロセッサを実行すると、次の警告が生成されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBwarning: [processing] No processor claimed any of these annotations: Anno\fR
\fB \fR
.fi
.if n \{\
.RE
.\}
この問題を解決するために、クラス\fBAnnosWithoutProcessors\fRで定義および使用される注釈の名前を、\fBAnno\fRから\fBNotAnno\fRに変更できます。
.RE
.PP
rawtypes
.RS 4
raw型に対する未検査操作について警告します。次の文では、\fBrawtypes\fR警告が生成されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBvoid countElements(List l) { \&.\&.\&. }\fR
 
.fi
.if n \{\
.RE
.\}
次の例では、\fBrawtypes\fR警告は生成されません。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBvoid countElements(List<?> l) { \&.\&.\&. }\fR
 
.fi
.if n \{\
.RE
.\}
\fBList\fRはraw型です。ただし、\fBList<?>\fRは、アンバウンド形式のワイルドカードのパラメータ化された型です。\fBList\fRはパラメータ化されたインタフェースであるため、常にその型引数を指定します。この例では、\fBList\fRの仮引数はアンバウンド形式のワイルドカード(\fB?\fR)を使用してその仮型パラメータとして指定されます。つまり、\fBcountElements\fRメソッドは\fBList\fRインタフェースのどのインスタンス化も受け付けることができます。
.RE
.PP
Serial
.RS 4
直列化可能クラスに\fBserialVersionUID\fR定義がないことを警告します。たとえば、次のようになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpublic class PersistentTime implements Serializable\fR
\fB{\fR
\fB  private Date time;\fR
\fB \fR
\fB   public PersistentTime() {\fR
\fB     time = Calendar\&.getInstance()\&.getTime();\fR
\fB   }\fR
\fB \fR
\fB   public Date getTime() {\fR
\fB     return time;\fR
\fB   }\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
コンパイラは次の警告を生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBwarning: [serial] serializable class PersistentTime has no definition of\fR
\fBserialVersionUID\fR
 
.fi
.if n \{\
.RE
.\}
直列化可能クラスが\fBserialVersionUID\fRという名前のフィールドを明示的に宣言しない場合、直列化ランタイム環境では、「Javaオブジェクト直列化仕様」で説明されているように、クラスの様々な側面に基づいて、クラスの\fBserialVersionUID\fRのデフォルト値を計算します。ただし、すべての直列化可能クラスが\fBserialVersionUID\fR値を明示的に宣言することを強くお薦めします。 これは、\fBserialVersionUID\fR値を計算するデフォルトのプロセスが、コンパイラの実装によって異なる可能性のあるクラスの詳細にきわめて影響を受けやすく、その結果、直列化復元中に予期しない\fBInvalidClassExceptions\fRが発生する可能性があるためです。Javaコンパイラの実装が異なっても\fBserialVersionUID\fR値の一貫性を確保にするには、直列化可能クラスが\fBserialVersionUID\fR値を明示的に宣言する必要があります。
.RE
.PP
static
.RS 4
staticの使用に関する問題について警告します。たとえば、次のようになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBclass XLintStatic {\fR
\fB    static void m1() { }\fR
\fB    void m2() { this\&.m1(); }\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
コンパイラは次の警告を生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBwarning: [static] static method should be qualified by type name, \fR
\fBXLintStatic, instead of by an expression\fR
 
.fi
.if n \{\
.RE
.\}
この問題を解決するために、次のように\fBstatic\fRメソッド\fBm1\fRを呼び出すことができます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBXLintStatic\&.m1();\fR
 
.fi
.if n \{\
.RE
.\}
あるいは、\fBstatic\fRキーワードをメソッド\fBm1\fRの宣言から削除することもできます。
.RE
.PP
try
.RS 4
try\-with\-resources文を含む、\fBtry\fRブロックの使用に関する問題について警告します。たとえば、\fBtry\fRブロックで宣言されたリソース\fBac\fRが使用されないために、次の文に対して警告が生成されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBtry ( AutoCloseable ac = getResource() ) {    // do nothing}\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
unchecked
.RS 4
Java言語仕様で指定されている未検査変換警告の詳細を示します。たとえば、次のようになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBList l = new ArrayList<Number>();\fR
\fBList<String> ls = l;       // unchecked warning\fR
 
.fi
.if n \{\
.RE
.\}
型の削除中、型\fBArrayList<Number>\fRおよび\fBList<String>\fRは、それぞれ\fBArrayList\fRおよび\fBList\fRになります。
.sp
\fBls\fRコマンドには、パラメータ化された型\fBList<String>\fRが指定されています。\fBl\fRによって参照される\fBList\fRが\fBls\fRに割り当てられた場合、コンパイラは未検査警告を生成します。コンパイル時に、コンパイラおよびJVMは、\fBl\fRが\fBList<String>\fR型を参照するかどうかを判別できません。この場合、\fBl\fRは、\fBList<String>\fR型を参照しません。その結果、ヒープ汚染が発生します。
.sp
ヒープ汚染状態が発生するのは、\fBList\fRオブジェクト\fBl\fR
(そのstatic型は\fBList<Number>\fR)が別の\fBList\fRオブジェクト\fBls\fR
(異なるstatic型\fBList<String>\fRを持つ)に代入される場合です。しかし、コンパイラではこの代入をいまだに許可しています。総称をサポートしないJava SEのリリースとの後方互換性を確保するために、この代入を許可する必要があります。型の削除のため、\fBList<Number>\fRおよび\fBList<String>\fRは、両方\fBList\fRになります。その結果、コンパイラはオブジェクト\fBl\fR
(\fBList\fRというraw型を持つ)をオブジェクト\fBls\fRに代入することを許可します。
.RE
.PP
varargs
.RS 4
可変引数(\fBvarargs\fR)メソッド、特に非具象化可能引数を含むものの使用が安全でないことを警告します。たとえば、次のようになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpublic class ArrayBuilder {\fR
\fB  public static <T> void addToList (List<T> listArg, T\&.\&.\&. elements) {\fR
\fB    for (T x : elements) {\fR
\fB      listArg\&.add(x);\fR
\fB    }\fR
\fB  }\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
\fB注意:\fR
非具象化可能型は、型情報が実行時に完全に使用不可能な型です。
.sp
コンパイラは、メソッド\fBArrayBuilder\&.addToList\fRの定義に関する次の警告を生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBwarning: [varargs] Possible heap pollution from parameterized vararg type T\fR
 
.fi
.if n \{\
.RE
.\}
コンパイラは、varargsメソッドを検出すると、\fBvarargs\fRの仮パラメータを配列に変換します。しかし、Javaプログラミング言語では、パラメータ化された型の配列の作成を許可していません。メソッド\fBArrayBuilder\&.addToList\fRでは、コンパイラは\fBvarargs\fRの仮パラメータ\fBT\&.\&.\&.\fR要素を仮パラメータ\fBT[]\fR要素(配列)に変換します。しかし、型消去により、コンパイラは\fBvarargs\fRの仮パラメータを\fBObject[]\fR要素に変換します。その結果、ヒープ汚染が発生する可能性があります。
.RE
.SH "コマンド行引数ファイル"
.PP
\fBjavac\fRコマンドを短くしたり簡潔にしたりするために、\fBjavac\fRコマンドに対する引数(\fB\-J\fRオプションを除く)を含む1つ以上のファイルを指定することができます。これにより、どのオペレーティング・システム上でも、任意の長さの\fBjavac\fRコマンドを作成できます。
.PP
引数ファイルには、\fBjavac\fRのオプションとソース・ファイル名を自由に組み合せて記述できます。ファイル内の引数は、空白または改行文字で区切ることができます。ファイル名に埋め込まれた空白がある場合、ファイル名全体を二重引用符で囲みます。
.PP
引数ファイル内のファイル名は、引数ファイルの位置ではなく、現在のディレクトリに相対的となります。これらのリストでは、ワイルドカード(*)は使用できません(たとえば、\fB*\&.java\fRとは指定できません)。アットマーク(@)を使用したファイルの再帰的な解釈はサポートされていません。また、\fB\-J\fRオプションもサポートされていません。このオプションは起動ツールに渡されますが、起動ツールでは引数ファイルをサポートしていないからです。
.PP
\fBjavac\fRコマンドを実行するときに、各引数ファイルのパスと名前の先頭にアットマーク(@)文字を付けて渡します。\fBjavac\fRコマンドは、アットマーク(@)で始まる引数を見つけると、そのファイルの内容を展開して引数リストに挿入します。
.PP
\fB例 1 \fR単一の引数ファイル
.RS 4
\fBargfile\fRという名前の単一の引数ファイルを使用して、すべての\fBjavac\fR引数を格納する場合は、次のように指定します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavac @argfile\fR
 
.fi
.if n \{\
.RE
.\}
この引数ファイルには、例2で示されている両方のファイルの内容を入れることができます。
.RE
.PP
\fB例 2 \fR2つの引数ファイル
.RS 4
\fBjavac\fRオプション用とソース・ファイル名用に、2つの引数ファイルを作成できます。次のリストには、行の継続文字はありません。
.sp
次を含むoptionsという名前のファイルを作成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-d classes\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-g\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-sourcepath /java/pubs/ws/1\&.3/src/share/classes\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
 
.fi
.if n \{\
.RE
.\}
次を含むclassesという名前のファイルを作成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBMyClass1\&.java\fR
\fBMyClass2\&.java\fR
\fBMyClass3\&.java\fR
 
.fi
.if n \{\
.RE
.\}
それから、次のように\fBjavac\fRコマンドを実行します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavac @options @classes\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fB例 3 \fRパスを使用した引数ファイル
.RS 4
引数ファイルはパスを指定できますが、ファイル内のすべてのファイル名は、(\fBpath1\fRや\fBpath2\fRではなく)次のように現在の作業ディレクトリに相対的となります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavac @path1/options @path2/classes\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.SH "注釈処理"
.PP
\fBjavac\fRコマンドが注釈処理を直接サポートしているため、独立した注釈処理コマンドである\fBapt\fRを使用する必要がなくなりました。
.PP
注釈プロセッサのAPIは、\fBjavax\&.annotation\&.processing\fRおよび\fBjavax\&.lang\&.model\fRパッケージとそのサブパッケージ内に定義されています。
.SS "注釈処理を行う方法"
.PP
\fB\-proc:none\fRオプションによって注釈処理が無効化されない限り、コンパイラは使用可能なすべての注釈プロセッサを検索します。検索パスは、\fB\-processorpath\fRオプションを使用して指定できます。パスを指定しない場合、ユーザー・クラス・パスが使用されます。プロセッサの検索は、検索パス上の\fBMETA\-INF/services/javax\&.annotation\&.processing\fR\&.Processorという名前のサービス・プロバイダ構成ファイルに基づいて行われます。このようなファイルには、使用するすべての注釈プロセッサの名前を、1行に1つずつ含めてください。また、別の方法として、\fB\-processor\fRオプションを使用してプロセッサを明示的に指定することもできます。
.PP
コンパイラは、コマンドラインのソース・ファイルやクラスを走査することで、どのような注釈が存在しているかを確認し終わると、プロセッサに対して問合せを行い、それらのプロセッサがどの注釈を処理できるのかを確認します。一致するものが見つかった場合、そのプロセッサが呼び出されます。各プロセッサは、自身が処理する注釈を要求できます。その場合、それらの注釈に対する別のプロセッサを見つける試みは行われません。すべての注釈が要求されると、コンパイラはそれ以上プロセッサの検索を行いません。
.PP
いずれかのプロセッサによって新しいソース・ファイルが生成されると、注釈処理の2回目のラウンドが開始されます。新しく生成されたすべてのソース・ファイルがスキャンされ、前回と同様に注釈が処理されます。以前のラウンドで呼び出されたプロセッサはすべて、後続のどのラウンドでも呼び出されます。これが、新しいソース・ファイルが生成されなくなるまで続きます。
.PP
あるラウンドで新しいソース・ファイルが生成されなかった場合、注釈プロセッサがあと1回のみ呼び出され、残りの処理を実行する機会が与えられます。最後に、\fB\-proc:only\fRオプションが使用されないかぎり、コンパイラは、元のソース・ファイルと生成されたすべてのソース・ファイルをコンパイルします。
.SS "暗黙的にロードされたソース・ファイル"
.PP
コンパイラは、一連のソース・ファイルをコンパイルする際に、別のソース・ファイルを暗黙的にロードすることが必要な場合があります。型の検索を参照してください。そのようなファイルは、現時点では注釈処理の対象になりません。デフォルトでは、注釈処理が実行され、かつ暗黙的にロードされた任意のソース・ファイルがコンパイルされた場合、コンパイラは警告を発行します。\fB\-implicit\fRオプションでは、警告を抑制する方法が提供されます。
.SH "型の検索"
.PP
ソース・ファイルをコンパイルするために、コンパイラは通常、型に関する情報を必要としますが、その型の定義はコマンド行で指定したソース・ファイルにありません。コンパイラは、ソース・ファイルで使用されているクラスまたはインタフェース、拡張されているクラスまたはインタフェース、あるいは実装されているクラスまたはインタフェースすべてについて、型の情報を必要とします。これには、ソース・ファイルで明示的には言及されていなくても、継承を通じて情報を提供するクラスとインタフェースも含まれます。
.PP
たとえば、サブクラス\fBjava\&.applet\&.Applet\fRを作成すると、\fBアプレット\fRの祖先のクラス(\fBjava\&.awt\&.Panel\fR、\fBjava\&.awt\&.Container\fR、\fBjava\&.awt\&.Component\fRおよび\fBjava\&.lang\&.Object\fR)を使用していることにもなります。
.PP
コンパイラは、型の情報が必要になると、その型を定義しているソース・ファイルまたはクラス・ファイルを検索します。まず、ブートストラップ・クラスと拡張機能クラスを検索し、続いてユーザー・クラス・パス(デフォルトでは現在のディレクトリ)を検索します。ユーザー・クラス・パスは、\fBCLASSPATH\fR環境変数を設定して定義するか、または\fB\-classpath\fRオプションを使用して定義します。
.PP
\fB\-sourcepath\fRオプションが設定されている場合、コンパイラは、指定されたパスからソース・ファイルを検索します。それ以外の場合、コンパイラは、ユーザー・クラス・パスからクラス・ファイルとソース・ファイルの両方を検索します。
.PP
\fB\-bootclasspath\fRオプションおよび\fB\-extdirs\fRオプションを使用して、別のブートストラップ・クラスや拡張機能クラスを指定できます。クロスコンパイル・オプションを参照してください。
.PP
型の検索に成功したときに得られる結果は、クラス・ファイル、ソース・ファイル、またはその両方である場合があります。両方が見つかった場合、そのどちらを使用するかを\fB\-Xprefer\fRオプションでコンパイラに指示できます。\fBnewer\fRが指定された場合、コンパイラは2つのファイルのうち新しい方を使用します。\fBsource\fRが指定された場合、コンパイラはソース・ファイルを使用します。デフォルトは\fBnewer\fRです。
.PP
型の検索自体によって、または\fB\-Xprefer\fRオプションが設定された結果として必要な型のソース・ファイルが見つかった場合、コンパイラはそのソース・ファイルを読み取り、必要な情報を取得します。デフォルトでは、コンパイラはソース・ファイルのコンパイルも行います。\fB\-implicit\fRオプションを使用してその動作を指定できます。\fBnone\fRが指定された場合、ソース・ファイルに対してクラス・ファイルは生成されません。\fBclass\fRが指定された場合、ソース・ファイルに対してクラス・ファイルが生成されます。
.PP
コンパイラは、注釈処理が完了するまで、ある型情報の必要性を認識しない場合があります。型情報がソース・ファイル内に見つかり、かつ\fB\-implicit\fRオプションが指定されていない場合、コンパイラによって、そのファイルが注釈処理の対象とならずにコンパイルされることが警告されます。この警告を無効にするには、(そのファイルが注釈処理の対象となるように)そのファイルをコマンド行に指定するか、あるいはそのようなソース・ファイルに対してクラス・ファイルを生成する必要があるかどうかを\fB\-implicit\fRオプションを使用して指定します。
.SH "プログラマティック・インタフェース"
.PP
\fBjavac\fRコマンドは、\fBjavax\&.tools\fRパッケージ内のクラスとインタフェースによって定義される新しいJava Compiler APIをサポートします。
.SS "例"
.PP
コマンドライン引数を指定するようにコンパイルするには、次の構文を使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBJavaCompiler javac = ToolProvider\&.getSystemJavaCompiler();\fR
 
.fi
.if n \{\
.RE
.\}
.PP
例では、診断を標準出力ストリームに書き込み、コマンド行からの呼出し時に\fBjavac\fRが指定する終了コードを返します。
.PP
\fBjavax\&.tools\&.JavaCompiler\fRインタフェースの他のメソッドを使用すると、診断の処理やファイルの読取り元/書込み先の制御などを行うことができます。
.SS "旧式のインタフェース"
.PP
\fB注意:\fR
このAPIは、後方互換性のためにのみ保持されています。すべての新しいコードは、新しいJava Compiler APIを使用する必要があります。
.PP
次のように、\fBcom\&.sun\&.tools\&.javac\&.Main\fRクラスには、プログラムからコンパイラを呼び出すためのstaticメソッドが2つ用意されています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpublic static int compile(String[] args);\fR
\fBpublic static int compile(String[] args, PrintWriter out);\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBargs\fRパラメータは、通常コンパイラに渡される任意のコマンド行引数を表しています。
.PP
\fBout\fRパラメータは、コンパイラの診断出力の宛先を示します。
.PP
\fBreturn\fR値は、\fBjavac\fRの\fBexit\fR値と同じです。
.PP
\fB注意:\fR
名前が\fBcom\&.sun\&.tools\&.javac\fRで始まるパッケージ(\fBcom\&.sun\&.tools\&.javac\fRのサブパッケージ)で検出される他のすべてのクラスおよびメソッドは、完全に内部用であり、いつでも変更される可能性があります。
.SH "例"
.PP
\fB例 1 \fR簡単なプログラムのコンパイル
.RS 4
この例では、greetingsディレクトリで\fBHello\&.java\fRソース・ファイルをコンパイルする方法を示しています。\fBHello\&.java\fRで定義されたクラスは、\fBgreetings\&.Hello\fRと呼ばれます。greetingsディレクトリは、ソース・ファイルとクラス・ファイルの両方があるパッケージ・ディレクトリで、現在のディレクトリのすぐ下にあります。これにより、デフォルトのユーザー・クラス・パスを使用できるようになります。また、\fB\-d\fRオプションを使用して別の出力先ディレクトリを指定する必要もありません。
.sp
\fBHello\&.java\fR内のソース・コードは次のとおりです。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpackage greetings;\fR
\fB \fR
\fBpublic class Hello {\fR
\fB    public static void main(String[] args) {\fR
\fB        for (int i=0; i < args\&.length; i++) {\fR
\fB            System\&.out\&.println("Hello " + args[i]);\fR
\fB        }\fR
\fB    }\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
greetings\&.Helloのコンパイル:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavac greetings/Hello\&.java\fR
 
.fi
.if n \{\
.RE
.\}
\fBgreetings\&.Hello\fRの実行:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava greetings\&.Hello World Universe Everyone\fR
\fBHello World\fR
\fBHello Universe\fR
\fBHello Everyone\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fB例 2 \fR複数のソース・ファイルのコンパイル
.RS 4
この例では、\fBgreetings\fRパッケージのソース・ファイル\fBAloha\&.java\fR、\fBGutenTag\&.java\fR、\fBHello\&.java\fRおよび\fBHi\&.java\fRをコンパイルします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB% javac greetings/*\&.java\fR
\fB% ls greetings\fR
\fBAloha\&.class         GutenTag\&.class      Hello\&.class         Hi\&.class\fR
\fBAloha\&.java          GutenTag\&.java       Hello\&.java          Hi\&.java\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fB例 3 \fRユーザー・クラス・パスの指定
.RS 4
前述の例のソース・ファイルのうち1つを変更した後に、そのファイルを再コンパイルします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpwd\fR
\fB/examples\fR
\fBjavac greetings/Hi\&.java\fR
 
.fi
.if n \{\
.RE
.\}
\fBgreetings\&.Hi\fRは\fBgreetings\fRパッケージ内の他のクラスを参照しているため、コンパイラはこれらの他のクラスを探す必要があります。デフォルトのユーザー・クラス・パスはパッケージ・ディレクトリを含むディレクトリであるため、前述の例は動作します。現在のディレクトリを気にせずにこのファイルを再コンパイルする場合、\fBCLASSPATH\fRを設定して、ユーザー・クラス・パスに例のディレクトリを追加します。この例では、\fB\-classpath\fRオプションを使用しています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavac \-classpath /examples /examples/greetings/Hi\&.java\fR
 
.fi
.if n \{\
.RE
.\}
\fBgreetings\&.Hi\fRを変更してバナー・ユーティリティを使用するようにした場合、 そのユーティリティもユーザー・クラス・パスを通じてアクセスできるようになっている必要があります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavac \-classpath /examples:/lib/Banners\&.jar \e\fR
\fB            /examples/greetings/Hi\&.java\fR
 
.fi
.if n \{\
.RE
.\}
\fBgreetings\fRパッケージでクラスを実行するには、プログラムは\fBgreetings\fRパッケージ、および\fBgreetings\fRクラスが使用するクラスにアクセスする必要があります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava \-classpath /examples:/lib/Banners\&.jar greetings\&.Hi\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\fB例 4 \fRソース・ファイルとクラス・ファイルの分離
.RS 4
次の例では、\fBjavac\fRを使用して、JVM 1\&.7上で実行するコードをコンパイルします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavac \-source 1\&.7 \-target 1\&.7 \-bootclasspath jdk1\&.7\&.0/lib/rt\&.jar \e \fR
\fB\-extdirs "" OldCode\&.java\fR
 
.fi
.if n \{\
.RE
.\}
\fB\-source 1\&.7\fRオプションにより、\fBOldCode\&.java\fRのコンパイルにはリリース1\&.7(または7)のJavaプログラミング言語が使用されます。\fB\-target 1\&.7\fRオプションにより、JVM 1\&.7と互換性のあるクラス・ファイルが生成されます。ほとんどの場合、\fB\-target\fRオプションの値は\fB\-source\fRオプションの値になります。この例では、\fB\-target\fRオプションを省略できます。
.sp
\fB\-bootclasspath\fRオプションを使用して、適切なバージョンのブートストラップ・クラス(\fBrt\&.jar\fRライブラリ)を指定する必要があります。指定しない場合は、コンパイラによって次の警告が生成されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavac \-source 1\&.7 OldCode\&.java\fR
\fBwarning: [options] bootstrap class path not set in conjunction with \fR
\fB\-source 1\&.7\fR
 
.fi
.if n \{\
.RE
.\}
適切なバージョンのブートストラップ・クラスを指定しない場合、コンパイラは古い言語仕様(この例では、バージョン1\&.7のJavaプログラミング言語)を新しいブートストラップ・クラスと組み合せて使用します。その結果、存在しないメソッドへの参照が含まれていることがあるため、クラス・ファイルが古いプラットフォーム(この場合はJava SE 7)で動作しない可能性があります。
.RE
.PP
\fB例 5 \fRクロス・コンパイル
.RS 4
この例では、\fBjavac\fRを使用して、JVM 1\&.7上で実行するコードをコンパイルします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavac \-source 1\&.7 \-target 1\&.7 \-bootclasspath jdk1\&.7\&.0/lib/rt\&.jar \e\fR
\fB            \-extdirs "" OldCode\&.java\fR
 
.fi
.if n \{\
.RE
.\}
\fB \-source 1\&.7\fRオプションにより、OldCode\&.javaのコンパイルにはリリース1\&.7(または7)のJavaプログラミング言語が使用されます。\fB\-target 1\&.7\fRオプションにより、JVM 1\&.7と互換性のあるクラス・ファイルが生成されます。
.sp
\fB\-bootclasspath\fRオプションを使用して、適切なバージョンのブートストラップ・クラス(\fBrt\&.jar\fRライブラリ)を指定する必要があります。指定しない場合は、コンパイラによって次の警告が生成されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavac \-source 1\&.7 OldCode\&.java\fR
\fBwarning: [options] bootstrap class path not set in conjunction with \-source 1\&.7\fR
 
.fi
.if n \{\
.RE
.\}
適切なバージョンのブートストラップ・クラスを指定しない場合、コンパイラは古い言語仕様を新しいブートストラップ・クラスと組み合せて使用します。この組合せは、存在しないメソッドへの参照が含まれていることがあるため、クラス・ファイルが古いプラットフォーム(この場合はJava SE 7)で動作しない可能性があります。この例では、コンパイラはJavaプログラミング言語のリリース1\&.7を使用します。
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
java(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jdb(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javah(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javadoc(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jar(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jdb(1)
.RE
.br
'pl 8.5i
'bp
