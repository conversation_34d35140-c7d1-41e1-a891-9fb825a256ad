'\" t
.\" Copyright (c) 1994, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: javah
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: 基本ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "javah" "1" "2013年11月21日" "JDK 8" "基本ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
javah \- JavaクラスからCヘッダーとソース・ファイルを生成します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavah\fR [ \fIoptions\fR ] f\fIully\-qualified\-class\-name \&.\&.\&.\fR
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIfully\-qualified\-class\-name\fR
.RS 4
Cヘッダーとソース・ファイルに変換されるクラスの完全修飾された場所。
.RE
.SH "説明"
.PP
\fBjavah\fRコマンドは、ネイティブ・メソッドを実装するために必要なCヘッダーとソース・ファイルを生成します。作成されたヘッダーとソース・ファイルは、ネイティブ・ソース・コードからオブジェクトのインスタンス変数を参照するためにCプログラムによって使用されます。\fB\&.h\fRファイルは、対応するクラスと一致する配置を持つ\fBstruct\fR定義を含みます。\fBstruct\fRのフィールドは、クラスのインスタンス変数に対応します。
.PP
ヘッダー・ファイルとその中で宣言される構造体の名前はクラスの名前から派生します。\fBjavah\fRコマンドに渡されるクラスがパッケージの中にある場合、パッケージ名はヘッダー・ファイル名と構造体名の両方の先頭に付加されます。下線(_)が名前の区切り文字として使用されます。
.PP
デフォルトでは\fBjavah\fRコマンドは、コマンド行にリストされる各クラスのヘッダー・ファイルを作成し、現在のディレクトリにファイルを置きます。ソース・ファイルを作成するには、\fB\-stubs\fRオプションを使用してください。1つのファイルの中に、リストされたすべてのクラスの結果を連結するには、\fB\-o\fRオプションを使用してください。
.PP
Java Native Interface (JNI)はヘッダー情報またはスタブ・ファイルを必要としません。\fBjavah\fRコマンドは引き続きJNI形式のネイティブ・メソッドに必要なネイティブ・メソッド関数プロトタイプの生成に使用できます。\fBjavah\fRコマンドはデフォルトでJNI形式の出力を生成し、その結果を\fB\&.h\fRファイルに格納します。
.SH "オプション"
.PP
\-o \fIoutputfile\fR
.RS 4
コマンドラインにリストされたすべてのクラスに対して、結果のヘッダーまたはソース・ファイルを連結して出力ファイルに格納します。\fB\-o\fRまたは\fB\-d\fRのどちらかのみ使用できます。
.RE
.PP
\-d \fIdirectory\fR
.RS 4
\fBjavah\fRがヘッダー・ファイルまたはスタブ・ファイルを保存する、ディレクトリを設定します。\fB\-d\fRまたは\fB\-o\fRのどちらかのみ使用できます。
.RE
.PP
\-stubs
.RS 4
\fBjavah\fRコマンドが、Javaオブジェクト・ファイルからC宣言を生成します。
.RE
.PP
\-verbose
.RS 4
詳細出力を指定し、作成ファイルの状態に関するメッセージを、\fBjavah\fRコマンドが\fB標準出力\fRに出力します。
.RE
.PP
\-help
.RS 4
\fBjavah\fRの使用方法についてのヘルプ・メッセージを出力します。
.RE
.PP
\-version
.RS 4
\fBjavah\fRコマンドのリリース情報を出力します。
.RE
.PP
\-jni
.RS 4
JNI形式のネイティブ・メソッド機能プロトタイプを含む出力ファイルを、\fBjavah\fRコマンドが作成します。これは標準出力であるため、\fB\-jni\fRの使用はオプションです。
.RE
.PP
\-classpath \fIpath\fR
.RS 4
クラスを探すために\fBjavah\fRコマンドが使用するパスを指定します。デフォルトまたは\fBCLASSPATH\fR環境変数の設定をオーバーライドします。ディレクトリはOracle Solarisの場合はコロンで、Windowsの場合はセミコロンで区切られます。パスの一般的な形式は次のようになります。
.sp
\fBOracle Solaris\fRの場合:
.sp
\&.:\fIyour\-path\fR
.sp
例:
\fB\&.:/home/<USER>/classes:/usr/local/java/classes\fR
.sp
\fBWindows\fRの場合:
.sp
\&.;\fIyour\-path\fR
.sp
例:
\fB\&.;C:\eusers\edac\eclasses;C:\etools\ejava\eclasses\fR
.sp
便宜上、*のベース名を含むクラス・パス要素は、\fB\&.jar\fRまたは\fB\&.JAR\fRを拡張子に持つディレクトリ内のすべてのファイルのリストを指定するのと同等とみなされます。
.sp
たとえば、ディレクトリ\fBmydir\fRに\fBa\&.jar\fRと\fBb\&.JAR\fRが含まれている場合、クラス・パス要素\fBmydir/*\fRは\fBA\fR\fB\&.jar:b\&.JAR\fRに展開されますが、JARファイルの順番は未指定となります。このリストには、隠しファイルも含め、指定されたディレクトリ内のすべてのJARファイルが含まれます。*からなるクラス・パス・エントリは、現在のディレクトリ内のすべてのJARファイルのリストに展開されます。\fBCLASSPATH\fR環境変数も、定義時には同様に展開されます。クラス・パスのワイルドカードの展開は、Java Virtual Machine (JVM)の開始前に行われます。Javaプログラムは、環境を問い合せる場合を除き、展開されていないワイルドカードを参照しません。たとえば、\fBSystem\&.getenv("CLASSPATH")\fRをコールして問い合せる場合です。
.RE
.PP
\-bootclasspath \fIpath\fR
.RS 4
ブートストラップ・クラスをロードするパスを指定します。ブートストラップ・クラスは、デフォルトでは\fBjre\elib\ert\&.jar\fRおよび他のいくつかのJARファイルにある、コアJavaプラットフォームを実装するクラスです。
.RE
.PP
\-old
.RS 4
古いJDK 1\&.0形式のヘッダー・ファイルを生成するように指定します。
.RE
.PP
\-force
.RS 4
出力ファイルが常に書き込まれるように指定します。
.RE
.PP
\-J\fIoption\fR
.RS 4
Java Virtual Machineに\fBoption\fRを渡します。\fBoption\fRには、Javaアプリケーション起動ツールのリファレンス・ページに記載されているオプションを1つ指定します。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。java(1)を参照してください。
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javah(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
java(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jdb(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javap(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javadoc(1)
.RE
.br
'pl 8.5i
'bp
