'\" t
.\" Copyright (c) 2011, 2015, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: javapackager
.\" Language: Japanese
.\" Date: 2015年3月3日
.\" SectDesc: Javaデプロイメント・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "javapackager" "1" "2015年3月3日" "JDK 8" "Javaデプロイメント・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
javapackager \- JavaおよびJavaFXアプリケーションのパッケージ化と署名に関連するタスクを実行します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavapackager\fR \fIcommand\fR [\fIoptions\fR]
.fi
.if n \{\
.RE
.\}
.PP
\fIcommand\fR
.RS 4
実行する必要のあるタスク。
.RE
.PP
options
.RS 4
空白で区切られた1つ以上のコマンド・オプション。
.RE
.SH "コマンド"
.PP
次のいずれかのコマンドを指定できます。コマンドの後に、そのオプションを指定します。
.PP
\-createbss
.RS 4
CSSファイルをバイナリ形式に変換します。
.RE
.PP
\-createjar
.RS 4
他のパラメータに従ってJARアーカイブを作成します。
.RE
.PP
\-deploy
.RS 4
再配布用のアプリケーション・パッケージを組み立てます。デプロイ・タスクでは、デフォルトでベース・アプリケーション・パッケージが生成されますが、指定すれば自己完結型アプリケーション・パッケージも生成できます。
.RE
.PP
\-makeall
.RS 4
1つの呼出しで、ほとんどの引数が事前定義されたコンパイル、\fBcreatejar\fRおよび\fBdeploy\fRステップを実行し、すべての該当する自己完結型アプリケーション・パッケージの生成を試行します。ソース・ファイルは\fBsrc\fRというフォルダに配置する必要があり、生成されるファイル(JAR、JNLP、HTMLおよび自己完結型アプリケーション・パッケージ)は\fBdist\fRというフォルダに出力されます。このコマンドでは最小限の構成のみが可能で、可能な限り自動化されています。
.RE
.PP
\-signjar
.RS 4
指定した証明書でJARファイルに署名します。
.RE
.SH "CREATEBSSコマンドのオプション"
.PP
\-outdir \fIdir\fR
.RS 4
生成された出力ファイルを受け取るディレクトリの名前。
.RE
.PP
\-srcdir \fIdir\fR
.RS 4
パッケージ化するファイルのベース・ディレクトリ。
.RE
.PP
\-srcfiles \fIfiles\fR
.RS 4
\fB\-srcdir\fRオプションで指定されたディレクトリ内のファイルのリスト。省略すると、ディレクトリ(この場合は必須の引数)にあるすべてのファイルが使用されます。リスト内のファイルはスペースで区切る必要があります。
.RE
.SH "CREATEJARコマンドのオプション"
.PP
\-appclass \fIapp\-class\fR
.RS 4
実行するアプリケーション・クラスの修飾名。
.RE
.PP
\-argument \fIarg\fR
.RS 4
\fB<fx:argument>\fR要素としてJNLPファイルに挿入される名前なし引数。
.RE
.PP
\-classpath \fIfiles\fR
.RS 4
依存するJARファイル名のリスト。
.RE
.PP
\-manifestAttrs \fImanifest\-attributes\fR
.RS 4
追加のマニフェスト属性の名前と値のリスト。構文:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB"name1=value1,name2=value2,name3=value3"\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-nocss2bin
.RS 4
パッケージャはJARにコピーする前にCSSファイルをバイナリ形式に変換しません。
.RE
.PP
\-outdir \fIdir\fR
.RS 4
生成された出力ファイルを受け取るディレクトリの名前。
.RE
.PP
\-outfile \fIfilename\fR
.RS 4
生成されるファイルの名前(拡張子なし)。
.RE
.PP
\-paramfile \fIfile\fR
.RS 4
デフォルトの名前付きアプリケーション・パラメータが含まれるプロパティ・ファイル。
.RE
.PP
\-preloader \fIpreloader\-class\fR
.RS 4
実行するJavaFXプリローダー・クラスの修飾名。このオプションは、JavaFXアプリケーションにのみ使用します。ヘッドレス・アプリケーションを含むJavaアプリケーションには使用しないでください。
.RE
.PP
\-srcdir \fIdir\fR
.RS 4
パッケージ化するファイルのベース・ディレクトリ。
.RE
.PP
\-srcfiles \fIfiles\fR
.RS 4
\fB\-srcdir\fRオプションで指定されたディレクトリ内のファイルのリスト。省略すると、ディレクトリ(この場合は必須の引数)にあるすべてのファイルが使用されます。リスト内のファイルはスペースで区切る必要があります。
.RE
.SH "DEPLOYコマンドのオプション"
.PP
\-allpermissions
.RS 4
指定した場合、JNLPファイル内のすべてのセキュリティ権限がアプリケーションに必要になります。
.RE
.PP
\-appclass \fIapp\-class\fR
.RS 4
実行するアプリケーション・クラスの修飾名。
.RE
.PP
\-argument \fIarg\fR
.RS 4
JNLPファイルの\fB<fx:argument>\fR要素に挿入される名前なし引数。
.RE
.PP
\-B\fIbundler\-argument=value\fR
.RS 4
自己完結型アプリケーションのパッケージ化に使用するバンドラへの情報を指定します。各バンドラの引数の詳細は、自己完結型アプリケーション・バンドラの引数を参照してください。
.RE
.PP
\-callbacks
.RS 4
生成後のHTMLでのユーザー・コールバック方式を指定します。形式は次のとおりです。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB"name1:value1,name2:value2,\&.\&.\&."\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-description \fIdescription\fR
.RS 4
アプリケーションの説明。
.RE
.PP
\-embedCertificates
.RS 4
指定した場合、証明書がJNLPファイルに埋め込まれます。
.RE
.PP
\-embedjnlp
.RS 4
指定した場合、JNLPファイルがHTMLドキュメントに埋め込まれます。
.RE
.PP
\-height \fIheight\fR
.RS 4
アプリケーションの高さ。
.RE
.PP
\-htmlparamfile \fIfile\fR
.RS 4
生成されるアプリケーションをブラウザで実行する場合のパラメータが含まれるプロパティ・ファイル。
.RE
.PP
\-isExtension
.RS 4
指定した場合、\fBsrcfiles\fRは拡張ファイルとして処理されます。
.RE
.PP
\-name \fIname\fR
.RS 4
アプリケーションの名前。
.RE
.PP
\-native \fItype\fR
.RS 4
自己完結型アプリケーション・バンドルを生成します(可能な場合)。\fB\-B\fRオプションを使用して、使用されているバンドラへの引数を指定します。\fItype\fRを指定すると、このタイプのバンドルのみが作成されます。タイプを指定しない場合、\fBall\fRが使用されます。
.sp
\fItype\fRには、次の値が有効です。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBall\fR: 実行されているプラットフォームのインストーラをすべて実行し、そのアプリケーションのディスク・イメージを作成します。\fItype\fRが指定されていない場合、この値が使用されます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBinstaller\fR: 実行されているプラットフォームのインストーラをすべて実行します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBimage\fR: アプリケーションのディスク・イメージを作成します。OS Xでは、イメージは\fB\&.app\fRファイルです。Linuxでは、イメージはインストールされるディレクトリです。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBdmg\fR: OS X用のDMGファイルを生成します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBpkg\fR: OS X用の\fB\&.pkg\fRパッケージを生成します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBmac\&.appStore\fR: Mac App Store用のパッケージを生成します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBrpm\fR: Linux用のRPMパッケージを生成します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBdeb\fR: Linux用のDebianパッケージを生成します。
.RE
.RE
.PP
\-outdir \fIdir\fR
.RS 4
生成された出力ファイルを受け取るディレクトリの名前。
.RE
.PP
\-outfile \fIfilename\fR
.RS 4
生成されるファイルの名前(拡張子なし)。
.RE
.PP
\-paramfile \fIfile\fR
.RS 4
デフォルトの名前付きアプリケーション・パラメータが含まれるプロパティ・ファイル。
.RE
.PP
\-preloader \fIpreloader\-class\fR
.RS 4
実行するJavaFXプリローダー・クラスの修飾名。このオプションは、JavaFXアプリケーションにのみ使用します。ヘッドレス・アプリケーションを含むJavaアプリケーションには使用しないでください。
.RE
.PP
\-srcdir \fIdir\fR
.RS 4
パッケージ化するファイルのベース・ディレクトリ。
.RE
.PP
\-srcfiles \fIfiles\fR
.RS 4
\fB\-srcdir\fRオプションで指定されたディレクトリ内のファイルのリスト。省略すると、ディレクトリ(この場合は必須の引数)にあるすべてのファイルが使用されます。リスト内のファイルはスペースで区切る必要があります。
.RE
.PP
\-templateId
.RS 4
テンプレート処理を行うアプリケーションのアプリケーションID。
.RE
.PP
\-templateInFilename
.RS 4
HTMLテンプレート・ファイルの名前。プレースホルダの形式は次のとおりです。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB#XXXX\&.YYYY(APPID)#\fR
 
.fi
.if n \{\
.RE
.\}
APPIDはアプリケーションの識別子であり、XXXは次のいずれかです。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBDT\&.SCRIPT\&.URL\fR
.sp
Deployment Toolkitのdtjava\&.jsの場所。デフォルトでは、この場所は次のとおりです。
.sp
http://java\&.com/js/dtjava\&.js
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBDT\&.SCRIPT\&.CODE\fR
.sp
Deployment Toolkitのdtjava\&.jsを含めるスクリプト要素。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBDT\&.EMBED\&.CODE\&.DYNAMIC\fR
.sp
特定のプレースホルダにアプリケーションを埋め込むコード。コードは、\fBfunction()\fRメソッドにラップされることが予想されます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBDT\&.EMBED\&.CODE\&.ONLOAD\fR
.sp
\fBonload\fRフックを使用してWebページにアプリケーションを埋め込むために必要なすべてのコード(dtjava\&.jsのインクルードを除く)。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBDT\&.LAUNCH\&.CODE\fR
.sp
アプリケーションの起動に必要なコード。コードは、\fBfunction()\fRメソッドにラップされることが予想されます。
.RE
.RE
.PP
\-templateOutFilename
.RS 4
テンプレートから生成されるHTMLファイルの名前。
.RE
.PP
\-title \fItitle\fR
.RS 4
アプリケーションのタイトル。
.RE
.PP
\-vendor \fIvendor\fR
.RS 4
アプリケーションのベンダー。
.RE
.PP
\-width \fIwidth\fR
.RS 4
アプリケーションの幅。
.RE
.PP
\-updatemode \fIupdate\-mode\fR
.RS 4
JNLPファイルの更新モードを設定します。
.RE
.SH "MAKEALLコマンドのオプション"
.PP
\-appclass \fIapp\-class\fR
.RS 4
実行するアプリケーション・クラスの修飾名。
.RE
.PP
\-classpath \fIfiles\fR
.RS 4
依存するJARファイル名のリスト。
.RE
.PP
\-height \fIheight\fR
.RS 4
アプリケーションの高さ。
.RE
.PP
\-name \fIname\fR
.RS 4
アプリケーションの名前。
.RE
.PP
\-preloader \fIpreloader\-class\fR
.RS 4
実行するJavaFXプリローダー・クラスの修飾名。このオプションは、JavaFXアプリケーションにのみ使用します。ヘッドレス・アプリケーションを含むJavaアプリケーションには使用しないでください。
.RE
.PP
\-width \fIwidth\fR
.RS 4
アプリケーションの幅。
.RE
.SH "SIGNJARコマンドのオプション"
.PP
\-alias
.RS 4
キーの別名。
.RE
.PP
\-keyPass
.RS 4
キーを復元するためのパスワード。
.RE
.PP
\-keyStore \fIfile\fR
.RS 4
キーストア・ファイル名。
.RE
.PP
\-outdir \fIdir\fR
.RS 4
生成された出力ファイルを受け取るディレクトリの名前。
.RE
.PP
\-srcdir \fIdir\fR
.RS 4
署名するファイルのベース・ディレクトリ。
.RE
.PP
\-srcfiles \fIfiles\fR
.RS 4
\fB\-srcdir\fRオプションで指定されたディレクトリ内のファイルのリスト。省略すると、ディレクトリ(この場合は必須の引数)にあるすべてのファイルが使用されます。リスト内のファイルはスペースで区切る必要があります。
.RE
.PP
\-storePass
.RS 4
キーストアの整合性を確認したり、ロックを解除するためのパスワード。
.RE
.PP
\-storeType
.RS 4
キーストアのタイプ。デフォルト値は"jks"です。
.RE
.SH "自己完結型アプリケーション・バンドラの引数"
.PP
自己完結型アプリケーションの作成に使用されるバンドラの引数を指定するには、\fB\-deploy\fRコマンドの\fB\-B\fRオプションを使用します。各バンドラ・タイプに、独自の引数セットがあります。
.SS "一般的なバンドラ引数"
.PP
appVersion=\fIversion\fR
.RS 4
アプリケーション・パッケージのバージョン。一部のバンドラでは、バージョン文字列の形式が制限されます。
.RE
.PP
classPath=\fIpath\fR
.RS 4
アセンブルされたアプリケーション・ディレクトリへの相対的なクラス・パス。一般的に、パスはJARファイル・マニフェストから抽出され、他の\fBjavapackager\fRコマンドを使用している場合は設定する必要はありません。
.RE
.PP
icon=\fIpath\fR
.RS 4
起動ツールおよびその他の支援ツールに使用されるデフォルト・アイコンの場所。OS Xの場合、形式は\fB\&.icns\fRである必要があります。Linuxの場合、形式は\fB\&.png\fRである必要があります。
.RE
.PP
identifier=\fIvalue\fR
.RS 4
\fBmac\&.CFBundleIdentifier\fRなど、他のプラットフォーム固有の値に使用されるデフォルト値。DNS順序の反転をお薦めします(例:
\fBcom\&.example\&.application\&.my\-application\fR)。
.RE
.PP
jvmOptions=\fIoption\fR
.RS 4
アプリケーションの実行時にJVMに渡されるオプション。\fBjava\fRコマンドに有効なオプションを使用できます。複数のオプションを渡すには、次の例に示すように\fB\-B\fRオプションの複数インスタンスを使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-BjvmOptions=\-Xmx128m \-BjvmOptions=\-Xms128m\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
jvmProperties=\fIproperty\fR=\fIvalue\fR
.RS 4
アプリケーションの実行時にVMに渡されるJavaシステム・プロパティ。\fBjava\fRコマンドの\fB\-D\fRオプションに有効なプロパティを使用できます。プロパティ名とそのプロパティの値の両方を指定します。複数のプロパティを渡すには、次の例に示すように\fB\-B\fRオプションの複数インスタンスを使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-BjvmProperties=apiUserName=example \-BjvmProperties=apiKey=abcdef1234567890\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
mainJar=\fIfilename\fR
.RS 4
アプリケーションのメイン・クラスを含むJARファイルの名前。一般的に、ファイル名はJARファイル・マニフェストから抽出され、他の\fBjavapackager\fRコマンドを使用している場合は設定する必要はありません。
.RE
.PP
preferencesID=\fInode\fR
.RS 4
ユーザーがオーバーライドできるJVMオプションを確認するために調べるプリファレンス・ノード。指定したノードは、オプション\fB\-Dapp\&.preferences\&.id\fRとして実行時にアプリケーションに渡されます。この引数は、\fBuserJVMOptions\fR引数とともに使用されます。
.RE
.PP
runtime=\fIpath\fR
.RS 4
パッケージ・バンドルに含めるJREまたはJDKの場所。JDKまたはJREのルート・フォルダへのファイル・パスを指定します。システム・デフォルトJREを使用するには、次の例に示すようにパスを指定しないでください。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-Bruntime=\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
userJvmOptions=\fIoption\fR=\fIvalue\fR
.RS 4
ユーザーがオーバーライドできるJVMオプション。\fBjava\fRコマンドに有効なオプションを使用できます。オプション名とそのオプションの値の両方を指定します。複数のオプションを渡すには、次の例に示すように\fB\-B\fRオプションの複数インスタンスを使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-BuserJvmOptions=\-Xmx=128m \-BuserJvmOptions=\-Xms=128m\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.SS "OS Xアプリケーション・バンドラ引数"
.PP
mac\&.category=\fIcategory\fR
.RS 4
アプリケーションのカテゴリ。カテゴリは、Apple Developer Webサイトにあるカテゴリ・リスト内に含まれている必要があります。
.RE
.PP
mac\&.CFBundleIdentifier=\fIvalue\fR
.RS 4
\fBCFBundleIdentifier\fRの情報plistに格納されている値。この値は、グローバルに一意である必要があり、文字、数字、ドットおよびダッシュのみを含む必要があります。DNS順序の反転をお薦めします(例:
\fBcom\&.example\&.application\&.my\-application\fR)。
.RE
.PP
mac\&.CFBundleName=\fIname\fR
.RS 4
OS Xメニュー・バーに表示されるアプリケーションの名前。16文字未満の名前をお薦めします。デフォルトは名前属性です。
.RE
.PP
mac\&.CFBundleVersion=\fIvalue\fR
.RS 4
アプリケーションのバージョン番号は内部的に使用されます。値は1つ以上3つ以下の整数である必要があり、1\&.3や2\&.0\&.1のようにピリオド(\&.)で区切ります。値は、\fBappVersion\fR引数の値と異なる可能性があります。\fBappVersion\fR引数が有効な値で指定され、\fBmac\&.CFBundleVersion\fR引数が指定されていない場合、\fBappVersion\fR値が使用されます。どちらの引数も指定されていない場合、\fB100\fRがバージョン番号として使用されます。
.RE
.PP
mac\&.signing\-key\-developer\-id\-app=\fIkey\fR
.RS 4
開発者IDまたはGatekeeper署名に使用する署名キーの名前。Apple Developer Webサイトから標準のキーをインポートした場合、そのキーがデフォルトで使用されます。キーを識別できない場合、アプリケーションは署名されません。
.RE
.PP
mac\&.bundle\-id\-signing\-prefix=\fIprefix\fR
.RS 4
plistまたは既存の署名がないバイナリがバンドル内に見つかった場合に署名済バイナリに適用される接頭辞。
.RE
.SS "OS X DMG (ディスク・イメージ)バンドラ引数"
.PP
ディスク・イメージがマウント可能になる前に、OS X DMGインストーラは、\fBlicenseFile\fRによって指定されているライセンス・ファイル(指定されている場合)を表示します。
.PP
licenseFile=\fIpath\fR
.RS 4
バンドラによって表示または記録される使用許諾契約(EULA)の場所。パスは、パッケージ・アプリケーション・リソースに相対的です(例:
\fB\-BlicenseFile=COPYING\fR)。
.RE
.PP
systemWide=\fIboolean\fR
.RS 4
使用対象のドラッグしてインストールするターゲットを示すフラグ。アプリケーション・フォルダを表示するには、\fBtrue\fRに設定します。デスクトップ・フォルダを表示するには、\fBfalse\fRに設定します。デフォルトは\fBtrue\fRです。
.RE
.PP
mac\&.CFBundleVersion=\fIvalue\fR
.RS 4
アプリケーションのバージョン番号は内部的に使用されます。値は1つ以上3つ以下の整数である必要があり、1\&.3や2\&.0\&.1のようにピリオド(\&.)で区切ります。値は、\fBappVersion\fR引数の値と異なる可能性があります。\fBappVersion\fR引数が有効な値で指定され、\fBmac\&.CFBundleVersion\fR引数が指定されていない場合、\fBappVersion\fR値が使用されます。どちらの引数も指定されていない場合、\fB100\fRがバージョン番号として使用されます。
.RE
.PP
mac\&.dmg\&.simple=\fIboolean\fR
.RS 4
AppleScriptコードの実行に依存するDMGカスタマイズ手順をスキップするかどうかを示すフラグ。\fBtrue\fRに設定して、手順をスキップします。\fBtrue\fRに設定されている場合、ディスク・ウィンドウにバックグラウンド・イメージがないため、アイコンが所定の位置に移動しません。\fBsystemWide\fR引数も\fBtrue\fRに設定されている場合、ルート・アプリケーション・フォルダのシンボリック・リンクがDMGファイルに追加されます。\fBsystemWide\fR引数が\fBfalse\fRに設定されている場合、アプリケーションのみがDMGファイルに追加され、デスクトップのリンクは追加されません。
.RE
.SS "OS X PKGバンドラ引数"
.PP
OS X PKGインストーラは、ウィザードを示し、\fBlicenseFile\fRによって指定されているライセンス・ファイルをウィザード内のページに表示します。ユーザーはアプリケーションをインストールする前に条件に同意する必要があります。
.PP
licenseFile=\fIpath\fR
.RS 4
バンドラによって表示または記録される使用許諾契約(EULA)の場所。パスは、パッケージ・アプリケーション・リソースに相対的です(例:
\fB\-BlicenseFile=COPYING\fR)。
.RE
.PP
mac\&.signing\-key\-developer\-id\-installer=\fIkey\fR
.RS 4
開発者IDまたはGatekeeper署名に使用する署名キーの名前。Apple Developer Webサイトから標準のキーをインポートした場合、そのキーがデフォルトで使用されます。キーを識別できない場合、アプリケーションは署名されません。
.RE
.PP
mac\&.CFBundleVersion=\fIvalue\fR
.RS 4
アプリケーションのバージョン番号は内部的に使用されます。値は1つ以上3つ以下の整数である必要があり、1\&.3や2\&.0\&.1のようにピリオド(\&.)で区切ります。値は、\fBappVersion\fR引数の値と異なる可能性があります。\fBappVersion\fR引数が有効な値で指定され、\fBmac\&.CFBundleVersion\fR引数が指定されていない場合、\fBappVersion\fR値が使用されます。どちらの引数も指定されていない場合、\fB100\fRがバージョン番号として使用されます。
.RE
.SS "Mac App Storeバンドラ引数"
.PP
mac\&.app\-store\-entitlements=\fIpath\fR
.RS 4
アプリケーションが動作する資格を含むファイルの場所。ファイルは、Appleによって指定されている形式である必要があります。ファイルへのパスは、絶対条件で指定することも、\fBjavapackager\fRの呼出しに対して相対的に指定することもできます。資格を指定しない場合、アプリケーションは、通常のアプレット・サンドボックスよりもより厳しいサンドボックスで動作し、ネットワーク・ソケットおよびすべてのファイルへのアクセスが拒否されます。
.RE
.PP
mac\&.signing\-key\-app=\fIkey\fR
.RS 4
Mac App Storeのアプリケーション署名キーの名前。Apple Developer Webサイトから標準のキーをインポートした場合、そのキーがデフォルトで使用されます。キーを識別できない場合、アプリケーションは署名されません。
.RE
.PP
mac\&.signing\-key\-pkg=\fIkey\fR
.RS 4
Mac App Storeのインストーラ署名キーの名前。Apple Developer Webサイトから標準のキーをインポートした場合、そのキーがデフォルトで使用されます。キーを識別できない場合、アプリケーションは署名されません。
.RE
.PP
mac\&.CFBundleVersion=\fIvalue\fR
.RS 4
アプリケーションのバージョン番号は内部的に使用されます。値は1つ以上3つ以下の整数である必要があり、1\&.3や2\&.0\&.1のようにピリオド(\&.)で区切ります。値は、\fBappVersion\fR引数の値と異なる可能性があります。\fBappVersion\fR引数が有効な値で指定され、\fBmac\&.CFBundleVersion\fR引数が指定されていない場合、\fBappVersion\fR値が使用されます。どちらの引数も指定されていない場合、\fB100\fRがバージョン番号として使用されます。このバージョンが既存のアプリケーションのアップグレードである場合、値は以前のバージョン番号より大きくする必要があります。
.RE
.SS "Linux Debianバンドラ引数"
.PP
\fBlicenseFile\fRによって指定されているライセンス・ファイルは、すべてのケースにおいてユーザーに示されませんが、ファイルはアプリケーション・メタデータに含まれています。
.PP
category=\fIcategory\fR
.RS 4
アプリケーションのカテゴリ。例は、http://standards\&.freedesktop\&.org/menu\-spec/latest/apa\&.htmlを参照してください。
.RE
.PP
copyright=\fIstring\fR
.RS 4
アプリケーションのコピーライト文字列。この引数は、Debianメタデータで使用されます。
.RE
.PP
email=\fIaddress\fR
.RS 4
DebianのMaintainerフィールドで使用される電子メール・アドレスです。
.RE
.PP
licenseFile=\fIpath\fR
.RS 4
バンドラによって表示または記録される使用許諾契約(EULA)の場所。パスは、パッケージ・アプリケーション・リソースに相対的です(例:
\fB\-BlicenseFile=COPYING\fR)。
.RE
.PP
licenseType=\fItype\fR
.RS 4
\fB\-BlicenseType=Proprietary\fRまたは\fB"\-BlicenseType=GPL v2 + Classpath Exception"\fRなどのライセンス・タイプの短縮名。
.RE
.PP
vendor=\fIvalue\fR
.RS 4
アプリケーションを提供する会社、組織または個人。この引数は、DebianのMaintainerフィールドで使用されます。
.RE
.SS "Linux RPMバンドラ引数"
.PP
category=\fIcategory\fR
.RS 4
アプリケーションのカテゴリ。例は、http://standards\&.freedesktop\&.org/menu\-spec/latest/apa\&.htmlを参照してください。
.RE
.PP
licenseFile=\fIpath\fR
.RS 4
バンドラによって表示または記録される使用許諾契約(EULA)の場所。パスは、パッケージ・アプリケーション・リソースに相対的です(例:
\fB\-BlicenseFile=COPYING\fR)。
.RE
.PP
licenseType=\fItype\fR
.RS 4
\fB\-BlicenseType=Proprietary\fRまたは\fB"\-BlicenseType=GPL v2 + Classpath Exception"\fRなどのライセンス・タイプの短縮名。
.RE
.PP
vendor=\fIvalue\fR
.RS 4
アプリケーションを提供する会社、組織または個人。
.RE
.SH "非推奨オプション"
.PP
次のオプションは、パッケージ化ツールで使用されておらず、存在していても無視されます。
.PP
\-runtimeversion \fIversion\fR
.RS 4
必要なJavaFXランタイムのバージョン。非推奨。
.RE
.PP
\-noembedlauncher
.RS 4
指定した場合、パッケージャはJARファイルにJavaFX起動クラスを追加しません。非推奨。
.RE
.SH "注意"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
タスク・コマンドとともに\fB\-v \fRオプションを使用すれば、詳細な出力が有効になります。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-srcdir\fRオプションをコマンドで使用できる場合は、複数回使用できます。\fB\-srcfiles\fRオプションを指定すると、引数に指定されたファイル名が、その前に指定されている\fBsrcdir\fRオプションで指定された場所で検索されます。\fB\-srcdir\fRが\fB\-srcfiles\fRの前に指定されていない場合は、\fBjavapackager\fRコマンドが実行されるディレクトリが使用されます。
.RE
.SH "例"
.PP
\fB例 1 \fR\-createjarコマンドの使用
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavapackager \-createjar \-appclass package\&.ClassName\fR
\fB  \-srcdir classes \-outdir out \-outfile outjar \-v\fR
 
.fi
.if n \{\
.RE
.\}
\fBclasses\fRディレクトリの内容を\fBoutjar\&.jar\fRにパッケージ化して、アプリケーション・クラスを\fBpackage\&.ClassName\fRに設定します。
.RE
.PP
\fB例 2 \fR\-deployコマンドの使用
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavapackager \-deploy \-outdir outdir \-outfile outfile \-width 34 \-height 43 \fR
\fB  \-name AppName \-appclass package\&.ClassName \-v \-srcdir compiled\fR
 
.fi
.if n \{\
.RE
.\}
\fBoutfile\&.jnlp\fRおよび対応する\fBoutfile\&.html\fRファイルをアプリケーション\fBAppName\fRの\fBoutdir\fRに生成します。これは\fBpackage\&.ClassName\fRで開始し、34 x 43ピクセルのサイズです。
.RE
.PP
\fB例 3 \fR\-makeallコマンドの使用
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavapackager \-makeall \-appclass brickbreaker\&.Main \-name BrickBreaker \-width 600\fR
\fB\-height 600\fR
 
.fi
.if n \{\
.RE
.\}
コンパイルを含むすべてのパッケージ化作業(\fBcreatejar\fRおよび\fBdeploy\fR)を実行します。
.RE
.PP
\fB例 4 \fR\-signjarコマンドの使用
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavapackager \-signJar \-\-outdir dist \-keyStore sampleKeystore\&.jks \-storePass ****\fR
\fB\-alias duke \-keypass **** \-srcdir dist\fR
 
.fi
.if n \{\
.RE
.\}
\fBdist\fRディレクトリにあるすべてのJARファイルに署名し、alias、\fBkeyStore\fRおよび\fBstorePass\fRを指定して証明書を添付し、署名されたJARファイルを\fBdist\fRディレクトリに戻します。
.RE
.PP
\fB例 5 \fRバンドラ引数を伴う\-deployコマンドの使用
.RS 4
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavapackager \-deploy \-native deb \-Bcategory=Education \-BjvmOptions=\-Xmx128m \fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fB    \-BjvmOptions=\-Xms128m \-outdir packages \-outfile BrickBreaker \-srcdir dist \fR
\fB    \-srcfiles BrickBreaker\&.jar \-appclass brickbreaker\&.Main \-name BrickBreaker \fR
\fB    \-title "BrickBreaker demo"\fR
 
.fi
.if n \{\
.RE
.\}
自己完結型アプリケーションとしてBrickBreakerアプリケーションを実行するためのネイティブLinux Debianパッケージを生成します。
.RE
.br
'pl 8.5i
'bp
