'\" t
.\" Copyright (c) 2003, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: javaws
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Java Web Startツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "javaws" "1" "2013年11月21日" "JDK 8" "Java Web Startツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
javaws \- Java Web Startを起動します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavaws\fR [ \fIrun\-options\fR ] \fIjnlp\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavaws\fR [ \fIcontrol\-options\fR ]
.fi
.if n \{\
.RE
.\}
.PP
\fIrun\-options\fR
.RS 4
コマンド行\fB実行オプション\fR。\fB実行オプション\fRは任意の順序で指定できます。実行オプションを参照してください。
.RE
.PP
\fIjnlp\fR
.RS 4
JNLP (Java Network Launching Protocol)ファイルのパスまたはURL (Uniform Resource Locator)のどちらか。
.RE
.PP
\fI制御オプション\fR
.RS 4
コマンド行\fB制御オプション\fR。\fB制御オプション\fRは任意の順序で指定できます。制御オプションを参照してください。
.RE
.SH "説明"
.PP
\fB注意:\fR
\fBjavaws\fRコマンドは、Oracle Solarisでは使用できません。
.PP
\fBjavaws\fRコマンドは、JNLPのリファレンス実装であるJava Web Startを起動します。Java Web Startは、ネットワーク上で動作するJavaアプリケーションおよびアプレットを起動します。
.PP
JNLPファイルが指定されると、\fBjavaws\fRコマンドはJNLPファイルで指定したJavaアプリケーションまたはアプレットを起動します。
.PP
\fBjavaws\fR起動ツールには、現在のリリースでサポートされている1組のオプションがあります。ただし、これらのオプションは将来のリリースでは削除される可能性があります。
.SH "実行オプション"
.PP
\-offline
.RS 4
Java Web Startをオフライン・モードで実行します。
.RE
.PP
\-Xnosplash
.RS 4
初期スプラッシュ画面を表示しません。
.RE
.PP
\-open \fIarguments\fR
.RS 4
このオプションを指定すると、JNLPファイル内の引数が\fB\-open\fR
\fBarguments\fRに置き換わります。
.RE
.PP
\-print \fIarguments\fR
.RS 4
このオプションを指定すると、JNLPファイル内の引数が\fB\-print\fR
\fBarguments\fRに置き換わります。
.RE
.PP
\-online
.RS 4
オンライン・モードを使用します。これは、デフォルトの動作です。
.RE
.PP
\-wait
.RS 4
\fBjavaws\fRプロセスは、アプリケーションが終了するまで終了しません。Windowsプラットフォーム上では、このオプションは説明したとおりに機能しません。
.RE
.PP
\-verbose
.RS 4
追加の出力を表示します。
.RE
.PP
\-J\fIoption\fR
.RS 4
Java Virtual Machineにoptionを渡します。\fBoption\fRには、Javaアプリケーション起動ツールのリファレンス・ページに記載されているオプションを1つ指定します。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。java(1)を参照してください。
.RE
.PP
\-system
.RS 4
アプリケーションをシステム・キャッシュのみから実行します。
.RE
.SH "制御オプション"
.PP
\-viewer
.RS 4
Javaコントロール・パネルでキャッシュ・ビューアを表示します。
.RE
.PP
\-clearcache
.RS 4
インストールされていないすべてのアプリケーションをキャッシュから削除します。
.RE
.PP
\-userConfig \fIproperty\-name\fR
.RS 4
指定されたデプロイメント・プロパティをクリアします。
.RE
.PP
\-userConfig \fIproperty\-name property\-value\fR
.RS 4
指定されたデプロイメント・プロパティを指定された値に設定します。
.RE
.PP
\-uninstall
.RS 4
キャッシュからすべてのアプリケーションを削除します。
.RE
.PP
\-uninstall \fIjnlp\fR
.RS 4
キャッシュからアプリケーションを削除します。
.RE
.PP
\-print \fIimport\-options \fRjnlp
.RS 4
キャッシュにアプリケーションをインポートします。
.RE
.SH "インポート・オプション"
.PP
\-silent
.RS 4
サイレント・モードでインポートします(ユーザー・インタフェースは表示されません)。
.RE
.PP
\-system
.RS 4
システム・キャッシュにアプリケーションをインポートします。
.RE
.PP
\-codebase \fIurl\fR
.RS 4
指定したコードベースからリソースを取得します。
.RE
.PP
\-shortcut
.RS 4
ユーザーがプロンプトを受け入れればショートカットをインストールします。このオプションは、
\fB\-silent\fRオプションも使用しないと効果がありません。
.RE
.PP
\-association
.RS 4
ユーザーがプロンプトを受け入れればアソシエーションをインストールします。このオプションは、
\fB\-silent\fRオプションも使用しないと効果がありません。
.RE
.PP
\fB注意:\fR\fBjavaws \-shortcut \-uninstall\fR
.SH "ファイル"
.PP
ユーザーおよびシステム・キャッシュならびにdeployment\&.propertiesファイルの詳細は、デプロイメント構成ファイルおよびプロパティ
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/jweb/jcp/properties\&.html)を参照してください
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Java Web Start
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/javaws/index\&.html)
.RE
.br
'pl 8.5i
'bp
