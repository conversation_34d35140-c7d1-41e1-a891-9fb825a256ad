'\" t
.\" Copyright (c) 2004, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jinfo
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: トラブルシューティング・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jinfo" "1" "2013年11月21日" "JDK 8" "トラブルシューティング・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jinfo \- 構成情報を生成します。このコマンドは試験的なもので、サポートされていません。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjinfo\fR [ \fIoption\fR ] \fIpid\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjinfo\fR [ \fIoption \fR] \fIexecutable core\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjinfo\fR [ \fIoption \fR] \fI[ servier\-id ] remote\-hostname\-or\-IP\fR
.fi
.if n \{\
.RE
.\}
.PP
\fIオプション\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIpid\fR
.RS 4
構成情報が出力されるプロセスID。プロセスはJavaプロセスである必要があります。マシン上で実行しているJavaプロセスの一覧を取得するには、jps(1)コマンドを使用します。
.RE
.PP
\fI実行可能ファイル\fR
.RS 4
コア・ダンプの作成元のJava実行可能ファイル。
.RE
.PP
\fIコア\fR
.RS 4
構成情報が出力されるコア・ファイル。
.RE
.PP
\fIremote\-hostname\-or\-IP\fR
.RS 4
リモート・デバッグ・サーバーの\fBホスト名\fRまたは\fBIP\fRアドレス。jsadebugd(1)を参照してください。
.RE
.PP
\fIserver\-id\fR
.RS 4
複数のデバッグ・サーバーが同一のリモート・ホストで実行している場合の、オプション固有のID。
.RE
.SH "説明"
.PP
\fBjinfo\fRコマンドは、指定されたJavaプロセスやコア・ファイルまたはリモート・デバッグ・サーバーのJava構成情報を出力します。構成情報には、Javaシステム・プロパティとJava仮想マシン(JVM)のコマンドライン・フラグが含まれています。指定されたプロセスが64ビットJVM上で実行されている場合、\fB\-J\-d64\fRオプションを指定する必要がある場合があります。次に例を示します。\fBjinfo\fR
\fB\-J\-d64 \-sysprops pid\fR。
.PP
このユーティリティはサポート対象外であり、将来のJDKのリリースでは利用できなくなる可能性があります。\fBdbgeng\&.dll\fRが存在していないWindowsシステムでは、Debugging Tools For Windowsをインストールしないとこれらのツールが正常に動作しません。\fBPATH\fR環境変数には、ターゲット・プロセスによって使用されるjvm\&.dllの場所、またはクラッシュ・ダンプ・ファイルが生成された場所が含まれるようにしてください。たとえば、\fBset PATH=%JDK_HOME%\ejre\ebin\eclient;%PATH%\fRです。
.SH "オプション"
.PP
no\-option
.RS 4
コマンド行フラグを、システム・プロパティ名と値のペアとともに出力します。
.RE
.PP
\-flag \fIname\fR
.RS 4
指定されたコマンドライン・フラグの名前と値を出力します。
.RE
.PP
\-flag \fI[+|\-]name\fR
.RS 4
指定されたブール型のコマンドライン・フラグを有効または無効にします。
.RE
.PP
\-flag \fIname=value\fR
.RS 4
指定されたコマンドライン・フラグを指定された値に設定します。
.RE
.PP
\-flags
.RS 4
JVMに渡されるコマンドライン・フラグを出力します。
.RE
.PP
\-sysprops
.RS 4
Javaシステム・プロパティを名前と値のペアとして出力します。
.RE
.PP
\-h
.RS 4
ヘルプ・メッセージが出力されます。
.RE
.PP
\-help
.RS 4
ヘルプ・メッセージが出力されます。
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jps(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jsadebugd(1)
.RE
.br
'pl 8.5i
'bp
