'\" t
.\" Copyright (c) 2004, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jps
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: モニタリング・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jps" "1" "2013年11月21日" "JDK 8" "モニタリング・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jps \- ターゲット・システム上で計測されたJava仮想マシン(JVM)を一覧表示します。このコマンドは試験的なもので、サポートされていません。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjps\fR [ \fIoptions\fR ] [ \fIhostid\fR ]
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIhostid\fR
.RS 4
プロセス・レポートを生成するホストの識別子。\fBhostid\fRには、通信プロトコル、ポート番号、実装に固有な他のデータを指定したオプション・コンポーネントを含めることができます。ホスト識別子を参照してください。
.RE
.SH "説明"
.PP
\fBjps\fRコマンドは、ターゲット・システム上で計測されたJava HotSpot VMを一覧表示します。このコマンドで表示できるレポート情報は、アクセス権を持ったJVMに関するものに限定されます。
.PP
\fBhostid\fRを指定せずに\fBjps\fRコマンドを実行した場合、ローカル・ホストで計測されたJVMが検索されます。\fBhostid\fRを指定して起動した場合、指定されたプロトコルとポートを使用して、指定されたホスト上のJVMを検索します。\fBjstatd\fRプロセスがターゲット・ホスト上で実行されていると想定されます。
.PP
\fBjps\fRコマンドは、ターゲット・システムで計測された各JVMについて、ローカルVM識別子、つまり\fBlvmid\fRをレポートします。\fBlvmid\fRは、一般的にはJVMプロセスに対するオペレーティング・システムのプロセス識別子ですが、必ずしもそうであるとは限りません。オプションを指定しない場合、\fBjps\fRによって、各Javaアプリケーションの\fBlvmid\fRが一覧表示され、それぞれにアプリケーションのクラス名またはJARファイル名が簡単な形式で示されます。この簡単な形式のクラス名とJARファイル名では、クラスのパッケージ情報またはJARファイル・パス情報が省略されています。
.PP
\fBjps\fRコマンドは、Java起動ツールを使用してmainメソッドに渡されるクラス名と引数を検索します。独自の起動ツールを使用してターゲットJVMを起動した場合は、\fBmain\fRメソッドに渡されるクラス名またはJARファイル名と引数は利用できません。この場合、\fBjps\fRコマンドは、\fBmain\fRメソッドに渡されるクラス名またはJARファイル名と引数に対して、文字列\fBUnknown\fRを出力します。
.PP
\fBjps\fRコマンドで生成されるJVMのリストは、このコマンドを実行するプリンシパルに与えられたアクセス権に基づき、制限される場合があります。このコマンドは、オペレーティング・システム独自のアクセス制御機構による決定に基づいて、プリンシパルにアクセス権が与えられているJVMのみを一覧表示します。
.SH "オプション"
.PP
\fBjps\fRコマンドでは、コマンドの出力を変更するオプションが多数サポートされています。将来、これらのオプションは、変更または廃止される可能性があります。
.PP
\-q
.RS 4
クラス名、JARファイル名、および\fBmain\fRメソッドに渡された引数の出力を抑制し、ローカルVM識別子の一覧のみを生成します。
.RE
.PP
\-m
.RS 4
\fBmain\fRメソッドに渡される引数を出力します。この出力は、組み込まれているJVMに対して\fBnull\fRになることもあります。
.RE
.PP
\-l
.RS 4
アプリケーションの\fBmain\fRクラスのフル・パッケージ名、またはアプリケーションのJARファイルへのフルパス名を出力します。
.RE
.PP
\-v
.RS 4
JVMに渡される引数を表示します。
.RE
.PP
\-V
.RS 4
クラス名、JARファイル名、およびmaiメソッドに渡された引数の出力を抑制し、ローカルVM識別子の一覧のみを生成します。
.RE
.PP
\-J\fBoption\fR
.RS 4
JVMに\fBoption\fRを渡します。optionには、Javaアプリケーション起動ツールのリファレンス・ページに記載されている\fBオプション\fRを1つ指定します。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。java(1)を参照してください。
.RE
.SH "ホスト識別子"
.PP
ホスト識別子、つまり\fBhostid\fRは、ターゲット・システムを示す文字列です。\fBhostid\fR文字列の構文は、URIの構文に対応しています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB[protocol:][[//]hostname][:port][/servername]\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fIprotocol\fR
.RS 4
通信プロトコルです。\fBprotocol\fRが省略され、\fBhostname\fRが指定されていない場合、デフォルトのプロトコルが、プラットフォーム固有の最適化されたローカル・プロトコルになります。プロトコルが省略され、ホスト名が指定されている場合は、デフォルト・プロトコルは\fBrmi\fRになります。
.RE
.PP
hostname
.RS 4
ターゲット・ホストを示すホスト名またはIPアドレスです。\fBhostname\fRパラメータが省略されている場合は、ターゲット・ホストはローカル・ホストになります。
.RE
.PP
port
.RS 4
リモート・サーバーと通信するためのデフォルト・ポートです。\fBhostname\fRパラメータが省略されているか、\fBprotocol\fRパラメータが、最適化されたローカル・プロトコルを指定している場合、\fBport\fRパラメータは無視されます。それ以外の場合、\fBport\fRパラメータの扱いは、実装によって異なります。デフォルトの
\fBrmi\fRプロトコルの場合、\fBport\fRパラメータは、リモート・ホスト上のrmiregistryのポート番号を示します。\fBport\fRパラメータが省略されているか、\fBprotocol\fRパラメータが\fBrmi\fRを示している場合、デフォルトのrmiregistryポート(1099)が使用されます。
.RE
.PP
servername
.RS 4
このパラメータの扱いは、実装によって異なります。最適化されたローカル・プロトコルの場合、このフィールドは無視されます。\fBrmi\fRプロトコルの場合、このパラメータは、リモート・ホスト上のRMIリモート・オブジェクトの名前を示す文字列になります。詳細は、\fBjstatd\fRコマンドの\fB\-n\fRオプションを参照してください。
.RE
.SH "出力フォーマット"
.PP
\fBjps\fRコマンドの出力は、次のパターンに従います。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBlvmid [ [ classname | JARfilename | "Unknown"] [ arg* ] [ jvmarg* ] ]\fR
 
.fi
.if n \{\
.RE
.\}
.PP
すべての出力トークンは空白文字で区切られます。\fBarg\fR値の中で空白を使用すると、実際の定位置パラメータに引数をマッピングしようとするときに、あいまいになります。
.PP
\fB注意:\fR
将来のリリースでこの形式は変更される可能性があるため、\fBjps\fRの出力を解析するスクリプトは作成しないことをお薦めします。\fBjps\fR出力を解析するスクリプトを作成すると、このツールの将来のリリースで、作成したスクリプトの変更が必要になる可能性があります。
.SH "例"
.PP
この項では、\fBjps\fRコマンドの例を示します。
.PP
ローカル・ホスト上で計測されたJVMを一覧表示する場合:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjps\fR
\fB18027 Java2Demo\&.JAR\fR
\fB18032 jps\fR
\fB18005 jstat\fR
 
.fi
.if n \{\
.RE
.\}
.PP
次の例では、リモート・ホスト上で計測されたJVMを一覧表示します。この例では、\fBjstat\fRサーバーと、その内部RMIレジストリまたは別の外部rmiregistryプロセスのいずれかが、リモート・ホストのデフォルト・ポート(ポート1099)で実行されていると想定しています。また、ローカル・ホストが、リモート・ホストへの有効なアクセス権を持っていることも想定しています。この例には、\fB\-l\fRオプションも含まれ、クラス名またはJARファイル名を詳細な形式で出力します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjps \-l remote\&.domain\fR
\fB3002 /opt/jdk1\&.7\&.0/demo/jfc/Java2D/Java2Demo\&.JAR\fR
\fB2857 sun\&.tools\&.jstatd\&.jstatd\fR
 
.fi
.if n \{\
.RE
.\}
.PP
次の例では、RMIレジストリにデフォルトではないポートを使用して、リモート・ホスト上で計測されたJVMを一覧表示します。この例では、内部RMIレジストリがポート2002にバインドされた\fBjstatd\fRサーバーが、リモート・ホスト上で実行されていると想定しています。また、\fB\-m\fRオプションを使用して、一覧表示されたそれぞれのJavaアプリケーションの\fBmain\fRメソッドに渡される引数を組み込んでいます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjps \-m remote\&.domain:2002\fR
\fB3002 /opt/jdk1\&.7\&.0/demo/jfc/Java2D/Java2Demo\&.JAR\fR
\fB3102 sun\&.tools\&.jstatd\&.jstatd \-p 2002\fR
 
.fi
.if n \{\
.RE
.\}
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
java(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jstat(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jstatd(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
rmiregistry(1)
.RE
.br
'pl 8.5i
'bp
