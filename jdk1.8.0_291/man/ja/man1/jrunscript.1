'\" t
.\" Copyright (c) 2006, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jrunscript
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: スクリプティング・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jrunscript" "1" "2013年11月21日" "JDK 8" "スクリプティング・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jrunscript \- 対話型モードとバッチ・モードをサポートするコマンド行スクリプト・シェルを実行します。このコマンドは試験的なもので、サポートされていません。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjrunscript\fR [\fIoptions\fR] [\fIarguments\fR]
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIarguments\fR
.RS 4
引数を使用する場合、オプションまたはコマンド名の直後に記述してください。引数を参照してください。
.RE
.SH "説明"
.PP
\fBjrunscript\fRコマンドは、言語に依存しないコマンド行スクリプト・シェルです。\fBjrunscript\fRは、対話型(read\-eval\-print)モードとバッチ(\fB\-f\fRオプション)・モードの両方のスクリプト実行をサポートします。デフォルトの使用言語はJavaScriptですが、\fB\-l\fRオプションを使用すれば他の言語も指定できます。\fBjrunscript\fRコマンドは、Javaとスクリプト言語との通信を使用して探求的なプログラミング・スタイルをサポートします。
.SH "オプション"
.PP
\-classpath \fIpath\fR
.RS 4
スクリプトがアクセスする必要のあるクラス・ファイルの場所を示します。
.RE
.PP
\-cp \fIpath\fR
.RS 4
\fB\-classpath\fR
\fIpath\fRと同じです。
.RE
.PP
\-D\fIname\fR=\fIvalue\fR
.RS 4
Javaのシステム・プロパティを設定します。
.RE
.PP
\-J\fIflag\fR
.RS 4
\fBflag\fRを、\fBjrunscript\fRコマンドが実行されているJava仮想マシンに直接渡します。
.RE
.PP
\-I \fIlanguage\fR
.RS 4
指定されたスクリプト言語を使用します。デフォルトではJavaScriptが使用されます。他のスクリプト言語を使用するには、\fB\-cp\fRまたは\fB\-classpath\fRオプションを使用して、対応するスクリプト・エンジンのJARファイルを指定する必要があります。
.RE
.PP
\-e \fIscript\fR
.RS 4
指定されたスクリプトを評価します。このオプションを使用すれば、コマンドラインにすべてが指定された1行スクリプトを実行できます。
.RE
.PP
\-encoding \fIencoding\fR
.RS 4
スクリプト・ファイルの読取り時に使用する文字エンコーディングを指定します。
.RE
.PP
\-f \fIscript\-file\fR
.RS 4
指定されたスクリプト・ファイル(バッチ・モード)を評価します。
.RE
.PP
\-f \-
.RS 4
標準入力からスクリプトを読み取り、それを評価します(対話型モード)。
.RE
.PP
\-help
.RS 4
ヘルプ・メッセージを表示して終了します。
.RE
.PP
\-?
.RS 4
ヘルプ・メッセージを表示して終了します。
.RE
.PP
\-q
.RS 4
利用可能なすべてのスクリプト・エンジンを一覧表示したあと、終了します。
.RE
.SH "引数"
.PP
argumentsが存在していて、かつ\fB\-e\fR、\fB\-f\fRのいずれのオプションも使用されなかった場合、最初の引数がスクリプト・ファイルとなり、他の引数が存在する場合はスクリプトに渡されます。argumentsと、\fB\-e\fRまたは\fB\-f\fRオプションが使用されている場合、すべてのargumentsがスクリプトに渡されます。arguments、\fB\-e\fR、\fB\-f\fRがどれも存在しなかった場合は、対話型モードが使用されます。スクリプトからスクリプト引数を使用するには、\fBarguments\fRという名前の\fBString\fR配列型のエンジン変数を使用します。
.SH "例"
.SS "インライン・スクリプトの実行"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjrunscript \-e "print(\*(Aqhello world\*(Aq)"\fR
\fBjrunscript \-e "cat(\*(Aqhttp://www\&.example\&.com\*(Aq)"\fR
 
.fi
.if n \{\
.RE
.\}
.SS "指定された言語の使用およびスクリプト・ファイルの評価"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjrunscript \-l js \-f test\&.js\fR
 
.fi
.if n \{\
.RE
.\}
.SS "対話型モード"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjrunscript\fR
\fBjs> print(\*(AqHello World\en\*(Aq);\fR
\fBHello World\fR
\fBjs> 34 + 55\fR
\fB89\&.0\fR
\fBjs> t = new java\&.lang\&.Thread(function() { print(\*(AqHello World\en\*(Aq); })\fR
\fBThread[Thread\-0,5,main]\fR
\fBjs> t\&.start()\fR
\fBjs> Hello World\fR
\fB \fR
\fBjs>\fR
 
.fi
.if n \{\
.RE
.\}
.SS "スクリプト引数を指定したスクリプト・ファイルの実行"
.PP
test\&.jsファイルはスクリプト・ファイルです。\fBarg1\fR、\fBarg2\fRおよび\fBarg3\fRの各引数がスクリプトに渡されます。スクリプトはarguments配列を使用してこれらの引数にアクセスできます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjrunscript test\&.js arg1 arg2 arg3\fR
 
.fi
.if n \{\
.RE
.\}
.SH "関連項目"
.PP
JavaScriptを使用している場合、ユーザー定義スクリプトを評価する前に、\fBjrunscript\fRコマンドによって特定の組込み関数およびオブジェクトが初期化されます(これらについては、次のjrunscript JavaScript組込みのAPI仕様に記載されています)。
.PP
http://docs\&.oracle\&.com/javase/7/docs/technotes/tools/share/jsdocs/overview\-summary\&.html
.br
'pl 8.5i
'bp
