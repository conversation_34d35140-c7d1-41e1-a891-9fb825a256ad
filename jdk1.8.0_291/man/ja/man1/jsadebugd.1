'\" t
.\" Copyright (c) 2004, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jsadebugd
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: トラブルシューティング・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jsadebugd" "1" "2013年11月21日" "JDK 8" "トラブルシューティング・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jsadebugd \- Javaプロセスまたはコア・ファイルに接続し、デバッグ・サーバーとして機能します。このコマンドは試験的なもので、サポートされていません。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjsadebugd\fR \fIpid\fR [ \fIserver\-id\fR ]
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjsadebugd\fR \fIexecutable\fR \fIcore\fR [ \fIserver\-id\fR ]
.fi
.if n \{\
.RE
.\}
.PP
\fIpid\fR
.RS 4
デバッグ・サーバーが接続するプロセスのプロセスIDです。プロセスはJavaプロセスである必要があります。マシン上で実行しているJavaプロセスの一覧を取得するには、jps(1)コマンドを使用します。単一のプロセスに接続できるデバッグ・サーバーのインスタンスは、1つに制限されます。
.RE
.PP
\fI実行可能ファイル\fR
.RS 4
コア・ダンプの作成元のJava実行可能ファイル。
.RE
.PP
\fIコア\fR
.RS 4
デバッグ・サーバーを接続するコア・ファイルです。
.RE
.PP
\fIserver\-id\fR
.RS 4
複数のデバッグ・サーバーが同一のマシン上で実行されている場合に必要になる、オプションの一意のIDです。このIDは、リモート・クライアントが、接続先のデバッグ・サーバーを特定するために使用する必要があります。このIDは、単一のマシン内で一意にする必要があります。
.RE
.SH "説明"
.PP
\fBjsadebugd\fRコマンドは、Javaプロセスまたはコア・ファイルに接続し、デバッグ・サーバーとして機能します。\fBjstack\fR、\fBjmap\fRおよび\fBjinfo\fRなどのリモート・クライアントは、Java Remote Method Invocation (RMI)を使用しているサーバーに接続できます。\fBjsadebugd\fRコマンドを起動する前に、\fBrmiregistry\fRコマンドでRMIレジストリを次のように起動します。\fI$JAVA_HOME\fRはJDKのインストール・ディレクトリです。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBrmiregistry \-J\-Xbootclasspath/p:$JAVA_HOME/lib/sajdi\&.jar\fR
 
.fi
.if n \{\
.RE
.\}
.PP
RMIレジストリが起動していない場合、\fBjsadebugd\fRコマンドはRMIレジストリを標準(1099)ポートで内部で起動します。デバッグ・サーバーは、\fBSIGINT\fRを送信することにより停止できます。SIGINTを送信するには、\fB[Ctrl] + [C]\fRを押します。
.PP
\fB注意:\fR
このユーティリティはサポート対象外であり、将来のJDKのリリースでは利用できなくなる可能性があります。\fBdbgeng\&.dll\fRが存在していないWindowsシステムでは、Debugging Tools For Windowsをインストールしないとこれらのツールが正常に動作しません。\fBPATH\fR環境変数には、ターゲット・プロセスによって使用されるjvm\&.dllの場所、またはクラッシュ・ダンプ・ファイルが生成された場所が含まれるようにしてください。例:
\fBs\fR\fBet PATH=%JDK_HOME%\ejre\ebin\eclient;%PATH%\fR
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jinfo(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jmap(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jps(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jstack(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
rmiregistry(1)
.RE
.br
'pl 8.5i
'bp
