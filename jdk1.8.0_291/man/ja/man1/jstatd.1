'\" t
.\" Copyright (c) 2004, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jstatd
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: モニタリング・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jstatd" "1" "2013年11月21日" "JDK 8" "モニタリング・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jstatd \- Java仮想マシン(JVM)をモニターし、リモート・モニタリング・ツールがJVMに接続できるようにします。このコマンドは試験的なもので、サポートされていません。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjstatd\fR [ \fIoptions\fR ]
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.SH "説明"
.PP
\fBjstatd\fRコマンドは、計測されたJava HotSpot VMの作成と終了をモニターし、ローカル・システム上で実行されているJVMに、リモート・モニタリング・ツールが接続できるようにするためのインタフェースを提供するRMIサーバー・アプリケーションです。
.PP
\fBjstatd\fRサーバーでは、ローカル・ホストにRMIレジストリが必要になります。\fBjstatd\fRサーバーはデフォルト・ポートで、または\fB\-p\fR
\fBport\fRオプションで指定したポートで、RMIレジストリに接続しようとします。RMIレジストリが見つからない場合、\fB\-p\fR
\fBport\fRオプションで指定されたポート、または\fB\-p\fR
\fBport\fRオプションが省略されている場合は、デフォルトRMIレジストリにバインドされた\fBjstatd\fRアプリケーション内に、1つのRMIレジストリが作成されます。内部RMIレジストリの作成は、\fB\-nr\fRオプションを指定することによって中止できます。
.SH "オプション"
.PP
\-nr
.RS 4
既存のRMIレジストリが見つからない場合、\fBjstatd\fRプロセス内に内部RMIレジストリを作成しないようにします。
.RE
.PP
\-p \fIport\fR
.RS 4
RMIレジストリがあると予想されるポート番号です。見つからない場合は、\fB\-nr\fRオプションが指定されていなければ作成されます。
.RE
.PP
\-n \fIrminame\fR
.RS 4
RMIレジストリにおいて、リモートRMIオブジェクトがバインドされる名前です。デフォルト名は\fBJStatRemoteHost\fRです。複数の\fBjstatd\fRサーバーが同じホスト上で起動している場合、各サーバーのエクスポートしたRMIオブジェクトの名前は、このオプションを指定することによって、一意の名前にすることができます。ただし、このオプションを使用する場合、モニタリング・クライアントの\fBhostid\fRおよび\fBvmid\fR文字列に、その一意のサーバー名を含める必要があります。
.RE
.PP
\-J\fIoption\fR
.RS 4
JVMに\fBoption\fRを渡します。optionには、Javaアプリケーション起動ツールのリファレンス・ページに記載されている\fBオプション\fRを1つ指定します。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。java(1)を参照してください。
.RE
.SH "セキュリティ"
.PP
\fBjstatd\fRサーバーは、適切なネイティブ・アクセス権を持つJVMのみをモニターできます。したがって、\fBjstatd\fRプロセスは、ターゲットJVMと同じユーザー資格証明で実行されている必要があります。Solaris、LinuxおよびOS Xオペレーティング・システムにおけるrootユーザーなどの一部のユーザー資格証明は、システム上の任意のJVMによってエクスポートされたインストゥルメンテーションへのアクセス権を持っています。このような資格証明で実行されている\fBjstatd\fRプロセスは、システム上のすべてのJVMをモニターできますが、セキュリティ上の別の問題が起こります。
.PP
\fBjstatd\fRサーバーには、リモート・クライアントの認証機能がありません。そのため、\fBjstatd\fRサーバー・プロセスを実行すると、\fBjstatd\fRプロセスがアクセス権を持つすべてのJVMによるインストゥルメンテーションのエクスポートを、ネットワーク上のすべてのユーザーに公開することになります。この無防備な状態は、環境によっては望ましくない場合があるので、特に本番環境またはセキュアでないネットワークでは、\fBjstatd\fRプロセスを起動する前に、ローカル・セキュリティ・ポリシーを検討する必要があります。
.PP
\fBjstatd\fRサーバーは、他のセキュリティ・マネージャがインストールされていない場合には、\fBRMISecurityPolicy\fRのインスタンスをインストールします。そのため、セキュリティ・ポリシー・ファイルを指定する必要があります。ポリシー・ファイルは、http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/PolicyFiles\&.htmlにある
「デフォルトのPolicyの実装とポリシー・ファイルの構文」に準拠している必要があります
.PP
次のポリシー・ファイルでは、セキュリティ例外を発生せずに\fBjstatd\fRサーバーを実行できます。このポリシーは、すべてのコード・ベースへのあらゆるアクセス権を認めるポリシーよりも自由度が低いですが、\fBjstatd\fRサーバーを実行するために最低限必要なアクセス権のみを認めるポリシーよりも自由度が高くなっています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBgrant codebase "file:${java\&.home}/\&.\&./lib/tools\&.jar" {   \fR
\fB    permission java\&.security\&.AllPermission;\fR
\fB};\fR
 
.fi
.if n \{\
.RE
.\}
.PP
このポリシー設定を使用するには、このテキストを\fBjstatd\&.all\&.policy\fRというファイルにコピーし、次のように\fBjstatd\fRサーバーを実行します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjstatd \-J\-Djava\&.security\&.policy=jstatd\&.all\&.policy\fR
 
.fi
.if n \{\
.RE
.\}
.PP
より厳しいセキュリティを実施するサイトの場合、カスタム・ポリシー・ファイルを使用して、特定の信頼できるホストまたはネットワークにアクセスを制限することができます。ただし、このような方法は、IPアドレスの盗聴攻撃を受けやすくなります。セキュリティの問題について、カスタマイズしたポリシー・ファイルでも対処できない場合は、\fBjstatd\fRサーバーを実行せずに、\fBjstat\fRと\fBjps\fRツールをローカルで使用することが最も安全な方法になります。
.SH "リモート・インタフェース"
.PP
\fBjstatd\fRプロセスがエクスポートするインタフェースは、独自に開発したものであり変更される予定です。ユーザーおよび開発者は、このインタフェースへの書込みを行わないでください。
.SH "例"
.PP
次に、\fBjstatd\fRコマンドの例を示します。\fBjstatd\fRスクリプトによって、サーバーはバックグラウンドで自動的に起動します。
.SS "内部RMIレジストリ"
.PP
この例は、内部RMIレジストリを使用した\fBjstatd\fRセッションの起動方法を表しています。この例では、デフォルトのRMIレジストリ・ポート(ポート1099)には、他のサーバーはバインドされていないと想定しています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjstatd \-J\-Djava\&.security\&.policy=all\&.policy\fR
 
.fi
.if n \{\
.RE
.\}
.SS "外部RMIレジストリ"
.PP
この例は、外部RMIレジストリを使用した\fBjstatd\fRセッションの起動を表しています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBrmiregistry&\fR
\fBjstatd \-J\-Djava\&.security\&.policy=all\&.policy\fR
 
.fi
.if n \{\
.RE
.\}
.PP
この例では、ポート2020の外部RMIレジストリを使用して\fBjstatd\fRセッションを起動します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjrmiregistry 2020&\fR
\fBjstatd \-J\-Djava\&.security\&.policy=all\&.policy \-p 2020\fR
 
.fi
.if n \{\
.RE
.\}
.PP
この例では、ポート2020の外部RMIレジストリを使用して、\fBAlternateJstatdServerName\fRにバインドされている\fBjstatd\fRセッションを起動します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBrmiregistry 2020&\fR
\fBjstatd \-J\-Djava\&.security\&.policy=all\&.policy \-p 2020\fR
\fB    \-n AlternateJstatdServerName\fR
 
.fi
.if n \{\
.RE
.\}
.SS "インプロセスRMIレジストリの作成の停止"
.PP
この例では、外部RMIレジストリがない場合に作成しない\fBjstatd\fRセッションを起動します。この例では、RMIレジストリがすでに実行されていると想定しています。RMIレジストリが実行されていない場合、エラー・メッセージが表示されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjstatd \-J\-Djava\&.security\&.policy=all\&.policy \-nr\fR
 
.fi
.if n \{\
.RE
.\}
.SS "RMIロギングの有効化"
.PP
この例では、RMIロギング機能を有効化して\fBjstatd\fRセッションを起動します。この方法は、トラブルシューティングまたはサーバー活動のモニタリングに役立ちます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjstatd \-J\-Djava\&.security\&.policy=all\&.policy\fR
\fB    \-J\-Djava\&.rmi\&.server\&.logCalls=true\fR
 
.fi
.if n \{\
.RE
.\}
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
java(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jps(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jstat(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
rmiregistry(1)
.RE
.br
'pl 8.5i
'bp
