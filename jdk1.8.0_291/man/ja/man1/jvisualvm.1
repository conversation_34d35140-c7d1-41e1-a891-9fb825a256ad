'\" t
.\" Copyright (c) 2008, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jvisualvm
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Javaトラブルシューティング、プロファイリング、モニタリングおよび管理ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jvisualvm" "1" "2013年11月21日" "JDK 8" "Javaトラブルシューティング、プロファイリング、モニタリン"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jvisualvm \- Javaアプリケーションを視覚的に監視、トラブルシュートおよびプロファイルします。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjvisualvm\fR [ \fIoptions\fR ]
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.SH "説明"
.PP
Java VisualVMは、指定されたJava Virtual Machine (JVM)でJavaテクノロジ・ベースのアプリケーション(Javaアプリケーション)が実行されているときに、そのJavaアプリケーションに関する詳細な情報を提供する直感的なグラフィカル・ユーザー・インタフェースです。Java VisualVMという名前は、Java VisualVMがJVMソフトウェアに関する情報を視覚的に提供するという事実に由来しています。
.PP
Java VisualVMは、いくつかのモニタリング、トラブルシューティングおよびプロファイリング・ユーティリティを1つのツールに統合します。たとえば、スタンドアロン・ツール\fBjmap\fR、\fBjinfo\fR、\fBjstat\fRおよび\fBjstack\fRで提供されている機能のほとんどが、Java VisualVMに組み込まれています。\fBjconsole\fRコマンドによって提供される一部の機能など、他の機能はオプションのプラグインとして追加できます。
.PP
Java VisualVMは、Javaアプリケーションの開発者がアプリケーションのトラブルシューティングを行ったり、アプリケーションのパフォーマンスを監視および改善したりするのに役立ちます。Java VisualVMを使用すると、開発者はヒープ・ダンプの生成および解析、メモリー・リークの特定、ガベージ・コレクションの実行および監視、およびメモリーとCPUの簡易プロファイリングの実行が可能になります。プラグインでJava VisualVMの機能を拡張できます。たとえば、\fBjconsole\fRコマンドのほとんどの機能は、「MBean」タブおよびJConsole Plug\-in Wrapperプラグインを介して使用できます。標準のJava VisualVMプラグインのカタログから選択するには、Java VisualVMメニューの\fB「ツール」\fR、\fB「プラグイン」\fRを選択します。
.PP
Java VisualVMを起動するには、次のコマンドを実行します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB%  jvisualvm <options>\fR
 
.fi
.if n \{\
.RE
.\}
.SH "オプション"
.PP
次のオプションは、Java VisualVMを起動したときに実行可能になります。
.PP
\-J\fIjvm_option\fR
.RS 4
この\fBjvm_option\fRをJVMソフトウェアに渡します。
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Java VisualVM開発者のサイト
http://visualvm\&.java\&.net/
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Java SEドキュメントのJava VisualVM
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/visualvm/index\&.html)
.RE
.br
'pl 8.5i
'bp
