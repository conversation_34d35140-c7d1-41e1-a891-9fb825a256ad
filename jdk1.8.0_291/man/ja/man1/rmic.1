'\" t
.\" Copyright (c) 1997, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: rmic
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Remote Method Invocation (RMI)ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "rmic" "1" "2013年11月21日" "JDK 8" "Remote Method Invocation (RMI)"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
rmic \- Java Remote Method Protocol (JRMP)またはInternet Inter\-Orb protocol (IIOP)を使用するリモート・オブジェクトのスタブ、スケルトンおよびTieクラスを生成します。Object Management Group (OMG)インタフェース定義言語(IDL)も生成します
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBrmic\fR [ \fIoptions\fR ] \fIpackage\-qualified\-class\-names\fR
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行\fBオプション\fR。オプションを参照してください。
.RE
.PP
\fIpackage\-qualified\-class\-names\fR
.RS 4
パッケージを含むクラス名。例:
\fBjava\&.awt\&.Color\fR。
.RE
.SH "説明"
.PP
\fB非推奨に関する注意:\fR
Java Remote Method Protocol (JRMP)スタブおよびスケルトンの静的な生成のサポートは非推奨になりました。動的に生成されるJRMPスタブをかわりに使用して、JRMPベースのアプリケーションにこのツールを使用する必要性をなくすことをお薦めします。詳細は、\fBjava\&.rmi\&.server\&.UnicastRemoteObject\fR仕様(http://docs\&.oracle\&.com/javase/8/docs/api/java/rmi/server/UnicastRemoteObject\&.html)を参照してください。
.PP
\fBrmic\fRコンパイラは、Java Remote Method Protocol (JRMP)とスタブおよびTieクラス・ファイル(IIOPプロトコル)を使用して、リモート・オブジェクトのスタブおよびスケルトン・クラス・ファイルを生成します。リモート・オブジェクトの実装クラスであるこれらのクラス・ファイルは、Javaプログラミング言語のクラスをコンパイルしたときに生成されます。リモート実装クラスは、\fBjava\&.rmi\&.Remote\fRインタフェースを実装するクラスです。\fBrmic\fRコマンドでのクラス名は、そのクラスが\fBjavac\fRコマンドでコンパイルが成功していて、かつ完全修飾パッケージ名である必要があります。たとえば、次に示すクラス・ファイル名\fBHelloImpl\fRで\fBrmic\fRコマンドを実行すると、helloサブディレクトリ(クラスのパッケージの名前の付いた)に\fBHelloImpl_Stub\&.class \fRファイルが作成されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBrmic hello\&.HelloImpl\fR
 
.fi
.if n \{\
.RE
.\}
.PP
リモート・オブジェクトのスケルトンはJRMPプロトコル・サーバー側のエンティティで、リモート・オブジェクト実装を呼び出すメソッドを含みます。
.PP
リモート・オブジェクトのTieは、スケルトンと同様にサーバー側のエンティティですが、IIOPプロトコルを使用してクライアントと通信します。
.PP
スタブとは、リモート・オブジェクトのクライアント側での代理です。スタブは、リモート・オブジェクトのメソッド呼出しを、実物のリモート・オブジェクトが常駐するサーバーと通信する役割を持ちます。したがって、クライアントのリモート・オブジェクトへの参照は、実際はローカル・スタブへの参照となります。
.PP
デフォルトで\fBrmic\fRコマンドは、1\&.2 JRMPスタブ・プロトコル・バージョンのみを使用するスタブ・クラスを生成します。これは、\fB\-v1\&.2\fRオプションを指定した場合と同じ動作です。リリース5\&.0以前では\fB\-vcompat\fRオプションがデフォルトでした。IIOPプロトコル用のスタブおよびTieクラスを生成するには\fB\-iiop\fRオプションを使用します。オプションを参照してください。
.PP
スタブはリモート・インタフェースのみを実装し、リモート・オブジェクトが実装するローカル・インタフェースは実装していません。JRMPスタブはリモート・オブジェクトが実装するリモート・インタフェースと同じものを実装しているので、クライアントは、キャストや型チェックにJavaプログラミング言語に組み込まれた演算子を使用することができます。IIOPの場合は、\fBPortableRemoteObject\&.narrow\fRメソッドを使用する必要があります。
.SH "オプション"
.PP
\-bootclasspath \fIpath\fR
.RS 4
ブートストラップ・クラス・ファイルの位置をオーバーライドします。
.RE
.PP
\-classpath path
.RS 4
\fBrmic\fRコマンドがクラスを探すためのパスを指定します。このオプションは、デフォルトや\fBCLASSPATH\fR環境変数設定をオーバーライドします。ディレクトリはコロンで分割します。パスの一般的な形式は\fB\&.:<your_path>\fRです。例:
\fB\&.:/usr/local/java/classes\fR
.RE
.PP
\-d \fIdirectory\fR
.RS 4
生成されたクラス階層の出力先ディレクトリのルートを指定します。このオプションを使用すると、スタブ、スケルトン、およびTieファイルを格納するディレクトリを指定できます。たとえば、次のコマンドはMyClassから導出されたスタブおよびスケルトン・クラスをディレクトリ/java/classes/exampleclassに格納します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBrmic \-d /java/classes exampleclass\&.MyClass\fR
 
.fi
.if n \{\
.RE
.\}
\fB\-d\fRオプションが指定されていない場合、デフォルトの動作は\fB\-d\fRが指定されていた場合と同じです。ターゲット・クラスのパッケージ階層が現在のディレクトリに作成され、stub/tie/skeletonファイルが格納されます。以前のリリースの\fBrmic\fRコマンドでは、\fB\-d\fRが指定されていない場合は、パッケージ階層は作成されず、出力ファイルはすべて現在のディレクトリに直接格納されていました。
.RE
.PP
\-extdirs \fIpath\fR
.RS 4
インストール済拡張機能の位置をオーバーライドします。
.RE
.PP
\-g
.RS 4
ローカル変数を含むすべてのデバッグ情報を生成します。デフォルトでは、行番号情報のみ生成されます。
.RE
.PP
\-idl
.RS 4
\fBrmic\fRコマンドによって、指定したクラスおよび参照されたクラスのOMG IDLが生成されます。IDLでは、プログラミング言語に依存せずに、宣言するだけでオブジェクトのAPIを指定することができます。IDLは、メソッドおよびデータの仕様として使用します。CORBAバインディングを提供する任意の言語で、メソッドおよびデータの作成および呼出しを行うことができます。これらの言語には、JavaおよびC++が含まれています。http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/mapping/jidlMapping\&.htmlの
「Java IDL: IDL to Java Language Mapping」を参照してください
.sp
\fB\-idl\fRオプションを使用するときには、他のオプションも指定できます。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
既存のスタブ/Tie/IDLが入力クラスよりも新しい場合でも、\fB\-always\fRまたは\fB\-alwaysgenerate\fRオプションは再生成を強制します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-factory\fRオプションは、生成されたIDLで\fBfactory\fRキーワードを使用します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
J\fBavaPackage[\&.class]\fR
\fBtoIDLModule\fRの\fB\-idlModule\fRは\fBIDLEntity\fRパッケージ・マッピングを指定します。例:
\fB\-idlModule\fR
\fBmy\&.module my::real::idlmod\fR
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-idlFile\fR
\fBfromJavaPackage[\&.class] toIDLFile\fRは\fBIDLEntity\fRファイル・マッピングを指定します。例:
\fB\-idlFile test\&.pkg\&.X TEST16\&.idl\fR
.RE
.RE
.PP
\-iiop
.RS 4
\fBrmic\fRコマンドによって、JRMPのスタブとスケルトン・クラスのかわりに、IIOPのスタブとTieクラスが生成されます。スタブ・クラスは、リモート・オブジェクトのローカル・プロキシで、クライアントからサーバーに呼出しを送信するときに使用されます。各リモート・インタフェースにはスタブ・クラスが必要です。スタブ・クラスによってリモート・インタフェースが実装されます。クライアントでリモート・オブジェクトを参照するときは、スタブを参照することになります。タイ・クラスは、サーバー側で着呼を処理し、その呼出しを適切な実装クラスにディスパッチするときに使用されます。各実装クラスには、タイ・クラスが必要です。
.sp
\fB\-iiop\fRを使用して\fBrmic\fRコマンドを呼び出すと、次の命名規則に準拠したスタブとTieが生成されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB_<implementationName>_stub\&.class\fR
\fB_<interfaceName>_tie\&.class\fR
 
.fi
.if n \{\
.RE
.\}
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-iiop\fRオプションを使用するときには、他のオプションも指定できます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
既存のスタブ/Tie/IDLが入力クラスよりも新しい場合でも、\fB\-always\fRまたは\fB\-alwaysgenerate\fRオプションは再生成を強制します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-nolocalstubs\fRオプションでは、同じプロセスのクライアントとサーバーに最適化されたスタブは作成されません。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-noValueMethods\fRオプションは\fB\-idl\fRオプションとともに使用する必要があります。\fB\-noValueMethods\fRオプションは、送信されるIDLに\fBvaluetype\fRメソッドおよび初期化子を追加できないようにします。このメソッドおよび初期化子は、valuetypeの場合はオプションです。\fB\-idl\fRオプションとともに\fB\-noValueMethods\fRオプションを指定しないかぎり生成されます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-poa\fRオプションは継承を\fBorg\&.omg\&.CORBA_2_3\&.portable\&.ObjectImpl\fRから\fBorg\&.omg\&.PortableServer\&.Servant\fRに変更します。ポータブル・オブジェクト・アダプタ(POA)の\fBPortableServer\fRモジュールは、ネイティブの\fBServant\fR型を定義します。Javaプログラミング言語では、\fBServant\fR型はJavaの\fBorg\&.omg\&.PortableServer\&.Servant\fRクラスにマップされます。これは、すべてのPOAサーバント実装のベース・クラスとして機能し、アプリケーション・プログラマが呼び出すことのできるいくつかのメソッド、およびPOAによって呼び出され、サーバントの動作を制御するためにユーザーがオーバーライドできるメソッドを提供します。OMG IDL to Java Language Mapping Specification、CORBA V 2\&.3\&.1 ptc/00\-01\-08\&.pdfに準拠しています。
.RE
.RE
.PP
\-J
.RS 4
Javaコマンドとともに使用して、\fB\-J\fRオプションは\fB\-J\fRの後ろに続く引数をJavaインタプリタに渡します(\fB\-J\fRと引数の間にスペースは入れません)。
.RE
.PP
\-keep or \-keepgenerated
.RS 4
スタブ、スケルトン、またはTieクラスのために生成された\fB\&.java\fRソース・ファイルを保持し、\fB\&.class\fRファイルと同じディレクトリに書き込みます。
.RE
.PP
\-nowarn
.RS 4
警告をオフにします。\fB\-nowarn\fRオプションが使用される場合。コンパイラは警告を表示しません。
.RE
.PP
\-nowrite
.RS 4
コンパイルしたクラスをファイル・システムに書き込みません。
.RE
.PP
\-vcompat (非推奨)
.RS 4
1\&.1と1\&.2の両方のJRMPスタブ・プロトコル・バージョンと互換性のあるスタブおよびスケルトン・クラスを作成します。5\&.0以前のリリースではこのオプションがデフォルトでした。生成されたスタブ・クラスは、JDK 1\&.1仮想マシンにロードされると1\&.1スタブ・プロトコル・バージョンを使用し、JDK 1\&.2以降の仮想マシンにロードされると1\&.2スタブ・プロトコル・バージョンを使用します。生成されたスケルトン・クラスでは、1\&.1と1\&.2の両方のスタブ・プロトコル・バージョンをサポートします。生成されたクラスは両方の操作モードをサポートするために、サイズが大きくなります。注意:このオプションは非推奨になりました。説明を参照してください。
.RE
.PP
\-verbose
.RS 4
コンパイラやリンカーが、コンパイルされているクラスやロードされているクラス・ファイルについてのメッセージを表示するようにします。
.RE
.PP
\-v1\&.1 (非推奨)
.RS 4
1\&.1 JRMPスタブ・プロトコル・バージョンのみのスタブおよびスケルトン・クラスを生成します。\fB\-v1\&.1\fRオプションを使用できるのは、JDK 1\&.1から\fBrmic\fRコマンドで生成され、アップグレードできない(さらにダイナミック・クラス・ローディングを使用していない)、既存の静的デプロイされたスタブ・クラスに対し、直列化互換性のあるスタブ・クラスを生成する場合のみです。注意:このオプションは非推奨になりました。説明を参照してください。
.RE
.PP
\-v1\&.2 (非推奨)
.RS 4
(デフォルト)1\&.2 JRMPスタブ・プロトコル・バージョンのみのスタブ・クラスを生成します。スケルトン・クラスは1\&.2スタブ・プロトコル・バージョンで使用できないため、スケルトン・クラスは生成されません。生成されたスタブ・クラスは、JDK 1\&.1仮想マシンにロードされても動作しません。注意:このオプションは非推奨になりました。説明を参照してください。
.RE
.SH "環境変数"
.PP
CLASSPATH
.RS 4
ユーザー定義クラスへのパスをシステムに指定します。ディレクトリはコロンで区切られます。例:
\fB\&.:/usr/local/java/classes\fR
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javac(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
java(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
クラス・パスの設定
.RE
.br
'pl 8.5i
'bp
