'\" t
.\" Copyright (c) 1997, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: rmiregistry
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Remote Method Invocation (RMI)ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "rmiregistry" "1" "2013年11月21日" "JDK 8" "Remote Method Invocation (RMI)"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
rmiregistry \- 現在のホストの指定したポート上にリモート・オブジェクト・レジストリを開始します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBrmiregistry\fR [ \fIport\fR ]
.fi
.if n \{\
.RE
.\}
.PP
\fIport\fR
.RS 4
リモート・オブジェクト・レジストリを開始する現在のホスト上の\fBport\fRの数。
.RE
.SH "説明"
.PP
\fBrmiregistry\fRコマンドは、現在のホストの指定したポート上にリモート・オブジェクト・レジストリを作成し、開始します。portの指定を省略した場合、レジストリはポート1099で開始します。\fBrmiregistry\fRコマンドに、出力機能はありません。通常、これはバックグラウンドで実行されます。次に例を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBrmiregistry &\fR
 
.fi
.if n \{\
.RE
.\}
.PP
リモート・オブジェクト・レジストリは、ブートストラップのネーム・サービスです。同一ホストのRMIサーバーが、リモート・オブジェクトを名前にバインドするために使用されます。次に、ローカルおよびリモート・ホストのクライアントはリモート・オブジェクトを検索し、リモート・メソッドの呼出しを行います。
.PP
レジストリは、一般的に、最初のリモート・オブジェクトの位置を指定します。そこで、アプリケーションはメソッドを呼び出す必要があります。その後、そのオブジェクトはアプリケーション指定のサポートを提供し、他のオブジェクトを探します。
.PP
\fBjava\&.rmi\&.registry\&.LocateRegistry\fRクラスのメソッドは、ローカル・ホスト、またはローカル・ホストとポートで動作するレジストリを取得するために使用されます。
.PP
\fBjava\&.rmi\&.Naming\fRクラスのURLベース・メソッドはレジストリに対して操作を実行し、任意のホストおよびローカル・ホストでのリモート・オブジェクトの検索に使用できます。単純名(文字列)をリモート・オブジェクトにバインドし、新しい名前をリモート・オブジェクトに再バインドし(古いバインドをオーバーライド)、リモート・オブジェクトをアンバインドし、レジストリにバインドされているURLをリスト表示します。
.SH "オプション"
.PP
\-J
.RS 4
Javaオプションとともに使用して、\fB\-J\fRの後ろに続くオプションをJavaインタプリタに引き渡します(\fB\-J\fRとオプションの間にスペースは入れません)。
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
java(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB「java\&.rmi\&.registry\&.LocateRegistry」\fR(http://docs\&.oracle\&.com/javase/8/docs/api/java/rmi/registry/LocateRegistry\&.html)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB「java\&.rmi\&.Naming class description」\fR(http://docs\&.oracle\&.com/javase/8/docs/api/java/rmi/Naming\&.html)
.RE
.br
'pl 8.5i
'bp
