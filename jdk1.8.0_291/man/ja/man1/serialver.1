'\" t
.\" Copyright (c) 1997, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: serialver
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Remote Method Invocation (RMI)ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "serialver" "1" "2013年11月21日" "JDK 8" "Remote Method Invocation (RMI)"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
serialver \- 指定したクラスのシリアル・バージョンUIDを戻します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBserialver\fR [ \fIoptions\fR ] [ \fIclassnames\fR ]
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIclassnames\fR
.RS 4
\fBserialVersionUID\fRを戻すクラスです。
.RE
.SH "説明"
.PP
\fBserialver\fRコマンドは、1つ以上のクラスの\fBserialVersionUID\fRを、展開しているクラスへコピーするのに適した形式で返します。引数なしで呼び出された場合、\fBserialver\fRコマンドは使用率行を出力します。
.SH "オプション"
.PP
\-classpath \fIpath\-files\fR
.RS 4
アプリケーションのクラスおよびリソースの検索パスを設定します。クラスとリソースをコロン(:)で区切ります。
.RE
.PP
\-show
.RS 4
簡単なユーザー・インタフェースを表示します。完全指定のクラス名を入力して、Enterキーか「表示」ボタンを押し、\fBserialVersionUID\fRを表示します。
.RE
.PP
\-J\fIoption\fR
.RS 4
Java Virtual Machineに\fBoption\fRを渡します。optionには、Javaアプリケーション起動ツールのリファレンス・ページに記載されているオプションを1つ指定します。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。java(1)を参照してください。
.RE
.SH "注意"
.PP
\fBserialver\fRコマンドは、指定されたクラスをその仮想マシン内に読み込んで初期化しますが、デフォルトではセキュリティ・マネージャの設定は行いません。信頼できないクラスとともに\fBserialver\fRコマンドを実行する場合には、次のオプションを使用してセキュリティ・マネージャを設定できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-J\-Djava\&.security\&.manager\fR
 
.fi
.if n \{\
.RE
.\}
.PP
必要であれば、次のオプションを使用してセキュリティ・ポリシーを指定できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-J\-Djava\&.security\&.policy=<policy file>\fR
 
.fi
.if n \{\
.RE
.\}
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
policytool(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
http://docs\&.oracle\&.com/javase/8/docs/api/java/io/ObjectStreamClass\&.htmlにある
\fBjava\&.io\&.ObjectStream\fRクラス記述
.RE
.br
'pl 8.5i
'bp
