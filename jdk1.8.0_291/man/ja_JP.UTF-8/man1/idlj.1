'\" t
.\" Copyright (c) 2001, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: idlj
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Java IDLおよびRMI-IIOPツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "idlj" "1" "2013年11月21日" "JDK 8" "Java IDLおよびRMI-IIOPツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
idlj \- 指定されたインタフェース定義言語(IDL)ファイルに対してJavaバインディングを生成します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBidlj\fR [ \fIoptions\fR ] \fIidlfile\fR
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。optionsの順番は任意ですが、\fBidlfile\fRよりも前に指定する必要があります。
.RE
.PP
\fIidlfile\fR
.RS 4
インタフェース定義言語(IDL)による定義が含まれるファイルの名前。
.RE
.SH "説明"
.PP
IDL\-to\-Javaコンパイラは、指定されたIDLファイルに対してJavaバインディングを生成します。バインディングの詳細は、http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/mapping/jidlMapping\&.htmlにある
Java IDL: Java言語マッピングへのIDLを参照してください。
.PP
IDL\-to\-Javaコンパイラの以前のリリースの中には、\fBidltojava\fRという名前だったものがあります。
.SS "クライアント・バインディングおよびサーバー・バインディングの発行"
.PP
次の\fBidlj\fRコマンドは、クライアント側バインディングを含む\fBMy\&.idl\fRという名前のIDLファイルを生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBidlj My\&.idl\fR
 
.fi
.if n \{\
.RE
.\}
.PP
前の構文は次と同等です。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBidlj \-fclient My\&.idl\fR
 
.fi
.if n \{\
.RE
.\}
.PP
次の例では、サーバー側バインディングを生成し、クライアント側バインディングおよびスケルトンを含めており、これらはすべて、POA (継承モデル)です。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBidlg \-fserver My\&.idl\fR
 
.fi
.if n \{\
.RE
.\}
.PP
クライアント側とサーバー側の両方のバインディングを生成する場合は、次のコマンド(どれも等価)のうちの1つを使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBidlj \-fclient \-fserver My\&.idl\fR
\fBidlj \-fall My\&.idl\fR
 
.fi
.if n \{\
.RE
.\}
.PP
サーバー側で可能なモデルは2つあります。移殖可能サーバント継承モデルとTieモデルです。Tie委譲モデルを参照してください。
.PP
\fB移殖可能サーバント継承モデル\fR. デフォルトのサーバー側のモデルは、移殖可能サーバント継承モデルです。\fBMy\&.idl\fR内で\fBMy\fRインタフェースが定義されている場合は、\fBMyPOA\&.java\fRというファイルが生成されます。\fBMy\fRインタフェースの実装を提供する必要があり、\fBMy\fRインタフェースは\fBMyPOA\fRクラスから継承する必要があります。\fBMyPOA\&.java\fRは、http://docs\&.oracle\&.com/javase/8/docs/api/org/omg/PortableServer/Servant\&.htmlにある
\fBorg\&.omg\&.PortableServer\&.Servant\fRクラスを拡張するストリームベースのスケルトンです。
.PP
\fBMy\fRインタフェースは、スケルトンが実装するIDLインタフェースに関連付けられている\fBcallHandler\fRインタフェースと操作インタフェースを実装します。
.PP
ポータブル・オブジェクト・アダプタ(POA)の\fBPortableServer\fRモジュールは、ネイティブの\fBServant\fR型を定義します。http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/POA\&.htmlにある
ポータブル・オブジェクト・アダプタ(POA)を参照してください。
.PP
Javaプログラミング言語では、\fBServant\fR型はJavaの\fBorg\&.omg\&.PortableServer\&.Servant\fRクラスにマップされます。これは、すべてのPOAサーバント実装のベース・クラスとして機能し、アプリケーション・プログラマが呼び出すことのできるいくつかのメソッド、およびPOAによって呼び出され、サーバントの動作を制御するためにユーザーがオーバーライドできるメソッドを提供します。
.PP
継承モデルのもう1つのオプションは、\fB\-oldImplBase\fRフラグを使用して、Java SE 1\&.4より前のリリースのJavaプログラミング言語と互換性のあるサーバー側バインディングを生成することです。\-\fBoldImplBase\fRフラグは非標準で、これらのAPIは非推奨です。このフラグを使用するのは、Java SE 1\&.3で記述された既存のサーバーとの互換性が必要な場合のみです。その場合、既存のmakeファイルを変更して、\fB\-oldImplBase\fRフラグを\fBidlj\fRコンパイラに追加する必要があります。それ以外の場合、POAベースのサーバー側マッピングが生成されます。後方互換性のあるサーバー側バインディングを生成するには、次を実行します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBidlj \-fclient \-fserver \-oldImplBase My\&.idl\fR
\fBidlj \-fall \-oldImplBase My\&.idl\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBMy\&.idl\fR内で\fBMy\fRインタフェースが定義されている場合は、\fB_MyImplBase\&.java\fRというファイルが生成されます。\fBMy\fRインタフェースの実装を提供する必要があり、\fBMy\fRインタフェースは\fB_MyImplBase\fRクラスから継承する必要があります。
.PP
\fBTie委譲モデル\fR. もう1つのサーバー側モデルは、Tieモデルと呼ばれるものです。このサーバー側モデルは、委譲モデルです。Tieとスケルトンを同時に生成することはできないため、それらは別々に生成する必要があります。次のコマンドによって、Tieモデル用のバインディングが生成されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBidlj \-fall My\&.idl\fR
\fBidlj \-fallTIE My\&.idl\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBMy\fRインタフェースの場合、2番目のコマンドにより、\fBMyPOATie\&.java\fRが生成されます。\fBMyPOATie\fRクラスへのコンストラクタは、delegateを取ります。この例では、デフォルトのPOAモデルを使用しているため、コンストラクタにもPOAが必要です。delegateに対して実装を提供する必要がありますが、この実装は\fBMyOperations\fRインタフェースから継承する必要があるのみで、その他のクラスから継承する必要はありません。これをORBと一緒に使用するには、たとえば次のように\fBMyPOATie\fRクラス内で実装をラップする必要があります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBORB orb = ORB\&.init(args, System\&.getProperties());\fR
\fB \fR
\fB// Get reference to rootpoa & activate the POAManager\fR
\fBPOA rootpoa = (POA)orb\&.resolve_initial_references("RootPOA");\fR
\fBrootpoa\&.the_POAManager()\&.activate();\fR
\fB \fR
\fB// create servant and register it with the ORB\fR
\fBMyServant myDelegate = new MyServant();\fR
\fBmyDelegate\&.setORB(orb); \fR
\fB \fR
\fB// create a tie, with servant being the delegate\&.\fR
\fBMyPOATie tie = new MyPOATie(myDelegate, rootpoa);\fR
\fB \fR
\fB// obtain the objectRef for the tie\fR
\fBMy ref = tie\&._this(orb);\fR
 
.fi
.if n \{\
.RE
.\}
.PP
他の実装から継承する必要がある場合、標準の継承モデルではなくTieモデルを使用することもできます。Javaの場合は、インタフェースの継承の個数に制限はありませんが、クラスの継承に使用できるスロットは1つのみです。継承モデルを使用した場合は、そのスロットが占有されます。Tieモデルを使用すると、そのスロットが使用されず、ユーザーが独自の目的で使用できます。この方法には、間接性のレベルが1つ導入されるという短所があります。メソッドを呼び出すときに、余分なメソッド呼出しが1回発生します。
.PP
サーバー側の生成の場合、Java SE 1\&.4より前のバージョンのJava言語にマッピングするIDLのバージョンと互換性のある、Tieモデルのバインディングです。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBidlj \-oldImplBase \-fall My\&.idl\fR
\fBidlj \-oldImplBase \-fallTIE My\&.idl\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBMy\fRインタフェースの場合、これにより、\fBMy_Tie\&.java\fRが生成されます。\fBMy_Tie\fRクラスへのコンストラクタは、\fBimpl\fRオブジェクトを取ります。\fBimpl\fRに対して実装を提供する必要がありますが、その実装は\fBHelloOperations\fRインタフェースから継承する必要があるのみで、その他のクラスから継承する必要はありません。しかし、これをORBと一緒に使用するには、たとえば次のように\fBMy_Tie\fR内で実装をラップする必要があります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBORB orb = ORB\&.init(args, System\&.getProperties());\fR
 
\fB// create servant and register it with the ORB\fR
\fBMyServant myDelegate = new MyServant();\fR
\fBmyDelegate\&.setORB(orb); \fR
\fB \fR
\fB// create a tie, with servant being the delegate\&.\fR
\fBMyPOATie tie = new MyPOATie(myDelegate);\fR
\fB \fR
\fB// obtain the objectRef for the tie\fR
\fBMy ref = tie\&._this(orb);\fR
 
.fi
.if n \{\
.RE
.\}
.SS "発行されたファイルの代替位置の指定"
.PP
発行されたファイルを現在のディレクトリ以外のディレクトリに置くには、\fBi\fR\fBdlj \-td /altdir My\&.idl\fRのコマンドでコンパイラを呼び出します。
.PP

\fBMy\fRインタフェースの場合、バインディングは、\fB\&./My\&.java\fRではなく、\fB/altdir/My\&.java\fRなどに発行されます。
.SS "インクルード・ファイルの代替位置の指定"
.PP
\fBMy\&.idl\fRファイルが別の\fBidl\fRファイルである\fBMyOther\&.idl\fRをインクルードする場合、コンパイラでは、\fBMyOther\&.idl\fRファイルがローカル・ディレクトリに存在することを前提としています。たとえば、それが\fB/includes\fRにある場合は、次のようなコマンドでコンパイラを呼び出します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBidlj \-i /includes My\&.idl\fR
 
.fi
.if n \{\
.RE
.\}
.PP
たとえば、\fB/moreIncludes\fRにある\fBAnother\&.idl\fRも\fBMy\&.idl\fRにインクルードされているのであれば、次のようなコマンドでコンパイラを呼び出します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBidlj \-i /includes \-i /moreIncludes My\&.idl\fR
 
.fi
.if n \{\
.RE
.\}
.PP
このような形式の\fBinclude\fRは長くなるため、インクルード・ファイルを検索する場所をコンパイラに指示するための別の方法が用意されています。この方法は、環境変数の考え方と似ています。\fBCLASSPATH\fR変数に一覧表示されているディレクトリ内にidl\&.configという名前のファイルを作成します。その\fBidl\&.config\fRの中に、次のような形式の行を入れます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBincludes=/includes;/moreIncludes\fR
 
.fi
.if n \{\
.RE
.\}
.PP
コンパイラは、このファイルを検索し、インクルード・リストを読み込みます。この例では、ディレクトリの間の区切り文字はセミコロン(;)になっています。この区切り文字は、プラットフォームによって異なります。Windowsプラットフォームではセミコロンを使用し、Solaris、LinuxおよびOS Xプラットフォームではコロンを使用します。
.SS "インクルード・ファイルに対するバインディングの発行"
.PP
デフォルトでは、コマンド行に指定した\fBidl\fRファイルで定義されているインタフェースや構造体などについてのみ、Javaバインディングが生成されます。インクルードされたファイルで定義されている型については生成されません。たとえば、次の2つの\fBidl\fRファイルについて考えてみます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBMy\&.idl file:\fR
\fB \fR
\fB#include <MyOther\&.idl>\fR
\fBinterface My\fR
\fB{\fR
\fB};\fR
\fB \fR
\fBMyOther\&.idl file:\fR
\fB \fR
\fBinterface MyOther\fR
\fB{\fR
\fB};\fR
 
.fi
.if n \{\
.RE
.\}
.PP
デフォルトのルールに関して警告があります。グローバル・スコープに表示される\fB#include\fR文は、前述のとおりに処理されます。これらの\fB#include\fR文は、インポート文と見なすことができます。囲まれたスコープ内に表示される\fB#include\fR文は、本当の意味での\fB#include\fR文として処理されます。つまり、インクルードされたファイルにあるコードが、元のファイルにそのまま表示されているかのように処理され、それに対してJavaバインディングが発行されます。次はその例です。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBMy\&.idl file:\fR
\fB \fR
\fB#include <MyOther\&.idl>\fR
\fBinterface My\fR
\fB{\fR
\fB  #include <Embedded\&.idl>\fR
\fB};\fR
\fB \fR
\fBMyOther\&.idl file:\fR
\fB \fR
\fBinterface MyOther\fR
\fB{\fR
\fB};\fR
\fB \fR
\fBEmbedded\&.idl\fR
\fB \fR
\fBenum E {one, two, three};\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fB idlj My\&.idl \fRを実行して、Javaファイルの次のリストを生成します。インポート文とみなされる\fB#include\fRに定義されていたため、\fBMyOther\&.java\fRは生成されませんでした。ただし、本当の意味での\fB#include\fRで定義されていたため、\fBE\&.java\fRは生成されました。\fBEmbedded\&.idl\fRファイルが\fBMy\fRインタフェースのスコープ内にインクルードされているため、\fBMy\fRのスコープ内(つまり、\fBMyPackage\fR内)に生成されています。\fB\-emitAll\fRフラグを使用した場合、インクルードされたすべてのファイルにあるすべての型が発行されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\&./MyHolder\&.java\fR
\fB\&./MyHelper\&.java\fR
\fB\&./_MyStub\&.java\fR
\fB\&./MyPackage\fR
\fB\&./MyPackage/EHolder\&.java\fR
\fB\&./MyPackage/EHelper\&.java\fR
\fB\&./MyPackage/E\&.java\fR
\fB\&./My\&.java\fR
 
.fi
.if n \{\
.RE
.\}
.SS "パッケージの接頭辞の挿入"
.PP
ABCという名前の会社のために作業していて、次のようなIDLファイルを構築したとしましょう。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBWidgets\&.idl file:\fR
\fB \fR
\fBmodule Widgets\fR
\fB{\fR
\fB  interface W1 {\&.\&.\&.};\fR
\fB  interface W2 {\&.\&.\&.};\fR
\fB};\fR
 
.fi
.if n \{\
.RE
.\}
.PP
IDL\-to\-Javaコンパイラを介してこのファイルを実行した場合、W1およびW2に対するJavaバインディングは、\fBWidgets\fRパッケージ内に格納されます。業界の慣例によると、会社のパッケージは、\fBcom\&.<company name>\fRという名前のパッケージ内に置くことになっています。この慣例に従うには、パッケージ名を\fBcom\&.abc\&.Widgets\fRにする必要があります。このパッケージ接頭辞をWidgetsモジュールに付加するには、次のコマンドを実行します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBidlj \-pkgPrefix Widgets com\&.abc Widgets\&.idl\fR
 
.fi
.if n \{\
.RE
.\}
.PP
Widgets\&.idlをインクルードしているIDLファイルがある場合は、そのコマンドにも\fB\-pkgPrefix\fRフラグが必要です。このフラグを指定しないと、そのIDLファイルは、\fBcom\&.abc\&.Widgets\fRパッケージではなく、\fBWidgets\fRパッケージを検索することになります。
.PP
接頭辞が必要なパッケージがいくつもある場合は、前述のidl\&.configファイルで接頭辞を指定するのが簡単です。各パッケージの接頭辞行は、\fBPkgPrefix\&.<type>=<prefix>\fRの形式にする必要があります。前述の例の行では、\fBPkgPrefix\&.Widgets=com\&.abc\fRになります。このオプションは、リポジトリIDには影響しません。
.SS "コンパイル前のシンボルの定義"
.PP
コンパイル用のシンボルがIDLファイル内で定義されていない場合は、そのシンボルを定義する必要があります。これは、たとえば、バインディング内にデバッグ・コードを組み入れるときに使用します。コマンド\fBidlj \-d MYDEF My\&.idl \fRは、My\&.idl内に行\fB#define MYDEF\fRを配置した場合と同等になります。
.SS "既存のバインディングの保持"
.PP
Javaバインディング・ファイルがすでに存在する場合は、\fB\-keep\fRフラグを指定すると、コンパイラによる上書きを回避できます。デフォルトでは、すでに存在するかどうかにかかわらず、すべてのファイルが生成されます。これらのファイルをカスタマイズした場合(ただし、それらの内容が正確であるとき以外はカスタマイズは避ける)、\fB\-keep\fRオプションは有用です。コマンド\fBidlj \-keep My\&.idl\fRは、すでに存在しないすべてのクライアント側バインディングを発行します。
.SS "コンパイルの進捗状況の表示"
.PP
IDL\-to\-Javaコンパイラは、実行の各段階で状態メッセージを生成します。\fB\-v\fRオプションを使用して、\fBidlj \-v My\&.idl\fRのような冗長モードをアクティブ化します。
.PP
デフォルトでは、コンパイラは冗長モードでは実行されません。
.SS "バージョン情報の表示"
.PP
IDL\-to\-Javaコンパイラのビルド・バージョンを表示するには、コマンドライン\fBidlj \-version\fRで\fB\-version\fRオプションを指定します。
.PP
バージョン情報は、コンパイラによって生成されたバインディング内にも書き込まれています。このオプションをコマンドラインに指定すると、それ以外のオプションを指定しても、すべて無視されます。
.SH "オプション"
.PP
\-d \fIsymbol\fR
.RS 4
このオプションは、IDLファイルに次のような行を追加した場合と等価です。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB#define \fR\fB\fIsymbol\fR\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-demitAll
.RS 4
\fB#include\fRファイル内で定義されているものも含めて、すべての型を発行します。
.RE
.PP
\-fside
.RS 4
発行するバインディングを定義します。\fBside\fRパラメータには、\fBclient\fR、\fBserver\fR、\fBserverTIE\fR、\fBall\fRまたは\fBallTIE\fRを指定できます。\fB\-fserverTIE\fRまたは\fB\-fallTIE\fRオプションを指定すると、委譲モデル・スケルトンが発行されます。フラグを指定しない場合、\fB\-fclient\fRにデフォルト設定されます。
.RE
.PP
\-i \fIinclude\-path\fR
.RS 4
デフォルトでは、インクルード・ファイルは現在のディレクトリから検索されます。このオプションを指定すると、他のディレクトリを追加できます。
.RE
.PP
\-i \fIkeep\fR
.RS 4
生成されるファイルがすでに存在している場合は、そのファイルが上書きされません。デフォルトでは、上書きされます。
.RE
.PP
\-noWarn
.RS 4
警告メッセージを表示しないようにします。
.RE
.PP
\-oldImplBase
.RS 4
1\&.4より前のJDK ORBと互換性のあるスケルトンを生成します。デフォルトでは、POA継承モデルのサーバー側バインディングが生成されます。このオプションを指定すると、\fBImplBase\fR継承モデルのクラスであるサーバー側バインディングが生成されるので、以前のリリースのJavaプログラミング言語との後方互換性が得られます。
.RE
.PP
\-pkgPrefix \fItype\fR \fIprefix\fR
.RS 4
\fBtype\fRがファイル・スコープで検出された場合は、その型に対して生成されるすべてのファイルについて、生成されるJavaパッケージ名に\fBprefix\fRという接頭辞が付加されます。typeは、トップレベル・モジュールの単純名か、どのモジュールよりも外側で定義されたIDL型の単純名のどちらかです。
.RE
.PP
\-pkgTranslate \fItype\fR \fIpackage\fR
.RS 4
識別子の中にモジュール名typeが検出されると、生成されるJavaパッケージ内のすべてのファイルについて、識別子の中のその名前がpackageで置き換えられます。最初に\fBpkgPrefix\fRの変更が行われます。typeの値は、トップレベルのモジュールの単純名、またはすべてのモジュールの外部で定義されたIDL型の単純名で、完全なパッケージ名に正確に一致する必要があります。
.sp
複数の変換が識別子に一致する場合、次の例に示すように、最も長い一致が選択されます。
.sp
\fBコマンド\fR:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpkgTranslate type pkg \-pkgTranslate type2\&.baz pkg2\&.fizz\fR
 
.fi
.if n \{\
.RE
.\}
\fB結果の変換\fR:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBtype => pkg\fR
\fBtype\&.ext => pkg\&.ext\fR
\fBtype\&.baz => pkg2\&.fizz\fR
\fBtype2\&.baz\&.pkg => pkg2\&.fizz\&.pkg\fR
 
.fi
.if n \{\
.RE
.\}
パッケージ名\fBorg\fR、\fBorg\fR\&.o\fBmg\fR、または\fBorg\&.omg\fRのサブパッケージは、変換できません。これらのパッケージ名を変換しようとすると、互換性のないコードが生成され、\fB\-pkgTranslate\fRの後の最初の引数としてそれらのパッケージを使用すると、エラーとして扱われます。
.RE
.PP
\-skeletonName \fIxxx%yyy\fR
.RS 4
\fBxxx%yyy\fRが、スケルトンに名前を付けるパターンとして使用されます。デフォルトは次のとおりです。\fBPOA\fRベース・クラスの場合は\fB%POA\fR
(\fB\-fserver\fRまたは\fB\-fall\fR)、\fBoldImplBase\fRクラスの場合は\fB_%ImplBase\fR
(\-\fBoldImplBase\fR)および(\fB\-fserver\fRまたは\fB\-fall\fR))。
.RE
.PP
\-td \fIdir\fR
.RS 4
出力ディレクトリとして、現在のディレクトリではなく、\fIdir\fRが使用されます。
.RE
.PP
\-tieName \fIxxx%yyy\fR
.RS 4
パターンに従って、\fBxxx%yyy\fRを使用します。デフォルトは次のとおりです。\fBPOA\fRベース・クラスの場合は\fB%POA\fR
(\fB\-fserverTieまたは\-fallTie\fR)、\fBoldImplBase\fR
tieクラスの場合は\fB_%Tie\fR
(\-\fBoldImplBase\fR)および(\fB\-fserverTie\fRまたは\fB\-fallTie\fR))。
.RE
.PP
\-nowarn、\-verbose
.RS 4
リリース情報を表示して終了します。
.RE
.PP
\-version
.RS 4
リリース情報を表示して終了します。
.RE
.SH "制限事項"
.PP
グローバル・スコープ内のエスケープされた識別子は、IDLプリミティブ型の\fBObject\fRまたは\fBValueBase\fRと同じ綴りにしないでください。これは、シンボル表がこれらの識別子でプリロードされているためです。これらの再定義を許可すると、元の定義が上書きされます。これは、おそらく恒久的な制約です。
.PP
\fBfixed\fRというIDL型はサポートされていません。
.SH "既知の問題"
.PP
グローバル識別子についてインポートが生成されません。予期されないローカル\fBimpl\fRオブジェクトを呼び出すと、例外を受け取ります。しかし、その原因は、\fBServerDelegate\fR
DSIコード内の\fBNullPointerException\fRにあるようです。
.br
'pl 8.5i
'bp
