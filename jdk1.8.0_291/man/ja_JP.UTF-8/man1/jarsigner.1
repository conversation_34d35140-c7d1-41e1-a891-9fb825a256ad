'\" t
.\" Copyright (c) 1998, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jarsigner
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: セキュリティ・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jarsigner" "1" "2013年11月21日" "JDK 8" "セキュリティ・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jarsigner \- Javaアーカイブ(JAR)ファイルに対して署名および検証を行います。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjarsigner\fR [ \fIoptions\fR ] \fIjar\-file\fR \fIalias\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjarsigner\fR \fB\-verify\fR [ \fIoptions\fR ] \fIjar\-file\fR [\fIalias \&.\&.\&.\fR]
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\-verify
.RS 4
\fB\-verify\fRオプションでは、JARファイル名の後に0個以上のキーストア別名を指定できます。\fB\-verify\fRオプションが指定された場合、\fBjarsigner\fRコマンドでは、JARファイル内の各署名付きエンティティの検証に使用される証明書が、いずれかのキーストア別名に一致することをチェックします。別名は、\fB\-keystore\fRで指定されたキーストア内またはデフォルトのキーストア内に定義されます。
.sp
\fB\-strict\fRオプションも指定した場合、\fBjarsigner\fRコマンドにより重大な警告が検出されると、メッセージ「jarが検証されました。署名者エラー」が表示されます。
.RE
.PP
\fIjar\-file\fR
.RS 4
署名されるJARファイル。
.sp
\fB\-strict\fRオプションも指定した場合、\fBjarsigner\fRコマンドにより重大な警告が検出されると、メッセージ「jarは署名されました \- 署名者エラーがあります。」というメッセージが表示されます。
.RE
.PP
\fIalias\fR
.RS 4
別名は、\fB\-keystore\fRで指定されたキーストア内またはデフォルトのキーストア内に定義されます。
.RE
.SH "説明"
.PP
\fBjarsigner\fRツールには、次の2つの目的があります。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
Javaアーカイブ(JAR)ファイルに署名する目的。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
署名付きJARファイルの署名と整合性を検証する目的。
.RE
.PP
JAR機能を使用すると、クラス・ファイル、イメージ、サウンドおよびその他のデジタル・データを単一のファイルにパッケージ化できるので、ファイルを迅速かつ容易に配布できます。\fBjar\fRという名前のツールを使用して、開発者はJARファイルを作成できます。(技術的な観点から言えば、すべてのZIPファイルもJARファイルとみなすことができます。ただし、\fBjar\fRコマンドによって作成されたJARファイル、または\fBjarsigner\fRコマンドによって処理されたJARファイルには、\fBMETA\-INF/MANIFEST\&.MF\fRファイルも含まれています。)
.PP
デジタル署名は、なんらかのデータ(署名の対象となるデータ)、およびエンティティ(人、会社など)の秘密鍵から計算されるビットの文字列です。手書きの署名同様、デジタル署名には多くの利点があります。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
署名の生成に使用された秘密鍵に対応する公開鍵を使用する計算によって、それが本物であることを検証できます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
秘密鍵が他人に知られない限り、デジタル署名の偽造は不可能です。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
これは、署名が付いたデータの機能であり、他のデータの署名となるように要求できません。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
署名付きデータは変更できません。データが変更された場合、署名によって本物であると検証できません。
.RE
.PP
ファイルに対してエンティティの署名を生成するには、まず、エンティティは、そのエンティティに関連する公開鍵/秘密鍵のペアと、公開鍵を認証する1つ以上の証明書を持つ必要があります。証明書とは、あるエンティティからのデジタル署名付きの文書で、別のエンティティの公開鍵が特定の値を持つことを示しています。
.PP
\fBjarsigner\fRコマンドは、キーストアからの鍵と証明書情報を使用して、JARファイルのデジタル署名を生成します。キーストアは、秘密鍵、および対応する公開鍵を認証する、秘密鍵に関連したX\&.509証明書チェーンのデータベースです。\fBkeytool\fRコマンドを使用して、キーストアを作成および管理します。
.PP
\fBjarsigner\fRコマンドでは、エンティティの秘密鍵を使用して署名を生成します。署名付きJARファイルには、特に、ファイルへの署名に使用する秘密鍵に対応する公開鍵のキーストアからの証明書のコピーが含まれます。\fBjarsigner\fRコマンドでは、内部(署名ブロック・ファイル内)の証明書を使用して、署名付きJARファイルのデジタル署名を検証できます。
.PP
\fBjarsigner\fRコマンドでは、署名証明書の有効期間中にJARファイルが署名されたされたかどうかをシステムやデプロイヤ(Java Plug\-inを含む)がチェックできる、タイムスタンプを含む署名を生成できます。さらに、APIを使用すると、アプリケーションでタイムスタンプ情報を取得できます。
.PP
現時点では、\fBjarsigner\fRコマンドでは、\fBjar\fRコマンドまたはZIPファイルによって作成されたJARファイルのみに署名できます。JARファイルはZIPファイルと同じですが、JARファイルには\fBMETA\-INF/MANIFEST\&.MF\fRファイルも含まれている点が異なります。\fBMETA\-INF/MANIFEST\&.MF\fRファイルは、\fBjarsigner\fRコマンドによってZIPファイルに署名する際に作成されます。
.PP
デフォルトの\fBjarsigner\fRコマンドの動作では、JARまたはZIPファイルに署名します。\fB\-verify\fRオプションを使用して、署名付きJARファイルを検証します。
.PP
\fBjarsigner\fRコマンドは、署名または検証の後に署名者の証明書の検証も試行します。検証エラーまたはその他の問題が発生すると、コマンドにより警告メッセージが生成されます。\fB\-strict\fRオプションを指定した場合、コマンドにより重大な警告がエラーとして処理されます。エラーと警告を参照してください。
.SS "キーストアの別名"
.PP
キーストアのすべてのエンティティは、一意の別名を使用してアクセスされます。
.PP
\fBjarsigner\fRコマンドを使用してJARファイルに署名するときは、署名の生成に必要な秘密鍵を含むキーストア・エントリの別名を指定する必要があります。たとえば、次のコマンドでは、\fBworking\fRディレクトリの\fBmystore\fRという名前のキーストアに含まれる別名\fBduke\fRに関連付けられた秘密鍵を使用して、\fBMyJARFile\&.jar\fRという名前のJARファイルに署名します。出力ファイルは指定されていないため、\fBMyJARFile\&.jar\fRは署名付きのJARファイルによって上書きされます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjarsigner \-keystore /working/mystore \-storepass <keystore password>\fR
\fB      \-keypass <private key password> MyJARFile\&.jar duke\fR
 
.fi
.if n \{\
.RE
.\}
.PP
キーストアはパスワードで保護されているので、ストアのパスワードを指定する必要があります。コマンド行で指定しないと、入力を求められます。同様に、秘密鍵もキーストア内でパスワードによって保護されているため、秘密鍵のパスワードを指定する必要があります。コマンド行でパスワードを指定していない場合、または指定したパスワートが保存されているパスワードと同じではない場合は、パスワードの入力を求められます。
.SS "キーストアの場所"
.PP
\fBjarsigner\fRコマンドには、使用するキーストアのURLを指定する\fB\-keystore\fRオプションがあります。キーストアはデフォルトで、\fBuser\&.home\fRシステム・プロパティで決まるユーザーのホーム・ディレクトリの\fB\&.keystore\fRという名前のファイル内に格納されます。
.PP
Oracle Solarisシステムの場合、\fBuser\&.home\fRは、ユーザーのホーム・ディレクトリにデフォルト設定されます。
.PP
\fB\-keystore\fRオプションからの入力ストリームは、\fBKeyStore\&.load\fRメソッドに渡されます。URLとして\fBNONE\fRが指定されている場合は、nullのストリームが\fBKeyStore\&.load\fRメソッドに渡されます。\fBNONE\fRは、\fBKeyStore\fRクラスがファイルベースではない場合、たとえば、ハードウェア・トークン・デバイスに置かれている場合に指定します。
.SS "キーストアの実装"
.PP
\fBjava\&.security package\fRで提供されている\fBKeyStore\fRクラスは、キーストア内の情報へのアクセスおよび情報の変更を行うための、明確に定義された多くのインタフェースを提供します。複数の異なる固定実装を指定することができ、各実装は特定のタイプのキーストアを対象とします。
.PP
現在、キーストアの実装を使用する2つのコマンド行ツール(\fBkeytool\fRと\fBjarsigner\fR)、およびポリシー・ツールという名前の1つのGUIベースのツールがあります。\fBKeyStore\fRクラスは公開されているため、JDKユーザーは、それを使用する他のセキュリティ・アプリケーションを書き込むことができます。
.PP
Oracleが提供する組込みのデフォルトの実装があります。これは、JKSという名前の独自のキーストア・タイプ(形式)を使用するもので、キーストアをファイルとして実装しています。組込みの実装では、各秘密鍵は個別のパスワードによって保護され、キーストア全体の整合性は(秘密鍵とは別の)パスワードによって保護されます。
.PP
キーストアの実装はプロバイダベースで、つまり、\fBKeyStore\fRクラスにより提供されるアプリケーション・インタフェースは、サービス・プロバイダ・インタフェース(SPI)に関して実装されます。対応する\fBKeystoreSpi\fR抽象クラスがあり、これも\fBjava\&.security package\fRに含まれています。このクラスが、プロバイダが実装する必要のあるサービス・プロバイダ・インタフェースのメソッドを定義しています。ここで、プロバイダとは、Java Security APIによってアクセス可能なサービスのサブセットに対し、その固定実装を提供するパッケージまたはパッケージの集合のことです。キーストアの実装を提供するには、http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/crypto/HowToImplAProvider\&.htmlにある
Java暗号化アーキテクチャのプロバイダの実装方法で説明しているように、クライアントはプロバイダを実装し、\fBKeystoreSpi\fRサブクラスの実装を提供する必要があります。
.PP
アプリケーションでは、\fBKeyStore\fRクラスの\fBgetInstance\fRファクトリ・メソッドを使用して、様々なプロバイダから異なるタイプのキーストアの実装を選択できます。キーストアのタイプは、キーストア情報の格納形式とデータ形式を定義するとともに、キーストア内の秘密鍵とキーストア自体の整合性を保護するために使用されるアルゴリズムを定義します。異なるタイプのキーストアの実装には、互換性はありません。
.PP
\fBjarsigner\fRおよび\fBpolicytool\fRコマンドは、URLを使用して指定できる任意の場所からファイルベースのキーストアを読み取ることができます。また、これらのコマンドは、Windows上のMSCAPIおよびすべてのプラットフォーム上のPKCS11で提供されるような非ファイルベースのキーストアを読み取ることができます。
.PP
\fBjarsigner\fRコマンドおよび\fBkeytool\fRコマンドの場合、\fB\-storetype\fRオプションを使用して、コマンド行でキーストアのタイプを指定できます。ポリシー・ツールの場合、\fB「キーストア」\fRメニューの\fB「編集」\fRコマンドを使用して、キーストアのタイプを指定できます。
.PP
ユーザーがキーストアのタイプを明示的に指定しなかった場合、セキュリティ・プロパティ・ファイルで指定された\fBkeystore\&.type\fRプロパティの値に基づいて、ツールによってキーストアの実装が選択されます。このセキュリティ・プロパティ・ファイルは\fBjava\&.security\fRと呼ばれ、JDKセキュリティ・プロパティ・ディレクトリ\fBjava\&.home/lib/security\fR内に存在しています。ここで、\fBjava\&.home\fRは実行時環境のディレクトリです。\fBjre\fRディレクトリは、JDKまたはJava Runtime Environment (JRE)の最上位のディレクトリにあります。
.PP
各ツールは、\fBkeystore\&.type\fRの値を取得し、そのタイプのキーストアを実装しているプロバイダが見つかるまで、インストールされているすべてのプロバイダを調べます。目的のプロバイダが見つかると、そのプロバイダからのキーストアの実装を使用します。
.PP
\fBKeyStore\fRクラスに定義されているstaticメソッド\fBgetDefaultType\fRを使用すると、アプリケーションやアプレットから\fBkeystore\&.type\fRプロパティの値を取得できます。次のコードの行では、\fBkeystore\&.type property\fRで指定された、デフォルトのキーストア・タイプのインスタンスを作成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBKeyStore keyStore = KeyStore\&.getInstance(KeyStore\&.getDefaultType());\fR
 
.fi
.if n \{\
.RE
.\}
.PP
デフォルトのキーストア・タイプは、\fBjks\fR
(Oracleが提供する独自のタイプのキーストアの実装)です。これは、セキュリティ・プロパティ・ファイル内の次の行によって指定されています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeystore\&.type=jks\fR
 
.fi
.if n \{\
.RE
.\}
.PP
キーストアのタイプの指定では、大文字と小文字は区別されません。たとえば、\fBJKS\fRは\fBjks\fRと同じになります。
.PP
ツールでデフォルト以外のキーストアの実装を使用するには、その行を変更して別のキーストアのタイプを指定します。たとえば、\fBpkcs12\fRと呼ばれるキーストアのタイプのキーストアの実装を提供するプロバイダ・パッケージがある場合、行を次のように変更します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeystore\&.type=pkcs12\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fB注意:\fR
PKCS 11プロバイダ・パッケージを使用する場合、http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/p11guide\&.htmlにある
Java PKCS #11リファレンス・ガイドの「KeyTool」および「JarSigner」を参照してください。
.SS "サポートされるアルゴリズム"
.PP
デフォルトで、\fBjarsigner\fRコマンドでは次のいずれかのアルゴリズムを使用してJARファイルに署名します。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
SHA1ダイジェスト・アルゴリズムを使用したデジタル署名アルゴリズム(DSA)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
SHA256ダイジェスト・アルゴリズムを使用したRSAアルゴリズム。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
SHA256と楕円曲線デジタル署名アルゴリズム(ECDSA)を使用した楕円曲線(EC)暗号方式アルゴリズム
.RE
.PP
署名者の公開鍵と秘密鍵がDSA鍵である場合、\fBjarsigner\fRは\fBSHA1withDSA\fRアルゴリズムを使用してJARファイルに署名します。署名者の鍵がRSA鍵である場合、\fBjarsigner\fRは\fBSHA256withRSA\fRアルゴリズムを使用してJARファイルに署名しようとします。署名者の鍵がEC鍵である場合、\fBjarsigner\fRは\fBSHA256withECDSA\fRアルゴリズムを使用してJARファイルに署名します。
.PP
これらのデフォルトの署名アルゴリズムは、\fB\-sigalg\fRオプションを使用してオーバーライドできます。
.SS "署名付きJARファイル"
.PP
\fBjarsigner\fRコマンドを使用してJARファイルに署名する場合、出力される署名付きJARファイルは入力JARファイルと同じですが、次の2つの追加ファイルがMETA\-INFディレクトリに置かれる点が異なります。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\&.SF\fR拡張子の付いた署名ファイル
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\&.DSA\fR、\fB\&.RSA\fRまたは\fB\&.EC\fR拡張子の付いた署名ブロック・ファイル
.RE
.PP
これら2つのファイルのベース・ファイル名は、\fB\-sigFile\fRオプションの値から作成されます。たとえば、オプションが\fB\-sigFile MKSIGN\fRの場合、ファイルは\fBMKSIGN\&.SF\fRおよび\fBMKSIGN\&.DSA\fRという名前になります。
.PP
コマンドラインで\fB\-sigfile\fRオプションを指定しなかった場合、\fB\&.SF\fRファイルと\fB\&.DSA\fRファイルのベース・ファイル名は、コマンドラインで指定された別名の先頭の8文字をすべて大文字に変換したものになります。別名が8文字未満の場合は、別名がそのまま使用されます。別名に、署名ファイル名で使用できない文字が含まれている場合、ファイル名の作成時に、該当する文字が下線(_)文字に変換されます。有効な文字は、アルファベット、数字、下線およびハイフンです。
.PP
署名ファイル
.PP
署名ファイル(\fB\&.SF\fRファイル)は、\fBjarsigner\fRコマンドを使用してファイルに署名する際にJARファイルに常に含まれるマニフェスト・ファイルと似ています。JARファイルに含まれているソース・ファイルごとに、\fB\&.SF\fRファイルには、マニフェスト・ファイルにあるような、次に示す3つの行があります。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
ファイル名
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
ダイジェスト・アルゴリズム(SHA)の名前
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
SHAダイジェストの値
.RE
.PP
マニフェスト・ファイルでは、各ソース・ファイルのSHAダイジェストの値は、ソース・ファイルのバイナリ・データのダイジェスト(ハッシュ)になります。\fB\&.SF\fRファイルでは、指定されたソース・ファイルのダイジェストの値は、ソース・ファイルのマニフェスト・ファイル内のその3行のハッシュになります。
.PP
署名ファイルには、デフォルトで、マニフェスト・ファイル全体のハッシュが格納されたヘッダーが含まれています。ヘッダーにはマニフェスト・ヘッダーのハッシュも含まれています。ヘッダーが存在すると、検証の最適化が有効になります。JARファイルの検証を参照してください。
.PP
署名ブロック・ファイル
.PP
\fB\&.SF\fRファイルには署名が付けられ、署名は署名ブロック・ファイルに置かれます。このファイルには、署名に使用された秘密鍵に対応する公開鍵を認証するキーストアからの証明書または証明書チェーンも、内部でエンコードされて含まれています。ファイルの拡張子は、使用されるダイジェスト・アルゴリズムに応じて、\fB\&.DSA\fR、\fB\&.RSA\fRまたは\fB\&.EC\fRになります。
.SS "署名タイムスタンプ"
.PP
\fBjarsigner\fRコマンドでは、JARファイルの署名時に署名タイムスタンプを生成および保存できます。さらに、\fBjarsigner\fRは代替署名機構をサポートします。この動作は省略可能で、署名時に次の各オプションによって制御されます。オプションを参照してください。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-tsa \fR\fB\fIurl\fR\fR
\fB\-tsacert \fR\fB\fIalias\fR\fR
\fB\-altsigner \fR\fB\fIclass\fR\fR
\fB\-altsignerpath \fR\fB\fIclasspathlist\fR\fR
\fB\-tsapolicyid \fR\fB\fIpolicyid\fR\fR
 
.fi
.if n \{\
.RE
.\}
.SS "JARファイルの検証"
.PP
JARファイルの検証が成功するのは、署名が有効であり、かつ署名の生成以降にJARファイル内のどのファイルも変更されていない場合です。JARファイルの検証は、次の手順で行われます。
.sp
.RS 4
.ie n \{\
\h'-04' 1.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  1." 4.2
.\}
\fB\&.SF\fRファイルの署名を検証します。
.sp
検証では、各署名ブロック(\fB\&.DSA\fR)ファイルに格納された署名が、証明書(または証明書チェーン)も\fB\&.DSA\fRファイルに示される公開鍵に対応する秘密鍵を使用して生成されたことを確認します。また、署名が対応する署名(\fB\&.SF\fR)ファイルの有効な署名であることが確認され、それにより、\fB\&.SF\fRファイルが改ざんされていないことも確認されます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 2.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  2." 4.2
.\}
\fB\&.SF\fRファイル内の各エントリに示されるダイジェストを、マニフェスト内の対応する各セクションと突きあわせて検証します。
.sp
\fB\&.SF\fRファイルには、マニフェスト・ファイル全体のハッシュが格納されたヘッダーがデフォルトで含まれています。ヘッダーが存在する場合、検証では、ヘッダー内のハッシュがマニフェスト・ファイルのハッシュと一致するかどうかを確認するためにチェックできます。一致する場合、検証は次の手順に進みます。
.sp
一致しない場合、\fB\&.SF\fRファイル内の各ソース・ファイル情報セクションのハッシュが、マニフェスト・ファイル内の対応するセクションのハッシュと一致することを確認するために、あまり最適化されていない検証が必要になります。署名ファイルを参照してください。
.sp
\fB\&.SF\fRファイルのヘッダーに格納されたマニフェスト・ファイルのハッシュが、現在のマニフェスト・ファイルのハッシュに一致しない理由の1つは、署名および\fB\&.SF\fRファイルの生成後に、(\fBjar\fRツールを使用して)1つ以上のファイルがJARファイルに追加されたことです。\fBjar\fRツールを使用してファイルを追加した場合、新しいファイル用のセクションが追加されることにより、マニフェスト・ファイルは変更されますが、\fB\&.SF\fRファイルは変更されません。検証がまだ成功しているとみなされるのは、署名の生成以降にJARファイル内のどのファイルも変更されていない場合です。これが発生するのは、\fB\&.SF\fRファイルのヘッダー以外のセクションのハッシュが、マニフェスト・ファイル内の対応するセクションのハッシュと一致する場合です。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 3.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  3." 4.2
.\}
\fB\&.SF\fRファイル内にエントリを持つJARファイル内の各ファイルを読み取ります。読取り中にファイルのダイジェストを計算し、結果をマニフェスト・セクション内のこのファイルのダイジェストと比較します。ダイジェストは同じである必要があり、そうでない場合は検証が失敗します。
.sp
検証プロセス中になんらかの重大な検証エラーが発生した場合、そのプロセスは停止され、セキュリティ例外がスローされます。\fBjarsigner\fRコマンドでは、例外を捕捉および表示します。
.RE
.PP
\fB注意:\fR
追加の警告(または、\fB\-strict\fRオプションを指定した場合はエラー)はすべて読む必要があります。同様に、証明が信頼できるかを決定するために、(\fB\-verbose\fRおよび\fB\-certs\fRオプションを指定して)証明書の内容も読む必要があります。
.SS "1つのJARファイルを対象とする複数の署名"
.PP
次のように、\fBjarsigner\fRコマンドをファイルで複数回実行し、実行のたびに異なるユーザーの別名を指定することによって、1つのJARファイルに複数のユーザーの署名を付けることができます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjarsigner myBundle\&.jar susan\fR
\fBjarsigner myBundle\&.jar kevin\fR
 
.fi
.if n \{\
.RE
.\}
.PP
JARファイルが複数回署名されている場合、そのJARファイルには、複数の\fB\&.SF\fRファイルと\fB\&.DSA\fRファイルが含まれており、1回の署名に対して1つのペアとなります。前述の例では、出力JARファイルには、次の名前のファイルが含まれています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBSUSAN\&.SF\fR
\fBSUSAN\&.DSA\fR
\fBKEVIN\&.SF\fR
\fBKEVIN\&.DSA\fR
.fi
.if n \{\
.RE
.\}
.SH "オプション"
.PP
次の各項では、様々な\fBjarsigner\fRオプションについて説明します。次の標準に注意してください。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
どのオプション名にも先頭にマイナス記号(\-)が付きます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
オプションは任意の順序で指定できます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
イタリックまたは下線付きの項目(オプションの値)の実際の値は、指定する必要があります。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fB\-storepass\fR、\fB\-keypass\fR、\fB\-sigfile\fR、\fB\-sigalg\fR、\fB\-digestalg\fR、\fB\-signedjar\fRおよびTSA関連のオプションを使用できるのは、JARファイルに署名する場合のみです。署名付きJARファイルを検証する場合ではありません。\fB\-keystore \fRオプションは、JARファイルの署名および検証に関連します。また、別名は、JARファイルの署名および検証時に指定します。
.RE
.PP
\-keystore \fIurl\fR
.RS 4
キーストアの場所を示すURLを指定します。これにより、\fBuser\&.home\fRシステム・プロパティで決定されたユーザーのホーム・ディレクトリ内のファイル\fB\&.keystore\fRにデフォルト設定されます。
.sp
キーストアは署名時には必要です。デフォルトのキーストアが存在しない場合、またはデフォルト以外のキーストアを使用する場合は、キーストアを明示的に指定する必要があります。
.sp
検証するときはキーストアは必要ありません。ただし、キーストアが指定されているか、あるいはデフォルトのキーストアが存在していて、さらに\fB\-verbose\fRオプションも指定されていた場合、JARファイルの検証に使用される証明書がそのキーストアに1つでも含まれているかどうかに関する追加情報が出力されます。
.sp
\fB\-keystore\fR引数には、URLではなくファイル名とパスを指定でき、この場合、ファイル: URLと同じように処理されます。たとえば、次にように指定すると同等になります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-keystore \fR\fB\fIfilePathAndName\fR\fR
\fB\-keystore file:\fR\fB\fIfilePathAndName\fR\fR
 
.fi
.if n \{\
.RE
.\}
(JREの\fB$JAVA_HOME/lib/security directory\fRにある)
\fBjava\&.security\fRセキュリティ・プロパティ・ファイル内でSun PKCS #11プロバイダが構成された場合、\fBkeytool\fRおよび\fBjarsigner\fRツールはPKCS#11トークンに基づいて動作できます。次のオプションを指定します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-keystore NONE\fR
\fB\-storetype PKCS11\fR
 
.fi
.if n \{\
.RE
.\}
たとえば、次のコマンドは、構成されたPKCS#11トークンの内容を一覧表示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBkeytool \-keystore NONE \-storetype PKCS11 \-list\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-storetype \fIstoretype\fR
.RS 4
インスタンスを生成するキーストアのタイプを指定します。デフォルトのキーストア・タイプは、セキュリティ・プロパティ・ファイル内の\fBkeystore\&.type\fRプロパティの値で指定されたタイプです。この値は、\fBjava\&.security\&.KeyStore\fRのstatic
\fBgetDefaultType\fRメソッドによって返されます。
.sp
\fB\-storepass\fRオプションを使用して、PCKS #11トークンのPINを指定することもできます。何も指定しない場合、\fBkeytool\fRおよび\fBjarsigner\fRコマンドによって、トークンPINの指定を求められます。トークンに保護された認証パス(専用のPINパッドや生体読取り機など)がある場合、\fB\-protected\fRオプションを指定する必要がありますが、パスワード・オプションは指定できません。
.RE
.PP
\-storepass[:env | :file] \fIargument\fR
.RS 4
キーストアにアクセスするのに必要なパスワードを指定します。これが必要なのは、JARファイルに署名を付けるときのみです(検証するときには不要です)。その場合、\fB\-storepass\fRオプションをコマンド行で指定しないと、パスワードの入力を求められます。
.sp
修飾子\fBenv\fRまたは\fBfile\fRが指定されていない場合、パスワードの値は\fIargument\fRになります。それ以外の場合、パスワードは次のようにして取得されます。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBenv\fR:
\fBargument\fRという名前の環境変数からパスワードを取得します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBfile\fR:
\fBargument\fRという名前のファイルからパスワードを取得します。
.RE
.sp
\fB注意:\fR
テストを目的とする場合またはセキュアなシステムを使用している場合以外は、コマンド行やスクリプトでパスワードを指定しないでください。
.RE
.PP
\-keypass [:env | :file] \fIargument\fR
.RS 4
コマンド行で指定された別名に対応するキーストア・エントリの秘密鍵を保護するのに使用するパスワードを指定します。\fBjarsigner\fRを使用してJARファイルに署名を付けるときは、パスワードが必要です。コマンド行でパスワードが指定されておらず、必要なパスワードがストアのパスワードと異なる場合は、パスワードの入力を求められます。
.sp
修飾子\fBenv\fRまたは\fBfile\fRが指定されていない場合、パスワードの値は\fBargument\fRになります。それ以外の場合、パスワードは次のようにして取得されます。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBenv\fR:
\fBargument\fRという名前の環境変数からパスワードを取得します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBfile\fR:
\fBargument\fRという名前のファイルからパスワードを取得します。
.RE
.sp
\fB注意:\fR
テストを目的とする場合またはセキュアなシステムを使用している場合以外は、コマンド行やスクリプトでパスワードを指定しないでください。
.RE
.PP
\-sigfile \fIfile\fR
.RS 4
生成された\fB\&.SF\fRファイルおよび\fB\&.DSA\fRファイルに使用するベース・ファイル名を指定します。たとえば、ファイルが\fBDUKESIGN\fRの場合、生成される\fB\&.SF\fRおよび\fB\&.DSA\fRファイルは、\fBDUKESIGN\&.SF\fRおよび\fBDUKESIGN\&.DSA\fRという名前で、署名付きJARファイルの\fBMETA\-INF\fRディレクトリに格納されます。
.sp
ファイル内の文字は、セット\fBa\-zA\-Z0\-9_\-\fRから指定される必要があります。アルファベット、数字、下線およびハイフン文字のみを使用できます。\fB\&.SF\fRおよび\fB\&.DSA\fRのファイル名では、小文字はすべて大文字に変換されます。
.sp
コマンド行で\fB\-sigfile\fRオプションを指定しなかった場合、\fB\&.SF\fRファイルと\fB\&.DSA\fRファイルのベース・ファイル名は、コマンド行で指定された別名の先頭の8文字をすべて大文字に変換したものになります。別名が8文字未満の場合は、別名がそのまま使用されます。別名に、署名ファイル名で無効な文字が含まれている場合、ファイル名を作成するために、該当する文字が下線(_)文字に変換されます。
.RE
.PP
\-sigalg \fIalgorithm\fR
.RS 4
JARファイルの署名に使用する署名アルゴリズムの名前を指定します。
.sp
標準的な署名アルゴリズム名のリストは、http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/crypto/CryptoSpec\&.html#AppAにある
Java Cryptography Architecture (JCA)リファレンス・ガイドの「付録A: 標準名」を参照してください。
.sp
このアルゴリズムには、JARファイルの署名に使用する秘密鍵との互換性が必要です。このオプションを指定しない場合、秘密鍵のタイプに応じて、\fBSHA1withDSA\fR、\fBSHA256withRSA\fRまたは\fBSHA256withECDSA\fRが使用されます。指定されたアルゴリズムの実装を提供するプロバイダが静的にインストールされているか、\fB\-providerClass\fRオプションを使用してそのようなプロバイダをユーザーが指定する必要があります。そうでない場合、コマンドは失敗します。
.RE
.PP
\-digestalg \fIalgorithm\fR
.RS 4
JARファイルのエントリをダイジェストする際に使用するメッセージ・ダイジェスト・アルゴリズムの名前を指定します。
.sp
標準的なメッセージ・ダイジェスト・アルゴリズム名のリストは、http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/crypto/CryptoSpec\&.html#AppAにある
Java Cryptography Architecture (JCA)リファレンス・ガイドの「付録A: 標準名」を参照してください。
.sp
このオプションを指定しない場合、\fBSHA256\fRが使用されます。指定されたアルゴリズムの実装を提供するプロバイダが静的にインストールされているか、\fB\-providerClass\fRオプションを使用してそのようなプロバイダをユーザーが指定する必要があります。そうでない場合、コマンドは失敗します。
.RE
.PP
\-certs
.RS 4
コマンド行で、\fB\-certs\fRオプションを\fB\-verify\fRおよび\fB\-verbose\fRオプションとともに指定した場合、JARファイルの各署名者の証明書情報が出力に含まれます。この情報には、署名者の公開鍵を証明する証明書(\fB\&.DSA\fRファイルに格納)のタイプの名前が含まれ、証明書がX\&.509証明書(\fBjava\&.security\&.cert\&.X509Certificate\fRのインスタンス)の場合、署名者の識別名が含まれます。
.sp
キーストアの確認も行われます。コマンド行でキーストアの値が指定されていない場合、デフォルトのキーストア・ファイル(ある場合)がチェックされます。署名者の公開鍵の証明書がキーストア内のエントリと一致する場合、その署名者のキーストアのエントリの別名が丸カッコ内に表示されます。
.RE
.PP
\-certchain \fIfile\fR
.RS 4
コマンド行で指定した別名によって表されるキーストア・エントリの秘密鍵に関連付けられた証明書チェーンが完全ではない場合に、使用する証明書チェーンを指定します。これは、証明書チェーン全体を保持するのに十分な領域がないハードウェア・トークン上にキーストアが格納されている場合に発生します。このファイルは一連の連結されたX\&.509証明書、PKCS#7形式の単一データ・ブロックのいずれかとなり、そのエンコーディング形式はバイナリ・エンコーディング形式、Internet RFC 1421標準で規定される印刷可能エンコーディング形式(Base64エンコーディングとも呼ばれる)のいずれかになります。インターネットRFC 1421証明書符号化規格およびhttp://tools\&.ietf\&.org/html/rfc1421を参照してください。
.RE
.PP
\-verbose
.RS 4
コマンド行で\fB\-verbose\fRオプションを指定した場合、冗長モードで動作し、このモードでは、\fBjarsigner\fRは、JARの署名または検証の進行状況に関する追加情報を出力します。
.RE
.PP
\-internalsf
.RS 4
以前は、JARファイルの署名時に生成された\fB\&.DSA\fR
(署名ブロック)ファイルに、生成された\fB\&.SF\fRファイル(署名ファイル)のエンコードされた完全なコピーが含まれていました。 この動作は変更されました。出力JARファイル全体のサイズを小さくするために、デフォルトでは\fB\&.DSA\fRファイルには\fB\&.SF\fRファイルのコピーが含まれないようになっています。コマンド行で\fB\-internalsf\fRを指定した場合、以前と同じように動作します。このオプションは、テストを行う場合に便利です。実際には、\fB\-internalsf\fRオプションは、オーバーヘッドが大きくなるため、使用しないでください。
.RE
.PP
\-sectionsonly
.RS 4
コマンド行で\fB\-sectionsonly\fRオプションを指定した場合、JARファイルの署名時に生成される\fB\&.SF\fRファイル(署名ファイル)には、マニフェスト・ファイル全体のハッシュを含むヘッダーは含まれません。これに含まれるのは、JARファイル内の各ソース・ファイルに関連する情報およびハッシュのみです。署名ファイルを参照してください。
.sp
デフォルトでは、最適化のために、このヘッダーが追加されます。ヘッダーが存在する場合、JARファイルを検証するたびに、検証では、まずヘッダー内のハッシュがマニフェスト・ファイル全体のハッシュと一致するかどうかを確認するためにチェックできます。一致する場合、検証は次の手順に進みます。一致しない場合、\fB\&.SF\fRファイル内の各ソース・ファイル情報セクションのハッシュが、マニフェスト・ファイル内の対応するセクションのハッシュと一致するという、あまり最適化されていない検証を実行する必要があります。JARファイルの検証を参照してください。
.sp
\fB\-sectionsonly\fRオプションは、主にテスト用に使用されます。これを使用するとオーバーヘッドが大きくなるため、テスト用以外では使用しないでください。
.RE
.PP
\-protected
.RS 4
\fBtrue\fRまたは\fBfalse\fRのいずれかの値を指定できます。専用PINリーダーなどの保護された認証パスによってパスワードを指定する必要がある場合、\fBtrue\fRを指定します。
.RE
.PP
\-providerClass \fIprovider\-class\-name\fR
.RS 4
暗号化サービス・プロバイダが\fBjava\&.security\fRセキュリティ・プロパティ・ファイルに指定されていないときは、そのマスター・クラス・ファイルの名前を指定するときに使用されます。
.sp
\fB\-providerArg ConfigFilePath\fRオプションとともに使用し、\fBkeytool\fRおよび\fBjarsigner\fRツールは、プロバイダを動的にインストールし、トークン構成ファイルへのパスに\fIConfigFilePath\fRを使用します。次の例は、Oracle PKCS #11プロバイダがセキュリティ・プロパティ・ファイルに構成されていなかった場合に\fBPKCS #11\fRキーストアを一覧表示するコマンドを示しています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjarsigner \-keystore NONE \-storetype PKCS11 \e\fR
\fB          \-providerClass sun\&.security\&.pkcs11\&.SunPKCS11 \e\fR
\fB          \-providerArg /mydir1/mydir2/token\&.config \e\fR
\fB          \-list\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-providerName \fIproviderName\fR
.RS 4
\fBjava\&.security\fRセキュリティ・プロパティ・ファイル内で2つ以上のプロバイダが構成された場合、\fB\-providerName\fRオプションを使用して、特定のプロバイダ・インスタンスを対象にすることができます。このオプションの引数は、プロバイダの名前です。
.sp
Oracle PKCS #11プロバイダの場合、\fIproviderName\fRは\fBSunPKCS11\-\fR\fITokenName\fRという形式になります。ここで、構成属性の表で詳細に説明されているように、\fITokenName\fRは、プロバイダ・インスタンスが構成された名前の接尾辞です。たとえば、次のコマンドは、名前接尾辞\fBSmartCard\fRの\fBPKCS #11\fRキーストア・プロバイダ・インスタンスの内容を一覧表示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjarsigner \-keystore NONE \-storetype PKCS11 \e\fR
\fB        \-providerName SunPKCS11\-SmartCard \e\fR
\fB        \-list\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-J\fIjavaoption\fR
.RS 4
指定された\fIjavaoption\fR文字列をJavaインタプリタに直接渡します。\fBjarsigner\fRコマンドは、インタプリタに対するラッパーです。このオプションには、空白を含めることはできません。このオプションは、実行環境またはメモリー使用を調整する場合に便利です。指定できるインタプリタ・オプションを一覧表示するには、コマンドラインで\fBjava \-h\fRまたは\fBjava \-X\fRと入力してください。
.RE
.PP
\-tsa \fIurl\fR
.RS 4
\fB\-tsa http://example\&.tsa\&.url\fRがJARファイルの署名時にコマンド行にある場合、署名のタイムスタンプが生成されます。URL
\fBhttp://example\&.tsa\&.url\fRは、Time Stamping Authority (TSA)の場所を特定し、\fB\-tsacert\fRオプションを指定して検出されたURLをオーバーライドします。\fB\-tsa\fRオプションでは、TSAの公開鍵証明書をキーストアに配置する必要はありません。
.sp
タイムスタンプを生成するために、\fBjarsigner\fRは、RFC 3161で定義されているタイムスタンプ・プロトコル(TSP)を使用してTSAと通信します。成功すると、TSAによって返されたタイムスタンプ・トークンは、署名ブロック・ファイルの署名とともに保存されます。
.RE
.PP
\-tsacert \fIalias\fR
.RS 4
\fB\-tsacert alias\fRがJARファイルの署名時にコマンド行にある場合、署名のタイムスタンプが生成されます。別名は、キーストア内の有効なTSAの公開鍵証明書を特定します。エントリの証明書で、TSAの場所を特定するURLを含むSubject Information Access拡張機能が確認されます。
.sp
\fB\-tsacert\fRオプションを使用する場合は、TSAの公開鍵証明書がキーストアに配置されている必要があります。
.RE
.PP
\-tsapolicyid \fIpolicyid\fR
.RS 4
TSAサーバーに送信するポリシーIDを識別するオブジェクト識別子(OID)を指定します。このオプションを指定しない場合、ポリシーIDは送信されず、TSAサーバーはデフォルトのポリシーIDを選択します。
.sp
オブジェクト識別子は、ITU Telecommunication Standardization Sector (ITU\-T)標準であるX\&.696によって定義されます。これらの識別子は、通常、\fB1\&.2\&.3\&.4\fRなどの、負ではない数字のピリオド区切りのセットです。
.RE
.PP
\-altsigner \fIclass\fR
.RS 4
このオプションは、代替署名メカニズムを指定します。完全修飾クラス名は、\fBcom\&.sun\&.jarsigner\&.ContentSigner\fR抽象クラスを拡張するクラス・ファイルを識別します。このクラス・ファイルへのパスは、\fB\-altsignerpath\fRオプションによって定義されます。\fB\-altsigner\fRオプションを使用した場合、\fBjarsigner\fRコマンドでは、指定されたクラスによって提供される署名メカニズムを使用します。それ以外の場合、\fBjarsigner\fRコマンドはデフォルトの署名メカニズムを使用します。
.sp
たとえば、\fBcom\&.sun\&.sun\&.jarsigner\&.AuthSigner\fRという名前のクラスが提供する署名メカニズムを使用するには、jarsignerのオプション\fB\-altsigner com\&.sun\&.jarsigner\&.AuthSigner\fRを使用します。
.RE
.PP
\-altsignerpath \fIclasspathlist\fR
.RS 4
クラス・ファイルおよびそれが依存するJARファイルへのパスを指定します。\fB\-altsigner\fRオプションを使用して、クラス・ファイル名を指定します。クラス・ファイルがJARファイル内にある場合、このオプションでは、そのJARファイルへのパスを指定します。
.sp
絶対パスまたは現在のディレクトリへの相対パスを指定できます。\fIclasspathlist\fRに複数のパスやJARファイルが含まれる場合、それらを、Oracle Solarisの場合はコロン(:)で、Windowsの場合はセミコロン(;)で、それぞれ区切ります。目的のクラスがすでに検索パス内にある場合は、このオプションは不要です。
.sp
次の例では、クラス・ファイルを含むJARファイルへのパスを指定する方法を示します。JARファイル名を含めます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-altsignerpath /home/<USER>/lib/authsigner\&.jar\fR
 
.fi
.if n \{\
.RE
.\}
次の例では、クラス・ファイルを含むJARファイルへのパスを指定する方法を示します。JARファイル名を省略します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\-altsignerpath /home/<USER>/classes/com/sun/tools/jarsigner/\fR
 
.fi
.if n \{\
.RE
.\}
.RE
.PP
\-strict
.RS 4
署名または検証処理中に、コマンドにより警告メッセージが発行される場合があります。このオプションを指定すると、コマンドにより検出された重大な警告メッセージがツールの終了コードに反映されます。エラーと警告を参照してください。
.RE
.PP
\-verbose \fIsuboptions\fR
.RS 4
検証処理の場合、\fB\-verbose\fRオプションには、表示する情報の量を決定するサブオプションを指定します。\fB\-certs\fRオプションも指定した場合、デフォルト・モード(またはサブオプション\fBall\fR)では、エントリが処理されるたびに各エントリが表示され、その後にJARファイルの各署名者の証明書情報も表示されます。\fB\-certs\fRおよび\fB\-verbose:grouped\fRサブオプションを指定した場合、同じ署名者情報を持つエントリが、その証明書情報とともに、グループ化されて表示されます。\fB\-certs\fRおよび\fB\-verbose:summary\fRサブオプションを指定した場合、同じ署名者情報を持つエントリが、その証明書情報とともに、グループ化されて表示されます。各エントリの詳細は、\fI1つのエントリ(以上)\fRにまとめられて表示されます。例を参照してください。
.RE
.SH "エラーと警告"
.PP
署名または検証プロセス中に、\fBjarsigner\fRコマンドで、様々なエラーまたは警告が発行される可能性があります。
.PP
障害がある場合、\fBjarsigner\fRコマンドはコード1で終了します。障害はないが、1つ以上の重大な警告がある場合、\fB\-strict\fRオプションが指定されて\fBいない\fR場合は、\fBjarsigner\fRコマンドはコード0で終了し、\fB\-strict\fRが指定されている場合は警告コードのOR値で終了します。情報警告のみがある、または警告がまったくない場合、コマンドは常にコード0で終了します。
.PP
たとえば、エントリの署名に使用される証明書が期限切れで、ファイルの署名を許可しないKeyUsage拡張機能を使用している場合、\fB\-strict\fRオプションが指定されていると、\fBjarsigner\fRコマンドはコード12 (=4+8)で終了します。
.PP
\fB注意:\fR
Solaris、LinuxおよびOS Xでは0から255までの値のみが有効のため、終了コードは再利用されます。
.PP
次のセクションでは、\fBjarsigner\fRコマンドにより発行できるエラーおよび警告の名前、コード、説明を記述します。
.SS "障害"
.PP
コマンド行解析エラー、JARファイルに署名する鍵ペアを検索できない、または署名付きJARファイルの検証失敗など(限定されません)、\fBjarsigner\fRコマンドの失敗理由。
.PP
failure
.RS 4
コード1。署名または検証が失敗します。
.RE
.SS "重大な警告"
.PP
\fB注意:\fR
\fB\-strict\fRオプションを指定した場合、重大な警告はエラーとして報告されます。
.PP
JARファイルの署名に使用する証明書にエラーがある、または署名付きJARファイルに他の問題があるなど、\fBjarsigner\fRコマンドが重大な警告を発行する理由。
.PP
hasExpiredCert
.RS 4
コード4。このjarには、署名者証明書が期限切れのエントリが含まれています。
.RE
.PP
notYetValidCert
.RS 4
コード4。このjarには、署名者証明書がまだ有効になっていないエントリが含まれています。
.RE
.PP
chainNotValidated
.RS 4
コード4。このjarには、証明書チェーンが検証されていないエントリが含まれています。
.RE
.PP
badKeyUsage
.RS 4
コード8。このJARには、署名者証明書のKeyUsage拡張機能がコード署名を許可しないエントリが含まれています。
.RE
.PP
badExtendedKeyUsage
.RS 4
コード8。このjarには、署名者証明書のExtendedKeyUsage拡張機能がコード署名を許可しないエントリが含まれています。
.RE
.PP
badNetscapeCertType
.RS 4
コード8。このjarには、署名者証明書のNetscapeCertType拡張機能がコード署名を許可しないエントリが含まれています。
.RE
.PP
hasUnsignedEntry
.RS 4
コード16。このjarには、整合性チェックをしていない未署名のエントリが含まれています。
.RE
.PP
notSignedByAlias
.RS 4
コード32。このjarには、指定された別名によって署名されていない署名済エントリが含まれています。
.RE
.PP
aliasNotInStore
.RS 4
コード32。このjarには、このキーストア内の別名によって署名されていない署名済エントリが含まれます。
.RE
.SS "情報警告"
.PP
情報警告には、エラーではないが不適切とみなされるものが含まれます。コードはありません。
.PP
hasExpiringCert
.RS 4
このjarには、署名者証明書が6か月以内に期限切れとなるエントリが含まれています。
.RE
.PP
noTimestamp
.RS 4
このjarには、タイムスタンプを含まない署名が含まれています。タイムスタンプなしでは、署名者証明書の有効期限(\fBYYYY\-MM\-DD\fR)後または将来の取消日後、ユーザーはこのJARファイルを検証できない場合があります。
.RE
.SH "例"
.SS "JARファイルの署名"
.PP
次のコマンドを使用して、キーストア別名が\fBworking\fRディレクトリの\fBmystore\fRという名前のキーストアにある\fBjane\fRであるユーザーの秘密鍵でbundle\&.jarに署名し、署名付きJARファイルに\fBsbundle\&.jar\fRという名前を付けます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjarsigner \-keystore /working/mystore\fR
\fB    \-storepass <keystore password>\fR
\fB    \-keypass <private key password>\fR
\fB    \-signedjar sbundle\&.jar bundle\&.jar jane\fR
 
.fi
.if n \{\
.RE
.\}
.PP
前述のコマンドでは\fB\-sigfile\fRが指定されていないため、署名付きJARファイルに格納される生成された\fB\&.SF\fRファイルおよび\fB\&.DSA\fRファイルの名前は、別名に基づいたデフォルトの名前になります。これらは、名前付きの\fBJANE\&.SF\fRおよび\fBJANE\&.DSA\fRです。
.PP
ストアのパスワードおよび秘密鍵のパスワードを求められる場合、前述のコマンドを短縮して、次のことを実行できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjarsigner \-keystore /working/mystore\fR
\fB    \-signedjar sbundle\&.jar bundle\&.jar jane\fR
 
.fi
.if n \{\
.RE
.\}
.PP
キーストアがデフォルトのキーストア(ホーム・ディレクトリ内の\&.keystore)である場合、次に示すように、キーストアを指定する必要がありません。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjarsigner \-signedjar sbundle\&.jar bundle\&.jar jane\fR
 
.fi
.if n \{\
.RE
.\}
.PP
入力JARファイル(bundle\&.jar)を署名付きJARファイルで上書きする場合、次のように\fB\-signedjar\fRオプションを指定する必要はありません。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjarsigner bundle\&.jar jane\fR
 
.fi
.if n \{\
.RE
.\}
.SS "署名付きJARファイルの検証"
.PP
署名付きJARファイルを検証して、署名が有効でJARファイルが改ざんされなかったことを確認するには、次のようなコマンドを使用します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjarsigner \-verify sbundle\&.jar\fR
 
.fi
.if n \{\
.RE
.\}
.PP
検証が成功すると、\fBjar verified\fRが表示されます。有効でない場合は、エラー・メッセージが表示されます。\fB\-verbose\fRオプションを使用すると、詳細情報を取得できます。\fBjarsigner\fRを\fB\-verbose\fRオプションとともに使用するサンプルを、次に示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjarsigner \-verify \-verbose sbundle\&.jar\fR
\fB \fR
\fB           198 Fri Sep 26 16:14:06 PDT 1997 META\-INF/MANIFEST\&.MF\fR
\fB           199 Fri Sep 26 16:22:10 PDT 1997 META\-INF/JANE\&.SF\fR
\fB          1013 Fri Sep 26 16:22:10 PDT 1997 META\-INF/JANE\&.DSA\fR
\fB    smk   2752 Fri Sep 26 16:12:30 PDT 1997 AclEx\&.class\fR
\fB    smk    849 Fri Sep 26 16:12:46 PDT 1997 test\&.class\fR
\fB \fR
\fB      s = signature was verified\fR
\fB      m = entry is listed in manifest\fR
\fB      k = at least one certificate was found in keystore\fR
\fB \fR
\fB    jar verified\&.\fR
 
.fi
.if n \{\
.RE
.\}
.SS "証明書情報を使用した検証"
.PP
\fB\-certs\fRオプションを\fB\-verify\fRおよび\fB\-verbose\fRオプションとともに指定した場合、JARファイルの各署名者の証明書情報が出力に含まれます。この情報には、証明書タイプ、署名者の識別名情報(X\&.509証明書の場合)が含まれ、丸カッコ内には、JARファイルの公開鍵証明書がキーストア・エントリの公開鍵証明書に一致する場合の署名者のキーストア別名が含まれます。たとえば、次のようになります。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjarsigner \-keystore /working/mystore \-verify \-verbose \-certs myTest\&.jar\fR
\fB \fR
\fB           198 Fri Sep 26 16:14:06 PDT 1997 META\-INF/MANIFEST\&.MF\fR
\fB           199 Fri Sep 26 16:22:10 PDT 1997 META\-INF/JANE\&.SF\fR
\fB          1013 Fri Sep 26 16:22:10 PDT 1997 META\-INF/JANE\&.DSA\fR
\fB           208 Fri Sep 26 16:23:30 PDT 1997 META\-INF/JAVATEST\&.SF\fR
\fB          1087 Fri Sep 26 16:23:30 PDT 1997 META\-INF/JAVATEST\&.DSA\fR
\fB    smk   2752 Fri Sep 26 16:12:30 PDT 1997 Tst\&.class\fR
\fB \fR
\fB      X\&.509, CN=Test Group, OU=Java Software, O=Oracle, L=CUP, S=CA, C=US (javatest)\fR
\fB      X\&.509, CN=Jane Smith, OU=Java Software, O=Oracle, L=cup, S=ca, C=us (jane)\fR
\fB \fR
\fB      s = signature was verified\fR
\fB      m = entry is listed in manifest\fR
\fB      k = at least one certificate was found in keystore\fR
\fB \fR
\fB    jar verified\&.\fR
 
.fi
.if n \{\
.RE
.\}
.PP
署名者の証明書がX\&.509証明書ではない場合、識別名情報は表示されません。その場合には、証明書のタイプと別名のみが表示されます。たとえば、証明書がPGP証明書で、別名が\fBbob\fRである場合、\fBPGP, (bob)\fRを取得します。
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jar(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
keytool(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
http://docs\&.oracle\&.com/javase/tutorial/security/index\&.htmlにある
「コース: Java SEのセキュリティ機能」
.RE
.br
'pl 8.5i
'bp
