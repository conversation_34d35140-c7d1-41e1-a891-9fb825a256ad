'\" t
.\" Copyright (c) 1994, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: javap
.\" Language: Japanese
.\" Date: 2014年8月8日
.\" SectDesc: 基本ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "javap" "1" "2014年8月8日" "JDK 8" "基本ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
javap \- 1つ以上のクラス・ファイルを逆アセンブルします。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavap\fR [\fIoptions\fR] \fIclassfile\fR\&.\&.\&.
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIclassfile\fR
.RS 4
注釈の処理対象となる、空白で区切った1つ以上のクラス(DocFooter\&.classなど)。クラス・パスで検出できるクラスを、ファイル名またはURL(\fBfile:///home/<USER>/myproject/src/DocFooter\&.class\fRなど)で指定できます。
.RE
.SH "説明"
.PP
\fBjavap\fRコマンドは、1つまたは複数のクラス・ファイルを逆アセンブルします。その出力は指定するオプションにより異なります。オプションを指定しない場合、\fBjavap\fRコマンドは、そのパッケージ、渡されたクラスのprotectedおよびpublicのフィールドとメソッドを出力します。\fBjavap\fRコマンドはその出力を\fB標準出力\fRに表示します。
.SH "オプション"
.PP
\-help
.br
\-\-help
.br
\-?
.RS 4
\fBjavap\fRコマンドについてのヘルプ・メッセージを出力します。
.RE
.PP
\-version
.RS 4
リリース情報を出力します。
.RE
.PP
\-l
.RS 4
行番号とローカル変数表を出力します。
.RE
.PP
\-public
.RS 4
publicクラスおよびメンバーのみ表示します。
.RE
.PP
\-protected
.RS 4
protectedおよびpublicのクラスとメンバーのみを表示します。
.RE
.PP
\-private
.br
\-p
.RS 4
すべてのクラスとメンバーを表示します。
.RE
.PP
\-J\fIoption\fR
.RS 4
指定されたオプションをJVMに渡します。次に例を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavap \-J\-version\fR
\fBjavap \-J\-Djava\&.security\&.manager \-J\-Djava\&.security\&.policy=MyPolicy MyClassName\fR
 
.fi
.if n \{\
.RE
.\}
JVMオプションの詳細は、コマンドのマニュアルを参照してください。
.RE
.PP
\-s
.RS 4
内部の型シグニチャを出力します。
.RE
.PP
\-sysinfo
.RS 4
処理中のクラスのシステム情報(パス、サイズ、日付、MD5ハッシュ)を表示します。
.RE
.PP
\-constants
.RS 4
\fBstatic final\fR定数を表示します。
.RE
.PP
\-c
.RS 4
クラスの各メソッドのために逆アセンブルされるコード、すなわちJavaバイトコードからなる命令を表示します。
.RE
.PP
\-verbose
.RS 4
メソッドのスタック・サイズ、localsとargumentsの数を出力します。
.RE
.PP
\-classpath \fIpath\fR
.RS 4
クラスを探すために\fBjavap\fRコマンドが使用するパスを指定します。デフォルトまたは\fBCLASSPATH\fR環境変数の設定を上書きします。
.RE
.PP
\-bootclasspath \fIpath\fR
.RS 4
ブートストラップ・クラスをロードするパスを指定します。ブートストラップ・クラスは、デフォルトでは\fBjre/lib/rt\&.jar\fRおよび他のいくつかのJARファイルにある、コアJavaプラットフォームを実装するクラスです。
.RE
.PP
\-extdir \fIdirs\fR
.RS 4
インストールされた拡張機能を検索する場所をオーバーライドします。拡張機能のデフォルト位置は\fBjava\&.ext\&.dirs\fRです。
.RE
.SH "例"
.PP
次の\fBDocFooter\fRクラスをコンパイルします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBimport java\&.awt\&.*;\fR
\fBimport java\&.applet\&.*;\fR
\fB \fR
\fBpublic class DocFooter extends Applet {\fR
\fB        String date;\fR
\fB        String email;\fR
\fB \fR
\fB        public void init() {\fR
\fB                resize(500,100);\fR
\fB                date = getParameter("LAST_UPDATED");\fR
\fB                email = getParameter("EMAIL");\fR
\fB        }\fR
\fB \fR
\fB        public void paint(Graphics g) {\fR
\fB                g\&.drawString(date + " by ",100, 15);\fR
\fB                g\&.drawString(email,290,15);\fR
\fB        }\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBjavap DocFooter\&.class\fRコマンドからの出力は次を生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBCompiled from "DocFooter\&.java"\fR
\fBpublic class DocFooter extends java\&.applet\&.Applet {\fR
\fB  java\&.lang\&.String date;\fR
\fB  java\&.lang\&.String email;\fR
\fB  public DocFooter();\fR
\fB  public void init();\fR
\fB  public void paint(java\&.awt\&.Graphics);\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBjavap \-c DocFooter\&.class\fRコマンドからの出力は次を生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBCompiled from "DocFooter\&.java"\fR
\fBpublic class DocFooter extends java\&.applet\&.Applet {\fR
\fB  java\&.lang\&.String date;\fR
\fB  java\&.lang\&.String email;\fR
 
\fB  public DocFooter();\fR
\fB    Code:\fR
\fB       0: aload_0       \fR
\fB       1: invokespecial #1                  // Method\fR
\fBjava/applet/Applet\&."<init>":()V\fR
\fB       4: return        \fR
 
\fB  public void init();\fR
\fB    Code:\fR
\fB       0: aload_0       \fR
\fB       1: sipush        500\fR
\fB       4: bipush        100\fR
\fB       6: invokevirtual #2                  // Method resize:(II)V\fR
\fB       9: aload_0       \fR
\fB      10: aload_0       \fR
\fB      11: ldc           #3                  // String LAST_UPDATED\fR
\fB      13: invokevirtual #4                  // Method\fR
\fB getParameter:(Ljava/lang/String;)Ljava/lang/String;\fR
\fB      16: putfield      #5                  // Field date:Ljava/lang/String;\fR
\fB      19: aload_0       \fR
\fB      20: aload_0       \fR
\fB      21: ldc           #6                  // String EMAIL\fR
\fB      23: invokevirtual #4                  // Method\fR
\fB getParameter:(Ljava/lang/String;)Ljava/lang/String;\fR
\fB      26: putfield      #7                  // Field email:Ljava/lang/String;\fR
\fB      29: return        \fR
 
\fB  public void paint(java\&.awt\&.Graphics);\fR
\fB    Code:\fR
\fB       0: aload_1       \fR
\fB       1: new           #8                  // class java/lang/StringBuilder\fR
\fB       4: dup           \fR
\fB       5: invokespecial #9                  // Method\fR
\fB java/lang/StringBuilder\&."<init>":()V\fR
\fB       8: aload_0       \fR
\fB       9: getfield      #5                  // Field date:Ljava/lang/String;\fR
\fB      12: invokevirtual #10                 // Method\fR
\fB java/lang/StringBuilder\&.append:(Ljava/lang/String;)Ljava/lang/StringBuilder;\fR
\fB      15: ldc           #11                 // String  by \fR
\fB      17: invokevirtual #10                 // Method\fR
\fB java/lang/StringBuilder\&.append:(Ljava/lang/String;)Ljava/lang/StringBuilder;\fR
\fB      20: invokevirtual #12                 // Method\fR
\fB java/lang/StringBuilder\&.toString:()Ljava/lang/String;\fR
\fB      23: bipush        100\fR
\fB      25: bipush        15\fR
\fB      27: invokevirtual #13                 // Method\fR
\fB java/awt/Graphics\&.drawString:(Ljava/lang/String;II)V\fR
\fB      30: aload_1       \fR
\fB      31: aload_0       \fR
\fB      32: getfield      #7                  // Field email:Ljava/lang/String;\fR
\fB      35: sipush        290\fR
\fB      38: bipush        15\fR
\fB      40: invokevirtual #13                 // Method\fR
\fBjava/awt/Graphics\&.drawString:(Ljava/lang/String;II)V\fR
\fB      43: return        \fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
java(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javac(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javadoc(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javah(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jdb(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jdeps(1)
.RE
.br
'pl 8.5i
'bp
