'\" t
.\" Copyright (c) 2013, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jdeps
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: 基本ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jdeps" "1" "2013年11月21日" "JDK 8" "基本ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jdeps \- Javaクラス依存性アナライザ。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjdeps\fR [\fIoptions\fR] \fIclasses\fR \&.\&.\&.
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIclasses\fR
.RS 4
分析するクラスの名前。クラス・パスで検出できるクラスを、ファイル名、ディレクトリまたはJARファイルで指定できます。
.RE
.SH "説明"
.PP
\fBjdeps\fRコマンドは、Javaクラス・ファイルのパッケージレベルまたはクラスレベルの依存性を示します。入力クラスには、\fB\&.class\fRファイルのパス名、ディレクトリ、JARファイル、またはすべてのクラス・ファイルを分析するための完全修飾クラス名を指定できます。オプションにより出力が決定します。デフォルトでは、\fBjdeps\fRはシステム出力に依存関係を出力します。DOT言語で依存関係を生成できます(\fB\-dotoutput\fRオプションを参照)。
.SH "オプション"
.PP
\-dotoutput <\fIdir\fR>
.RS 4
DOTファイル出力の宛先ディレクトリ。指定した場合、\fBjdeps\fRは<\fIarchive\-file\-name\fR>\&.dotという名前の分析済アーカイブごとに依存関係をリストする1つのdotファイルを生成し、アーカイブ間の依存関係をリストするsummary\&.dotという名前のサマリー・ファイルも生成します。
.RE
.PP
\-s
.br
\-summary
.RS 4
依存関係のサマリーのみを出力します。
.RE
.PP
\-v
.br
\-verbose
.RS 4
すべてのクラスレベルの依存関係を出力します。
.RE
.PP
\-verbose:package
.RS 4
同じアーカイブ内の依存関係を除き、パッケージレベルの依存関係を出力します。
.RE
.PP
\-verbose:class
.RS 4
同じアーカイブ内の依存関係を除き、クラスレベルの依存関係を出力します。
.RE
.PP
\-cp <\fIpath\fR>
.br
\-classpath <\fIpath\fR>
.RS 4
クラス・ファイルの検索場所を指定します。
.sp
クラス・パスの設定 も参照してください。
.RE
.PP
\-p <\fIpkg name\fR>
.br
\-package <\fIpkg name\fR>
.RS 4
指定したパッケージの依存関係を検出します。異なるパッケージに対してこのオプションを複数回指定できます。\fB\-p\fRオプションと\fB\-e\fRオプションを同時に指定することはできません。
.RE
.PP
\-e <\fIregex\fR>
.br
\-regex <\fIregex\fR>
.RS 4
指定した正規表現パターンと一致するパッケージの依存関係を検出します。\fB\-p\fRオプションと\fB\-e\fRオプションを同時に指定することはできません。
.RE
.PP
\-include <\fIregex\fR>
.RS 4
分析をパターンに一致するクラスに制限します。このオプションは、分析するクラスのリストをフィルタします。依存関係にパターンを適用する\fB\-p\fRおよび\fB\-e\fRとともに使用できます。
.RE
.PP
\-jdkinternals
.RS 4
JDKの内部APIのクラスレベルの依存関係を検出します。デフォルトでは、\fB\-include\fRオプションを指定しないかぎり、\fB\-classpath\fRオプションおよび入力ファイルに指定されたすべてのクラスを分析します。このオプションは、\fB\-p\fRオプション、\fB\-e\fRオプションおよび\fB\-s\fRオプションとともに使用できません。
.sp
\fB警告\fR: JDKの内部APIは、今後のリリースでアクセスできなくなる可能性があります。
.RE
.PP
\-P
.br
\-profile
.RS 4
パッケージを含むプロファイルまたはファイルを表示します。
.RE
.PP
\-apionly
.RS 4
フィールド・タイプ、メソッド・パラメータ・タイプ、戻りタイプ、チェックされた例外タイプを含むパブリック・クラスの\fBpublic\fRおよび\fBprotected\fRメンバーの署名からの依存関係など、分析をAPIに制限します。
.RE
.PP
\-R
.br
\-recursive
.RS 4
すべての依存関係を再帰的に走査します。
.RE
.PP
\-version
.RS 4
バージョン情報を出力します。
.RE
.PP
\-h
.br
\-?
.br
\-help
.RS 4
\fBjdeps\fRに関するヘルプ・メッセージを出力します。
.RE
.SH "例"
.PP
Notepad\&.jarの依存関係の分析。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB$ jdeps demo/jfc/Notepad/Notepad\&.jar\fR
\fB \fR
\fBdemo/jfc/Notepad/Notepad\&.jar \-> /usr/java/jre/lib/rt\&.jar\fR
\fB   <unnamed> (Notepad\&.jar)\fR
\fB      \-> java\&.awt                                           \fR
\fB      \-> java\&.awt\&.event                                     \fR
\fB      \-> java\&.beans                                         \fR
\fB      \-> java\&.io                                            \fR
\fB      \-> java\&.lang                                          \fR
\fB      \-> java\&.net                                           \fR
\fB      \-> java\&.util                                          \fR
\fB      \-> java\&.util\&.logging                                  \fR
\fB      \-> javax\&.swing                                        \fR
\fB      \-> javax\&.swing\&.border                                 \fR
\fB      \-> javax\&.swing\&.event                                  \fR
\fB      \-> javax\&.swing\&.text                                   \fR
\fB      \-> javax\&.swing\&.tree                                   \fR
\fB      \-> javax\&.swing\&.undo  \fR
 
.fi
.if n \{\
.RE
.\}
.PP
\-Pまたは\-profileオプションを使用して、メモ帳が依存するプロファイルを表示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB$ jdeps \-profile demo/jfc/Notepad/Notepad\&.jar \fR
\fBdemo/jfc/Notepad/Notepad\&.jar \-> /usr/java/jre/lib/rt\&.jar (Full JRE)\fR
\fB   <unnamed> (Notepad\&.jar)\fR
\fB      \-> java\&.awt                                           Full JRE\fR
\fB      \-> java\&.awt\&.event                                     Full JRE\fR
\fB      \-> java\&.beans                                         Full JRE\fR
\fB      \-> java\&.io                                            compact1\fR
\fB      \-> java\&.lang                                          compact1\fR
\fB      \-> java\&.net                                           compact1\fR
\fB      \-> java\&.util                                          compact1\fR
\fB      \-> java\&.util\&.logging                                  compact1\fR
\fB      \-> javax\&.swing                                        Full JRE\fR
\fB      \-> javax\&.swing\&.border                                 Full JRE\fR
\fB      \-> javax\&.swing\&.event                                  Full JRE\fR
\fB      \-> javax\&.swing\&.text                                   Full JRE\fR
\fB      \-> javax\&.swing\&.tree                                   Full JRE\fR
\fB      \-> javax\&.swing\&.undo                                   Full JRE\fR
 
.fi
.if n \{\
.RE
.\}
.PP
tools\&.jarファイル内の\fBcom\&.sun\&.tools\&.jdeps\&.Main\fRクラスなど、特定のクラスパス内の特定のクラスの直接依存関係の分析。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB$ jdeps \-cp lib/tools\&.jar com\&.sun\&.tools\&.jdeps\&.Main\fR
\fBlib/tools\&.jar \-> /usr/java/jre/lib/rt\&.jar\fR
\fB   com\&.sun\&.tools\&.jdeps (tools\&.jar)\fR
\fB      \-> java\&.io                                            \fR
\fB      \-> java\&.lang \fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fB\-verbose:class\fRオプションを使用して、クラスレベル依存関係を検索するか\fB\-v\fRまたは\fB\-verbose\fRオプションを使用して同じJARファイルからの依存関係を含めます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB$ jdeps \-verbose:class \-cp lib/tools\&.jar com\&.sun\&.tools\&.jdeps\&.Main\fR
\fB \fR
\fBlib/tools\&.jar \-> /usr/java/jre/lib/rt\&.jar\fR
\fB   com\&.sun\&.tools\&.jdeps\&.Main (tools\&.jar)\fR
\fB      \-> java\&.io\&.PrintWriter                                \fR
\fB      \-> java\&.lang\&.Exception                                \fR
\fB      \-> java\&.lang\&.Object                                   \fR
\fB      \-> java\&.lang\&.String                                   \fR
\fB      \-> java\&.lang\&.System \fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fB\-R\fRまたは\fB\-recursive\fRオプションを使用して、\fBcom\&.sun\&.tools\&.jdeps\&.Main\fRクラスの推移的な依存関係を分析します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB$ jdeps \-R \-cp lib/tools\&.jar com\&.sun\&.tools\&.jdeps\&.Main\fR
\fBlib/tools\&.jar \-> /usr/java/jre/lib/rt\&.jar\fR
\fB   com\&.sun\&.tools\&.classfile (tools\&.jar)\fR
\fB      \-> java\&.io                                            \fR
\fB      \-> java\&.lang                                          \fR
\fB      \-> java\&.lang\&.reflect                                  \fR
\fB      \-> java\&.nio\&.charset                                   \fR
\fB      \-> java\&.nio\&.file                                      \fR
\fB      \-> java\&.util                                          \fR
\fB      \-> java\&.util\&.regex                                    \fR
\fB   com\&.sun\&.tools\&.jdeps (tools\&.jar)\fR
\fB      \-> java\&.io                                            \fR
\fB      \-> java\&.lang                                          \fR
\fB      \-> java\&.nio\&.file                                      \fR
\fB      \-> java\&.nio\&.file\&.attribute                            \fR
\fB      \-> java\&.text                                          \fR
\fB      \-> java\&.util                                          \fR
\fB      \-> java\&.util\&.jar                                      \fR
\fB      \-> java\&.util\&.regex                                    \fR
\fB      \-> java\&.util\&.zip                                      \fR
\fB/usr/java/jre/lib/jce\&.jar \-> /usr/java/jre/lib/rt\&.jar\fR
\fB   javax\&.crypto (jce\&.jar)\fR
\fB      \-> java\&.io                                            \fR
\fB      \-> java\&.lang                                          \fR
\fB      \-> java\&.lang\&.reflect                                  \fR
\fB      \-> java\&.net                                           \fR
\fB      \-> java\&.nio                                           \fR
\fB      \-> java\&.security                                      \fR
\fB      \-> java\&.security\&.cert                                 \fR
\fB      \-> java\&.security\&.spec                                 \fR
\fB      \-> java\&.util                                          \fR
\fB      \-> java\&.util\&.concurrent                               \fR
\fB      \-> java\&.util\&.jar                                      \fR
\fB      \-> java\&.util\&.regex                                    \fR
\fB      \-> java\&.util\&.zip                                      \fR
\fB      \-> javax\&.security\&.auth                                \fR
\fB      \-> sun\&.security\&.jca                                   JDK internal API (rt\&.jar)\fR
\fB      \-> sun\&.security\&.util                                  JDK internal API (rt\&.jar)\fR
\fB   javax\&.crypto\&.spec (jce\&.jar)\fR
\fB      \-> java\&.lang                                          \fR
\fB      \-> java\&.security\&.spec                                 \fR
\fB      \-> java\&.util                                          \fR
\fB/usr/java/jre/lib/rt\&.jar \-> /usr/java/jre/lib/jce\&.jar\fR
\fB   java\&.security (rt\&.jar)\fR
\fB      \-> javax\&.crypto\fR
 
.fi
.if n \{\
.RE
.\}
.PP
メモ帳デモの依存関係のdotファイルを生成します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB$ jdeps \-dotoutput dot demo/jfc/Notepad/Notepad\&.jar\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBjdeps\fRは、\fB\-dotoutput\fRオプションで指定されたdotディレクトリに<\fIfilename\fR>\&.dotという名前のdotファイルを特定のJARファイルごとに作成し、JARファイル間の依存関係をリストするsummary\&.dotという名前のサマリー・ファイルも作成します
.sp
.if n \{\
.RS 4
.\}
.nf
\fB$ cat dot/Notepad\&.jar\&.dot \fR
\fBdigraph "Notepad\&.jar" {\fR
\fB    // Path: demo/jfc/Notepad/Notepad\&.jar\fR
\fB   "<unnamed>"                                        \-> "java\&.awt";\fR
\fB   "<unnamed>"                                        \-> "java\&.awt\&.event";\fR
\fB   "<unnamed>"                                        \-> "java\&.beans";\fR
\fB   "<unnamed>"                                        \-> "java\&.io";\fR
\fB   "<unnamed>"                                        \-> "java\&.lang";\fR
\fB   "<unnamed>"                                        \-> "java\&.net";\fR
\fB   "<unnamed>"                                        \-> "java\&.util";\fR
\fB   "<unnamed>"                                        \-> "java\&.util\&.logging";\fR
\fB   "<unnamed>"                                        \-> "javax\&.swing";\fR
\fB   "<unnamed>"                                        \-> "javax\&.swing\&.border";\fR
\fB   "<unnamed>"                                        \-> "javax\&.swing\&.event";\fR
\fB   "<unnamed>"                                        \-> "javax\&.swing\&.text";\fR
\fB   "<unnamed>"                                        \-> "javax\&.swing\&.tree";\fR
\fB   "<unnamed>"                                        \-> "javax\&.swing\&.undo";\fR
\fB}\fR
\fB \fR
\fB$ cat dot/summary\&.dot\fR
\fBdigraph "summary" {\fR
\fB   "Notepad\&.jar"                  \-> "rt\&.jar";\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javap(1)
.RE
.br
'pl 8.5i
'bp
