'\" t
.\" Copyright (c) 2006, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jhat
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: トラブルシューティング・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jhat" "1" "2013年11月21日" "JDK 8" "トラブルシューティング・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
jhat \- Javaヒープを分析します。このコマンドは試験的なもので、サポートされていません。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjhat\fR [ \fIoptions\fR ] \fIheap\-dump\-file\fR 
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIheap\-dump\-file\fR
.RS 4
ブラウズ対象となるJavaバイナリ・ヒープ・ダンプ・ファイル。複数のヒープ・ダンプを含むダンプ・ファイルの場合、\fBmyfile\&.hprof#3\fRのようにファイル名の後に\fB#<number>\fRを付加することで、ファイル内の特定のダンプを指定できます。
.RE
.SH "説明"
.PP
\fBjhat\fRコマンドはJavaヒープ・ダンプ・ファイルを解析し、Webサーバーを開始します。\fBjhat\fRコマンドを使用して、お気に入りのブラウザでヒープ・ダンプを参照できます。\fBjhat\fRコマンドは、既知のクラス\fBMyClass\fRのすべてのインスタンスを表示するなどの事前設計済の問合せやObject Query Language (OQL)をサポートします。ヒープ・ダンプの問合せを除き、OQLはSQLに似ています。OQLのヘルプには、\fBjhat\fRコマンドによって表示されるOQLヘルプ・ページからアクセスできます。デフォルト・ポートを使用する場合、OQLのヘルプはhttp://localhost:7000/oqlhelp/で利用可能です。
.PP
Javaのヒープ・ダンプを生成するには、次のいくつかの方法があります。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBjmap \-dump\fRオプションを使用して実行時にヒープ・ダンプを取得します。jmap(1)を参照してください。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBjconsole\fRオプションを使用して\fBHotSpotDiagnosticMXBean\fR経由で実行時にヒープ・ダンプを取得します。jconsole(1)および\fBHotSpotDiagnosticMXBean\fRのインタフェースの説明(
http://docs\&.oracle\&.com/javase/8/docs/jre/api/management/extension/com/sun/management/HotSpotDiagnosticMXBean\&.html)を参照してください。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
ヒープ・ダンプは、\fB\-XX:+HeapDumpOnOutOfMemoryError\fR
Java Virtual Machine (JVM)オプションを指定することで、\fBOutOfMemoryError\fRがスローされたときに生成されます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
\fBhprof\fRコマンドを使用します。HPROF: Heap/CPUプロファイリング・ツール
(http://docs\&.oracle\&.com/javase/8/docs/technotes/samples/hprof\&.html)を参照してください
.RE
.SH "オプション"
.PP
\-stack false|true
.RS 4
オブジェクト割当呼出しスタックの追跡を無効にします。ヒープ・ダンプ内で割当サイト情報が使用できない場合、このフラグを\fBfalse\fRに設定する必要があります。デフォルトは\fBtrue\fRです。
.RE
.PP
\-refs false|true
.RS 4
オブジェクトへの参照の追跡を無効にします。デフォルトは\fBtrue\fRです。デフォルトでは、ヒープ内のすべてのオブジェクトについて、バックポインタ(指定されたオブジェクトをポイントしているオブジェクト。参照者または受信参照とも呼ばれる)が計算されます。
.RE
.PP
\-port \fIport\-number\fR
.RS 4
\fBjhat\fRのHTTPサーバーのポートを設定します。デフォルトは7000です。
.RE
.PP
\-exclude \fIexclude\-file\fR
.RS 4
到達可能なオブジェクトの問合せから除外する必要があるデータ・メンバーの一覧を含むファイルを指定します。たとえば、このファイルに\fBjava\&.lang\&.String\&.value\fRが含まれていた場合、特定のオブジェクト\fBo\fRから到達可能なオブジェクトのリストを計算する際に、\fBjava\&.lang\&.String\&.value\fRフィールドに関連する参照パスが考慮されなくなります。
.RE
.PP
\-baseline \fIexclude\-file\fR
.RS 4
ベースラインとなるヒープ・ダンプを指定します。両方のヒープ・ダンプ内で同じオブジェクトIDを持つオブジェクトは新規ではないとしてマークされます。他のオブジェクトは新規としてマークされます。これは、異なる2つのヒープ・ダンプを比較する際に役立ちます。
.RE
.PP
\-debug \fIint\fR
.RS 4
このツールのデバッグ・レベルを設定します。レベル0はデバッグ出力がないことを意味します。より大きな値を設定すると、より冗長なモードになります。
.RE
.PP
\-version
.RS 4
リリース番号をレポートして終了します
.RE
.PP
\-h
.RS 4
ヘルプ・メッセージを表示して終了します。
.RE
.PP
\-help
.RS 4
ヘルプ・メッセージを表示して終了します。
.RE
.PP
\-J\fIflag\fR
.RS 4
\fBjhat\fRコマンドを実行しているJava Virtual Machineに\fBflag\fRを渡します。たとえば、512Mバイトの最大ヒープ・サイズを使用するには、\fB\-J\-Xmx512m\fRとします。
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jmap(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jconsole(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
HPROF: Heap/CPUプロファイリング・ツール
(http://docs\&.oracle\&.com/javase/8/docs/technotes/samples/hprof\&.html)
.RE
.br
'pl 8.5i
'bp
