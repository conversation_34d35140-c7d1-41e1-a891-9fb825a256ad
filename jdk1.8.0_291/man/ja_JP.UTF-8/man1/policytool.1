'\" t
.\" Copyright (c) 2001, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: policytool
.\" Language: Japanese
.\" Date: 2015年3月3日
.\" SectDesc: セキュリティ・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "policytool" "1" "2015年3月3日" "JDK 8" "セキュリティ・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
policytool \- ユーティリティGUI経由で取得したユーザー入力に基づいて、プレーン・テキストのポリシー・ファイルを読み書きします。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpolicytool\fR [ \fB\-file\fR ] [ \fIfilename\fR ] 
.fi
.if n \{\
.RE
.\}
.PP
\-file
.RS 4
ポリシー・ファイルを読み込むように\fBpolicytool\fRに指示します。
.RE
.PP
\fIfilename\fR
.RS 4
ロードするファイルの名前。
.RE
.PP
\fB例\fR:
.PP
ポリシー・ツール管理ユーティリティを実行します:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpolicytool\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBpolicytool\fRコマンドを実行し、指定されたファイルをロードします:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBpolicytool \-file \fR\fB\fImypolicyfile\fR\fR
 
.fi
.if n \{\
.RE
.\}
.SH "説明"
.PP
\fBpolicytool\fRコマンドは、管理者のGUIを呼び出します。これにより、システム管理者はローカル・ポリシー・ファイルの内容を管理できるようになります。ポリシー・ファイルは\fB\&.policy\fR拡張子を持つプレーンテキスト・ファイルで、ドメイン別にリモート・リクエスタを権限オブジェクトにマップします。詳細は、http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/PolicyFiles\&.htmlにある
「Default Policy Implementation and Policy File Syntax」を参照してください
.SH "オプション"
.PP
\-file
.RS 4
ポリシー・ファイルを読み込むように\fBpolicytool\fRに指示します。
.RE
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
「Default Policy Implementation and Policy File Syntax」
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/PolicyFiles\&.html)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
「Policy File Creation and Management」
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/PolicyGuide\&.html)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
「Permissions in Java SE Development Kit (JDK)」
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/permissions\&.html)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
「Java Security Overview」
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/overview/jsoverview\&.html)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
「Java Cryptography Architecture (JCA) Reference Guide」
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/crypto/CryptoSpec\&.html)
.RE
.br
'pl 8.5i
'bp
