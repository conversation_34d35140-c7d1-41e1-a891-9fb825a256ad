'\" t
.\" Copyright (c) 1999, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: tnameserv
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Java IDLおよびRMI-IIOPツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "tnameserv" "1" "2013年11月21日" "JDK 8" "Java IDLおよびRMI-IIOPツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
tnameserv \- インタフェース定義言語(IDL)。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBtnameserve\fR \fB\-ORBInitialPort\fR [ \fInameserverport\fR ]
.fi
.if n \{\
.RE
.\}
.PP
\-ORBInitialPort \fInameserverport\fR
.RS 4
ネーミング・サービスがORBの\fBresolve_initial_references\fRメソッドと\fBlist_initial_references\fRメソッドの実装に使用するブートストラップ・プロトコルをリスニングする初期ポートです。
.RE
.SH "説明"
.PP
Java IDLには、Object Request Broker Daemon (ORBD)が含まれます。ORBDは、ブートストラップ・サービス、一時ネーミング・サービス、永続ネーミング・サービスおよびサーバー・マネージャを含むデーモン・プロセスです。Java IDLのすべてのチュートリアルではORBDを使用していますが、一時ネーミング・サービスを使用する例では、\fBorbd\fRのかわりに\fBtnameserv\fRを使用できます。
.PP
orbd(1)http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/jidlNaming\&.html
にあるまたは「Naming Service」を参照してください。
.PP
CORBAのCOS (Common Object Services)ネーミング・サービスは、ファイル・システムがファイルに対してディレクトリ構造を提供しているのと同じように、オブジェクト参照に対してツリー構造のディレクトリを提供します。Java IDLの一時ネーム・サービスである\fBtnameserv\fRは、COSネーム・サービスの仕様を単純な形で実装したものです。
.PP
オブジェクト参照はネームスペースに名前で格納され、オブジェクト参照と名前のペアは、それぞれネーム・バインディングと呼ばれます。ネーム・バインディングはネーミング・コンテキストに組み込むことができます。ネーミング・コンテキストはネーム・バインディングであり、ファイル・システムのサブディレクトリと同じ編成機能を持ちます。すべてのバインディングは初期ネーミング・コンテキストの下に格納されます。初期ネーミング・コンテキストは、ネームスペースの唯一の永続バインディングです。Java IDLネーミング・サービス・プロセスを停止して再起動すると、残りのネームスペースは失われます。
.PP
アプレットまたはアプリケーションからCOSネーミング・サービスを使用するためには、そのORBがネーミング・サービスが動作しているホストのポートを知っているか、そのネーミング・サービスの初期ネーミング・コンテキスト文字列にアクセスできる必要があります。ネーム・サービスは、Java IDLのネーム・サービスでもその他のCOS準拠のネーム・サービスでもかまいません。
.SS "ネーミング・サービスの起動"
.PP
Java IDLネーム・サービスは、ネーム・サービスを使用するアプリケーションまたはアプレットより前に起動しておく必要があります。Java IDL製品をインストールすると、Java IDLネーミング・サービスを起動するスクリプト(Oracle Solaris:
\fBtnameserv\fR)または実行可能ファイル(Windows:
\fBtnameserv\&.exe\fR)が作成されます。バックグラウンドで動作するように、ネーム・サービスを起動してください。
.PP
特に指定しない場合、Java IDLネーミング・サービスは、ORBの\fBresolve_initial_references\fRメソッドと\fBlist_initial_references methods\fRメソッドの実装に使用するブートストラップ・プロトコルに対してポート900でリスニングします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBtnameserv \-ORBInitialPort nameserverport&\fR
 
.fi
.if n \{\
.RE
.\}
.PP
ネーム・サーバー・ポートを指定しない場合、デフォルトでポート900が使用されます。Oracle Solarisソフトウェアの実行時、1024より小さいポートでプロセスを開始する場合は、rootユーザーになる必要があります。このため、1024以上のポート番号を使用することをお薦めします。1050のように別のポートを指定し、ネーム・サービスをバックグラウンドで実行するには、Solaris、LinuxまたはOS Xコマンド・シェルで次のように入力します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBtnameserv \-ORBInitialPort 1050&\fR
 
.fi
.if n \{\
.RE
.\}
.PP
WindowsのMS\-DOSシステム・プロンプトでは、次のように入力します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBstart tnameserv \-ORBInitialPort 1050\fR
 
.fi
.if n \{\
.RE
.\}
.PP
ネーム・サーバーのクライアントには、新しいポート番号を知らせる必要があります。これを行うには、ORBオブジェクトの作成時に\fBorg\&.omg\&.CORBA\&.ORBInitialPort\fRプロパティに新しいポート番号を設定します。
.SS "異なるホスト上でのサーバーとクライアントの実行"
.PP
Java IDLとRMI\-IIOPのほとんどのチュートリアルでは、ネーミング・サービス、サーバーおよびクライアントはすべて開発用のマシン上で実行されます。実際にデプロイメントする場合には、クライアントとサーバーを、ネーミング・サービスとは異なるホスト・マシン上で実行することが多くなります。
.PP
クライアントとサーバーがネーム・サービスを見つけるには、クライアントとサーバーが、ネーム・サービスが実行されているポートの番号とホストを認識している必要があります。そのためには、クライアントとサーバーのファイル内の\fBorg\&.omg\&.CORBA\&.ORBInitialPort\fRプロパティと\fBorg\&.omg\&.CORBA\&.ORBInitialHost\fRプロパティをネーム・サービスが実行されているポートの番号とマシンの名前に設定します。この例は、「Getting Started Using RMI\-IIOP」
(http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/rmi\-iiop/rmiiiopexample\&.html)に示されています
.PP
コマンド行オプション\fB\-ORBInitialPort nameserverport#\fRと\fB\-ORBInitialHost nameserverhostname\fRを使用して、クライアントとサーバーに対してネーミング・サービスを探す場所を指定することもできます。コマンド行オプションを使用してこれを行う方法の1つの例は、http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/tutorial/jidl2machines\&.htmlの「Java IDL: The Hello World Example on Two Machines」
を参照してください
.PP
たとえば、一時ネーム・サービス\fBtnameserv\fRが、ホスト\fBnameserverhost\fRのポート1050上で実行されているとします。さらに、クライアントがホスト\fBclienthost\fR上で実行され、サーバーはホスト\fBserverhost\fR上で実行されているとします。
.PP
ホスト\fBnameserverhost\fR上で\fBtnameserv\fRを起動します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBtnameserv \-ORBInitialPort 1050\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBserverhost\fR上でサーバーを起動します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava Server \-ORBInitialPort 1050 \-ORBInitialHost nameserverhost\fR
 
.fi
.if n \{\
.RE
.\}
.PP

\fBclienthost\fR上でクライアントを起動します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjava Client \-ORBInitialPort 1050 \-ORBInitialHost nameserverhost\fR
 
.fi
.if n \{\
.RE
.\}
.SS "ネーミング・サービスの停止"
.PP
Java IDLネーミング・サービスを停止するには、Solaris、LinuxまたはOS Xの場合は、\fBkill\fRなどのオペレーティング・システムのコマンドを使用し、Windowsの場合は、\fB[Ctrl]+[C]\fRキーを使用します。ネーミング・サービスを明示的に停止するまでは、呼出し待機状態が続きます。サービスを終了させると、Java IDLネーム・サービスに登録されている名前は失われます。
.SH "オプション"
.PP
\-J\fIoption\fR
.RS 4
Java Virtual Machineに\fBoption\fRを渡します。\fBoption\fRには、Javaアプリケーション起動ツールのリファレンス・ページに記載されているオプションを1つ指定します。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。java(1)を参照してください。
.RE
.SH "例"
.SS "ネームスペースへのオブジェクトの追加"
.PP
次の例では、ネームスペースに名前を追加する方法を示します。このサンプル・プログラムは、このままの状態で完全に動作する一時ネーム・サービス・クライアントで、次のような単純なツリーを作成するものです。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBInitial Naming Context\fR
\fB     plans\fR
\fB     Personal\fR
\fB          calendar\fR
\fB          schedule\fR
 
.fi
.if n \{\
.RE
.\}
.PP
この例で、\fBplans\fRはオブジェクト参照、\fBPersonal\fRは\fBcalendar\fRと\fBschedule\fRの2つのオブジェクト参照を含むネーミング・コンテキストです。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBimport java\&.util\&.Properties;\fR
\fBimport org\&.omg\&.CORBA\&.*;\fR
\fBimport org\&.omg\&.CosNaming\&.*;\fR
\fB \fR
\fBpublic class NameClient {\fR
 
\fB    public static void main(String args[]) {\fR
 
\fB        try {\fR
 
.fi
.if n \{\
.RE
.\}
.PP
ネーミング・サービスの起動で、\fBnameserver\fRはポート1050で起動されました。次のコードで、このポート番号をクライアント・システムに知らせます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB            Properties props = new Properties();\fR
\fB            props\&.put("org\&.omg\&.CORBA\&.ORBInitialPort", "1050");\fR
\fB            ORB orb = ORB\&.init(args, props);\fR
 
.fi
.if n \{\
.RE
.\}
.PP
次のコードでは、初期ネーミング・コンテキストを取得し、それを\fBctx\fRに代入します。2行目では、\fBctx\fRをダミーのオブジェクト参照\fBobjref\fRにコピーします。このobjrefには、あとで様々な名前を割り当ててネームスペースに追加します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB            NamingContext ctx =\fR
\fB                NamingContextHelper\&.narrow(\fR
\fB                    orb\&.resolve_initial_references("NameService"));\fR
\fB             NamingContext objref = ctx;\fR
 
.fi
.if n \{\
.RE
.\}
.PP
次のコードでは、\fBtext\fRタイプの名前\fBplans\fRを作成し、それをダミーのオブジェクト参照にバインドします。その後、\fBrebind\fRメソッドを使用して初期ネーミング・コンテキストの下に\fBplans\fRを追加しています。\fBrebind\fRメソッドを使用すれば、\fBbind\fRメソッドを使用した場合に発生する例外を発生させずに、このプログラムを何度も繰返し実行できます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB            NameComponent nc1 = new NameComponent("plans", "text");\fR
\fB            NameComponent[] name1 = {nc1};\fR
\fB            ctx\&.rebind(name1, objref);\fR
\fB            System\&.out\&.println("plans rebind successful!");\fR
 
.fi
.if n \{\
.RE
.\}
.PP
次のコードでは、\fBdirectory\fRタイプの\fBPersonal\fRというネーミング・コンテキストを作成します。その結果得られるオブジェクト参照\fBctx2\fRを\fBname\fRにバインドし、初期ネーミング・コンテキストに追加します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB            NameComponent nc2 = new NameComponent("Personal", "directory");\fR
\fB            NameComponent[] name2 = {nc2};\fR
\fB            NamingContext ctx2 = ctx\&.bind_new_context(name2);\fR
\fB            System\&.out\&.println("new naming context added\&.\&.");\fR
 
.fi
.if n \{\
.RE
.\}
.PP
残りのコードでは、ダミーのオブジェクト参照を\fBschedule\fRと\fBcalendar\fRという名前でネーミング・コンテキスト\fBPersonal\fR(\fBctx2\fR)にバインドします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB            NameComponent nc3 = new NameComponent("schedule", "text");\fR
\fB            NameComponent[] name3 = {nc3};\fR
\fB            ctx2\&.rebind(name3, objref);\fR
\fB            System\&.out\&.println("schedule rebind successful!");\fR
\fB \fR
\fB            NameComponent nc4 = new NameComponent("calender", "text");\fR
\fB            NameComponent[] name4 = {nc4};\fR
\fB            ctx2\&.rebind(name4, objref);\fR
\fB            System\&.out\&.println("calender rebind successful!");\fR
\fB        } catch (Exception e) {\fR
\fB            e\&.printStackTrace(System\&.err);\fR
\fB        }\fR
\fB    }\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
.SS "ネームスペースの参照"
.PP
次のサンプル・プログラムでは、ネームスペースをブラウズする方法を示します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBimport java\&.util\&.Properties;\fR
\fBimport org\&.omg\&.CORBA\&.*;\fR
\fBimport org\&.omg\&.CosNaming\&.*;\fR
\fB \fR
\fBpublic class NameClientList {\fR
 
\fB    public static void main(String args[]) {\fR
 
\fB        try {\fR
 
.fi
.if n \{\
.RE
.\}
.PP
ネーミング・サービスの起動で、\fBnameserver\fRはポート1050で起動されました。次のコードで、このポート番号をクライアント・システムに知らせます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB            Properties props = new Properties();\fR
\fB            props\&.put("org\&.omg\&.CORBA\&.ORBInitialPort", "1050");\fR
\fB            ORB orb = ORB\&.init(args, props);\fR
 
.fi
.if n \{\
.RE
.\}
.PP
次のコードでは、初期ネーミング・コンテキストを取得しています。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB            NamingContext nc =\fR
\fB            NamingContextHelper\&.narrow(\fR
\fB                orb\&.resolve_initial_references("NameService"));\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBlist\fRメソッドは、ネーミング・コンテキストのバインディングをリストします。この場合、最大1000個までのバインディングが初期ネーミング・コンテキストから\fBBindingListHolder\fRに返されます。残りのバインディングは、\fBBindingIteratorHolder\fRに返されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB            BindingListHolder bl = new BindingListHolder();\fR
\fB            BindingIteratorHolder blIt= new BindingIteratorHolder();\fR
\fB            nc\&.list(1000, bl, blIt);\fR
 
.fi
.if n \{\
.RE
.\}
.PP
次のコードでは、返された\fBBindingListHolder\fRからバインディングの配列を取得します。バインディングがない場合は、プログラムが終了します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB            Binding bindings[] = bl\&.value;\fR
\fB            if (bindings\&.length == 0) return;\fR
 
.fi
.if n \{\
.RE
.\}
.PP
残りのコードでは、バインディングに対してループ処理を行い、名前を出力します。
.sp
.if n \{\
.RS 4
.\}
.nf
\fB            for (int i=0; i < bindings\&.length; i++) {\fR
\fB \fR
\fB                // get the object reference for each binding\fR
\fB                org\&.omg\&.CORBA\&.Object obj = nc\&.resolve(bindings[i]\&.binding_name);\fR
\fB                String objStr = orb\&.object_to_string(obj);\fR
\fB                int lastIx = bindings[i]\&.binding_name\&.length\-1;\fR
\fB \fR
\fB                // check to see if this is a naming context\fR
\fB                if (bindings[i]\&.binding_type == BindingType\&.ncontext) {\fR
\fB                    System\&.out\&.println("Context: " +\fR
\fB                        bindings[i]\&.binding_name[lastIx]\&.id);\fR
\fB                } else {\fR
\fB                    System\&.out\&.println("Object: " +\fR
\fB                        bindings[i]\&.binding_name[lastIx]\&.id);\fR
\fB                }\fR
\fB            }\fR
\fB        } catch (Exception e) {\fR
\fB            e\&.printStackTrace(System\&.err)\fR
\fB        }\fR
\fB    }\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
orbd(1)
.RE
.br
'pl 8.5i
'bp
