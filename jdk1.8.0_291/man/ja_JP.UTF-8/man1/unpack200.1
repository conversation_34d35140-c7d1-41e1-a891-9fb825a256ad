'\" t
.\" Copyright (c) 2004, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: unpack200
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Javaデプロイメント・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "unpack200" "1" "2013年11月21日" "JDK 8" "Javaデプロイメント・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
unpack200 \- \fBpack200\fR(1)で作成されたパック・ファイルを、WebデプロイメントのためにJARファイルに変換します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBunpack200\fR [ \fIoptions\fR ] input\-file \fIJAR\-file\fR
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIinput\-file\fR
.RS 4
入力ファイルの名前。pack200 gzipファイルかpack200ファイルを指定できます。入力ファイルには、\fBpack200\fR(1)で作成されたJARファイルを指定することもできます(手間は\fB0\fRです)。この場合、入力ファイルの内容はPack2000マーカーで出力JARファイルにコピーされます。
.RE
.PP
\fIJAR\-file\fR
.RS 4
出力JARファイル名。
.RE
.SH "説明"
.PP
\fBunpack200\fRコマンドは、\fBpack200\fR\fB(1)\fRで作成されたパック・ファイルをJARファイルに変換するネイティブ実装です。一般的な使用方法は次のとおりです。次の例では、デフォルトの\fBunpack200\fRコマンド設定で、\fBmyarchive\&.jar\fRファイルが\fBmyarchive\&.pack\&.gz\fRから作成されます。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBunpack200 myarchive\&.pack\&.gz myarchive\&.jar\fR
 
.fi
.if n \{\
.RE
.\}
.SH "オプション"
.PP
\-Hvalue \-\-deflate\-hint=\fIvalue\fR
.RS 4
JARファイル内のすべてのエントリに\fBtrue\fR、\fBfalse\fRまたは\fBkeep\fRのデフレーションを設定します。デフォルト・モードは\fBkeep\fRです。値が\fBtrue\fRまたは\fBfalse\fR場合、\fB\-\-deflate=hint\fRオプションはデフォルトの動作をオーバーライドして、出力JARファイル内のすべてのエントリのデフレーション・モードが設定されます。
.RE
.PP
\-r \-\-remove\-pack\-file
.RS 4
入力パック・ファイルを削除します。
.RE
.PP
\-v \-\-verbose
.RS 4
最小限のメッセージが表示されます。このオプションの複数の仕様には、より詳細なメッセージが表示されます。
.RE
.PP
\-q \-\-quiet
.RS 4
メッセージを表示せずに動作するように指定します。
.RE
.PP
\-lfilename \-\-log\-file=\fIfilename\fR
.RS 4
出力メッセージが記録されるログ・ファイルを指定します。
.RE
.PP
\-? \-h \-\-help
.RS 4
\fBunpack200\fRコマンドに関するヘルプ情報を出力します。
.RE
.PP
\-V \-\-version
.RS 4
\fBunpack200\fRコマンドに関するバージョン情報を出力します。
.RE
.PP
\-J\fIoption\fR
.RS 4
Java Virtual Machineにoptionを渡します。\fBoption\fRには、Javaアプリケーション起動ツールのリファレンス・ページに記載されているオプションを1つ指定します。たとえば、\fB\-J\-Xms48m\fRと指定すると、スタートアップ・メモリーは48MBに設定されます。java(1)を参照してください。
.RE
.SH "注意"
.PP
このコマンドと\fBunpack\fRコマンドを混同しないでください。これらは別製品です。
.PP
JDKに付属するJava SE API仕様との相違が見つかった場合には、仕様を優先してください。
.SH "終了ステータス"
.PP
次の終了値が返されます: 正常終了の場合は0、エラーが発生した場合は0より大きい値。
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
pack200(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jar(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jarsigner(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
「Compression Formats for Network Deployment」

http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/jweb/networking/compression_formats\&.html
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
「Java SE Technical Documentation」
(http://docs\&.oracle\&.com/javase/)
.RE
.br
'pl 8.5i
'bp
