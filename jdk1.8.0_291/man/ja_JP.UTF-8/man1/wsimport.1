'\" t
.\" Copyright (c) 2005, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: wsimport
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Java Webサービス・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "wsimport" "1" "2013年11月21日" "JDK 8" "Java Webサービス・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
wsimport \- Webアプリケーション・アーカイブ(WAR)ファイルにパッケージできるJAX\-WSポータブル・アーティファクトを生成して、Antタスクを指定します。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBwsimport\fR [ \fIoptions\fR ] \fIwsdl\fR
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
\fIwsdl\fR
.RS 4
Webサービスを呼び出す方法、パラメータに必要な値、返されるデータ構造についてのコンピュータで読取り可能な記述が含まれまるファイルです。
.RE
.SH "説明"
.PP
\fBwsimport\fRコマンドは、次のJAX\-WSポータブル・アーティファクトを生成します。これらのアーティファクトは、デプロイするWSDLおよびスキーマ・ドキュメントおよびエンドポイント実装とともに、WARファイルにパッケージできます。\fBwsimport\fRコマンドでは\fBwsimport\fR
Antタスクも提供されます。
http://jax\-ws\&.java\&.net/nonav/2\&.1\&.1/docs/wsimportant\&.htmlのWsimport Ant TaskページのToolsタブを参照してください。
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
サービス・エンドポイント・インタフェース(SEI)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
サービス
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
例外クラスは\fBwsdl:fault\fRからマップされます(存在する場合)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
非同期レスポンスBeanはレスポンス\fBwsdl:message\fRから派生します(存在する場合)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
JAXBが生成する値タイプ(スキーマのタイプからマップされたJavaクラス)
.RE
.PP
\fBwsgen\fRコマンドを起動するには、次を行います。
.PP
\fBOracle Solaris/Linux\fR:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB/bin/wsimport\&.sh \-help\fR
 
.fi
.if n \{\
.RE
.\}
.PP
\fBWindows\fRの場合:
.sp
.if n \{\
.RS 4
.\}
.nf
\fB\ebin\ewsimport\&.bat \-help\fR
 
.fi
.if n \{\
.RE
.\}
.SH "オプション"
.PP
\-d \fIdirectory\fR
.RS 4
生成される出力ファイルを格納する場所を指定します。
.RE
.PP
\-b \fIpath\fR
.RS 4
外部JAX\-WSまたはJAXBバインディング・ファイルを指定します。\fB\-b\fRオプションで複数のJAX\-WSおよびJAXBバインディング・ファイルを指定できます。これらのファイルを使用して、パッケージ名、Bean名などをカスタマイズできます。JAX\-WSおよびJAXBバインディング・ファイルの詳細は、
http://jax\-ws\&.java\&.net/nonav/2\&.1\&.1/docs/wsimportant\&.htmlのWSDL CustomizationのUsers Guideタブを参照してください
.RE
.PP
\-B \fIjaxbOption\fR
.RS 4
JAXBスキーマ・コンパイラに\fBjaxbOption\fRオプションを渡します。
.RE
.PP
\-catalog
.RS 4
外部エンティティ参照を解決するカタログ・ファイルを指定します。\fB\-catalog\fRオプションは、TR9401、XCatalog、OASIS XML Catalogの各形式をサポートしています。http://jax\-ws\&.java\&.net/nonav/2\&.1\&.1/docs/catalog\-support\&.htmlの
Catalog SupportページのUsers Guideタブを参照してください。
.RE
.PP
\-extension
.RS 4
ベンダー拡張を使用可能にします。拡張を使用すると、アプリケーションの移植性が失われたり、他の実装と連携できなくなる可能性があります。
.RE
.PP
\-help
.RS 4
\fBwsimport\fRコマンドに関するヘルプ・メッセージを表示します。
.RE
.PP
\-httpproxy: \fIhost\fR:\fIport\fR
.RS 4
HTTPプロキシ・サーバーを指定します。デフォルトは8080です。
.RE
.PP
\-keep
.RS 4
生成されたファイルを保持します
.RE
.PP
\-p \fIname\fR
.RS 4
ターゲット・パッケージ\fIname\fRを指定して、WSDLおよびスキーマ・バインディングのカスタマイズ、および仕様部で定義されたデフォルトのアルゴリズムをオーバーライドします。
.RE
.PP
\-s \fIdirectory\fR
.RS 4
生成されるソース・ファイルを格納する場所を指定します
.RE
.PP
\-verbose
.RS 4
コンパイラ・メッセージを表示します。
.RE
.PP
\-version
.RS 4
リリース情報を出力します。
.RE
.PP
\-wsdllocation \fIlocation\fR
.RS 4
\fB@WebServiceClient\&.wsdlLocation\fRの値を指定します。
.RE
.PP
\-target
.RS 4
指定されたJAX\-WS仕様バージョンに従って、コードを生成します。バージョン2\&.0では、JAX\-WS 2\&.0仕様に準拠したコードを生成します。
.RE
.PP
\-quiet
.RS 4
\fBwsimport\fRコマンド出力を抑制します。
.RE
.PP
\fB\-b\fRオプションを使用して、複数の\fBJAX\-WS\fRおよび\fBJAXB\fRバインディング・ファイルを指定できます。これらのファイルを使用して、パッケージ名やBean名など、様々なものをカスタマイズできます。\fBJAX\-WS\fRおよび\fBJAXB\fRバインディング・ファイルの詳細は、
https://jax\-ws\&.java\&.net/nonav/2\&.1\&.2/docs/customizations\&.htmlのカスタマイズ・ドキュメントを参照してください
.SH "非標準オプション"
.PP
\-XadditionalHeaders
.RS 4
リクエストまたはレスポンス・メッセージにバインドされないヘッダーをJavaメソッドのパラメータにマップします
.RE
.PP
\-Xauthfile \fIfile\fR
.RS 4
認証情報を含むファイルを指定するWSDL URIです。このURIの形式は次のとおりです。
.sp
http://\fIuser\-name\fR:\fBpassword\fR@\fIhost\-name\fR/\fIweb\-service\-name\fR>?wsdl\&.
.RE
.PP
\-Xdebug
.RS 4
デバッグ情報を出力します
.RE
.PP
\-Xno\-addressing\-databinding
.RS 4
W3C EndpointReferenceTypeとJavaのバインディングを有効にします。
.RE
.PP
\-Xnocompile
.RS 4
生成されたJavaファイルをコンパイルしません
.RE
.SH "例"
.PP
次の例では、Javaアーティファクトを生成し、\fBhttp://stockquote\&.example\&.com/quote?wsdl\fRをインポートしてアーティファクトをコンパイルします。
.sp
.if n \{\
.RS 4
.\}
.nf
\fBwsimport \-p stockquote http://stockquote\&.example\&.com/quote?wsdl\fR
 
.fi
.if n \{\
.RE
.\}
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
wsgen(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
http://jax\-ws\&.java\&.net/nonav/2\&.1\&.1/docs/wsimportant\&.htmlの
Wsimport Ant TaskページのToolsタブ
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
http://jax\-ws\&.java\&.net/nonav/2\&.1\&.1/docs/catalog\-support\&.htmlの
Catalog SupportページのUsers Guideタブ
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
http://jax\-ws\&.java\&.net/nonav/2\&.1\&.1/docs/wsimportant\&.htmlの
WSDL CustomizationページのUsers Guideタブ
.RE
.br
'pl 8.5i
'bp
