'\" t
.\" Copyright (c) 2005, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: xjc
.\" Language: Japanese
.\" Date: 2013年11月21日
.\" SectDesc: Java Webサービス・ツール
.\" Software: JDK 8
.\" Arch: 汎用
.\" Part Number: E58104-01
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "xjc" "1" "2013年11月21日" "JDK 8" "Java Webサービス・ツール"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "名前"
xjc \- XMLスキーマ・ファイルを完全注釈付きのJavaクラスにコンパイルします。
.SH "概要"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBxjc\fR [ \fIoptions\fR ] \fBschema\fR \fIfile/URL/dir/jar\fR \&.\&.\&. [\fB\-b\fR \fIbindinfo\fR ] \&.\&.\&.
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
コマンド行オプション。オプションを参照してください。
.RE
.PP
schema \fIfile/URL/dir/jar \&.\&.\&.\fR
.RS 4
XMLスキーマ・ファイルの場所。\fBdir\fRが指定されている場合は、すべてのスキーマ・ファイルがコンパイルされます。\fBjar\fRが指定されている場合は、\fB/META\-INF/sun\-jaxb\&.episode\fRバインディング・ファイルがコンパイルされます。
.RE
.PP
\-b \fIbindinfo\fR
.RS 4
バインディング・ファイルの場所。
.RE
.SH "説明"
.PP
プラットフォームのbinディレクトリにある適切な\fBxjc\fRシェル・スクリプトを使用して、バインディング・コンパイラを起動します。バインディング・コンパイラを実行するAntタスクもあります。http://jaxb\&.java\&.net/nonav/2\&.1\&.3/docs/xjcTask\&.htmlの
Using the XJC with Antを参照してください
.SH "オプション"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
非標準オプションも参照してください
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
非推奨で削除されたオプションも参照してください
.RE
.PP
\-nv
.RS 4
デフォルトでは、XJCバインディング・コンパイラは、ソース・スキーマを処理する前に厳密な検証を実行します。このオプションを使用すると、厳密なスキーマ検証か無効になります。これは、バインディング・コンパイラが検証を一切実行しないということではありません。より厳密でない検証を実行するということです。
.RE
.PP
\-extension
.RS 4
デフォルトでは、XJCバインディング・コンパイラは、JAXB仕様のCompatibilityの章で説明されているルールを厳密に強制します。付録E\&.2には、JAXB v1\&.0で完全にはサポートされていない一連のW3C XMLスキーマ機能が定義されています。場合によっては、このスイッチで有効になる\fB\-extension\fRモードでそれらの機能が使用できる場合があります。また、デフォルトの厳密なモードでは、仕様に定義されているバインディング・カスタマイズのみが使用できます。\fB\-extension\fRスイッチを指定すれば、JAXB Vendor Extensionを使用できます。
.RE
.PP
\-b \fIfile\fR
.RS 4
処理する外部バインディング・ファイルを1つまたは複数指定します。バインディング・ファイルごとに\fB\-b\fRスイッチを指定する必要があります。外部バインディング・ファイルの構文は柔軟です。複数のスキーマのカスタマイズが含まれる1つのバインディング・ファイルを使用したり、それらのカスタマイズを複数のバインディング・ファイルに分割したりできます。次に例を示します。\fBxjc schema1\&.xsd schema2\&.xsd schema3\&.xsd \-b bindings123\&.xjb\fR
\fBxjc schema1\&.xsd schema2\&.xsd schema3\&.xsd \-b bindings1\&.xjb \-b bindings2\&.xjb \-b bindings3\&.xjb\fRまた、コマンドラインにスキーマ・ファイルとバインディング・ファイルを指定する順番は任意です。
.RE
.PP
\-d \fIdir\fR
.RS 4
デフォルトでは、XJCバインディング・コンパイラは、Javaコンテンツ・クラスを現在のディレクトリに生成します。このオプションを使用すると、代替出力ディレクトリを指定できます。出力ディレクトリはすでに存在している必要があります。XJCバインディング・コンパイラでは作成されません。
.RE
.PP
\-p \fIpkg\fR
.RS 4
このコマンドライン・オプションでターゲット・パッケージを指定した場合、その指定内容は、パッケージ名に対するすべてのバインディング・カスタマイズや、仕様で規定されているデフォルトのパッケージ名アルゴリズムよりも優先されます。
.RE
.PP
\-httpproxy \fIproxy\fR
.RS 4
\fI[user[:password]@]proxyHost[:proxyPort]\fR形式でHTTPまたはHTTPSプロキシを指定します。古い\fB\-host\fRおよび\fB\-port\fRオプションは、下位互換性のために引き続きRIでサポートされますが、非推奨となりました。このオプションで指定されたパスワードは、topコマンドを使用する他のユーザーが表示できる引数です。セキュリティを高めるには、\fB\-httpproxyfile\fRを使用してください。
.RE
.PP
\-httpproxyfile file
.RS 4
ファイルを使用して、HTTPまたはHTTPSプロキシを指定します。形式は\fB\-httpproxy\fRオプションと同じですが、このファイル内に指定されたパスワードを他のユーザーが表示することはできません。
.RE
.PP
\-classpath arg
.RS 4
\fIjxb:javaType\fRおよびxjc:\fIsuperClass\fRカスタマイズが使用するクライアント・アプリケーションのクラス・ファイルの検索場所を指定します。
.RE
.PP
\-catalog file
.RS 4
外部エンティティ参照を解決するカタログ・ファイルを指定します。TR9401、XCatalogおよびOASIS XML Catalogの各形式がサポートされます。http://xerces\&.apache\&.org/xml\-commons/components/resolver/resolver\-article\&.htmlの
XML Entity and URI Resolversを参照してください
.RE
.PP
\-readOnly
.RS 4
デフォルトでは、XJCバインディング・コンパイラは、生成するJavaソース・ファイルを書込みから保護しません。このオプションを使用すると、XJCバインディング・コンパイラは生成されるJavaソースを強制的に読取り専用にします。
.RE
.PP
\-npa
.RS 4
\fB**/package\-info\&.java\fRへのパッケージ・レベルの注釈の生成を抑制します。このスイッチを使用して生成するコードでは、これらの注釈が他の生成済クラスに内部化されます。
.RE
.PP
\-no\-header
.RS 4
多少のメモとタイムスタンプを含むファイル・ヘッダー・コメントの生成を抑制します。これを使用すると、生成されたコードと\fBdiff\fRコマンドとの互換性がより強くなります。
.RE
.PP
\-target 2\&.0
.RS 4
JAXB 2\&.1機能に依存するコードを生成しないようにします。これにより、生成されたコードをJAXB 2\&.0ランタイム環境(Java SE 6など)で実行できるようになります。
.RE
.PP
\-xmlschema
.RS 4
入力スキーマをW3C XMLスキーマとして扱います(デフォルト)。このスイッチを指定しない場合、入力スキーマはW3C XMLスキーマと同じように扱われます。
.RE
.PP
\-relaxing
.RS 4
入力スキーマをRELAX NGとして扱います(試験的および未サポート)。RELAX NGスキーマのサポートはJAXB Vendor Extensionとして提供されています。
.RE
.PP
\-relaxing\-compact
.RS 4
入力スキーマをRELAX NG圧縮構文として処理します(試験的および未サポート)。RELAX NGスキーマのサポートはJAXB Vendor Extensionとして提供されています。
.RE
.PP
\-dtd
.RS 4
入力スキーマをXML DTDとして扱います(試験的および未サポート)。RELAX NGスキーマのサポートはJAXB Vendor Extensionとして提供されています。
.RE
.PP
\-wsdl
.RS 4
入力をWSDLとして扱い、その内部のスキーマをコンパイルします(試験的および未サポート)。
.RE
.PP
\-quiet
.RS 4
進捗情報や警告など、コンパイラの出力を抑制します。
.RE
.PP
\-verbose
.RS 4
情報メッセージを出力したり特定のエラー発生時にスタック・トレースを表示したりするなど、きわめて冗長になります。
.RE
.PP
\-help
.RS 4
コンパイラ・スイッチのサマリーを表示します。
.RE
.PP
\-version
.RS 4
コンパイラのバージョン情報を表示します。
.RE
.PP
\fIschema file/URL/dir\fR
.RS 4
コンパイル対象となる1つまたは複数のスキーマ・ファイルを指定します。ディレクトリを指定する場合、\fBxjc\fRコマンドはすべてのスキーマ・ファイルをスキャンしてコンパイルします。
.RE
.SS "非標準オプション"
.PP
\-XLocator
.RS 4
生成されたコードでは、非整列化の後にJava Beanインスタンスに含まれるソースXMLに関するSAX Locator情報が公開されます。
.RE
.PP
\-Xsync\-methods
.RS 4
生成されたすべてのメソッド・シグニチャに\fBsynchronized\fRキーワードが含められます。
.RE
.PP
\-mark\-generated
.RS 4
生成されたコードに注釈\fB@javax\&.annotation\&.Generated\fRを付けます。
.RE
.PP
\-episode file
.RS 4
コンパイルごとに指定されたエピソード・ファイルを生成します。
.RE
.SS "非推奨で削除されたオプション"
.PP
\-host & \-port
.RS 4
これらのオプションは\fB\-httpproxy\fRオプションで置き換えられました。これらのオプションは、後方互換性を確保するためにサポートされますが、ドキュメントには記載されず、将来のリリースで削除される可能性もあります。
.RE
.PP
\-use\-runtime
.RS 4
JAXB 2\&.0仕様では、移植性のあるランタイム環境が定義されたため、JAXB RIが\fB**/impl/runtime \fRパッケージを生成する必要がなくなりました。このため、このスイッチは不要となり、削除されました。
.RE
.PP
\-source
.RS 4
\fB\-source\fR互換性スイッチは、JAXB 2\&.0の最初のEarly Access版で導入されました。このスイッチはJAXB 2\&.0の今後のリリースから削除されます。1\&.0\&.xコードを生成する必要がある場合は、1\&.0\&.xコード・ベースのインストールを使用してください。
.RE
.SH "コンパイラの制限"
.PP
通常は、関連するすべてのスキーマを、同じバインディング・コンパイラ・スイッチを指定して1つの単位としてコンパイルするのが最も安全です。\fBxjc\fRコマンドの実行時には、次の制限リストに留意してください。これらの問題のほとんどは、\fBxjc\fRコマンドを何度か呼び出して複数のスキーマをコンパイルする場合にのみ当てはまります。
.PP
複数のスキーマを同時にコンパイルする場合は、ターゲットのJavaパッケージ名に次の優先順位のルールが適用されることに注意してください。
.sp
.RS 4
.ie n \{\
\h'-04' 1.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  1." 4.2
.\}
\fB\-p\fRオプションが最も優先されます。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 2.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  2." 4.2
.\}
\fIjaxb:package\fRのカスタマイズ。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 3.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  3." 4.2
.\}
\fBtargetNamespace\fRが宣言されている場合は、\fBt\fR\fBargetNamespace\fRを仕様で定義されているJavaパッケージ名のアルゴリズムに適用します。
.RE
.sp
.RS 4
.ie n \{\
\h'-04' 4.\h'+01'\c
.\}
.el \{\
.sp -1
.IP "  4." 4.2
.\}
\fBtargetNamespace\fRが宣言されていない場合は、\fBgenerated\fRという名前のハードコードされたパッケージを使用します。
.RE
.PP
1つのネームスペースが複数の\fIjaxb:schemaBindings\fRを持つことはできないため、異なるJavaパッケージにコンパイラされる同一ターゲット・ネームスペースが2つのスキーマを持つことはできません。
.PP
同じJavaパッケージにコンパイラされるスキーマはすべて、同時にXJCバインディング・コンパイラに送信される必要があります。別々にコンパイルすると、予想どおりに機能しません。
.PP
複数のスキーマ・ファイルにまたがる要素置換グループは、同時にコンパイルする必要があります。
.SH "関連項目"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
http://jaxb\&.java\&.net/nonav/2\&.2\&.3u1/docs/xjc\&.htmlの
Binding Compiler (xjc)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
http://www\&.oracle\&.com/technetwork/articles/javase/index\-140168\&.htmlの
Java Architecture for XML Binding (JAXB)
.RE
.br
'pl 8.5i
'bp
