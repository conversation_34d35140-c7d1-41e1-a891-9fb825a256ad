'\" t
.\"  Copyright (c) 1998, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Basic Tools
.\"     Title: extcheck.1
.\"
.if n .pl 99999
.TH extcheck 1 "21 November 2013" "JDK 8" "Basic Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
extcheck \- Detects version conflicts between a target Java Archive (JAR) file and currently installed extension JAR files\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBextcheck\fR [\fIoptions\fR] \fItargetfile\&.jar\fR
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
\fItargetfile\&.jar\fR
The target JAR file against which the currently installed extension JAR files are compared to detect version conflicts\&.
.SH DESCRIPTION    
The \f3extcheck\fR command checks a specified JAR file for title and version conflicts with any extensions installed in the Java SE SDK\&. Before installing an extension, you can use this utility to see whether the same or a more recent version of the extension is already installed\&.
.PP
The \f3extcheck\fR command compares the Specification-title and Specification-version headers in the manifest of the \f3targetfile\&.jar\fR file against the corresponding headers in all JAR files currently installed in the extension directory\&. By default, the extension directory is \f3jre/lib/ext\fR on Oracle Solaris and \f3\ejre\elib\eext\fR on Windows\&. The \f3extcheck\fR command compares version numbers in the same way as the \f3java\&.lang\&.Package\&.isCompatibleWith\fR method\&.
.PP
If no conflict is detected, then the return code is 0\&.
.PP
If the manifest of any JAR file in the extensions directory has the same \f3Specification-title\fR and the same or a newer \f3Specification-version\fR number, then a non-zero error code is returned\&. A non-zero error code is also returned when \f3targetfile\&.jar\fR does not have the \f3Specification-title\fR or \f3Specification-version\fR attributes in its manifest file\&.
.SH OPTIONS    
.TP
-verbose
.br
Lists JAR files in the extension directory as they are checked\&. Additionally, manifest attributes of the target JAR file and any conflicting JAR files are also reported\&.
.TP
-J\fIoption\fR
.br
Passes \fIoption\fR to the Java Virtual Machine (JVM), where option is one of the options described on the reference page for the Java launcher\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&. See java(1)\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
jar(1)
.RE
.br
'pl 8.5i
'bp
