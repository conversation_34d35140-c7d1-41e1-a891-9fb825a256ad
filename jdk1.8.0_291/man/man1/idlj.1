'\" t
.\"  Copyright (c) 2001, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Java IDL and RMI-IIOP Tools
.\"     Title: idlj.1
.\"
.if n .pl 99999
.TH idlj 1 "21 November 2013" "JDK 8" "Java IDL and RMI-IIOP Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
idlj \- Generates Java bindings for a specified Interface Definition Language (IDL) file\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBidlj\fR [ \fIoptions\fR ] \fIidlfile\fR
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&. Options can appear in any order, but must precede the \f3idlfile\fR\&.
.TP     
\fIidlfile\fR
The name of a file that contains Interface Definition Language (IDL) definitions\&.
.SH DESCRIPTION    
The IDL-to-Java Compiler generates the Java bindings for a specified IDL file\&. For binding details, see Java IDL: IDL to Java Language Mapping at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/mapping/jidlMapping\&.html
.PP
Some earlier releases of the IDL-to-Java compiler were named \f3idltojava\fR\&.
.SS EMIT\ CLIENT\ AND\ SERVER\ BINDINGS    
The following \f3idlj\fR command generates an IDL file named \f3My\&.idl\fR with client-side bindings\&.
.sp     
.nf     
\f3idlj My\&.idl\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
The previous syntax is equivalent to the following:
.sp     
.nf     
\f3idlj \-fclient My\&.idl\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
The next example generates the server-side bindings, and includes the client-side bindings plus the skeleton, all of which are POA (Inheritance Model)\&.
.sp     
.nf     
\f3idlg \-fserver My\&.idl\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
If you want to generate both client and server-side bindings, then use one of the following (equivalent) commands:
.sp     
.nf     
\f3idlj \-fclient \-fserver My\&.idl\fP
.fi     
.nf     
\f3idlj \-fall My\&.idl\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
There are two possible server-side models: the Portal Servant Inheritance Model and the Tie Model\&. See Tie Delegation Model\&.
.PP
\f3Portable Servant Inheritance Model\fR\&. The default server-side model is the Portable Servant Inheritance Model\&. Given an interface \f3My\fR defined in \f3My\&.idl\fR, the file \f3MyPOA\&.java\fR is generated\&. You must provide the implementation for the \f3My\fR interface, and the \f3My\fR interface must inherit from the \f3MyPOA\fR class\&. \f3MyPOA\&.java\fR is a stream-based skeleton that extends the \f3org\&.omg\&.PortableServer\&.Servant\fR class at http://docs\&.oracle\&.com/javase/8/docs/api/org/omg/PortableServer/Servant\&.html The \f3My\fR interface implements the \f3callHandler\fR interface and the operations interface associated with the IDL interface the skeleton implements\&.The \f3PortableServer\fR module for the Portable Object Adapter (POA) defines the native \f3Servant\fR type\&. See Portable Object Adapter (POA) at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/POA\&.html In the Java programming language, the \f3Servant\fR type is mapped to the Java \f3org\&.omg\&.PortableServer\&.Servant\fR class\&. It serves as the base class for all POA servant implementations and provides a number of methods that can be called by the application programmer, and methods that are called by the POA and that can be overridden by the user to control aspects of servant behavior\&.Another option for the Inheritance Model is to use the \f3-oldImplBase\fR flag to generate server-side bindings that are compatible with releases of the Java programming language before Java SE 1\&.4\&. The -\f3oldImplBase\fR flag is nonstandard, and these APIs are deprecated\&. You would use this flag only for compatibility with existing servers written in Java SE 1\&.3\&. In that case, you would need to modify an existing make file to add the \f3-oldImplBase\fR flag to the \f3idlj\fR compiler\&. Otherwise POA-based server-side mappings are generated\&. To generate server-side bindings that are backward compatible, do the following:
.sp     
.nf     
\f3idlj \-fclient \-fserver \-oldImplBase My\&.idl\fP
.fi     
.nf     
\f3idlj \-fall \-oldImplBase My\&.idl\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
Given an interface \f3My\fR defined in \f3My\&.idl\fR, the file \f3_MyImplBase\&.java\fR is generated\&. You must provide the implementation for the \f3My\fR interface, and the \f3My\fR interface must inherit from the \f3_MyImplBase\fR class\&.
.PP
\f3Tie Delegation Model\fR\&. The other server-side model is called the Tie Model\&. This is a delegation model\&. Because it is not possible to generate ties and skeletons at the same time, they must be generated separately\&. The following commands generate the bindings for the Tie Model:
.sp     
.nf     
\f3idlj \-fall My\&.idl\fP
.fi     
.nf     
\f3idlj \-fallTIE My\&.idl\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
For the \f3My\fR interface, the second command generates \f3MyPOATie\&.java\fR\&. The constructor to the \f3MyPOATie\fR class takes a delegate\&. In this example, using the default POA model, the constructor also needs a POA\&. You must provide the implementation for the delegate, but it does not have to inherit from any other class, only the interface \f3MyOperations\fR\&. To use it with the ORB, you must wrap your implementation within the \f3MyPOATie\fR class, for example:
.sp     
.nf     
\f3ORB orb = ORB\&.init(args, System\&.getProperties());\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3// Get reference to rootpoa & activate the POAManager\fP
.fi     
.nf     
\f3POA rootpoa = (POA)orb\&.resolve_initial_references("RootPOA");\fP
.fi     
.nf     
\f3rootpoa\&.the_POAManager()\&.activate();\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3// create servant and register it with the ORB\fP
.fi     
.nf     
\f3MyServant myDelegate = new MyServant();\fP
.fi     
.nf     
\f3myDelegate\&.setORB(orb); \fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3// create a tie, with servant being the delegate\&.\fP
.fi     
.nf     
\f3MyPOATie tie = new MyPOATie(myDelegate, rootpoa);\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3// obtain the objectRef for the tie\fP
.fi     
.nf     
\f3My ref = tie\&._this(orb);\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
You might want to use the Tie model instead of the typical Inheritance model when your implementation must inherit from some other implementation\&. Java allows any number of interface inheritance, but there is only one slot for class inheritance\&. If you use the inheritance model, then that slot is used up\&. With the Tie Model, that slot is freed up for your own use\&. The drawback is that it introduces a level of indirection: one extra method call occurs when a method is called\&.
.PP
For server-side generation, Tie model bindings that are compatible with versions of the IDL to Java language mapping in versions earlier than Java SE 1\&.4\&.
.sp     
.nf     
\f3idlj \-oldImplBase \-fall My\&.idl\fP
.fi     
.nf     
\f3idlj \-oldImplBase \-fallTIE My\&.idl\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
For the \f3My\fR interface, the this generates \f3My_Tie\&.java\fR\&. The constructor to the \f3My_Tie\fR class takes an \f3impl\fR object\&. You must provide the implementation for \f3impl\fR, but it does not have to inherit from any other class, only the interface \f3HelloOperations\fR\&. But to use it with the ORB, you must wrap your implementation within \f3My_Tie\fR, for example:
.sp     
.nf     
\f3ORB orb = ORB\&.init(args, System\&.getProperties());\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3// create servant and register it with the ORB\fP
.fi     
.nf     
\f3MyServant myDelegate = new MyServant();\fP
.fi     
.nf     
\f3myDelegate\&.setORB(orb); \fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3// create a tie, with servant being the delegate\&.\fP
.fi     
.nf     
\f3MyPOATie tie = new MyPOATie(myDelegate);\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3// obtain the objectRef for the tie\fP
.fi     
.nf     
\f3My ref = tie\&._this(orb);\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
.SS SPECIFY\ ALTERNATE\ LOCATIONS\ FOR\ EMITTED\ FILES    
If you want to direct the emitted files to a directory other than the current directory, then call the compiler this way: \f3i\fR\f3dlj -td /altdir My\&.idl\fR\&.
.PP
For the \f3My\fR interface, the bindings are emitted to \f3/altdir/My\&.java\fR, etc\&., instead of \f3\&./My\&.java\fR\&.
.SS SPECIFY\ ALTERNATE\ LOCATIONS\ FOR\ INCLUDE\ FILES    
If the \f3My\&.idl\fR file includes another \f3idl\fR file, \f3MyOther\&.idl\fR, then the compiler assumes that the \f3MyOther\&.idl\fR file resides in the local directory\&. If it resides in \f3/includes\fR, for example, then you call the compiler with the following command:
.sp     
.nf     
\f3idlj \-i /includes My\&.idl\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
If \f3My\&.idl\fR also included \f3Another\&.idl\fR that resided in \f3/moreIncludes\fR, for example, then you call the compiler with the following command:
.sp     
.nf     
\f3idlj \-i /includes \-i /moreIncludes My\&.idl\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
Because this form of \f3include\fR can become long, another way to indicate to the compiler where to search for included files is provided\&. This technique is similar to the idea of an environment variable\&. Create a file named idl\&.config in a directory that is listed in your \f3CLASSPATH\fR variable\&. Inside of \f3idl\&.config\fR, provide a line with the following form:
.sp     
.nf     
\f3includes=/includes;/moreIncludes\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
The compiler will find this file and read in the includes list\&. Note that in this example the separator character between the two directories is a semicolon (;)\&. This separator character is platform dependent\&. On the Windows platform, use a semicolon, on the Unix platform, use a colon, and so on\&.
.SS EMIT\ BINDINGS\ FOR\ INCLUDE\ FILES    
By default, only those interfaces, structures, and so on, that are defined in the \f3idl\fR file on the command line have Java bindings generated for them\&. The types defined in included files are not generated\&. For example, assume the following two \f3idl\fR files:
.sp     
.nf     
\f3My\&.idl file:\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3#include <MyOther\&.idl>\fP
.fi     
.nf     
\f3interface My\fP
.fi     
.nf     
\f3{\fP
.fi     
.nf     
\f3};\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3MyOther\&.idl file:\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3interface MyOther\fP
.fi     
.nf     
\f3{\fP
.fi     
.nf     
\f3};\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
There is a caveat to the default rule\&. Any \f3#include\fR statements that appear at the global scope are treated as described\&. These \f3#include\fR statements can be thought of as import statements\&. The \f3#include\fR statements that appear within an enclosed scope are treated as true \f3#include\fR statements, which means that the code within the included file is treated as though it appeared in the original file and, therefore, Java bindings are emitted for it\&. Here is an example:
.sp     
.nf     
\f3My\&.idl file:\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3#include <MyOther\&.idl>\fP
.fi     
.nf     
\f3interface My\fP
.fi     
.nf     
\f3{\fP
.fi     
.nf     
\f3  #include <Embedded\&.idl>\fP
.fi     
.nf     
\f3};\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3MyOther\&.idl file:\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3interface MyOther\fP
.fi     
.nf     
\f3{\fP
.fi     
.nf     
\f3};\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3Embedded\&.idl\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3enum E {one, two, three};\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
Run\f3idlj My\&.idl\fRto generate the following list of Java files\&. Notice that \f3MyOther\&.java\fR is not generated because it is defined in an import-like \f3#include\fR\&. But \f3E\&.java\fR was generated because it was defined in a true \f3#include\fR\&. Notice that because the \f3Embedded\&.idl\fR file is included within the scope of the interface \f3My\fR, it appears within the scope of \f3My\fR (in \f3MyPackage\fR)\&. If the \f3-emitAll\fR flag had been used, then all types in all included files would have been emitted\&.
.sp     
.nf     
\f3\&./MyHolder\&.java\fP
.fi     
.nf     
\f3\&./MyHelper\&.java\fP
.fi     
.nf     
\f3\&./_MyStub\&.java\fP
.fi     
.nf     
\f3\&./MyPackage\fP
.fi     
.nf     
\f3\&./MyPackage/EHolder\&.java\fP
.fi     
.nf     
\f3\&./MyPackage/EHelper\&.java\fP
.fi     
.nf     
\f3\&./MyPackage/E\&.java\fP
.fi     
.nf     
\f3\&./My\&.java\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
.SS INSERT\ PACKAGE\ PREFIXES    
Suppose that you work for a company named ABC that has constructed the following IDL file:
.sp     
.nf     
\f3Widgets\&.idl file:\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3module Widgets\fP
.fi     
.nf     
\f3{\fP
.fi     
.nf     
\f3  interface W1 {\&.\&.\&.};\fP
.fi     
.nf     
\f3  interface W2 {\&.\&.\&.};\fP
.fi     
.nf     
\f3};\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
If you run this file through the IDL-to-Java compiler, then the Java bindings for W1 and W2 are placed within the \f3Widgets\fR package\&. There is an industry convention that states that a company\&'s packages should reside within a package named \f3com\&.<company name>\fR\&. To follow this convention, the package name should be \f3com\&.abc\&.Widgets\fR\&. To place this package prefix onto the Widgets module, execute the following:
.sp     
.nf     
\f3idlj \-pkgPrefix Widgets com\&.abc Widgets\&.idl\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
If you have an IDL file that includes Widgets\&.idl, then the \f3-pkgPrefix\fR flag must appear in that command also\&. If it does not, then your IDL file will be looking for a \f3Widgets\fR package rather than a \f3com\&.abc\&.Widgets\fR package\&.
.PP
If you have a number of these packages that require prefixes, then it might be easier to place them into the idl\&.config file described previously\&. Each package prefix line should be of the form: \f3PkgPrefix\&.<type>=<prefix>\fR\&. The line for the previous example would be \f3PkgPrefix\&.Widgets=com\&.abc\fR\&. This option does not affect the Repository ID\&.
.SS DEFINE\ SYMBOLS\ BEFORE\ COMPILATION    
You might need to define a symbol for compilation that is not defined within the IDL file, perhaps to include debugging code in the bindings\&. The command \f3idlj -d MYDEF My\&.idl\fRis equivalent to putting the line \f3#define MYDEF\fR inside My\&.idl\&.
.SS PRESERVE\ PREEXISTING\ BINDINGS    
If the Java binding files already exist, then the \f3-keep\fR flag keeps the compiler from overwriting them\&. The default is to generate all files without considering that they already exist\&. If you have customized those files (which you should not do unless you are very comfortable with their contents), then the \f3-keep\fR option is very useful\&. The command \f3idlj -keep My\&.idl\fR emits all client-side bindings that do not already exist\&.
.SS VIEW\ COMPILATION\ PROGRESS    
The IDL-to-Java compiler generates status messages as it progresses through its phases of execution\&. Use the \f3-v\fR option to activate the verbose mode: \f3idlj -v My\&.idl\fR\&.
.PP
By default the compiler does not operate in verbose mode
.SS DISPLAY\ VERSION\ INFORMATION    
To display the build version of the IDL-to-Java compiler, specify the \f3-version\fR option on the command-line: \f3idlj -version\fR\&.
.PP
Version information also appears within the bindings generated by the compiler\&. Any additional options appearing on the command-line are ignored\&.
.SH OPTIONS    
.TP
-d \fIsymbol\fR
.br
This is equivalent to the following line in an IDL file:
.sp     
.nf     
\f3#define \fIsymbol\fR\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     

.TP
-demitAll
.br
Emit all types, including those found in \f3#include\fR files\&.
.TP
-fside
.br
Defines what bindings to emit\&. The \f3side\fR parameter can be \f3client\fR, \f3server\fR, \f3serverTIE\fR, \f3all\fR, or \f3allTIE\fR\&. The \f3-fserverTIE\fR and \f3-fallTIE\fR options cause delegate model skeletons to be emitted\&. Defaults to \f3-fclient\fR when the flag is not specified\&.
.TP
-i \fIinclude-path\fR
.br
By default, the current directory is scanned for included files\&. This option adds another directory\&.
.TP
-i \fIkeep\fR
.br
If a file to be generated already exists, then do not overwrite it\&. By default it is overwritten\&.
.TP
-noWarn
.br
Suppress warning messages\&.
.TP
-oldImplBase
.br
Generates skeletons compatible with pre-1\&.4 JDK ORBs\&. By default, the POA Inheritance Model server-side bindings are generated\&. This option provides backward-compatibility with earlier releases of the Java programming language by generating server-side bindings that are \f3ImplBase\fR Inheritance Model classes\&.
.TP
-pkgPrefix \fItype\fR\fIprefix\fR
.br
Wherever \f3type\fR is encountered at file scope, prefix the generated Java package name with \f3prefix\fR for all files generated for that type\&. The type is the simple name of either a top-level module, or an IDL type defined outside of any module\&.
.TP
-pkgTranslate \fItype\fR\fIpackage\fR
.br
Whenever the module name type is encountered in an identifier, replace it in the identifier with package for all files in the generated Java package\&. Note that \f3pkgPrefix\fR changes are made first\&. The type value is the simple name of either a top-level module, or an IDL type defined outside of any module and must match the full package name exactly\&.

If more than one translation matches an identifier, then the longest match is chosen as shown in the following example:

\fICommand\fR:
.sp     
.nf     
\f3pkgTranslate type pkg \-pkgTranslate type2\&.baz pkg2\&.fizz\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     


\fIResulting Translation\fR:
.sp     
.nf     
\f3type => pkg\fP
.fi     
.nf     
\f3type\&.ext => pkg\&.ext\fP
.fi     
.nf     
\f3type\&.baz => pkg2\&.fizz\fP
.fi     
.nf     
\f3type2\&.baz\&.pkg => pkg2\&.fizz\&.pkg\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     


The following package names \f3org\fR, \f3org\fR\&.o\f3mg\fR, or any subpackages of \f3org\&.omg\fR cannot be translated\&. Any attempt to translate these packages results in uncompilable code, and the use of these packages as the first argument after \f3-pkgTranslate\fR is treated as an error\&.
.TP
-skeletonName \fIxxx%yyy\fR
.br
Use \f3xxx%yyy\fR as the pattern for naming the skeleton\&. The defaults are: \f3%POA\fR for the \f3POA\fR base class (\f3-fserver\fR or \f3-fall\fR), and \f3_%ImplBase\fR for the \f3oldImplBase\fR class (-\f3oldImplBase\fR) and (\f3-fserver\fR or \f3-fall\fR))\&.
.TP
-td \fIdir\fR
.br
Use \fIdir\fR for the output directory instead of the current directory\&.
.TP
-tieName \fIxxx%yyy\fR
.br
Use \f3xxx%yyy\fR according to the pattern\&. The defaults are: \f3%POA\fR for the \f3POA\fR base class (\f3-fserverTie or -fallTie\fR), and \f3_%Tie\fR for the \f3oldImplBase\fR tie class (-\f3oldImplBase\fR) and (\f3-fserverTie\fR or \f3-fallTie\fR))
.TP
-nowarn, -verbose
.br
Displays release information and terminates\&.
.TP
-version
.br
Displays release information and terminates\&.
.SH RESTRICTIONS    
Escaped identifiers in the global scope cannot have the same spelling as IDL primitive types, \f3Object\fR, or \f3ValueBase\fR\&. This is because the symbol table is preloaded with these identifiers\&. Allowing them to be redefined would overwrite their original definitions\&. Possible permanent restriction\&.
.PP
The \f3fixed\fR IDL type is not supported\&.
.SH KNOWN\ PROBLEMS    
No import is generated for global identifiers\&. If you call an unexported local \f3impl\fR object, then you do get an exception, but it seems to be due to a \f3NullPointerException\fR in the \f3ServerDelegate\fR DSI code\&.
.RE
.br
'pl 8.5i
'bp
