'\" t
.\"  Copyright (c) 1998, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Security Tools
.\"     Title: jarsigner.1
.\"
.if n .pl 99999
.TH jarsigner 1 "21 November 2013" "JDK 8" "Security Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
jarsigner \- Signs and verifies Java Archive (JAR) files\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjarsigner\fR [ \fIoptions\fR ] \fIjar\-file\fR \fIalias\fR
.fi     
.nf     

\fBjarsigner\fR \fB\-verify\fR [ \fIoptions\fR ] \fIjar\-file\fR [\fIalias \&.\&.\&.\fR]
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP
-verify
.br
The \f3-verify\fR option can take zero or more keystore alias names after the JAR file name\&. When the \f3-verify\fR option is specified, the \f3jarsigner\fR command checks that the certificate used to verify each signed entry in the JAR file matches one of the keystore aliases\&. The aliases are defined in the keystore specified by \f3-keystore\fR or the default keystore\&.

If you also specified the \f3-strict\fR option, and the \f3jarsigner\fR command detected severe warnings, the message, "jar verified, with signer errors" is displayed\&.
.TP     
\fIjar-file\fR
The JAR file to be signed\&.

If you also specified the \f3-strict\fR option, and the \f3jarsigner\fR command detected severe warnings, the message, "jar signed, with signer errors" is displayed\&.
.TP     
\fIalias\fR
The aliases are defined in the keystore specified by \f3-keystore\fR or the default keystore\&.
.SH DESCRIPTION    
The \f3jarsigner\fR tool has two purposes:
.TP 0.2i    
\(bu
To sign Java Archive (JAR) files\&.
.TP 0.2i    
\(bu
To verify the signatures and integrity of signed JAR files\&.
.PP
The JAR feature enables the packaging of class files, images, sounds, and other digital data in a single file for faster and easier distribution\&. A tool named \f3jar\fR enables developers to produce JAR files\&. (Technically, any zip file can also be considered a JAR file, although when created by the \f3jar\fR command or processed by the \f3jarsigner\fR command, JAR files also contain a \f3META-INF/MANIFEST\&.MF\fR file\&.)
.PP
A digital signature is a string of bits that is computed from some data (the data being signed) and the private key of an entity (a person, company, and so on)\&. Similar to a handwritten signature, a digital signature has many useful characteristics:
.TP 0.2i    
\(bu
Its authenticity can be verified by a computation that uses the public key corresponding to the private key used to generate the signature\&.
.TP 0.2i    
\(bu
It cannot be forged, assuming the private key is kept secret\&.
.TP 0.2i    
\(bu
It is a function of the data signed and thus cannot be claimed to be the signature for other data as well\&.
.TP 0.2i    
\(bu
The signed data cannot be changed\&. If the data is changed, then the signature cannot be verified as authentic\&.
.PP
To generate an entity\&'s signature for a file, the entity must first have a public/private key pair associated with it and one or more certificates that authenticate its public key\&. A certificate is a digitally signed statement from one entity that says that the public key of another entity has a particular value\&.
.PP
The \f3jarsigner\fR command uses key and certificate information from a keystore to generate digital signatures for JAR files\&. A keystore is a database of private keys and their associated X\&.509 certificate chains that authenticate the corresponding public keys\&. The \f3keytool\fR command is used to create and administer keystores\&.
.PP
The \f3jarsigner\fR command uses an entity\&'s private key to generate a signature\&. The signed JAR file contains, among other things, a copy of the certificate from the keystore for the public key corresponding to the private key used to sign the file\&. The \f3jarsigner\fR command can verify the digital signature of the signed JAR file using the certificate inside it (in its signature block file)\&.
.PP
The \f3jarsigner\fR command can generate signatures that include a time stamp that lets a systems or deployer (including Java Plug-in) to check whether the JAR file was signed while the signing certificate was still valid\&. In addition, APIs allow applications to obtain the timestamp information\&.
.PP
At this time, the \f3jarsigner\fR command can only sign JAR files created by the \f3jar\fR command or zip files\&. JAR files are the same as zip files, except they also have a \f3META-INF/MANIFEST\&.MF\fR file\&. A \f3META-INF/MANIFEST\&.MF\fR file is created when the \f3jarsigner\fR command signs a zip file\&.
.PP
The default \f3jarsigner\fR command behavior is to sign a JAR or zip file\&. Use the \f3-verify\fR option to verify a signed JAR file\&.
.PP
The \f3jarsigner\fR command also attempts to validate the signer\&'s certificate after signing or verifying\&. If there is a validation error or any other problem, the command generates warning messages\&. If you specify the \f3-strict\fR option, then the command treats severe warnings as errors\&. See Errors and Warnings\&.
.SS KEYSTORE\ ALIASES    
All keystore entities are accessed with unique aliases\&.
.PP
When you use the \f3jarsigner\fR command to sign a JAR file, you must specify the alias for the keystore entry that contains the private key needed to generate the signature\&. For example, the following command signs the JAR file named \f3MyJARFile\&.jar\fR with the private key associated with the alias \f3duke\fR in the keystore named \f3mystore\fR in the \f3working\fR directory\&. Because no output file is specified, it overwrites \f3MyJARFile\&.jar\fR with the signed JAR file\&.
.sp     
.nf     
\f3jarsigner \-keystore /working/mystore \-storepass <keystore password>\fP
.fi     
.nf     
\f3      \-keypass <private key password> MyJARFile\&.jar duke\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
Keystores are protected with a password, so the store password must be specified\&. You are prompted for it when you do not specify it on the command line\&. Similarly, private keys are protected in a keystore with a password, so the private key\&'s password must be specified, and you are prompted for the password when you do not specify it on the command line and it is not the same as the store password\&.
.SS KEYSTORE\ LOCATION    
The \f3jarsigner\fR command has a \f3-keystore\fR option for specifying the URL of the keystore to be used\&. The keystore is by default stored in a file named \f3\&.keystore\fR in the user\&'s home directory, as determined by the \f3user\&.home\fR system property\&.
.PP
On Oracle Solaris systems, \f3user\&.home\fR defaults to the user\&'s home directory\&.
.PP
The input stream from the \f3-keystore\fR option is passed to the \f3KeyStore\&.load\fR method\&. If \f3NONE\fR is specified as the URL, then a null stream is passed to the \f3KeyStore\&.load\fR method\&. \f3NONE\fR should be specified when the \f3KeyStore\fR class is not file based, for example, when it resides on a hardware token device\&.
.SS KEYSTORE\ IMPLEMENTATION    
The \f3KeyStore\fR class provided in the \f3java\&.security\fR package supplies a number of well-defined interfaces to access and modify the information in a keystore\&. You can have multiple different concrete implementations, where each implementation is for a particular type of keystore\&.
.PP
Currently, there are two command-line tools that use keystore implementations (\f3keytool\fR and \f3jarsigner\fR), and a GUI-based tool named Policy Tool\&. Because the \f3KeyStore\fR class is publicly available, JDK users can write additional security applications that use it\&.
.PP
There is a built-in default implementation provided by Oracle that implements the keystore as a file, that uses a proprietary keystore type (format) named JKS\&. The built-in implementation protects each private key with its individual password and protects the integrity of the entire keystore with a (possibly different) password\&.
.PP
Keystore implementations are provider-based, which means the application interfaces supplied by the \f3KeyStore\fR class are implemented in terms of a Service Provider Interface (SPI)\&. There is a corresponding abstract \f3KeystoreSpi\fR class, also in the \f3java\&.security package\fR, that defines the Service Provider Interface methods that providers must implement\&. The term provider refers to a package or a set of packages that supply a concrete implementation of a subset of services that can be accessed by the Java Security API\&. To provide a keystore implementation, clients must implement a provider and supply a \f3KeystoreSpi\fR subclass implementation, as described in How to Implement a Provider in the Java Cryptography Architecture at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/crypto/HowToImplAProvider\&.html
.PP
Applications can choose different types of keystore implementations from different providers, with the \f3getInstance\fR factory method in the \f3KeyStore\fR class\&. A keystore type defines the storage and data format of the keystore information and the algorithms used to protect private keys in the keystore and the integrity of the keystore itself\&. Keystore implementations of different types are not compatible\&.
.PP
The \f3jarsigner\fR and \f3policytool\fR commands can read file-based keystores from any location that can be specified using a URL\&. In addition, these commands can read non-file-based keystores such as those provided by MSCAPI on Windows and PKCS11 on all platforms\&.
.PP
For the \f3jarsigner\fR and \f3keytool\fR commands, you can specify a keystore type at the command line with the \f3-storetype\fR option\&. For Policy Tool, you can specify a keystore type with the \fIEdit\fR command in the \fIKeyStore\fR menu\&.
.PP
If you do not explicitly specify a keystore type, then the tools choose a keystore implementation based on the value of the \f3keystore\&.type\fR property specified in the security properties file\&. The security properties file is called \f3java\&.security\fR, and it resides in the JDK security properties directory, \f3java\&.home/lib/security\fR, where \f3java\&.home\fR is the runtime environment\&'s directory\&. The \f3jre\fR directory in the JDK or the top-level directory of the Java Runtime Environment (JRE)\&.
.PP
Each tool gets the \f3keystore\&.type\fR value and then examines all the installed providers until it finds one that implements keystores of that type\&. It then uses the keystore implementation from that provider\&.
.PP
The \f3KeyStore\fR class defines a static method named \f3getDefaultType\fR that lets applications and applets retrieve the value of the \f3keystore\&.type\fR property\&. The following line of code creates an instance of the default keystore type as specified in the \f3keystore\&.type property\fR:
.sp     
.nf     
\f3KeyStore keyStore = KeyStore\&.getInstance(KeyStore\&.getDefaultType());\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
The default keystore type is \f3jks\fR (the proprietary type of the keystore implementation provided by Oracle)\&. This is specified by the following line in the security properties file:
.sp     
.nf     
\f3keystore\&.type=jks\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
Case does not matter in keystore type designations\&. For example, \f3JKS\fR is the same as \f3jks\fR\&.
.PP
To have the tools use a keystore implementation other than the default, change that line to specify a different keystore type\&. For example, if you have a provider package that supplies a keystore implementation for a keystore type called \f3pkcs12\fR, then change the line to the following:
.sp     
.nf     
\f3keystore\&.type=pkcs12\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
\fINote:\fR If you use the PKCS 11 provider package, then see "KeyTool" and "JarSigner" in Java PKCS #11 Reference Guide at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/p11guide\&.html
.SS SUPPORTED\ ALGORITHMS    
By default, the \f3jarsigner\fR command signs a JAR file using one of the following algorithms:
.TP 0.2i    
\(bu
Digital Signature Algorithm (DSA) with the SHA1 digest algorithm
.TP 0.2i    
\(bu
RSA algorithm with the SHA256 digest algorithm
.TP 0.2i    
\(bu
Elliptic Curve (EC) cryptography algorithm with the SHA256 with Elliptic Curve Digital Signature Algorithm (ECDSA)\&.
.PP
If the signer\&'s public and private keys are DSA keys, then \f3jarsigner\fR signs the JAR file with the \f3SHA1withDSA\fR algorithm\&. If the signer\&'s keys are RSA keys, then \f3jarsigner\fR attempts to sign the JAR file with the \f3SHA256withRSA\fR algorithm\&. If the signer\&'s keys are EC keys, then \f3jarsigner\fR signs the JAR file with the \f3SHA256withECDSA\fR algorithm\&.
.PP
These default signature algorithms can be overridden using the \f3-sigalg\fR option\&.
.SS THE\ SIGNED\ JAR\ FILE    
When the \f3jarsigner\fR command is used to sign a JAR file, the output signed JAR file is exactly the same as the input JAR file, except that it has two additional files placed in the META-INF directory:
.TP 0.2i    
\(bu
A signature file with an \f3\&.SF\fR extension
.TP 0.2i    
\(bu
A signature block file with a \f3\&.DSA\fR, \f3\&.RSA\fR, or \f3\&.EC\fR extension
.PP
The base file names for these two files come from the value of the \f3-sigFile\fR option\&. For example, when the option is \f3-sigFile MKSIGN\fR, the files are named \f3MKSIGN\&.SF\fR and \f3MKSIGN\&.DSA\fR
.PP
If no \f3-sigfile\fR option appears on the command line, then the base file name for the \f3\&.SF\fR and \f3\&.DSA\fR files is the first 8 characters of the alias name specified on the command line, all converted to uppercase\&. If the alias name has fewer than 8 characters, then the full alias name is used\&. If the alias name contains any characters that are not allowed in a signature file name, then each such character is converted to an underscore (_) character in forming the file name\&. Valid characters include letters, digits, underscores, and hyphens\&.
.PP
Signature File

A signature file (\f3\&.SF\fR file) looks similar to the manifest file that is always included in a JAR file when the \f3jarsigner\fR command is used to sign the file\&. For each source file included in the JAR file, the \f3\&.SF\fR file has three lines, such as in the manifest file, that list the following:
.TP 0.2i    
\(bu
File name
.TP 0.2i    
\(bu
Name of the digest algorithm (SHA)
.TP 0.2i    
\(bu
SHA digest value
.PP
In the manifest file, the SHA digest value for each source file is the digest (hash) of the binary data in the source file\&. In the \f3\&.SF\fR file, the digest value for a specified source file is the hash of the three lines in the manifest file for the source file\&.
.PP
The signature file, by default, includes a header with a hash of the whole manifest file\&. The header also contains a hash of the manifest header\&. The presence of the header enables verification optimization\&. See JAR File Verification\&.
.PP
Signature Block File

The \f3\&.SF\fR file is signed and the signature is placed in the signature block file\&. This file also contains, encoded inside it, the certificate or certificate chain from the keystore that authenticates the public key corresponding to the private key used for signing\&. The file has the extension \f3\&.DSA\fR, \f3\&.RSA\fR, or \f3\&.EC\fR, depending on the digest algorithm used\&.
.SS SIGNATURE\ TIME\ STAMP    
The \f3jarsigner\fR command can generate and store a signature time stamp when signing a JAR file\&. In addition, \f3jarsigner\fR supports alternative signing mechanisms\&. This behavior is optional and is controlled by the user at the time of signing through these options\&. See Options\&.
.sp     
.nf     
\f3\-tsa \fIurl\fR\fP
.fi     
.nf     
\f3\-tsacert \fIalias\fR\fP
.fi     
.nf     
\f3\-altsigner \fIclass\fR\fP
.fi     
.nf     
\f3\-altsignerpath \fIclasspathlist\fR\fP
.fi     
.nf     
\f3\-tsapolicyid \fIpolicyid\fR\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
.SS JAR\ FILE\ VERIFICATION    
A successful JAR file verification occurs when the signatures are valid, and none of the files that were in the JAR file when the signatures were generated have changed since then\&. JAR file verification involves the following steps:
.TP 0.4i    
1\&.
Verify the signature of the \f3\&.SF\fR file\&.

The verification ensures that the signature stored in each signature block (\f3\&.DSA\fR) file was generated using the private key corresponding to the public key whose certificate (or certificate chain) also appears in the \f3\&.DSA\fR file\&. It also ensures that the signature is a valid signature of the corresponding signature (\f3\&.SF\fR) file, and thus the \f3\&.SF\fR file was not tampered with\&.
.TP 0.4i    
2\&.
Verify the digest listed in each entry in the \f3\&.SF\fR file with each corresponding section in the manifest\&.

The \f3\&.SF\fR file by default includes a header that contains a hash of the entire manifest file\&. When the header is present, the verification can check to see whether or not the hash in the header matches the hash of the manifest file\&. If there is a match, then verification proceeds to the next step\&.

If there is no match, then a less optimized verification is required to ensure that the hash in each source file information section in the \f3\&.SF\fR file equals the hash of its corresponding section in the manifest file\&. See Signature File\&.

One reason the hash of the manifest file that is stored in the \f3\&.SF\fR file header might not equal the hash of the current manifest file is that one or more files were added to the JAR file (with the \f3jar\fR tool) after the signature and \f3\&.SF\fR file were generated\&. When the \f3jar\fR tool is used to add files, the manifest file is changed by adding sections to it for the new files, but the \f3\&.SF\fR file is not changed\&. A verification is still considered successful when none of the files that were in the JAR file when the signature was generated have been changed since then\&. This happens when the hashes in the non-header sections of the \f3\&.SF\fR file equal the hashes of the corresponding sections in the manifest file\&.
.TP 0.4i    
3\&.
Read each file in the JAR file that has an entry in the \f3\&.SF\fR file\&. While reading, compute the file\&'s digest and compare the result with the digest for this file in the manifest section\&. The digests should be the same or verification fails\&.

If any serious verification failures occur during the verification process, then the process is stopped and a security exception is thrown\&. The \f3jarsigner\fR command catches and displays the exception\&.
.PP
\fINote:\fR You should read any addition warnings (or errors if you specified the \f3-strict\fR option), as well as the content of the certificate (by specifying the \f3-verbose\fR and \f3-certs\fR options) to determine if the signature can be trusted\&.
.SS MULTIPLE\ SIGNATURES\ FOR\ A\ JAR\ FILE    
A JAR file can be signed by multiple people by running the \f3jarsigner\fR command on the file multiple times and specifying the alias for a different person each time, as follows:
.sp     
.nf     
\f3jarsigner myBundle\&.jar susan\fP
.fi     
.nf     
\f3jarsigner myBundle\&.jar kevin\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
When a JAR file is signed multiple times, there are multiple \f3\&.SF\fR and \f3\&.DSA\fR files in the resulting JAR file, one pair for each signature\&. In the previous example, the output JAR file includes files with the following names:
.sp     
.nf     
\f3SUSAN\&.SF\fP
.fi     
.nf     
\f3SUSAN\&.DSA\fP
.fi     
.nf     
\f3KEVIN\&.SF\fP
.fi     
.nf     
\f3KEVIN\&.DSA\fP
.fi     
.sp     
.SH OPTIONS    
The following sections describe the various \f3jarsigner\fR options\&. Be aware of the following standards:
.TP 0.2i    
\(bu
All option names are preceded by a minus sign (-)\&.
.TP 0.2i    
\(bu
The options can be provided in any order\&.
.TP 0.2i    
\(bu
Items that are in italics or underlined (option values) represent the actual values that must be supplied\&.
.TP 0.2i    
\(bu
The \f3-storepass\fR, \f3-keypass\fR, \f3-sigfile\fR, \f3-sigalg\fR, \f3-digestalg\fR, \f3-signedjar\fR, and TSA-related options are only relevant when signing a JAR file; they are not relevant when verifying a signed JAR file\&. The \f3-keystore\fR option is relevant for signing and verifying a JAR file\&. In addition, aliases are specified when signing and verifying a JAR file\&.
.TP
-keystore \fIurl\fR
.br
Specifies the URL that tells the keystore location\&. This defaults to the file \f3\&.keystore\fR in the user\&'s home directory, as determined by the \f3user\&.home\fR system property\&.

A keystore is required when signing\&. You must explicitly specify a keystore when the default keystore does not exist or if you want to use one other than the default\&.

A keystore is not required when verifying, but if one is specified or the default exists and the \f3-verbose\fR option was also specified, then additional information is output regarding whether or not any of the certificates used to verify the JAR file are contained in that keystore\&.

The \f3-keystore\fR argument can be a file name and path specification rather than a URL, in which case it is treated the same as a file: URL, for example, the following are equivalent:
.sp     
.nf     
\f3\-keystore \fIfilePathAndName\fR\fP
.fi     
.nf     
\f3\-keystore file:\fIfilePathAndName\fR\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     


If the Sun PKCS #11 provider was configured in the \f3java\&.security\fR security properties file (located in the JRE\&'s \f3$JAVA_HOME/lib/security directory\fR), then the \f3keytool\fR and \f3jarsigner\fR tools can operate on the PKCS #11 token by specifying these options:
.sp     
.nf     
\f3\-keystore NONE\fP
.fi     
.nf     
\f3\-storetype PKCS11\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     


For example, the following command lists the contents of the configured PKCS#11 token:
.sp     
.nf     
\f3keytool \-keystore NONE \-storetype PKCS11 \-list\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     

.TP
-storetype \fIstoretype\fR
.br
Specifies the type of keystore to be instantiated\&. The default keystore type is the one that is specified as the value of the \f3keystore\&.type\fR property in the security properties file, which is returned by the static \f3getDefaultType\fR method in \f3java\&.security\&.KeyStore\fR\&.

The PIN for a PCKS #11 token can also be specified with the \f3-storepass\fR option\&. If none is specified, then the \f3keytool\fR and \f3jarsigner\fR commands prompt for the token PIN\&. If the token has a protected authentication path (such as a dedicated PIN-pad or a biometric reader), then the \f3-protected\fR option must be specified and no password options can be specified\&.
.TP
-storepass[:env | :file] \fIargument\fR
.br
Specifies the password that is required to access the keystore\&. This is only needed when signing (not verifying) a JAR file\&. In that case, if a \f3-storepass\fR option is not provided at the command line, then the user is prompted for the password\&.

If the modifier \f3env\fR or \f3file\fR is not specified, then the password has the value \fIargument\fR\&. Otherwise, the password is retrieved as follows:
.RS     
.TP 0.2i    
\(bu
\f3env\fR: Retrieve the password from the environment variable named \f3argument\fR\&.
.TP 0.2i    
\(bu
\f3file\fR: Retrieve the password from the file named \f3argument\fR\&.
.RE     


\fINote:\fR The password should not be specified on the command line or in a script unless it is for testing purposes, or you are on a secure system\&.
.TP
-keypass [:env | :file] \fIargument\fR
.br
Specifies the password used to protect the private key of the keystore entry addressed by the alias specified on the command line\&. The password is required when using \f3jarsigner\fR to sign a JAR file\&. If no password is provided on the command line, and the required password is different from the store password, then the user is prompted for it\&.

If the modifier \f3env\fR or \f3file\fR is not specified, then the password has the value \f3argument\fR\&. Otherwise, the password is retrieved as follows:
.RS     
.TP 0.2i    
\(bu
\f3env\fR: Retrieve the password from the environment variable named \f3argument\fR\&.
.TP 0.2i    
\(bu
\f3file\fR: Retrieve the password from the file named \f3argument\fR\&.
.RE     


\fINote:\fR The password should not be specified on the command line or in a script unless it is for testing purposes, or you are on a secure system\&.
.TP
-sigfile \fIfile\fR
.br
Specifies the base file name to be used for the generated \f3\&.SF\fR and \f3\&.DSA\fR files\&. For example, if file is \f3DUKESIGN\fR, then the generated \f3\&.SF\fR and \f3\&.DSA\fR files are named \f3DUKESIGN\&.SF\fR and \f3DUKESIGN\&.DSA\fR, and placed in the \f3META-INF\fR directory of the signed JAR file\&.

The characters in the file must come from the set \f3a-zA-Z0-9_-\fR\&. Only letters, numbers, underscore, and hyphen characters are allowed\&. All lowercase characters are converted to uppercase for the \f3\&.SF\fR and \f3\&.DSA\fR file names\&.

If no \f3-sigfile\fR option appears on the command line, then the base file name for the \f3\&.SF\fR and \f3\&.DSA\fR files is the first 8 characters of the alias name specified on the command line, all converted to upper case\&. If the alias name has fewer than 8 characters, then the full alias name is used\&. If the alias name contains any characters that are not valid in a signature file name, then each such character is converted to an underscore (_) character to form the file name\&.
.TP
-sigalg \fIalgorithm\fR
.br
Specifies the name of the signature algorithm to use to sign the JAR file\&.

For a list of standard signature algorithm names, see "Appendix A: Standard Names" in the Java Cryptography Architecture (JCA) Reference Guide at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/crypto/CryptoSpec\&.html#AppA

This algorithm must be compatible with the private key used to sign the JAR file\&. If this option is not specified, then \f3SHA1withDSA\fR, \f3SHA256withRSA\fR, or \f3SHA256withECDSA\fR are used depending on the type of private key\&. There must either be a statically installed provider supplying an implementation of the specified algorithm or the user must specify one with the \f3-providerClass\fR option; otherwise, the command will not succeed\&.
.TP
-digestalg \fIalgorithm\fR
.br
Specifies the name of the message digest algorithm to use when digesting the entries of a JAR file\&.

For a list of standard message digest algorithm names, see "Appendix A: Standard Names" in the Java Cryptography Architecture (JCA) Reference Guide at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/crypto/CryptoSpec\&.html#AppA

If this option is not specified, then \f3SHA256\fR is used\&. There must either be a statically installed provider supplying an implementation of the specified algorithm or the user must specify one with the \f3-providerClass\fR option; otherwise, the command will not succeed\&.
.TP
-certs
.br
If the \f3-certs\fR option appears on the command line with the \f3-verify\fR and \f3-verbose\fR options, then the output includes certificate information for each signer of the JAR file\&. This information includes the name of the type of certificate (stored in the \f3\&.DSA\fR file) that certifies the signer\&'s public key, and if the certificate is an X\&.509 certificate (an instance of the \f3java\&.security\&.cert\&.X509Certificate\fR), then the distinguished name of the signer\&.

The keystore is also examined\&. If no keystore value is specified on the command line, then the default keystore file (if any) is checked\&. If the public key certificate for a signer matches an entry in the keystore, then the alias name for the keystore entry for that signer is displayed in parentheses\&.
.TP
-certchain \fIfile\fR
.br
Specifies the certificate chain to be used when the certificate chain associated with the private key of the keystore entry that is addressed by the alias specified on the command line is not complete\&. This can happen when the keystore is located on a hardware token where there is not enough capacity to hold a complete certificate chain\&. The file can be a sequence of concatenated X\&.509 certificates, or a single PKCS#7 formatted data block, either in binary encoding format or in printable encoding format (also known as Base64 encoding) as defined by the Internet RFC 1421 standard\&. See Internet RFC 1421 Certificate Encoding Standard and http://tools\&.ietf\&.org/html/rfc1421\&.
.TP
-verbose
.br
When the \f3-verbose\fR option appears on the command line, it indicates verbose mode, which causes \f3jarsigner\fR to output extra information about the progress of the JAR signing or verification\&.
.TP
-internalsf
.br
In the past, the \f3\&.DSA\fR (signature block) file generated when a JAR file was signed included a complete encoded copy of the \f3\&.SF\fR file (signature file) also generated\&. This behavior has been changed\&. To reduce the overall size of the output JAR file, the \f3\&.DSA\fR file by default does not contain a copy of the \f3\&.SF\fR file anymore\&. If \f3-internalsf\fR appears on the command line, then the old behavior is utilized\&. This option is useful for testing\&. In practice, do not use the \f3-internalsf\fR option because it incurs higher overhead\&.
.TP
-sectionsonly
.br
If the \f3-sectionsonly\fR option appears on the command line, then the \f3\&.SF\fR file (signature file) generated when a JAR file is signed does not include a header that contains a hash of the whole manifest file\&. It contains only the information and hashes related to each individual source file included in the JAR file\&. See Signature File\&.

By default, this header is added, as an optimization\&. When the header is present, whenever the JAR file is verified, the verification can first check to see whether the hash in the header matches the hash of the whole manifest file\&. When there is a match, verification proceeds to the next step\&. When there is no match, it is necessary to do a less optimized verification that the hash in each source file information section in the \f3\&.SF\fR file equals the hash of its corresponding section in the manifest file\&. See JAR File Verification\&.

The \f3-sectionsonly\fR option is primarily used for testing\&. It should not be used other than for testing because using it incurs higher overhead\&.
.TP
-protected
.br
Values can be either \f3true\fR or \f3false\fR\&. Specify \f3true\fR when a password must be specified through a protected authentication path such as a dedicated PIN reader\&.
.TP
-providerClass \fIprovider-class-name\fR
.br
Used to specify the name of cryptographic service provider\&'s master class file when the service provider is not listed in the \f3java\&.security\fR security properties file\&.

Used with the \f3-providerArg ConfigFilePath\fR option, the \f3keytool\fR and \f3jarsigner\fR tools install the provider dynamically and use \fIConfigFilePath\fR for the path to the token configuration file\&. The following example shows a command to list a \f3PKCS #11\fR keystore when the Oracle PKCS #11 provider was not configured in the security properties file\&.
.sp     
.nf     
\f3jarsigner \-keystore NONE \-storetype PKCS11 \e\fP
.fi     
.nf     
\f3          \-providerClass sun\&.security\&.pkcs11\&.SunPKCS11 \e\fP
.fi     
.nf     
\f3          \-providerArg /mydir1/mydir2/token\&.config \e\fP
.fi     
.nf     
\f3          \-list\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     

.TP
-providerName \fIproviderName\fR
.br
If more than one provider was configured in the \f3java\&.security\fR security properties file, then you can use the \f3-providerName\fR option to target a specific provider instance\&. The argument to this option is the name of the provider\&.

For the Oracle PKCS #11 provider, \fIproviderName\fR is of the form \f3SunPKCS11-\fR\fITokenName\fR, where \fITokenName\fR is the name suffix that the provider instance has been configured with, as detailed in the configuration attributes table\&. For example, the following command lists the contents of the \f3PKCS #11\fR keystore provider instance with name suffix \f3SmartCard\fR:
.sp     
.nf     
\f3jarsigner \-keystore NONE \-storetype PKCS11 \e\fP
.fi     
.nf     
\f3        \-providerName SunPKCS11\-SmartCard \e\fP
.fi     
.nf     
\f3        \-list\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     

.TP
-J\fIjavaoption\fR
.br
Passes through the specified \fIjavaoption\fR string directly to the Java interpreter\&. The \f3jarsigner\fR command is a wrapper around the interpreter\&. This option should not contain any spaces\&. It is useful for adjusting the execution environment or memory usage\&. For a list of possible interpreter options, type \f3java -h\fR or \f3java -X\fR at the command line\&.
.TP
-tsa \fIurl\fR
.br
If \f3-tsa http://example\&.tsa\&.url\fR appears on the command line when signing a JAR file then a time stamp is generated for the signature\&. The URL, \f3http://example\&.tsa\&.url\fR, identifies the location of the Time Stamping Authority (TSA) and overrides any URL found with the \f3-tsacert\fR option\&. The \f3-tsa\fR option does not require the TSA public key certificate to be present in the keystore\&.

To generate the time stamp, \f3jarsigner\fR communicates with the TSA with the Time-Stamp Protocol (TSP) defined in RFC 3161\&. When successful, the time stamp token returned by the TSA is stored with the signature in the signature block file\&.
.TP
-tsacert \fIalias\fR
.br
When \f3-tsacert alias\fR appears on the command line when signing a JAR file, a time stamp is generated for the signature\&. The alias identifies the TSA public key certificate in the keystore that is in effect\&. The entry\&'s certificate is examined for a Subject Information Access extension that contains a URL identifying the location of the TSA\&.

The TSA public key certificate must be present in the keystore when using the \f3-tsacert\fR option\&.
.TP
-tsapolicyid \fIpolicyid\fR
.br
Specifies the object identifier (OID) that identifies the policy ID to be sent to the TSA server\&. If this option is not specified, no policy ID is sent and the TSA server will choose a default policy ID\&.

Object identifiers are defined by X\&.696, which is an ITU Telecommunication Standardization Sector (ITU-T) standard\&. These identifiers are typically period-separated sets of non-negative digits like \f31\&.2\&.3\&.4\fR, for example\&.
.TP
-altsigner \fIclass\fR
.br
This option specifies an alternative signing mechanism\&. The fully qualified class name identifies a class file that extends the \f3com\&.sun\&.jarsigner\&.ContentSigner\fR abstract class\&. The path to this class file is defined by the \f3-altsignerpath\fR option\&. If the \f3-altsigner\fR option is used, then the \f3jarsigner\fR command uses the signing mechanism provided by the specified class\&. Otherwise, the \f3jarsigner\fR command uses its default signing mechanism\&.

For example, to use the signing mechanism provided by a class named \f3com\&.sun\&.sun\&.jarsigner\&.AuthSigner\fR, use the jarsigner option \f3-altsigner com\&.sun\&.jarsigner\&.AuthSigner\fR\&.
.TP
-altsignerpath \fIclasspathlist\fR
.br
Specifies the path to the class file and any JAR file it depends on\&. The class file name is specified with the \f3-altsigner\fR option\&. If the class file is in a JAR file, then this option specifies the path to that JAR file\&.

An absolute path or a path relative to the current directory can be specified\&. If \fIclasspathlist\fR contains multiple paths or JAR files, then they should be separated with a colon (:) on Oracle Solaris and a semicolon (;) on Windows\&. This option is not necessary when the class is already in the search path\&.

The following example shows how to specify the path to a JAR file that contains the class file\&. The JAR file name is included\&.
.sp     
.nf     
\f3\-altsignerpath /home/<USER>/lib/authsigner\&.jar\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     


The following example shows how to specify the path to the JAR file that contains the class file\&. The JAR file name is omitted\&.
.sp     
.nf     
\f3\-altsignerpath /home/<USER>/classes/com/sun/tools/jarsigner/\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     

.TP
-strict
.br
During the signing or verifying process, the command may issue warning messages\&. If you specify this option, the exit code of the tool reflects the severe warning messages that this command found\&. See Errors and Warnings\&.
.TP
-verbose \fIsuboptions\fR
.br
For the verifying process, the \f3-verbose\fR option takes suboptions to determine how much information is shown\&. If the \f3-certs\fR option is also specified, then the default mode (or suboption \f3all\fR) displays each entry as it is being processed, and after that, the certificate information for each signer of the JAR file\&. If the \f3-certs\fR and the \f3-verbose:grouped\fR suboptions are specified, then entries with the same signer info are grouped and displayed together with their certificate information\&. If \f3-certs\fR and the \f3-verbose:summary\fR suboptions are specified, then entries with the same signer information are grouped and displayed together with their certificate information\&. Details about each entry are summarized and displayed as \fIone entry (and more)\fR\&. See Examples\&.
.SH ERRORS\ AND\ WARNINGS    
During the signing or verifying process, the \f3jarsigner\fR command may issue various errors or warnings\&.
.PP
If there is a failure, the \f3jarsigner\fR command exits with code 1\&. If there is no failure, but there are one or more severe warnings, the \f3jarsigner\fR command exits with code 0 when the \f3-strict\fR option is \fInot\fR specified, or exits with the OR-value of the warning codes when the \f3-strict\fR is specified\&. If there is only informational warnings or no warning at all, the command always exits with code 0\&.
.PP
For example, if a certificate used to sign an entry is expired and has a KeyUsage extension that does not allow it to sign a file, the \f3jarsigner\fR command exits with code 12 (=4+8) when the \f3-strict\fR option is specified\&.
.PP
\fINote:\fR Exit codes are reused because only the values from 0 to 255 are legal on Unix-based operating systems\&.
.PP
The following sections describes the names, codes, and descriptions of the errors and warnings that the \f3jarsigner\fR command can issue\&.
.SS FAILURE    
Reasons why the \f3jarsigner\fR command fails include (but are not limited to) a command line parsing error, the inability to find a keypair to sign the JAR file, or the verification of a signed JAR fails\&.
.TP     
failure
Code 1\&. The signing or verifying fails\&.
.SS SEVERE\ WARNINGS    
\fINote:\fR Severe warnings are reported as errors if you specify the \f3-strict\fR option\&.
.PP
Reasons why the \f3jarsigner\fR command issues a severe warning include the certificate used to sign the JAR file has an error or the signed JAR file has other problems\&.
.TP     
hasExpiredCert
Code 4\&. This jar contains entries whose signer certificate has expired\&.
.TP     
notYetValidCert
Code 4\&. This jar contains entries whose signer certificate is not yet valid\&.
.TP     
chainNotValidated
Code 4\&. This jar contains entries whose certificate chain cannot be correctly validated\&.
.TP     
badKeyUsage
Code 8\&. This jar contains entries whose signer certificate\&'s KeyUsage extension doesn\&'t allow code signing\&.
.TP     
badExtendedKeyUsage
Code 8\&. This jar contains entries whose signer certificate\&'s ExtendedKeyUsage extension doesn\&'t allow code signing\&.
.TP     
badNetscapeCertType
Code 8\&. This jar contains entries whose signer certificate\&'s NetscapeCertType extension doesn\&'t allow code signing\&.
.TP     
hasUnsignedEntry
Code 16\&. This jar contains unsigned entries which have not been integrity-checked\&.
.TP     
notSignedByAlias
Code 32\&. This jar contains signed entries which are not signed by the specified alias(es)\&.
.TP     
aliasNotInStore
Code 32\&. This jar contains signed entries that are not signed by alias in this keystore\&.
.SS INFORMATIONAL\ WARNINGS    
Informational warnings include those that are not errors but regarded as bad practice\&. They do not have a code\&.
.TP     
hasExpiringCert
This jar contains entries whose signer certificate will expire within six months\&.
.TP     
noTimestamp
This jar contains signatures that does not include a timestamp\&. Without a timestamp, users may not be able to validate this JAR file after the signer certificate\&'s expiration date (\f3YYYY-MM-DD\fR) or after any future revocation date\&.
.SH EXAMPLES    
.SS SIGN\ A\ JAR\ FILE    
Use the following command to sign bundle\&.jar with the private key of a user whose keystore alias is \f3jane\fR in a keystore named \f3mystore\fR in the \f3working\fR directory and name the signed JAR file \f3sbundle\&.jar\fR:
.sp     
.nf     
\f3jarsigner \-keystore /working/mystore\fP
.fi     
.nf     
\f3    \-storepass <keystore password>\fP
.fi     
.nf     
\f3    \-keypass <private key password>\fP
.fi     
.nf     
\f3    \-signedjar sbundle\&.jar bundle\&.jar jane\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
There is no \f3-sigfile\fR specified in the previous command so the generated \f3\&.SF\fR and \f3\&.DSA\fR files to be placed in the signed JAR file have default names based on the alias name\&. They are named \f3JANE\&.SF\fR and \f3JANE\&.DSA\fR\&.
.PP
If you want to be prompted for the store password and the private key password, then you could shorten the previous command to the following:
.sp     
.nf     
\f3jarsigner \-keystore /working/mystore\fP
.fi     
.nf     
\f3    \-signedjar sbundle\&.jar bundle\&.jar jane\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
If the keystore is the default keystore (\&.keystore in your home directory), then you do not need to specify a keystore, as follows:
.sp     
.nf     
\f3jarsigner \-signedjar sbundle\&.jar bundle\&.jar jane\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
If you want the signed JAR file to overwrite the input JAR file (bundle\&.jar), then you do not need to specify a \f3-signedjar\fR option, as follows:
.sp     
.nf     
\f3jarsigner bundle\&.jar jane\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
.SS VERIFY\ A\ SIGNED\ JAR\ FILE    
To verify a signed JAR file to ensure that the signature is valid and the JAR file was not been tampered with, use a command such as the following:
.sp     
.nf     
\f3jarsigner \-verify sbundle\&.jar\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
When the verification is successful, \f3jar verified\fR is displayed\&. Otherwise, an error message is displayed\&. You can get more information when you use the \f3-verbose\fR option\&. A sample use of \f3jarsigner\fR with the\f3-verbose\fR option follows:
.sp     
.nf     
\f3jarsigner \-verify \-verbose sbundle\&.jar\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3           198 Fri Sep 26 16:14:06 PDT 1997 META\-INF/MANIFEST\&.MF\fP
.fi     
.nf     
\f3           199 Fri Sep 26 16:22:10 PDT 1997 META\-INF/JANE\&.SF\fP
.fi     
.nf     
\f3          1013 Fri Sep 26 16:22:10 PDT 1997 META\-INF/JANE\&.DSA\fP
.fi     
.nf     
\f3    smk   2752 Fri Sep 26 16:12:30 PDT 1997 AclEx\&.class\fP
.fi     
.nf     
\f3    smk    849 Fri Sep 26 16:12:46 PDT 1997 test\&.class\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3      s = signature was verified\fP
.fi     
.nf     
\f3      m = entry is listed in manifest\fP
.fi     
.nf     
\f3      k = at least one certificate was found in keystore\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3    jar verified\&.\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
.SS VERIFICATION\ WITH\ CERTIFICATE\ INFORMATION    
If you specify the \f3-certs\fR option with the \f3-verify\fR and \f3-verbose\fR options, then the output includes certificate information for each signer of the JAR file\&. The information includes the certificate type, the signer distinguished name information (when it is an X\&.509 certificate), and in parentheses, the keystore alias for the signer when the public key certificate in the JAR file matches the one in a keystore entry, for example:
.sp     
.nf     
\f3jarsigner \-keystore /working/mystore \-verify \-verbose \-certs myTest\&.jar\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3           198 Fri Sep 26 16:14:06 PDT 1997 META\-INF/MANIFEST\&.MF\fP
.fi     
.nf     
\f3           199 Fri Sep 26 16:22:10 PDT 1997 META\-INF/JANE\&.SF\fP
.fi     
.nf     
\f3          1013 Fri Sep 26 16:22:10 PDT 1997 META\-INF/JANE\&.DSA\fP
.fi     
.nf     
\f3           208 Fri Sep 26 16:23:30 PDT 1997 META\-INF/JAVATEST\&.SF\fP
.fi     
.nf     
\f3          1087 Fri Sep 26 16:23:30 PDT 1997 META\-INF/JAVATEST\&.DSA\fP
.fi     
.nf     
\f3    smk   2752 Fri Sep 26 16:12:30 PDT 1997 Tst\&.class\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3      X\&.509, CN=Test Group, OU=Java Software, O=Oracle, L=CUP, S=CA, C=US (javatest)\fP
.fi     
.nf     
\f3      X\&.509, CN=Jane Smith, OU=Java Software, O=Oracle, L=cup, S=ca, C=us (jane)\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3      s = signature was verified\fP
.fi     
.nf     
\f3      m = entry is listed in manifest\fP
.fi     
.nf     
\f3      k = at least one certificate was found in keystore\fP
.fi     
.nf     
\f3\fR
.fi     
.nf     
\f3    jar verified\&.\fP
.fi     
.nf     
\f3\fR
.fi     
.sp     
If the certificate for a signer is not an X\&.509 certificate, then there is no distinguished name information\&. In that case, just the certificate type and the alias are shown\&. For example, if the certificate is a PGP certificate, and the alias is \f3bob\fR, then you would get: \f3PGP, (bob)\fR\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
jar(1)
.TP 0.2i    
\(bu
keytool(1)
.TP 0.2i    
\(bu
Trail: Security Features in Java SE at http://docs\&.oracle\&.com/javase/tutorial/security/index\&.html
.RE
.br
'pl 8.5i
'bp
