'\" t
.\" Copyright (c) 1994, 2014, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: javap
.\" Language: English
.\" Date: 8 August 2014
.\" SectDesc: Basic Tools
.\" Software: JDK 8
.\" Arch: generic
.\" Part Number: E38209-03
.\"
.if n .pl 99999
.TH "javap" "1" "8 August 2014" "JDK 8" "Basic Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
javap \- Disassembles one or more class files\&.
.SH "SYNOPSIS"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavap\fR [\fIoptions\fR] \fIclassfile\fR\&.\&.\&.
.fi
.if n \{\
.RE
.\}
.PP
\fIoptions\fR
.RS 4
The command\-line options\&. See Options\&.
.RE
.PP
\fIclassfile\fR
.RS 4
One or more classes separated by spaces to be processed for annotations such as DocFooter\&.class\&. You can specify a class that can be found in the class path, by its file name or with a URL such as
\fBfile:///home/<USER>/myproject/src/DocFooter\&.class\fR\&.
.RE
.SH "DESCRIPTION"
.PP
The
\fBjavap\fR
command disassembles one or more class files\&. The output depends on the options used\&. When no options are used, then the
\fBjavap\fR
command prints the package, protected and public fields, and methods of the classes passed to it\&. The
\fBjavap\fR
command prints its output to
\fBstdout\fR\&.
.SH "OPTIONS"
.PP
\-help
.br
\-\-help
.br
\-?
.RS 4
Prints a help message for the
\fBjavap\fR
command\&.
.RE
.PP
\-version
.RS 4
Prints release information\&.
.RE
.PP
\-l
.RS 4
Prints line and local variable tables\&.
.RE
.PP
\-public
.RS 4
Shows only public classes and members\&.
.RE
.PP
\-protected
.RS 4
Shows only protected and public classes and members\&.
.RE
.PP
\-private
.br
\-p
.RS 4
Shows all classes and members\&.
.RE
.PP
\-J\fIoption\fR
.RS 4
Passes the specified option to the JVM\&. For example:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjavap \-J\-version\fR
\fBjavap \-J\-Djava\&.security\&.manager \-J\-Djava\&.security\&.policy=MyPolicy MyClassName\fR
 
.fi
.if n \{\
.RE
.\}
For more information about JVM options, see the command documentation\&.
.RE
.PP
\-s
.RS 4
Prints internal type signatures\&.
.RE
.PP
\-sysinfo
.RS 4
Shows system information (path, size, date, MD5 hash) of the class being processed\&.
.RE
.PP
\-constants
.RS 4
Shows
\fBstatic final\fR
constants\&.
.RE
.PP
\-c
.RS 4
Prints disassembled code, for example, the instructions that comprise the Java bytecodes, for each of the methods in the class\&.
.RE
.PP
\-verbose
.RS 4
Prints stack size, number of locals and arguments for methods\&.
.RE
.PP
\-classpath \fIpath\fR
.RS 4
Specifies the path the
\fBjavap\fR
command uses to look up classes\&. Overrides the default or the
\fBCLASSPATH\fR
environment variable when it is set\&.
.RE
.PP
\-bootclasspath \fIpath\fR
.RS 4
Specifies the path from which to load bootstrap classes\&. By default, the bootstrap classes are the classes that implement the core Java platform located in
\fBjre/lib/rt\&.jar\fR
and several other JAR files\&.
.RE
.PP
\-extdir \fIdirs\fR
.RS 4
Overrides the location at which installed extensions are searched for\&. The default location for extensions is the value of
\fBjava\&.ext\&.dirs\fR\&.
.RE
.SH "EXAMPLE"
.PP
Compile the following
\fBDocFooter\fR
class:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBimport java\&.awt\&.*;\fR
\fBimport java\&.applet\&.*;\fR
\fB \fR
\fBpublic class DocFooter extends Applet {\fR
\fB        String date;\fR
\fB        String email;\fR
\fB \fR
\fB        public void init() {\fR
\fB                resize(500,100);\fR
\fB                date = getParameter("LAST_UPDATED");\fR
\fB                email = getParameter("EMAIL");\fR
\fB        }\fR
\fB \fR
\fB        public void paint(Graphics g) {\fR
\fB                g\&.drawString(date + " by ",100, 15);\fR
\fB                g\&.drawString(email,290,15);\fR
\fB        }\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
.PP
The output from the
\fBjavap DocFooter\&.class\fR
command yields the following:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBCompiled from "DocFooter\&.java"\fR
\fBpublic class DocFooter extends java\&.applet\&.Applet {\fR
\fB  java\&.lang\&.String date;\fR
\fB  java\&.lang\&.String email;\fR
\fB  public DocFooter();\fR
\fB  public void init();\fR
\fB  public void paint(java\&.awt\&.Graphics);\fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
.PP
The output from
\fBjavap \-c DocFooter\&.class\fR
command yields the following:
.sp
.if n \{\
.RS 4
.\}
.nf
\fBCompiled from "DocFooter\&.java"\fR
\fBpublic class DocFooter extends java\&.applet\&.Applet {\fR
\fB  java\&.lang\&.String date;\fR
\fB  java\&.lang\&.String email;\fR
 
\fB  public DocFooter();\fR
\fB    Code:\fR
\fB       0: aload_0       \fR
\fB       1: invokespecial #1                  // Method\fR
\fBjava/applet/Applet\&."<init>":()V\fR
\fB       4: return        \fR
 
\fB  public void init();\fR
\fB    Code:\fR
\fB       0: aload_0       \fR
\fB       1: sipush        500\fR
\fB       4: bipush        100\fR
\fB       6: invokevirtual #2                  // Method resize:(II)V\fR
\fB       9: aload_0       \fR
\fB      10: aload_0       \fR
\fB      11: ldc           #3                  // String LAST_UPDATED\fR
\fB      13: invokevirtual #4                  // Method\fR
\fB getParameter:(Ljava/lang/String;)Ljava/lang/String;\fR
\fB      16: putfield      #5                  // Field date:Ljava/lang/String;\fR
\fB      19: aload_0       \fR
\fB      20: aload_0       \fR
\fB      21: ldc           #6                  // String EMAIL\fR
\fB      23: invokevirtual #4                  // Method\fR
\fB getParameter:(Ljava/lang/String;)Ljava/lang/String;\fR
\fB      26: putfield      #7                  // Field email:Ljava/lang/String;\fR
\fB      29: return        \fR
 
\fB  public void paint(java\&.awt\&.Graphics);\fR
\fB    Code:\fR
\fB       0: aload_1       \fR
\fB       1: new           #8                  // class java/lang/StringBuilder\fR
\fB       4: dup           \fR
\fB       5: invokespecial #9                  // Method\fR
\fB java/lang/StringBuilder\&."<init>":()V\fR
\fB       8: aload_0       \fR
\fB       9: getfield      #5                  // Field date:Ljava/lang/String;\fR
\fB      12: invokevirtual #10                 // Method\fR
\fB java/lang/StringBuilder\&.append:(Ljava/lang/String;)Ljava/lang/StringBuilder;\fR
\fB      15: ldc           #11                 // String  by \fR
\fB      17: invokevirtual #10                 // Method\fR
\fB java/lang/StringBuilder\&.append:(Ljava/lang/String;)Ljava/lang/StringBuilder;\fR
\fB      20: invokevirtual #12                 // Method\fR
\fB java/lang/StringBuilder\&.toString:()Ljava/lang/String;\fR
\fB      23: bipush        100\fR
\fB      25: bipush        15\fR
\fB      27: invokevirtual #13                 // Method\fR
\fB java/awt/Graphics\&.drawString:(Ljava/lang/String;II)V\fR
\fB      30: aload_1       \fR
\fB      31: aload_0       \fR
\fB      32: getfield      #7                  // Field email:Ljava/lang/String;\fR
\fB      35: sipush        290\fR
\fB      38: bipush        15\fR
\fB      40: invokevirtual #13                 // Method\fR
\fBjava/awt/Graphics\&.drawString:(Ljava/lang/String;II)V\fR
\fB      43: return        \fR
\fB}\fR
 
.fi
.if n \{\
.RE
.\}
.SH "SEE ALSO"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
java(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javac(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javadoc(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
javah(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jdb(1)
.RE
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jdeps(1)
.RE
.br
'pl 8.5i
'bp
