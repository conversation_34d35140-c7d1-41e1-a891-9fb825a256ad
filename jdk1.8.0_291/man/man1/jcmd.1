'\" t
.\" Copyright (c) 2012, 2015, Oracle and/or its affiliates. All rights reserved.
.\"
.\" Title: jcmd
.\" Language: English
.\" Date: 03 March 2015
.\" SectDesc: Troubleshooting Tools
.\" Software: JDK 8
.\" Arch: generic
.\" Part Number: E38209-04
.\" Doc ID: JSSOR
.\"
.if n .pl 99999
.TH "jcmd" "1" "03 March 2015" "JDK 8" "Troubleshooting Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
jcmd \- Sends diagnostic command requests to a running Java Virtual Machine (JVM)\&.
.SH "SYNOPSIS"
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjcmd\fR [\fB\-l\fR|\fB\-h\fR|\fB\-help\fR]
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjcmd\fR \fIpid\fR|\fImain\-class\fR \fBPerfCounter\&.print\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjcmd\fR \fIpid\fR|\fImain\-class\fR \fB\-f\fR \fIfilename\fR
.fi
.if n \{\
.RE
.\}
.sp
.if n \{\
.RS 4
.\}
.nf
\fBjcmd\fR \fIpid\fR|\fImain\-class\fR \fIcommand\fR[ \fIarguments\fR]
.fi
.if n \{\
.RE
.\}
.SH "DESCRIPTION"
.PP
The
\fBjcmd\fR
utility is used to send diagnostic command requests to the JVM\&. It must be used on the same machine on which the JVM is running, and have the same effective user and group identifiers that were used to launch the JVM\&.
.if n \{\
.sp
.\}
.RS 4
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
.br
.ps +1
\fBNote\fR
.ps -1
.br
.TS
allbox tab(:);
l.
T{
.PP
To invoke diagnostic commands from a remote machine or with different identifiers, you can use the
\fBcom\&.sun\&.management\&.DiagnosticCommandMBean\fR
interface\&. For more information about the
\fBDiagnosticCommandMBean\fR
interface, see the API documentation at http://docs\&.oracle\&.com/javase/8/docs/jre/api/management/extension/com/sun/management/DiagnosticCommandMBean\&.html
T}
.TE
.sp 1
.sp .5v
.RE
.PP
If you run
\fBjcmd\fR
without arguments or with the
\fB\-l\fR
option, it prints the list of running Java process identifiers with the main class and command\-line arguments that were used to launch the process\&. Running
\fBjcmd\fR
with the
\fB\-h\fR
or
\fB\-help\fR
option prints the tool\(cqs help message\&.
.if n \{\
.sp
.\}
.RS 4
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
.br
.ps +1
\fBNote\fR
.ps -1
.br
.TS
allbox tab(:);
l.
T{
.PP
The
\fBjcmd\fR
utility can be used to dynamically interact with Java Flight Recorder (JFR) in a JVM that is already running\&. You can use it to unlock commercial features, enable/start/stop flight recordings, and obtain various status messages from the system\&. For a list of examples, see the Java Flight Recorder Runtime Guide at http://docs\&.oracle\&.com/javacomponents/jmc\&.htm
T}
.TE
.sp 1
.sp .5v
.RE
.PP
If you specify the processes identifier (\fIpid\fR) or the main class (\fImain\-class\fR) as the first argument,
\fBjcmd\fR
sends the diagnostic command request to the Java process with the specified identifier or to all Java processes with the specified name of the main class\&. You can also send the diagnostic command request to all available Java processes by specifying
\fB0\fR
as the process identifier\&. Use one of the following as the diagnostic command request:
.PP
Perfcounter\&.print
.RS 4
Prints the performance counters available for the specified Java process\&. The list of performance counters might vary with the Java process\&.
.RE
.PP
\-f \fIfilename\fR
.RS 4
The name of the file from which to read diagnostic commands and send them to the specified Java process\&. Used only with the
\fB\-f\fR
option\&. Each command in the file must be written on a single line\&. Lines starting with a number sign (\fB#\fR) are ignored\&. Processing of the file ends when all lines have been read or when a line containing the
\fBstop\fR
keyword is read\&.
.RE
.PP
\fIcommand\fR [\fIarguments\fR]
.RS 4
The command to be sent to the specified Java process\&. The list of available diagnostic commands for a given process can be obtained by sending the
\fBhelp\fR
command to this process\&. Each diagnostic command has its own set of arguments\&. To see the description, syntax, and a list of available arguments for a command, use the name of the command as the argument for the
\fBhelp\fR
command\&.
.sp
\fBNote:\fR
If any arguments contain spaces, you must surround them with single or double quotation marks (\fB\*(Aq\fR
or
\fB"\fR)\&. In addition, you must escape single or double quotation marks with a backslash (\fB\e\fR) to prevent the operating system shell from processing quotation marks\&. Alternatively, you can surround these arguments with single quotation marks and then with double quotation marks (or with double quotation marks and then with single quotation marks)\&.
.RE
.SH "OPTIONS"
.PP
Options are mutually exclusive\&.
.PP
\-f \fIfilename\fR
.RS 4
Reads commands from the specified file\&. This option can be used only if you specify the process identifier or the main class as the first argument\&. Each command in the file must be written on a single line\&. Lines starting with a number sign (\fB#\fR) are ignored\&. Processing of the file ends when all lines have been read or when a line containing the
\fBstop\fR
keyword is read\&.
.RE
.PP
\-h
.br
\-help
.RS 4
Prints a help message\&.
.RE
.PP
\-l
.RS 4
Prints the list of running Java processes identifiers with the main class and command\-line arguments\&.
.RE
.SH "SEE ALSO"
.sp
.RS 4
.ie n \{\
\h'-04'\(bu\h'+03'\c
.\}
.el \{\
.sp -1
.IP \(bu 2.3
.\}
jps(1)
.RE
.br
'pl 8.5i
'bp
