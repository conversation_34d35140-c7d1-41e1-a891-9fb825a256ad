'\" t
.\"  Copyright (c) 2004, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Troubleshooting Tools
.\"     Title: jinfo.1
.\"
.if n .pl 99999
.TH jinfo 1 "21 November 2013" "JDK 8" "Troubleshooting Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
jinfo \- Generates configuration information\&. This command is experimental and unsupported\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjinfo\fR [ \fIoption\fR ] \fIpid\fR
.fi     
.nf     

\fBjinfo\fR [ \fIoption \fR] \fIexecutable core\fR
.fi     
.nf     

\fBjinfo\fR [ \fIoption \fR] \fI[ servier\-id ] remote\-hostname\-or\-IP\fR
.fi     
.sp     
.TP     
\fIoption\fR
The command-line options\&. See Options\&.
.TP     
\fIpid\fR
The process ID for which the configuration information is to be printed\&. The process must be a Java process\&. To get a list of Java processes running on a machine, use the jps(1) command\&.
.TP     
\fIexecutable\fR
The Java executable from which the core dump was produced\&.
.TP     
\fIcore\fR
The core file for which the configuration information is to be printed\&.
.TP     
\fIremote-hostname-or-IP\fR
The remote debug server \f3hostname\fR or \f3IP\fR address\&. See jsadebugd(1)\&.
.TP     
\fIserver-id\fR
An optional unique ID to use when multiple debug servers are running on the same remote host\&.
.SH DESCRIPTION    
The \f3jinfo\fR command prints Java configuration information for a specified Java process or core file or a remote debug server\&. The configuration information includes Java system properties and Java Virtual Machine (JVM) command-line flags\&. If the specified process is running on a 64-bit JVM, then you might need to specify the \f3-J-d64\fR option, for example: \f3jinfo\fR\f3-J-d64 -sysprops pid\fR\&.
.PP
This utility is unsupported and might not be available in future releases of the JDK\&. In Windows Systems where \f3dbgeng\&.dll\fR is not present, Debugging Tools For Windows must be installed to have these tools working\&. The \f3PATH\fR environment variable should contain the location of the jvm\&.dll that is used by the target process or the location from which the crash dump file was produced\&. For example, \f3set PATH=%JDK_HOME%\ejre\ebin\eclient;%PATH%\fR \&.
.SH OPTIONS    
.TP     
no-option
Prints both command-line flags and system property name-value pairs\&.
.TP
-flag \fIname\fR
.br
Prints the name and value of the specified command-line flag\&.
.TP
-flag \fI[+|-]name\fR
.br
enables or disables the specified Boolean command-line flag\&.
.TP
-flag \fIname=value\fR
.br
Sets the specified command-line flag to the specified value\&.
.TP
-flags
.br
Prints command-line flags passed to the JVM\&.
.TP
-sysprops
.br
Prints Java system properties as name-value pairs\&.
.TP
-h
.br
Prints a help message\&.
.TP
-help
.br
Prints a help message\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
jps(1)
.TP 0.2i    
\(bu
jsadebugd(1)
.RE
.br
'pl 8.5i
'bp
