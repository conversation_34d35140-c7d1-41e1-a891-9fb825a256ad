'\" t
.\"  Copyright (c) 2004, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Troubleshooting Tools
.\"     Title: jmap.1
.\"
.if n .pl 99999
.TH jmap 1 "21 November 2013" "JDK 8" "Troubleshooting Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
jmap \- Prints shared object memory maps or heap memory details for a process, core file, or remote debug server\&. This command is experimental and unsupported\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjmap\fR [ \fIoptions\fR ] \fIpid\fR
.fi     
.nf     

\fBjmap\fR [ \fIoptions\fR ] \fIexecutable\fR \fIcore\fR
.fi     
.nf     

\fBjmap\fR [ \fIoptions\fR ] [ \fIpid\fR ] \fIserver\-id\fR@ ] \fIremote\-hostname\-or\-IP\fR
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
\fIpid\fR
The process ID for which the memory map is to be printed\&. The process must be a Java process\&. To get a list of Java processes running on a machine, use the jps(1) command\&.
.TP     
\fIexecutable\fR
The Java executable from which the core dump was produced\&.
.TP     
\fIcore\fR
The core file for which the memory map is to be printed\&.
.TP     
\fIremote-hostname-or-IP\fR
The remote debug server \f3hostname\fR or \f3IP\fR address\&. See jsadebugd(1)\&.
.TP     
\fIserver-id\fR
An optional unique ID to use when multiple debug servers are running on the same remote host\&.
.SH DESCRIPTION    
The \f3jmap\fR command prints shared object memory maps or heap memory details of a specified process, core file, or remote debug server\&. If the specified process is running on a 64-bit Java Virtual Machine (JVM), then you might need to specify the \f3-J-d64\fR option, for example: \f3jmap\fR\f3-J-d64 -heap pid\fR\&.
.PP
\fINote:\fR This utility is unsupported and might not be available in future releases of the JDK\&. On Windows Systems where the \f3dbgeng\&.dll\fR file is not present, Debugging Tools For Windows must be installed to make these tools work\&. The \f3PATH\fR environment variable should contain the location of the \f3jvm\&.dll\fR file that is used by the target process or the location from which the crash dump file was produced, for example: \f3set PATH=%JDK_HOME%\ejre\ebin\eclient;%PATH%\fR\&.
.SH OPTIONS    
.TP     
<no option>
When no option is used, the \f3jmap\fR command prints shared object mappings\&. For each shared object loaded in the target JVM, the start address, size of the mapping, and the full path of the shared object file are printed\&. This behavior is similar to the Oracle Solaris \f3pmap\fR utility\&.
.TP
-dump:[live,] format=b, file=\fIfilename\fR
.br
Dumps the Java heap in \f3hprof\fR binary format to \f3filename\fR\&. The \f3live\fR suboption is optional, but when specified, only the active objects in the heap are dumped\&. To browse the heap dump, you can use the jhat(1) command to read the generated file\&.
.TP
-finalizerinfo
.br
Prints information about objects that are awaiting finalization\&.
.TP
-heap
.br
Prints a heap summary of the garbage collection used, the head configuration, and generation-wise heap usage\&. In addition, the number and size of interned Strings are printed\&.
.TP
-histo[:live]
.br
Prints a histogram of the heap\&. For each Java class, the number of objects, memory size in bytes, and the fully qualified class names are printed\&. The JVM internal class names are printed with an asterisk (*) prefix\&. If the \f3live\fR suboption is specified, then only active objects are counted\&.
.TP
-clstats
.br
Prints class loader wise statistics of Java heap\&. For each class loader, its name, how active it is, address, parent class loader, and the number and size of classes it has loaded are printed\&.
.TP
-F
.br
Force\&. Use this option with the \f3jmap -dump\fR or \f3jmap -histo\fR option when the pid does not respond\&. The \f3live\fR suboption is not supported in this mode\&.
.TP
-h
.br
Prints a help message\&.
.TP
-help
.br
Prints a help message\&.
.TP
-J\fIflag\fR
.br
Passes \f3flag\fR to the Java Virtual Machine where the \f3jmap\fR command is running\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
jhat(1)
.TP 0.2i    
\(bu
jps(1)
.TP 0.2i    
\(bu
jsadebugd(1)
.RE
.br
'pl 8.5i
'bp
