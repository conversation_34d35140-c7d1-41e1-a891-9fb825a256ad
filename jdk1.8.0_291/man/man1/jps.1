'\" t
.\"  Copyright (c) 2004, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Monitoring Tools
.\"     Title: jps.1
.\"
.if n .pl 99999
.TH jps 1 "21 November 2013" "JDK 8" "Monitoring Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
jps \- Lists the instrumented Java Virtual Machines (JVMs) on the target system\&. This command is experimental and unsupported\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjps\fR [ \fIoptions\fR ] [ \fIhostid\fR ]
.fi     
.sp     
.TP     
\fIoptions\fR
Command-line options\&. See Options\&.
.TP     
\fIhostid\fR
The identifier of the host for which the process report should be generated\&. The \f3hostid\fR can include optional components that indicate the communications protocol, port number, and other implementation specific data\&. See Host Identifier\&.
.SH DESCRIPTION    
The \f3jps\fR command lists the instrumented Java HotSpot VMs on the target system\&. The command is limited to reporting information on JVMs for which it has the access permissions\&.
.PP
If the \f3jps\fR command is run without specifying a \f3hostid\fR, then it searches for instrumented JVMs on the local host\&. If started with a \f3hostid\fR, then it searches for JVMs on the indicated host, using the specified protocol and port\&. A \f3jstatd\fR process is assumed to be running on the target host\&.
.PP
The \f3jps\fR command reports the local JVM identifier, or \f3lvmid\fR, for each instrumented JVM found on the target system\&. The \f3lvmid\fR is typically, but not necessarily, the operating system\&'s process identifier for the JVM process\&. With no options, \f3jps\fR lists each Java application\&'s \f3lvmid\fR followed by the short form of the application\&'s class name or jar file name\&. The short form of the class name or JAR file name omits the class\&'s package information or the JAR files path information\&.
.PP
The \f3jps\fR command uses the Java launcher to find the class name and arguments passed to the main method\&. If the target JVM is started with a custom launcher, then the class or JAR file name and the arguments to the \f3main\fR method are not available\&. In this case, the \f3jps\fR command outputs the string \f3Unknown\fR for the class name or JAR file name and for the arguments to the \f3main\fR method\&.
.PP
The list of JVMs produced by the \f3jps\fR command can be limited by the permissions granted to the principal running the command\&. The command only lists the JVMs for which the principle has access rights as determined by operating system-specific access control mechanisms\&.
.SH OPTIONS    
The \f3jps\fR command supports a number of options that modify the output of the command\&. These options are subject to change or removal in the future\&.
.TP
-q
.br
Suppresses the output of the class name, JAR file name, and arguments passed to the \f3main\fR method, producing only a list of local JVM identifiers\&.
.TP
-m
.br
Displays the arguments passed to the \f3main\fR method\&. The output may be \f3null\fR for embedded JVMs\&.
.TP
-l
.br
Displays the full package name for the application\&'s \f3main\fR class or the full path name to the application\&'s JAR file\&.
.TP
-v
.br
Displays the arguments passed to the JVM\&.
.TP
-V
.br
Suppresses the output of the class name, JAR file name, and arguments passed to the main method, producing only a list of local JVM identifiers\&.
.TP
-J\f3option\fR
.br
Passes \f3option\fR to the JVM, where option is one of the \f3options\fR described on the reference page for the Java application launcher\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&. See java(1)\&.
.SH HOST\ IDENTIFIER    
The host identifier, or \f3hostid\fR is a string that indicates the target system\&. The syntax of the \f3hostid\fR string corresponds to the syntax of a URI:
.sp     
.nf     
\f3[protocol:][[//]hostname][:port][/servername]\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.TP     
\fIprotocol\fR
The communications protocol\&. If the \f3protocol\fR is omitted and a \f3hostname\fR is not specified, then the default protocol is a platform-specific, optimized, local protocol\&. If the protocol is omitted and a host name is specified, then the default protocol is \f3rmi\fR\&.
.TP     
hostname
A hostname or IP address that indicates the target host\&. If you omit the \f3hostname\fR parameter, then the target host is the local host\&.
.TP     
port
The default port for communicating with the remote server\&. If the \f3hostname\fR parameter is omitted or the \f3protocol\fR parameter specifies an optimized, local protocol, then the \f3port\fR parameter is ignored\&. Otherwise, treatment of the \f3port\fR parameter is implementation specific\&. For the default \f3rmi\fR protocol, the \f3port\fR parameter indicates the port number for the rmiregistry on the remote host\&. If the \f3port\fR parameter is omitted, and the \f3protocol\fR parameter indicates \f3rmi\fR, then the default rmiregistry port (1099) is used\&.
.TP     
servername
The treatment of this parameter depends on the implementation\&. For the optimized, local protocol, this field is ignored\&. For the \f3rmi\fR protocol, this parameter is a string that represents the name of the RMI remote object on the remote host\&. See the \f3jstatd\fR command \f3-n\fRoption for more information\&.
.SH OUTPUT\ FORMAT    
The output of the \f3jps\fR command follows the following pattern:
.sp     
.nf     
\f3lvmid [ [ classname | JARfilename | "Unknown"] [ arg* ] [ jvmarg* ] ]\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
All output tokens are separated by white space\&. An \f3arg\fR value that includes embedded white space introduces ambiguity when attempting to map arguments to their actual positional parameters\&.
.PP
\fINote:\fR It is recommended that you do not write scripts to parse \f3jps\fR output because the format might change in future releases\&. If you write scripts that parse \f3jps\fR output, then expect to modify them for future releases of this tool\&.
.SH EXAMPLES    
This section provides examples of the \f3jps\fR command\&.
.PP
List the instrumented JVMs on the local host:
.sp     
.nf     
\f3jps\fP
.fi     
.nf     
\f318027 Java2Demo\&.JAR\fP
.fi     
.nf     
\f318032 jps\fP
.fi     
.nf     
\f318005 jstat\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The following example lists the instrumented JVMs on a remote host\&. This example assumes that the \f3jstat\fR server and either the its internal RMI registry or a separate external rmiregistry process are running on the remote host on the default port (port 1099)\&. It also assumes that the local host has appropriate permissions to access the remote host\&. This example also includes the \f3-l\fR option to output the long form of the class names or JAR file names\&.
.sp     
.nf     
\f3jps \-l remote\&.domain\fP
.fi     
.nf     
\f33002 /opt/jdk1\&.7\&.0/demo/jfc/Java2D/Java2Demo\&.JAR\fP
.fi     
.nf     
\f32857 sun\&.tools\&.jstatd\&.jstatd\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The following example lists the instrumented JVMs on a remote host with a non-default port for the RMI registry\&. This example assumes that the \f3jstatd\fR server, with an internal RMI registry bound to port 2002, is running on the remote host\&. This example also uses the \f3-m\fR option to include the arguments passed to the \f3main\fR method of each of the listed Java applications\&.
.sp     
.nf     
\f3jps \-m remote\&.domain:2002\fP
.fi     
.nf     
\f33002 /opt/jdk1\&.7\&.0/demo/jfc/Java2D/Java2Demo\&.JAR\fP
.fi     
.nf     
\f33102 sun\&.tools\&.jstatd\&.jstatd \-p 2002\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
java(1)
.TP 0.2i    
\(bu
jstat(1)
.TP 0.2i    
\(bu
jstatd(1)
.TP 0.2i    
\(bu
rmiregistry(1)
.RE
.br
'pl 8.5i
'bp
