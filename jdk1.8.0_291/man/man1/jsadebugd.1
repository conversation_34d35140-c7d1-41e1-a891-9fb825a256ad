'\" t
.\"  Copyright (c) 2004, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Troubleshooting Tools
.\"     Title: jsadebugd.1
.\"
.if n .pl 99999
.TH jsadebugd 1 "21 November 2013" "JDK 8" "Troubleshooting Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
jsadebugd \- Attaches to a Java process or core file and acts as a debug server\&. This command is experimental and unsupported\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBjsadebugd\fR \fIpid\fR [ \fIserver\-id\fR ]
.fi     
.nf     

\fBjsadebugd\fR \fIexecutable\fR \fIcore\fR [ \fIserver\-id\fR ]
.fi     
.sp     
.TP     
\fIpid\fR
The process ID of the process to which the debug server attaches\&. The process must be a Java process\&. To get a list of Java processes running on a machine, use the jps(1) command\&. At most one instance of the debug server can be attached to a single process\&.
.TP     
\fIexecutable\fR
The Java executable from which the core dump was produced\&.
.TP     
\fIcore\fR
The core file to which the debug server should attach\&.
.TP     
\fIserver-id\fR
An optional unique ID that is needed when multiple debug servers are started on the same machine\&. This ID must be used by remote clients to identify the particular debug server to which to attach\&. Within a single machine, this ID must be unique\&.
.SH DESCRIPTION    
The \f3jsadebugd\fR command attaches to a Java process or core file and acts as a debug server\&. Remote clients such as \f3jstack\fR, \f3jmap\fR, and \f3jinfo\fR can attach to the server through Java Remote Method Invocation (RMI)\&. Before you start the \f3jsadebugd\fR command, start the RMI registry with the \f3rmiregistry\fR command as follows where \fI$JAVA_HOME\fR is the JDK installation directory:
.sp     
.nf     
\f3rmiregistry \-J\-Xbootclasspath/p:$JAVA_HOME/lib/sajdi\&.jar\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
If the RMI registry was not started, then the \f3jsadebugd\fR command starts an RMI registry in a standard (1099) port internally\&. The debug server can be stopped by sending a \f3SIGINT\fR to it\&. To send a SIGINT press \fICtrl+C\fR\&.
.PP
\fINote:\fR This utility is unsupported and may or may not be available in future releases of the JDK\&. In Windows Systems where \f3dbgeng\&.dll\fR is not present, Debugging Tools For Windows must be installed to have these tools working\&. The \f3PATH\fR environment variable should contain the location of jvm\&.dll used by the target process or the location from which the crash dump file was produced\&. For example, \f3s\fR\f3et PATH=%JDK_HOME%\ejre\ebin\eclient;%PATH%\fR\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
jinfo(1)
.TP 0.2i    
\(bu
jmap(1)
.TP 0.2i    
\(bu
jps(1)
.TP 0.2i    
\(bu
jstack(1)
.TP 0.2i    
\(bu
rmiregistry(1)
.RE
.br
'pl 8.5i
'bp
