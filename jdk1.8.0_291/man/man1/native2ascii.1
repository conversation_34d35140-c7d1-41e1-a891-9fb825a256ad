'\" t
.\"  Copyright (c) 1997, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Internationalization Tools
.\"     Title: native2ascii.1
.\"
.if n .pl 99999
.TH native2ascii 1 "21 November 2013" "JDK 8" "Internationalization Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
native2ascii \- Creates localizable applications by converting a file with characters in any supported character encoding to one with ASCII and/or Unicode escapes or vice versa\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBnative2ascii\fR [ \fIinputfile\fR ] [ \fIoutputfile\fR ]
.fi     
.sp     
.TP     
\fIinputfile\fR
The encoded file to be converted to ASCII\&.
.TP     
\fIoutputfile\fR
The converted ASCII file\&.
.SH DESCRIPTION    
The \f3native2ascii\fR command converts encoded files supported by the Java Runtime Environment (JRE) to files encoded in ASCII, using Unicode escapes (\f3\eu\fR\fIxxxx\fR) notation for all characters that are not part of the ASCII character set\&. This process is required for properties files that contain characters not in ISO-8859-1 character sets\&. The tool can also perform the reverse conversion\&.
.PP
If the \f3outputfile\fR value is omitted, then standard output is used for output\&. If, in addition, the \f3inputfile\fR value is omitted, then standard input is used for input\&.
.SH OPTIONS    
.TP
-reverse
.br
Perform the reverse operation: Converts a file encoded in ISO-8859-1 with Unicode escapes to a file in any character encoding supported by the JRE\&.
.TP
-encoding \fIencoding_name\fR
.br
Specifies the name of the character encoding to be used by the conversion procedure\&. If this option is not present, then the default character encoding (as determined by the \f3java\&.nio\&.charset\&.Charset\&.defaultCharset\fR method) is used\&. The \f3encoding_name\fR string must be the name of a character encoding that is supported by the JRE\&. See Supported Encodings at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/intl/encoding\&.doc\&.html
.TP
-J\fIoption\fR
.br
Passes \f3option\fR to the Java Virtual Machine (JVM), where option is one of the options described on the reference page for the Java application launcher\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&. See java(1)\&.
.RE
.br
'pl 8.5i
'bp
