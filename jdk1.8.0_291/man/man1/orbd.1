'\" t
.\"  Copyright (c) 2001, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Java IDL and RMI-IIOP Tools
.\"     Title: orbd.1
.\"
.if n .pl 99999
.TH orbd 1 "21 November 2013" "JDK 8" "Java IDL and RMI-IIOP Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
orbd \- Enables clients to locate and call persistent objects on servers in the CORBA environment\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBorbd\fR [ \fIoptions\fR ]
.fi     
.sp     
.TP     
\fIoptions\fR
Command-line options\&. See Options\&.
.SH DESCRIPTION    
The \f3orbd\fR command enables clients to transparently locate and call persistent objects on servers in the CORBA environment\&. The Server Manager included with the orbd tool is used to enable clients to transparently locate and call persistent objects on servers in the CORBA environment\&. The persistent servers, while publishing the persistent object references in the naming service, include the port number of the ORBD in the object reference instead of the port number of the server\&. The inclusion of an ORBD port number in the object reference for persistent object references has the following advantages:
.TP 0.2i    
\(bu
The object reference in the naming service remains independent of the server life cycle\&. For example, the object reference could be published by the server in the Naming Service when it is first installed, and then, independent of how many times the server is started or shut down, the ORBD returns the correct object reference to the calling client\&.
.TP 0.2i    
\(bu
The client needs to look up the object reference in the naming service only once, and can keep reusing this reference independent of the changes introduced due to server life cycle\&.
.PP
To access the ORBD Server Manager, the server must be started using \f3servertool\fR, which is a command-line interface for application programmers to register, unregister, start up, and shut down a persistent server\&. For more information on the Server Manager, see Server Manager\&.
.PP
When \f3orbd\fR starts, it also starts a naming service\&. For more information about the naming service\&. See Start and Stop the Naming Service\&.
.SH OPTIONS    
.TP
-ORBInitialPort \fInameserverport\fR
.br
Required\&. Specifies the port on which the name server should be started\&. After it is started, \f3orbd\fR listens for incoming requests on this port\&. On Oracle Solaris software, you must become the root user to start a process on a port below 1024\&. For this reason, Oracle recommends that you use a port number above or equal to 1024\&.
.SS NONREQUIRED\ OPTIONS    
.TP
-port \fIport\fR
.br
Specifies the activation port where ORBD should be started, and where ORBD will be accepting requests for persistent objects\&. The default value for this port is 1049\&. This port number is added to the port field of the persistent Interoperable Object References (IOR)\&.
.TP
-defaultdb \fIdirectory\fR
.br
Specifies the base where the ORBD persistent storage directory, \f3orb\&.db\fR, is created\&. If this option is not specified, then the default value is \f3\&./orb\&.db\fR\&.
.TP
-serverPollingTime \fImilliseconds\fR
.br
Specifies how often ORBD checks for the health of persistent servers registered through \f3servertool\fR\&. The default value is 1000 ms\&. The value specified for \f3milliseconds\fR must be a valid positive integer\&.
.TP
-serverStartupDelay milliseconds
.br
Specifies how long ORBD waits before sending a location forward exception after a persistent server that is registered through \f3servertool\fR is restarted\&. The default value is 1000 ms\&. The value specified for \f3milliseconds\fR must be a valid positive integer\&.
.TP
-J\fIoption\fR
.br
Passes \f3option\fR to the Java Virtual Machine, where \f3option\fR is one of the options described on the reference page for the Java application launcher\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&. See java(1)\&.
.SS START\ AND\ STOP\ THE\ NAMING\ SERVICE    
A naming service is a CORBA service that allows CORBA objects to be named by means of binding a name to an object reference\&. The name binding can be stored in the naming service, and a client can supply the name to obtain the desired object reference\&.
.PP
Before running a client or a server, you will start ORBD\&. ORBD includes a persistent naming service and a transient naming service, both of which are an implementation of the COS Naming Service\&.
.PP
The Persistent Naming Service provides persistence for naming contexts\&. This means that this information is persistent across service shutdowns and startups, and is recoverable in the event of a service failure\&. If ORBD is restarted, then the Persistent Naming Service restores the naming context graph, so that the binding of all clients\&' and servers\&' names remains intact (persistent)\&.
.PP
For backward compatibility, \f3tnameserv\fR, a Transient Naming Service that shipped with earlier releases of the JDK, is also included in this release of Java SE\&. A transient naming service retains naming contexts as long as it is running\&. If there is a service interruption, then the naming context graph is lost\&.
.PP
The \f3-ORBInitialPort\fR argument is a required command-line argument for \f3orbd\fR, and is used to set the port number on which the naming service runs\&. The following instructions assume you can use port 1050 for the Java IDL Object Request Broker Daemon\&. When using Oracle Solaris software, you must become a root user to start a process on a port lower than 1024\&. For this reason, it is recommended that you use a port number above or equal to 1024\&. You can substitute a different port when necessary\&.
.PP
To start \f3orbd\fR from a UNIX command shell, enter:
.sp     
.nf     
\f3orbd \-ORBInitialPort 1050&\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
From an MS-DOS system prompt (Windows), enter:
.sp     
.nf     
\f3start orbd \-ORBInitialPort 1050\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Now that ORBD is running, you can run your server and client applications\&. When running the client and server applications, they must be made aware of the port number (and machine name, when applicable) where the Naming Service is running\&. One way to do this is to add the following code to your application:
.sp     
.nf     
\f3Properties props = new Properties();\fP
.fi     
.nf     
\f3props\&.put("org\&.omg\&.CORBA\&.ORBInitialPort", "1050");\fP
.fi     
.nf     
\f3props\&.put("org\&.omg\&.CORBA\&.ORBInitialHost", "MyHost");\fP
.fi     
.nf     
\f3ORB orb = ORB\&.init(args, props);\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
In this example, the naming service is running on port 1050 on host \f3MyHost\fR\&. Another way is to specify the port number and/or machine name when running the server or client application from the command line\&. For example, you would start your \f3HelloApplication\fR with the following command line:
.sp     
.nf     
\f3java HelloApplication \-ORBInitialPort 1050 \-ORBInitialHost MyHost\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
To stop the naming service, use the relevant operating system command, such as \f3pkill\fR\f3orbd\fR on Oracle Solaris, or \fICtrl+C\fR in the DOS window in which \f3orbd\fR is running\&. Note that names registered with the naming service can disappear when the service is terminated because of a transient naming service\&. The Java IDL naming service will run until it is explicitly stopped\&.
.PP
For more information about the naming service included with ORBD, see Naming Service at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/jidlNaming\&.html
.SH SERVER\ MANAGER    
To access the ORBD Server Manager and run a persistent server, the server must be started with \f3servertool\fR, which is a command-line interface for application programmers to register, unregister, start up, and shut down a persistent server\&. When a server is started using \f3servertool\fR, it must be started on the same host and port on which \f3orbd\fR is executing\&. If the server is run on a different port, then the information stored in the database for local contexts will be invalid and the service will not work properly\&.
.PP
See Java IDL: The "Hello World" Example at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/jidlExample\&.html
.PP
In this example, you run the \f3idlj\fR compiler and \f3javac\fR compiler as shown in the tutorial\&. To run the ORBD Server Manager, follow these steps for running the application:
.PP
Start \f3orbd\fR\&.
.PP
UNIX command shell, enter: \f3orbd -ORBInitialPort 1050\fR\&.
.PP
MS-DOS system prompt (Windows), enter: \f3s\fR\f3tart orbd -ORBInitialPort 105\fR\f30\fR\&.
.PP
Port 1050 is the port on which you want the name server to run\&. The \f3-ORBInitialPort\fR option is a required command-line argument\&. When using Oracle Solaris software, you must become a root user to start a process on a port below 1024\&. For this reason, it is recommended that you use a port number above or equal to 1024\&.
.PP
Start the \f3servertool\fR: \f3servertool -ORBInitialPort 1050\fR\&.
.PP
Make sure the name server (\f3orbd\fR) port is the same as in the previous step, for example, \f3-ORBInitialPort 1050\&.\fR The \f3servertool\fR must be started on the same port as the name server\&.
.PP
In the \f3servertool\fR command line interface, start the \f3Hello\fR server from the \f3servertool\fR prompt:
.sp     
.nf     
\f3servertool  > register \-server HelloServer \-classpath \&. \-applicationName\fP
.fi     
.nf     
\f3                HelloServerApName\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The \f3servertool\fR registers the server, assigns it the name \f3HelloServerApName\fR, and displays its server ID with a listing of all registered servers\&.Run the client application from another terminal window or prompt:
.sp     
.nf     
\f3java HelloClient \-ORBInitialPort 1050 \-ORBInitialHost localhost\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
For this example, you can omit \f3-ORBInitialHost localhost\fR because the name server is running on the same host as the \f3Hello\fR client\&. If the name server is running on a different host, then use the -\f3ORBInitialHost nameserverhost\fR option to specify the host on which the IDL name server is running\&.Specify the name server (\f3orbd\fR) port as done in the previous step, for example, \f3-ORBInitialPort 1050\fR\&. When you finish experimenting with the ORBD Server Manager, be sure to shut down or terminate the name server (\f3orbd\fR) and \f3servertool\fR\&. To shut down \f3orbd\fR from am MS-DOS prompt, select the window that is running the server and enter \fICtrl+C\fR to shut it down\&.
.PP
To shut down \f3orbd\fR from an Oracle Solaris shell, find the process, and terminate with the \f3kill\fR command\&. The server continues to wait for invocations until it is explicitly stopped\&. To shut down the \f3servertool\fR, type \fIquit\fR and press the \fIEnter\fR key\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
servertool(1)
.TP 0.2i    
\(bu
Naming Service at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/jidlNaming\&.html
.RE
.br
'pl 8.5i
'bp
