'\" t
.\"  Copyright (c) 2001, 2015, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 03 March 2015
.\"     SectDesc: Security Tools
.\"     Title: policytool.1
.\"
.if n .pl 99999
.TH policytool 1 "03 March 2015" "JDK 8" "Security Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
policytool \- Reads and writes a plain text policy file based on user input through the utility GUI\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBpolicytool\fR [ \fB\-file\fR ] [ \fIfilename\fR ] 
.fi     
.sp     
.TP
-file
.br
Directs the \f3policytool\fR command to load a policy file\&.
.TP     
\fIfilename\fR
The name of the file to be loaded\&.
.PP
\fIExamples\fR:
.PP
Run the policy tool administrator utility:
.sp     
.nf     
\f3policytool\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Run the \f3policytool\fR command and load the specified file:
.sp     
.nf     
\f3policytool \-file \fImypolicyfile\fR\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH DESCRIPTION    
The \f3policytool\fR command calls an administrator\&'s GUI that enables system administrators to manage the contents of local policy files\&. A policy file is a plain-text file with a \f3\&.policy\fR extension, that maps remote requestors by domain, to permission objects\&. For details, see Default Policy Implementation and Policy File Syntax at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/PolicyFiles\&.html
.SH OPTIONS    
.TP
-file
.br
Directs the \f3policytool\fR command to load a policy file\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
Default Policy Implementation and Policy File Syntax at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/PolicyFiles\&.html
.TP 0.2i    
\(bu
Policy File Creation and Management at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/PolicyGuide\&.html
.TP 0.2i    
\(bu
Permissions in Java SE Development Kit (JDK) at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/permissions\&.html
.TP 0.2i    
\(bu
Java Security Overview at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/overview/jsoverview\&.html
.TP 0.2i    
\(bu
Java Cryptography Architecture (JCA) Reference Guide at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/security/crypto/CryptoSpec\&.html
.RE
.br
'pl 8.5i
'bp
