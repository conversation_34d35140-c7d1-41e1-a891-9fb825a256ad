'\" t
.\"  Copyright (c) 1999, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Java IDL and RMI-IIOP Tools
.\"     Title: tnameserv.1
.\"
.if n .pl 99999
.TH tnameserv 1 "21 November 2013" "JDK 8" "Java IDL and RMI-IIOP Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
tnameserv \- Interface Definition Language (IDL)\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBtnameserve\fR \fB\-ORBInitialPort\fR [ \fInameserverport\fR ]
.fi     
.sp     
.TP
-ORBInitialPort \fInameserverport\fR
.br
The initial port where the naming service listens for the bootstrap protocol used to implement the ORB \f3resolve_initial_references\fR and \f3list_initial_references\fR methods\&.
.SH DESCRIPTION    
Java IDL includes the Object Request Broker Daemon (ORBD)\&. ORBD is a daemon process that contains a Bootstrap Service, a Transient Naming Service, a Persistent Naming Service, and a Server Manager\&. The Java IDL tutorials all use ORBD, but you can substitute the \f3tnameserv\fR command for the \f3orbd\fR command in any of the examples that use a Transient Naming Service\&.
.PP
See orbd(1) or Naming Service at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/jidlNaming\&.html
.PP
The CORBA Common Object Services (COS) Naming Service provides a tree-structure directory for object references similar to a file system that provides a directory structure for files\&. The Transient Naming Service provided with Java IDL, \f3tnameserv\fR, is a simple implementation of the COS Naming Service specification\&.
.PP
Object references are stored in the name space by name and each object reference-name pair is called a name binding\&. Name bindings can be organized under naming contexts\&. Naming contexts are name bindings and serve the same organizational function as a file system subdirectory\&. All bindings are stored under the initial naming context\&. The initial naming context is the only persistent binding in the name space\&. The rest of the name space is lost when the Java IDL naming service process stops and restarts\&.
.PP
For an applet or application to use COS naming, its ORB must know the port of a host running a naming service or have access to an initial naming context string for that naming service\&. The naming service can either be the Java IDL naming service or another COS-compliant naming service\&.
.SS START\ THE\ NAMING\ SERVICE    
You must start the Java IDL naming service before an application or applet that uses its naming service\&. Installation of the Java IDL product creates a script (Oracle Solaris: \f3tnameserv\fR) or executable file (Windows: \f3tnameserv\&.exe\fR) that starts the Java IDL naming service\&. Start the naming service so it runs in the background\&.
.PP
If you do not specify otherwise, then the Java IDL naming service listens on port 900 for the bootstrap protocol used to implement the ORB \f3resolve_initial_references\fR and \f3list_initial_references methods\fR, as follows:
.sp     
.nf     
\f3tnameserv \-ORBInitialPort nameserverport&\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
If you do not specify the name server port, then port 900 is used by default\&. When running Oracle Solaris software, you must become the root user to start a process on a port below 1024\&. For this reason, it is recommended that you use a port number greater than or equal to 1024\&. To specify a different port, for example, 1050, and to run the naming service in the background, from a UNIX command shell, enter:
.sp     
.nf     
\f3tnameserv \-ORBInitialPort 1050&\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
From an MS-DOS system prompt (Windows), enter:
.sp     
.nf     
\f3start tnameserv \-ORBInitialPort 1050\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Clients of the name server must be made aware of the new port number\&. Do this by setting the \f3org\&.omg\&.CORBA\&.ORBInitialPort\fR property to the new port number when you create the ORB object\&.
.SS RUN\ THE\ SERVER\ AND\ CLIENT\ ON\ DIFFERENT\ HOSTS    
In most of the Java IDL and RMI-IIOP tutorials, the naming service, server, and client are all running on the development machine\&. In real-world deployment, the client and server probably run on different host machines from the Naming Service\&.
.PP
For the client and server to find the Naming Service, they must be made aware of the port number and host on which the naming service is running\&. Do this by setting the \f3org\&.omg\&.CORBA\&.ORBInitialPort\fR and \f3org\&.omg\&.CORBA\&.ORBInitialHost\fR properties in the client and server files to the machine name and port number on which the Naming Service is running\&. An example of this is shown in Getting Started Using RMI-IIOP at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/rmi-iiop/rmiiiopexample\&.html
.PP
You could also use the command-line options \f3-ORBInitialPort nameserverport#\fR and \f3-ORBInitialHost nameserverhostname\fR to tell the client and server where to find the naming service\&. For one example of doing this using the command-line option, see Java IDL: The Hello World Example on Two Machines at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/idl/tutorial/jidl2machines\&.html
.PP
For example, suppose the Transient Naming Service, \f3tnameserv\fR is running on port 1050 on host \f3nameserverhost\fR\&. The client is running on host \f3clienthost,\fR and the server is running on host \f3serverhost\fR\&.
.PP
Start \f3tnameserv\fR on the host \f3nameserverhost\fR:
.sp     
.nf     
\f3tnameserv \-ORBInitialPort 1050\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Start the server on the \f3serverhost\fR:
.sp     
.nf     
\f3java Server \-ORBInitialPort 1050 \-ORBInitialHost nameserverhost\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
Start the client on the \f3clienthost\fR:
.sp     
.nf     
\f3java Client \-ORBInitialPort 1050 \-ORBInitialHost nameserverhost\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS STOP\ THE\ NAMING\ SERVICE    
To stop the Java IDL naming service, use the relevant operating system command, such as \f3kill\fR for a Unix process or \f3Ctrl+C\fR for a Windows process\&. The naming service continues to wait for invocations until it is explicitly shut down\&. Note that names registered with the Java IDL naming service disappear when the service is terminated\&.
.SH OPTIONS    
.TP
-J\fIoption\fR
.br
Passes \f3option\fR to the Java Virtual Machine, where \f3option\fR is one of the options described on the reference page for the Java application launcher\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&. See java(1)\&.
.SH EXAMPLES    
.SS ADD\ OBJECTS\ TO\ THE\ NAME\ SPACE    
The following example shows how to add names to the name space\&. It is a self-contained Transient Naming Service client that creates the following simple tree\&.
.sp     
.nf     
\f3Initial Naming Context\fP
.fi     
.nf     
\f3     plans\fP
.fi     
.nf     
\f3     Personal\fP
.fi     
.nf     
\f3          calendar\fP
.fi     
.nf     
\f3          schedule\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
In this example, \f3plans\fR is an object reference and \f3Personal\fR is a naming context that contains two object references: \f3calendar\fR and \f3schedule\fR\&.
.sp     
.nf     
\f3import java\&.util\&.Properties;\fP
.fi     
.nf     
\f3import org\&.omg\&.CORBA\&.*;\fP
.fi     
.nf     
\f3import org\&.omg\&.CosNaming\&.*;\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3public class NameClient {\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3    public static void main(String args[]) {\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3        try {\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
In Start the Naming Service, the \f3nameserver\fR was started on port 1050\&. The following code ensures that the client program is aware of this port number\&.
.sp     
.nf     
\f3            Properties props = new Properties();\fP
.fi     
.nf     
\f3            props\&.put("org\&.omg\&.CORBA\&.ORBInitialPort", "1050");\fP
.fi     
.nf     
\f3            ORB orb = ORB\&.init(args, props);\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
This code obtains the initial naming context and assigns it to \f3ctx\fR\&. The second line copies \f3ctx\fR into a dummy object reference \f3objref\fR that is attached to various names and added into the name space\&.
.sp     
.nf     
\f3            NamingContext ctx =\fP
.fi     
.nf     
\f3                NamingContextHelper\&.narrow(\fP
.fi     
.nf     
\f3                    orb\&.resolve_initial_references("NameService"));\fP
.fi     
.nf     
\f3             NamingContext objref = ctx;\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
This code creates a name \f3plans\fR of type \f3text\fR and binds it to the dummy object reference\&. \f3plans\fR is then added under the initial naming context using the \f3rebind\fR method\&. The \f3rebind\fR method enables you to run this program over and over again without getting the exceptions from using the \f3bind\fR method\&.
.sp     
.nf     
\f3            NameComponent nc1 = new NameComponent("plans", "text");\fP
.fi     
.nf     
\f3            NameComponent[] name1 = {nc1};\fP
.fi     
.nf     
\f3            ctx\&.rebind(name1, objref);\fP
.fi     
.nf     
\f3            System\&.out\&.println("plans rebind successful!");\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
This code creates a naming context called \f3Personal\fR of type \f3directory\fR\&. The resulting object reference, \f3ctx2\fR, is bound to the \f3name\fR and added under the initial naming context\&.
.sp     
.nf     
\f3            NameComponent nc2 = new NameComponent("Personal", "directory");\fP
.fi     
.nf     
\f3            NameComponent[] name2 = {nc2};\fP
.fi     
.nf     
\f3            NamingContext ctx2 = ctx\&.bind_new_context(name2);\fP
.fi     
.nf     
\f3            System\&.out\&.println("new naming context added\&.\&.");\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The remainder of the code binds the dummy object reference using the names \f3schedule\fR and \f3calendar\fR under the \f3Personal\fR naming context (\f3ctx2\fR)\&.
.sp     
.nf     
\f3            NameComponent nc3 = new NameComponent("schedule", "text");\fP
.fi     
.nf     
\f3            NameComponent[] name3 = {nc3};\fP
.fi     
.nf     
\f3            ctx2\&.rebind(name3, objref);\fP
.fi     
.nf     
\f3            System\&.out\&.println("schedule rebind successful!");\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3            NameComponent nc4 = new NameComponent("calender", "text");\fP
.fi     
.nf     
\f3            NameComponent[] name4 = {nc4};\fP
.fi     
.nf     
\f3            ctx2\&.rebind(name4, objref);\fP
.fi     
.nf     
\f3            System\&.out\&.println("calender rebind successful!");\fP
.fi     
.nf     
\f3        } catch (Exception e) {\fP
.fi     
.nf     
\f3            e\&.printStackTrace(System\&.err);\fP
.fi     
.nf     
\f3        }\fP
.fi     
.nf     
\f3    }\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SS BROWSING\ THE\ NAME\ SPACE    
The following sample program shoes how to browse the name space\&.
.sp     
.nf     
\f3import java\&.util\&.Properties;\fP
.fi     
.nf     
\f3import org\&.omg\&.CORBA\&.*;\fP
.fi     
.nf     
\f3import org\&.omg\&.CosNaming\&.*;\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3public class NameClientList {\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3    public static void main(String args[]) {\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3        try {\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
In Start the Naming Service, the \f3nameserver\fR was started on port 1050\&. The following code ensures that the client program is aware of this port number\&.
.sp     
.nf     
\f3            Properties props = new Properties();\fP
.fi     
.nf     
\f3            props\&.put("org\&.omg\&.CORBA\&.ORBInitialPort", "1050");\fP
.fi     
.nf     
\f3            ORB orb = ORB\&.init(args, props);\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The following code obtains the initial naming context\&.
.sp     
.nf     
\f3            NamingContext nc =\fP
.fi     
.nf     
\f3            NamingContextHelper\&.narrow(\fP
.fi     
.nf     
\f3                orb\&.resolve_initial_references("NameService"));\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The \f3list\fR method lists the bindings in the naming context\&. In this case, up to 1000 bindings from the initial naming context will be returned in the \f3BindingListHolder\fR; any remaining bindings are returned in the \f3BindingIteratorHolder\fR\&.
.sp     
.nf     
\f3            BindingListHolder bl = new BindingListHolder();\fP
.fi     
.nf     
\f3            BindingIteratorHolder blIt= new BindingIteratorHolder();\fP
.fi     
.nf     
\f3            nc\&.list(1000, bl, blIt);\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
This code gets the array of bindings out of the returned \f3BindingListHolder\fR\&. If there are no bindings, then the program ends\&.
.sp     
.nf     
\f3            Binding bindings[] = bl\&.value;\fP
.fi     
.nf     
\f3            if (bindings\&.length == 0) return;\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The remainder of the code loops through the bindings and prints outs the names\&.
.sp     
.nf     
\f3            for (int i=0; i < bindings\&.length; i++) {\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3                // get the object reference for each binding\fP
.fi     
.nf     
\f3                org\&.omg\&.CORBA\&.Object obj = nc\&.resolve(bindings[i]\&.binding_name);\fP
.fi     
.nf     
\f3                String objStr = orb\&.object_to_string(obj);\fP
.fi     
.nf     
\f3                int lastIx = bindings[i]\&.binding_name\&.length\-1;\fP
.fi     
.nf     
\f3\fP
.fi     
.nf     
\f3                // check to see if this is a naming context\fP
.fi     
.nf     
\f3                if (bindings[i]\&.binding_type == BindingType\&.ncontext) {\fP
.fi     
.nf     
\f3                    System\&.out\&.println("Context: " +\fP
.fi     
.nf     
\f3                        bindings[i]\&.binding_name[lastIx]\&.id);\fP
.fi     
.nf     
\f3                } else {\fP
.fi     
.nf     
\f3                    System\&.out\&.println("Object: " +\fP
.fi     
.nf     
\f3                        bindings[i]\&.binding_name[lastIx]\&.id);\fP
.fi     
.nf     
\f3                }\fP
.fi     
.nf     
\f3            }\fP
.fi     
.nf     
\f3        } catch (Exception e) {\fP
.fi     
.nf     
\f3            e\&.printStackTrace(System\&.err)\fP
.fi     
.nf     
\f3        }\fP
.fi     
.nf     
\f3    }\fP
.fi     
.nf     
\f3}\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
orbd(1)
.RE
.br
'pl 8.5i
'bp
