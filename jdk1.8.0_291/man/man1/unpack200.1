'\" t
.\"  Copyright (c) 2004, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Java Deployment Tools
.\"     Title: unpack200.1
.\"
.if n .pl 99999
.TH unpack200 1 "21 November 2013" "JDK 8" "Java Deployment Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
unpack200 \- Transforms a packed file produced by pack200(1) into a JAR file for web deployment\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBunpack200\fR [ \fIoptions\fR ] input\-file \fIJAR\-file\fR
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
\fIinput-file\fR
Name of the input file, which can be a pack200 gzip file or a pack200 file\&. The input can also be JAR file produced by \f3pack200\fR(1) with an effort of \f30\fR, in which case the contents of the input file are copied to the output JAR file with the Pack200 marker\&.
.TP     
\fIJAR-file\fR
Name of the output JAR file\&.
.SH DESCRIPTION    
The \f3unpack200\fR command is a native implementation that transforms a packed file produced by \f3pack200\fR\f3(1)\fR into a JAR file\&. A typical usage follows\&. In the following example, the \f3myarchive\&.jar\fR file is produced from \f3myarchive\&.pack\&.gz\fR with the default \f3unpack200\fR command settings\&.
.sp     
.nf     
\f3unpack200 myarchive\&.pack\&.gz myarchive\&.jar\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH OPTIONS    
.TP
-Hvalue --deflate-hint=\fIvalue\fR
.br
Sets the deflation to be \f3true\fR, \f3false\fR, or \f3keep\fR on all entries within a JAR file\&. The default mode is \f3keep\fR\&. If the value is \f3true\fR or \f3false\fR, then the \f3--deflate=hint\fR option overrides the default behavior and sets the deflation mode on all entries within the output JAR file\&.
.TP
-r --remove-pack-file
.br
Removes the input pack file\&.
.TP
-v --verbose
.br
Displays minimal messages\&. Multiple specifications of this option displays more verbose messages\&.
.TP
-q --quiet
.br
Specifies quiet operation with no messages\&.
.TP
-lfilename --log-file=\fIfilename\fR
.br
Specifies a log file where output messages are logged\&.
.TP
-? -h --help
.br
Prints help information about the \f3unpack200\fR command\&.
.TP
-V --version
.br
Prints version information about the \f3unpack200\fR command\&.
.TP
-J\fIoption\fR
.br
Passes option to the Java Virtual Machine, where \f3option\fR is one of the options described on the reference page for the Java application launcher\&. For example, \f3-J-Xms48m\fR sets the startup memory to 48 MB\&. See java(1)\&.
.SH NOTES    
This command should not be confused with the \f3unpack\fR command\&. They are distinctly separate products\&.
.PP
The Java SE API Specification provided with the JDK is the superseding authority in case of discrepancies\&.
.SH EXIT\ STATUS    
The following exit values are returned: 0 for successful completion, and a value that is greater than 0 when an error occurred\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
pack200(1)
.TP 0.2i    
\(bu
jar(1)
.TP 0.2i    
\(bu
jarsigner(1)
.TP 0.2i    
\(bu
Pack200 and Compression at http://docs\&.oracle\&.com/javase/8/docs/technotes/guides/deployment/deployment-guide/pack200\&.html
.TP 0.2i    
\(bu
The Java SE Technical Documentation page at http://docs\&.oracle\&.com/javase/
.RE
.br
'pl 8.5i
'bp
