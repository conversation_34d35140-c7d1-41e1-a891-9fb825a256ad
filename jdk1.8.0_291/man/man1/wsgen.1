'\" t
.\"  Copyright (c) 2005, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Java Web Services Tools
.\"     Title: wsgen.1
.\"
.if n .pl 99999
.TH wsgen 1 "21 November 2013" "JDK 8" "Java Web Services Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
wsgen \- Reads a web service endpoint implementation (SEI) class and generates all of the required artifacts for web service deployment, and invocation\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBwsgen\fR [ \fIoptions\fR ] \fISEI\fR
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
\fISEI\fR
The web service endpoint implementation class (SEI) to be read\&.
.SH DESCRIPTION    
The \f3wsgen\fR command generates JAX-WS portable artifacts used in JAX-WS web services\&. The tool reads a web service endpoint class and generates all the required artifacts for web service deployment and invocation\&. JAXWS 2\&.1\&.1 RI also provides a \f3wsgen\fR Ant task, see the \fITools\fR tab of the JAX-WS (wsgen) page at http://jax-ws\&.java\&.net/nonav/2\&.1\&.1/docs/wsgenant\&.html
.PP
To start the \f3wsgen\fR command, do the following:
.sp     
.nf     
\f3export JAXWS_HOME=/pathto/jaxws\-ri\fP
.fi     
.nf     
\f3$JAXWS_HOME/bin/wsgen\&.sh \-help\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH OPTIONS    
.TP
-classpath \fIpath\fR
.br
The location of the input class files\&.
.TP
-cp \fIpath\fR
.br
The location of the input class files\&.
.TP
-d \fIdirectory\fR
.br
The location for where to place generated output files\&.
.TP
-extension
.br
Allow vendor extensions\&. Use of extensions can result in applications that are not portable or that do not work with other implementations\&.
.TP
-help
.br
Displays a help message about the \f3wsgen\fR command\&.
.TP
-keep
.br
Keeps the generated files\&.
.TP
-r \fIdirectory\fR
.br
Uses this option with the \f3-wsdl\fR option to specify where to place generated resource files such as WSDLs\&.
.TP
-s \fIdirectory\fR
.br
The location for where to place generated source files\&.
.TP
-verbose
.br
Displays compiler messages\&.
.TP
-version
.br
Prints release information\&.
.TP
-wsdl [ :protocol ] \fI\fR
.br
An optional command that generates a WSDL file to review before endpoint deployment\&. The WSDL files contains a machine-readable description of how the service can be called, what parameters it expects, and what data structures it returns\&.

By default the \f3wsgen\fR command does not generate a WSDL file\&. The \f3protocol\fR value is optional and is used to specify what protocol should be used for the WSDL binding (\f3wsdl:binding\fR)\&. Valid protocols are \f3soap1\&.1\fR and \f3Xsoap1\&.2\fR\&. The default is \f3soap1\&.1\fR\&. The \f3Xsoap1\&.2\fR protocol is not standard and can only be used with the \f3-extension\fR option\&.
.TP
-servicename \fIname\fR
.br
Used only with the \f3-wsdl\fR option to specify a particular WSDL service (\f3wsdl:service\fR) name to be generated in the WSDL, for example: \f3-servicename "{http://mynamespace/}MyService"\fR\&.
.TP
-portname \fIname\fR
.br
Used only with the \f3-wsdl\fR option to specify a particular WSDL port (\f3wsdl:port\fR) name to be generated in the WSDL, for example: \f3-portname "{http://mynamespace/}MyPort"\fR\&.
.SH EXAMPLES    
The following example generates the wrapper classes for \f3StockService\fR with \f3@WebService\fR annotations inside stock directory\&.
.sp     
.nf     
\f3wsgen \-d stock \-cp myclasspath stock\&.StockService\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The following example generates a SOAP 1\&.1 WSDL and schema for the \f3stock\&.StockService\fR class with \f3@WebService\fR annotations\&.
.sp     
.nf     
\f3wsgen \-wsdl \-d stock \-cp myclasspath stock\&.StockService\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
The following example generates a SOAP 1\&.2 WSDL\&.
.sp     
.nf     
\f3wsgen \-wsdl:Xsoap1\&.2 \-d stock \-cp myclasspath stock\&.StockService \fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\fINote:\fR You do not have to generate WSDL at development time because the JAXWS run time environment generates a WSDL for you when you deploy your service\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
wsimport(1)
.TP 0.2i    
\(bu
\fIThe Tools\fR tab of the JAX-WS (wsgen) page http://jax-ws\&.java\&.net/nonav/2\&.1\&.1/docs/wsgenant\&.html
.RE
.br
'pl 8.5i
'bp
