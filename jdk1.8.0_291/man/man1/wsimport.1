'\" t
.\"  Copyright (c) 2005, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Java Web Services Tools
.\"     Title: wsimport.1
.\"
.if n .pl 99999
.TH wsimport 1 "21 November 2013" "JDK 8" "Java Web Services Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
wsimport \- Generates JAX-WS portable artifacts that can be packaged in a web application archive (WAR) file and provides an Ant task\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBwsimport\fR [ \fIoptions\fR ] \fIwsdl\fR
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
\fIwsdl\fR
The file that contains the machine-readable description of how the web service can be called, what parameters it expects, and what data structures it returns\&.
.SH DESCRIPTION    
The \f3wsimport\fR command generates the following JAX-WS portable artifacts\&. These artifacts can be packaged in a WAR file with the WSDL and schema documents and the endpoint implementation to be deployed\&. The \f3wsimport\fR command also provides a \f3wsimport\fR Ant task, see the Tools tab of the Wsimport Ant Task page at http://jax-ws\&.java\&.net/nonav/2\&.1\&.1/docs/wsimportant\&.html
.TP 0.2i    
\(bu
Service Endpoint Interface (SEI)
.TP 0.2i    
\(bu
Service
.TP 0.2i    
\(bu
Exception class is mapped from \f3wsdl:fault\fR (if any)
.TP 0.2i    
\(bu
Async Response Bean is derived from response \f3wsdl:message\fR (if any)
.TP 0.2i    
\(bu
JAXB generated value types (mapped java classes from schema types)
.PP
To start the \f3wsgen\fR command, do the following:
.PP
\fIOracle Solaris/Linux\fR:
.sp     
.nf     
\f3/bin/wsimport\&.sh \-help\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
\fIWindows\fR:
.sp     
.nf     
\f3\ebin\ewsimport\&.bat \-help\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH OPTIONS    
.TP
-d \fIdirectory\fR
.br
Specifies where to place generated output files\&.
.TP
-b \fIpath\fR
.br
Specifies external JAX-WS or JAXB binding files\&. Multiple JAX-WS and JAXB binding files can be specified with the \f3-b\fR option\&. You can use these files to customize package names, bean names, and so on\&. For more information about JAX-WS and JAXB binding files, see the \fIUsers Guide\fR tab of WSDL Customization at http://jax-ws\&.java\&.net/nonav/2\&.1\&.1/docs/wsimportant\&.html
.TP
-B \fIjaxbOption\fR
.br
Passes the \f3jaxbOption\fR option to the JAXB schema compiler\&.
.TP
-catalog
.br
Specifies a catalog file to resolve external entity references\&. The \f3-catalog\fR option supports the TR9401, XCatalog, and OASIS XML Catalog formats\&. See the \fIUsers Guide\fR tab of the Catalog Support page at http://jax-ws\&.java\&.net/nonav/2\&.1\&.1/docs/catalog-support\&.html
.TP
-extension
.br
Allows vendor extensions\&. Use of extensions can result in applications that are not portable or that do not work with other implementations\&.
.TP
-help
.br
Displays a help message for the \f3wsimport\fR command\&.
.TP
-httpproxy: \fIhost\fR:\fIport\fR
.br
Specifies an HTTP proxy server\&. The default is 8080\&.
.TP
-keep
.br
Keeps generated files\&.
.TP
-p \fIname\fR
.br
Specifies a target package \fIname\fR to override the WSDL and schema binding customizations, and the default algorithm defined in the specification\&.
.TP
-s \fIdirectory\fR
.br
Specifies where to place generated source files\&.
.TP
-verbose
.br
Displays compiler messages\&.
.TP
-version
.br
Prints release information\&.
.TP
-wsdllocation \fIlocation\fR
.br
Specifies the \f3@WebServiceClient\&.wsdlLocation\fR value\&.
.TP
-target
.br
Generates code according to the specified JAX-WS specification version\&. Version 2\&.0 generates compliant code for the JAX-WS 2\&.0 specification\&.
.TP
-quiet
.br
Suppresses the \f3wsimport\fR command output\&.
.PP
Multiple \f3JAX-WS\fR and \f3JAXB\fR binding files can be specified using the \f3-b\fR option, and they can be used to customize various things such as package names and bean names\&. More information about \f3JAX-WS\fR and \f3JAXB\fR binding files can be found in the customization documentation at https://jax-ws\&.dev\&.java\&.net/nonav/2\&.1\&.1/docs/customizations\&.html
.SH NONSTANDARD\ OPTIONS    
.TP
-XadditionalHeaders
.br
Maps headers not bound to a request or response message to Java method parameters\&.
.TP
-Xauthfile \fIfile\fR
.br
The WSDL URI that specifies the file that contains authorization information\&. This URI is in the following format:

http://\fIuser-name\fR:\f3password\fR@\fIhost-name\fR/\fIweb-service-name\fR>?wsdl\&.
.TP
-Xdebug
.br
Prints debugging information\&.
.TP
-Xno-addressing-databinding
.br
Enables binding of W3C EndpointReferenceType to Java\&.
.TP
-Xnocompile
.br
Does not compile the generated Java files\&.
.SH EXAMPLE    
The following example generates the Java artifacts and compiles the artifacts by importing \f3http://stockquote\&.example\&.com/quote?wsdl\fR
.sp     
.nf     
\f3wsimport \-p stockquote http://stockquote\&.example\&.com/quote?wsdl\fP
.fi     
.nf     
\f3\fP
.fi     
.sp     
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
wsgen(1)
.TP 0.2i    
\(bu
The Tools tab of Wsimport Ant Task page http://jax-ws\&.java\&.net/nonav/2\&.1\&.1/docs/wsimportant\&.html
.TP 0.2i    
\(bu
The \fIUsers Guide\fR tab of Catalog Support page http://jax-ws\&.java\&.net/nonav/2\&.1\&.1/docs/catalog-support\&.html
.TP 0.2i    
\(bu
The \fIUsers Guide\fR tab of WSDL Customization page http://jax-ws\&.java\&.net/nonav/2\&.1\&.1/docs/wsimportant\&.html
.RE
.br
'pl 8.5i
'bp
