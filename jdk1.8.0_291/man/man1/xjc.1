'\" t
.\"  Copyright (c) 2005, 2013, Oracle and/or its affiliates. All rights reserved.
.\"     Arch: generic
.\"     Software: JDK 8
.\"     Date: 21 November 2013
.\"     SectDesc: Java Web Services Tools
.\"     Title: xjc.1
.\"
.if n .pl 99999
.TH xjc 1 "21 November 2013" "JDK 8" "Java Web Services Tools"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------

.SH NAME    
xjc \- Compiles an XML schema file into fully annotated Java classes\&.
.SH SYNOPSIS    
.sp     
.nf     

\fBxjc\fR [ \fIoptions\fR ] \fBschema\fR \fIfile/URL/dir/jar\fR \&.\&.\&. [\fB\-b\fR \fIbindinfo\fR ] \&.\&.\&.
.fi     
.sp     
.TP     
\fIoptions\fR
The command-line options\&. See Options\&.
.TP     
schema \fIfile/URL/dir/jar \&.\&.\&.\fR
The location of the XML schema file\&. If \f3dir\fR is specified, then all schema files in it are compiled\&. If \f3jar\fR is specified, then the \f3/META-INF/sun-jaxb\&.episode\fR binding file is compiled\&.
.TP
-b \fIbindinfo\fR
.br
The location of the bindings files\&.
.SH DESCRIPTION    
Start the binding compiler with the appropriate \f3xjc\fR shell script in the bin directory for your platform\&. There is also an Ant task to run the binding complier\&. See Using the XJC with Ant at http://jaxb\&.java\&.net/nonav/2\&.1\&.3/docs/xjcTask\&.html
.SH OPTIONS    
.TP 0.2i    
\(bu
See also Nonstandard Options
.TP 0.2i    
\(bu
See also Deprecated and Removed Options
.TP
-nv
.br
By default, the XJC binding compiler performs strict validation of the source schema before processing it\&. Use this option to disable strict schema validation\&. This does not mean that the binding compiler will not perform any validation, but means that it will perform a less-strict validation\&.
.TP
-extension
.br
By default, the XJC binding compiler strictly enforces the rules outlined in the Compatibility chapter of the JAXB Specification\&. Appendix E\&.2 defines a set of W3C XML Schema features that are not completely supported by JAXB v1\&.0\&. In some cases, you may be allowed to use them in the \f3-extension\fR mode enabled by this switch\&. In the default (strict) mode, you are also limited to using only the binding customization defined in the specification\&. By using the \f3-extension\fR switch, you will be allowed to use the JAXB Vendor Extensions\&.
.TP
-b \fIfile\fR
.br
Specifies one or more external binding files to process\&. Each binding file must have its own \f3-b\fR switch\&. The syntax of the external binding files is flexible\&. You can have a single binding file that contains customization for multiple schemas or you can break the customization into multiple bindings files: \f3xjc schema1\&.xsd schema2\&.xsd schema3\&.xsd -b bindings123\&.xjb\fR\f3xjc schema1\&.xsd schema2\&.xsd schema3\&.xsd -b bindings1\&.xjb -b bindings2\&.xjb -b bindings3\&.xjb\fR\&. In addition, the ordering of the schema files and binding files on the command line does not matter\&.
.TP
-d \fIdir\fR
.br
By default, the XJC binding compiler generates the Java content classes in the current directory\&. Use this option to specify an alternate output directory\&. The output directory must already exist\&. The XJC binding compiler does not create it for you\&.
.TP
-p \fIpkg\fR
.br
When you specify a target package with this command-line option, it overrides any binding customization for the package name and the default package name algorithm defined in the specification\&.
.TP
-httpproxy \fIproxy\fR
.br
Specifies the HTTP or HTTPS proxy in the format \fI[user[:password]@]proxyHost[:proxyPort]\fR\&. The old \f3-host\fR and \f3-port\fR options are still supported by the RI for backward compatibility, but they were deprecated\&. The password specified with this option is an argument that is visible to other users who use the top command\&. For greater security, use the \f3-httpproxyfile\fR option\&.
.TP
-httpproxyfile file
.br
Specifies the HTTP or HTTPS proxy with a file\&. The same format as the \f3-httpproxy\fR option, but the password specified in the file is not visible to other users\&.
.TP
-classpath arg
.br
Specifies where to find client application class files used by the \fIjxb:javaType\fR and xjc:\fIsuperClass\fR customization\&.
.TP
-catalog file
.br
Specifies catalog files to resolve external entity references\&. Supports the TR9401, XCatalog, and OASIS XML Catalog formats\&. See XML Entity and URI Resolvers at http://xerces\&.apache\&.org/xml-commons/components/resolver/resolver-article\&.html
.TP
-readOnly
.br
By default, the XJC binding compiler does not write-protect the Java source files it generates\&. Use this option to force the XJC binding compiler to mark the generated Java sources as read-only\&.
.TP
-npa
.br
Suppresses the generation of package level annotations into \f3**/package-info\&.java\fR\&. Using this switch causes the generated code to internalize those annotations into the other generated classes\&.
.TP
-no-header
.br
Suppresses the generation of a file header comment that includes some note and time stamp\&. Using this makes the generated code more compatible with the \f3diff\fR command\&.
.TP
-target 2\&.0
.br
Avoids generating code that relies on any JAXB 2\&.1 features\&. This will allow the generated code to run with JAXB 2\&.0 runtime environment (such as Java SE 6)\&.
.TP
-xmlschema
.br
Treats input schemas as W3C XML Schema (default)\&. If you do not specify this switch, then your input schemas are treated as though they are W3C XML Schemas\&.
.TP
-relaxing
.br
Treats input schemas as RELAX NG (experimental and unsupported)\&. Support for RELAX NG schemas is provided as a JAXB Vendor Extension\&.
.TP
-relaxing-compact
.br
Treat input schemas as RELAX NG compact syntax (experimental and unsupported)\&. Support for RELAX NG schemas is provided as a JAXB Vendor Extension\&.
.TP
-dtd
.br
Treats input schemas as XML DTD (experimental and unsupported)\&. Support for RELAX NG schemas is provided as a JAXB Vendor Extension\&.
.TP
-wsdl
.br
Treats input as WSDL and compiles schemas inside it (experimental and unsupported)\&.
.TP
-quiet
.br
Suppress compiler output, such as progress information and warnings\&.
.TP
-verbose
.br
Be extra verbose, such as printing informational messages or displaying stack traces upon some errors\&.
.TP
-help
.br
Displays a brief summary of the compiler switches\&.
.TP
-version
.br
Displays the compiler version information\&.
.TP     
\fIschema file/URL/dir\fR
Specifies one or more schema files to compile\&. If you specify a directory, then the \f3xjc\fR command scans it for all schema files and compiles them\&.
.SS NONSTANDARD\ OPTIONS    
.TP
-XLocator
.br
Causes the generated code to expose SAX Locator information about the source XML in the Java bean instances after unmarshalling\&.
.TP
-Xsync-methods
.br
Causes all of the generated method signatures to include the \f3synchronized\fR keyword\&.
.TP
-mark-generated
.br
Marks the generated code with the annotation \f3@javax\&.annotation\&.Generated\fR\&.
.TP
-episode file
.br
Generates the specified episode file for separate compilation\&.
.SS DEPRECATED\ AND\ REMOVED\ OPTIONS    
.TP
-host & -port
.br
These options are replaced with the \f3-httpproxy\fR option\&. For backward compatibility, these options are supported, but will not be documented and might be removed from future releases\&.
.TP
-use-runtime
.br
Because the JAXB 2\&.0 specification has defined a portable runtime environment, it is no longer necessary for the JAXB RI to generate \f3**/impl/runtime\fRpackages\&. Therefore, this switch is obsolete and was removed\&.
.TP
-source
.br
The \f3-source\fR compatibility switch was introduced in the first JAXB 2\&.0 Early Access release\&. This switch is removed from future releases of JAXB 2\&.0\&. If you need to generate 1\&.0\&.x code, then use an installation of the 1\&.0\&.x code base\&.
.SH COMPILER\ RESTRICTIONS    
In general, it is safest to compile all related schemas as a single unit with the same binding compiler switches\&. Keep the following list of restrictions in mind when running the \f3xjc\fR command\&. Most of these issues only apply when you compile multiple schemas with multiple invocations of the \f3xjc\fR command\&.
.PP
To compile multiple schemas at the same time, keep the following precedence rules for the target Java package name in mind:
.TP 0.4i    
1\&.
The \f3-p\fR option has the highest precedence\&.
.TP 0.4i    
2\&.
\fIjaxb:package\fR customization\&.
.TP 0.4i    
3\&.
If \f3targetNamespace\fR is declared, then apply the \f3t\fR\f3argetNamespace\fR to the Java package name algorithm defined in the specification\&.
.TP 0.4i    
4\&.
If no \f3targetNamespace\fR is declared, then use a hard coded package named \f3generated\fR\&.
.PP
You cannot have more than one \fIjaxb:schemaBindings\fR per name space, so it is impossible to have two schemas in the same target name space compiled into different Java packages\&.
.PP
All schemas being compiled into the same Java package must be submitted to the XJC binding compiler at the same time\&. They cannot be compiled independently and work as expected\&.
.PP
Element substitution groups that are spread across multiple schema files must be compiled at the same time\&.
.SH SEE\ ALSO    
.TP 0.2i    
\(bu
Binding Compiler (xjc) at http://jaxb\&.java\&.net/nonav/2\&.2\&.3u1/docs/xjc\&.html
.TP 0.2i    
\(bu
Java Architecture for XML Binding (JAXB) at http://www\&.oracle\&.com/technetwork/articles/javase/index-140168\&.html
.RE
.br
'pl 8.5i
'bp
