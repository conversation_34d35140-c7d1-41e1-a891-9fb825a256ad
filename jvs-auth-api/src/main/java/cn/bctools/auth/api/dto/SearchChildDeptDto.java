package cn.bctools.auth.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class SearchChildDeptDto {

    @ApiModelProperty("返回下级机构/返回当前机构及以下(开启时返回单层下级机构,关闭时返回当前机构及以下多层)")
    public Boolean flag;

    @ApiModelProperty("只返回id数组")
    public Boolean onlyId;

    @ApiModelProperty("是否包含同级同区域部门")
    public Boolean containSameRegion;

}