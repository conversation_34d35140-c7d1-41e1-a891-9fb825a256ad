package cn.bctools.auth.entity;

import cn.bctools.auth.entity.enums.PortalCardComponentEnum;
import cn.bctools.auth.entity.enums.PortalCardCustomTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户门户卡片表
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "sys_portal_card", autoResultMap = true)
public class PortalCard implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "卡片名称")
    @NotNull(message = "卡片名称不能为空")
    private String name;

    @ApiModelProperty(value = "卡片类型")
    @NotNull(message = "卡片类型不能为空")
    private PortalCardComponentEnum component;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "权限数组")
    @NotNull(message = "卡片权限不能为空")
    @TableField(exist = false)
    private List<String> permissions;
    @ApiModelProperty(value = "权限数组", notes = "用于存储数据库")
    @TableField("permissions")
    private String permissionField;

    @ApiModelProperty(value = "卡片状态", notes = "0--上架  1--下架")
    private Integer state;

    @ApiModelProperty(value = "卡片排序")
    private Integer sort;

    /**
     * 当component为custom时，传递
     */
    @ApiModelProperty(value = "自定义卡片类型")
    private PortalCardCustomTypeEnum customType;

    /**
     * 当component为custom、customType为list时，传递
     */
    @ApiModelProperty(value = "列表卡片url")
    private String listApi;

    @ApiModelProperty(value = "列表卡片每页数据条数")
    private Integer listPageSize;

    @ApiModelProperty(value = "列表卡片是否可以翻页")
    private Boolean listCanPaginate;

    /**
     * 当component为custom、customType为application时，传递
     */
    @ApiModelProperty(value = "应用卡片信息")
    @TableField(exist = false)
    private List<ApplicationInfo> applicationInfo;
    @ApiModelProperty(value = "应用卡片信息", notes = "用于存储数据库")
    @TableField("application_info")
    private String applicationInfoField;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

}
