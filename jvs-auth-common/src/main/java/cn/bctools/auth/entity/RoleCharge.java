package cn.bctools.auth.entity;

import cn.bctools.database.handler.Fastjson2TypeHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName(value = "sys_role_charge", autoResultMap = true)
public class RoleCharge implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键")
    private String id;
    @NotEmpty(message = "应用ID不能为空")
    @ApiModelProperty(value = "应用ID")
    private String appId;
    @NotEmpty(message = "角色ID不能为空")
    @ApiModelProperty(value = "角色ID")
    private String roleId;
    @ApiModelProperty(value = "管理角色ID")
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<String> inChargeRoleIds;
}
