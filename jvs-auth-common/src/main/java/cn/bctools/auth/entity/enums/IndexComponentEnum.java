package cn.bctools.auth.entity.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 如果有启动低代码，表示可以加载所有组件类型， 如果没有启动低代码，只能加载基础组件类型
 */
public enum IndexComponentEnum {
    卡片(false, true),
    图片(true, true),
    数字(false, true),
    列表页(false, true),
    公告(true, false),
    日历(true, false),
    项目导航(true, false),
    模块导航(false, true),
    消息通知(true, false),
    在线客服(true, false),
    流程申请(false, false),
    待办任务(false, false),
    分析占比图(false, true),
    折线趋势图(false, true),
    百分比趋势图(false, true);


    /**
     * 是否是基础组件,如果是基础组件，可以在没有低代码的情况下添加和设计模块的排版布局
     */
    public boolean base = false;
    /**
     * 是否是支持零活配置化的组件
     */
    public boolean configuration = false;

    IndexComponentEnum() {

    }

    IndexComponentEnum(boolean base, boolean configuration) {
        this.configuration = configuration;
        this.base = base;
    }

    /**
     * 基础组件
     */
    public static List<IndexComponentEnum> BASE_COMPONENT = Arrays.stream(IndexComponentEnum.values()).filter(e -> e.base).collect(Collectors.toList());

}
