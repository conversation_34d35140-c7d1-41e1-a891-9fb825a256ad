package cn.bctools.auth.entity.vo;

import cn.bctools.auth.entity.Dept;
import cn.bctools.common.utils.BeanCopyUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * 部门查询实体类
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@Builder
public class DeptSearchVo
{
    //基础必须
    private String keyword;
    private String deptId;
    private Integer deptType;
    private Integer status;
    private String typeDict;
    private String tagDict;
    private String regionCode;
    private Dept dept;

    //新增扩展
    private Boolean permitAll;
    //
    private Boolean inner;

    //适配DeptPageReqVo初始化
    public static DeptSearchVo init(Object object) {
        DeptSearchVo copy = BeanCopyUtil.copy(object, DeptSearchVo.class);
        return new DeptSearchVoBuilder()
                .keyword(copy.getKeyword())
                .deptId(copy.getDeptId())
                .deptType(copy.getDeptType())
                .status(copy.getStatus())
                .typeDict(copy.getTypeDict())
                .tagDict(copy.getTagDict())
                .regionCode(copy.getRegionCode())
                .dept(copy.getDept())
                .permitAll(copy.getPermitAll())
                .inner(copy.getInner())
                .build();
    }
}