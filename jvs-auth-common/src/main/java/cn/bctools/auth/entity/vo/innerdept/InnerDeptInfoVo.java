package cn.bctools.auth.entity.vo.innerdept;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * 院内部门信息新增/修改实体类
 */
@ApiModel("院内部门信息新增/修改实体类")
@Data
@Accessors(chain = true)
public class InnerDeptInfoVo {
    @ApiModelProperty(value = "院内部门id")
    private String id;

    @ApiModelProperty(value = "部门名称", required = true)
    @NotEmpty(message = "部门名称不能为空")
    private String name;

    @ApiModelProperty(value = "负责人id")
    private String leaderId;

    @ApiModelProperty(value = "所属机构", required = true)
    @NotEmpty(message = "所属机构不能为空")
    private String parentId;
}
