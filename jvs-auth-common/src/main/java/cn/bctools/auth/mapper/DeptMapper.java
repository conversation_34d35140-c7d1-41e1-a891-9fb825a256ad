package cn.bctools.auth.mapper;

import cn.bctools.auth.entity.Dept;
import cn.bctools.auth.entity.vo.innerdept.InnerDeptVo;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 部门基础
 *
 * <AUTHOR>
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)

public interface DeptMapper extends BaseMapper<Dept> {

    // 机构负责人Id
    String AGENCY_HEAD_ID = "4a002ee274649f2582ecbe3ab6d0bc15";

    /**
     * 根据删除状态, 和数据
     *
     * @param delFlag 删除状态
     * @param source  这个部门的数据来源,可能是同步fpgor数据
     * @param deptIds 部门数组
     */
    @Update("UPDATE sys_dept SET del_flag = ${delFlag} WHERE id in ${deptIds} AND source = '${source}'")
    void updateDelFlag(@Param("delFlag") Boolean delFlag, @Param("source") String source, @Param("deptIds") String deptIds);

    @Select("SELECT sd.id FROM sys_dept sd WHERE sd.name = #{name}")
    String getParentIdByName(@Param("name") String parentName);

    @Select("SELECT srd.item_value FROM sys_region_dict srd WHERE srd.item_name = #{name}")
    String getItemValueByName(@Param("name") String name);

    @Select("SELECT * FROM sys_dept sd ${ew.isEmptyOfNormal() ? '' : 'WHERE ' + ew.getSqlSegment() }  ORDER BY sd.update_time asc")
    IPage<Dept> syncDeptPage(IPage<Dept> page,@Param("ew") QueryWrapper queryWrapper);

    @Select("SELECT sd.*,su.real_name as leaderName FROM sys_dept sd " +
            "left join sys_user_info su on sd.leader_id = su.id " +
            "where ${ew.sqlSegment}")
    IPage<InnerDeptVo> innerDeptPage(IPage<InnerDeptVo> page, @Param("ew") QueryWrapper<InnerDeptVo> queryWrapper);

    /**
     * 查询部门信息，包括删除的部门
     * @param queryWrapper 查询条件
     * @return 部门信息列表
     */
    @Select("SELECT * FROM sys_dept " +
            "${ew.isEmptyOfNormal() ? '' : 'WHERE ' + ew.getSqlSegment() }")
    List<Dept> getDelDept(@Param("ew") QueryWrapper<Dept> queryWrapper);
}
