package cn.bctools.auth.mapper;

import cn.bctools.auth.entity.Opinion;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.CacheNamespace;

/**
 * <AUTHOR>
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
public interface OpinionMapper extends BaseMapper<Opinion> {

}
