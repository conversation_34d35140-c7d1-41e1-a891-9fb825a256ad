package cn.bctools.auth.service;

import cn.bctools.auth.entity.Dept;
import cn.bctools.auth.entity.ImportFeedback;
import cn.bctools.auth.entity.vo.DeptSearchVo;
import cn.bctools.auth.entity.vo.innerdept.InnerDeptVo;
import cn.bctools.common.entity.dto.UserDto;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
public interface DeptService extends IService<Dept> {

    /**
     * 移除指定用户信息
     * <p>
     * 仅处理部门负责人信息
     *
     * @param userId 用户id
     */
    void clearUser(@NotNull String userId);

    /**
     * 校验部门id
     *
     * @param deptId 部门id
     * @return 部门信息
     */
    Dept checkId(String deptId);

    /**
     * 获取指定部门的所有子部门id(不包括当前部门)
     *
     * @param deptId 部门id
     * @return 子部门id集合
     */
    List<String> getAllChildId(String deptId);

    List<Dept> getAllChild(String deptId);

    /**
     * 拉取三方系统组织架构
     *
     * @param userDto
     * @param remoteDepts 三方系统组织架构
     */
    void pull(UserDto userDto, List<Dept> remoteDepts);

    /**
     * 根据行政区域获取部门下拉列表数据-关联当前用户部门和角色
     *
     * @param itemValue 行政区域字典值
     * @return {@link List}<{@link Dept}>
     */
    List<Dept> getByRegionDict(String itemValue);

    /**
     * 根据区划获取当前机构id
     *
     * @return
     */
    String getMaxDeptId(String shCode, String dsCode, String xqCode, String jdCode);

    Integer getDeptType(String typeDict, String deptId, Integer level);

    Integer getLevel(Dept dept);

    void replenishDept(Dept dept);

    void exportDeptExcel(List<Dept> list, HttpServletResponse response);

    LambdaQueryWrapper<Dept> buildWrapper(DeptSearchVo vo);

    ImportFeedback importDept(MultipartFile file);

    String getParentIdByName(String parentName);

    String getItemValueByName(String shName);

    void downloadExcelTemplate(HttpServletResponse response);

    void syncRegionCode(Dept dept,Boolean generateRegionName);

    List<String> getChildIdsByRegionCode(String deptId);

    List<Dept> getAllChilds(String deptId);

    List<String> getAllParentIds(String deptId,Boolean hasPeer);

    List<String> getInnerParentIds(String deptId);

    String getShowPath(String deptId);

    IPage<Dept> syncDeptPage(Page<Dept> page, QueryWrapper queryWrapper);

    Dept getInfo(String id);

    Dept getDept(String id);

    IPage<InnerDeptVo> innerDeptPage(Page<InnerDeptVo> page, QueryWrapper<InnerDeptVo> queryWrapper);
}
