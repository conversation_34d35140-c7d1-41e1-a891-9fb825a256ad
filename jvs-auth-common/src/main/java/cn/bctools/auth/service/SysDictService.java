package cn.bctools.auth.service;

import cn.bctools.auth.entity.SysDict;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * <p>
 * 字典表 服务类
 * </p>
 *
 * <AUTHOR>
 */
public interface SysDictService extends IService<SysDict> {

    /**
     * 保存字典
     *
     * @param sysDict 字典信息
     * @return 保存后的字典id
     */
    String saveDict(SysDict sysDict);

    /**
     * 根据id删除字典
     *
     * @param id 字典id
     */
    void removeDict(String id);

    /**
     * 更新字典
     *
     * @param sysDict 字典信息
     */
    void updateDict(SysDict sysDict);

    /**
     * 根据类型查询低代码字典
     *
     * @param type 字典类型
     * @return 字典列表
     */
    Map<String, Object> getByDesignTreeType(String type,Integer level);

}
