package cn.bctools.auth.service.impl;

import cn.bctools.auth.component.SysDeptComponent;
import cn.bctools.auth.constants.DeptTypeConstant;
import cn.bctools.auth.entity.Dept;
import cn.bctools.auth.entity.User;
import cn.bctools.auth.entity.UserTenant;
import cn.bctools.auth.entity.vo.innerdept.*;
import cn.bctools.auth.service.DeptService;
import cn.bctools.auth.service.UserService;
import cn.bctools.auth.service.UserTenantService;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 院内部门服务实现类
 */
@Service
@AllArgsConstructor
public class InnerDeptService {

    private static final String INNER_CODE_PREFIX = "4498";

    private final DeptService deptService;

    private final UserTenantService userTenantService;

    private final UserService userService;

//    private final SysDeptComponent deptComponent;

    public void page(Page<InnerDeptVo> page, InnerDeptPageVo pageVo) {
        QueryWrapper<InnerDeptVo> q = new QueryWrapper<InnerDeptVo>()
//                .in("sd.parent_id", deptComponent.getOrganizationChilds(UserCurrentUtils.getDeptId()))
                //只返回当前部门管理的院内部门
                .eq("sd.parent_id",UserCurrentUtils.getDeptId())
                .eq("sd.dept_type", DeptTypeConstant.INNER)
                .eq("sd.del_flag",Boolean.FALSE)
                .and(StrUtil.isNotEmpty(pageVo.getKeyword()),
                        e -> e.like("sd.name", pageVo.getKeyword())
                                .or().like("su.real_name", pageVo.getKeyword()))
                .orderByAsc("sd.sh_code", "sd.ds_code", "sd.xq_code", "sd.jd_code", "sd.code");
        deptService.innerDeptPage(page, q);
        if (page.getTotal() == 0) {
            return;
        }
        String parentName = UserCurrentUtils.getDeptName();

        page.getRecords()
                .forEach(e -> e.setParentName(parentName));
    }

    public void save(InnerDeptInfoVo dept) {
        //院内部门默认前缀4498 === 生成id时传递dsCode=4489，但保存的dsCode不变
        Dept parent = deptService.getDept(dept.getParentId());
        Dept inner = BeanCopyUtil.copy(dept, Dept.class);
        inner.handleRegionInfo(parent);
        inner.setLevel(parent.getLevel() + 1);
        inner.setDeptType(DeptTypeConstant.INNER);
        String id = deptService.getMaxDeptId(inner.getShCode(), INNER_CODE_PREFIX, inner.getXqCode(), inner.getJdCode());
        inner.setId(id);
        inner.setDeptCode(id);
        deptService.save(inner);
    }

    public void update(InnerDeptInfoVo dept) {
        Dept parent = deptService.getDept(dept.getParentId());
        Dept inner = deptService.getDept(dept.getId());
        BeanUtil.copyProperties(dept, inner);
        inner.handleRegionInfo(parent);
        inner.setLevel(parent.getLevel() + 1);
        if (!Objects.equals(dept.getParentId(), inner.getParentId())) {
            String id = deptService.getMaxDeptId(inner.getShCode(), INNER_CODE_PREFIX, inner.getXqCode(), inner.getJdCode());
            inner.setDeptCode(id);
        }
        deptService.updateById(inner);
    }


    public void addMember(String deptId, List<String> userIds) {
        Dept inner = deptService.getDept(deptId);
        List<UserTenant> deptList = userTenantService.lambdaQuery().in(UserTenant::getUserId, userIds)
                .select(UserTenant::getDeptId).list();
        // 用户是否都在父级机构下
        boolean check = deptList.stream().allMatch(e -> StrUtil.equals(e.getDeptId(), inner.getParentId()));
        if (!check) {
            throw new BusinessException("所选择成员必须在院内部门所属机构下");
        }
        userTenantService.lambdaUpdate().in(UserTenant::getUserId, userIds)
                .set(UserTenant::getInnerDeptId, deptId)
                .update();
    }

    public void removeMember(String deptId, List<String> userIds) {
        userTenantService.lambdaUpdate().in(UserTenant::getUserId, userIds)
                .eq(UserTenant::getInnerDeptId, deptId)
                .set(UserTenant::getInnerDeptId, null)
                .update();
    }

    public void memberPage(Page<InnerDeptMemberVo> page, InnerDeptMemberPageVo pageVo) {
        Page<UserTenant> poPage = new Page<>(page.getCurrent(), page.getSize());
        userTenantService.lambdaQuery()
                .eq(UserTenant::getInnerDeptId, pageVo.getDeptId())
                .select(UserTenant::getUserId, UserTenant::getRealName)
                .page(poPage);
        if (poPage.getTotal() == 0) {
            return;
        }
        List<String> userIds = poPage.getRecords().stream().map(UserTenant::getUserId).collect(Collectors.toList());
        Map<String, User> nameMap = userService.lambdaQuery()
                .in(User::getId, userIds)
                .select(User::getId, User::getRealName,User::getAccountName)
                .list()
                .stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> b));
        page.setTotal(poPage.getTotal());
        page.setRecords(BeanCopyUtil.copys(poPage.getRecords(), InnerDeptMemberVo.class));
        page.getRecords().forEach(e ->{
            User user = nameMap.get(e.getUserId());
            if (ObjectNull.isNotNull(user)) {
                e.setRealName(user.getRealName());
                e.setAccountName(user.getAccountName());
            }
        });
    }
}