package cn.bctools.auth.service.impl;

import cn.bctools.auth.entity.Role;
import cn.bctools.auth.entity.RoleCharge;
import cn.bctools.auth.entity.RolePermission;
import cn.bctools.auth.entity.vo.RoleChargeVo;
import cn.bctools.auth.mapper.RoleChargeMapper;
import cn.bctools.auth.service.RoleChargeService;
import cn.bctools.auth.service.RolePermissionService;
import cn.bctools.auth.service.RoleService;
import cn.bctools.common.utils.ObjectNull;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class RoleChargeServiceImpl extends ServiceImpl<RoleChargeMapper, RoleCharge> implements RoleChargeService {
    RoleService roleService;
    RolePermissionService rolePermissionService;

    @Override
    public RoleChargeVo showRelation(String appId) {
        List<RoleCharge> roleCharges = list(Wrappers.lambdaQuery(RoleCharge.class).eq(RoleCharge::getAppId, appId));
        RoleChargeVo roleChargeVo = new RoleChargeVo();
        if (ObjectNull.isNotNull(roleCharges)) {
            List<String> roleIds = roleCharges.stream().map(RoleCharge::getRoleId).collect(Collectors.toList());
            List<Role> roles = roleService.list(Wrappers.lambdaQuery(Role.class)
                    .in(Role::getId, roleIds).select(Role::getId, Role::getRoleName, Role::getRoleDesc));
            roleChargeVo.setRows(roles);
            roleChargeVo.setCols(roles);
            roleChargeVo.setRoles(roleCharges);
        }
        return roleChargeVo;
    }

    @Override
    public void saveRelation(RoleChargeVo roleChargeVo) {
        if (ObjectNull.isNull(roleChargeVo.getRoles())) {
            return;
        }

        String appId = roleChargeVo.getRoles().get(0).getAppId();
        List<RoleCharge> roles = roleChargeVo.getRoles().stream()
                .filter(role -> ObjectNull.isNotNull(role.getRoleId()) &&
                        roleService.getById(role.getRoleId()) != null)
                .collect(Collectors.toList());

        List<String> rolePermissions = Arrays.asList("116", "117", "289", "290");
        List<RolePermission> newRolePermissions = new ArrayList<>();

        // 查询需要删除的角色
        List<String> deleteIds = roles.stream().map(RoleCharge::getId).collect(Collectors.toList());
        List<RoleCharge> removeRoleCharges = list(Wrappers.lambdaQuery(RoleCharge.class)
                .eq(RoleCharge::getAppId, appId)
                .notIn(RoleCharge::getId,deleteIds));

        // 删除角色及对应的权限
        remove(Wrappers.lambdaQuery(RoleCharge.class)
                .eq(RoleCharge::getAppId, appId)
                .notIn(RoleCharge::getId,deleteIds));

        // 更新权限
        for (RoleCharge removeRoleCharge : removeRoleCharges) {
            List<RoleCharge> otherRoleCharges = list(Wrappers.lambdaQuery(RoleCharge.class)
                    .eq(RoleCharge::getRoleId, removeRoleCharge.getRoleId())
                    .ne(RoleCharge::getAppId, appId));
            boolean flag = otherRoleCharges.stream()
                    .anyMatch(roleCharge -> ObjectNull.isNotNull(roleCharge.getInChargeRoleIds()));
            if (!flag) {
                // 其他应用没有设置该角色的管理范围，则删除他的上述权限
                rolePermissionService.remove(new LambdaQueryWrapper<RolePermission>()
                        .eq(RolePermission::getRoleId, removeRoleCharge.getRoleId())
                        .in(RolePermission::getPermissionId, rolePermissions));
            }
        }

        // 新增权限
        for (RoleCharge role : roles) {
            if (ObjectNull.isNull(role.getInChargeRoleIds())) {
                List<RoleCharge> notCur = list(Wrappers.lambdaQuery(RoleCharge.class)
                        .ne(RoleCharge::getAppId, appId)
                        .eq(RoleCharge::getRoleId, role.getRoleId()));
                boolean flag = notCur.stream().anyMatch(z -> ObjectNull.isNotNull(z.getInChargeRoleIds()));
                if (!flag) {
                    // 其他应用没有设置该角色的管理范围，则删除他的上述权限
                    rolePermissionService.remove(new LambdaQueryWrapper<RolePermission>()
                            .eq(RolePermission::getRoleId, role.getRoleId())
                            .in(RolePermission::getPermissionId, rolePermissions));
                }
            } else {
                // 判断角色有没有这些权限，没有则添加
                for (String permission : rolePermissions) {
                    long count = rolePermissionService.count(new LambdaQueryWrapper<RolePermission>()
                            .eq(RolePermission::getPermissionId, permission)
                            .eq(RolePermission::getRoleId, role.getRoleId()));
                    if (count == 0) {
                        newRolePermissions.add(new RolePermission()
                                .setPermissionId(permission)
                                .setRoleId(role.getRoleId()));
                    }
                }
            }
        }

        // 保存新权限
        rolePermissionService.saveBatch(newRolePermissions);
        // 批量保存角色
        saveOrUpdateBatch(roles);
    }

}


