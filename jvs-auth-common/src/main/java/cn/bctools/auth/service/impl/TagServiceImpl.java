package cn.bctools.auth.service.impl;

import cn.bctools.auth.entity.Dept;
import cn.bctools.auth.entity.DeptTag;
import cn.bctools.auth.entity.Tag;
import cn.bctools.auth.mapper.DeptTagMapper;
import cn.bctools.auth.mapper.TagMapper;
import cn.bctools.auth.service.DeptTagService;
import cn.bctools.auth.service.TagService;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class TagServiceImpl extends ServiceImpl<TagMapper, Tag> implements TagService {

    TagMapper tagMapper;

    @Override
    public void saveTag(Tag tag) {
        // 确保标签名称唯一
        int uniqueNameCount = (int) this.count(new LambdaQueryWrapper<Tag>().eq(Tag::getName, tag.getName()));
        if (uniqueNameCount > 0) {
            throw new BusinessException("系统已有此标签");
        }
        int maxValue = tagMapper.getMaxValue();
        if (ObjectNull.isNotNull(maxValue)) {
            tag.setSort(maxValue + 1);
        }
        tag.setStatus(1);
        tag.setCreateTime(LocalDateTime.now());
        this.save(tag);
    }
}
