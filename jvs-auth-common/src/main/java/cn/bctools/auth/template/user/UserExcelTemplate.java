package cn.bctools.auth.template.user;

import cn.bctools.auth.entity.enums.SexTypeEnum;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: Zhu<PERSON>iaoKang
 * @Description: 导入用户excel模板
 */
@Data
@EqualsAndHashCode
@ExcelIgnoreUnannotated
public class UserExcelTemplate {

    @ExcelIgnore
    private Integer rowIndex;

    @ExcelIgnore
    private int successNum;
    @ExcelIgnore
    private int failNum;

    @ExcelProperty(value = "*姓名")
    private String realName;
    @ExcelProperty(value = "*账号")
    private String accountName;
    @ExcelProperty(value = "*单位名称")
    String deptName;
    @ExcelProperty(value = "*手机号")
    private String phone;
    @ExcelProperty(value = "学段学科")
    private String perAndSubs;
    @ExcelProperty(value = "密码")
    private String password;
    @ExcelProperty(value = "性别")
    private String gender;
    @ExcelProperty(value = "电子邮箱")
    private String email;
    @ExcelProperty(value = "导入反馈")
    String feedback;

    @ExcelIgnore
    String jobName;
    @ExcelIgnore
    String level;
    @ExcelIgnore
    String employeeNo;
    @ExcelIgnore
    String status;

    @ExcelIgnore
    String deptId;
    @ExcelIgnore
    String jobId;
    @ExcelIgnore
    SexTypeEnum sex;
}
