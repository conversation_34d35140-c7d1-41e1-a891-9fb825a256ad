package cn.bctools.auth.controller;


import cn.bctools.auth.config.JvsSystemConfig;
import cn.bctools.auth.entity.po.DynamicResource;
import cn.bctools.auth.service.OauthOtherService;
import cn.bctools.auth.service.SysConfigsService;
import cn.bctools.common.constant.SysConstant;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.enums.ConfigsTypeEnum;
import cn.bctools.common.enums.SysApplyConfig;
import cn.bctools.common.enums.SysConfigBase;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import cn.bctools.common.utils.TenantContextHolder;
import cn.bctools.log.annotation.Log;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.lang.Dict;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Api(value = "动态服务资源", tags = "动态服务资源")
@RestController
@RequestMapping("/dynamicResource")
public class DynamicResourceController {

    DiscoveryClient discoveryClient;
    SysConfigsService configService;
    OauthOtherService oauthOtherService;
    JvsSystemConfig jvsSystemConfig;

    @Log
    @ApiOperation(value = "是否有低代码资源", notes = "是否有低代码资源,服务不存在,则请求地址就不操作")
    @GetMapping("/design")
    public R design() {
        boolean data = discoveryClient.getInstances(ConfigsTypeEnum.JVS_DESIGN_MGR.name().toLowerCase().replace("_", "-")).size() > 0;
        return R.ok(Dict.create().set(ConfigsTypeEnum.JVS_DESIGN_MGR.name(), data));
    }

    @Log(back = false)
    @ApiOperation(value = "获取服务资源", notes = "后台判断那些服务资源有启动， 根据启动的服务返回资源")
    @GetMapping
    public R<List> index(@RequestParam(value = "client_id", defaultValue = "frame") String clientId) {
        UserDto currentUser = UserCurrentUtils.getCurrentUser();
        String tenantId = TenantContextHolder.getTenantId();

        //判断是是多租户(域名),还是单租户Ip(模式)
        //根据配置获取相关其它服务资源的功能
        List<DynamicResource> service = jvsSystemConfig.getService().stream().filter(e -> !e.getName().clientId.equals(clientId)).map(e -> {
            String shortName = "";
            //如果是多租户模式,则根据租户信息,获取对应的配置信息
            if (jvsSystemConfig.getMultiTenantMode()) {
                TenantContextHolder.setTenantId(tenantId);
                SysApplyConfig config = configService.getConfig(e.getName());
                //如果子租户没有此配置，使用主租户信息
                if (ObjectNull.isNotNull(config)) {
                    shortName = config.getDomainName() + ".";
                } else if (!currentUser.getTenantId().equals("1")) {
                    //查询如果是顶级租户，就获取顶级租户的信息
                    TenantContextHolder.setTenantId("1");
                    config = configService.getConfig(e.getName());
                    shortName = config.getDomainName() + ".";
                }
            }
            return new DynamicResource().setClientId(e.getName().clientId).setType(e.getName()).setName(e.getName().msg).setUrl("http://" + shortName + jvsSystemConfig.getDomain() + (jvsSystemConfig.getMultiTenantMode() ? "" : (":" + e.getPort()))).setDesc(e.getName().desc).setIconUrl(e.getName().iconUrl);
        }).collect(Collectors.toList());
        //加载其它的配置的三方服务模块
        oauthOtherService.list().stream().forEach(e -> {
            try {
                URI uri = new URI(e.getAuthorize());
                DynamicResource dynamicResource = new DynamicResource().setClientId(e.getName()).setName(e.getName()).setUrl(uri.getScheme() + "://" + uri.getHost()).setIconUrl(e.getIconUrl());
                service.add(dynamicResource);
            } catch (URISyntaxException ex) {
            }
        });
        //记录重定向地址由后端处理需要记录
        service.forEach(e -> {
            try {
                e.setUrl("/auth/token/" + e.getClientId() + "?redirect_url=" + URLEncoder.encode(e.getUrl(), "utf-8"));
            } catch (UnsupportedEncodingException ex) {

            }
        });
        return R.ok(service);
    }

    @Log
    @ApiOperation(value = "判断此配置是否存在", notes = "判断配置是否存在,不存在 ,则不显示界面")
    @GetMapping("/check/{type}")
    @Cacheable(value = SysConstant.CACHE_SYS_CONFIG)
    public R<SysConfigBase> check(@PathVariable ConfigsTypeEnum type) {
        SysConfigBase config = configService.getConfig(type);
        return R.ok(config);
    }

}
