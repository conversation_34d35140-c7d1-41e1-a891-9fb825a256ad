package cn.bctools.auth.controller;

import cn.bctools.auth.entity.RoleCharge;
import cn.bctools.auth.entity.vo.RoleChargeVo;
import cn.bctools.auth.service.RoleChargeService;
import cn.bctools.common.utils.R;
import cn.bctools.log.annotation.Log;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "应用角色范围")
@RestController
@RequestMapping("/role/charge")
@AllArgsConstructor
public class RoleChargeController {
    RoleChargeService roleChargeService;

    @Log
    @ApiOperation("获取应用角色范围")
    @GetMapping("/relation/{appId}")
    public R<RoleChargeVo> showRelation(@PathVariable String appId) {
        RoleChargeVo roleChargeVo = roleChargeService.showRelation(appId);
        return R.ok(roleChargeVo);
    }


    @Log
    @ApiOperation("保存应用角色范围")
    @PostMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public R<List<RoleCharge>> saveRelation(@RequestBody RoleChargeVo roleChargeVo) {
        roleChargeService.saveRelation(roleChargeVo);
        return R.ok();
    }
}
