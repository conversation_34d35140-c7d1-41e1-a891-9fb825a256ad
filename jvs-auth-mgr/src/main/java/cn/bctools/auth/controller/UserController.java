package cn.bctools.auth.controller;

import cn.bctools.auth.api.dto.UserRangTypeDto;
import cn.bctools.auth.api.dto.SearchDto;
import cn.bctools.auth.api.dto.UserSelectedDto;
import cn.bctools.auth.component.SmsEmailComponent;
import cn.bctools.auth.component.UserRoleComponent;
import cn.bctools.auth.config.JvsSystemConfig;
import cn.bctools.auth.entity.*;
import cn.bctools.auth.entity.po.TenantUserData;
import cn.bctools.auth.feign.SelectedApiImpl;
import cn.bctools.auth.mapper.UserInviteMapper;
import cn.bctools.auth.mapper.UserTenantMapper;
import cn.bctools.auth.service.*;
import cn.bctools.auth.util.AvatarUtils;
import cn.bctools.auth.vo.*;
import cn.bctools.common.constant.SysConstant;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.enums.ConfigsTypeEnum;
import cn.bctools.common.enums.SysFrameApplyConfig;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.*;
import cn.bctools.common.utils.function.Get;
import cn.bctools.gateway.entity.TenantPo;
import cn.bctools.log.annotation.Log;
import cn.bctools.oauth2.config.JvsOAuth2AuthorizationServiceImpl;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.oss.dto.BaseFile;
import cn.bctools.oss.template.OssTemplate;
import cn.bctools.redis.utils.RedisUtils;
import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.FIFOCache;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.util.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 用户管理
 */
@Slf4j
@RestController
@RequestMapping("/user")
@AllArgsConstructor
@Api(tags = "用户管理")
public class UserController {

    public static final int QR_EXPIRED_MIN = 30;
    public static final String EMAIL_PATTERN = "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@" + "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$";
    public static final String ACCOUNT_REGEX = "^[a-zA-Z\\d][a-zA-Z\\d_]{0,17}[a-zA-Z\\d]$";
    //    public static final String REAL_NAME_REGEX = "^[\\u4e00-\\u9fa5A-Za-z]{1,50}$";
//    public static final String REAL_NAME_INTERVALS_REGEX = "^(?![·])(?!.*[·]{2})[\\u4e00-\\u9fa5·]{1,50}(?<![·])$";
    public static final String REAL_NAME_REGEX = "^[\\u4e00-\\u9fa5A-Za-z][\\u4e00-\\u9fa5A-Za-z0-9]{1,49}$";
    public static final String REAL_NAME_INTERVALS_REGEX = "^[\\u4e00-\\u9fa5A-Za-z][\\u4e00-\\u9fa5A-Za-z\\d·]{0,49}$";


    RedisUtils redisUtils;
    SysConfigsService configsService;
    RedisTemplate<String, Object> redisTemplate;
    SmsEmailComponent smsComponent;
    JobService jobService;
    UserService userService;
    PasswordEncoder passwordEncoder;
    SelectedApiImpl selectedApi;
    DeptService deptService;
    OssTemplate ossTemplate;
    TenantService tenantService;
    UserTenantService userTenantService;
    UserRoleComponent userRoleComponent;
    UserRoleService userRoleService;
    UserExtensionService userExtensionService;
    UserGroupService userGroupService;
    UserInviteMapper userInviteMapper;
    UserLevelService userLevelService;
    JvsSystemConfig jvsSystemConfig;
    LoginLogService loginLogService;
    JvsOAuth2AuthorizationServiceImpl oAuth2AuthorizationService;
    UserPeriodSubjectService userPeriodSubjectService;

    static FIFOCache<String, String> PASS_CACHE = CacheUtil.newFIFOCache(100, 1000 * 60 * 60);

//    @Log(back = false)
//    @ApiOperation("所有用户的列表")
//    @GetMapping("/all")
//    public R<List<UserVo>> users() {
//        // 查询用户租户信息 此接口现在暂时没用，只有项目管理在用
//        List<UserTenant> userTenants = userTenantService.list(new LambdaQueryWrapper<UserTenant>().last(" limit 1000"));
//        if (ObjectUtil.isEmpty(userTenants)) {
//            return R.ok(Collections.emptyList());
//        }
//        List<String> ids = userTenants.stream().map(e -> e.getUserId()).collect(Collectors.toList());
//        // 查询用户基本信息
//        Map<String, User> userMap = userService.list(new LambdaQueryWrapper<User>().in(User::getId, ids))
//                .stream().collect(Collectors.toMap(User::getId, Function.identity()));
//        // User与UserTenant都有cancel_flag字段, 需要以UserTenant的为准, 需要注意这里copy的顺序
//        List<UserVo> list = userTenants.stream().map(e -> BeanCopyUtil.copy(UserVo.class, userMap.get(e.getUserId()), e).setId(e.getUserId())).collect(Collectors.toList());
//        return R.ok(list);
//    }

    @Log(back = false)
    @ApiOperation("所有用户的列表")
    @PostMapping("/all")
    public R<List<UserVo>> users(@RequestBody List<String> ids) {
        //传递参数为空
        if (ObjectNull.isNull(ids)) {
            return R.ok();
        }
        // 查询用户租户信息
        List<UserTenant> userTenants = userTenantService.list(new LambdaQueryWrapper<UserTenant>().in(UserTenant::getUserId, ids));
        if (ObjectUtil.isEmpty(userTenants)) {
            return R.ok(Collections.emptyList());
        }
        // 查询用户基本信息
        Map<String, User> userMap = userService.list(new LambdaQueryWrapper<User>().in(User::getId, ids)).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        // User与UserTenant都有cancel_flag字段, 需要以UserTenant的为准, 需要注意这里copy的顺序
        List<UserVo> list = userTenants.stream().map(e -> BeanCopyUtil.copy(UserVo.class, userMap.get(e.getUserId()), e).setId(e.getUserId())).collect(Collectors.toList());
        return R.ok(list);
    }

    @Log(back = false)
    @ApiOperation(value = "用户列表", notes = "组织机构管理，当前选择部门，下面的人员有哪些，显示到右侧")
    @GetMapping("/page")
    public R<Page<UserVo>> users(Page<TenantUserData> page, UserTenantPageVo userTenant) {
        Page<UserVo> userVoPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal(), page.isSearchCount());
        UserDto currentUser = UserCurrentUtils.getCurrentUser();
        QueryWrapper queryWrapper = null;
        //如果不是平台管理员或者超级管理员，则只允许查看本部门及以下的用户
        if (currentUser.getPlatformAdmin() || currentUser.getAdminFlag()) {
            queryWrapper = Wrappers.query()
                    .eq(ObjectNull.isNotNull(userTenant.getDeptId()), UserTenantMapper.SYS_USER_TENANT_ALIAS + ".dept_id", userTenant.getDeptId())
                    .eq(ObjectNull.isNotNull(userTenant.getSex()), ".sex", userTenant.getSex())
                    .eq(UserTenantMapper.SYS_USER_TENANT_ALIAS + ".cancel_flag", userTenant.getCancelFlag())
                    .ne(UserTenantMapper.SYS_USER_TENANT_ALIAS + ".user_id", currentUser.getId())
                    .and(ObjectNull.isNotNull(userTenant.getKeywords()), wrapper -> wrapper
                            .like(UserTenantMapper.SYS_USER_TENANT_ALIAS + ".real_name", userTenant.getKeywords())
                            .or().like(UserTenantMapper.SYS_USER_TENANT_ALIAS + ".phone", userTenant.getKeywords())
                            .or().like(".account_name", userTenant.getKeywords())
                    );
        } else {
            List<String> depts = deptService.getAllChildId(UserCurrentUtils.getDeptId());
            depts.add(UserCurrentUtils.getDeptId());
            if (ObjectNull.isNotNull(userTenant.getDeptId())) {
                if (!depts.contains(userTenant.getDeptId())) {
                    return R.failed("没有权限");
                }
            }
            queryWrapper = Wrappers.query()
                    .eq(ObjectNull.isNotNull(userTenant.getDeptId()), UserTenantMapper.SYS_USER_TENANT_ALIAS + ".dept_id", userTenant.getDeptId())
                    .eq(ObjectNull.isNotNull(userTenant.getSex()), ".sex", userTenant.getSex())
                    .eq(UserTenantMapper.SYS_USER_TENANT_ALIAS + ".cancel_flag", userTenant.getCancelFlag())
                    .ne(UserTenantMapper.SYS_USER_TENANT_ALIAS + ".user_id", currentUser.getId())
                    .in(ObjectNull.isNotNull(depts), UserTenantMapper.SYS_USER_TENANT_ALIAS + ".dept_id", depts)
                    .and(ObjectNull.isNotNull(userTenant.getKeywords()), wrapper -> wrapper
                            .like(UserTenantMapper.SYS_USER_TENANT_ALIAS + ".real_name", userTenant.getKeywords())
                            .or().like(UserTenantMapper.SYS_USER_TENANT_ALIAS + ".phone", userTenant.getKeywords())
                            .or().like(".account_name", userTenant.getKeywords())
                    );
        }
        userTenantService.tenantUsers(page, queryWrapper, ObjectNull.isNotNull(userTenant.getAreaCode()) ? userTenant.getAreaCode() : "44");
        if (ObjectUtil.isEmpty(page.getRecords())) {
            return R.ok(userVoPage);
        }
        Map<String, UserVo> map = new LinkedHashMap<>(page.getRecords().size());
        page.getRecords().forEach(e -> {
            UserVo userVo = BeanCopyUtil.copy(e, UserVo.class);
            userVo.setId(e.getUserId());
            userVo.setLevel(e.getAccountLevel());
            userVo.setLevelId(e.getAccountLevelId());
            map.put(userVo.getId(), userVo);
        });
        Map<String, List<Role>> roleByUserId = userRoleComponent.getUserRoleIds(map.keySet());
        List<UserVo> list = new ArrayList<>();
        for (String s : map.keySet()) {
            UserVo userVo = map.get(s)
                    .setRoleNames(roleByUserId.get(s).stream().map(Role::getRoleName)
                            .collect(Collectors.toList()));
            list.add(userVo);
        }
        userVoPage.setRecords(list);
        userVoPage.setTotal(page.getTotal());
        return R.ok(userVoPage);
    }

    /**
     * 用户搜索
     *
     * @param key    真实姓名或手机号(模糊搜索)
     * @param deptId 部门id
     * @return 用户信息集合
     */
    @Log
    @ApiOperation(value = "用户搜索", notes = "用户搜索操作,目前只提供手机号或姓名搜索")
    @GetMapping("/user/search")
    public R<List<User>> search(@RequestParam(required = false) String key, @RequestParam(required = false) String deptId) {
        if (StringUtils.isNotBlank(deptId)) {
            List<String> userIds = userTenantService.list(Wrappers.<UserTenant>lambdaQuery()
                            .select(UserTenant::getUserId)
                            .eq(UserTenant::getDeptId, deptId))
                    .stream().map(UserTenant::getUserId).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(userIds)) {
                return R.ok(userService.listByIds(userIds));
            }
        } else {
            List<User> list = userService.list(Wrappers.<User>lambdaQuery()
                    .select(User::getId, User::getRealName, User::getPhone, User::getHeadImg)
                    .orderByDesc(User::getCreateTime)
                    .like(ObjectUtil.isNotEmpty(key), User::getRealName, key)
                    .or()
                    .like(ObjectUtil.isNotEmpty(key), User::getPhone, key));
            return R.ok(list);
        }
        return R.ok(Collections.emptyList());
    }

    @Log
    @ApiOperation(value = "用户选择", notes = "多纬度选择对象")
    @PostMapping("/user/selected")
    public R<UserSelectedDto> search(@RequestBody SearchDto searchDto) {
        return selectedApi.search(searchDto);
    }

    /**
     * 通过名称快速搜索
     *
     * @param name 名称简称
     * @return
     */
    @ApiOperation(value = "用户姓名搜索", notes = "支持拼音简称搜索")
    @GetMapping("/user/short/{name}")
    public R searchShort(@PathVariable String name) {
        Map<String, Map<String, Object>> list = userTenantService.list(new LambdaQueryWrapper<UserTenant>()
                .select(UserTenant::getUserId, UserTenant::getRealName)
                .like(UserTenant::getRealName, name)
                .last(" limit 6")).stream().collect(Collectors.toMap(UserTenant::getUserId, BeanCopyUtil::beanToMap));
        String headImg = Get.name(User::getHeadImg);
        userService.list(new LambdaQueryWrapper<User>().select(User::getHeadImg, User::getId).in(User::getId, list.keySet())).forEach(e -> {
            list.get(e.getId()).put(headImg, e.getHeadImg());
        });
        return R.ok(list.values());
    }

    @Log
    @ApiOperation(value = "彻底删除用户", notes = "把用户从当前租户下删除")
    @DeleteMapping("/user/{id}")
    public R delete(@PathVariable String id) {
        userTenantService.remove(Wrappers.query(new UserTenant().setUserId(id)));
        TenantContextHolder.clear();
        // 已无租户信息，则删除该用相关信息
        if (userTenantService.count(Wrappers.<UserTenant>lambdaQuery().eq(UserTenant::getUserId, id)) == 0) {
            userService.removeById(id);
            userExtensionService.remove(Wrappers.<UserExtension>lambdaQuery().eq(UserExtension::getUserId, id));
            userGroupService.clearUser(id);
            userInviteMapper.delete(Wrappers.<UserInvite>lambdaQuery().eq(UserInvite::getUserId, id));
            userRoleComponent.clearUser(id);
        }
        //根据用户Id获取用户角色集
        List<Role> roleList = userRoleService.getRoleByUserId(id);
        //遍历，移出用户相应角色
        for (Role sigleRole : roleList) {
            String roleId = sigleRole.getId();
            userRoleService.remove(Wrappers.query(new UserRole().setUserId(id).setRoleId(roleId)));
        }
        return R.ok();
    }

    @Log
    @ApiOperation(value = "批量彻底删除用户", notes = "把用户从当前租户下删除")
    @DeleteMapping("/user/deleteMore")
    public R deleteMore(@RequestBody List<String> ids) {
        if (ObjectNull.isNull(ids)) {
            return R.failed("用户Id不能为空");
        }
        for (String id : ids) {
            userTenantService.remove(Wrappers.query(new UserTenant().setUserId(id)));
            TenantContextHolder.clear();
            // 已无租户信息，则删除该用相关信息
            if (userTenantService.count(Wrappers.<UserTenant>lambdaQuery().eq(UserTenant::getUserId, id)) == 0) {
                userService.removeById(id);
                userExtensionService.remove(Wrappers.<UserExtension>lambdaQuery().eq(UserExtension::getUserId, id));
                userGroupService.clearUser(id);
                userInviteMapper.delete(Wrappers.<UserInvite>lambdaQuery().eq(UserInvite::getUserId, id));
                userRoleComponent.clearUser(id);
            }
            //根据用户Id获取用户角色集
            List<Role> roleList = userRoleService.getRoleByUserId(id);
            //遍历，移出用户相应角色
            for (Role sigleRole : roleList) {
                String roleId = sigleRole.getId();
                userRoleService.remove(Wrappers.query(new UserRole().setUserId(id).setRoleId(roleId)));
            }
        }
        return R.ok();
    }

    @Log
    @ApiOperation(value = "用户选择", notes = "多纬度选择对象")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "多纬度查询条件", dataTypeClass = String.class),
            @ApiImplicitParam(name = "all", value = "false-只查询未删除的用户；true-查询所有用户(包括已删除的)", dataTypeClass = Boolean.class),
            @ApiImplicitParam(name = "deptId", value = "部门id", dataTypeClass = String.class),
            @ApiImplicitParam(name = "jobId", value = "岗位id", dataTypeClass = String.class),
    })
    @PostMapping("/user/selected/page")
    public R searchPageRang(Page<UserTenant> page,
                            @RequestBody UserSelectedVo vo) {
        page.setSize(vo.getSize());
        page.setCurrent(vo.getCurrent());
        //如果有指定范围，则直接使用指定范围的查询结果
        String deptId = vo.getDeptId();
        UserRangTypeDto rangType = vo.getRangType();
        List<String> rangIds = vo.getRangIds();
        String jobId = vo.getJobId();
        String key = vo.getKey();
        Boolean all = vo.getAll();
        List<String> userIds = null;
        List<String> depts = null;
        List<String> jobs = null;
        if (ObjectNull.isNotNull(rangType)) {
            switch (rangType) {
                case user:
                    userIds = rangIds;
                    break;
                case job:
                    jobs = rangIds;
                    break;
                case dept:
                    //获取所有的部门
                    depts = rangIds.stream().flatMap(e -> deptService.getAllChildId(e).stream()).collect(Collectors.toList());
                    break;
                case role:
                    //根据这个角色查询用户
                    userIds = userRoleService.list(new LambdaQueryWrapper<UserRole>().in(UserRole::getRoleId, rangIds)).stream()
                            .map(UserRole::getUserId).collect(Collectors.toList());
                    break;
                case currentDept:
                    depts = deptService.getAllChildId(UserCurrentUtils.getDeptId());
                    break;
                case onlyCurrentDept:
                    depts = new ArrayList<>();
                    depts.add(UserCurrentUtils.getDeptId());
                    break;
            }
        }

        // 非超管，限制数据返回为本单位及以下
        List<String> limitDept = new ArrayList<>();
        if (vo.getIsLimit()) {
            UserDto currentUser = UserCurrentUtils.getCurrentUser();
            if (!currentUser.getPlatformAdmin() && !currentUser.getAdminFlag()) {
                if (currentUser.getDeptInfo().getLevel() != 1 && ObjectNull.isNotNull(currentUser.getDeptInfo().getLevel())) {
                    if (!rangType.equals(UserRangTypeDto.onlyCurrentDept)) {
                        limitDept = deptService.getAllChildId(currentUser.getDeptId());
                    }
                    limitDept.add(currentUser.getDeptId());
                }
            }
        }

        List<UserTenant> userTenants = userTenantService.list(new LambdaQueryWrapper<UserTenant>()
                .select(UserTenant::getId, UserTenant::getUserId, UserTenant::getPhone, UserTenant::getRealName, UserTenant::getDeptId, UserTenant::getDeptName)
                .eq(ObjectNull.isNotNull(deptId), UserTenant::getDeptId, deptId)
                .eq(ObjectNull.isNotNull(jobId), UserTenant::getJobId, jobId)
                .in(ObjectNull.isNotNull(userIds), UserTenant::getUserId, userIds)
                .in(ObjectNull.isNotNull(depts), UserTenant::getDeptId, depts)
                .in(ObjectNull.isNotNull(jobs), UserTenant::getJobId, jobs)
                // 默认查询未删除的用户
                .eq(ObjectNull.isNull(all) || Boolean.FALSE.equals(all), UserTenant::getCancelFlag, false)
                .in(ObjectNull.isNotNull(limitDept), UserTenant::getDeptId, limitDept)
                .orderByDesc(UserTenant::getCreateTime)
        );
        if (ObjectNull.isNull(userTenants)) {
            return R.ok(page);
        }
        Map<String, UserTenant> ids = userTenants.stream().collect(Collectors.toMap(UserTenant::getUserId, Function.identity()));
        Page<User> userPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        userService.page(userPage, Wrappers.<User>query()
                .in(!ids.isEmpty(), "id", ids.keySet())
                .and(ObjectNull.isNotNull(key), wrapper -> wrapper
                        .like("real_name", key)
                        .or().like("phone", key)
                        .or().like("account_name", key)
                        .or().like("email", key))
                .orderByDesc("create_time")
        );
        Page<UserDto> userDtoPage = new Page<>(userPage.getCurrent(), userPage.getSize(), userPage.getTotal());
        List<UserDto> userDtos = new ArrayList<>();
        userPage.getRecords().forEach(e -> {
            UserDto userDto = BeanCopyUtil.copy(e, UserDto.class);
            List<UserTenant> userTenantList = userTenants.stream().filter(userTenant ->
                    userTenant.getUserId().equals(e.getId())).collect(Collectors.toList());
            userDto.setDeptId(userTenantList.get(0).getDeptId());
            userDto.setDeptName(userTenantList.get(0).getDeptName());
            List<String> perAndSubs = userPeriodSubjectService
                    .list(new LambdaQueryWrapper<UserPeriodSubject>()
                            .eq(UserPeriodSubject::getUserId, e.getId())
                            .select(UserPeriodSubject::getPerAndSubject))
                    .stream().map(UserPeriodSubject::getPerAndSubject).distinct().collect(Collectors.toList());
            userDto.setPerAndSubs(perAndSubs);
            userDtos.add(userDto);
        });
        userDtoPage.setRecords(userDtos);
        return R.ok(userDtoPage);
    }

    @GetMapping("/user/selected/page")
    public R searchPage(Page<UserTenant> page, @RequestParam(value = "key", required = false) String key, @RequestParam(value = "all", required = false) Boolean all, String deptId, String jobId) {
        page = userTenantService.page(page, new LambdaQueryWrapper<UserTenant>()
                .select(UserTenant::getId, UserTenant::getUserId, UserTenant::getPhone, UserTenant::getRealName)
                .eq(ObjectNull.isNotNull(deptId), UserTenant::getDeptId, deptId)
                .eq(ObjectNull.isNotNull(jobId), UserTenant::getJobId, jobId)
                // 默认查询未删除的用户
                .eq(ObjectNull.isNull(all) || Boolean.FALSE.equals(all), UserTenant::getCancelFlag, false)
                .and(ObjectNull.isNotNull(key), e -> e.like(ObjectNull.isNotNull(key), UserTenant::getRealName, key).or().like(ObjectNull.isNotNull(key), UserTenant::getPhone, key))
                .orderByDesc(UserTenant::getCreateTime)
        );
        if (ObjectNull.isNull(page.getRecords())) {
            return R.ok(page);
        }
        Map<String, UserTenant> ids = page.getRecords().stream().collect(Collectors.toMap(UserTenant::getUserId, Function.identity()));
        List<User> users = userService.listByIds(ids.keySet());
        Page<User> userPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        userPage.setRecords(users);
        return R.ok(userPage);
    }

    @Log
    @ApiOperation(value = "获取用户详情", notes = "组织机构管理，点击用户详情操作")
    @GetMapping("/user/{userId}")
    public R<UserVo> user(@PathVariable String userId) {
        User user = userService.getById(userId);
        if (Objects.isNull(user)) {
            return R.failed("用户不存在");
        }
        UserTenant userTenant = userTenantService.getOne(Wrappers.<UserTenant>lambdaQuery().eq(UserTenant::getUserId, userId));
        if (Objects.isNull(userTenant)) {
            return R.failed("用户未加入当前组织");
        }
        List<String> perAndSubs = userPeriodSubjectService
                .list(new LambdaQueryWrapper<UserPeriodSubject>()
                        .eq(UserPeriodSubject::getUserId, userId)
                        .select(UserPeriodSubject::getPerAndSubject))
                .stream().map(UserPeriodSubject::getPerAndSubject).distinct().collect(Collectors.toList());
        UserVo userVo = BeanCopyUtil.copy(UserVo.class, user, userTenant);
        userVo.setPerAndSubs(perAndSubs);
        return R.ok(userVo);
    }

    @Log
    @ApiOperation(value = "禁用用户", notes = "组织机构管理，可对用户进行移除，为不影响功能使用，只做注销不做删除,目前不支持注销后找回")
    @DeleteMapping("/users/disabled/{userId}")
    public R<Boolean> disabled(@PathVariable String userId) {
        userTenantService.update(Wrappers.<UserTenant>lambdaUpdate().set(UserTenant::getCancelFlag, true).eq(UserTenant::getUserId, userId));
        String userName = user(userId).getData().getRealName();
        return R.ok(true, "用户" + userName + "被禁用");
    }

    @Log
    @ApiOperation(value = "启用用户", notes = "组织机构管理，可对用户进行移除，为不影响功能使用，只做注销不做删除,目前不支持注销后找回")
    @DeleteMapping("/users/enable/{userId}")
    public R<Boolean> enable(@PathVariable String userId) {
        userTenantService.update(Wrappers.<UserTenant>lambdaUpdate().set(UserTenant::getCancelFlag, false).eq(UserTenant::getUserId, userId));
        String userName = user(userId).getData().getRealName();
        return R.ok(true, "用户" + userName + "被启用");
    }

    @Log
    @ApiOperation(value = "超级管理员修改用户密码", notes = "只有超级管理员才能修改用户密码")
    @PostMapping("/users/update/password/{userId}")
    public R passWord(@RequestBody ResetPasswordDto resetPasswordDto, @PathVariable String userId) {
        //只有平台超级管理员才能修改
        if (!resetPasswordDto.getPassword().equals(resetPasswordDto.getRePassword())) {
            return R.failed("密码不匹配");
        }
        // appId为与前端对应的应用id
        String decodedPassword = PasswordUtil.decodedPassword(resetPasswordDto.getRePassword(), SpringContextUtil.getKey());
        // 校验密码格式
        PasswordUtil.checkPasswordFormat(decodedPassword);
        //添加密码难度
//        PasswordUtil.checkPassword(resetPasswordDto.getPassword());
        String encodedPassword = passwordEncoder.encode(decodedPassword);
        User byId = userService.getById(userId);
        if (ObjectNull.isNull(byId)) {
            return R.failed("用户不存在");
        } else {
            // 修改密码
            userService.updateInfo(User::getPassword, encodedPassword, userId);
            // 获取用户登录日志，根据相应数据生成jvs
            LoginLog userLog = loginLogService.getOne(new LambdaQueryWrapper<LoginLog>()
                    .eq(LoginLog::getUserId, userId)
                    .orderByDesc(LoginLog::getOperateTime)
                    .last("LIMIT 1"));
            if (ObjectNull.isNotNull(userLog)) {
                String jvs = userLog.getTenantId() + DigestUtils.md5Hex(userLog.getUserId() + userLog.getIp() + userLog.getUserAgent());
                // 清空token以强制退出
                List<OAuth2Authorization> authorization = oAuth2AuthorizationService.keys(jvs, OAuth2TokenType.ACCESS_TOKEN);
                authorization.forEach(oAuth2AuthorizationService::remove);
                Object redisKey = redisUtils.get("jvs:token:" + jvs);
                redisUtils.del("jvs:token:" + jvs);
            }
            // 清空用户的 LastUpdatePasswordTime 字段数据、以强制用户登录时修改密码
            userService.cleanLastUpdatePasswordTime(userId);
            return R.ok().setMsg("密码修改成功");
        }
    }

//    @Log
//    @ApiOperation("手机号查重验证")
//    @PostMapping("/checkPhone")
//    public R<String> checkPhone(@RequestParam String phone){
//        long count = userService.count(Wrappers.<User>lambdaQuery().eq(User::getPhone, phone));
//        if (count > 0) {
//            return R.failed("系统已有该手机号");
//        } else {
//            return R.ok();
//        }
//    }
//
//    @Log
//    @ApiOperation("邮箱查重验证")
//    @PostMapping("/checkEmail")
//    public R<String> checkEmail(@RequestParam String email){
//        long count = userService.count(Wrappers.<User>lambdaQuery().eq(User::getEmail, email));
//        if (count > 0) {
//            return R.failed("系统已有该邮箱");
//        } else {
//            return R.ok();
//        }
//    }

    //    @Log
    @ApiOperation(value = "新增用户", notes = "后台新增的用户，默认类型为1，另外在租户中间表中进行添加一行处理")
    @PostMapping("/save")
//    @Transactional(rollbackFor = Exception.class)
    public R<UserVo> save(@RequestBody UserVo vo) {
        if (StringUtils.isBlank(vo.getAccountName())) {
            //手机号前面加随机号
            vo.setAccountName(IdGenerator.getIdStr(36));
        }
        if (!vo.getAccountName().matches(ACCOUNT_REGEX)) {
            throw new BusinessException("账号长度2-19个字符，只支持字母、数字、下划线，且不能以下划线开头或结尾");
        }

        if (!(vo.getRealName().matches(REAL_NAME_REGEX) || vo.getRealName().matches(REAL_NAME_INTERVALS_REGEX))) {
            throw new BusinessException("姓名只能包含中文和英文字母，长度最大为50个字符");
        }

        User user = BeanCopyUtil.copy(vo, User.class);
        UserTenant userTenant = BeanCopyUtil.copy(vo, UserTenant.class);

        // 校验两次密码是否匹配
        if (ObjectNull.isNotNull(vo.getPassword())) {
//            if (!vo.getPassword().equals(vo.getRePassword())) {
//                return R.failed("密码不匹配");
//            }
            String decodedPassword = PasswordUtil.decodedPassword(vo.getPassword(), SpringContextUtil.getKey());
            PasswordUtil.checkPasswordFormat(decodedPassword);
//            PasswordUtil.checkPassword(vo.getPassword());
            // 密码加密
            String encodedPassword = passwordEncoder.encode(decodedPassword);
            user.setPassword(encodedPassword);
        }

        //用户是否使用默认头像问题
        if (jvsSystemConfig.getHeadImg()) {
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            AvatarUtils.generateImg(vo.getRealName(), output);
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(output.toByteArray());
            BaseFile baseFile = ossTemplate.putFile("jvs-public", vo.getRealName() + ".jpg", byteArrayInputStream, "headImg");
            String link = ossTemplate.fileJvsPublicLink(baseFile.getFileName());
            user.setHeadImg(link);
        }
        userTenant.setAccountLevelId(vo.getLevelId());
        user = userService.saveUser(user, userTenant);

        if (ObjectNull.isNotNull(vo.getPerAndSubs())) {
            userPeriodSubjectService.modifyPerAndSubject(user.getId(), vo.getPerAndSubs());
        }
//        UserDto currentUser = UserCurrentUtils.getCurrentUser();
//        TenantPo tenantPo = tenantService.getById(currentUser.getTenantId());
        //发送短信,给用户发送邀请通知
//        smsComponent.sendUserInvite(currentUser, user, tenantPo);
        //再查询一次返回给前端
        UserVo userVo = BeanCopyUtil.copy(UserVo.class, user, userTenant);
        return R.ok(userVo);
    }

    @Log
    @ApiOperation(value = "生成邀请码", notes = "添加用户的两种方式，通过邀请码进入和通过直接添加,邀请生成规则为租户域名,加租户时间加密串")
    @GetMapping("/get/invite")
    public R<InviteVo> invite() {
        //默认设置为10分钟有效
        String tenantId = UserCurrentUtils.getCurrentUser().getTenantId();
        TenantPo tenantPo = tenantService.getById(tenantId);
        //将邀请码放到缓存中，         过期时间30分钟
        String code = IdGenerator.getIdStr(36).toUpperCase();
        Date date = new Date(System.currentTimeMillis() + 30 * 60 * 1000);
        String format = DateUtils.format(date);
        String invite = SysConstant.redisKey("invite", code);
        InviteVo inviteVo = new InviteVo().setCode(code).setStatus(false).setContent(UserCurrentUtils.getRealName() + " 邀请您参加组织\n" +
                "组织名称：【" + tenantPo.getName() + "】\n" +
                "有效期：" + format + "\n" +
                "\n" +
                "邀请码：\n" +
                code + "\n");
        inviteVo.setTenantId(tenantId);
        redisUtils.setExpire(invite, inviteVo, QR_EXPIRED_MIN, TimeUnit.MINUTES);
        return R.ok(inviteVo);
    }

    @Log
    @ApiOperation(value = "设置邀请码是否需要审核默认不需要")
    @GetMapping("/get/invite/{status}/{code}")
    public R<InviteVo> inviteStatus(@PathVariable Boolean status, @PathVariable String code) {
        InviteVo o = (InviteVo) redisUtils.get(SysConstant.redisKey("invite", code));
        o.setStatus(status);
        String invite = SysConstant.redisKey("invite", code);
        redisUtils.setExpire(invite, o, QR_EXPIRED_MIN, TimeUnit.MINUTES);
        return R.ok();
    }

    @Log
    @ApiOperation(value = "修改用户", notes = "后台修改用户")
    @PutMapping("/update")
    @Transactional(rollbackFor = Exception.class)
    public R update(@RequestBody UserVo vo) {
        if (ObjectNull.isNull(vo.getRealName())) {
            return R.failed("用户名不能修改为空");
        }

        if (!vo.getAccountName().matches(ACCOUNT_REGEX)) {
            throw new BusinessException("账号长度2-19个字符，只支持字母、数字、下划线，且不能以下划线开头或结尾");
        }

        if (!(vo.getRealName().matches(REAL_NAME_REGEX) || vo.getRealName().matches(REAL_NAME_INTERVALS_REGEX))) {
            throw new BusinessException("姓名只能包含中文和英文字母，长度最大为50个字符");
        }

        String tenantId = TenantContextHolder.getTenantId();
        User user = BeanCopyUtil.copy(vo, User.class);
        // 手机号校验
        String phone = user.getPhone();
        if (StringUtils.isNotBlank(phone)) {
            long count = userService.count(Wrappers.<User>lambdaQuery().eq(User::getPhone, phone).ne(User::getId, user.getId()));
            if (count >= 1) {
                return R.failed("手机号已被使用");
            }
        }
        String accountName = user.getAccountName();
        if (StringUtils.isNotBlank(accountName)) {
            long count = userService.count(Wrappers.<User>lambdaQuery().eq(User::getAccountName, accountName).ne(User::getId, user.getId()));
            if (count >= 1) {
                return R.failed("帐号已经存在");
            }
        }
        // 邮箱校验
        boolean hasEmail = ObjectNull.isNull(user.getEmail());
        if (hasEmail) {
            user.setEmail("");
        } else {
            long count = userService.count(Wrappers.<User>lambdaQuery().eq(User::getEmail, user.getEmail()).ne(User::getId, user.getId()));
            if (count >= 1) {
                return R.failed("邮箱已存在");
            }
            if (!user.getEmail().matches(EMAIL_PATTERN)) {
                return R.failed("邮箱格式不正确");
            }
        }

        userPeriodSubjectService.modifyPerAndSubject(user.getId(), vo.getPerAndSubs());

        UserTenant userTenant = BeanCopyUtil.copy(vo, UserTenant.class);
        String deptId = userTenant.getDeptId();
        if (ObjectUtil.isNotEmpty(deptId)) {
            Dept dept = deptService.getById(deptId);
            if (Objects.isNull(dept)) {
                log.error("该部门不存在, 部门id: {}", deptId);
                return R.failed("该部门不存在");
            }
            userTenant.setDeptName(dept.getName());
        } else {
            userTenant.setDeptName(null);
        }

        String jobId = userTenant.getJobId();
        if (ObjectUtil.isNotEmpty(jobId)) {
            Job job = jobService.getById(jobId);
            if (Objects.isNull(job)) {
                log.error("该岗位不存在, 岗位id: {}", jobId);
                return R.failed("该岗位不存在");
            }
            userTenant.setJobName(job.getName());
        } else {
            userTenant.setJobName(null);
        }

        String levelId = vo.getLevelId();
        if (ObjectUtil.isNotEmpty(levelId)) {
            UserLevel userLevel = userLevelService.getById(levelId);
            if (Objects.isNull(userLevel)) {
                log.error("该用户等级不存在, 用户等级id: {}", levelId);
                return R.failed("该用户等级不存在");
            }
            userTenant.setAccountLevel(userLevel.getName());
            userTenant.setAccountLevelId(levelId);
        }

//        userPeriodSubjectService.modifyPerAndSubject(user.getId(), vo.getPerAndSubs());

        UserTenant updateUserTenant = userTenantService.getOne(Wrappers.query(new UserTenant().setUserId(user.getId()).setTenantId(tenantId)));
        userTenant.setId(updateUserTenant.getId());
        userTenantService.updateById(userTenant);
        userService.updateById(user);
//        userService.updateUserTokenPerAndSub(user.getId());
        return R.ok();
    }


    @ApiOperation("下载模板")
    @GetMapping("/template/excel/download")
    public void downloadExcelTemplate(HttpServletResponse response) {
        userService.downloadExcelTemplate(response);
    }

    @ApiOperation("导入")
    @PostMapping("/import")
    @Transactional(rollbackFor = Exception.class)
    public R<ImportFeedback> importUser(@RequestPart("file") MultipartFile file) {
        UserDto currentUser = UserCurrentUtils.getCurrentUser();
        ImportFeedback feedbackFile = userService.importUserExcel(currentUser, file);
        return R.ok(feedbackFile, "导入成功，已生成反馈结果!");
    }

    /**
     * 导出用户
     **/
    @ApiOperation(value = "导出用户", notes = "默认导出当前用户所属部门及子部门的用户，可传部门Id导出相应部门下的用户")
    @GetMapping("/exportUser")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> exportUser(HttpServletResponse response, Page<TenantUserData> page, UserTenantPageVo userTenant,
                                 @RequestParam(value = "perAndSubs", required = false) List<String> perAndSubIds,
                                 @RequestParam(value = "userIds", required = false) List<String> ids,
                                 @RequestParam(value = "areaCode", required = false) String areaCode,
                                 @RequestParam(value = "selectDeptId", required = false) String deptId,
                                 @RequestParam(value = "selecJobId", required = false) String jobId,
                                 @RequestParam(value = "selectRoleId", required = false) String roleId) {
        UserDto currentUser = UserCurrentUtils.getCurrentUser();

        userService.retrieval(page, userTenant.getCancelFlag(), ids, deptId, jobId, roleId, userTenant.getKeywords(), currentUser, areaCode, perAndSubIds);

        if (ObjectUtil.isEmpty(page.getRecords())) {
            return R.ok(false, "暂无数据");
        }

        Map<String, UserVo> map;
        List<String> userIds = page.getRecords().stream().map(TenantUserData::getUserId).collect(Collectors.toList());
        Map<String, String> psMap = userPeriodSubjectService.getPerAndSubjectTxtMap(userIds);
        if (page.getCurrent() == 1 && page.getSize() == 10) {
            map = new LinkedHashMap<>((int) page.getTotal());
            Page<TenantUserData> newPage = new Page<>(page.getCurrent(), page.getTotal(), page.getTotal());
            userService.retrieval(newPage, userTenant.getCancelFlag(), ids, deptId, jobId, roleId, userTenant.getKeywords(), currentUser, areaCode, perAndSubIds);
            newPage.getRecords().forEach(e -> {
                UserVo userVo = BeanCopyUtil.copy(e, UserVo.class);
                userVo.setId(e.getUserId());
                userVo.setLevel(e.getAccountLevel());
                userVo.setLevelId(e.getAccountLevelId());
                userVo.setStatus(e.getCancelFlag() ? "禁用" : "启用");
                userVo.setPerAndSubsName(psMap.get(e.getUserId()));
                map.put(userVo.getId(), userVo);
            });
        } else {
            map = new LinkedHashMap<>(page.getRecords().size());
            page.getRecords().forEach(e -> {
                UserVo userVo = BeanCopyUtil.copy(e, UserVo.class);
                userVo.setId(e.getUserId());
                userVo.setLevel(e.getAccountLevel());
                userVo.setLevelId(e.getAccountLevelId());
                userVo.setStatus(e.getCancelFlag() ? "禁用" : "启用");
                userVo.setPerAndSubsName(psMap.get(e.getUserId()));
                map.put(userVo.getId(), userVo);
            });
        }
        Map<String, List<Role>> roleByUserId = userRoleComponent.getUserRoleIds(map.keySet());
        List<UserVo> list = new ArrayList<>();
        for (String s : map.keySet()) {
            UserVo userVo = map.get(s).setRoleNames(roleByUserId.get(s).stream().map(Role::getRoleName).collect(Collectors.toList()));
            userVo.setRoleName(userVo.getRoleNames().toString().replace("[", "").replace("]", ""));
            list.add(userVo);
        }
        userService.exportUserExcel(list, response);
        return R.ok(true, "导出成功");
    }

    /**
     * 用户列表检索
     *
     * @param
     * @return
     */
    @ApiOperation(value = "用户列表检索", notes = "支持角色、部门、岗位检索用户")
    @GetMapping("/retrieval")
    @Transactional(rollbackFor = Exception.class)
    public R<Page<UserVo>> userRetrieval(Page<TenantUserData> page, UserTenantPageVo userTenant,
                                         @RequestParam(value = "perAndSubs", required = false) List<String> perAndSubIds,
                                         @RequestParam(value = "areaCode", required = false) String areaCode,
                                         @RequestParam(value = "selectDeptId", required = false) String deptId,
                                         @RequestParam(value = "selecJobId", required = false) String jobId,
                                         @RequestParam(value = "selectRoleId", required = false) String roleId) {

        Boolean cancelFlag = userTenant.getCancelFlag();
        String keywords = userTenant.getKeywords();
        UserDto currentUser = UserCurrentUtils.getCurrentUser();

        userService.retrieval(page, cancelFlag, null, deptId, jobId, roleId, keywords, currentUser, areaCode, perAndSubIds);
        Page<UserVo> userVoPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal(), page.isSearchCount());
        if (ObjectUtil.isEmpty(page.getRecords())) {
            return R.ok(userVoPage);
        }

        List<String> userIds = page.getRecords().stream().map(TenantUserData::getUserId).collect(Collectors.toList());
        Map<String, String> psMap = userPeriodSubjectService.getPerAndSubjectTxtMap(userIds);
        Map<String, List<String>> userPSMap = userPeriodSubjectService.list(new LambdaQueryWrapper<UserPeriodSubject>()
                        .in(UserPeriodSubject::getUserId, userIds))
                .stream().collect(Collectors.groupingBy(UserPeriodSubject::getUserId
                        , Collectors.mapping(UserPeriodSubject::getPerAndSubject, Collectors.toList())));
        Map<String, List<Role>> roleByUserId = userRoleComponent.getUserRoleIds(userIds);
        List<UserVo> res = new ArrayList<>();
        page.getRecords().forEach(e -> {
            UserVo userVo = BeanCopyUtil.copy(e, UserVo.class);
            userVo.setId(e.getUserId());
            userVo.setLevel(e.getAccountLevel());
            userVo.setLevelId(e.getAccountLevelId());
            userVo.setLastLoginTime(e.getLastLoginTime());
            userVo.setStatus(e.getCancelFlag() ? "禁用" : "启用");
            userVo.setPerAndSubs(userPSMap.get(e.getUserId()));
            userVo.setPerAndSubsName(psMap.get(e.getUserId()));
            res.add(userVo);
        });
        res.forEach(e -> e.setRoleNames(
                roleByUserId.get(e.getId())
                        .stream()
                        .map(Role::getRoleName)
                        .collect(Collectors.toList())));
        userVoPage.setRecords(res);
        return R.ok(userVoPage);
    }

    /**
     * 解锁用户
     *
     * @param userName 指定用户的用户名
     * @return 返回此操作状态
     */
    @ApiOperation(value = "解锁用户")
    @PostMapping("/unlock")
    @Transactional(rollbackFor = Exception.class)
    public R<String> unlockUser(@RequestParam(value = "accountName") String userName, String deptId) {
        UserDto currentUser = UserCurrentUtils.getCurrentUser();
        Set<String> lockKeys = redisTemplate.keys(RedisUtils.prefix + SysConstant.JVS_AUTH + ":lock:*" + userName);
        Set<String> checkKeys = redisTemplate.keys(RedisUtils.prefix + SysConstant.JVS_AUTH + ":checklogin:*" + userName);
        if (currentUser.getPlatformAdmin() || currentUser.getAdminFlag()) {
            boolean hasLocked = CollectionUtils.isNotEmpty(lockKeys);
            boolean hasChecked = CollectionUtils.isNotEmpty(checkKeys);
            if (hasLocked && hasChecked) {
                redisTemplate.delete(lockKeys);
                redisTemplate.delete(checkKeys);
            }
            return R.ok("用户" + userName + "解锁成功");
        } else {
            List<String> depts = deptService.getAllChildId(currentUser.getDeptId());
            depts.add(currentUser.getDeptId());
            if (ObjectNull.isNotNull(deptId)) {
                if (!depts.contains(deptId)) {
                    return R.failed("当前用户没有权限");
                }
            }
            boolean hasLocked = CollectionUtils.isNotEmpty(lockKeys);
            boolean hasChecked = CollectionUtils.isNotEmpty(checkKeys);
            if (hasLocked && hasChecked) {
                redisTemplate.delete(lockKeys);
                redisTemplate.delete(checkKeys);
            }
            return R.ok("用户" + userName + "解锁成功");
        }
    }

    @ApiOperation(value = "获取用户学段学科")
    @GetMapping("/get/perAndSub")
    public R<List<String>> getPerAndSub(@RequestParam(value = "userId", required = false) String userId) {
        return R.ok(userPeriodSubjectService.getUserPerAndSubject(userId));
    }

    @Log
    @ApiOperation(value = "重置密码")
    @PostMapping("/reset/{userId}/password")
    public R<Boolean> resetUserPassWord(@PathVariable("userId") String userId) {
        User byId = userService.getById(userId);
        if (ObjectNull.isNull(byId)) {
            return R.failed("用户不存在");
        } else {
            String encodedPassword;
            SysFrameApplyConfig config = configsService.getConfig(ConfigsTypeEnum.BACKGROUND_PERSONALIZED_CONFIGURATION);
            if (PASS_CACHE.containsKey(config.getDefaultPassword())) {
                encodedPassword = PASS_CACHE.get(config.getDefaultPassword());
            } else {
                // 设置初始密码
                String encode = passwordEncoder.encode(config.getDefaultPassword());
                PASS_CACHE.put(config.getDefaultPassword(), encode);
                encodedPassword = encode;
            }

            if (ObjectNull.isNull(encodedPassword)) {
                return R.failed("暂未设置初始密码，请联系管理员");
            }

            // 修改密码
            userService.updateInfo(User::getPassword, encodedPassword, userId);
            // 获取用户登录日志，根据相应数据生成jvs
            LoginLog userLog = loginLogService.getOne(new LambdaQueryWrapper<LoginLog>()
                    .eq(LoginLog::getUserId, userId)
                    .orderByDesc(LoginLog::getOperateTime)
                    .last("LIMIT 1"));
            if (ObjectNull.isNotNull(userLog)) {
                String jvs = userLog.getTenantId() + DigestUtils.md5Hex(userLog.getUserId() + userLog.getIp() + userLog.getUserAgent());
                // 清空token以强制退出
                List<OAuth2Authorization> authorization = oAuth2AuthorizationService.keys(jvs, OAuth2TokenType.ACCESS_TOKEN);
                authorization.forEach(oAuth2AuthorizationService::remove);
                Object redisKey = redisUtils.get("jvs:token:" + jvs);
                redisUtils.del("jvs:token:" + jvs);
            }
            // 清空用户的 LastUpdatePasswordTime 字段数据、以强制用户登录时修改密码
            userService.cleanLastUpdatePasswordTime(userId);
            return R.ok(true, "重置成功");
        }
    }

}