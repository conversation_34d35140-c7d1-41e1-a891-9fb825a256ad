package cn.bctools.auth.feign;

import cn.bctools.auth.api.api.AuthRoleServiceApi;
import cn.bctools.auth.api.dto.PersonnelDto;
import cn.bctools.auth.api.dto.RoleChargeDto;
import cn.bctools.auth.api.dto.SysRoleDto;
import cn.bctools.auth.api.enums.PersonnelTypeEnum;
import cn.bctools.auth.entity.Role;
import cn.bctools.auth.entity.RoleCharge;
import cn.bctools.auth.entity.RolePermission;
import cn.bctools.auth.entity.UserRole;
import cn.bctools.auth.service.RoleChargeService;
import cn.bctools.auth.service.RolePermissionService;
import cn.bctools.auth.service.RoleService;
import cn.bctools.auth.service.UserRoleService;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequestMapping
@RestController
@AllArgsConstructor
public class RoleApiImpl implements AuthRoleServiceApi {

    RoleService roleService;
    UserRoleService userRoleService;
    RoleChargeService roleChargeService;
    RolePermissionService rolePermissionService;

    @Override
    public R<List<SysRoleDto>> getAll() {
        List<Role> list = roleService.list(Wrappers.<Role>lambdaQuery().select(Role::getId, Role::getRoleName, Role::getRoleDesc));
        return R.ok(BeanCopyUtil.copys(list, SysRoleDto.class));
    }

    @Override
    public R<SysRoleDto> getById(String roleId) {
        Role role = roleService.getOne(Wrappers.<Role>lambdaQuery()
                .select(Role::getId, Role::getRoleName, Role::getRoleDesc)
                .eq(Role::getId, roleId));
        return R.ok(BeanCopyUtil.copy(role, SysRoleDto.class));
    }

    @Override
    public R setUser(String roleId, List<String> userIds) {
        List<String> list = userRoleService.list(Wrappers.query(new UserRole().setRoleId(roleId))).stream().map(UserRole::getUserId).collect(Collectors.toList());
        userIds.removeAll(list);
        List<UserRole> collect = userIds.stream().map(e -> new UserRole().setRoleId(roleId).setUserId(e)).collect(Collectors.toList());
        userRoleService.saveBatch(collect);
        userIds.forEach(e -> userRoleService.updateUserTokenRoles(e));
        return R.ok();
    }

    @Override
    public R<List<SysRoleDto>> getByIds(List<String> roleIds) {
        if (ObjectUtils.isEmpty(roleIds)) {
            return R.ok(Collections.emptyList());
        }
        List<Role> list = roleService.list(Wrappers.<Role>lambdaQuery()
                .select(Role::getId, Role::getRoleName, Role::getRoleDesc)
                .in(Role::getId, roleIds));
        return R.ok(BeanCopyUtil.copys(list, SysRoleDto.class));
    }

    @Override
    public R<Boolean> updateRoleCharge(RoleChargeDto roleChargeDto) {
        List<RoleCharge> list = new ArrayList<>();
        String appId = roleChargeDto.getAppId();
        if (ObjectNull.isNull(roleChargeDto.getRoles())) {
            return R.ok();
        }
        List<String> ids = roleChargeDto.getRoles().stream().map(PersonnelDto::getId).collect(Collectors.toList());
        if (ObjectNull.isNotNull(ids)) {
            List<String> rolePermissions = Arrays.asList("116", "117", "289", "290");
            List<RoleCharge> roleCharges = roleChargeService.list(Wrappers.lambdaQuery(RoleCharge.class).eq(RoleCharge::getAppId, appId).notIn(RoleCharge::getRoleId, ids));
            roleCharges.forEach(e -> {
                //更新角色权限，需要设置以下权限
                //jvs_role_add_user —— 116 —— 添加人员
                //jvs_role_delete_user —— 117 —— 移出人员
                //jvs_role_export_user —— 289 —— 导出成员
                //jvs_role_member —— 290 ——查看人员
                List<RoleCharge> otherRoleCharges = roleChargeService.list(new LambdaQueryWrapper<RoleCharge>().eq(RoleCharge::getRoleId, e.getRoleId()).ne(RoleCharge::getAppId, appId));
                boolean flag = otherRoleCharges.stream().anyMatch(f -> ObjectNull.isNotNull(f.getInChargeRoleIds()));
                if (!flag) {
                    //其他应用没有设置该角色的管理范围，则删除他的上述权限
                    rolePermissionService.remove(new LambdaQueryWrapper<RolePermission>()
                            .eq(RolePermission::getRoleId, e.getRoleId())
                            .in(RolePermission::getPermissionId, rolePermissions));
                }

            });

            roleChargeService.remove(Wrappers.lambdaQuery(RoleCharge.class).eq(RoleCharge::getAppId, appId).notIn(RoleCharge::getRoleId, ids));

        }

        List<String> existRoleIds = roleChargeService.list(new LambdaQueryWrapper<RoleCharge>().eq(RoleCharge::getAppId, appId).select(RoleCharge::getRoleId))
                .stream().map(RoleCharge::getRoleId).collect(Collectors.toList());

        roleChargeDto.getRoles().stream()
                .filter(e -> ObjectNull.isNotNull(e.getId()))
                .filter(e -> ObjectNull.isNotNull(roleService.getOne(Wrappers.lambdaQuery(Role.class).eq(Role::getId, e.getId()))))
                .filter(e -> !existRoleIds.contains(e.getId()))
                .forEach(e -> {
                    RoleCharge roleCharge = new RoleCharge();
                    roleCharge.setAppId(appId);
                    roleCharge.setRoleId(e.getId());
                    list.add(roleCharge);
                });
        return R.ok(roleChargeService.saveBatch(list));
    }

    @Override
    public R<Boolean> delRoleCharge(String appId) {
        return R.ok(roleChargeService.remove(Wrappers.lambdaQuery(RoleCharge.class).eq(RoleCharge::getAppId, appId)));
    }

    @Override
    public R checkRoleByRoleNames(List<String> roleNames) {
        List<String> result = new ArrayList<>();
        roleNames.forEach(e -> {
            Role role = roleService.getOne(new LambdaQueryWrapper<Role>().eq(Role::getRoleName, e));
            if (ObjectNull.isNull(role)) {
                result.add("角色" + e + "不存在");
            }
        });
        if (ObjectNull.isNotNull(result)) {
            String error = String.join(", ", result);
            return R.ok(error);
        }
        return R.ok(true);
    }

    @Override
    public R<Boolean> deleteUser(String roleId, String userId) {
        //将某个用户移出某个角色
        userRoleService.remove(Wrappers.query(new UserRole().setUserId(userId).setRoleId(roleId)));
        return R.ok(true, "移出成功");
    }

    @Override
    public void setUserRoleByExternalPage(String type, List<JSONObject> permissions) {
        // 获取所有具有权限的角色
        Set<String> roleIds = permissions.stream().flatMap(e ->
                        BeanCopyUtil.copys(e.getJSONArray("personnels"), PersonnelDto.class).stream())
                .filter(e -> PersonnelTypeEnum.role.equals(e.getType()))
                .map(PersonnelDto::getId)
                .collect(Collectors.toSet());

        List<String> permissionIds = new ArrayList<>();
        if ("user_audit".equals(type)) {
            // 设置用户审核角色
            permissionIds = Collections.singletonList("379");
        } else if ("user_manage".equals(type)) {
            // 设置用户管理角色
            permissionIds = Arrays.asList("116", "117", "289", "290", "378");
        }
        // 判断角色有没有这些权限，没有则添加
        LambdaQueryWrapper<RolePermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RolePermission::getRoleId, roleIds);
        wrapper.in(RolePermission::getPermissionId, permissionIds);
        List<RolePermission> existingRolePermissions = rolePermissionService.list(wrapper);

        // 创建Map快速查找已存在的角色权限组合
        Map<String, RolePermission> existingPermissionsMap = existingRolePermissions.stream()
                .collect(Collectors.toMap(rp -> rp.getRoleId() + "_" + rp.getPermissionId(), Function.identity()));

        // 生成新增组合
        List<RolePermission> newRolePermissions = new ArrayList<>();
        for (String roleId : roleIds) {
            for (String permissionId : permissionIds) {
                String key = roleId + "_" + permissionId;
                if (!existingPermissionsMap.containsKey(key)) {
                    newRolePermissions.add(new RolePermission().setRoleId(roleId).setPermissionId(permissionId));
                }
            }
        }

        // 批量保存
        rolePermissionService.saveBatch(newRolePermissions);
    }
}
