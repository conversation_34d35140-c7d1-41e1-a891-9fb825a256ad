package cn.bctools.auth.vo;

import cn.bctools.auth.entity.enums.SexTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class SysRegisterVo {

    @ApiModelProperty(value = "主键")
    String id;

    @ApiModelProperty(value = "姓名")
    String realName;

    @ApiModelProperty(value = "账号")
    String accountName;

    @ApiModelProperty(value = "主管部门Id")
    String deptId;

    @ApiModelProperty(value = "主管部门Name")
    String deptName;

    @ApiModelProperty(value = "所属地区code")
    String regionCode;
    List<String> regionCodeList;

    @ApiModelProperty(value = "手机号")
    String phone;

    @ApiModelProperty(value = "性别")
    SexTypeEnum sex;

    @ApiModelProperty(value = "电子邮箱")
    String email;

    @ApiModelProperty(value = "审核人Id")
    private String auditBy;

    @ApiModelProperty(value = "审核人Name")
    private String auditByName;

    @ApiModelProperty(value = "审核状态", notes = "0-待审核 、 1-通过 、 2-不通过")
    private Integer auditStatus;
    private String auditStatusStr;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "应用id")
    private String appId;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

}
