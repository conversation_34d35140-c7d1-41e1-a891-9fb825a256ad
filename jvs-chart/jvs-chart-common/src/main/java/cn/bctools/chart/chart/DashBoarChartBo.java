package cn.bctools.chart.chart;

import cn.bctools.chart.chart.bo.DashBoarLogicSettingBo;
import cn.bctools.chart.chart.bo.FieldsData;
import cn.bctools.chart.chart.calculate.FieldCalculateFactory;
import cn.bctools.chart.chart.po.DashBoardChartPo;
import cn.bctools.common.exception.BusinessException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * [description]：仪表盘
 * @modified By：
 * @version: 1.0.0$
 */
@Slf4j
@Data
@Component
@AllArgsConstructor
public class DashBoarChartBo implements ChartElementInterface {
    private final FieldCalculateFactory fieldCalculateFactory;
    private List<DashBoardChartPo> data;


    @Override
    public void dataTreating(JSONObject json) {
        json.put("tableData", data);
    }

    @Override
    public void setDefaultValue(Map<String, Object> logicSetting) {
        this.data = null;
    }


    @Override
    public void exec(List<Map<String, Object>> data, String chartName, Map<String, Object> logicSetting) {
        DashBoarLogicSettingBo logicSettingBo = JSONObject.parseObject(JSONObject.toJSONString(logicSetting), DashBoarLogicSettingBo.class);
        if (!logicSettingBo.check()) {
            throw new BusinessException("设计参数不完整");
        }
        DashBoardChartPo dashBoardChartPo = new DashBoardChartPo();
        FieldsData calculateKey = logicSettingBo.getCalculateKey();
        Object execute = fieldCalculateFactory.execute(data, calculateKey);
        if (StrUtil.isNotBlank(calculateKey.getFunctionStr())) {
            execute = this.FunctionExec(execute, calculateKey.getFunctionStr());
        }
        dashBoardChartPo.setValue(execute);
        this.data = Arrays.asList(dashBoardChartPo);
    }
}
