package cn.bctools.chart.chart;

import cn.bctools.chart.chart.bo.FieldsData;
import cn.bctools.chart.chart.bo.TreeSettingBo;
import cn.bctools.chart.chart.calculate.FieldCalculateFactory;
import cn.bctools.chart.chart.po.TreePo;
import cn.bctools.common.exception.BusinessException;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 雷达图
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Component
@AllArgsConstructor
public class TreeChartBo implements ChartElementInterface {
    private final FieldCalculateFactory fieldCalculateFactory;
    private List<TreePo> data;

    @Override
    public void dataTreating(JSONObject json) {
        json.put("tableData", data);
    }

    @Override
    public void setDefaultValue(Map<String, Object> logicSetting) {
        this.data = null;
    }

    @Override
    public void exec(List<Map<String, Object>> data, String chartName, Map<String, Object> logicSetting) {
        TreeSettingBo treeSettingBo = JSONObject.parseObject(JSONObject.toJSONString(logicSetting), TreeSettingBo.class);
        if (!treeSettingBo.check()) {
            throw new BusinessException("设计参数不完整");
        }
        TreePo treePo = new TreePo();
        treePo.setName("树形");
        //通过
        List<TreePo.TreeData> extracted = this.extracted(data, treeSettingBo.getDimensionality(), 0);
        treePo.setData(extracted);
        this.data = Arrays.asList(treePo);
    }

    /**
     * @param data  数据
     * @param list  维度
     * @param index 当前下标
     * @return 树形数据 {@link TreePo.TreeData}
     */
    private List<TreePo.TreeData> extracted(List<Map<String, Object>> data, List<FieldsData> list, int index) {
        FieldsData fieldsData = list.get(index);
        LinkedHashMap<Object, List<Map<String, Object>>> map = data
                .stream()
                .collect(Collectors.groupingBy(e -> ObjUtil.isNull(e.get(fieldsData.getFieldKey())) ? "" : e.get(fieldsData.getFieldKey()), LinkedHashMap::new, Collectors.toList()));
        ++index;
        boolean b = list.size() == index;
        int finalIndex = index;
        return map.keySet().stream().map(e -> {
            TreePo.TreeData treeData = new TreePo.TreeData()
                    .setName(e.toString());
            if (!b) {
                List<TreePo.TreeData> children = extracted(map.get(e), list, finalIndex);
                treeData.setChildren(children);
            }
            return treeData;
        }).collect(Collectors.toList());

    }

}
