package cn.bctools.chart.chart.bo;


import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 3d柱状图 入参
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BasicsHistogram3DSettingBo {

    /**
     * x轴维度
     */
    private FieldsData xAxis;
    /**
     * y轴维度
     */
    private FieldsData yAxis;

    /**
     * 指标
     */
    private List<FieldsData> indexList;

    /**
     * 参数是否完整
     */
    public Boolean check() {
        if (indexList == null || indexList.isEmpty()) {
            return false;
        }
        boolean anyMatch = indexList.stream().anyMatch(FieldsData::check);
        return xAxis != null && xAxis.check() && yAxis != null && yAxis.check() && anyMatch;
    }

}
