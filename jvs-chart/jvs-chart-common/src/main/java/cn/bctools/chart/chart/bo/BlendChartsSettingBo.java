package cn.bctools.chart.chart.bo;


import cn.hutool.core.util.ObjectUtil;
import lombok.Data;

/**
 * 柱线混合图 入参
 */
@Data
public class BlendChartsSettingBo {

    /**
     * 横轴
     */
    private FieldsData xAxis;
    /**
     * 左纵轴
     */
    private FieldsData yAxisL;
    /**
     * 右纵轴
     */
    private FieldsData yAxisR;

    /**
     * 参数是否完整
     */
    public Boolean check() {
        boolean allNotEmpty = ObjectUtil.isAllNotEmpty(xAxis, yAxisL, yAxisR);
        if (allNotEmpty) {
            return xAxis.check() && yAxisL.check() && yAxisR.check();
        }
        return false;
    }

}
