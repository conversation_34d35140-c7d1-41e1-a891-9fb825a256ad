package cn.bctools.chart.chart.bo;


import lombok.Data;

import java.util.List;

/**
 * 饼图设计入参
 */
@Data
public class DataCardLogicSettingBo {
    /**
     * 分组字段
     */
    private List<FieldsData> infoCard;
    /**
     * 标题名称
     */
    private String titleName;


    public Boolean check() {
        return this.infoCard != null && !this.infoCard.isEmpty() && this.getInfoCard().get(0).check();
    }
}
