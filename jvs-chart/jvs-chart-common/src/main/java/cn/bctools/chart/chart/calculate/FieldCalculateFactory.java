package cn.bctools.chart.chart.calculate;

import cn.bctools.chart.chart.bo.FieldsData;
import cn.bctools.common.utils.SpringContextUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 字段值计算 工厂
 */
@Component
public class FieldCalculateFactory {

    /**
     * 计算执行
     *
     * @param list       需要计算的对象
     * @param fieldsData 计算的key值 可能会存在多个 具体根据不同的计算 业务自行判断
     */
    public BigDecimal execute(List<Map<String, Object>> list, FieldsData... fieldsData) {
        FieldsData fieldsDatum = fieldsData[0];
        FieldCalculate bean = SpringContextUtil.getBean(fieldsDatum.getCalculateType().getCls());
        return bean.execute(list, fieldsData);
    }

}
