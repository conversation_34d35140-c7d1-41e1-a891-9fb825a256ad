package cn.bctools.chart.chart.po;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * [description]：基础折线图数据对象,多折线图，其他折线图，基础柱状图,分组柱状图,堆叠柱状图，直方图
 * @modified By：
 * @version: *******$
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasicsChartPo {
    /**
     * x轴数据
     */
    @JSONField(name = "xAxis")
    private String xAxis;
    /**
     * y轴数据
     */
    @JSONField(name = "yAxis")
    private Object yAxis;

}
