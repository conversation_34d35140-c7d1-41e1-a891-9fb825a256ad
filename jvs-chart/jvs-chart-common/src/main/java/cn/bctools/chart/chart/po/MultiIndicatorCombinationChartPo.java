package cn.bctools.chart.chart.po;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 多指标线柱混合图 返回值
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MultiIndicatorCombinationChartPo {
    private List<String> legend;
    @JSONField(name = "xAxis")
    private List<Object> xAxis;
    @JSONField(name = "yAxis")
    private List<YAxisData> yAxis;
    private List<Series> seriesList;

    /**
     * y轴返回数据
     *
     * <AUTHOR>
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class YAxisData {
        /**
         * 名称
         */
        private String name;
        /**
         * 单位
         */
        private String unit;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class Series {
        /**
         * 名称
         */
        private String name;
        /**
         * 类型 bar 柱状 line 线条
         */
        private String type;
        /**
         * 对应 legend数据的下标值
         */
        @JSONField(name = "yAxisIndex")
        private Integer yAxisIndex;
        /**
         * 具体值
         */
        private List<Object> data;
    }
}
