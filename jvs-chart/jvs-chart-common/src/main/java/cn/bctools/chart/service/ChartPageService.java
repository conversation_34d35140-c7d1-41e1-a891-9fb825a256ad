package cn.bctools.chart.service;

import cn.bctools.chart.dto.MonomerDto;
import cn.bctools.chart.entity.ChartPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 页面配置
 *
 * <AUTHOR>
 */
public interface ChartPageService extends IService<ChartPage> {

    /**
     * 通过json字符串进行数据的替换
     *
     * @param json       整个设计数据
     * @param monomerDto 查询条件  联动  下钻
     * @param chartName  名称
     * @return 替换后的json数据
     * <AUTHOR>
     * @Time 19:58
     **/
    String dataTranslation(String json, MonomerDto monomerDto, String chartName);

}
