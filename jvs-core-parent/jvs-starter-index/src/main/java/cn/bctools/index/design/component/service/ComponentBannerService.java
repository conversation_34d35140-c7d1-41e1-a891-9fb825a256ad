package cn.bctools.index.design.component.service;

import cn.bctools.index.design.component.ComponentBanner;
import cn.bctools.index.design.render.ComponentBannerRender;
import cn.bctools.index.dto.FormQueryParamsDto;

import java.util.List;
import java.util.Map;

/**
 * banner
 */
public interface ComponentBannerService extends ComponentBaseService {

    @Override
    ComponentBanner generate();

    @Override
    ComponentBannerRender fillData(List<FormQueryParamsDto> param, Map<String, Object> componentMetaData);
}
