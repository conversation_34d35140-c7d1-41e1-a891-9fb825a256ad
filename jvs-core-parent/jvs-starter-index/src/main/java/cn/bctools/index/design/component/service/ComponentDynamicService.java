package cn.bctools.index.design.component.service;

import cn.bctools.index.design.component.ComponentDynamic;
import cn.bctools.index.design.render.ComponentDynamicRender;
import cn.bctools.index.dto.FormQueryParamsDto;

import java.util.List;
import java.util.Map;

/**
 * 步骤条
 */
public interface ComponentDynamicService extends ComponentBaseService  {

    @Override
    ComponentDynamic generate();

    @Override
    ComponentDynamicRender fillData(List<FormQueryParamsDto> param, Map<String, Object> componentMetaData);
}
