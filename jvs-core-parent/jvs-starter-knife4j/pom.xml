<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.bctools</groupId>
        <artifactId>jvs-core-parent</artifactId>
        <version>2.1.8</version>
    </parent>

    <groupId>cn.bctools</groupId>
    <artifactId>jvs-starter-knife4j</artifactId>

    <dependencies>
        <!--Unable to infer base url. This is common when using dynamic servlet registration or when the API is behind an API Gateway. The base url is the root of where all the swagger resources are served. For e.g. if the api is available at http://example.org/api/v2/api-docs then the base url is http://example.org/api/. Please enter the location manually: -->
        <!--       与swagger 冲突, 启动效率高, 开发环境屏蔽此包,否则会出现swagger无法正常使用 其它环境建议开启启动懒加载优化 必须是打包时操作开启此操作-->
        <!--        <dependency>-->
        <!--            <groupId>org.springframework</groupId>-->
        <!--            <artifactId>spring-context-indexer</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>3.0.0</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-common</artifactId>
        </dependency>

    </dependencies>

</project>
