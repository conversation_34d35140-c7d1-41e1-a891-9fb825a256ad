package cn.bctools.oss.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WatermarkOptionDto {
    public enum WatermarkType {
        TEXT_SINGLE,
        TEXT_TILE,
    }

    /**
     * 水印文字
     */
    public String text = "";

    /**
     * 水印类型
     */
    public WatermarkType type = WatermarkType.TEXT_SINGLE;

    /** 字体大小 */
    public int fontSize = 18;

    /** 不透明度 */
    public float alpha = 1f;

    /** 角度，以右边为起点，往上 0 到 -180，往下 0 到 180 */
    public double angle = 45;

    /** 颜色 */
    public String color = "#808080";

    /** 平铺间距 */
    public int tileSpacing = 100;

    /** 是否加粗 */
    public boolean isBold = false;

    public WatermarkOptionDto(String text, WatermarkType type, int fontSize, float alpha, double angle,
                              String color, int tileSpacing, boolean isBold) {
        this.text = text;
        this.type = type;
        this.fontSize = fontSize;
        this.alpha = alpha;
        this.angle = angle;
        this.color = color;
        this.tileSpacing = tileSpacing;
        this.isBold = isBold;
    }
}
