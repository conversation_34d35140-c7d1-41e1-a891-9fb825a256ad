package cn.bctools.oss.template;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanToMapUtils;
import cn.bctools.common.utils.FileUtils;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.oss.cons.OssSystemCons;
import cn.bctools.oss.dto.BaseFile;
import cn.bctools.oss.dto.Etag;
import cn.bctools.oss.props.OssProperties;
import cn.bctools.oss.service.FileDataInterface;
import cn.bctools.oss.utils.OssUtils;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.util.IOUtils;
import com.obs.services.ObsClient;
import com.obs.services.ObsConfiguration;
import com.obs.services.exception.ObsException;
import com.obs.services.model.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class ObsTemplate implements OssTemplate {
    private final ObsClient obsClient;
    private final OssProperties ossProperties;
    private final FileDataInterface fileDataInterface;

    private static String POLICY = "{\n" +
//            "    \"Version\":\"1.0\",\n" +
            "    \"Statement\":[\n" +
            "%s" +
            "  ]\n" +
            "}";

    private static String STATEMENT = "{\n" +
            "      \"Sid\":\"%s\",\n" +
            "      \"Effect\":\"Allow\",\n" +
            "      \"Principal\": \"*\",\n" +
            "      \"Action\":[\"s3:GetObject\"],\n" +
            "      \"Resource\":[\"arn:aws:s3:::%s/%s/*\"]\n" +
            "    }";

    private static String SID = "Sid";

    private static String STATE = "Statement";

    private String bucket() {
        return ossProperties.getBucketName();
    }

    public ObsTemplate(OssProperties ossProperties, FileDataInterface fileDataInterface) {
        this.ossProperties = ossProperties;
        this.fileDataInterface = fileDataInterface;

        // 可以通过环境变量获取访问密钥AK/SK，也可以使用其他外部引入方式传入。如果使用硬编码可能会存在泄露风险。
        // 可以登录访问管理控制台获取访问密钥AK/SK
        String ak = ossProperties.getAccessKey();
        String sk = ossProperties.getSecretKey();

        // endpoint填写桶所在的endpoint, 此处以华北-北京四为例，其他地区请按实际情况填写。
        String endPoint = ossProperties.getEndpoint();

        // 可以通过环境变量获取endPoint，也可以使用其他外部引入方式传入。
        //String endPoint = System.getenv("ENDPOINT");

        // 创建ObsClient实例
        // 使用永久AK/SK初始化客户端
        ObsConfiguration config = new ObsConfiguration();
        config.setEndPoint(endPoint);
        //自定义域名需设置Cname
//        config.setCname(true);
        this.obsClient = new ObsClient(ak, sk, config);
        List<String> publicBuckets = ossProperties.getPublicBuckets();
        //初始化公开桶信息
        if (ObjectNull.isNotNull(publicBuckets)) {
            StringBuilder statements = new StringBuilder();
            try {
                String edf = obsClient.getBucketPolicy(bucket());
                JSONObject obj = JSON.parseObject(edf);
                List<JSONObject> statement = obj.getJSONArray(STATE).toJavaList(JSONObject.class);
                for (String folderName : publicBuckets) {
                    boolean flag = statement.stream().anyMatch(e -> folderName.equals(e.getString(SID)));
                    if (!flag) {
                        statement.add(JSONObject.parseObject(String.format(STATEMENT, folderName, bucket(), folderName)));
                        obj.put(STATE, statement);
                    }
                }
                obsClient.setBucketPolicy(bucket(), JSON.toJSONString(obj));
            } catch (ObsException e) {
                if ("NoSuchBucketPolicy".equals(e.getErrorCode())) {
                    log.warn("NoSuchBucketPolicy：{}", bucket());
                    for (int i = 0; i < publicBuckets.size(); i++) {
                        //生成策略，只有一个桶，根据文件夹策略区分权限
                        String folderName = publicBuckets.get(i);
                        String statement = String.format(STATEMENT, folderName, bucket(), folderName);
                        statements.append(statement);
                        if (i != publicBuckets.size() - 1) {
                            statements.append(",");
                        }
                    }
                    obsClient.setBucketPolicy(bucket(), String.format(POLICY, statements));
                } else {
                    log.error("创建公有读文件夹失败：", e);
                    throw e;
                }
            }
        }
    }

    //创建私有读写桶
    @Override
    public void makeBucket(String bucketName) {
        if (!bucketExists(bucketName)) {
            obsClient.createBucket(bucketName, ossProperties.getLocation());
        }
    }

    //创建公共桶
    private void makePublicBucket(String bucketName) {
        if (!bucketExists(bucketName)) {
            CreateBucketRequest request = new CreateBucketRequest();
            request.setBucketName(bucketName);
            request.setLocation(ossProperties.getLocation());
            // 设置桶的 ACL 公有读
            request.setAcl(AccessControlList.REST_CANNED_PUBLIC_READ);

            obsClient.createBucket(request);
        }
    }

    @Override
    public boolean bucketExists(String bucketName) {
        return obsClient.headBucket(bucketName);
    }


    @Override
    public void listObjects(String bucketName, String path, String label) {
        //暂时不刷新
    }

    @Override
    public String fileLink(String fileName, String bucketName) {
        //判断是否公开桶
        if (ossProperties.getPublicBuckets().contains(bucketName)) {
            if (StrUtil.isNotBlank(ossProperties.getAccessEndpoint())) {
                return ossProperties.getAccessEndpoint()  + OssUtils.generateObsFileKeyUrl(bucketName, fileName);
            }
            // 自定义拼接，处理url转义
            return String.format("https://%s.%s/%s", bucket(), ossProperties.getEndpoint(), OssUtils.generateObsFileKeyUrl(bucketName, fileName));
        }
        // 生成签名URL
        long expirationTime = ossProperties.getTimelinessHour() * 60 * 60;
        TemporarySignatureRequest request = new TemporarySignatureRequest(HttpMethodEnum.GET, expirationTime);
        request.setBucketName(bucket());
        request.setObjectKey(OssUtils.generateObsFileKey(bucketName, fileName));

        // 创建临时签名
        TemporarySignatureResponse response = obsClient.createTemporarySignature(request);
        String signedUrl = response.getSignedUrl();
        if (StrUtil.isNotBlank(ossProperties.getAccessEndpoint())) {
            signedUrl = ossProperties.getAccessEndpoint() + signedUrl.substring(signedUrl.indexOf(bucketName));
        }
        return signedUrl;
    }

    @SneakyThrows
    @Override
    public BaseFile put(String bucketName, String businessId, String module, InputStream stream, String key, boolean cover) {
        if (!FileUtils.checkBucket(ossProperties.getAllowBuckets(), bucketName)) {
            throw new BusinessException("不支持的上传");
        }

        ObjectMetadata metadata = new ObjectMetadata();

        Long size = (long) stream.available();
//        makeBucket(bucketName);
        String originalName = key;
        key = getFileName(module, key, businessId);

        int i = key.lastIndexOf(".") + 1;
        String substring = key.substring(i);

        String contentType = MIME_MAPPINGS.get(substring);
        if (contentType == null) {
            contentType = MediaType.APPLICATION_OCTET_STREAM_VALUE;
        }
        String s3Key = key.replaceAll("//", "/");
        metadata.setContentType(contentType);
        metadata.setContentLength(size);
        PutObjectResult putObjectResult = obsClient.putObject(bucket(), OssUtils.generateObsFileKey(bucketName, s3Key), new ByteArrayInputStream(IOUtils.toByteArray(stream)), metadata);

        BaseFile file = new BaseFile();
        file.setOriginalName(originalName);
        file.setFileName(s3Key);
        file.setBucketName(bucketName);
        file.setFileType(contentType);
        file.setModule(module);
        file.setSize(size);
        file.setUrl(putObjectResult.getObjectUrl());
        //记录信息
        fileDataInterface.insert(file);
        return file;
    }

    @Override
    public BaseFile put(String bucketName, String module, InputStream stream, String key, boolean cover) {
        return put(bucketName, null, module, stream, key, cover);
    }

    @Override
    public void removeFile(String bucketName, String fileName) {
        obsClient.deleteObject(bucket(), OssUtils.generateObsFileKey(bucketName, fileName));
    }

    @Override
    public InputStream getObject(String bucketName, String fileName) {
        return obsClient.getObject(bucket(), OssUtils.generateObsFileKey(bucketName, fileName)).getObjectContent();
    }

    @Override
    public List<BaseFile> listFiles(String bucketName) {
        return new ArrayList<>();
    }

    /**
     * 分片处理文件上传
     *
     * @param bucketName 桶
     * @param filename   文件名
     * @return 分片上传ID
     */
    @Override
    public String createMultipartUpload(String bucketName, String filename) {
        InitiateMultipartUploadRequest request = new InitiateMultipartUploadRequest(bucket(), OssUtils.generateObsFileKey(bucketName, filename));
        return obsClient.initiateMultipartUpload(request).getUploadId();
    }

    @Override
    public Etag uploadPart(byte[] bytes, String bucketName, Integer partNumber, String key, String uploadId) {

        UploadPartRequest request = new UploadPartRequest();
        request.setUploadId(uploadId);
        String fileKey = OssUtils.generateObsFileKey(bucketName, key);
        request.setObjectKey(fileKey);
        request.setPartNumber(partNumber);
        request.setInput(new ByteArrayInputStream(bytes));
        request.setPartSize((long) bytes.length);
        request.setBucketName(bucket());
        UploadPartResult uploadPartResult = obsClient.uploadPart(request);
        return new Etag().setEtag(uploadPartResult.getEtag()).setPartNumber(partNumber).setBucketName(bucket()).setFileName(fileKey).setUploadId(uploadId);
    }

    @Override
    public void completeMultipartUpload(String bucketName, String filename, String uploadId, Set<Etag> etagList) {
        CompleteMultipartUploadRequest request = new CompleteMultipartUploadRequest();
        request.setObjectKey(OssUtils.generateObsFileKey(bucketName, filename));
        request.setBucketName(bucket());
        request.setUploadId(uploadId);
        request.setPartEtag(etagList.stream().map(e -> new PartEtag(e.getEtag(), e.getPartNumber())).collect(Collectors.toList()));
        obsClient.completeMultipartUpload(request);
    }

    @Override
    public void abortMultipartUpload(String bucketName, String filename, String uploadId) {
        AbortMultipartUploadRequest abortMultipartUploadRequest = new AbortMultipartUploadRequest(bucket(), OssUtils.generateObsFileKey(bucketName, filename), uploadId);
        obsClient.abortMultipartUpload(abortMultipartUploadRequest);
    }

    @Override
    public String fileJvsPublicLink(String fileName) {
        return fileLink(fileName, OssSystemCons.OSS_BUCKET_NAME);
    }

    @Override
    public Map<String, Object> copyFile(String sourceBucketName, String sourceKey, String destinationBucketName, String originalName, String module) {
        String destinationKey = getFileName(module, originalName, null);
        destinationKey = OssUtils.generateObsFileKey(destinationBucketName, destinationKey);
        CopyObjectResult copyObjectResult = obsClient.copyObject(bucket(), OssUtils.generateObsFileKey(sourceBucketName, sourceKey),
                bucket(), OssUtils.generateObsFileKey(destinationBucketName, destinationKey));
        Map<String, Object> map = BeanToMapUtils.beanToMap(copyObjectResult);
        map.put("filePath", destinationKey);
        return map;
    }

    @Override
    public Map<String, Object> getMetaObject(String bucketName, String key) {
        ObjectMetadata metadata = obsClient.getObjectMetadata(bucket(), OssUtils.generateObsFileKey(bucketName, key));
        return BeanToMapUtils.beanToMap(metadata);
    }

    @SneakyThrows
    @Override
    public BaseFile putWithoutRandomId(String bucketName, String module, InputStream stream, String key, boolean cover) {
        if (!FileUtils.checkBucket(ossProperties.getAllowBuckets(), bucketName)) {
            throw new BusinessException("不支持的上传");
        }

        ObjectMetadata metadata = new ObjectMetadata();

        Long size = (long) stream.available();
//        makeBucket(bucketName);
        String originalName = key;
        key = getFileNameWithoutRandomId(module, key);

        int i = key.lastIndexOf(".") + 1;
        String substring = key.substring(i);

        String contentType = MIME_MAPPINGS.get(substring);
        if (contentType == null) {
            contentType = MediaType.APPLICATION_OCTET_STREAM_VALUE;
        }
        String s3Key = key.replaceAll("//", "/");
        metadata.setContentType(contentType);
        obsClient.putObject(bucket(), OssUtils.generateObsFileKey(bucketName, s3Key), new ByteArrayInputStream(IOUtils.toByteArray(stream)), metadata);

        BaseFile file = new BaseFile();
        file.setOriginalName(originalName);
        file.setFileName(s3Key);
        file.setBucketName(bucketName);
        file.setFileType(contentType);
        file.setModule(module);
        file.setSize(size);
        //记录信息
        fileDataInterface.insert(file);
        return file;
    }

    @Override
    public Boolean isPublicBucket(String bucketName) {
        return ossProperties.getPublicBuckets().contains(bucketName);
    }
}