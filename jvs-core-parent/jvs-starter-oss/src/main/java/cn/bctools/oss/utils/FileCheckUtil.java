package cn.bctools.oss.utils;

import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import org.apache.tika.Tika;
import org.apache.tika.mime.MimeType;
import org.apache.tika.mime.MimeTypes;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * 文件内容检测工具类
 */
@UtilityClass
public class FileCheckUtil {

    private static final Tika tika = new Tika();

    private static final MimeTypes allTypes = MimeTypes.getDefaultMimeTypes();

    // 常见的 WebShell 和脚本关键字（不区分大小写）
    private static final List<String> ILLEGAL_PATTERNS = Arrays.asList(
            "<%",
            "%>",
            "<?",
            "?>",
            "eval(",
            "assert(",
            "system(",
            "exec(",
            "shell_exec",
            "passthru",
            "base64_decode",
            "gzinflate",
            "phpcode",
            "file_put_contents",
            "fopen",
            "fwrite"
    );

    @SneakyThrows
    public String getFileExt(InputStream inputStream) {
        // 此处检测文件内容,返回文件MimeType名称
        String mimeTypeStr = tika.detect(inputStream);

        // 获取tika提供的默认参照表
        // 可以进行自定义,参照https://stackoverflow.com/questions/13650372/how-to-determine-appropriate-file-extension-from-mime-type-in-java
        MimeTypes allTypes = MimeTypes.getDefaultMimeTypes();
        // 根据MimeType名称获取MimeType类型
        MimeType mimeType = allTypes.forName(mimeTypeStr);
        // 根据MimeType类型获取对应的后缀名
        return mimeType.getExtension();
    }

    public void checkImgContent(InputStream inputStream) {

    }

    /**
     * 扫描图片文件内容是否包含非法脚本内容
     * @param file 上传的文件
     * @throws SecurityException 如果发现非法内容
     */
    public static void scan(InputStream inputStream) throws SecurityException {
        String content;
        try {
            // 使用 ISO-8859-1 编码确保不会丢字节（比 UTF-8 更安全，用于扫描）
            content = new String(file.getBytes(), StandardCharsets.ISO_8859_1);
        } catch (Exception e) {
            throw new SecurityException("无法读取文件内容");
        }

        String lowerContent = content.toLowerCase(); // 转小写便于匹配

        for (String pattern : ILLEGAL_PATTERNS) {
            if (lowerContent.contains(pattern.toLowerCase())) {
                throw new SecurityException("检测到非法内容：" + pattern);
            }
        }
    }
}