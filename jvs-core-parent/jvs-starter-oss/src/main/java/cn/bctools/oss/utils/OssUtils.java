package cn.bctools.oss.utils;

import cn.bctools.common.utils.SystemThreadLocal;
import lombok.SneakyThrows;

import java.net.URLEncoder;

/**
 * 将业务数据的文件归纳到一个目录下
 */
public class OssUtils {
    /**
     * 将业务类型和业务数据值放到上下文中
     *
     * @param type       业务类型
     * @param businessId 业务值
     */
    public static void setOssTemplateBusinessId(String type, String businessId) {
        //根据目录进行分离
        SystemThreadLocal.set("ossbusinessId", "jvs_" + businessId + "/" + type + "/");
    }

    public static Object getOssTemplateBusinessId() {
        return SystemThreadLocal.get("ossbusinessId");
    }

    /**
     * 生成obs文件key
     */
    public static String generateObsFileKey(String bucketName, String fileName) {
        String s = bucketName + "/" + fileName;
        return s.replace("//", "/");
    }

    /**
     * 生成obs转义的文件key
     */
    @SneakyThrows
    public static String generateObsFileKeyUrl(String bucketName, String fileName) {
        String obsFileKey = generateObsFileKey(bucketName, fileName);
        // 找到最后一个/的索引
        int lastSlashIndex = obsFileKey.lastIndexOf('/');

        // 前面
        String base = obsFileKey.substring(0, lastSlashIndex + 1);
        // 后面
        String toEncode = obsFileKey.substring(lastSlashIndex + 1);

        // 仅对后面进行URL编码
        String encodedPart = URLEncoder.encode(toEncode, "UTF-8");
        return base + encodedPart;
    }
}