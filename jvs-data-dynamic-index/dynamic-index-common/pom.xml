<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.bctools</groupId>
        <artifactId>jvs-data-dynamic-index</artifactId>
        <version>2.1.8</version>
    </parent>

    <artifactId>dynamic-index-common</artifactId>
    <version>2.1.8</version>

    <dependencies>
        <!--文件上传服务-->
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-oss</artifactId>
            <version>${jvs.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-oauth2</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-redis</artifactId>
            <version>${jvs.version}</version>
        </dependency>

        <!--只引用数据，不引限用数据权-->
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-database</artifactId>
            <version>${jvs.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-knife4j</artifactId>
            <version>${jvs.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-oauth2</artifactId>
            <version>${jvs.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>cn.bctools</groupId>-->
<!--            <artifactId>jvs-starter-license</artifactId>-->
<!--            <version>${jvs.version}</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>message-push-dto</artifactId>
            <version>${jvs.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-job</artifactId>
            <version>${jvs.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>message-push-api</artifactId>
            <version>${jvs.version}</version>
        </dependency>

        <!--websocket 站内消息-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-index</artifactId>
            <version>2.1.8</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.3.2</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>