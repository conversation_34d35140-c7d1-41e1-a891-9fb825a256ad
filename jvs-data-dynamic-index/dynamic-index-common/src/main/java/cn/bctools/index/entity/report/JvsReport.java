package cn.bctools.index.entity.report;

import cn.bctools.database.entity.po.BasalPo;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 数据报表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Data
@ApiModel("数据报表")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "jvs_report", autoResultMap = true)
public class JvsReport extends BasalPo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("设计")
    @TableField("name")
    private String name;
    @ApiModelProperty("排序")
    private Integer sort;
    @ApiModelProperty("渲染json")
    private String viewJson;
    @NotBlank(message = "类型不能为空")
    @ApiModelProperty("分类")
    private String type;
    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("数据源")
    @TableField(typeHandler = FastjsonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private List<JSONObject> dataSource;

    @ApiModelProperty("权限设置")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<JSONObject> role;
    @ApiModelProperty("权限类型,true 应用 权限，false 自定义权限")
    Boolean roleType;
}
