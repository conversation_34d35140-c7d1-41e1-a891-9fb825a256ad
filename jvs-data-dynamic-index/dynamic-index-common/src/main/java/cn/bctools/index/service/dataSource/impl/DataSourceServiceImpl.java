package cn.bctools.index.service.dataSource.impl;

import cn.bctools.index.entity.dataSource.DataSource;
import cn.bctools.index.mapper.dataSource.DataSourceMapper;
import cn.bctools.index.service.dataSource.DataSourceService;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 数据源配置信息
 */
@Service
@Slf4j
@DS("jvs_data_source")
public class DataSourceServiceImpl extends ServiceImpl<DataSourceMapper, DataSource> implements DataSourceService {
}
