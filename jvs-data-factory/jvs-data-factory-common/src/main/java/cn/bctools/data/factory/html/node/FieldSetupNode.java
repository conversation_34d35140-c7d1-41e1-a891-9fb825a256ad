package cn.bctools.data.factory.html.node;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.data.factory.dto.FieldDisposeFactoryReturnDto;
import cn.bctools.data.factory.entity.enums.FieldSetupReplaceEnum;
import cn.bctools.data.factory.enums.DataFieldTypeEnum;
import cn.bctools.data.factory.field.FieldDisposeFactory;
import cn.bctools.data.factory.html.DataSourceField;
import cn.bctools.data.factory.html.FData;
import cn.bctools.data.factory.html.FNodeType;
import cn.bctools.data.factory.html.node.params.FieldSetupParams;
import cn.bctools.data.factory.html.run.Frun;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 字段设置
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class FieldSetupNode implements Frun<FieldSetupParams> {
    private final FieldDisposeFactory fieldDisposeFactory;


    @Override
    public FData run(Boolean formal, Map<String, FData> linkBody, FieldSetupParams fieldSetupParams) {
        String next = linkBody.keySet().iterator().next();
        List<FieldSetupParams.FieldSetup> list = fieldSetupParams.getSourceData().getFieldObj();
        //合并字段  因为这里可能存在前面的节点字段新增 这里只有行转列节点才会存在 因为行转列在设计时不是所有数据  转为后的数据title 会存在缺失
        if (linkBody.get(next).getType().equals(FNodeType.table)) {
            List<DataSourceField> title = linkBody.get(next).getTitle();
            List<FieldSetupParams.FieldSetup> setups = title.stream()
                    .filter(e -> list.stream().noneMatch(v -> v.getFieldKey().equals(e.getFieldKey())))
                    .map(e -> BeanCopyUtil.copy(e, FieldSetupParams.FieldSetup.class))
                    .collect(Collectors.toList());
            list.addAll(setups);
        }
        //防止没有数据
        FData fData = new FData().setTitle(new ArrayList<>());
        Long current = 1L;
        //中间表名称
        String nodeDocumentName = fieldSetupParams.getTableName();
        fData.setDocumentName(nodeDocumentName);
        //获取上一个节点的表名称
        String documentName = linkBody.get(next).getDocumentName();
        while (true) {
            List<Map<String, Object>> maps = this.getData(documentName, current, formal, new Query());
            if (maps.isEmpty()) {
                break;
            }
            //只需要显示的数据
            maps = maps.stream().map(e -> {
                HashMap<String, Object> hashMap = new HashMap<>(list.size());
                list.forEach(v -> {
                    String fieldKey = v.getFieldKey();
                    hashMap.put(fieldKey, e.get(fieldKey));
                });
                return hashMap;
            }).collect(Collectors.toList());
            //值替换
            replaceValue(list, maps);
            //如果字段属性变更为时间或者时间格式变更后 转换一下
            List<FieldSetupParams.FieldSetup> isChange = list.stream().filter(e -> (e.getFieldType().equals(DataFieldTypeEnum.时间) || e.getFieldType().equals(DataFieldTypeEnum.数字)) && Optional.ofNullable(e.getPropertyIsChange()).orElse(Boolean.FALSE) && e.getIsShow()).collect(Collectors.toList());
            if (!isChange.isEmpty()) {
                maps.forEach(e ->
                        isChange.forEach(v -> {
                            Object o = e.get(v.getFieldKey());
                            if (ObjectUtil.isNotEmpty(o)) {
                                if (v.getFieldType().equals(DataFieldTypeEnum.时间)) {
                                    if (NumberUtil.isLong(o.toString())) {
                                        Timestamp timestamp = new Timestamp(NumberUtil.parseLong(o.toString()));
                                        e.put(v.getFieldKey(), DateUtil.format(timestamp, v.getFormat()));
                                    } else {
                                        e.put(v.getFieldKey(), DateUtil.format(DateUtil.parse(o.toString(), v.getFormat()), v.getFormat()));
                                    }
                                } else if (v.getFieldType().equals(DataFieldTypeEnum.数字)) {
                                    if (NumberUtil.isInteger(o.toString())) {
                                        e.put(v.getFieldKey(), new Integer(o.toString()));
                                    } else if (NumberUtil.isLong(o.toString())) {
                                        e.put(v.getFieldKey(), new Long(o.toString()));
                                    } else if (NumberUtil.isDouble(o.toString())) {
                                        e.put(v.getFieldKey(), new Double(o.toString()));
                                    }
                                }else if (v.getFieldType().equals(DataFieldTypeEnum.字符串)){
                                    e.put(v.getFieldKey(),o.toString());
                                }
                            }
                        }));
            }
            //头部
            List<DataSourceField> sourceFields = list.stream().map(e -> BeanCopyUtil.copy(e, DataSourceField.class)).collect(Collectors.toList());
            //扩展数据处理
            FieldDisposeFactoryReturnDto input = fieldDisposeFactory.input(sourceFields, maps);
            //入库
            this.save(input.getData(), nodeDocumentName);
            fData.setTitle(input.getTitle());
            current++;
        }
        return fData;
    }


    /***
     * 值替换
     * @param dataList  值
     * @param list 字段属性
     * */
    private void replaceValue(List<FieldSetupParams.FieldSetup> list, List<Map<String, Object>> dataList) {
        //先替换值 因为可能存在排序值替换只支持 字符串 数字 时间
        List<FieldSetupParams.FieldSetup> replaceField = list.stream().filter(e -> e.getReplaceList() != null && !e.getReplaceList().isEmpty()).collect(Collectors.toList());
        if (replaceField.isEmpty()) {
            return;
        }
        dataList.forEach(e ->
                replaceField.forEach(v ->
                        v.getReplaceList().forEach(x -> {
                            FieldSetupReplaceEnum replaceEnum = x.getReplaceEnum();
                            Object o = e.get(v.getFieldKey());
                            if (replaceEnum.equals(FieldSetupReplaceEnum.replaceNull)) {
                                if (ObjectUtil.isEmpty(o)) {
                                    e.put(v.getFieldKey(), x.getReplaceValue());
                                }
                            } else {
                                if (ObjectUtil.isNotEmpty(o)) {
                                    String replaceOriginalValue = x.getReplaceOriginalValue();
                                    boolean isEquals = Boolean.FALSE;
                                    if (v.getFieldType() == DataFieldTypeEnum.数字) {
                                        BigDecimal bigDecimal = new BigDecimal(replaceOriginalValue);
                                        BigDecimal bigDecimal1 = new BigDecimal(o.toString());
                                        isEquals = NumberUtil.equals(bigDecimal1, bigDecimal);
                                    } else if (v.getFieldType() == DataFieldTypeEnum.字符串) {
                                        isEquals = o.toString().equals(replaceOriginalValue);
                                    } else if (v.getFieldType() == DataFieldTypeEnum.时间) {
                                        DateTime parse1 = DateUtil.parse(o.toString(), v.getFormat());
                                        DateTime parse2 = DateUtil.parse(replaceOriginalValue, v.getFormat());
                                        isEquals = DateUtil.isSameTime(parse1, parse2);
                                    }
                                    if (isEquals) {
                                        e.put(v.getFieldKey(), x.getReplaceValue());
                                    }
                                }
                            }
                        })
                )
        );
    }

}
