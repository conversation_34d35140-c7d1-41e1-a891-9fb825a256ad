package cn.bctools.data.factory.html.node;


import cn.bctools.data.factory.html.FData;
import cn.bctools.data.factory.html.node.params.FilterParams;
import cn.bctools.data.factory.html.run.Frun;
import cn.bctools.data.factory.query.QueryExecuteFactory;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 数据过滤
 *
 * <AUTHOR>
 */

@Data
@Service
@AllArgsConstructor
public class FilterNode implements Frun<FilterParams> {

    private final QueryExecuteFactory queryExecuteFactory;
    private final MongoTemplate mongoTemplate;

    @Override
    public FData run(Boolean formal, Map<String, FData> linkBody, FilterParams filterParams) {
        String next = linkBody.keySet().iterator().next();
        FData fData = new FData();
        //中间表名称
        String nodeDocumentName = filterParams.getTableName();
        fData.setDocumentName(nodeDocumentName);
        //当前页码
        Long current = 1L;
        //上一个节点的数据库名称
        String documentName = linkBody.get(next).getDocumentName();
        while (true) {
            //获取数据
            List<Map<String, Object>> data = this.getData(documentName, current, formal, new Query());
            if (data.isEmpty()) {
                break;
            }
            List<Map<String, Object>> execute = queryExecuteFactory.execute(data.stream(), filterParams);
            this.save(execute, nodeDocumentName);
            current++;
        }
        return fData.setTitle(linkBody.get(next).getTitle());
    }
}
