package cn.bctools.data.factory.html.node;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.data.factory.config.CommonConfig;
import cn.bctools.data.factory.constant.Constant;
import cn.bctools.data.factory.entity.JvsDataFactory;
import cn.bctools.data.factory.field.FieldDisposeFactory;
import cn.bctools.data.factory.html.DataSourceField;
import cn.bctools.data.factory.html.FData;
import cn.bctools.data.factory.html.dto.InputNodeGetDataDto;
import cn.bctools.data.factory.html.node.dto.InParameterDto;
import cn.bctools.data.factory.html.node.params.InputParams;
import cn.bctools.data.factory.html.run.Frun;
import cn.bctools.data.factory.util.SystemTool;
import cn.bctools.data.source.api.DataSourceApi;
import cn.bctools.data.source.entity.enums.InParameterTypeEnums;
import cn.bctools.data.source.entity.po.InParameterJsonPo;
import cn.bctools.data.source.entity.po.SettingJsonPo;
import cn.bctools.data.source.param.DataSourceApiDto;
import cn.bctools.data.source.param.RequestSourceDto;
import cn.bctools.data.source.param.RequestTypeEnum;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * 输入节点
 */
@Data
@Service
public class InputNode implements Frun<InputParams> {

    private final DataSourceApi datasourceApi;
    private final FieldDisposeFactory fieldDisposeFactory;
    private final CommonConfig commonConfig;
    private final MongoTemplate mongoTemplate;


    @Override
    public FData run(Boolean formal, Map linkBody, InputParams nodeHtml) {
        InputParams.FromSource fromSource = nodeHtml.getSourceData().getFromSource();
        Long size = fromSource.getSize();
        //是否为测试阶段 输入用户可以自定义 查询条数 不能超过1000条 默认为 100
        if (ObjectUtil.isNull(size)) {
            size = 0L;
            if (!formal) {
                size = 50L;
            }
        }
        JvsDataFactory jvsDataFactory = null;
        //如果是正式运行需要设置每页数量
        if (formal) {
            size = commonConfig.getNodeExecuteNumber();
            SystemTool<JvsDataFactory> tool = new SystemTool<>();
            jvsDataFactory = tool.get(Constant.SYSTEM_NOW_JVS_DATA_FACTORY);
        }
        //数据库名称
        String documentName = nodeHtml.getTableName();
        FData fData = new FData().setDocumentName(documentName);
        fromSource.setSize(size);
        //获取的数据量
        Long count = 0L;
        //页码
        Long curren = 1L;
        //获取表结构
        List<DataSourceField> fieldList = nodeHtml.getFieldList();

        while (true) {
            InputNodeGetDataDto data = this.getData(fromSource, nodeHtml.getId(), formal, curren, jvsDataFactory);
            List<Map<String, Object>> list = data.getData();
            //数据过滤只需要返回用户选中的key
            List<String> keys = fieldList.stream().filter(e -> !e.getIsShow()).map(DataSourceField::getFieldKey).collect(Collectors.toList());
            if (!keys.isEmpty()) {
                list = list.stream()
                        .peek(e -> keys.forEach(e::remove))
                        .collect(Collectors.toList());
            }
            //数据入库
            this.save(list, documentName);
            count += data.getTotal();
            //判断是否可以结束
            if (data.getIsAccomplish()) {
                break;
            }
            if (!formal && count >= size) {
                break;
            }
            curren++;
        }
        return fData.setTitle(fieldList);
    }

    /**
     * 获取数据
     *
     * @param current    当前页码
     * @param fromSource 数据源对应的请求入参
     * @param nodeId     节点id
     * @param formal     是否为设计时的数据获取
     */
    private InputNodeGetDataDto getData(InputParams.FromSource fromSource, String nodeId, Boolean formal, Long current, JvsDataFactory jvsDataFactory) {
        InputNodeGetDataDto nodeGetDataDto = new InputNodeGetDataDto()
                .setIsAccomplish(Boolean.FALSE);
        SettingJsonPo settingJson = fromSource.getSettingJson();
        Long size = fromSource.getSize();
        DataSourceApiDto dataSourceApiDto = new DataSourceApiDto()
                .setDataSourceId(fromSource.getDataSourceId())
                .setSourceType(fromSource.getSourceType())
                .setSize(size)
                .setCurrent(current)
                .setId(fromSource.getId());
        if (fromSource.getInParameterJson() != null && !fromSource.getInParameterJson().isEmpty()) {
            //获取入参
            List<InParameterJsonPo> inParameterJson = fromSource.getInParameterJson();
            dataSourceApiDto.setInParameterJson(inParameterJson);
        }
        //如果是正式运行直接获取线程中的入参
        if (formal) {
            SystemTool<List<InParameterDto>> tool = new SystemTool<>();
            List<InParameterDto> inParameter = Optional.ofNullable(tool.get("inParameter")).orElse(new ArrayList<>());
            List<InParameterDto> parameterDtos = inParameter.stream().filter(e -> e.getInParameterTypeEnums().equals(InParameterTypeEnums.ordinary))
                    .filter(e -> e.getNodeId().equals(nodeId))
                    .collect(Collectors.toList());
            long count = parameterDtos.stream().filter(InParameterJsonPo::getRequiredIs).filter(e -> ObjectUtil.isEmpty(e.getValue())).count();
            if (count > 0) {
                throw new BusinessException("检测到入参存在未填写的数据");
            }
            List<InParameterJsonPo> collect = parameterDtos.stream().map(e -> BeanCopyUtil.copy(e, InParameterJsonPo.class)).collect(Collectors.toList());
            dataSourceApiDto.setInParameterJson(collect);
            //正式运行设置请求来源
            RequestSourceDto requestSourceDto = new RequestSourceDto()
                    .setType(RequestTypeEnum.dataFactory)
                    .setDesignId(jvsDataFactory.getId())
                    .setDesignName(jvsDataFactory.getName())
                    .setApplyName("数据集");
            dataSourceApiDto.setRequestSourceDto(requestSourceDto);
        }
        //存在分页的情况 如果存在分页就需要重复请求
        Page<Map<String, Object>> data;
        //todo 这里有个问题 设计阶段用户选择的数量并不一定是最终返回的数据
        if (settingJson != null && settingJson.getPageIs()) {
            //防止每页数量超过最大限制
            if (settingJson.getSizeMax() < size) {
                size = settingJson.getSizeMax().longValue();
            }
            dataSourceApiDto.setSize(size)
                    .setCurrent(current);
            //开始请求
            data = datasourceApi.getData(dataSourceApiDto).getData();
        } else {
            //开启分页处理
            data = datasourceApi.getData(dataSourceApiDto).getData();
        }
        nodeGetDataDto.setTotal(data.getSize());
        long total = data.getTotal();
        long l = total / size;
        //需要判断是否存在余数
        if (total % size > 1) {
            l++;
        }
        if (l < current + 1) {
            nodeGetDataDto.setIsAccomplish(Boolean.TRUE);
        }
        return nodeGetDataDto.setData(data.getRecords());
    }


}
