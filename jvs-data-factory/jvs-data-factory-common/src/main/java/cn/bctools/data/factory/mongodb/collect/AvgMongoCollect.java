package cn.bctools.data.factory.mongodb.collect;

import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.stereotype.Component;

/**
 * 平均
 *
 * <AUTHOR>
 */
@Component
public class AvgMongoCollect implements MongoCollectCondition {
    @Override
    public GroupOperation addCondition(GroupOperation aggregation, String keyName, String asName) {
        return aggregation.avg(keyName).as(asName);
    }
}
