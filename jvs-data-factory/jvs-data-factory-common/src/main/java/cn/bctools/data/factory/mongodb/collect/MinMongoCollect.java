package cn.bctools.data.factory.mongodb.collect;

import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.stereotype.Component;

/**
 * 最小值
 *
 * <AUTHOR>
 */
@Component
public class MinMongoCollect implements MongoCollectCondition {
    @Override
    public GroupOperation addCondition(GroupOperation aggregation, String keyName, String asName) {
        return aggregation.min(keyName).as(asName);
    }

}
