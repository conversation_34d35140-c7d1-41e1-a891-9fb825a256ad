package cn.bctools.data.factory.neo4j.entity;

import cn.bctools.data.factory.enums.DataFactoryNeo4jTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Property;
import org.springframework.data.neo4j.core.schema.Relationship;
import org.springframework.data.neo4j.core.schema.Relationship.Direction;

import java.util.List;

/**
 * <AUTHOR>
 */
@Node("DataFactory")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class DataFactoryNeo4jEntity {

    @Id
    @ApiModelProperty("设计id+下级id通过_分割 保证唯一性")
    private String id;

    @ApiModelProperty("名称 设计名称")
    @Property("title")
    private String title;

    @ApiModelProperty("设计id")
    @Property("designId")
    private String designId;

    @ApiModelProperty("下级名称 图表设计的单个图表名称")
    @Property("subordinateTile")
    private String subordinateTile;

    @ApiModelProperty("下级id 图表设计的单个图表id")
    @Property("subordinateId")
    private String subordinateId;

    @ApiModelProperty("租户id")
    @Property("tenantId")
    private String tenantId;

    @ApiModelProperty("是否被删除")
    @Property("deleteIs")
    private Boolean deleteIs;


    @ApiModelProperty("类型")
    @Property("type")
    private DataFactoryNeo4jTypeEnum type;


    @ApiModelProperty("来源-请求方")
    @Relationship(type = "INCOMING", direction = Direction.INCOMING)
    private List<DataFactoryNeo4jEntity> actorsAndRoles;

    @ApiModelProperty("去向-数据源")
    @Relationship(type = "OUTGOING", direction = Direction.OUTGOING)
    private List<DataFactoryNeo4jEntity> outgoingAndRoles;


}


