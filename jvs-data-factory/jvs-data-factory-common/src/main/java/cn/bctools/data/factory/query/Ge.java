package cn.bctools.data.factory.query;

import cn.bctools.data.factory.enums.DataFieldTypeEnum;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 大于等于
 * <AUTHOR>
 */
@Component
@Slf4j
public class Ge implements Query {
    @Override
    public boolean exec(QueryExecDto queryExecDto) {
        DataFieldTypeEnum dataFieldTypeEnum = queryExecDto.getDataFieldTypeEnum();
        Object value = queryExecDto.getValue();
        String value1 = queryExecDto.getValue1();
        String format = queryExecDto.getFormat();
        if (!check(dataFieldTypeEnum, value, value1)) {
            return false;
        }
        String str = value.toString();
        if (dataFieldTypeEnum.equals(DataFieldTypeEnum.数字)) {
            int compare = NumberUtil.compare(NumberUtil.parseDouble(str), NumberUtil.parseDouble(value1));
            return compare >= BigDecimal.ROUND_UP;
        }
        if (dataFieldTypeEnum.equals(DataFieldTypeEnum.时间)) {
            DateTime dateTime = DateUtil.parse(str, format);
            DateTime dateTime1 = DateUtil.parse(value1, format);
            int compare = DateUtil.compare(dateTime, dateTime1);
            return compare >= BigDecimal.ROUND_UP;
        }
        return false;
    }
}
