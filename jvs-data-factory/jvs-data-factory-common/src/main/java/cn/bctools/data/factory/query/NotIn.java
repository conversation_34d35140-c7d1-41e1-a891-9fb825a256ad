package cn.bctools.data.factory.query;


import cn.bctools.data.factory.enums.DataFieldTypeEnum;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 等于任一
 * <AUTHOR>
 */
@Slf4j
@Component
public class NotIn implements Query {

    @Override
    public boolean exec(QueryExecDto queryExecDto) {
        DataFieldTypeEnum dataFieldTypeEnum = queryExecDto.getDataFieldTypeEnum();
        Object value = queryExecDto.getValue();
        String value1 = queryExecDto.getValue1();
        if (!check(dataFieldTypeEnum, value, value1)) {
            return false;
        }
        //包含分为多个值或者一个值
        JSONArray objects = JSONObject.parseArray(value1);
        if (objects.isEmpty()) {
            return true;
        }
        return objects.stream().noneMatch(e -> StrUtil.equals(StrUtil.toString(value),StrUtil.toString(e)));
    }

    @Override
    public boolean check(DataFieldTypeEnum dataFieldTypeEnum, Object value, String value1) {
        return ObjectUtil.isNotEmpty(value);
    }
}
