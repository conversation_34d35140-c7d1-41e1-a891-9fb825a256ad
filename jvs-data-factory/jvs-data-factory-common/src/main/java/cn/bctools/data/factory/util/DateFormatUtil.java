package cn.bctools.data.factory.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;

import java.math.BigDecimal;
import java.util.List;

public class DateFormatUtil {


    /**
     * 通过时间获取时间格式
     * 目前只适配
     * yyyy-MM-dd HH:mm:ss
     * yyyy
     * yyyy-MM
     * yyyy-MM-dd
     * MM
     * HH:mm:ss
     *
     * @param dateCharSequence 时间字符串
     */
    public static String getFormat(CharSequence dateCharSequence) {
        if (StrUtil.isBlank(dateCharSequence)) {
            return null;
        }
        String dateStr = dateCharSequence.toString();

        //标准日期格式（包括单个数字的日期时间）
        dateStr = normalize(dateStr);
        //如果时间为6为 并且包含-表示这是一个yyyy-MM格式的数据
        if (dateStr.length() == BigDecimal.ROUND_HALF_EVEN) {
            if (StrUtil.count(dateStr, CharUtil.DASHED) == BigDecimal.ROUND_CEILING) {
                return DatePattern.NORM_MONTH_PATTERN;
            } else if (StrUtil.count(dateStr, CharUtil.COLON) == BigDecimal.ROUND_CEILING) {
                return DatePattern.NORM_TIME_PATTERN;
            }
            return null;
        }
        if (dateStr.length() == BigDecimal.ROUND_HALF_UP && NumberUtil.isNumber(dateStr)) {
            return "yyyy";
        }
        if (dateStr.length() == BigDecimal.ROUND_CEILING && NumberUtil.isNumber(dateStr)) {
            return "MM";
        }
        if (ReUtil.isMatch(DatePattern.REGEX_NORM, dateStr)) {
            final int colonCount = StrUtil.count(dateStr, CharUtil.COLON);
            switch (colonCount) {
                case 0:
                    // yyyy-MM-dd
                    return DatePattern.NORM_DATE_PATTERN;
                case 1:
                    // yyyy-MM-dd HH:mm
                    return DatePattern.NORM_DATETIME_MINUTE_PATTERN;
                case 2:
                    // yyyy-MM-dd HH:mm:ss
                    return DatePattern.NORM_DATETIME_PATTERN;
            }
        }
        return null;
    }


    /**
     * 标准化日期，默认处理以空格区分的日期时间格式，空格前为日期，空格后为时间：<br>
     * 将以下字符替换为"-"
     *
     * <pre>
     * "."
     * "/"
     * "年"
     * "月"
     * </pre>
     * <p>
     * 将以下字符去除
     *
     * <pre>
     * "日"
     * </pre>
     * <p>
     * 将以下字符替换为":"
     *
     * <pre>
     * "时"
     * "分"
     * "秒"
     * </pre>
     * <p>
     * 当末位是":"时去除之（不存在毫秒时）
     *
     * @param dateStr 日期时间字符串
     * @return 格式化后的日期字符串
     */
    private static String normalize(CharSequence dateStr) {
        if (StrUtil.isBlank(dateStr)) {
            return StrUtil.str(dateStr);
        }

        // 日期时间分开处理
        final List<String> dateAndTime = StrUtil.splitTrim(dateStr, ' ');
        final int size = dateAndTime.size();
        if (size < 1 || size > 2) {
            // 非可被标准处理的格式
            return StrUtil.str(dateStr);
        }

        final StringBuilder builder = StrUtil.builder();

        // 日期部分（"\"、"/"、"."、"年"、"月"都替换为"-"）
        String datePart = dateAndTime.get(0).replaceAll("[/.年月]", "-");
        datePart = StrUtil.removeSuffix(datePart, "日");
        builder.append(datePart);

        // 时间部分
        if (size == 2) {
            builder.append(' ');
            String timePart = dateAndTime.get(1).replaceAll("[时分秒]", ":");
            timePart = StrUtil.removeSuffix(timePart, ":");
            //将ISO8601中的逗号替换为.
            timePart = timePart.replace(',', '.');
            builder.append(timePart);
        }

        return builder.toString();
    }
}
