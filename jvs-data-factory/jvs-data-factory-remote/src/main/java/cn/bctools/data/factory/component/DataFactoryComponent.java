package cn.bctools.data.factory.component;

import cn.bctools.data.factory.api.DataFactoryApi;
import cn.bctools.data.factory.dto.AsyncDataDto;
import cn.bctools.data.factory.dto.JvsDataRemoteSecretDto;
import cn.bctools.data.factory.entity.ConsanguinityAnalyse;
import cn.bctools.data.factory.entity.JvsDataFactory;
import cn.bctools.data.factory.enums.DataFactoryNeo4jTypeEnum;
import cn.bctools.data.factory.es.service.JvsRemoteServerLogService;
import cn.bctools.data.factory.html.FNodeType;
import cn.bctools.data.factory.html.NodeHtml;
import cn.bctools.data.factory.html.node.dto.InParameterDto;
import cn.bctools.data.factory.po.JvsDataRemoteServer;
import cn.bctools.data.factory.service.JvsDataFactoryOutService;
import cn.bctools.data.source.entity.enums.DataFieldTypeEnum;
import cn.bctools.data.source.entity.po.InParameterJsonPo;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.convert.ConvertException;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DataFactoryComponent {

    @Resource
    private DataFactoryApi dataFactoryApi;
    @Resource
    private JvsRemoteServerLogService jvsRemoteServerLogService;
    @Resource
    private JvsDataFactoryOutService jvsDataFactoryOutService;

    //关键字 有效期
    public static final String KEYWORD_VALIDITY = "validityDay";
    //关键字 输出节点类型
    public static final String KEYWORD_OUT = "outPutObj";

    /**
     * 根据设计id获取数据
     *
     * @param remoteServer api服务信息
     * @param dataFactory  数据智仓信息
     * @return
     */
    public List<Map<String, Object>> getData(JvsDataRemoteServer remoteServer,
                                             JvsDataFactory dataFactory,
                                             JvsDataRemoteSecretDto secret,
                                             Map<String, Object> params,
                                             String invoker,
                                             String ip) {
        List<Map<String, Object>> data = Collections.emptyList();
        Boolean execute = Boolean.TRUE;
        try {
            //检查数据是否有效
            data = this.getDataFromFactory(remoteServer, dataFactory);

            //过滤数据
            if (StrUtil.isNotBlank(remoteServer.getInParameter())) {
                String inParameter = remoteServer.getInParameter();
                List<InParameterDto> filterList = JSONUtil.toList(inParameter, InParameterDto.class);
                List<InParameterJsonPo> collect = filterList.stream()
                        .map(e -> e.setValue(params.getOrDefault(e.getKey()
                                , Optional.of(e).map(InParameterJsonPo::getDefaultValue).orElse(null))))
                        .filter(e -> ObjectUtil.isNotEmpty(e.getValue()))
                        .collect(Collectors.toList());

                data = data.stream().filter(e ->
                        collect.stream().filter(o -> this.eq(o.getFieldType(), o.getValue(), e.get(o.getKey()))).count() == collect.size()
                ).collect(Collectors.toList());
            }
            //设置需要返回的字段
            if (StrUtil.isNotBlank(remoteServer.getOutParameter())) {
                String outParameter = remoteServer.getOutParameter();
                List<InParameterDto> outDtoList = JSONUtil.toList(outParameter, InParameterDto.class);
                data = data.stream().map(e -> {
                    Map<String, Object> row = new HashMap<>();
                    outDtoList.forEach(column -> row.put(column.getKey(), Convert.convert(column.getFieldType().getAClass(), e.get(column.getKey()))));
                    return row;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.info("-----数据服务异常:获取数据失败-----");
            e.printStackTrace();
            execute = Boolean.FALSE;
        }
        //保存调用日志
        jvsRemoteServerLogService.simpleSave(remoteServer, secret, execute, CollectionUtil.isNotEmpty(data), invoker, ip);
        return data;
    }

    /**
     * 获取数据
     *
     * @param dataFactory 智仓设计
     * @return
     */
    private List<Map<String, Object>> getDataFromFactory(JvsDataRemoteServer server, JvsDataFactory dataFactory) {
        ConsanguinityAnalyse consanguinityAnalyse = new ConsanguinityAnalyse()
                .setType(DataFactoryNeo4jTypeEnum.remote)
                .setDesignId(server.getId())
                .setDesignName(server.getName())
                .setDataFactoryId(dataFactory.getId());
        List<Map<String, Object>> data = jvsDataFactoryOutService.getData(dataFactory.getId(), 0L, 1, consanguinityAnalyse);
        //解析智仓设计拿到 输出节点 判断输出数据是否有有效期
        if (this.enableValidityDay(dataFactory)) {
            //去除有效期外的数据
            data = data.stream().filter(e -> e.containsKey(KEYWORD_VALIDITY)).filter(e ->
                            DateUtil.compare(DateUtil.parseDate(e.get(KEYWORD_VALIDITY).toString()), DateUtil.date()) >= 0)
                    .collect(Collectors.toList());
        }
        return data;
    }

    /**
     * 检查智仓数据是否有有效期
     *
     * @param dataFactory
     * @return
     */
    private Boolean enableValidityDay(JvsDataFactory dataFactory) {
        String viewJson = dataFactory.getViewJson();
        if (StrUtil.isNotBlank(viewJson)) {
            JSONArray nodeArr = JSONUtil.parseObj(viewJson).getJSONArray("nodeList");
            List<NodeHtml> nodeHtmls = JSONUtil.toList(nodeArr, NodeHtml.class);
            NodeHtml export = nodeHtmls.stream().filter(e -> ObjectUtil.equals(FNodeType.export, e.getType())).findFirst().orElse(null);
            if (ObjectUtil.isNotNull(export)) {
                String validityDay = JSONUtil.parseObj(export.getSourceData()).getJSONObject(KEYWORD_OUT).getStr(KEYWORD_VALIDITY);
                return NumberUtil.parseInt(validityDay) > 0;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 异步执行智仓设计 获取数据
     *
     * @param remoteServer api服务信息
     * @param dataFactory  智仓信息
     * @param executeNo    执行批次号
     */
    @Async
    public void getAsyncData(JvsDataRemoteServer remoteServer,
                             JvsDataFactory dataFactory,
                             JvsDataRemoteSecretDto secret,
                             String executeNo,
                             Map<String, Object> params,
                             String invoker,
                             String ip) {
        String callbackAddr = remoteServer.getCallbackAddr();
        if (StrUtil.isNotBlank(callbackAddr)) {
            AsyncDataDto asyncDataDto = new AsyncDataDto();
            List<Map<String, Object>> data = this.getData(remoteServer, dataFactory, secret, params, invoker, ip);
            asyncDataDto.setExecuteNo(executeNo).setData(data);
            HttpUtil.createPost(callbackAddr)
                    .header(Header.CONTENT_TYPE, ContentType.JSON.getValue())
                    .body(JSONUtil.toJsonStr(asyncDataDto)).execute();
        }
    }

    /**
     * 检查必填参数 以及参数格式
     *
     * @param inParams 参数
     * @param params   入参
     * @return 异常消息
     */
    public String checkParams(List<InParameterDto> inParams, Map<String, Object> params) {
        for (InParameterDto inParam : inParams) {
            String key = inParam.getKey();
            if (params.containsKey(key)) {
                try {
                    Object o = params.get(key);
                    Class<?> aClass = inParam.getFieldType().getAClass();
                    if (!o.getClass().equals(aClass)) {
                        return StrUtil.format("参数:{}类型异常,类型应为:{}", inParam.getName(), inParam.getFieldType());
                    }
                } catch (ConvertException e) {
                    return StrUtil.format("参数:{}类型异常,类型应为:{}", inParam.getName(), inParam.getFieldType());
                }
            } else if (inParam.getRequiredIs()) {
                return StrUtil.format("参数:{}必填", inParam.getName());
            }
        }
        return null;
    }

    /**
     * 检查必填参数 以及参数格式
     *
     * @return 异常消息
     */
    public List<InParameterDto> getAllInParameters(JvsDataRemoteServer remoteServer, JvsDataFactory dataFactory) {
        List<InParameterDto> allInParameters = new ArrayList<>();
        String inParameter = remoteServer.getInParameter();
        if (StrUtil.isNotBlank(inParameter)) {
            List<InParameterDto> parameterDtos = JSONUtil.toList(inParameter, InParameterDto.class);
            allInParameters.addAll(parameterDtos);
        }
        if (CollectionUtil.isNotEmpty(dataFactory.getInParameterPo())) {
            allInParameters.addAll(dataFactory.getInParameterPo());
        }
        return allInParameters;
    }

    /**
     * 生成返回示例
     *
     * @param outParams
     * @return
     */
    public String generateOutExample(String outParams) {
        List<Map<String, Object>> exampleData = new ArrayList<>();
        if (StrUtil.isNotBlank(outParams)) {
            List<InParameterJsonPo> parameterDtos = JSONUtil.toList(outParams, InParameterJsonPo.class);
            Map<String, Object> exampleItem = parameterDtos.stream().peek(e -> e.setValue("")).collect(Collectors.toMap(InParameterJsonPo::getKey, InParameterJsonPo::getValue));
            exampleData = Collections.singletonList(exampleItem);
        }
        Map<String, Object> example = new HashMap<>();
        example.put("code", 0);
        example.put("message", "success");
        example.put("data", exampleData);
        example.put("timestamp", DateUtil.now());
        return JSONUtil.toJsonStr(example);
    }

    /**
     * 等于
     *
     * @param type
     * @param value
     * @param value2
     * @return
     */
    private Boolean eq(DataFieldTypeEnum type, Object value, Object value2) {
        switch (type) {
            case 数字:
                return NumberUtil.equals(NumberUtil.parseDouble(StrUtil.toString(value)), NumberUtil.parseDouble(StrUtil.toString(value2)));
            case 时间:
                DateTime dateTime = DateUtil.parse(StrUtil.toString(value), "yyyy-MM-dd HH:mm:ss");
                DateTime dateTime1 = DateUtil.parse(StrUtil.toString(value2), "yyyy-MM-dd HH:mm:ss");
                return DateUtil.isSameTime(dateTime, dateTime1);
            default:
                return StrUtil.equals(StrUtil.toString(value), StrUtil.toString(value2));
        }
    }

}
