package cn.bctools.data.factory.es.po;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDateTime;

@Data
@Document(indexName = "jvs-remote-server-log")
@Accessors(chain = true)
public class JvsRemoteServerLog {

    @Id
    private String id;

    /**
     * 服务id
     */
    @Field(type = FieldType.Keyword)
    private String serverId;

    /**
     * 服务名称
     */
    @Field(type = FieldType.Text)
    private String serverName;

    /**
     * 调用时使用的凭证
     */
    @Field(type = FieldType.Keyword)
    private String secret;

    /**
     * 凭证说明
     */
    @Field(type = FieldType.Text)
    private String secretRemark;

    /**
     * 服务详情
     */
    @Field(type = FieldType.Text)
    private String serverAttr;

    /**
     * 调用人
     */
    @Field(type = FieldType.Text)
    private String invoker;

    /**
     * ip
     */
    @Field(type = FieldType.Text)
    private String ip;

    /**
     * 调用时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime callDate;

    /**
     * 调用状态 true调用成功 false调用失败
     */
    @Field(type = FieldType.Keyword)
    private Boolean callStatus;

    /**
     * 数据获取状态 true有数据 false无数据
     */
    @Field(type = FieldType.Keyword)
    private Boolean dataGetStatus;

}
