package cn.bctools.data.factory.es.service.impl;

import cn.bctools.common.utils.function.Get;
import cn.bctools.data.factory.dto.JvsDataRemoteSecretDto;
import cn.bctools.data.factory.dto.JvsRemoteServerQueryDto;
import cn.bctools.data.factory.es.po.JvsRemoteServerLog;
import cn.bctools.data.factory.es.service.JvsRemoteServerLogService;
import cn.bctools.data.factory.po.JvsDataRemoteServer;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.core.*;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@AllArgsConstructor
public class JvsRemoteServerLogServiceImpl implements JvsRemoteServerLogService {

    private final ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Override
    @Async
    public void simpleSave(JvsDataRemoteServer server, JvsDataRemoteSecretDto secret, Boolean status, Boolean dataGetStatus, String invoker, String ip){
        try {
            JvsRemoteServerLog dto = new JvsRemoteServerLog();
            dto.setInvoker(invoker)
                    .setServerId(server.getId())
                    .setServerName(server.getName())
                    .setServerAttr(JSONUtil.toJsonStr(server))
                    .setCallDate(LocalDateTime.now())
                    .setCallStatus(status)
                    .setIp(ip)
                    .setSecret(secret.getSecret())
                    .setSecretRemark(secret.getRemark())
                    .setDataGetStatus(dataGetStatus);
            ;
            elasticsearchRestTemplate.save(dto);
        } catch (Exception e) {
            log.info("数据服务调用日志保存失败 {}", DateUtil.now());
        }
    }

    @Override
    public Page<JvsRemoteServerLog> queryByCondition(Page page, JvsRemoteServerQueryDto dto){
        String indexName = JvsRemoteServerLog.class.getAnnotation(Document.class).indexName();
        Boolean aBoolean = this.checkIndex(indexName);
        if(!aBoolean){
            return page;
        }
        BoolQueryBuilder boolQuery = new BoolQueryBuilder();
        if(StrUtil.isNotBlank(dto.getServerId())){
            boolQuery.filter(QueryBuilders.termsQuery(Get.name(JvsRemoteServerLog::getServerId), dto.getServerId()));
        }
        if(ObjectUtil.isNotNull(dto.getQueryStartTime()) && ObjectUtil.isNotNull(dto.getQueryEndTime())){
            LocalDateTime start = LocalDateTime.ofInstant(DateUtil.parseDate(dto.getQueryStartTime()).toInstant(), ZoneId.systemDefault());
            LocalDateTime end = LocalDateTime.ofInstant(DateUtil.parseDate(dto.getQueryEndTime()).toInstant(), ZoneId.systemDefault());

            boolQuery.filter(QueryBuilders.rangeQuery(Get.name(JvsRemoteServerLog::getCallDate))
                    .gte(start).lte(end));
        }
        if(ObjectUtil.isNotNull(dto.getCallStatus())){
            boolQuery.filter(QueryBuilders.termsQuery(Get.name(JvsRemoteServerLog::getCallStatus),dto.getCallStatus()));
        }
        if(ObjectUtil.isNotNull(dto.getIp())){
            boolQuery.filter(QueryBuilders.termsQuery(Get.name(JvsRemoteServerLog::getIp),dto.getIp()));
        }
        if(StrUtil.isNotBlank(dto.getInvoker())){
            boolQuery.filter(QueryBuilders.termsQuery(Get.name(JvsRemoteServerLog::getInvoker),dto.getInvoker()));
        }
        if(ObjectUtil.isNotNull(dto.getDataGetStatus())){
            boolQuery.filter(QueryBuilders.termsQuery(Get.name(JvsRemoteServerLog::getDataGetStatus),dto.getDataGetStatus()));
        }
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder()
                .withQuery(boolQuery)
                .withPageable(PageRequest.of((int) (page.getCurrent() - 1), (int) page.getSize()))
                //按调用时间倒序
                .withSort(new FieldSortBuilder(Get.name(JvsRemoteServerLog::getCallDate)).order(SortOrder.DESC));
        NativeSearchQuery query = queryBuilder.build();
        SearchHits<JvsRemoteServerLog> search = elasticsearchRestTemplate.search(query, JvsRemoteServerLog.class);
        SearchPage<JvsRemoteServerLog> searchHits = SearchHitSupport.searchPageFor(search, query.getPageable());

        List<JvsRemoteServerLog> logList = new ArrayList<>();
        long total = Optional.of(searchHits.getContent().size()).orElse(0) == 0 ? 0 : searchHits.getTotalElements();
        Page<JvsRemoteServerLog> resVoPage = new Page<>(page.getCurrent(), page.getSize(), total);
        for (SearchHit<JvsRemoteServerLog> documentEsPoSearchHit : searchHits.getContent()) {
            JvsRemoteServerLog log = new JvsRemoteServerLog();
            BeanUtils.copyProperties(documentEsPoSearchHit.getContent(), log);
            logList.add(log);
        }
        resVoPage.setRecords(logList);
        return resVoPage;
    };

    private Boolean checkIndex(String indexName){
        IndexCoordinates indexCoordinates = IndexCoordinates.of(indexName);
        return elasticsearchRestTemplate.indexOps(indexCoordinates).exists();
    }
}
