package cn.bctools.remote.mongo.service.impl;

import cn.bctools.common.utils.function.Get;
import cn.bctools.remote.dto.JvsRemoteServerQueryDto;
import cn.bctools.remote.mongo.entity.RemoteLog;
import cn.bctools.remote.mongo.service.RemoteLogService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RemoteLogServiceImpl implements RemoteLogService {

    private final MongoTemplate mongoTemplate;

    @Override
    public void saveLog(String collectionName, RemoteLog log) {
        mongoTemplate.insert(log,collectionName);
    }

    @Override
    public Page<RemoteLog> queryByCondition(String collectionName, Page<RemoteLog> page, JvsRemoteServerQueryDto queryDto) {
        Query query = new Query();
        Long size = page.getSize();
        long skip = size * (page.getCurrent() - 1);
        query.skip(skip).limit(size.intValue());

        Map<String, Object> queryMap = BeanUtil.beanToMap(queryDto);

        List<Criteria> collect = queryMap.keySet().stream()
                .filter(e -> ObjectUtil.isNotNull(queryMap.get(e)))
                .map(e -> {
                    if (Get.name(JvsRemoteServerQueryDto::getQueryStartTime).equals(e)) {
                        return Criteria.where(Get.name(RemoteLog::getCallDate)).gte(queryMap.get(e));
                    }
                    if (Get.name(JvsRemoteServerQueryDto::getQueryEndTime).equals(e)) {
                        return Criteria.where(Get.name(RemoteLog::getCallDate)).lte(queryMap.get(e));
                    }
                    return Criteria.where(e).is(queryMap.get(e));
                }).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(collect)){
            Criteria criteria = new Criteria();
            criteria.andOperator(collect);
            query.addCriteria(criteria);
        }
        List<RemoteLog> remoteLogs = mongoTemplate.find(query, RemoteLog.class, collectionName);
        page.setRecords(remoteLogs);
        return page;
    }

    @Override
    public void remove(String collectionName) {
        mongoTemplate.dropCollection(collectionName);
    }

    @Override
    public List<String> getAllCollection() {
        return new ArrayList<>(mongoTemplate.getCollectionNames());
    }
}
