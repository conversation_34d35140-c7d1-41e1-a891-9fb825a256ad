package cn.bctools.report.data.component.subtotalConfiguration.service;

import cn.bctools.report.data.component.subtotalConfiguration.SubtotalBaseService;
import cn.bctools.report.po.CellData;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 计数
 */
@Service
public class CountServiceImpl implements SubtotalBaseService {
    @Override
    public BigDecimal exec(CellData setting,BigDecimal[] bigDecimals) {
        return new BigDecimal(bigDecimals.length);
    }
}
