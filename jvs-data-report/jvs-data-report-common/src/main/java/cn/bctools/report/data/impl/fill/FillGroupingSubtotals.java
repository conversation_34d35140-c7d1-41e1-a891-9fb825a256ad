package cn.bctools.report.data.impl.fill;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.data.source.entity.enums.DataFieldTypeEnum;
import cn.bctools.report.data.FillDataExecute;
import cn.bctools.report.data.constant.Constant;
import cn.bctools.report.data.util.FillCellDataUtils;
import cn.bctools.report.data.util.SortUtil;
import cn.bctools.report.enums.*;
import cn.bctools.report.po.CellData;
import cn.bctools.report.po.ReportDataPo;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class FillGroupingSubtotals implements FillDataExecute {

    @Override
    public void generatedData(CellData cell, ReportDataPo reportDataPo) {
        Map<String, List<Map<String, Object>>> sourceData = reportDataPo.getSourceData();
        //1.获取主数据源数据
        String dataSource = FillCellDataUtils.getDataSource(cell);
        List<Map<String, Object>> mainSource = sourceData.getOrDefault(dataSource, Collections.emptyList());
        if(CollectionUtil.isEmpty(mainSource)){
            cell.setExecList(Collections.emptyList());
            return;
        }

        CellData.GroupingSubtotals groupingSubtotals = cell.getGroupingSubtotals();
        //2.分组
        List<CellData> condition = groupingSubtotals.getCondition();

        List<CellData> statistics = groupingSubtotals.getStatistics();
        CellData copy = BeanCopyUtil.copy(cell, CellData.class);
        statistics.add(copy);

        //3.筛选作为分组依据的单元格
        List<CellData> basis = condition.stream().filter(e -> e.getSubtotalConfiguration().getBasis()).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(basis)){
            return;
        }
        //拓展方向

        boolean isCross = ExpandDirectionEnum.横向.equals(cell.getExpandDirection());
        if(isCross){
            condition.sort(Comparator.comparing(CellData::getR));
        }else{
            condition.sort(Comparator.comparing(CellData::getC));
        }

        Item root = new Item().setValue("root").setKey("root");
        buildTree(condition, 0,root, mainSource);
        List<Item> children = root.getChildren();
        List<CellData> group = new ArrayList<>();

        //被占用的行 合计会占用一行 如果被占用需要换一行
        List<Integer> occupation = new ArrayList<>();
        generateCellData(children,group,isCross,occupation);

        List<Item> lastChildren = getLastChildren(children);

        if(isCross){
            statistics.sort(Comparator.comparing(CellData::getR));
        }else{
            statistics.sort(Comparator.comparing(CellData::getC));
        }

        List<CellData> baseComputedData = new ArrayList<>();
        AtomicInteger incrementing;
        if(isCross){
            incrementing = new AtomicInteger(cell.getC());
        }else{
            incrementing = new AtomicInteger(cell.getR());
        }
        for (Item lastChild : lastChildren) {
            List<Map<String, Object>> source = lastChild.getSource();
            if(CollectionUtil.isEmpty(source)){
                incrementing.getAndIncrement();
                continue;
            }
            List<CellData> collect = source.stream().map(e -> {
                int andIncrement = incrementing.getAndIncrement();
                while (occupation.contains(andIncrement)){
                    andIncrement = incrementing.getAndIncrement();
                }
                int finalAndIncrement = andIncrement;
                return statistics.stream().map(v -> {
                    String fieldKey = FillCellDataUtils.getFieldKey(v);
                    Object value = e.getOrDefault(fieldKey, "");
                    if(isCross){
                        return buildCellDataDesignate(v, value, v.getR(),finalAndIncrement);
                    }
                    return buildCellDataDesignate(v, value, finalAndIncrement,v.getC() );
                }).collect(Collectors.toList());
            }).flatMap(Collection::stream).collect(Collectors.toList());
            baseComputedData.addAll(collect);
        }

        //计算合计
        List<CellData> subtotalList;
        if(isCross){
            subtotalList = computeCrossSubtotal(cell,group,statistics,baseComputedData);
        }else{
            subtotalList = computeLengthSubtotal(cell,group,statistics,baseComputedData);
        }

        List<CellData> collect = Stream.of(group, baseComputedData,subtotalList).flatMap(Collection::stream).collect(Collectors.toList());
        cell.setExecList(collect);
    }

    /**
     * 构建条件树
     * @param condition 条件单元格
     * @param index 下标
     * @param parent 上级节点
     * @param source 资源
     */
    void buildTree(List<CellData> condition,int index, Item parent,List<Map<String, Object>> source){
        if(index<condition.size()){
            if(CollectionUtil.isEmpty(source)){
                return;
            }
            CellData setting = condition.get(index);

            String fieldKey = FillCellDataUtils.getFieldKey(setting);

            Map<Object, List<Map<String, Object>>> collect = source
                    .stream()
                    .collect(Collectors.groupingBy(e -> e.getOrDefault(fieldKey,"")));
            List<Item> group = collect
                    .keySet()
                    .stream()
                    .map(e -> new Item().setSetting(setting).setKey(fieldKey).setValue(e).setSource(collect.get(e)))
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(e -> StrUtil.toString(e.getValue())))), ArrayList::new));

            //判断是否需要设置合计
            if(setting.getSubtotalConfiguration().getBasis()){
                group.add(new Item().setSetting(setting).setValue("合计").setIdentifier(CellIdentifierEnum.合计));
            }
            //排序
            sort(setting,group);
            index++;
            int finalIndex = index;
            group.forEach(e -> buildTree(condition, finalIndex,e,e.getSource()) );
            parent.setChildren(group);
        }

    }

    /**
     * 计算纵向小计
     * @param cell 小计单元格
     * @param group 分组条件
     * @param statistics 需要做统计的单元格
     * @param baseComputedData 统计的基础数据
     * @return
     */
    private List<CellData> computeLengthSubtotal(CellData cell,List<CellData> group,List<CellData> statistics,List<CellData> baseComputedData){
        Map<Integer, List<CellData>> collect1 = group.stream().filter(e -> CellIdentifierEnum.合计.equals(e.getIdentifier())).collect(Collectors.groupingBy(CellData::getC));
        Map<Integer, CellData> computeSetting = statistics.stream().collect(Collectors.toMap(CellData::getC, Function.identity()));
        Map<Integer, List<CellData>> baseComputeDataMap = baseComputedData.stream().collect(Collectors.groupingBy(CellData::getC));
        return collect1.keySet().stream().map(r -> {
            List<CellData> cellDataList = collect1.get(r);
            AtomicInteger scopeStart = new AtomicInteger(cell.getR());
            List<List<Integer>> scope = cellDataList.stream().map(e -> {
                ArrayList<Integer> integers = CollectionUtil.toList(scopeStart.get(), e.getR());
                scopeStart.set(e.getR() + 1);
                return integers;
            }).collect(Collectors.toList());

            return baseComputeDataMap.keySet().stream().map(c -> {
                List<CellData> columnDataList = baseComputeDataMap.get(c);
                CellData cellData = computeSetting.get(c);
                StatisticalMethodEnum statisticalMethodEnum = cellData.getSubtotalConfiguration().getStatisticalMethodEnum();
                return scope.stream().map(v -> {
                    Integer start = CollectionUtil.getFirst(v);
                    Integer end = CollectionUtil.getLast(v);
                    List<CellData> sub = columnDataList.stream().filter(o -> o.getR() >= start && o.getR() < end).collect(Collectors.toList());
                    if(StatisticalMethodEnum.无.equals(statisticalMethodEnum)){
                        return buildCellDataDesignate(cellData, "", end, c);
                    }
                    BigDecimal exec = SpringContextUtil.getBean(statisticalMethodEnum.getAClass()).exec(cellData, sub);
                    return buildCellDataDesignate(cellData, exec, end, c);
                }).collect(Collectors.toList());
            }).flatMap(Collection::stream).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 计算横向小计
     * @param cell 小计单元格
     * @param group 分组条件
     * @param statistics 需要做统计的单元格
     * @param baseComputedData 统计的基础数据
     * @return
     */
    private List<CellData> computeCrossSubtotal(CellData cell,List<CellData> group,List<CellData> statistics,List<CellData> baseComputedData){
        Map<Integer, List<CellData>> collect1 = group.stream().filter(e -> CellIdentifierEnum.合计.equals(e.getIdentifier())).collect(Collectors.groupingBy(CellData::getR));
        Map<Integer, CellData> computeSetting = statistics.stream().collect(Collectors.toMap(CellData::getR, Function.identity()));
        Map<Integer, List<CellData>> baseComputeDataMap = baseComputedData.stream().collect(Collectors.groupingBy(CellData::getR));
        return collect1.keySet().stream().map(r -> {
            List<CellData> cellDataList = collect1.get(r);
            AtomicInteger scopeStart = new AtomicInteger(cell.getC());
            List<List<Integer>> scope = cellDataList.stream().map(e -> {
                ArrayList<Integer> integers = CollectionUtil.toList(scopeStart.get(), e.getC());
                scopeStart.set(e.getC() + 1);
                return integers;
            }).collect(Collectors.toList());

            return baseComputeDataMap.keySet().stream().map(c -> {
                List<CellData> columnDataList = baseComputeDataMap.get(c);
                CellData cellData = computeSetting.get(c);
                StatisticalMethodEnum statisticalMethodEnum = cellData.getSubtotalConfiguration().getStatisticalMethodEnum();
                return scope.stream().map(v -> {
                    Integer start = CollectionUtil.getFirst(v);
                    Integer end = CollectionUtil.getLast(v);
                    List<CellData> sub = columnDataList.stream().filter(o -> o.getC() >= start && o.getC() < end).collect(Collectors.toList());
                    if(StatisticalMethodEnum.无.equals(statisticalMethodEnum)){
                        return buildCellDataDesignate(cellData, "",c , end);
                    }
                    BigDecimal exec = SpringContextUtil.getBean(statisticalMethodEnum.getAClass()).exec(cellData, sub);
                    return buildCellDataDesignate(cellData, exec,c , end);
                }).collect(Collectors.toList());
            }).flatMap(Collection::stream).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     *
     * @param dtoList
     * @param direction 拓展方向 true横向 false纵向
     * @param
     * @return
     */
    private void generateCellData(List<Item> dtoList,List<CellData> cellDataList, boolean direction,List<Integer> occupation){
        //列偏移
        AtomicInteger lateralOffset = new AtomicInteger(0);
        //行偏移
        AtomicInteger portraitOffset = new AtomicInteger(0);
        for (Item groupDto : dtoList) {
            CellData setting = groupDto.getSetting();
            //这一个条件有多少列
            int size = getRowSize(groupDto);
            for (int i = 0; i < size; i++) {
                CellData cellData = buildCellData(setting, groupDto.getValue(), lateralOffset.get(), portraitOffset.get());
                cellData.setIdentifier(groupDto.getIdentifier());

                //判断是否添加空白行
                if(direction){
                    addBlackCell(cellDataList,occupation, true,cellData,lateralOffset);
                }else{
                    addBlackCell(cellDataList,occupation, false,cellData,portraitOffset);
                }
                cellDataList.add(cellData);
                //如果是顶部分组 则只需要对列数进行偏移;如果是左侧分组 则只需要对行数偏移
                Integer occupationValue;
                if(direction){
                    occupationValue = cellData.getC();
                    lateralOffset.getAndIncrement();
                }else{
                    occupationValue = cellData.getR();
                    portraitOffset.getAndIncrement();
                }
                if(CellIdentifierEnum.合计.equals(groupDto.getIdentifier())){
                    occupation.add(occupationValue);
                }
            }
        }
        List<Item> collect = dtoList.stream().map(Item::getChildren).filter(CollectionUtil::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(collect)){
            generateCellData(collect,cellDataList, direction,occupation);
        }
    }

    private Integer getRowSize(Item dto){
        int size = 1;
        List<Item> children = dto.getChildren();
        if(CollectionUtil.isNotEmpty(children)){
            long count = 0;
            while (CollectionUtil.isNotEmpty(children)){
                if(CollectionUtil.isNotEmpty(children)){
                    size = children.stream().map(e -> Optional.ofNullable(e.getSource()).map(Collection::size).orElse(0)).mapToInt(Integer::intValue).sum();
                }
                long count1 = children.stream().filter(e -> CellIdentifierEnum.合计.equals(e.getIdentifier())).count();
                count+=count1;
                children = children.stream().map(Item::getChildren).filter(CollectionUtil::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
            }
            size+=count;
        }else{
            size = Optional.ofNullable(dto.getSource()).map(Collection::size).orElse(1);
        }
        return size;
    }

    /**
     * 创建单元格数据
     * @param setting 单元格设置
     * @param value 单元格值
     * @param lateralOffset 列 偏移
     * @param portraitOffset 行 偏移
     * @return
     */
    private CellData buildCellData(CellData setting,Object value,Integer lateralOffset,Integer portraitOffset){
        CellData copy = BeanCopyUtil.copy(setting, CellData.class);
        //去除合并设置
        copy.getV().remove(Constant.MC_KEY);
        //设置原始行列
        copy.setOriginC(setting.getC()).setOriginR(setting.getR());
        copy.setR(copy.getR() + portraitOffset);
        copy.setC(copy.getC() + lateralOffset);
        Map<String, Object> v = copy.getV();
        v.put("m", value);
        v.put("v", value);
        copy.setV(v);
        return copy;
    }
    /**
     * 创建单元格数据
     * @param setting 单元格设置
     * @param value 单元格值
     * @return
     */
    private CellData buildCellDataDesignate(CellData setting,Object value,Integer r,Integer c){
        CellData copy = BeanCopyUtil.copy(setting, CellData.class);
        //去除合并设置
        copy.getV().remove(Constant.MC_KEY);
        //设置原始行列
        copy.setOriginC(setting.getC()).setOriginR(setting.getR());
        copy.setR(r);
        copy.setC(c);
        Map<String, Object> v = copy.getV();
        v.put("m", value);
        v.put("v", value);
        copy.setV(v);
        return copy;
    }

    private List<Item> getLastChildren(List<Item> parent){
        List<Item> collect = parent.stream().map(Item::getChildren).filter(CollectionUtil::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(collect)){
            return getLastChildren(collect);
        }
        return parent;
    }

    /**
     * 排序
     * @param setting
     * @param list
     */
    private void sort(CellData setting,List<Item> list){

        DataFieldTypeEnum fieldType = FillCellDataUtils.getFieldType(setting);
        SortEnums sortType = FillCellDataUtils.getSortType(setting);

        Comparator<Item> comparator;

        switch (fieldType){
            case 数字:
                comparator = Comparator.nullsLast(Comparator.comparingLong(t1 -> ((Number) t1.getValue()).longValue()));
                break;
            case 时间:
                Function<Item, Date> mapDateFunction = obj -> {
                    String dateCharSequence = StrUtil.toString(obj.getValue());
                    return SortUtil.convert2Date(dateCharSequence,"yyyy-MM-dd HH:mm:ss");
                };
                comparator = Comparator.nullsLast(Comparator.comparing(mapDateFunction));
                break;
            default:
                comparator = Comparator.comparing(obj -> {
                    Object o = obj.getValue();
                    if (o == null) {
                        return "";
                    }
                    return StrUtil.toString(obj);
                });

        }
        if(SortEnums.desc.equals(sortType)){
            comparator = comparator.reversed();
        }
        list.sort(comparator);
    }

    /**
     * 添加空白单元格
     * @param list
     * @param occupation
     * @param cellData
     */
    private void addBlackCell(List<CellData> list,List<Integer> occupation,boolean direction,CellData cellData,AtomicInteger atomicInteger){
        if(direction){
            if(occupation.contains(cellData.getC())){
                //添加一个空白单元格
                CellData blackCellData = buildCellDataDesignate(cellData, "", cellData.getR(), cellData.getC());
                list.add(blackCellData);
                atomicInteger.getAndIncrement();
                cellData.setC(cellData.getC()+1);
                addBlackCell(list,occupation, true,cellData,atomicInteger);
            }
        }else{
            if(occupation.contains(cellData.getR())){
                //添加一个空白单元格
                CellData blackCellData = buildCellDataDesignate(cellData, "", cellData.getR(), cellData.getC());
                list.add(blackCellData);
                atomicInteger.getAndIncrement();
                cellData.setR(cellData.getR()+1);
                addBlackCell(list,occupation, false,cellData,atomicInteger);
            }
        }
    }

    @Data
    @Accessors(chain = true)
    public static class Item{

        private CellData setting;

        private String key;

        private Object value;

        private CellIdentifierEnum identifier = CellIdentifierEnum.无;

        private List<Item> children;

        private List<Map<String,Object>> source;
    }
}
