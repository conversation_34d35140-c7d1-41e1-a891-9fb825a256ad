package cn.bctools.report.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class DynamicCellBorderDto {

    /**
     * 最小列
     */
    private int minC;
    /**
     * 最大列
     */
    private int maxC;
    /**
     * 最小行
     */
    private int minR;
    /**
     * 最大行
     */
    private int maxR;
    /**
     * 是否包含全部
     */
    private Boolean containsAll = Boolean.FALSE;
    /**
     * 边框样式
     */
    private List<JSONObject> borderInfo;

}
