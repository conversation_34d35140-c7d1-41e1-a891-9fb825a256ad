package cn.bctools.report.enums;

import cn.bctools.report.data.FillDataExecute;
import cn.bctools.report.data.impl.fill.*;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CellNodeType {

    length("纵向拓展", FillLengthwiseTableDataNode.class),
    cross("横向拓展", FillCrosswiseTableDataNode.class),
    staticState("静态", FillStaticStateTableDataNode.class),
    dynamic("动态组合", FillDynamicTableDataNode.class),
    subtotal("分组小计", FillGroupingSubtotals.class),
    ;

    private final String desc;
    private final Class<? extends FillDataExecute> aClass;

}
