package cn.bctools.report.enums;

import cn.bctools.report.data.component.subtotalConfiguration.SubtotalBaseService;
import cn.bctools.report.data.component.subtotalConfiguration.service.*;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StatisticalMethodEnum {
    无(null,null),
    求和(SummationServiceImpl.class,"合计"),
    最大值(MaximumServiceImpl.class,"最大值"),
    最小值(MinimumServiceImpl.class,"最小值"),
    平均值(AverageServiceImpl.class,"平均值"),
    计数(CountServiceImpl.class,"总数"),
    ;

    private final Class<? extends SubtotalBaseService> aClass;
    private final String desc;

}
