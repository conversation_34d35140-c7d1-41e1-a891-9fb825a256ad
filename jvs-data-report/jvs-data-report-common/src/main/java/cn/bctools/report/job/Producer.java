package cn.bctools.report.job;

import cn.bctools.report.dto.ReportExecuteDto;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class Producer {

    private final RabbitTemplate rabbitTemplate;

    public void send(ReportExecuteDto executeDto){
        rabbitTemplate.convertAndSend(RabbitConfiguration.QUEUE_INFORM_REPORT, JSONUtil.toJsonStr(executeDto));
    }
}
