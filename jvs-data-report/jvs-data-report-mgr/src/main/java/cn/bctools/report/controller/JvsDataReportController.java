package cn.bctools.report.controller;

import cn.bctools.common.utils.PasswordUtil;
import cn.bctools.common.utils.R;
import cn.bctools.common.utils.function.Get;
import cn.bctools.database.entity.po.BasalPo;
import cn.bctools.design.use.api.AppApi;
import cn.bctools.log.annotation.Log;
import cn.bctools.report.dto.ChartMoveDto;
import cn.bctools.report.dto.CopyDto;
import cn.bctools.report.dto.CronDto;
import cn.bctools.report.entity.JvsReport;
import cn.bctools.report.enums.CronEnum;
import cn.bctools.report.enums.OperationEnum;
import cn.bctools.report.enums.ReportTypeEnum;
import cn.bctools.report.mongo.service.MongoCellService;
import cn.bctools.report.service.JvsReportCronService;
import cn.bctools.report.service.JvsReportService;
import cn.bctools.report.utils.AuthUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 报表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Api(tags = "报表")
@RestController
@AllArgsConstructor
@RequestMapping("/jvs/data/report")
@Slf4j
public class JvsDataReportController {

    private final JvsReportService jvsReportService;
    private final JvsReportCronService jvsReportCronService;
    private final MongoCellService mongoCellService;
    private final AppApi appApi;

    @ApiOperation("分页数据")
    @GetMapping("/page")
    public R<Page<JvsReport>> page(Page<JvsReport> page, JvsReport jvsReport) {
        LambdaQueryWrapper<JvsReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JvsReport::getReportType,ReportTypeEnum.dynamic).orderByDesc(JvsReport::getSort);
        if (StrUtil.isNotEmpty(jvsReport.getName())) {
            queryWrapper.like(JvsReport::getName, jvsReport.getName());
        }
        queryWrapper.select(JvsReport.class, e -> !e.getProperty().contains(Get.name(JvsReport::getViewJson)));
        page = jvsReportService.page(page, queryWrapper);
        return R.ok(page);
    }

    @ApiOperation("保存数据")
    @PostMapping("/save")
    public R<JvsReport> save(@RequestBody JvsReport dto) {
        //设置顺序
        long count = jvsReportService.count(new LambdaQueryWrapper<JvsReport>().eq(JvsReport::getType, dto.getType())) + 1;
        dto.setSort(new BigDecimal(count).intValue());
        jvsReportService.save(dto);
        if(Optional.of(dto).map(JvsReport::getCronStatus).orElse(Boolean.FALSE)){
            jvsReportCronService.enable(dto,dto.getCronSetting());
        }
        return R.ok(dto);
    }

    @ApiOperation("移动不管是调整顺序还是移动到其它目录")
    @PutMapping("/move")
    @Transactional(rollbackFor = Exception.class)
    public R<List<JvsReport>> move(@RequestBody ChartMoveDto chartMoveDto) {
        //获取数据
        JvsReport byId = jvsReportService.getById(chartMoveDto.getId());

        List<OperationEnum> check = AuthUtil.check(byId, OperationEnum.移动);
        if(CollectionUtil.isEmpty(check)){
            return R.failed("暂无权限,请联系此报表创建者");
        }
        //获取当前目录下面的所有主报表
        List<JvsReport> list = jvsReportService.list(new LambdaQueryWrapper<JvsReport>()
                .eq(JvsReport::getReportType,ReportTypeEnum.dynamic)
                .eq(JvsReport::getType, chartMoveDto.getParentId())
                .ne(JvsReport::getId, chartMoveDto.getId())
                .orderByAsc(JvsReport::getSort));
        int indexOf = 0;
        if (StrUtil.isNotBlank(chartMoveDto.getFrontId())) {
            indexOf = list.stream().map(JvsReport::getId).collect(Collectors.toList()).indexOf(chartMoveDto.getFrontId()) + 1;
        }
        //插入数据
        byId.setType(chartMoveDto.getParentId());
        list.add(indexOf, byId);
        //重新排序
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setSort(i);
        }
        jvsReportService.updateBatchById(list);
        //修改静态报表目录
        jvsReportService.update(Wrappers.lambdaUpdate(JvsReport.class)
                .set(JvsReport::getType,chartMoveDto.getParentId())
                .eq(JvsReport::getReportType,ReportTypeEnum.statistic)
                .eq(JvsReport::getMainReportId,byId.getId()));
        return R.ok(list);
    }

    @ApiOperation("编辑数据")
    @PutMapping("/edit")
    public R<JvsReport> edit(@RequestBody JvsReport dto) {
        JvsReport old = jvsReportService.getById(dto.getId());
        jvsReportService.updateById(dto);
        Map<String,Object> map=new HashMap<>();
        map.put("designId",dto.getId());
        map.put("role",dto.getRole());
        appApi.updateByDesignId(map);
        //如果停止 则移除定时任务
        if(!dto.getCronStatus().equals(old.getCronStatus())){
            if(dto.getCronStatus()){
                jvsReportCronService.enable(dto,dto.getCronSetting());
            }
            if(!dto.getCronStatus()){
                jvsReportCronService.remove(old);
            }
        }
        return R.ok(dto);
    }

    @ApiOperation("删除数据")
    @DeleteMapping("/del/{id}")
    public R<Boolean> del(@ApiParam("id") @PathVariable("id") String id) {
        //删除定时任务
        JvsReport report = jvsReportService.getById(id);
        jvsReportCronService.remove(report);
        //删除所有的静态报表(异步)
        List<JvsReport> staticReportList = jvsReportService.list(Wrappers.lambdaQuery(JvsReport.class).select(JvsReport::getId)
                .eq(JvsReport::getReportType, ReportTypeEnum.statistic)
                .eq(JvsReport::getMainReportId, id));
        List<String> collect = staticReportList.stream().map(JvsReport::getId).collect(Collectors.toList());
        //删除数据
        jvsReportService.removeByIds(collect);
        mongoCellService.asyncDrop(collect);
        return R.ok(jvsReportService.removeById(id));
    }

    @ApiOperation("获取数据id")
    @GetMapping("/{id}")
    public R<JvsReport> byId(@ApiParam("id") @PathVariable("id") String id) {
        JvsReport byId = jvsReportService.getById(id);
        return R.ok(byId);
    }

    @ApiOperation("复制报表")
    @PostMapping("/copy/{id}/{typeId}")
    public R<JvsReport> byId(@ApiParam("报表id") @PathVariable("id") String id
            ,@ApiParam("目录id") @PathVariable("typeId") String typeId
            ,@RequestBody CopyDto dto) {
        JvsReport byId = jvsReportService.getById(id);
        if(ObjectUtil.isNull(byId)){
            return R.failed("报表不存在");
        }
        List<OperationEnum> check = AuthUtil.check(byId, OperationEnum.复制);
        if(CollectionUtil.isEmpty(check)){
            return R.failed("未拥有复制权限");
        }
        byId.setId(null).setType(typeId).setName(dto.getName()).setCronStatus(Boolean.FALSE);
        byId.setCreateBy(null);
        byId.setCreateTime(null);
        byId.setCreateById(null);
        byId.setUpdateBy(null);
        byId.setUpdateTime(null);
        jvsReportService.save(byId);
        return R.ok(byId);
    }

    @ApiOperation("获取报表详情")
    @GetMapping("/{mainId}/static/list")
    public R<JvsReport> getStaticList(@ApiParam("主报表id")@PathVariable("mainId")String mainId,String search){
        JvsReport main = jvsReportService.getOne(Wrappers.lambdaQuery(JvsReport.class)
                .select(JvsReport.class,e -> !StrUtil.equals(e.getProperty(),Get.name(JvsReport::getViewJson)))
                .eq(JvsReport::getId,mainId));
        if(ObjectUtil.isNull(main)){
            return R.failed(StrUtil.format("报表【{}】未找到",mainId));
        }
        LambdaQueryWrapper<JvsReport> eq = Wrappers.lambdaQuery(JvsReport.class)
                .select(JvsReport.class, e -> !StrUtil.equals(e.getProperty(), Get.name(JvsReport::getViewJson)))
                .eq(JvsReport::getMainReportId, mainId)
                .like(StrUtil.isNotBlank(search),JvsReport::getName,search)
                .orderByDesc(BasalPo::getCreateTime);
        List<JvsReport> list = jvsReportService.list(eq);
        main.setStaticList(list);
        return R.ok(main);
    }

    @ApiOperation("获取默认定时任务规则")
    @GetMapping("/cron")
    public R<List<CronDto>> cron() {
        List<CronDto> objects = Arrays.stream(CronEnum.values())
                .map(e -> new CronDto().setName(e.getName()).setCron(e))
                .collect(Collectors.toList());
        return R.ok(objects);
    }

    @ApiOperation("导出设计")
    @PostMapping("/export")
    public void byId(@RequestBody List<String> ids) {
        String path = "etl" + DateUtil.format(DateUtil.date(), DatePattern.CHINESE_DATE_TIME_PATTERN) + ".jvs";
        List<JvsReport> list = jvsReportService.listByIds(ids);
        String encodePassword = PasswordUtil.encodePassword(JSONObject.toJSONString(list));
        File file = FileUtil.file(path);
        //写入内容
        FileUtil.writeString(encodePassword, file, "utf-8");
        fileOutput(path, FileUtil.readBytes(file));
        //删除文件
        file.delete();
    }

    @ApiOperation("导入设计")
    @PostMapping("/import")
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public R<List<JvsReport>> byId(@RequestParam("file") MultipartFile file) {
        File file1 = FileUtil.file(file.getName());
        FileUtil.writeFromStream(file.getInputStream(), file1);
        String string = FileUtil.readUtf8String(file1);
        file1.delete();
        if (StrUtil.isBlank(string)) {
            return R.failed("文件内容不能为空!");
        }
        try {
            string = PasswordUtil.decodedPassword(string);
        } catch (Exception e) {
            log.error("数据导入失败!", e);
            return R.failed("文件解密失败!");
        }
        List<JvsReport> list = JSONObject.parseArray(string, JvsReport.class);
        //删除id重置状态 清空taskid与状态
        list = list
                .stream()
                .peek(e -> {
                    e.setId(null);
                    e.setCreateBy(null);
                    e.setCreateTime(null);
                    e.setCreateById(null);
                    e.setUpdateTime(null);
                    e.setUpdateBy(null);
                })
                .collect(Collectors.toList());
        jvsReportService.saveBatch(list);
        return R.ok(list);
    }

    /***
     * 功能描述: <br>
     * 〈输出流文件〉
     * @Param: [response]
     * @Return: void
     * @Author:
     * @Date: 2021/11/18 21:54
     */
    @SneakyThrows
    private static void fileOutput(String fileName, byte[] bytes) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
        OutputStream outputStream = response.getOutputStream();
        outputStream.write(bytes);
        outputStream.flush();
        IoUtil.close(outputStream);
    }

}
