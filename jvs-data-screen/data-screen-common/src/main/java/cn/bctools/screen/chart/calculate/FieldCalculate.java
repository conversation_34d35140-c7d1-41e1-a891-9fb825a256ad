package cn.bctools.screen.chart.calculate;

import cn.bctools.screen.chart.bo.FieldsData;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 字段值计算 用于统计
 */
public interface FieldCalculate {

    /**
     * 计算执行
     *
     * @param list       需要计算的对象
     * @param fieldsData 计算的key值 可能会存在多个 具体更加不同的计算 执行判断
     */
    BigDecimal execute(List<Map<String, Object>> list, FieldsData... fieldsData);

}
