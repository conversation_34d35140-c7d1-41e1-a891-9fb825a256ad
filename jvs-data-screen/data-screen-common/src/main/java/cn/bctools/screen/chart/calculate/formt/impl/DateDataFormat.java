package cn.bctools.screen.chart.calculate.formt.impl;

import cn.bctools.screen.chart.bo.FieldsData;
import cn.bctools.screen.chart.calculate.formt.DataFormat;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.stream.Stream;

/**
 * 取整与四舍五入
 */
@Component
@Order(3)
public class DateDataFormat implements DataFormat {
    @Override
    public Stream<Map<String, Object>> formatList(Stream<Map<String, Object>> data, FieldsData fieldsData) {
        String dateFormat = fieldsData.getFormatParams().getDateFormat();
        if (StrUtil.isNotBlank(dateFormat)) {
            return data.peek(e -> {
                Object o = e.get(fieldsData.getFieldKey());
                if (ObjectUtil.isNotEmpty(o)) {
                    DateTime parse = DateUtil.parse(o.toString());
                    String format = DateUtil.format(parse, dateFormat);
                    e.put(fieldsData.getFieldKey(), format);
                }
            });
        }
        return data;
    }
}
