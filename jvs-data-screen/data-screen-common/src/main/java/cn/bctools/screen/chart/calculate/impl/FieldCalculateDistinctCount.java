package cn.bctools.screen.chart.calculate.impl;

import cn.bctools.screen.chart.bo.FieldsData;
import cn.bctools.screen.chart.calculate.FieldCalculate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

/**
 * 去重总数量
 */
@Component
public class FieldCalculateDistinctCount implements FieldCalculate {


    @Override
    public BigDecimal execute(List<Map<String, Object>> list, FieldsData... fieldsData) {
        //获取计算的字段属性
        FieldsData fieldsDatum = fieldsData[0];
        long count = list.stream().map(e -> e.get(fieldsDatum.getFieldKey())).distinct().count();
        BigDecimal bigDecimal = new BigDecimal(String.valueOf(count));
        //总数量不需要小数位
        bigDecimal = bigDecimal.setScale(0, RoundingMode.DOWN);
        return bigDecimal;
    }
}
