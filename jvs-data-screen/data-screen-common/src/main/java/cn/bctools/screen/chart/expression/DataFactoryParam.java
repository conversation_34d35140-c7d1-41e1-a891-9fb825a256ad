package cn.bctools.screen.chart.expression;

import cn.bctools.screen.enums.DataFieldTypeEnum;
import cn.bctools.function.entity.vo.ElementVo;
import cn.bctools.function.enums.JvsParamType;
import cn.bctools.function.handler.IJvsParam;
import cn.bctools.function.handler.JvsExpression;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 所有模型的字段
 * datafactory 场景
 *
 * @Author: guojing
 */
@Order(1)
@Component
@JvsExpression(groupName = "大屏", useCase = {"jvsScreen"}, prefix = "jvsScreen")
@AllArgsConstructor
public class DataFactoryParam implements IJvsParam<ElementVo> {
    private final static String FIELD_TYPE = "fieldType";
    private final static String FIELD_NAME = "fieldName";
    private final static String CALCULATE_IS = "calculateIs";
    /**
     * 函数执行时的固定key
     */
    private final static String FIELD_KEY = "jvsScreenFunctionKey";

    @Override

    public Object get(String paramName, Map<String, Object> data) {
        Object read = data.get(paramName);
        return read;
    }

    @Override
    public List<ElementVo> getAllElements() {
        //获取字段名称与字段属性
        String extendJson = this.getExtendJson();
        JSONObject jsonObject = JSONObject.parseObject(extendJson);
        //获取属性与名称用于前端展示
        String type = jsonObject.getString(FIELD_TYPE);
        DataFieldTypeEnum dataFieldTypeEnum = DataFieldTypeEnum.getType(type);
        //是否参与计算 如果参与计算那么 类型就应该变为 数字
        Boolean aBoolean = jsonObject.getBoolean(CALCULATE_IS);
        JvsParamType toJvsParamType = getToJvsParamType(dataFieldTypeEnum);
        if (aBoolean != null && aBoolean) {
            toJvsParamType = JvsParamType.number;
        }
        ElementVo elementVo = new ElementVo();
        elementVo.setType("入参");
        elementVo.setId(FIELD_KEY);
        elementVo.setName(jsonObject.getString(FIELD_NAME));
        elementVo.setInfo(jsonObject.getString(FIELD_NAME) + " " + type);
        elementVo.setJvsParamType(toJvsParamType);
        return Collections.singletonList(elementVo);
    }

    /**
     * 参数类型转换
     *
     * @param fieldType 类型
     */
    public static JvsParamType getToJvsParamType(DataFieldTypeEnum fieldType) {
        switch (fieldType) {
            case 布尔:
                return JvsParamType.bool;
            case 数字:
                return JvsParamType.number;
            case 集合类型:
                return JvsParamType.array;
            case 对象:
                return JvsParamType.object;
            case 字符串:
                return JvsParamType.text;
            case 时间:
                return JvsParamType.date;
            default:
                return JvsParamType.unknown;
        }
    }
}
