package cn.bctools.screen.chart.handle.dataV;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.screen.chart.ChartElementInterface;
import cn.bctools.screen.chart.bo.BasicsPirLogicSettingBo;
import cn.bctools.screen.chart.bo.FieldsData;
import cn.bctools.screen.chart.calculate.FieldCalculateFactory;
import cn.bctools.screen.chart.po.UniversalChartPo;
import cn.bctools.screen.chart.util.SortUtil;
import cn.bctools.screen.enums.SortEnums;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * [description]：胶囊柱图 DataV
 * @modified By：
 * @version: 1.0.0$
 */
@Slf4j
@Data
@Component
public class CapsuleChartBo implements ChartElementInterface {

    @Resource
    private final FieldCalculateFactory fieldCalculateFactory;

    private List<UniversalChartPo> data;
    private BasicsPirLogicSettingBo logicSetting;

    @Override
    public void dataTreating(JSONObject options) {
        options.put("data", Optional.ofNullable(this.data).orElse(Collections.emptyList()));
    }

    @Override
    public void setDefaultValue() {
        this.data = null;
    }

    @Override
    public void exec(List<Map<String, Object>> data, String chartName, Map<String, Object> logicSetting) {
        BasicsPirLogicSettingBo basicsPirLogicSettingBo = JSONObject.parseObject(JSONObject.toJSONString(logicSetting), BasicsPirLogicSettingBo.class);
        if (!basicsPirLogicSettingBo.check()) {
            throw new BusinessException("设计参数不完整");
        }
        this.logicSetting = basicsPirLogicSettingBo;
        FieldsData groupKey = basicsPirLogicSettingBo.getGroupKey();
        boolean notBlank = StrUtil.isNotBlank(groupKey.getFunctionStr());
        if (!groupKey.getSort().equals(SortEnums.nosort)) {
            data = SortUtil.getSortData(data, Arrays.asList(groupKey), null);
        }
        //先分组
        Map<Object, List<Map<String, Object>>> map = data.parallelStream()
                .collect(Collectors.groupingBy(e -> e.get(groupKey.getFieldKey()), LinkedHashMap::new, Collectors.toList()));
        //根据分组结果进行统计或计算
        List<UniversalChartPo> pos = map.keySet().parallelStream()
                .map(e -> {
                    UniversalChartPo universalChartPo = new UniversalChartPo();
                    FieldsData calculateKey = basicsPirLogicSettingBo.getCalculateKey();
                    Object execute = fieldCalculateFactory.execute(map.get(e), calculateKey);
                    //函数执行
                    if (notBlank) {
                        e = this.FunctionExec(e, groupKey.getFunctionStr());
                    }
                    if (StrUtil.isNotBlank(calculateKey.getFunctionStr())) {
                        execute = this.FunctionExec(execute, calculateKey.getFunctionStr());
                    }
                    //x轴是否存在函数设计
                    universalChartPo.setName(e.toString());
                    universalChartPo.setValue(execute);
                    return universalChartPo;
                }).collect(Collectors.toList());
        this.data = pos;
    }
}
