package cn.bctools.screen.chart.handle.echarts;

import cn.bctools.screen.chart.ChartElementInterface;
import cn.bctools.screen.chart.bo.BasicsLineLogicSettingBo;
import cn.bctools.screen.chart.bo.FieldsData;
import cn.bctools.screen.chart.calculate.FieldCalculateFactory;
import cn.bctools.screen.chart.po.BasicsBarChartPo;
import cn.bctools.screen.chart.po.BasicsChartPo;
import cn.bctools.screen.chart.po.BasicsHistogramChartPo;
import cn.bctools.screen.chart.util.SortUtil;
import cn.bctools.screen.enums.SortEnums;
import cn.bctools.common.exception.BusinessException;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * [description]：基础条形图
 * @modified By：
 * @version: 1.0.0$
 */
@Slf4j
@Data
@Component
public class BasicsBarChartBo implements ChartElementInterface {
    @Resource
    private final FieldCalculateFactory fieldCalculateFactory;
    private List<BasicsBarChartPo> data;
    private BasicsLineLogicSettingBo logicSetting;

    @Override
    public void dataTreating(JSONObject options) {
        //获取x轴数据
        List<String> xAxis =  Optional.ofNullable(data).orElse(Collections.emptyList()).stream().map(BasicsBarChartPo::getData).flatMap(Collection::stream).map(BasicsChartPo::getXAxis).distinct().collect(Collectors.toList());
        options.getJSONObject("yAxis").put("data",xAxis);
        //分组获取y轴数据
        JSONArray series = options.getJSONArray("series");
        if(CollectionUtil.isNotEmpty(this.data) && Optional.ofNullable(this.logicSetting).map(BasicsLineLogicSettingBo::getYAxis).isPresent()){
            if(ObjectUtil.isNull(this.logicSetting.getClassifyField())){
                for (int i = 0; i < series.size(); i++) {
                    JSONObject seriesItem = series.getJSONObject(i);
                    BasicsBarChartPo basicsHistogramChartPo = this.data.get(i);
                    List<Object> collect = basicsHistogramChartPo.getData().stream().map(BasicsChartPo::getYAxis).collect(Collectors.toList());
                    seriesItem.put("data",collect);
                }
            }else{
                JSONObject jsonObject = series.getJSONObject(0);
                List<JSONObject> seriesData = this.data.stream().map(e -> {
                    JSONObject newData = BeanUtil.copyProperties(jsonObject, JSONObject.class);
                    newData.put("name", e.getName());
                    List<Object> collect = e.getData().stream().map(BasicsChartPo::getYAxis).collect(Collectors.toList());
                    newData.put("data", collect);
                    return newData;
                }).collect(Collectors.toList());
                options.put("series",seriesData);
            }
        }else {
            for (int i = 0; i < series.size(); i++) {
                JSONObject seriesItem = series.getJSONObject(i);
                seriesItem.put("data",Collections.emptyList());
            }
        }
    }

    @Override
    public void setDefaultValue() {
        this.data = null;
    }

    @Override
    public void exec(List<Map<String, Object>> data, String chartName, Map<String, Object> logicSetting) {
        BasicsLineLogicSettingBo logicSettingBo = JSONObject.parseObject(JSONObject.toJSONString(logicSetting), BasicsLineLogicSettingBo.class);
        if (!logicSettingBo.check()) {
            throw new BusinessException("设计参数不完整");
        }
        this.logicSetting = logicSettingBo;
        //获取x轴
        FieldsData xAxis = logicSettingBo.getXAxis();
        String xAxFiledKey = xAxis.getFieldKey();
        //x轴是否存在函数设计
        boolean notBlank = StrUtil.isNotBlank(xAxis.getFunctionStr());
        if (!xAxis.getSort().equals(SortEnums.nosort)) {
            data = SortUtil.getSortData(data, Arrays.asList(xAxis), null);
        }
        //先按照 x轴分组
        Map<Object, List<Map<String, Object>>> map = data.parallelStream().collect(Collectors.groupingBy(e -> e.get(xAxFiledKey), LinkedHashMap::new, Collectors.toList()));
        //在通过y轴数据进行分组
        List<BasicsBarChartPo> list = logicSettingBo.getYAxis().stream()
                .map(e -> {
                    BasicsBarChartPo chartPo = new BasicsBarChartPo();
                    chartPo.setName(e.getAliasName());
                    //y轴数据统计
                    List<BasicsChartPo> chartPos = map.keySet().parallelStream().map(v -> {
                        BasicsChartPo basicsChartPo = new BasicsChartPo();
                        Object execute = fieldCalculateFactory.execute(map.get(v), e);
                        //函数执行
                        if (notBlank) {
                            v = this.FunctionExec(v, xAxis.getFunctionStr());
                        }
                        if (StrUtil.isNotBlank(e.getFunctionStr())) {
                            execute = this.FunctionExec(execute, e.getFunctionStr());
                        }
                        basicsChartPo.setXAxis(v.toString());
                        basicsChartPo.setYAxis(execute);
                        return basicsChartPo;
                    }).collect(Collectors.toList());
                    chartPo.setData(chartPos);
                    return chartPo;
                }).collect(Collectors.toList());
        this.data = list;
    }
}
