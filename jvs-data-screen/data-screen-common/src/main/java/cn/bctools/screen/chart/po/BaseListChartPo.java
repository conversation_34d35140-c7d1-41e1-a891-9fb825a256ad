package cn.bctools.screen.chart.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseListChartPo {
    /**
     * 页码
     */
    private Integer current;
    /**
     * 每页数量
     */
    private Integer size;
    /**
     * 总条数
     */
    private Integer total;
    /**
     * 数据
     */
    private List<Map<String,Object>> records;
}
