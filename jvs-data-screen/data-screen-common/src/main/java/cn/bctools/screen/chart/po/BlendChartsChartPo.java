package cn.bctools.screen.chart.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * <p>
 * [description]：柱线混合图
 * @modified By：
 * @version: *******$
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class BlendChartsChartPo {
    /**
     * x轴数据
     */
    private String xAxis;
    /**
     * 扩展字段
     */
    private Object call;
    /**
     * 左轴
     */
    private Object yAxisL;
    /**
     * 右轴
     */
    private Object yAxisR;
}
