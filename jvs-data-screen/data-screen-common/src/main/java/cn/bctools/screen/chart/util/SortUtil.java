package cn.bctools.screen.chart.util;

import cn.bctools.screen.chart.bo.FieldsData;
import cn.bctools.screen.enums.DataFieldTypeEnum;
import cn.bctools.screen.enums.SortEnums;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据排序按
 */
@Slf4j
public class SortUtil {
    /**
     * 排序
     *
     * @param list    数据集
     * @param keyList 排序字段
     */
    public static <T> List<T> getSortData(List<T> list, List<FieldsData> keyList, Class<T> clazz) {
        List<Map> maps1 = JSONObject.parseArray(JSONObject.toJSONString(list), Map.class);
        //获取key值属性
        LinkedHashMap<String, Class> objectObjectHashMap = new LinkedHashMap<>();
        for (int i = 0; i < keyList.size(); i++) {
            FieldsData field = keyList.get(i);
            //设置默认值
            maps1.parallelStream().peek(e -> {
                if (!e.containsKey(field.getFieldKey())) {
                    e.put(field.getFieldKey(), null);
                }
            }).collect(Collectors.toList());
            if (field.getFieldType().equals(DataFieldTypeEnum.数字)) {
                objectObjectHashMap.put(field.getFieldKey(), Number.class);
            }
            if (DataFieldTypeEnum.时间.equals(field.getFieldType())) {
                objectObjectHashMap.put(field.getFieldKey(), Date.class);
            }
            if (DataFieldTypeEnum.字符串.equals(field.getFieldType())) {
                objectObjectHashMap.put(field.getFieldKey(), String.class);
            }
        }
        if (keyList.isEmpty()) {
            return list;
        }
        Comparator comparator = getComparator(keyList);
        Object maps;
        try {
            maps = list.stream().sorted(comparator).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("排序失败将返回原有数据", e);
            maps = list;
        }
        if (clazz == null) {
            return (List<T>) maps;
        }
        return JSONObject.parseArray(JSONObject.toJSONString(maps), clazz);
    }


    private static Comparator getComparator(List<FieldsData> keyList) {
        Comparator st = null;
        for (FieldsData filed : keyList) {
            //判断类型
            DataFieldTypeEnum fieldType = filed.getFieldType();
            Comparator<Map> comparator = null;
            if (fieldType.equals(DataFieldTypeEnum.字符串)) {
                comparator = Comparator.comparing(objectObjectMap -> {
                    Object o = objectObjectMap.get(filed.getFieldKey());
                    if (o == null) {
                        return "";
                    }
                    return o.toString();
                });
            }
            if (fieldType.equals(DataFieldTypeEnum.数字)) {
                comparator = Comparator.nullsLast(Comparator.comparingLong(t1 -> ((Number) t1.get(filed.getFieldKey())).longValue()));
            }
            if (fieldType.equals(DataFieldTypeEnum.时间)) {
                Function<Map, Date> mapDateFunction = objectObjectMap -> {
                    String dateCharSequence = (String) objectObjectMap.get(filed.getFieldKey());
                    if (StrUtil.isNotBlank(dateCharSequence)) {
                        DateTime parse = DateUtil.parse(dateCharSequence, filed.getFormat());
                        return parse;
                    }
                    return null;
                };
                comparator = Comparator.nullsLast(Comparator.comparing(mapDateFunction));
            }
            if (filed.getSort() != null && filed.getSort().equals(SortEnums.desc)) {
                comparator = comparator.reversed();
            }
            if (ObjectUtil.isNull(st) && ObjectUtil.isNotNull(comparator)) {
                st = comparator;
            } else {
                st = st.thenComparing(comparator);
            }
        }
        return st;
    }
}
