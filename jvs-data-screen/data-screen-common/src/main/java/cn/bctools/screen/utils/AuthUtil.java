package cn.bctools.screen.utils;

import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.screen.dto.AuthRole;
import cn.bctools.screen.entity.ChartPage;
import cn.bctools.screen.enums.AuthRolePersonType;
import cn.bctools.screen.enums.OperationEnum;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单权限过滤
 */
public class AuthUtil {

    /**
     * 判断是否有权限
     *
     * @param buttonName 需要校验的权限按钮
     * @param chartPage  需要校验的数据
     * @return 当前数据可操作的权限 如果没有权限返回空数组
     */
    public static List<OperationEnum> check(ChartPage chartPage, OperationEnum buttonName) {
        //默认权限
        List<OperationEnum> defaultList = Arrays.asList(OperationEnum.values());
        //如果是创建人 拥有所有权限
        UserDto user = UserCurrentUtils.getCurrentUser();
        if (chartPage.getCreateById().equals(user.getId())) {
            return defaultList;
        }
        //在第一次 设计时 权限值不会存在
        if (ObjectUtil.isNull(chartPage.getRole())) {
            return defaultList;
        }
        List<AuthRole> roles = chartPage.getRole().stream().map(e -> e.toJavaObject(AuthRole.class)).collect(Collectors.toList());
        //判断是否有权限
        if (roles.isEmpty()) {
            return defaultList;
        }
        List<String> roleList = UserCurrentUtils.init().getRoles();
        //获取当前用户的操作权限
        List<OperationEnum> list = roles.stream().filter(e -> {
            if (e.getPersonType().equals(AuthRolePersonType.all)) {
                return true;
            }
            if (e.getPersonnels().isEmpty()) {
                return false;
            }
            //如果操作按钮有值需要判断是否存在此按钮 如果按钮没有值 只需要判断选择的用户中 是否存在当前用户  没有值 就表示 是查看权限
            if (buttonName != null && !OperationEnum.查看.equals(buttonName)) {
                boolean match = e.getOperation().stream().anyMatch(x -> x.equals(buttonName));
                if (!match) {
                    return false;
                }
            }
            return e.getPersonnels().stream().anyMatch(x -> {
                switch (x.getType()) {
                    case job:
                        return user.getJobId().equals(x.getId());
                    case role:
                        return roleList.contains(x.getId());
                    case user:
                        return x.getId().equals(user.getId());
                    case dept:
                        return StrUtil.equals(user.getDeptId(),x.getId());
                    default:
                        return false;
                }
            });
        })
                .flatMap(e -> {
                    if (e.getPersonType().equals(AuthRolePersonType.all)) {
                        return defaultList.stream();
                    }
                    //如果此条数据有当前用户 但是没有按钮 表示只能查看
                    if (e.getOperation().isEmpty()) {
                        return Arrays.asList(OperationEnum.查看).stream();
                    }
                    return e.getOperation().stream();
                })
                .distinct()
                .collect(Collectors.toList());
        return list;
    }

    /**
     * 过滤当前用户没有权限的数据
     *
     * @param list 需要过滤的数据
     * @return 过滤后的数据
     */
    public static List<ChartPage> auth(List<ChartPage> list) {
        return list.stream().peek(e -> e.setOperationList(check(e, null)))
                .filter(e -> !e.getOperationList().isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 获取单个的权限
     *
     * @param chartPage 单个
     * @return 过滤后的数据
     */
    public static ChartPage auth(ChartPage chartPage) {
        return chartPage.setOperationList(check(chartPage, null));
    }
}
