package cn.bctools.screen.controller;

import cn.bctools.common.utils.R;
import cn.bctools.database.entity.po.BasalPo;
import cn.bctools.oss.template.OssTemplate;
import cn.bctools.screen.dto.BatchMaterialDto;
import cn.bctools.screen.dto.BatchRemoveMaterialDto;
import cn.bctools.screen.dto.MaterialDto;
import cn.bctools.screen.dto.MaterialTypeDto;
import cn.bctools.screen.entity.JvsMaterial;
import cn.bctools.screen.service.JvsMaterialService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 素材管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
@Api(tags = "大屏-素材管理")
@RestController
@RequestMapping("/material")
@AllArgsConstructor
@Slf4j
public class JvsMaterialController {

    private final OssTemplate ossTemplate;
    private final JvsMaterialService jvsMaterialService;

    @ApiOperation("查询素材")
    @GetMapping("/find")
    public R<MaterialDto> find(String search){
        List<JvsMaterial> list = jvsMaterialService.list(Wrappers.lambdaQuery(JvsMaterial.class)
                .like(StrUtil.isNotBlank(search), JvsMaterial::getName, search)
                .orderByDesc(BasalPo::getCreateTime)
        );
        list.forEach(e -> e.setFileLink(ossTemplate.fileLink(e.getFileName(),e.getBucketName())));
        LinkedHashMap<String, List<JvsMaterial>> jvsMaterialMap = list.stream().collect(Collectors.groupingBy(JvsMaterial::getType, LinkedHashMap::new, Collectors.toList()));
        List<MaterialTypeDto> collect = jvsMaterialMap.keySet().stream().map(e -> new MaterialTypeDto().setId(SecureUtil.md5(e)).setName(e).setMaterialList(jvsMaterialMap.get(e))).collect(Collectors.toList());
        Set<String> types = jvsMaterialMap.keySet();
        return R.ok(new MaterialDto().setMaterialMenu(collect).setType(types));
    }

    @ApiOperation("保存素材")
    @PostMapping("/save")
    public R<JvsMaterial> save(@Validated@RequestBody JvsMaterial dto){
        jvsMaterialService.save(dto);
        return R.ok(dto);
    }

    @ApiOperation("批量保存素材")
    @PostMapping("/batch/save")
    public R<List<JvsMaterial>> saveBatch(@Validated @RequestBody BatchMaterialDto dto){
        List<JvsMaterial> materialList = dto.getMaterialList();
        materialList.forEach(e ->e.setType(dto.getType()));
        jvsMaterialService.saveBatch(materialList);
        return R.ok(materialList);
    }

    @ApiOperation("删除素材")
    @DeleteMapping("/{id}/remove")
    public R<Boolean> remove(@ApiParam("素材id") @PathVariable("id")String id){
        return R.ok(jvsMaterialService.removeById(id));
    }

    @ApiOperation("批量删除素材")
    @DeleteMapping("/batch/remove")
    public R remove(@RequestBody BatchRemoveMaterialDto dto){
        if(StrUtil.isNotBlank(dto.getType())){
            jvsMaterialService.remove(Wrappers.lambdaQuery(JvsMaterial.class).eq(JvsMaterial::getType,dto.getType()));
        }
        if(CollectionUtil.isNotEmpty(dto.getMaterialIds())){
            jvsMaterialService.removeByIds(dto.getMaterialIds());
        }
        return R.ok();
    }

    @ApiOperation("重命名")
    @PutMapping("/rename")
    public R rename(@RequestBody JvsMaterial dto){
        jvsMaterialService.update(Wrappers.lambdaUpdate(JvsMaterial.class).set(JvsMaterial::getName,dto.getName()).eq(JvsMaterial::getId,dto.getId()));
        return R.ok();
    }
}
