package cn.bctools.data.source.data.config;

import cn.bctools.data.source.dto.PublicConnectDto;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.influxdb.InfluxDB;
import org.influxdb.InfluxDBFactory;
import org.influxdb.dto.Pong;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;

import java.util.*;
import java.util.stream.Collectors;

@Data
public class InfluxDbClientConfig {

    /**
     * jdbc
     */
    private InfluxDB influxDB;

    /**
     * 构造方法
     */
    private InfluxDbClientConfig() {
    }

    /**
     * 获取客户端
     */
    public static InfluxDbClientConfig init(PublicConnectDto publicConnectDto) {
        String url = StrUtil.format("http://{}:{}", publicConnectDto.getSourceHost(), publicConnectDto.getSourcePort());
        InfluxDbClientConfig config = new InfluxDbClientConfig();
        InfluxDB connect = InfluxDBFactory.connect(url, publicConnectDto.getSourceUserName(), publicConnectDto.getSourceUserName());
        connect.setDatabase(publicConnectDto.getSourceLibraryName());
        config.setInfluxDB(connect);
        return config;
    }

    public List<Map<String, Object>> queryData(String sql) {
        QueryResult query = influxDB.query(new Query(sql));
        List<String> columns = getColumns(query);
        List<List<Object>> values = getValues(query);
        return values.stream().map(e -> {
            Map<String, Object> row = new HashMap<>();
            for (int i = 0; i < columns.size(); i++) {
                row.put(columns.get(i), e.get(i));
            }
            return row;
        }).collect(Collectors.toList());
    }

    public Long getTotalCount(String tableName) {
        String countSql = String.format("SELECT COUNT(*) AS total FROM %s ", tableName);
        QueryResult query = influxDB.query(new Query(countSql));
        return Optional.of(query).map(QueryResult::getResults)
                .map(CollectionUtil::getFirst)
                .map(QueryResult.Result::getSeries)
                .map(CollectionUtil::getFirst)
                .map(QueryResult.Series::getValues)
                .map(CollectionUtil::getFirst)
                .map(StrUtil::toString)
                .map(NumberUtil::parseLong).orElse(0L);
    }

    public Boolean ping(){
        boolean isConnected = false;
        Pong pong;
        try {
            pong = influxDB.ping();
            if (pong != null) {
                isConnected = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return isConnected;
    }

    /**
     * 从查询结果中获取列名称
     * @param queryResult 查询结果
     * @return
     */
    public List<String> getColumns(QueryResult queryResult){
        return Optional.of(queryResult).map(QueryResult::getResults)
                .map(CollectionUtil::getFirst)
                .map(QueryResult.Result::getSeries)
                .map(CollectionUtil::getFirst)
                .map(QueryResult.Series::getColumns).orElse(new ArrayList<>());
    }
    /**
     * 从查询结果中获取数据
     * @param queryResult 查询结果
     * @return
     */
    public List<List<Object>> getValues(QueryResult queryResult){
        return Optional.of(queryResult).map(QueryResult::getResults)
                .map(CollectionUtil::getFirst)
                .map(QueryResult.Result::getSeries)
                .map(CollectionUtil::getFirst)
                .map(QueryResult.Series::getValues).orElse(new ArrayList<>());
    }

}
