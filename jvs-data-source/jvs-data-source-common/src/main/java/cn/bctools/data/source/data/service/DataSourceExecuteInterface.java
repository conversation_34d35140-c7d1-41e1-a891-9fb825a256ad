package cn.bctools.data.source.data.service;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.data.source.data.po.ReadDataPo;
import cn.bctools.data.source.dto.DataSourceExecSqlDto;
import cn.bctools.data.source.entity.DataSource;
import cn.bctools.data.source.entity.DataSourceStructure;
import cn.bctools.data.source.entity.enums.DataFieldTypeEnum;
import cn.bctools.data.source.entity.enums.DataSourceTypeEnum;
import cn.bctools.data.source.service.DataSourceService;
import cn.bctools.data.source.service.DataSourceStructureService;
import cn.bctools.data.source.service.JvsDataSourceInterfaceService;
import cn.bctools.data.source.util.GetValueTypeUtil;
import cn.bctools.database.entity.po.BasalPo;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public interface DataSourceExecuteInterface<T extends ReadDataPo> {

    /**
     * 获取表结构
     * 获取整个数据源的表结构
     *
     * @param dataSource 数据源
     * @return 集合 {@link  cn.bctools.data.source.entity.DataSourceStructure}
     */
    List<DataSourceStructure> syncTableStructure(DataSource dataSource);

    /**
     * 执行sql 并返回数据与字段属性
     * 注意这种方式无法保证安全性
     *
     * @param dataSource 数据源
     * @param execSql    需要执行的sql
     * @param size       获取条数 如果为0就是获取全部
     * @param current    当前页码
     * @return 集合 {@link  DataSourceExecSqlDto}
     */
    default DataSourceExecSqlDto excelSql(DataSource dataSource, String execSql, long size, long current) {
        throw new BusinessException("此数据源,暂不支持sql自定义");
    }


    /**
     * 删除数据源
     *
     * @param id 数据源 id
     */
    default void removeDataSource(String id) {
        DataSourceStructureService dataSourceStructureService = SpringContextUtil.getBean(DataSourceStructureService.class);
        JvsDataSourceInterfaceService jvsDataSourceInterfaceService = SpringContextUtil.getBean(JvsDataSourceInterfaceService.class);
        DataSourceService dataSourceService = SpringContextUtil.getBean(DataSourceService.class);
        dataSourceStructureService.remove(new LambdaQueryWrapper<DataSourceStructure>().eq(DataSourceStructure::getDataSourceId, id));
        jvsDataSourceInterfaceService.removeBySourceId(id);
        dataSourceService.removeById(id);
    }

    /**
     * 删除数据源其中的表结构
     *
     * @param id 数据源结构id
     */
    default void removeDataSourceStructure(String id) {
        DataSourceStructureService dataSourceStructureService = SpringContextUtil.getBean(DataSourceStructureService.class);
        JvsDataSourceInterfaceService jvsDataSourceInterfaceService = SpringContextUtil.getBean(JvsDataSourceInterfaceService.class);
        dataSourceStructureService.remove(new LambdaQueryWrapper<DataSourceStructure>().eq(DataSourceStructure::getId, id));
        jvsDataSourceInterfaceService.removeByStructureId(id);
    }

    /**
     * 数据读取 目前只有excel 数据源是支持数据读取的
     *
     * @param readData 数据读取的入参
     */
    default void read(T readData) {
        throw new BusinessException("此数据源,不支持数据读取");
    }

    /**
     * 获取此类型的所有可选数据源
     *
     * @param dataSourceType 数据源类型
     * @return DataSourceTableDto {@link  cn.bctools.data.source.entity.DataSource}
     */
    default List<DataSource> getDataBase(DataSourceTypeEnum dataSourceType) {
        DataSourceService bean = SpringContextUtil.getBean(DataSourceService.class);
        return bean.list(new LambdaQueryWrapper<DataSource>()
                .select(DataSource::getSourceType, DataSource::getRole, DataSource::getSourceName, DataSource::getId, BasalPo::getCreateById)
                .eq(DataSource::getSourceType, dataSourceType));
    }


    /**
     * 获取表名称
     *
     * @param id 数据源
     * @return {@link  cn.bctools.data.source.entity.DataSourceStructure}
     */
    default List<DataSourceStructure> getTableNames(String id) {
        DataSourceStructureService dataSourceStructureService = SpringContextUtil.getBean(DataSourceStructureService.class);
        List<DataSourceStructure> list = dataSourceStructureService.list(new LambdaQueryWrapper<DataSourceStructure>()
                .eq(DataSourceStructure::getCheckIs, Boolean.TRUE)
                .eq(DataSourceStructure::getDataSourceId, id)
                .orderByDesc(DataSourceStructure::getCreateTime));
        return list;
    }

    /**
     * 获取单表结构 只包含数据结构
     *
     * @param dataSourceStructure 数据表数据
     * @return DataSourceTableDto {@link  cn.bctools.data.source.entity.DataSourceStructure.Structure}
     */
    default List<DataSourceStructure.Structure> getTableStructure(DataSourceStructure dataSourceStructure) {
        DataSourceStructureService dataSourceStructureService = SpringContextUtil.getBean(DataSourceStructureService.class);
        DataSourceStructure one = dataSourceStructureService.getById(dataSourceStructure.getId());
        return JSONObject.parseObject(JSONObject.toJSONString(one), DataSourceStructure.class).getStructure()
                .parallelStream()
                .peek(e -> e.setFrom(one.getId()))
                .collect(Collectors.toList());
    }


    /**
     * 生成数据源工厂 并不是 所有数据源都需要
     *
     * @param dataSource 数据源
     * @return 是否成功
     */
    default Boolean init(DataSource dataSource) {
        return true;
    }

    /**
     * 获取数据
     *
     * @param dataSource          数据源
     * @param dataSourceStructure 表对象
     * @param size                获取条数 如果为0就是获取全部
     * @param current             当前页码
     * @return 分页数据 {@link com.baomidou.mybatisplus.extension.plugins.pagination.Page}
     */
    Page<Map<String, Object>> findAll(DataSource dataSource, DataSourceStructure dataSourceStructure, long size, long current);

    /**
     * 获取总数据量
     *
     * @param dataSource          数据源
     * @param dataSourceStructure 表名称
     */
    Long getCount(DataSource dataSource, DataSourceStructure dataSourceStructure);

    /**
     * 校验此数据源是否正常
     *
     * @param json 入参
     */
    void check(String json);

    /**
     * 数据类型获取
     *
     * @param value 值
     */
    default void fieldTypeEnum(Object value, DataSourceStructure.Structure structure) {
        DataFieldTypeEnum typeEnum = GetValueTypeUtil.get(value);
        if (typeEnum.equals(DataFieldTypeEnum.时间)) {
            structure.setFormat("yyyy-MM-dd HH:mm:ss");
        }
        structure.setDataFieldTypeEnum(typeEnum);
    }

}
