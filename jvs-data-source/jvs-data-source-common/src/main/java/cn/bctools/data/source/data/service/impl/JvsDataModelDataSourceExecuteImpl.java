package cn.bctools.data.source.data.service.impl;

import cn.bctools.data.source.data.service.DataSourceExecuteInterface;
import cn.bctools.data.source.dto.DataModelDataSourceDto;
import cn.bctools.data.source.entity.DataSource;
import cn.bctools.data.source.entity.DataSourceStructure;
import cn.bctools.data.source.entity.JvsDataSourceInterface;
import cn.bctools.data.source.entity.enums.DataFieldTypeEnum;
import cn.bctools.data.source.util.QuerySqlBuildUtil;
import cn.bctools.design.use.api.AppApi;
import cn.bctools.design.use.api.DataModelApi;
import cn.bctools.design.use.api.dto.DataFiledDto;
import cn.bctools.design.use.api.dto.DataModelDto;
import cn.bctools.design.use.api.dto.DataModelSearchDto;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component(value = "dataModel")
@Slf4j
public class JvsDataModelDataSourceExecuteImpl implements DataSourceExecuteInterface {

    @Autowired
    DataModelApi dataModelApi;
    @Autowired
    AppApi appApi;

    @Override
    public List<DataSourceStructure> syncTableStructure(DataSource dataSource) {
        DataModelDataSourceDto dataModelDataSourceDto = dataSource.getCustomJson().toJavaObject(DataModelDataSourceDto.class);
        //根据应用ID查询模型集
        List<DataModelDto> dataModelDtos = dataModelApi.dataModelList(dataModelDataSourceDto.getSourceLibraryName()).getData();
        if (dataModelDtos.isEmpty()) {
            return new ArrayList<>();
        }
        List<DataSourceStructure> tableNames = dataModelDtos
                .stream()
                .map(e -> new DataSourceStructure()
                        .setId(e.getId())
                        .setExecuteName(e.getId())
                        .setDataSourceId(dataSource.getId())
                        .setName(e.getTableName())
                        .setTableNameDesc(e.getTableNameDesc()))
                .collect(Collectors.toList());
        //获取表结构
        tableNames.forEach(e -> {
            List<DataFiledDto> map = dataModelApi.fieldMap(dataModelDataSourceDto.getSourceLibraryName(), e.getExecuteName()).getData();
            List<DataSourceStructure.Structure> collect = map
                    .stream()
                    .map(s -> {
                        DataFieldTypeEnum value = DataFieldTypeEnum.value(s.getCls());
                        return new DataSourceStructure.Structure()
                                .setOriginalColumnName(s.getColumnName())
                                .setColumnCount(s.getColumnCount())
                                .setDataFieldTypeEnum(value);
                    }).collect(Collectors.toList());
            e.setStructure(collect)
                    .setCheckIs(Boolean.TRUE)
                    .setFieldCount(collect.size());
        });
        return tableNames;
    }


    /**
     * 获取数据
     *
     * @param dataSource          数据源
     * @param dataSourceStructure 表名称
     * @param size                获取条数 如果为0就是获取全部
     * @param current             当前页码
     */
    @Override
    public Page<Map<String, Object>> findAll(DataSource dataSource, DataSourceStructure dataSourceStructure, long size, long current) {
        List mapList;
        Long total;
        if (Optional.of(dataSourceStructure).map(DataSourceStructure::getInterfaceDetail).map(JvsDataSourceInterface::getSqlExpression).filter(StrUtil::isNotBlank).isPresent()) {
            JvsDataSourceInterface interfaceDetail = dataSourceStructure.getInterfaceDetail();
            List<DataModelSearchDto.SearchGroup> searchGroups = QuerySqlBuildUtil.buildDataModelQuery(interfaceDetail, dataSourceStructure.getInParameterJson());
            DataModelSearchDto dataModelSearchDto = new DataModelSearchDto()
                    .setCurrent(current)
                    .setSize(size)
                    .setId(dataSourceStructure.getExecuteName()).setGroup(searchGroups);
            mapList = dataModelApi.search(dataModelSearchDto).getData();
            total = dataModelApi.countBySearch(dataModelSearchDto).getData();
        } else {
            mapList = dataModelApi.list(dataSourceStructure.getExecuteName(), size, current).getData();
            total = dataModelApi.getCount(dataSourceStructure.getExecuteName()).getData();
        }
        return new Page<Map<String, Object>>()
                .setTotal(total)
                .setCurrent(current)
                .setSize(size)
                .setRecords(mapList);
    }

    @Override
    public Long getCount(DataSource dataSource, DataSourceStructure dataSourceStructure) {
        return dataModelApi.getCount(dataSourceStructure.getId()).getData();
    }

    @Override
    public void check(String json) {
    }

}
