package cn.bctools.data.source.controller;

import cn.bctools.common.utils.R;
import cn.bctools.common.utils.function.Get;
import cn.bctools.data.source.dto.QueryTypeDto;
import cn.bctools.data.source.entity.DataSource;
import cn.bctools.data.source.entity.JvsDataSourceInterface;
import cn.bctools.data.source.entity.enums.DataModelQueryEnum;
import cn.bctools.data.source.entity.enums.DataSourceTypeEnum;
import cn.bctools.data.source.entity.enums.InfluxDBQueryEnum;
import cn.bctools.data.source.entity.enums.MysqlQueryEnum;
import cn.bctools.data.source.service.DataSourceService;
import cn.bctools.data.source.service.JvsDataSourceInterfaceService;
import cn.bctools.design.use.api.enums.DataModelQueryType;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 数据源-接口 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Api(tags = "数据源-接口")
@RestController
@RequestMapping("/data/source/interface")
@AllArgsConstructor
public class JvsDataSourceInterfaceController {

    private final DataSourceService dataSourceService;

    private final JvsDataSourceInterfaceService jvsDataSourceInterfaceService;

    @ApiOperation("新增接口")
    @PostMapping("/save")
    public R<JvsDataSourceInterface> save(@RequestBody JvsDataSourceInterface dto){
        DataSource dataSource = dataSourceService.getById(dto.getDataSourceId());
        if(ObjectUtil.isNull(dataSource)){
            return R.failed("数据源不存在");
        }
        String expression = jvsDataSourceInterfaceService.replaceExpression(dto.getSqlExpression(), dto.getQuerySettings(),dataSource.getSourceType());
        dto.setSqlExpression(expression);
        jvsDataSourceInterfaceService.save(dto);
        jvsDataSourceInterfaceService.refreshCache();
        return R.ok(dto);
    }

    @ApiOperation("修改接口")
    @PutMapping("/edit")
    public R<JvsDataSourceInterface> update(@RequestBody JvsDataSourceInterface dto){
        DataSource dataSource = dataSourceService.getById(dto.getDataSourceId());
        if(ObjectUtil.isNull(dataSource)){
            return R.failed("数据源不存在");
        }
        String expression = jvsDataSourceInterfaceService.replaceExpression(dto.getSqlExpression(), dto.getQuerySettings(),dataSource.getSourceType());
        dto.setSqlExpression(expression);
        jvsDataSourceInterfaceService.updateById(dto);
        jvsDataSourceInterfaceService.refreshCache();
        return R.ok(dto);
    }

    @ApiOperation("删除接口")
    @DeleteMapping("/del/{id}")
    public R del(@ApiParam("接口id")@PathVariable("id")String id){
        jvsDataSourceInterfaceService.removeById(id);
        jvsDataSourceInterfaceService.refreshCache();
        return R.ok();
    }

    @ApiOperation("获取数据源所有接口")
    @GetMapping("/{dataSourceId}/list")
    public R<List<JvsDataSourceInterface>> listBySource(@ApiParam("数据源id")@PathVariable("dataSourceId")String dataSourceId,
                                                        JvsDataSourceInterface dto){
        List<String> ignoreFieldList = Stream.of(Get.name(JvsDataSourceInterface::getQuerySettings),Get.name(JvsDataSourceInterface::getRenderData)).collect(Collectors.toList());
        List<JvsDataSourceInterface> list = jvsDataSourceInterfaceService
                .list(Wrappers.lambdaQuery(JvsDataSourceInterface.class)
                        .select(JvsDataSourceInterface.class,e -> !ignoreFieldList.contains(e.getProperty()))
                        .eq(JvsDataSourceInterface::getDataSourceId, dataSourceId)
                        .eq(StrUtil.isNotBlank(dto.getStructureId()),JvsDataSourceInterface::getStructureId,dto.getStructureId())
                        .like(StrUtil.isNotBlank(dto.getInterfaceName()),JvsDataSourceInterface::getInterfaceName,dto.getInterfaceName())
                .orderByAsc(JvsDataSourceInterface::getCreateTime));
        return R.ok(list);
    }

    @ApiOperation("查询接口详情")
    @GetMapping("/detail/{id}")
    public R<JvsDataSourceInterface> detail(@ApiParam("接口id")@PathVariable("id")String id){
        JvsDataSourceInterface byId = jvsDataSourceInterfaceService.getById(id);
        return R.ok(byId);
    }

    @ApiOperation("获取数据源查询条件")
    @GetMapping("/query/condition/{type}")
    public R<Map<String, List<QueryTypeDto>>> getQueryType(@ApiParam("类型 mysql")@PathVariable("type") String type){
        try {
            DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.valueOf(type);
            Map<String, List<QueryTypeDto>> collect;
            switch (dataSourceTypeEnum){
                case dataModel:
                    collect = Arrays.stream(DataModelQueryEnum.values()).map(e -> Arrays.stream(e.getType().split(",")).map(v -> new QueryTypeDto().setName(e.getDesc()).setShorthand(e.name()).setType(v)).collect(Collectors.toList())
                    ).flatMap(Collection::stream).collect(Collectors.groupingBy(QueryTypeDto::getType));
                    break;
                case influxDBDataSource:
                    collect = Arrays.stream(InfluxDBQueryEnum.values()).map(e -> Arrays.stream(e.getType().split(",")).map(v -> new QueryTypeDto().setName(e.getDesc()).setShorthand(e.name()).setType(v)).collect(Collectors.toList())
                    ).flatMap(Collection::stream).collect(Collectors.groupingBy(QueryTypeDto::getType));
                    break;
                default:
                    collect = Arrays.stream(MysqlQueryEnum.values())
                            .map(e -> Arrays.stream(e.getType().split(",")).map(v -> new QueryTypeDto().setName(e.getDesc()).setShorthand(e.name()).setType(v)).collect(Collectors.toList())
                            ).flatMap(Collection::stream).collect(Collectors.groupingBy(QueryTypeDto::getType));
            }
            return R.ok(collect);
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        }
        return R.failed(StrUtil.format("数据源【{}】不存在",type));
    }


}
