package cn.bctools.data.source.entity.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("查询设置")
public class QuerySettingPo {

    @ApiModelProperty("配置项编号")
    private String settingNo;

    @ApiModelProperty("查询字段")
    private String queryKey;

    @ApiModelProperty("查询条件")
    private String queryType;

    @ApiModelProperty("参数")
    private List<String> inParamKeys;
}
