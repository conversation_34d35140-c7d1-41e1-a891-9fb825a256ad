<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.bctools</groupId>
        <artifactId>jvs-design</artifactId>
        <version>2.1.8</version>
    </parent>

    <groupId>cn.bctools</groupId>
    <artifactId>jvs-design-api</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-feign</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <!--为了公开开发使用，需要推送到public公开私服中-->
    <distributionManagement>
        <repository>
            <!--必须与 settings.xml 的 id 一致-->
            <id>j8s-jvs-jvs</id>
            <name>jvs</name>
            <url>https://j8s-maven.pkg.coding.net/repository/jvs/jvs/</url>
        </repository>
    </distributionManagement>

</project>
