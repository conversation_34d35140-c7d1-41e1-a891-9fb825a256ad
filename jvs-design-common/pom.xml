<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.bctools</groupId>
        <artifactId>jvs-design</artifactId>
        <version>2.1.8</version>
    </parent>

    <groupId>cn.bctools</groupId>
    <artifactId>jvs-design-common</artifactId>

    <dependencies>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-mongodb</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-design-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-database</artifactId>
        </dependency>


        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity</artifactId>
            <version>1.7</version>
        </dependency>

        <!-- Redis -->
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-redis</artifactId>
        </dependency>

        <!--        &lt;!&ndash; Swagger &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>com.github.xiaoymin</groupId>-->
        <!--            <artifactId>swagger-bootstrap-ui</artifactId>-->
        <!--            <version>1.9.6</version>-->
        <!--        </dependency>-->

        <!-- rabbit -->
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-rabbit</artifactId>
        </dependency>

        <!-- Jvs表达式 -->
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-function</artifactId>
        </dependency>

        <!--逻辑引擎-->
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-function-run</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-function-base</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-word</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.mail</groupId>
                    <artifactId>mailapi</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 消息中心 -->
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>message-push-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 图表 -->
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-chart-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 报表 -->
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-data-report-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--定时任务管理端-->
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-job</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.bctools.custom</groupId>
            <artifactId>app-resource-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.9.3</version>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <!-- 配置信息 -->
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>
