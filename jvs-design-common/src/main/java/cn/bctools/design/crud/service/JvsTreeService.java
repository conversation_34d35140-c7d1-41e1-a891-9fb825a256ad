package cn.bctools.design.crud.service;

import cn.bctools.design.crud.entity.JvsTree;
import cn.bctools.design.crud.entity.enums.PerAndSubTypeEnum;
import cn.bctools.design.crud.vo.TreeVo;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> GaoZeXi
 */
public interface JvsTreeService extends IService<JvsTree> {

    /**
     * 删除树形字典
     *
     * @param id 字典id
     */
    void deleteTree(String id);

    /**
     * 字典名称查重校验
     *
     * @param name     字典名称
     * @param parentId 上级节点id
     */
    void checkName(String name, String parentId);

    /**
     * 根据字典标识获取字典树
     *
     * @param uniqueName 字典唯一标识
     * @return 字典树
     */
    Map<String, Object> getByUniqueName(String uniqueName);

        /**
         * 根据字典标识获取字典树
         *
         * @param uniqueName 字典唯一标识
         * @param level 返回层级
         * @return 字典树
         */
    Map<String, Object> getByUniqueName(String uniqueName, Integer level);

    /**
     * 根据名称返回次名称的所有树字段
     *
     * @param name
     * @return
     */
    List<Tree<Object>> tree(String name);

    /**
     * 根据字典标识获取字典节点
     *
     * @param uniqueName
     * @return
     */
    JvsTree getItemByUniqueName(String uniqueName);

    Map<String, Object> getByType(String type, Integer level);

    String getParentByUniqueName(String uniqueName);

    String getNameByUniqueNames(Object uniqueNames);

    Map<String, String> getNameByUniqueNameList(String type, Object uniqueNames);

    Map<String, String> getUniqueNameByType(String type, String name);

    List<JvsTree> getTreeSelectList(String type, Integer level);

    Map<String, String> getTreeNameByType(String type);

    Map<String, String> getTreeRemarkByType(String type);

    List<TreeVo> getPerAndSubList(PerAndSubTypeEnum type, Boolean hasSub, String periodType, String subjectType);

    Map<String, String> getPerAndSubMap(String periodType, String subjectType);

    Map<String, String> getGradeMap(String periodId);

    JvsTree getInfoByPath(String name, String type, String remarksFilter);

    Map<String, String> getNameMapByType(String type);

    Map<String, String> getUniMapByType(String type);

    Map<String, Object> getNationRegion();

    Boolean refreshCache();
}