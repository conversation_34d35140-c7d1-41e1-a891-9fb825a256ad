package cn.bctools.design.crud.service.impl;

import cn.bctools.common.entity.po.TreePo;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.TreeUtils;
import cn.bctools.design.crud.aop.TreeCache;
import cn.bctools.design.crud.entity.JvsTree;
import cn.bctools.design.crud.entity.enums.PerAndSubTypeEnum;
import cn.bctools.design.crud.entity.enums.TreeCacheTypeEnum;
import cn.bctools.design.crud.mapper.JvsTreeMapper;
import cn.bctools.design.crud.service.JvsTreeService;
import cn.bctools.design.crud.utils.DictTreeUtil;
import cn.bctools.design.crud.vo.TreeVo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Splitter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> GaoZeXi
 */
@Slf4j
@Service
@AllArgsConstructor
public class JvsTreeImpl extends ServiceImpl<JvsTreeMapper, JvsTree> implements JvsTreeService {

    private final Splitter splitter = Splitter.on(',').trimResults().omitEmptyStrings();

    private final DictTreeUtil dictTreeUtil;

    /**
     * 删除分类以及子集
     *
     * @param id 字典id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTree(String id) {
        JvsTree sysTree = this.getOne(Wrappers.query(new JvsTree().setUniqueName(id)));
        String parentId = sysTree.getParentId();
        String groupId = sysTree.getGroupId();
        // 删除当前节点以及子节点
        if (JvsTree.DICT_ID_ROOT.equals(parentId)) {
            this.remove(Wrappers.<JvsTree>lambdaQuery().eq(JvsTree::getGroupId, groupId));
        } else {
            List<String> childIds = this.getChild(id, null);
            this.remove(Wrappers.<JvsTree>lambdaQuery().in(JvsTree::getUniqueName, childIds));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkName(String name, String parentId) {
        long count = this.count(Wrappers.<JvsTree>lambdaQuery()
                .eq(JvsTree::getName, name)
                .eq(JvsTree::getParentId, parentId));
        if (count > 0) {
            log.error("[树形字典] 同一层级的字典名称不能重复, name: {}, id: {}", name, parentId);
            throw new BusinessException("同一层级的字典名称不能重复: {}", name);
        }
    }

    @Override
    @TreeCache(type = TreeCacheTypeEnum.function)
    public Map<String, Object> getByUniqueName(String uniqueName){
        return getByUniqueName(uniqueName,0);
    }

    @Override
    public Map<String, Object> getByUniqueName(String uniqueName, Integer level) {
        List<String> disableType = Arrays.asList("positional_title", "school_type_and_level", "major");

        JvsTree tree = this.getOne(Wrappers.<JvsTree>lambdaQuery().select(JvsTree::getGroupId, JvsTree::getType).eq(JvsTree::getUniqueName, uniqueName));
        if (Objects.isNull(tree)) {
            return new HashMap<>(1);
        }

        // 缓存设置
        String treeCacheKey = DictTreeUtil.getTreeCacheKey("getByType", "type", tree.getType(), "level", level);
        Map<String, Object> cacheData = (Map<String, Object>) dictTreeUtil.getCacheData(treeCacheKey, Object.class);
        if (ObjectNull.isNotNull(cacheData)) {
            return cacheData;
        }

        String groupId = tree.getGroupId();
        List<JvsTree> treeList = list(Wrappers.<JvsTree>lambdaQuery().eq(JvsTree::getGroupId, groupId).isNotNull(JvsTree::getUniqueName));
        List<TreePo> treePos = treeList.stream()
                .map(e -> BeanCopyUtil.copy(e, TreePo.class).setExtend(e).setId(e.getUniqueName()))
                .collect(Collectors.toList());
        List<Tree<Object>> result = TreeUtils.tree(treePos, JvsTree.DICT_ID_ROOT);
        //过滤超过层级数据
        if (!(level == null || level == 0)) {
            limitTree(result.get(0), 1, level);
        }
        //只可选不包含子节点数据
        if (tree.getType() != null && disableType.contains(tree.getType())) {
            setDisabledNodeWithChildren(result.get(0));
        }

        dictTreeUtil.setCacheData(treeCacheKey, result.get(0), DictTreeUtil.DEFAULT_CACHE_TIME);

        return result.get(0);
    }


    public static void limitTree(Tree<Object> node, int currentLevel, int maxLevel) {
        if (currentLevel > maxLevel || Objects.isNull(node.getChildren())) {
            node.setChildren(null);
            return;
        }

        List<Tree<Object>> children = node.getChildren();
        for (Tree<Object> child : children) {
            limitTree(child, currentLevel + 1, maxLevel);
        }
    }

    /**
     * 设置disabled属性为true
     *
     * @param node //树节点
     */
    private static void setDisabledNodeWithChildren(Tree<Object> node) {
        if (node.getChildren() != null && !(node.getChildren()).isEmpty()) {
            node.put("disabled", true);
            List<Tree<Object>> children = node.getChildren();
            for (Tree<Object> child : children) {
                setDisabledNodeWithChildren(child);
            }
        }
    }

    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = Tree.class)
    public List<Tree<Object>> tree(String name) {
        List<JvsTree> list = list(Wrappers.<JvsTree>lambdaQuery()
                .like(StringUtils.isNotBlank(name), JvsTree::getName, name));
        if (StringUtils.isNotBlank(name) && ObjectNull.isNotNull(list)) {
            //反推得到整体树形
            Set<String> unqNames = new HashSet<>();

            for (JvsTree jvsTree : list) {
                if (!unqNames.contains(jvsTree.getParentId())) {
                    JvsTree parent = getOne(new LambdaQueryWrapper<JvsTree>().eq(JvsTree::getUniqueName, jvsTree.getParentId()));
                    while (ObjectNull.isNotNull(parent)) {
                        unqNames.add(parent.getUniqueName());
                        parent = getOne(new LambdaQueryWrapper<JvsTree>().eq(JvsTree::getUniqueName, parent.getParentId()));
                    }
                }
            }

            unqNames.addAll(list.stream().map(JvsTree::getUniqueName).collect(Collectors.toList()));
            list = list(new LambdaQueryWrapper<JvsTree>().in(JvsTree::getUniqueName, unqNames));
        }
        List<TreePo> treePos = list.stream()
                .map(e -> BeanCopyUtil.copy(e, TreePo.class)
                        .setId(Optional.ofNullable(e.getUniqueName()).orElse(e.getId()))
                        .setExtend(JSONObject.parse(JSONObject.toJSONString(e))))
                .collect(Collectors.toList());
        List<Tree<Object>> tree = TreeUtils.tree(treePos, JvsTree.DICT_ID_ROOT);
        return tree;
    }

    /**
     * 递归查询所有的子集
     *
     * @param id  当前节点id
     * @param ids 子节点id集合
     */
    private List<String> getChild(String id, List<String> ids) {
        if (ids == null) {
            ids = new ArrayList<>();
        }
        if (id == null) {
            return null;
        }
        ids.add(id);
        List<JvsTree> list = list(Wrappers.query(new JvsTree().setParentId(id)));
        if (ObjectUtil.isNotEmpty(list)) {
            for (JvsTree sysTree : list) {
                String sysTreeId = sysTree.getId();
                getChild(sysTreeId, ids);
            }
        }
        return ids;
    }

    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = JvsTree.class)
    public JvsTree getItemByUniqueName(String uniqueName) {
        JvsTree jvsTree = this.getOne(Wrappers.<JvsTree>lambdaQuery().eq(JvsTree::getUniqueName, uniqueName));
        if (Objects.isNull(jvsTree)) {
            return null;
        }
        return jvsTree;
    }

    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = Map.class)
    public Map<String, Object> getByType(String type, Integer level) {
        List<String> disableType = Arrays.asList("positional_title", "school_type_and_level", "major");

        List<JvsTree> treeList = list(new LambdaQueryWrapper<JvsTree>()
                .eq(JvsTree::getType, type).isNotNull(JvsTree::getUniqueName));

        if (treeList.isEmpty()) {
            return new HashMap<>();
        }

        List<TreePo> items = treeList.stream()
                .map(e -> BeanCopyUtil.copy(e, TreePo.class).setExtend(e).setId(e.getUniqueName()))
                .collect(Collectors.toList());

        List<Tree<Object>> result = TreeUtils.tree(items, JvsTree.DICT_ID_ROOT);
        //过滤超过层级数据
        if (!(level == null || level == 0)) {
            limitTree(result.get(0), 1, level);
        }

        //只可选不包含子节点数据
        if (treeList.get(0).getType() != null && disableType.contains(treeList.get(0).getType())) {
            setDisabledNodeWithChildren(result.get(0));
        }
        return result.get(0);
    }

    @Override
    public String getParentByUniqueName(String uniqueName) {
        return getParentUniqueName(uniqueName, uniqueName);
    }

    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = String.class)
    public String getNameByUniqueNames(Object uniqueNames) {
        if (uniqueNames instanceof String) {
            if (StrUtil.isNotBlank(uniqueNames.toString())) {
                JvsTree one = getOne(new LambdaQueryWrapper<JvsTree>().eq(JvsTree::getUniqueName, uniqueNames));
                if (ObjectUtil.isNotNull(one)) {
                    return one.getName();
                }
            }
        } else if (uniqueNames instanceof List) {
            if (CollUtil.isNotEmpty((Collection<?>) uniqueNames)) {
                List<JvsTree> list = list(new LambdaQueryWrapper<JvsTree>().in(JvsTree::getUniqueName, (Collection<?>) uniqueNames));
                if (CollUtil.isNotEmpty(list)) {
                    return list.stream().map(JvsTree::getName).collect(Collectors.joining(","));
                }
            }
        }
        return "";
    }

    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = Map.class)
    public Map<String, String> getNameByUniqueNameList(String type, Object uniqueNames) {
        List<JvsTree> treeList = list(new LambdaQueryWrapper<JvsTree>()
                .in(JvsTree::getUniqueName, (Collection<?>) uniqueNames)
                .eq(JvsTree::getType, type)
                .orderByAsc(JvsTree::getSort)
        );
        Map<String, String> map = new HashMap<>();
        treeList.forEach(tree -> {
            map.put(tree.getUniqueName(), tree.getName());
        });
        return map;
    }

    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = Map.class)
    public Map<String, String> getUniqueNameByType(String type, String name) {
        JvsTree one = getOne(new LambdaQueryWrapper<JvsTree>().eq(JvsTree::getType, type).eq(JvsTree::getName, name));
        Map<String, String> map = new HashMap<>();
        if (ObjectUtil.isNotNull(one)) {
            map.put(one.getName(), one.getUniqueName());
        }
        return map;
    }

    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = JvsTree.class)
    public List<JvsTree> getTreeSelectList(String type, Integer level) {
        List<JvsTree> treeList = list(new LambdaQueryWrapper<JvsTree>()
                .select(JvsTree::getName, JvsTree::getValue, JvsTree::getUniqueName, JvsTree::getSort, JvsTree::getRemarks, JvsTree::getType, JvsTree::getParentId)
                .eq(JvsTree::getType, type)
                .orderByAsc(JvsTree::getSort)
        );
        List<JvsTree> result = new ArrayList<>();

        JvsTree root = getOne(new LambdaQueryWrapper<JvsTree>().eq(JvsTree::getType, type).eq(JvsTree::getParentId, JvsTree.DICT_ID_ROOT));
        limitResult(root, treeList, 0, level, result);

        return result;
    }

    private void limitResult(JvsTree parent, List<JvsTree> allTrees, int currentLevel, int targetLevel, List<JvsTree> result) {
        if (currentLevel == targetLevel) {
            result.add(parent);
            return;
        }

        for (JvsTree tree : allTrees) {
            if (!JvsTree.DICT_ID_ROOT.equals(tree.getParentId()) && tree.getParentId().equals(parent.getUniqueName())) {
                limitResult(tree, allTrees, currentLevel + 1, targetLevel, result);
            }
        }
    }


    private String getParentUniqueName(String parentUniqueName, String uniqueName) {
        JvsTree one = getOne(new LambdaQueryWrapper<JvsTree>().eq(JvsTree::getUniqueName, uniqueName));
        JvsTree parent = getOne(new LambdaQueryWrapper<JvsTree>().eq(JvsTree::getUniqueName, parentUniqueName));
        if (ObjectUtil.isNotNull(one) && ObjectUtil.isNotNull(parent)) {
            if (JvsTree.DICT_ID_ROOT.equals(parent.getParentId())) {
                return uniqueName;
            }
            return getParentUniqueName(parent.getParentId(), parent.getUniqueName());
        }
        return null;
    }

    /**
     * 根据type获取对应分组的所有字典名称
     *
     * @param type
     * @return
     */
    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = Map.class)
    public Map<String, String> getTreeNameByType(String type) {
        List<JvsTree> treeList = list(new LambdaQueryWrapper<JvsTree>()
                .eq(JvsTree::getType, type).isNotNull(JvsTree::getUniqueName).ne(JvsTree::getParentId, -1).orderByAsc(JvsTree::getSort));
        Map<String, String> map = new LinkedHashMap<>();
        treeList.forEach(tree -> {
            map.put(tree.getUniqueName(), tree.getName());
        });
        return map;
    }

    /**
     * 根据type获取对应分组的所有字典的remark值
     *
     * @param type 字典类型
     * @return Map
     */
    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = Map.class)
    public Map<String, String> getTreeRemarkByType(String type) {
        List<JvsTree> treeList = list(new LambdaQueryWrapper<JvsTree>()
                .eq(JvsTree::getType, type).isNotNull(JvsTree::getUniqueName).ne(JvsTree::getParentId, JvsTree.DICT_ID_ROOT));
        Map<String, String> map = new HashMap<>();
        treeList.forEach(tree -> {
            map.put(tree.getUniqueName(), tree.getRemarks());
        });
        return map;
    }

    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = TreeVo.class)
    public List<TreeVo> getPerAndSubList(PerAndSubTypeEnum type, Boolean hasSub, String periodType, String subjectType) {
        boolean disableFlag;
        boolean removeNullSubjectFlag = PerAndSubTypeEnum.hasSubject.equals(type);
        if (removeNullSubjectFlag) {
            // 不返回没有学科的学段默认可选
            disableFlag = false;
        } else {
            disableFlag = !PerAndSubTypeEnum.api.equals(type);
        }
        List<JvsTree> periodsTree = list(new LambdaQueryWrapper<JvsTree>()
                .eq(JvsTree::getType, periodType)
                .ne(JvsTree::getParentId, JvsTree.DICT_ID_ROOT)
                .isNotNull(removeNullSubjectFlag, JvsTree::getRemarks)
                .orderByAsc(JvsTree::getSort));

        if (periodsTree.isEmpty()) {
            return null;
        }

        List<TreeVo> periods = BeanCopyUtil.copys(periodsTree, TreeVo.class);

        List<TreeVo> result = new ArrayList<>();

        for (TreeVo period : periods) {
            String treeValues = period.getRemarks();
            if (ObjectNull.isNull(treeValues)) {
                period.setDisabled(true);
                result.add(period);
                continue;
            }

            List<String> valueList = Arrays.asList(treeValues.split(","));
            List<JvsTree> allSubjectEntries = list(
                    new LambdaQueryWrapper<JvsTree>()
                            .eq(JvsTree::getType, subjectType)
                            .ne(JvsTree::getParentId, JvsTree.DICT_ID_ROOT)
                            .in(JvsTree::getValue, valueList)
                            .orderByAsc(JvsTree::getSort)
            );

            List<TreeVo> allSubEntries = BeanCopyUtil.copys(allSubjectEntries, TreeVo.class);

            List<String> uniqueNameList = allSubEntries.stream()
                    .map(TreeVo::getUniqueName)
                    .collect(Collectors.toList());

            List<TreeVo> subjectTrees = allSubEntries.stream()
                    .filter(entity -> !uniqueNameList.contains(entity.getParentId()))
                    .map(entity -> buildTree(entity, allSubEntries, period.getUniqueName(), disableFlag, hasSub))
                    .collect(Collectors.toList());

            period.setChildren(subjectTrees);
            //学段下没有学科默认不可选择
            if (!disableFlag) {
                period.setDisabled(subjectTrees.isEmpty());
            } else {
                period.setDisabled(true);
            }

            result.add(period);
        }
        // 设置只可选叶子节点
        if (PerAndSubTypeEnum.node.equals(type)) {
            result.forEach(e -> {
                if (e.getChildren() == null) {
                    e.setDisabled(false);
                }
            });
        }
        return result;
    }

    private TreeVo buildTree(TreeVo entity, List<TreeVo> allSubjectEntries, String parentUniqueName, boolean disableFlag, boolean hasSub) {
        String rawUniqueName = entity.getUniqueName();
        entity.setUniqueName(parentUniqueName + '-' + entity.getUniqueName());

        List<TreeVo> children;
        if (hasSub) {
            children = allSubjectEntries.stream()
                    .filter(child -> rawUniqueName.equals(child.getParentId()))
                    .map(child -> buildTree(child, allSubjectEntries, parentUniqueName, disableFlag, hasSub))
                    .collect(Collectors.toList());
        } else {
            children = new ArrayList<>();
        }

        if (!children.isEmpty()) {
            entity.setChildren(children);
            if (disableFlag) {
                entity.setDisabled(true);
            }
        } else {
            entity.setChildren(null);
            if (disableFlag) {
                entity.setDisabled(false);
            }
        }
        return entity;
    }

    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = Map.class)
    public Map<String, String> getPerAndSubMap(String periodType, String subjectType) {

        Map<String, String> map = new HashMap<>();

        List<JvsTree> periodsTree = list(new LambdaQueryWrapper<JvsTree>()
                .eq(JvsTree::getType, periodType)
                .isNotNull(JvsTree::getRemarks)
                .ne(JvsTree::getParentId, JvsTree.DICT_ID_ROOT)
                .orderByAsc(JvsTree::getSort));

        if (periodsTree.isEmpty()) {
            return map;
        }

        for (JvsTree period : periodsTree) {
            List<String> valueList = new ArrayList<>();
            splitter.split(period.getRemarks()).forEach(valueList::add);

            List<JvsTree> allSubjectEntries = list(
                    new LambdaQueryWrapper<JvsTree>()
                            .eq(JvsTree::getType, subjectType)
                            //排除外语
                            .ne(JvsTree::getValue, "40")
                            .ne(JvsTree::getParentId, JvsTree.DICT_ID_ROOT)
                            .in(JvsTree::getValue, valueList)
                            .orderByAsc(JvsTree::getSort)
            );

            allSubjectEntries.forEach(e -> map.put(period.getName() + "/" + e.getName(), period.getUniqueName() + "-" + e.getUniqueName()));
        }
        return map;
    }

    /**
     * 1. 根据学段字典的id去获取tree_value
     * 2. 然后拿这个value去获取年级字典中对应的学段
     * 3. 然后再获取下面的年级
     *
     * @param periodId 学段字段的学段id
     * @return
     */
    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = Map.class)
    public Map<String, String> getGradeMap(String periodId) {
        Map<String, String> map = new LinkedHashMap<>();

        JvsTree one = getOne(new LambdaQueryWrapper<JvsTree>().eq(JvsTree::getUniqueName, periodId).eq(JvsTree::getType, "period"));
        if (ObjectNull.isNull(one)) {
            return map;
        }

        JvsTree period = getOne(new LambdaQueryWrapper<JvsTree>()
                .eq(JvsTree::getValue, one.getValue())
                .eq(JvsTree::getType, "grade")
        );
        if (ObjectNull.isNull(period)) {
            return map;
        }

        List<JvsTree> gradeTree = list(new LambdaQueryWrapper<JvsTree>()
                .eq(JvsTree::getType, "grade")
                .eq(JvsTree::getParentId, period.getUniqueName())
                .ne(JvsTree::getParentId, JvsTree.DICT_ID_ROOT)
                .orderByAsc(JvsTree::getSort));

        if (gradeTree.isEmpty()) {
            return map;
        }

        gradeTree.forEach(e -> {
            map.put(e.getName(), e.getUniqueName());
        });
        return map;
    }

    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = JvsTree.class)
    public JvsTree getInfoByPath(String name, String type, String remarksFilter) {
        if (ObjectNull.isNull(name, type)) {
            return null;
        }
        String[] path = name.split("/");
        JvsTree tree = null;
        for (int i = 0; i < path.length; i++) {
            LambdaQueryWrapper<JvsTree> wrapper = new LambdaQueryWrapper<JvsTree>().eq(JvsTree::getType, type)
                    .eq(JvsTree::getName, path[i])
                    .eq(ObjectNull.isNotNull(remarksFilter), JvsTree::getRemarks, remarksFilter);
            if (i > 0) {
                wrapper.eq(JvsTree::getParentId, tree.getUniqueName());
            }
            tree = getOne(wrapper);
            if (ObjectNull.isNull(tree)) {
                return null;
            }
        }
        return tree;
    }

    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = Map.class)
    public Map<String, String> getNameMapByType(String type) {
        //会有name名称相同的问题(暂时适用于不重名的情况)
        Map<String, String> map = new HashMap<>();
        List<JvsTree> treeList = list(new LambdaQueryWrapper<JvsTree>().eq(JvsTree::getType, type)
                .ne(JvsTree::getParentId, JvsTree.DICT_ID_ROOT)
                .orderByAsc(JvsTree::getSort));
        treeList.forEach(e -> {
            map.put(e.getName(), e.getUniqueName());
        });

        return map;
    }

    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = Map.class)
    public Map<String, String> getUniMapByType(String type) {
        List<JvsTree> jvsTrees = list(new LambdaQueryWrapper<JvsTree>().eq(JvsTree::getType, type)
                .ne(JvsTree::getParentId, JvsTree.DICT_ID_ROOT)
                .orderByAsc(JvsTree::getSort));
        Map<String, String> map = jvsTrees.stream().collect(Collectors.toMap(
                JvsTree::getUniqueName,
                JvsTree::getName,
                (oldValue, newValue) -> oldValue,
                LinkedHashMap::new
        ));
        return map;
    }

    @Override
    @TreeCache(type = TreeCacheTypeEnum.function, clazz = Map.class)
    public Map<String, Object> getNationRegion() {
        String region3GdsUniqueName = "94225598344";
        String regionGdsUniqueName = "93225598344";
        String region3NationUniqueName = "942255983801896960";
        List<JvsTree> treeList = list(new LambdaQueryWrapper<JvsTree>()
                .ne(JvsTree::getUniqueName, region3GdsUniqueName)
                .ne(JvsTree::getParentId, region3GdsUniqueName)
                .eq(JvsTree::getType, "region_3").isNotNull(JvsTree::getUniqueName));

        List<JvsTree> gdsList = list(new LambdaQueryWrapper<JvsTree>()
                .eq(JvsTree::getType, "region").isNotNull(JvsTree::getUniqueName))
                .stream()
                .filter(e -> e.getUniqueName().length() <= 15)
                .peek(e -> {
                    if (regionGdsUniqueName.equals(e.getUniqueName())) {
                        e.setParentId(region3NationUniqueName);
                    }
                }).collect(Collectors.toList());

        treeList.addAll(gdsList);

        List<TreePo> items = treeList.stream()
                .map(e -> BeanCopyUtil.copy(e, TreePo.class).setExtend(e).setId(e.getUniqueName()))
                .collect(Collectors.toList());

        List<Tree<Object>> result = TreeUtils.tree(items, JvsTree.DICT_ID_ROOT);
        return result.get(0);
    }

    @Override
    public Boolean refreshCache() {
        dictTreeUtil.clearCache();
        return true;
    }
}