package cn.bctools.design.data.cache;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.data.fields.enums.DataFieldType;
import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 请求级别的回显缓存
 * 使用ThreadLocal存储单次请求的回显数据，请求结束后自动清空
 * 解决分页查询中重复查询数据源的性能问题
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RequestLevelEchoCache {
    
    /**
     * 请求级别的缓存存储
     * 结构：Map<cacheKey, Map<dataKey, echoValue>>
     * cacheKey: 字段类型+配置标识，如 "cascader:system:region"
     * dataKey: 具体的数据值，如 "110000"
     * echoValue: 回显值，如 "北京市"
     */
    private static final ThreadLocal<Map<String, Map<String, Object>>> REQUEST_CACHE =
            TransmittableThreadLocal.withInitial(HashMap::new);
    
    /**
     * 缓存统计信息（仅用于当前请求）
     */
    private static final ThreadLocal<Map<String, RequestCacheStats>> REQUEST_STATS =
            TransmittableThreadLocal.withInitial(HashMap::new);
    
    /**
     * 获取缓存值
     * 
     * @param fieldType 字段类型
     * @param configKey 配置标识（如dictName、modelId等）
     * @param dataKey 数据键
     * @return 缓存的回显值，如果不存在返回null
     */
    public Object getCachedValue(DataFieldType fieldType, String configKey, String dataKey) {
        if (ObjectNull.isNull(fieldType) || ObjectNull.isNull(configKey) || ObjectNull.isNull(dataKey)) {
            return null;
        }
        
        String cacheKey = buildCacheKey(fieldType, configKey);
        Map<String, Map<String, Object>> cache = REQUEST_CACHE.get();
        
        Map<String, Object> dataMapping = cache.get(cacheKey);
        if (ObjectNull.isNull(dataMapping)) {
            recordCacheMiss(cacheKey);
            return null;
        }
        
        Object value = dataMapping.get(dataKey);
        if (ObjectNull.isNotNull(value)) {
            recordCacheHit(cacheKey);
            return value;
        } else {
            recordCacheMiss(cacheKey);
            return null;
        }
    }
    
    /**
     * 批量获取缓存映射
     * 
     * @param fieldType 字段类型
     * @param configKey 配置标识
     * @return 整个数据映射，如果不存在返回null
     */
    public Map<String, Object> getCachedMapping(DataFieldType fieldType, String configKey) {
        if (ObjectNull.isNull(fieldType) || ObjectNull.isNull(configKey)) {
            return null;
        }
        
        String cacheKey = buildCacheKey(fieldType, configKey);
        Map<String, Map<String, Object>> cache = REQUEST_CACHE.get();
        
        return cache.get(cacheKey);
    }
    
    /**
     * 缓存数据映射
     * 
     * @param fieldType 字段类型
     * @param configKey 配置标识
     * @param dataMapping 数据映射（id->name的映射）
     */
    public void cacheMapping(DataFieldType fieldType, String configKey, Map<String, Object> dataMapping) {
        if (ObjectNull.isNull(fieldType) || ObjectNull.isNull(configKey) || ObjectNull.isNull(dataMapping)) {
            return;
        }
        
        String cacheKey = buildCacheKey(fieldType, configKey);
        Map<String, Map<String, Object>> cache = REQUEST_CACHE.get();
        
        // 使用ConcurrentHashMap确保线程安全（虽然是ThreadLocal，但防止并发修改）
        cache.put(cacheKey, new ConcurrentHashMap<>(dataMapping));
        
        log.debug("缓存数据映射: cacheKey={}, size={}", cacheKey, dataMapping.size());
    }
    
    /**
     * 缓存单个值
     * 
     * @param fieldType 字段类型
     * @param configKey 配置标识
     * @param dataKey 数据键
     * @param echoValue 回显值
     */
    public void cacheValue(DataFieldType fieldType, String configKey, String dataKey, Object echoValue) {
        if (ObjectNull.isNull(fieldType) || ObjectNull.isNull(configKey) || ObjectNull.isNull(dataKey)) {
            return;
        }
        
        String cacheKey = buildCacheKey(fieldType, configKey);
        Map<String, Map<String, Object>> cache = REQUEST_CACHE.get();
        
        cache.computeIfAbsent(cacheKey, k -> new ConcurrentHashMap<>())
             .put(dataKey, echoValue);
        
        log.debug("缓存单个值: cacheKey={}, dataKey={}", cacheKey, dataKey);
    }
    
    /**
     * 检查是否已缓存
     * 
     * @param fieldType 字段类型
     * @param configKey 配置标识
     * @return 是否已缓存该配置的数据
     */
    public boolean isCached(DataFieldType fieldType, String configKey) {
        if (ObjectNull.isNull(fieldType) || ObjectNull.isNull(configKey)) {
            return false;
        }
        
        String cacheKey = buildCacheKey(fieldType, configKey);
        Map<String, Map<String, Object>> cache = REQUEST_CACHE.get();
        
        return cache.containsKey(cacheKey);
    }
    
    /**
     * 构建缓存键
     * 
     * @param fieldType 字段类型
     * @param configKey 配置标识
     * @return 缓存键
     */
    private String buildCacheKey(DataFieldType fieldType, String configKey) {
        return fieldType.name() + ":" + configKey;
    }
    
    /**
     * 记录缓存命中
     */
    private void recordCacheHit(String cacheKey) {
        Map<String, RequestCacheStats> stats = REQUEST_STATS.get();
        stats.computeIfAbsent(cacheKey, k -> new RequestCacheStats()).hit();
    }
    
    /**
     * 记录缓存未命中
     */
    private void recordCacheMiss(String cacheKey) {
        Map<String, RequestCacheStats> stats = REQUEST_STATS.get();
        stats.computeIfAbsent(cacheKey, k -> new RequestCacheStats()).miss();
    }
    
    /**
     * 获取当前请求的缓存统计信息
     */
    public Map<String, RequestCacheStats> getCurrentRequestStats() {
        return new HashMap<>(REQUEST_STATS.get());
    }
    
    /**
     * 清空当前请求的缓存
     * 应在请求结束时调用
     */
    public void clearCurrentRequestCache() {
        Map<String, Map<String, Object>> cache = REQUEST_CACHE.get();
        Map<String, RequestCacheStats> stats = REQUEST_STATS.get();
        
        int totalCacheSize = cache.values().stream().mapToInt(Map::size).sum();
        
        if (log.isDebugEnabled() && totalCacheSize > 0) {
            log.debug("清空请求缓存: 缓存项数={}, 统计信息={}", totalCacheSize, stats);
        }
        
        cache.clear();
        stats.clear();
    }
    
    /**
     * 完全清理ThreadLocal
     * 防止内存泄漏，应在请求完全结束时调用
     */
    public void removeThreadLocalCache() {
        REQUEST_CACHE.remove();
        REQUEST_STATS.remove();
        log.debug("清理ThreadLocal缓存");
    }
    
    /**
     * 获取当前请求缓存大小
     */
    public int getCurrentCacheSize() {
        Map<String, Map<String, Object>> cache = REQUEST_CACHE.get();
        return cache.values().stream().mapToInt(Map::size).sum();
    }
    
    /**
     * 请求级缓存统计信息
     */
    public static class RequestCacheStats {
        private int hits = 0;
        private int misses = 0;
        
        public void hit() {
            hits++;
        }
        
        public void miss() {
            misses++;
        }
        
        public int getHits() {
            return hits;
        }
        
        public int getMisses() {
            return misses;
        }
        
        public double getHitRate() {
            int total = hits + misses;
            return total == 0 ? 0.0 : (double) hits / total;
        }
        
        @Override
        public String toString() {
            return String.format("RequestCacheStats{hits=%d, misses=%d, hitRate=%.2f%%}", 
                               hits, misses, getHitRate() * 100);
        }
    }
}