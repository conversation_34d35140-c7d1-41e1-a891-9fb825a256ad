package cn.bctools.design.data.fields.impl.advance;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.design.constant.DictConstant;
import cn.bctools.design.crud.entity.JvsTree;
import cn.bctools.design.crud.service.JvsTreeService;
import cn.bctools.design.data.entity.DynamicDataPo;
import cn.bctools.design.data.fields.DataFieldHandler;
import cn.bctools.design.data.fields.DesignField;
import cn.bctools.design.data.fields.IDataFieldHandler;
import cn.bctools.design.data.fields.dto.QueryConditionDto;
import cn.bctools.design.data.fields.dto.QueryListDto;
import cn.bctools.design.data.fields.dto.TreeDictDto;
import cn.bctools.design.data.fields.dto.form.FormValueHtml;
import cn.bctools.design.data.fields.dto.form.MultipleHtml;
import cn.bctools.design.data.fields.dto.form.item.CascaderItemHtml;
import cn.bctools.design.data.fields.enums.DataFieldType;
import cn.bctools.design.data.fields.enums.DataQueryType;
import cn.bctools.design.data.fields.impl.RequestCachedMultipleTypeHandler;
import cn.bctools.design.data.service.DynamicDataService;
import cn.bctools.design.rule.RuleStartUtils;
import cn.bctools.design.rule.entity.RuleDesignPo;
import cn.bctools.design.rule.entity.RunLogPo;
import cn.bctools.design.rule.service.RuleDesignService;
import cn.bctools.design.rule.service.RunLogService;
import cn.bctools.rule.entity.enums.RunType;
import cn.bctools.rule.utils.dto.RuleExecDto;
import cn.bctools.rule.utils.html.HtmlGraph;
import cn.bctools.rule.utils.html.RuleExecuteDto;
import cn.hutool.core.lang.tree.Tree;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 表单字段: 级联选择
 * 支持请求级缓存优化
 *
 * @Author: GuoZi
 */
@Slf4j
@Component
@DesignField(value = "级联选择", type = DataFieldType.cascader)
@AllArgsConstructor
public class CascaderFieldHandler extends RequestCachedMultipleTypeHandler implements IDataFieldHandler<CascaderItemHtml> {

    JvsTreeService jvsTreeService;

    //本地缓存，只对特定字典生效
    Map<String, List<TreeDictDto>> treeDictCache;

    @Override
    public List<DataQueryType> getEnabledQueryTypes(CascaderItemHtml html) {
        return super.getEnabledQueryTypes(html);
    }

    @Override
    public Object getEchoValue(CascaderItemHtml cascaderItem, Object data, boolean override, Map<String, Object> lineData, String... paths) {
        // 使用请求级缓存优化回显处理
        return echoValueWithRequestCache(
                cascaderItem,
                data,
                lineData,
                DataFieldType.cascader,
                this::getConfigKey,
                cacheKey -> loadDataSource(cascaderItem, cacheKey)
        );
    }

    /**
     * 原始的回显处理方法（作为回退方案）
     * 重构后的版本，提高代码质量和性能
     */
    private Object getEchoValueOriginal(CascaderItemHtml cascaderItem, Object data, boolean override, Map<String, Object> lineData, String... paths) {
        if (ObjectNull.isNull(cascaderItem) || ObjectNull.isNull(data)) {
            return data;
        }

        // 提取公共变量，避免重复计算
        final boolean isMulti = Boolean.TRUE.equals(cascaderItem.getMultiple());
        final boolean showPath = Boolean.TRUE.equals(cascaderItem.getShowalllevels());

        // 缓存Spring Bean，避免重复获取
        final DataFieldHandler dataFieldHandler = getDataFieldHandler();

        try {
            switch (cascaderItem.getDatatype()) {
                case option:
                    return handleOptionType(cascaderItem, data, isMulti, showPath, dataFieldHandler);

                case dataModel:
                    return handleDataModelType(cascaderItem, data, isMulti, showPath, dataFieldHandler);

                case system:
                    return handleSystemType(cascaderItem, data, isMulti, showPath, dataFieldHandler);

                case rule:
                    return handleRuleType(cascaderItem, data, lineData, isMulti, showPath);

                default:
                    log.warn("不支持的级联选择数据类型: {}", cascaderItem.getDatatype());
                    return data;
            }
        } catch (Exception e) {
            log.error("级联选择回显处理失败: datatype={}, error={}",
                     cascaderItem.getDatatype(), e.getMessage(), e);
            return data; // 异常时返回原始数据
        }
    }

    public Object getRuleValue(CascaderItemHtml cascaderItem, String optionHttp, Map<String, Object> lineData, Object data, boolean showPath) {
        RunLogService logService = SpringContextUtil.getApplicationContext().getBean(RunLogService.class);
        RuleStartUtils ruleStartUtils = SpringContextUtil.getApplicationContext().getBean(RuleStartUtils.class);
        RuleDesignService ruleDesignService = SpringContextUtil.getApplicationContext().getBean(RuleDesignService.class);
        //逻辑如果需要传递参数，直接查询数据库修改Data即可
        RuleDesignPo po = ruleDesignService.getEnableDesign(optionHttp);
        if (ObjectNull.isNotNull(po)) {
            RunLogPo logPo = logService.create(po.getJvsAppId(), po.getSecret(), RunType.REAL, new HashMap<>(), false);
            RuleExecuteDto dto = new RuleExecuteDto().setReqVariableMap(lineData).setVariableMap(lineData);
            RuleExecDto ruleExecDto = new RuleExecDto()
                    .setExecuteDto(dto)
                    .setType(RunType.REAL)
                    .setSecret(po.getSecret())
                    .setGraph(JSONObject.parseObject(po.getDesignDrawingJson(), HtmlGraph.class));
            ruleStartUtils.start(po, logPo, ruleExecDto);
            if (ObjectNull.isNotNull(ruleExecDto.getExecuteDto().getEndResult())) {
                Object value = ruleExecDto.getExecuteDto().getEndResult().getValue();
                if (value instanceof List) {
                    List<Map> list = JSONArray.parseArray(JSONObject.toJSONString(value)).stream().map(e -> BeanCopyUtil.copy(e,Map.class)).collect(Collectors.toList());
                    List<FormValueHtml> copys = getFormValueHtml(list, cascaderItem.getProps().getLabel(), cascaderItem.getProps().getValue());
                    if (data instanceof List) {
                        return ((List<?>) data).stream().map(e -> getLabel(e.toString(), copys, showPath))
                                .filter(ObjectNull::isNotNull)
                                .collect(Collectors.joining(","));
                    } else {
                        return getLabel(data.toString(), copys, showPath);
                    }
                }
            }
        }
        if (ObjectNull.isNull(data)) {
            return "";
        }
        return "字典未匹配" + JSONObject.toJSONString(data);
    }

    /**
     * 递归根据属性组装属性对象值
     *
     * @param list  树形数据
     * @param label 组件属性指定的 label  key
     * @param value 组件属性指定的 value  key
     * @return
     */
    private List<FormValueHtml> getFormValueHtml(List<Map> list, String label, String value) {
        List<FormValueHtml> html = new ArrayList<>();
        for (Map jsonObject : list) {
            List children = (List) jsonObject.get("children");
            FormValueHtml formValueHtml = new FormValueHtml().setLabel(jsonObject.get(label).toString()).setValue(jsonObject.get(value).toString());
            if (ObjectNull.isNotNull(children)) {
                //装下一层
                formValueHtml.setChildren(getFormValueHtml(children, label, value));
            }
            html.add(formValueHtml);
        }
        return html;
    }


    @Override
    public Object getConversionKey(CascaderItemHtml cascaderItem, Object data, Map<String, Object> oldData) {
        //递归循环查询，是否存在这样的数据
        List<String> collect = Arrays.stream(data.toString().split("/")).map(String::trim).filter(ObjectNull::isNotNull).collect(Collectors.toList());
        switch (cascaderItem.getDatatype()) {
            case option:

            case dataModel:
                //此处直接跳过数据模型
                List<String> fieldList = new ArrayList<>();
                //显示字段
                fieldList.add(cascaderItem.getProps().getLabel());
                //传递字段
                fieldList.add(cascaderItem.getProps().getValue());
                //父级字段
                fieldList.add(cascaderItem.getProps().getSecTitle());
                //遍历数据,找出名称与这个字段相同的数据
                Object next = getNextCascaderId(collect, 0, fieldList, cascaderItem);
                return next;
            //根据模型获取字段
            case system:
                Map<String, Object> byUniqueName = jvsTreeService.getByUniqueName(cascaderItem.getDictName());
                return getNextSystem(collect, 0, (List<Tree>) byUniqueName.get("children"));
            case rule:
                //逻辑引擎获取级联数据
                String optionHttp = cascaderItem.getOptionHttp();
                if (ObjectNull.isNotNull(optionHttp)) {
                    RunLogService logService = SpringContextUtil.getApplicationContext().getBean(RunLogService.class);
                    RuleStartUtils ruleStartUtils = SpringContextUtil.getApplicationContext().getBean(RuleStartUtils.class);
                    RuleDesignService ruleDesignService = SpringContextUtil.getApplicationContext().getBean(RuleDesignService.class);
                    //逻辑如果需要传递参数，直接查询数据库修改Data即可
                    RuleDesignPo po = ruleDesignService.getEnableDesign(optionHttp);
                    if (ObjectNull.isNotNull(po)) {
                        RunLogPo logPo = logService.create(po.getJvsAppId(), po.getSecret(), RunType.REAL, new HashMap<>(), false);
                        RuleExecuteDto dto = new RuleExecuteDto().setReqVariableMap(oldData).setVariableMap(oldData);
                        RuleExecDto ruleExecDto = new RuleExecDto()
                                .setExecuteDto(dto)
                                .setType(RunType.REAL)
                                .setSecret(po.getSecret())
                                .setGraph(JSONObject.parseObject(po.getDesignDrawingJson(), HtmlGraph.class));
                        ruleStartUtils.start(po, logPo, ruleExecDto);
                        if (ObjectNull.isNotNull(ruleExecDto.getExecuteDto().getEndResult())) {
                            Object value = ruleExecDto.getExecuteDto().getEndResult().getValue();
                            if (value instanceof List) {
                                List<JSONObject> valueArray = BeanCopyUtil.copys((List) value, JSONObject.class);
                                List<FormValueHtml> copys = valueArray.stream()
                                        .map(e -> new FormValueHtml().setLabel(e.getString(cascaderItem.getProps().getLabel())).setValue(e.getString(cascaderItem.getProps().getValue())))
                                        .collect(Collectors.toList());
                                return getValue(data.toString(), copys);
                            }
                        } else {
                            if (ObjectNull.isNull(data)) {
                                return "";
                            }
                            return "字典未匹配" + JSONObject.toJSONString(data);
                        }
                    }
                }

            default:
                return null;
        }
    }

    private Object getNextSystem(List<String> collect, int i, List<Tree> list) {
        String s = collect.get(i);
        if (ObjectNull.isNotNull(list)) {
            Optional<Tree> first = list.stream().filter(e -> e.getName().equals(s)).findFirst();
            if (first.isPresent()) {
                if (i + 1 == collect.size()) {
                    return first.get().getId();
                }
                return getNextSystem(collect, ++i, first.get().getChildren());
            }
        }
        return null;
    }

    private Object getNextCascaderId(List<String> collect, int i, List<String> fieldList, CascaderItemHtml cascaderItem) {
        DynamicDataService dynamicDataService = SpringContextUtil.getBean(DynamicDataService.class);
        QueryListDto dto = new QueryListDto();
        dto.setFieldList(fieldList);
        dto.setConditions(Arrays.asList(new QueryConditionDto().setEnabledQueryTypes(DataQueryType.eq).setValue(collect.get(i)).setFieldKey(cascaderItem.getProps().getLabel())));
        List<Map<String, Object>> maps = dynamicDataService.postQueryList(cascaderItem.getFormId(), dto);
        if (ObjectNull.isNotNull(maps)) {
            if (i + 1 == collect.size()) {
                return maps.get(0).get("id");
            } else {
                return getNextCascaderId(collect, ++i, fieldList, cascaderItem);
            }
        }
        return null;
    }

    /**
     * 获取树形字典项的集合
     *
     * @param dictUniqueName 字典标识
     * @return 字典项集合
     */
    public List<TreeDictDto> getTreeDict(String dictUniqueName) {
        List<TreeDictDto> result = new ArrayList<>();
        Map<String, String> idMap = new HashMap<>(64);
        Deque<Map<String, Object>> keyQueue = new ArrayDeque<>(64);
        // 查询树形字典数据
        Map<String, Object> treeDict = jvsTreeService.getByUniqueName(dictUniqueName);
        keyQueue.add(treeDict);
        while (ObjectNull.isNotNull(keyQueue)) {
            Map<String, Object> poll = keyQueue.poll();
            JvsTree extend = BeanCopyUtil.copy(poll, JvsTree.class);
            String id = extend.getUniqueName();
            String name = extend.getName();
            String parentId = extend.getParentId();
            String uniqueName = extend.getUniqueName();
            TreeDictDto dict = new TreeDictDto().setId(id).setName(name).setParentId(parentId);
            result.add(dict);
            // 记录节点id与uniqueName的对应关系
            idMap.put(id, uniqueName);
            // 添加子节点
            List<Map<String, Object>> children = (List<Map<String, Object>>) poll.get("children");
            if (ObjectUtils.isNotEmpty(children)) {
                keyQueue.addAll(children);
            }
        }
        // 因为前端的传递值使用的是uniqueName, 这里将id都换成uniqueName
        for (TreeDictDto dict : result) {
            String newId = idMap.get(dict.getId());
            String newParentId = idMap.get(dict.getParentId());
            dict.setId(newId);
            dict.setParentId(newParentId);
        }
        return result;
    }

    @Override
    public Boolean whetherCoverValue() {
        return Boolean.FALSE;
    }

    // ========== 请求级缓存相关方法 ==========

    @Override
    protected String getConfigKey(MultipleHtml fieldDto) {
        CascaderItemHtml cascaderItem = (CascaderItemHtml) fieldDto;
        // 根据不同的数据类型生成不同的配置键
        switch (cascaderItem.getDatatype()) {
            case system:
                return "system:" + cascaderItem.getDictName();
            case dataModel:
                return "dataModel:" + cascaderItem.getFormId() + ":" +
                        cascaderItem.getProps().getValue() + ":" +
                        cascaderItem.getProps().getLabel();
            case option:
                return "option:" + cascaderItem.hashCode();
            case rule:
//                return "rule:" + cascaderItem.getOptionHttp();
                //逻辑回显暂无法适配
                return null;
            default:
                return "unknown:" + cascaderItem.hashCode();
        }
    }

    @Override
    protected DataFieldType getFieldType() {
        return DataFieldType.cascader;
    }

    // 回退到原始处理方式
    @Override
    protected Object echoValueFallback(MultipleHtml fieldDto, Object data, Map<String, Object> lineData) {
        CascaderItemHtml cascaderItem = (CascaderItemHtml) fieldDto;
        return getEchoValueOriginal(cascaderItem, data, false, lineData);
    }

    /**
     * 加载数据源
     * 只在缓存未命中时调用
     */
    private Map<String, Object> loadDataSource(CascaderItemHtml cascaderItem, String cacheKey) {
        try {
            log.debug("加载级联选择数据源: datatype={}, cacheKey={}",
                    cascaderItem.getDatatype(), cacheKey);

            switch (cascaderItem.getDatatype()) {
                case system:
                    return loadSystemDictData(cascaderItem.getDictName());
                case dataModel:
                    return loadDataModelData(cascaderItem);
                case option:
                    return loadOptionData(cascaderItem);
                case rule:
                    return loadRuleData(cascaderItem);
                default:
                    log.warn("不支持的级联选择数据类型: {}", cascaderItem.getDatatype());
                    return new HashMap<>();
            }
        } catch (Exception e) {
            log.error("加载级联选择数据源失败: cacheKey={}", cacheKey, e);
            return new HashMap<>();
        }
    }

    /**
     * 加载系统字典数据
     */
    private Map<String, Object> loadSystemDictData(String dictName) {
        try {
            List<TreeDictDto> dictList = this.getTreeDictCache(dictName);
            if (ObjectNull.isNotNull(dictList)) {
                return dictList.stream()
                        .collect(Collectors.toMap(
                                TreeDictDto::getId,
                                TreeDictDto::getName,
                                (existing, replacement) -> existing
                        ));
            }
        } catch (Exception e) {
            log.warn("加载系统字典数据失败: dictName={}", dictName, e);
        }
        return new HashMap<>();
    }

    /**
     * 加载数据模型数据
     */
    private Map<String, Object> loadDataModelData(CascaderItemHtml cascaderItem) {
        try {
            DynamicDataService dynamicDataService = SpringContextUtil.getBean(DynamicDataService.class);
            List<Map<String, Object>> dictList = dynamicDataService.queryList(
                    cascaderItem.getFormId(),
                    cascaderItem.getProps().getLabel(),
                    cascaderItem.getProps().getSecTitle()
            );

            if (ObjectNull.isNotNull(dictList)) {
                return dictList.stream()
                        .filter(e -> ObjectNull.isNotNull(e.get(cascaderItem.getProps().getValue())))
                        .collect(Collectors.toMap(
                                e -> e.get(cascaderItem.getProps().getValue()).toString(),
                                e -> e.get(cascaderItem.getProps().getLabel()),
                                (existing, replacement) -> existing
                        ));
            }
        } catch (Exception e) {
            log.warn("加载数据模型数据失败: formId={}", cascaderItem.getFormId(), e);
        }
        return new HashMap<>();
    }

    /**
     * 加载选项数据
     */
    private Map<String, Object> loadOptionData(CascaderItemHtml cascaderItem) {
        try {
            Map<String, Object> optionMap = new HashMap<>();

            // 处理选项配置，转换为id->name的映射
            if (ObjectNull.isNotNull(cascaderItem.getDicData()) && !cascaderItem.getDicData().isEmpty()) {
                cascaderItem.getDicData().forEach(option -> {
                    if (ObjectNull.isNotNull(option.getValue()) && ObjectNull.isNotNull(option.getLabel())) {
                        optionMap.put(option.getValue().toString(), option.getLabel());
                    }
                });
                log.debug("加载选项数据完成: size={}", optionMap.size());
            } else {
                log.debug("选项数据为空");
            }

            return optionMap;
        } catch (Exception e) {
            log.error("加载选项数据失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 加载规则引擎数据
     */
    private Map<String, Object> loadRuleData(CascaderItemHtml cascaderItem) {
        try {
            String optionHttp = cascaderItem.getOptionHttp();
            if (ObjectNull.isNull(optionHttp)) {
                log.warn("规则引擎配置为空");
                return new HashMap<>();
            }

            // 获取规则引擎相关服务
            RuleDesignService ruleDesignService = SpringContextUtil.getApplicationContext().getBean(RuleDesignService.class);
            RunLogService logService = SpringContextUtil.getApplicationContext().getBean(RunLogService.class);
            RuleStartUtils ruleStartUtils = SpringContextUtil.getApplicationContext().getBean(RuleStartUtils.class);

            // 获取规则设计
            RuleDesignPo po = ruleDesignService.getEnableDesign(optionHttp);
            if (ObjectNull.isNull(po)) {
                log.warn("找不到有效的规则设计: optionHttp={}", optionHttp);
                return new HashMap<>();
            }

            // 创建执行日志
            RunLogPo logPo = logService.create(po.getJvsAppId(), po.getSecret(), RunType.REAL, new HashMap<>(), false);

            // 准备执行参数（用空的lineData来获取所有可能的选项）
            Map<String, Object> emptyLineData = new HashMap<>();
            RuleExecuteDto dto = new RuleExecuteDto()
                .setReqVariableMap(emptyLineData)
                .setVariableMap(emptyLineData);

            RuleExecDto ruleExecDto = new RuleExecDto()
                .setExecuteDto(dto)
                .setType(RunType.REAL)
                .setSecret(po.getSecret())
                .setGraph(JSONObject.parseObject(po.getDesignDrawingJson(), HtmlGraph.class));

            // 执行规则获取选项数据
            ruleStartUtils.start(po, logPo, ruleExecDto);

            Map<String, Object> ruleDataMap = new HashMap<>();

            // 处理规则执行结果
            if (ObjectNull.isNotNull(ruleExecDto.getExecuteDto().getEndResult())) {
                Object value = ruleExecDto.getExecuteDto().getEndResult().getValue();

                if (value instanceof List) {
                    List<Map> list = JSONArray.parseArray(JSONObject.toJSONString(value))
                        .stream()
                        .map(e -> BeanCopyUtil.copy(e, Map.class))
                        .collect(Collectors.toList());

                    // 转换为id->name的映射
                    String valueField = cascaderItem.getProps().getValue();
                    String labelField = cascaderItem.getProps().getLabel();

                    for (Map<String, Object> item : list) {
                        Object id = item.get(valueField);
                        Object name = item.get(labelField);
                        if (ObjectNull.isNotNull(id) && ObjectNull.isNotNull(name)) {
                            ruleDataMap.put(id.toString(), name);
                        }
                    }

                    log.debug("规则引擎数据加载完成: optionHttp={}, size={}", optionHttp, ruleDataMap.size());
                } else if (ObjectNull.isNotNull(value)) {
                    // 如果返回的是单个值，尝试解析
                    log.debug("规则引擎返回单个值: {}", value);
                }
            } else {
                log.debug("规则引擎执行无结果: optionHttp={}", optionHttp);
            }

            return ruleDataMap;
        } catch (Exception e) {
            log.error("加载规则引擎数据失败: optionHttp={}", cascaderItem.getOptionHttp(), e);
            return new HashMap<>();
        }
    }

    // ========== 重构后的子方法 ==========

    /**
     * 获取DataFieldHandler Bean（缓存避免重复获取）
     */
    private DataFieldHandler getDataFieldHandler() {
        return SpringContextUtil.getBean(DataFieldHandler.class);
    }

    /**
     * 处理选项类型
     */
    private Object handleOptionType(CascaderItemHtml cascaderItem, Object data,
                                   boolean isMulti, boolean showPath, DataFieldHandler dataFieldHandler) {
        try {
            log.debug("处理选项类型级联选择: fieldKey={}", cascaderItem.getFieldKey());

            // 检查选项配置
            if (ObjectNull.isNull(cascaderItem.getDicData()) || cascaderItem.getDicData().isEmpty()) {
                log.debug("选项配置为空，返回原始数据");
                return data;
            }

            // 构建选项映射
            Map<String, Object> optionMap = new HashMap<>();
            List<FormValueHtml> optionList = new ArrayList<>();

            for (FormValueHtml option : cascaderItem.getDicData()) {
                if (ObjectNull.isNotNull(option.getValue()) && ObjectNull.isNotNull(option.getLabel())) {
                    String valueStr = option.getValue().toString();
                    optionMap.put(valueStr, option.getLabel());

                    // 构建用于路径处理的选项列表
                    FormValueHtml optionItem = new FormValueHtml();
                    optionItem.setValue(option.getValue());
                    optionItem.setLabel(option.getLabel());
                    // 设置父级关系（如果有的话）
                    if (ObjectNull.isNotNull(option.getParentId())) {
                        optionItem.setParentId(option.getParentId());
                    }
                    optionList.add(optionItem);
                }
            }

            if (optionMap.isEmpty()) {
                log.debug("有效选项为空，返回原始数据");
                return data;
            }

            // 如果需要显示路径，处理层级关系
            if (showPath && hasHierarchicalStructure(optionList)) {
                Object processedData = dataFieldHandler.handlePathId(
                    data, isMulti, showPath, optionList,
                    option -> option.getValue().toString(),
                    option -> ObjectNull.isNotNull(option.getParentId()) ? option.getParentId().toString() : "-1"
                );
                return dataFieldHandler.joinFormItems(optionMap, processedData, isMulti, showPath);
            } else {
                // 简单的值映射
                return dataFieldHandler.joinFormItems(optionMap, data, isMulti, false);
            }

        } catch (Exception e) {
            log.error("处理选项类型失败: fieldKey={}", cascaderItem.getFieldKey(), e);
            return data;
        }
    }

    /**
     * 检查选项列表是否具有层级结构
     */
    private boolean hasHierarchicalStructure(List<FormValueHtml> optionList) {
        return optionList.stream()
            .anyMatch(option -> ObjectNull.isNotNull(option.getParentId()) &&
                               !"-1".equals(option.getParentId().toString()) &&
                               !"0".equals(option.getParentId().toString()));
    }

    /**
     * 处理数据模型类型
     */
    private Object handleDataModelType(CascaderItemHtml cascaderItem, Object data,
                                      boolean isMulti, boolean showPath, DataFieldHandler dataFieldHandler) {
        try {
            final String formId = cascaderItem.getFormId();
            if (ObjectNull.isNull(formId)) {
                log.warn("数据模型类型缺少formId配置");
                return data;
            }

            final DynamicDataService dynamicDataService = SpringContextUtil.getBean(DynamicDataService.class);
            final String labelField = cascaderItem.getProps().getLabel();
            final String valueField = cascaderItem.getProps().getValue();
            final String parentField = cascaderItem.getProps().getSecTitle();

            if (showPath) {
                return handleDataModelWithPath(dynamicDataService, formId, labelField, parentField,
                                             data, isMulti, showPath, dataFieldHandler);
            } else {
                return handleDataModelSingle(dynamicDataService, formId, labelField, data);
            }
        } catch (Exception e) {
            log.error("处理数据模型类型失败: formId={}", cascaderItem.getFormId(), e);
            return data;
        }
    }

    /**
     * 处理数据模型类型（带路径）
     */
    private Object handleDataModelWithPath(DynamicDataService dynamicDataService, String formId,
                                          String labelField, String parentField, Object data,
                                          boolean isMulti, boolean showPath, DataFieldHandler dataFieldHandler) {
        try {
            List<Map<String, Object>> dictList = dynamicDataService.queryList(formId, labelField, parentField);
            if (ObjectNull.isNull(dictList) || dictList.isEmpty()) {
                log.debug("数据模型查询结果为空: formId={}", formId);
                return data;
            }

            Map<String, Object> dataMap = dictList.stream()
                .filter(item -> ObjectNull.isNotNull(item.get(labelField)) && ObjectNull.isNotNull(item.get("id")))
                .collect(Collectors.toMap(
                    item -> item.get("id").toString(),
                    item -> item.get(labelField),
                    (existing, replacement) -> existing
                ));

            Object processedData = dataFieldHandler.handlePathId(data, isMulti, showPath, dictList,
                item -> item.get("id").toString(),
                item -> item.getOrDefault(parentField, "-1").toString());

            return dataFieldHandler.joinFormItems(dataMap, processedData, isMulti, showPath);
        } catch (Exception e) {
            log.error("处理数据模型路径失败: formId={}", formId, e);
            return data;
        }
    }

    /**
     * 处理数据模型类型（单个值）
     */
    private Object handleDataModelSingle(DynamicDataService dynamicDataService, String formId,
                                        String labelField, Object data) {
        try {
            if (ObjectNull.isNull(data)) {
                return null;
            }

            DynamicDataPo result = dynamicDataService.getById(formId, data.toString());
            if (ObjectNull.isNotNull(result) && ObjectNull.isNotNull(result.getJsonData())) {
                Object labelValue = result.getJsonData().get(labelField);
                return ObjectNull.isNotNull(labelValue) ? labelValue : data;
            }

            return data;
        } catch (Exception e) {
            log.error("查询数据模型单个值失败: formId={}, dataId={}", formId, data, e);
            return data;
        }
    }

    /**
     * 处理系统字典类型
     */
    private Object handleSystemType(CascaderItemHtml cascaderItem, Object data,
                                   boolean isMulti, boolean showPath, DataFieldHandler dataFieldHandler) {
        try {
            final String dictName = cascaderItem.getDictName();
            if (ObjectNull.isNull(dictName)) {
                log.warn("系统字典类型缺少dictName配置");
                return data;
            }

            List<TreeDictDto> dictList = getTreeDictCache(dictName);
            if (ObjectNull.isNull(dictList) || dictList.isEmpty()) {
                log.debug("系统字典查询结果为空: dictName={}", dictName);
                return data;
            }

            Map<String, Object> dictMap = dictList.stream()
                .collect(Collectors.toMap(
                    TreeDictDto::getId,
                    TreeDictDto::getName,
                    (existing, replacement) -> existing
                ));

            Object processedData = dataFieldHandler.handlePathId(
                data, isMulti, showPath, dictList,
                TreeDictDto::getId, TreeDictDto::getParentId
            );

            return dataFieldHandler.joinFormItems(dictMap, processedData, isMulti, showPath);
        } catch (Exception e) {
            log.error("处理系统字典类型失败: dictName={}", cascaderItem.getDictName(), e);
            return data;
        }
    }

    /**
     * 处理规则类型
     */
    private Object handleRuleType(CascaderItemHtml cascaderItem, Object data,
                                 Map<String, Object> lineData, boolean isMulti, boolean showPath) {
        try {
            final String optionHttp = cascaderItem.getOptionHttp();
            if (ObjectNull.isNull(optionHttp)) {
                log.warn("规则类型缺少optionHttp配置");
                return data;
            }

            // 处理多选情况
            if (isMulti && data instanceof List ||
                (data != null && JSON.isValidArray(JSON.toJSONString(data)))) {

                return JSON.parseArray(JSON.toJSONString(data)).stream()
                    .map(item -> getRuleValue(cascaderItem, optionHttp, lineData, item, showPath))
                    .filter(ObjectNull::isNotNull)
                    .map(Object::toString)
                    .collect(Collectors.joining(","));
            } else {
                // 处理单选情况
                return getRuleValue(cascaderItem, optionHttp, lineData, data, showPath);
            }
        } catch (Exception e) {
            log.error("处理规则类型失败: optionHttp={}", cascaderItem.getOptionHttp(), e);
            return data;
        }
    }

    /**
     * 优化的getRuleValue方法
     * 添加了更好的异常处理和日志
     */
    private Object getRuleValue(CascaderItemHtml cascaderItem, String optionHttp,
                              Map<String, Object> lineData, Object data, boolean showPath) {
        try {
            RunLogService logService = SpringContextUtil.getApplicationContext().getBean(RunLogService.class);
            RuleStartUtils ruleStartUtils = SpringContextUtil.getApplicationContext().getBean(RuleStartUtils.class);
            RuleDesignService ruleDesignService = SpringContextUtil.getApplicationContext().getBean(RuleDesignService.class);

            // 获取规则设计
            RuleDesignPo po = ruleDesignService.getEnableDesign(optionHttp);
            if (ObjectNull.isNull(po)) {
                log.warn("找不到有效的规则设计: optionHttp={}", optionHttp);
                return data;
            }

            // 创建日志
            RunLogPo logPo = logService.create(po.getJvsAppId(), po.getSecret(), RunType.REAL, new HashMap<>(), false);

            // 准备执行参数
            RuleExecuteDto dto = new RuleExecuteDto()
                .setReqVariableMap(lineData)
                .setVariableMap(lineData);

            RuleExecDto ruleExecDto = new RuleExecDto()
                .setExecuteDto(dto)
                .setType(RunType.REAL)
                .setSecret(po.getSecret())
                .setGraph(JSONObject.parseObject(po.getDesignDrawingJson(), HtmlGraph.class));

            // 执行规则
            ruleStartUtils.start(po, logPo, ruleExecDto);

            // 处理结果
            if (ObjectNull.isNotNull(ruleExecDto.getExecuteDto().getEndResult())) {
                Object value = ruleExecDto.getExecuteDto().getEndResult().getValue();

                if (value instanceof List) {
                    List<Map> list = JSONArray.parseArray(JSONObject.toJSONString(value)).toJavaList(Map.class);

                    List<FormValueHtml> formValues = getFormValueHtml(
                        list,
                        cascaderItem.getProps().getLabel(),
                        cascaderItem.getProps().getValue()
                    );

                    // 根据showPath和data处理结果
                    return processRuleResult(formValues, data, showPath);
                } else {
                    return value;
                }
            }

            return data;
        } catch (Exception e) {
            log.error("执行规则失败: optionHttp={}", optionHttp, e);
            return data;
        }
    }

    /**
     * 处理规则执行结果
     */
    private Object processRuleResult(List<FormValueHtml> formValues, Object data, boolean showPath) {
        try {
            if (ObjectNull.isNull(formValues) || formValues.isEmpty()) {
                log.debug("规则执行结果为空");
                return data;
            }

            // 构建值映射
            Map<String, Object> valueMap = formValues.stream()
                .filter(item -> ObjectNull.isNotNull(item.getValue()) && ObjectNull.isNotNull(item.getLabel()))
                .collect(Collectors.toMap(
                    item -> item.getValue().toString(),
                    FormValueHtml::getLabel,
                    (existing, replacement) -> existing
                ));

            if (valueMap.isEmpty()) {
                log.debug("规则结果映射为空");
                return data;
            }

            // 获取DataFieldHandler
            DataFieldHandler dataFieldHandler = getDataFieldHandler();

            // 根据数据类型处理
            if (data instanceof List) {
                // 处理多选情况
                List<?> dataList = (List<?>) data;
                List<String> resultList = dataList.stream()
                    .filter(ObjectNull::isNotNull)
                    .map(item -> {
                        Object mappedValue = valueMap.get(item.toString());
                        return ObjectNull.isNotNull(mappedValue) ? mappedValue.toString() : item.toString();
                    })
                    .collect(Collectors.toList());

                return String.join(",", resultList);
            } else if (ObjectNull.isNotNull(data)) {
                // 处理单选情况
                Object mappedValue = valueMap.get(data.toString());
                return ObjectNull.isNotNull(mappedValue) ? mappedValue : data;
            }

            return data;
        } catch (Exception e) {
            log.error("处理规则结果失败", e);
            return data;
        }
    }

    public List<TreeDictDto> getTreeDictCache(String dictUniqueName) {
        List<TreeDictDto> treeDictDtoList;
        //广东省四级区划行政字典
        if (DictConstant.DICT_REGION_UNIQUE_NAME.equals(dictUniqueName)) {
            treeDictDtoList = treeDictCache.get(dictUniqueName);
            if (null == treeDictDtoList) {
                treeDictDtoList = getTreeDict(dictUniqueName);
                treeDictCache.put(dictUniqueName, treeDictDtoList);
            } else {
            }
        } else {
            treeDictDtoList = getTreeDict(dictUniqueName);
        }
        return treeDictDtoList;
    }
}