package cn.bctools.design.data.fields.impl.advance;

import cn.bctools.common.entity.po.TreePo;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.common.utils.function.Get;
import cn.bctools.design.constant.DictConstant;
import cn.bctools.design.crud.entity.JvsTree;
import cn.bctools.design.crud.service.JvsTreeService;
import cn.bctools.design.data.fields.DataFieldHandler;
import cn.bctools.design.data.fields.DesignField;
import cn.bctools.design.data.fields.IDataFieldHandler;
import cn.bctools.design.data.fields.dto.QueryConditionDto;
import cn.bctools.design.data.fields.dto.QueryListDto;
import cn.bctools.design.data.fields.dto.TreeDictDto;
import cn.bctools.design.data.fields.dto.form.FormValueHtml;
import cn.bctools.design.data.fields.dto.form.item.CascaderItemHtml;
import cn.bctools.design.data.fields.enums.DataFieldType;
import cn.bctools.design.data.fields.enums.DataQueryType;
import cn.bctools.design.data.fields.impl.IMultipleTypeHandler;
import cn.bctools.design.data.service.DynamicDataService;
import cn.bctools.design.rule.RuleStartUtils;
import cn.bctools.design.rule.entity.RuleDesignPo;
import cn.bctools.design.rule.entity.RunLogPo;
import cn.bctools.design.rule.service.RuleDesignService;
import cn.bctools.design.rule.service.RunLogService;
import cn.bctools.rule.entity.enums.RunType;
import cn.bctools.rule.utils.dto.RuleExecDto;
import cn.bctools.rule.utils.html.HtmlGraph;
import cn.bctools.rule.utils.html.RuleExecuteDto;
import cn.hutool.core.lang.tree.Tree;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 表单字段: 级联选择
 *
 * @Author: GuoZi
 */
@Slf4j
@Component
@DesignField(value = "级联选择", type = DataFieldType.cascader)
@AllArgsConstructor
public class CascaderFieldHandler extends IMultipleTypeHandler implements IDataFieldHandler<CascaderItemHtml> {

    JvsTreeService jvsTreeService;

    //本地缓存，只对特定字典生效
    Map<String, List<TreeDictDto>> treeDictCache = new ConcurrentHashMap<>();

    @Override
    public List<DataQueryType> getEnabledQueryTypes(CascaderItemHtml html) {
        return super.getEnabledQueryTypes(html);
    }

    @Override
    public Object getEchoValue(CascaderItemHtml cascaderItem, Object data, boolean override, Map<String, Object> lineData, String... paths) {
        String dictName = cascaderItem.getDictName();
        boolean isMulti = Boolean.TRUE.equals(cascaderItem.getMultiple());
        boolean showPath = Boolean.TRUE.equals(cascaderItem.getShowalllevels());
        DataFieldHandler dataFieldHandler = SpringContextUtil.getBean(DataFieldHandler.class);
        switch (cascaderItem.getDatatype()) {
            case option:

            case dataModel:
                DynamicDataService dynamicDataService = SpringContextUtil.getBean(DynamicDataService.class);
                //此处直接跳过数据模型
                List<String> fieldList = new ArrayList<>();
                //显示字段
                fieldList.add(cascaderItem.getProps().getLabel());
                //传递字段
                fieldList.add(cascaderItem.getProps().getValue());
                //父级字段
                fieldList.add(cascaderItem.getProps().getSecTitle());
                Object o = null;
                if (showPath) {
                    List<Map<String, Object>> dictList = dynamicDataService.queryList(cascaderItem.getFormId(), cascaderItem.getProps().getLabel(), cascaderItem.getProps().getSecTitle());
                    Map<String, Object> map = dictList
                            .stream()
                            .filter(e -> ObjectNull.isNotNull(e.get(cascaderItem.getProps().getLabel())))
                            .collect(Collectors.toMap(e -> e.get("id").toString(), e -> e.get(cascaderItem.getProps().getLabel())));
                    data = dataFieldHandler.handlePathId(data, isMulti, showPath, dictList, e -> e.get("id").toString(), e -> e.getOrDefault(cascaderItem.getProps().getSecTitle(), "-1").toString());
                    return dataFieldHandler.joinFormItems(map, data, isMulti, showPath);
                } else {
                    try {
                        o = dynamicDataService.getById(cascaderItem.getFormId(), data.toString()).getJsonData().get(cascaderItem.getProps().getLabel());
                    } catch (Exception e) {
                        log.error("找不到数据");
                    }
                }
                return o;
            //根据模型获取字段
            case system:
                List<TreeDictDto> dictList = this.getTreeDictCache(dictName);
                if (ObjectNull.isNotNull(dictList)) {
                    Map<String, Object> map = dictList.stream().collect(Collectors.toMap(TreeDictDto::getId, TreeDictDto::getName, (s1, s2) -> s1));
                    data = dataFieldHandler.handlePathId(data, isMulti, showPath, dictList, TreeDictDto::getId, TreeDictDto::getParentId);
                    return dataFieldHandler.joinFormItems(map, data, isMulti, showPath);
                } else {
                    return data;
                }
            case rule:
                //fixme：后面可以考虑把特定的逻辑加入缓存
                //逻辑引擎获取级联数据
                String optionHttp = cascaderItem.getOptionHttp();
                if (ObjectNull.isNotNull(optionHttp)) {
                    if (isMulti && JSON.isValidArray(JSON.toJSONString(data))) {
                        return JSON.parseArray(JSON.toJSONString(data)).stream()
                                .map(e -> getRuleValue(cascaderItem, optionHttp, lineData, e, showPath).toString())
                                .filter(ObjectNull::isNotNull)
                                .collect(Collectors.joining(","));
                    } else {
                        return getRuleValue(cascaderItem, optionHttp, lineData, data, showPath);
                    }
                }
            default:
                return data;
        }
    }

    public Object getRuleValue(CascaderItemHtml cascaderItem, String optionHttp, Map<String, Object> lineData, Object data, boolean showPath) {
        RunLogService logService = SpringContextUtil.getApplicationContext().getBean(RunLogService.class);
        RuleStartUtils ruleStartUtils = SpringContextUtil.getApplicationContext().getBean(RuleStartUtils.class);
        RuleDesignService ruleDesignService = SpringContextUtil.getApplicationContext().getBean(RuleDesignService.class);
        //逻辑如果需要传递参数，直接查询数据库修改Data即可
        RuleDesignPo po = ruleDesignService.getEnableDesign(optionHttp);
        if (ObjectNull.isNotNull(po)) {
            RunLogPo logPo = logService.create(po.getJvsAppId(), po.getSecret(), RunType.REAL, new HashMap<>(), false);
            RuleExecuteDto dto = new RuleExecuteDto().setReqVariableMap(lineData).setVariableMap(lineData);
            RuleExecDto ruleExecDto = new RuleExecDto()
                    .setExecuteDto(dto)
                    .setType(RunType.REAL)
                    .setSecret(po.getSecret())
                    .setGraph(JSONObject.parseObject(po.getDesignDrawingJson(), HtmlGraph.class));
            ruleStartUtils.start(po, logPo, ruleExecDto);
            if (ObjectNull.isNotNull(ruleExecDto.getExecuteDto().getEndResult())) {
                Object value = ruleExecDto.getExecuteDto().getEndResult().getValue();
                if (value instanceof List) {
                    List<Map> list = JSONArray.parseArray(JSONObject.toJSONString(value)).stream().map(e -> BeanCopyUtil.copy(e,Map.class)).collect(Collectors.toList());
                    List<FormValueHtml> copys = getFormValueHtml(list, cascaderItem.getProps().getLabel(), cascaderItem.getProps().getValue());
                    if (data instanceof List) {
                        return ((List<?>) data).stream().map(e -> getLabel(e.toString(), copys, showPath))
                                .filter(ObjectNull::isNotNull)
                                .collect(Collectors.joining(","));
                    } else {
                        return getLabel(data.toString(), copys, showPath);
                    }
                }
            }
        }
        if (ObjectNull.isNull(data)) {
            return "";
        }
        return "字典未匹配" + JSONObject.toJSONString(data);
    }

    /**
     * 递归根据属性组装属性对象值
     *
     * @param list  树形数据
     * @param label 组件属性指定的 label  key
     * @param value 组件属性指定的 value  key
     * @return
     */
    private List<FormValueHtml> getFormValueHtml(List<Map> list, String label, String value) {
        List<FormValueHtml> html = new ArrayList<>();
        for (Map jsonObject : list) {
            List children = (List) jsonObject.get("children");
            FormValueHtml formValueHtml = new FormValueHtml().setLabel(jsonObject.get(label).toString()).setValue(jsonObject.get(value).toString());
            if (ObjectNull.isNotNull(children)) {
                //装下一层
                formValueHtml.setChildren(getFormValueHtml(children, label, value));
            }
            html.add(formValueHtml);
        }
        return html;
    }


    @Override
    public Object getConversionKey(CascaderItemHtml cascaderItem, Object data, Map<String, Object> oldData) {
        //递归循环查询，是否存在这样的数据
        List<String> collect = Arrays.stream(data.toString().split("/")).map(String::trim).filter(ObjectNull::isNotNull).collect(Collectors.toList());
        switch (cascaderItem.getDatatype()) {
            case option:

            case dataModel:
                //此处直接跳过数据模型
                List<String> fieldList = new ArrayList<>();
                //显示字段
                fieldList.add(cascaderItem.getProps().getLabel());
                //传递字段
                fieldList.add(cascaderItem.getProps().getValue());
                //父级字段
                fieldList.add(cascaderItem.getProps().getSecTitle());
                //遍历数据,找出名称与这个字段相同的数据
                Object next = getNextCascaderId(collect, 0, fieldList, cascaderItem);
                return next;
            //根据模型获取字段
            case system:
                Map<String, Object> byUniqueName = jvsTreeService.getByUniqueName(cascaderItem.getDictName(), 0);
                return getNextSystem(collect, 0, (List<Tree>) byUniqueName.get("children"));
            case rule:
                //逻辑引擎获取级联数据
                String optionHttp = cascaderItem.getOptionHttp();
                if (ObjectNull.isNotNull(optionHttp)) {
                    RunLogService logService = SpringContextUtil.getApplicationContext().getBean(RunLogService.class);
                    RuleStartUtils ruleStartUtils = SpringContextUtil.getApplicationContext().getBean(RuleStartUtils.class);
                    RuleDesignService ruleDesignService = SpringContextUtil.getApplicationContext().getBean(RuleDesignService.class);
                    //逻辑如果需要传递参数，直接查询数据库修改Data即可
                    RuleDesignPo po = ruleDesignService.getEnableDesign(optionHttp);
                    if (ObjectNull.isNotNull(po)) {
                        RunLogPo logPo = logService.create(po.getJvsAppId(), po.getSecret(), RunType.REAL, new HashMap<>(), false);
                        RuleExecuteDto dto = new RuleExecuteDto().setReqVariableMap(oldData).setVariableMap(oldData);
                        RuleExecDto ruleExecDto = new RuleExecDto()
                                .setExecuteDto(dto)
                                .setType(RunType.REAL)
                                .setSecret(po.getSecret())
                                .setGraph(JSONObject.parseObject(po.getDesignDrawingJson(), HtmlGraph.class));
                        ruleStartUtils.start(po, logPo, ruleExecDto);
                        if (ObjectNull.isNotNull(ruleExecDto.getExecuteDto().getEndResult())) {
                            Object value = ruleExecDto.getExecuteDto().getEndResult().getValue();
                            if (value instanceof List) {
                                List<JSONObject> valueArray = BeanCopyUtil.copys((List) value, JSONObject.class);
                                List<FormValueHtml> copys = valueArray.stream()
                                        .map(e -> new FormValueHtml().setLabel(e.getString(cascaderItem.getProps().getLabel())).setValue(e.getString(cascaderItem.getProps().getValue())))
                                        .collect(Collectors.toList());
                                return getValue(data.toString(), copys);
                            }
                        } else {
                            if (ObjectNull.isNull(data)) {
                                return "";
                            }
                            return "字典未匹配" + JSONObject.toJSONString(data);
                        }
                    }
                }

            default:
                return null;
        }
    }

    private Object getNextSystem(List<String> collect, int i, List<Tree> list) {
        String s = collect.get(i);
        if (ObjectNull.isNotNull(list)) {
            Optional<Tree> first = list.stream().filter(e -> e.getName().equals(s)).findFirst();
            if (first.isPresent()) {
                if (i + 1 == collect.size()) {
                    return first.get().getId();
                }
                return getNextSystem(collect, ++i, first.get().getChildren());
            }
        }
        return null;
    }

    private Object getNextCascaderId(List<String> collect, int i, List<String> fieldList, CascaderItemHtml cascaderItem) {
        DynamicDataService dynamicDataService = SpringContextUtil.getBean(DynamicDataService.class);
        QueryListDto dto = new QueryListDto();
        dto.setFieldList(fieldList);
        dto.setConditions(Arrays.asList(new QueryConditionDto().setEnabledQueryTypes(DataQueryType.eq).setValue(collect.get(i)).setFieldKey(cascaderItem.getProps().getLabel())));
        List<Map<String, Object>> maps = dynamicDataService.postQueryList(cascaderItem.getFormId(), dto);
        if (ObjectNull.isNotNull(maps)) {
            if (i + 1 == collect.size()) {
                return maps.get(0).get("id");
            } else {
                return getNextCascaderId(collect, ++i, fieldList, cascaderItem);
            }
        }
        return null;
    }

    /**
     * 获取树形字典项的集合
     *
     * @param dictUniqueName 字典标识
     * @return 字典项集合
     */
    public List<TreeDictDto> getTreeDict(String dictUniqueName) {
        List<TreeDictDto> result = new ArrayList<>();
        Map<String, String> idMap = new HashMap<>(64);
        Deque<Map<String, Object>> keyQueue = new ArrayDeque<>(64);
        // 查询树形字典数据
        Map<String, Object> treeDict = jvsTreeService.getByUniqueName(dictUniqueName, 0);
        keyQueue.add(treeDict);
        while (ObjectNull.isNotNull(keyQueue)) {
            Map<String, Object> poll = keyQueue.poll();
            JvsTree extend = BeanCopyUtil.copy(poll, JvsTree.class);
            String id = extend.getUniqueName();
            String name = extend.getName();
            String parentId = extend.getParentId();
            String uniqueName = extend.getUniqueName();
            TreeDictDto dict = new TreeDictDto().setId(id).setName(name).setParentId(parentId);
            result.add(dict);
            // 记录节点id与uniqueName的对应关系
            idMap.put(id, uniqueName);
            // 添加子节点
            List<Map<String, Object>> children = (List<Map<String, Object>>) poll.get("children");
            if (ObjectUtils.isNotEmpty(children)) {
                keyQueue.addAll(children);
            }
        }
        // 因为前端的传递值使用的是uniqueName, 这里将id都换成uniqueName
        for (TreeDictDto dict : result) {
            String newId = idMap.get(dict.getId());
            String newParentId = idMap.get(dict.getParentId());
            dict.setId(newId);
            dict.setParentId(newParentId);
        }
        return result;
    }

    @Override
    public Boolean whetherCoverValue() {
        return Boolean.FALSE;
    }

    public List<TreeDictDto> getTreeDictCache(String dictUniqueName) {
        List<TreeDictDto> treeDictDtoList = new ArrayList<>();
        //广东省四级区划行政字典
        if (DictConstant.DICT_REGION_UNIQUE_NAME.equals(dictUniqueName)) {
            treeDictDtoList = treeDictCache.get(dictUniqueName);
            if (null == treeDictDtoList) {
                treeDictDtoList = getTreeDict(dictUniqueName);
                treeDictCache.put(dictUniqueName, treeDictDtoList);
            } else {
            }
        } else {
            treeDictDtoList = getTreeDict(dictUniqueName);
        }
        return treeDictDtoList;
    }
}