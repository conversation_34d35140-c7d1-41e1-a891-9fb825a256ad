package cn.bctools.design.data.fields.impl.basic;

import cn.bctools.common.utils.JvsJsonPath;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.data.fields.DesignField;
import cn.bctools.design.data.fields.IDataFieldHandler;
import cn.bctools.design.data.fields.dto.FieldBasicsHtml;
import cn.bctools.design.data.fields.dto.FieldPublicHtml;
import cn.bctools.design.data.fields.dto.QueryConditionDto;
import cn.bctools.design.data.fields.dto.form.MultipleHtml;
import cn.bctools.design.data.fields.dto.form.item.FilterHtml;
import cn.bctools.design.data.fields.dto.form.item.TypeHtml;
import cn.bctools.design.data.fields.enums.DataFieldType;
import cn.bctools.design.data.fields.enums.DataQueryType;
import cn.bctools.design.data.fields.impl.RequestCachedMultipleTypeHandler;
import cn.bctools.design.data.fields.impl.ISelectorDataHandler;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONPath;
import com.jayway.jsonpath.JsonPath;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 表单字段: 下拉框
 *
 * @Author: GuoZi
 */
@Slf4j
@Data
@Component
@AllArgsConstructor
@DesignField(value = "下拉框", type = DataFieldType.select)
public class SelectFieldHandler extends RequestCachedMultipleTypeHandler implements IDataFieldHandler<MultipleHtml>, ISelectorDataHandler {

    @Override
    public void filterOrDataLinkage(String appId, Map<String, ? extends FieldBasicsHtml> fieldMap, String key, MultipleHtml e, Map<String, Object> map, Integer index, String... parentPath) {
        if (ObjectNull.isNotNull(e.getDataFilterList()) || ObjectNull.isNotNull(e.getDataFilterGroupList())) {
            List<List<FilterHtml>> dataFilterGroupList = CollectionUtils.isNotEmpty(e.getDataFilterGroupList()) ? e.getDataFilterGroupList() : Collections.singletonList(e.getDataFilterList());
            //如果查询条件与触发级件相关,则执行,如果不相关则退出关联,处理下拉关联问题
            if (dataFilterGroupList.stream().flatMap(Collection::stream).filter(a -> a.getFieldKey().equals(key)).findFirst().isPresent()) {
                //需要查询的字段，是关联字段和显示字段
                List<String> collect = new ArrayList<>(1);
                collect.add(e.getProps().getValue());
                collect.add(e.getProps().getLabel());
                //添加下级字段，目前只有表格才有这个操作
                addFields(collect, e);
                //获取查询条件
                List<List<QueryConditionDto>> queryConditionDtos = dataFilterGroupList.stream().map(filterGroup ->
                        filterGroup.stream()
                                .peek(s -> {
                                    //添加查询字段
                                    collect.add(s.getFieldKey());
                                }).map(s -> {
                                    try {
                                        TypeHtml type = s.getType();
                                        if (ObjectNull.isNull(type)) {
                                            type = TypeHtml.value;
                                        }
                                        QueryConditionDto queryConditionDto = new QueryConditionDto().setEnabledQueryTypes(s.getEnabledQueryTypes()).setFieldKey(s.getFieldKey());
                                        switch (type) {
                                            case prop:
                                                //获取字段路径
                                                List<String> strings = new ArrayList<String>(Arrays.asList(parentPath));
                                                strings.add(e.getProp());
                                                String paths = strings.stream().filter(ObjectNull::isNotNull).collect(Collectors.joining("."));
                                                Object read = JvsJsonPath.read(JSONUtil.toJsonStr(map), paths);
                                                return queryConditionDto.setValue(read);
                                            case value:
                                            default:
                                                return queryConditionDto.setValue(s.getValue());
                                        }
                                    } catch (Exception exception) {
                                        return null;
                                    }
                                }).filter(ObjectNull::isNotNull).collect(Collectors.toList())
                ).collect(Collectors.toList());
                //获取表格字段
                setValue(appId, e.getDataModelId(), e, map, collect, queryConditionDtos, parentPath);
            }
        }
        // 数据联动处理
        dataLinkage(appId, fieldMap, key, e, map, index, parentPath);
    }

    @Override
    public List<DataQueryType> getEnabledQueryTypes(MultipleHtml html) {
        return super.getEnabledQueryTypes(html);
    }

    @Override
    public Object getEchoValue(MultipleHtml fieldDto, Object data, boolean override, Map<String, Object> lineData, String... paths) {
        // 使用请求级缓存优化回显处理
        return echoValueWithRequestCache(
            fieldDto,
            data,
            lineData,
            DataFieldType.select,
            this::getConfigKey,
            cacheKey -> loadDataSource(fieldDto, cacheKey)
        );
    }

    /**
     * 原始的回显处理方法（作为回退方案）
     */
    private Object getEchoValueOriginal(MultipleHtml fieldDto, Object data, Map<String, Object> lineData) {
        return echoValue(fieldDto, data, lineData);
    }

    @Override
    public Object getConversionKey(MultipleHtml dto, Object o, Map<String, Object> oldData) {
        return conversionKey(dto, o, oldData);
    }

    @Override
    public Boolean whetherCoverValue() {
        return Boolean.FALSE;
    }
}

            // 复用ISelectorDataHandler中的规则引擎逻辑
            Map<String, Object> resultMap = new HashMap<>();

            // 调用父类的规则数据查询方法
            List<Map<String, Object>> ruleData = getRuleData(fieldDto, new HashMap<>());
            if (ObjectNull.isNotNull(ruleData)) {
                String valueField = fieldDto.getProps().getValue();
                String labelField = fieldDto.getProps().getLabel();

                for (Map<String, Object> item : ruleData) {
                    Object value = item.get(valueField);
                    Object label = item.get(labelField);
                    if (ObjectNull.isNotNull(value) && ObjectNull.isNotNull(label)) {
                        resultMap.put(value.toString(), label);
                    }
                }
            }

            log.debug("规则数据加载完成: optionHttp={}, size={}", optionHttp, resultMap.size());
            return resultMap;
        } catch (Exception e) {
            log.error("加载规则数据失败: optionHttp={}", fieldDto.getOptionHttp(), e);
            return new HashMap<>();
        }
    }
}