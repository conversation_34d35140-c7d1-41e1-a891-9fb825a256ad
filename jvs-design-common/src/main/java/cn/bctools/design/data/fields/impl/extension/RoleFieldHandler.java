package cn.bctools.design.data.fields.impl.extension;

import cn.bctools.auth.api.api.AuthRoleServiceApi;
import cn.bctools.auth.api.dto.SysRoleDto;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.design.constant.CacheConsts;
import cn.bctools.design.data.fields.DataFieldHandler;
import cn.bctools.design.data.fields.DesignField;
import cn.bctools.design.data.fields.IDataFieldHandler;
import cn.bctools.design.data.fields.dto.form.MultipleHtml;
import cn.bctools.design.data.fields.enums.DataFieldType;
import cn.bctools.design.data.fields.impl.IMultipleTypeHandler;
import cn.bctools.design.data.fields.impl.ISelectorDataHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 表单字段: 角色选择
 *
 * @Author: GuoZi
 */
@Slf4j
@Data
@Component
@DesignField(value = "角色选择", type = DataFieldType.role)
@AllArgsConstructor
public class RoleFieldHandler extends IMultipleTypeHandler implements IDataFieldHandler<MultipleHtml>, ISelectorDataHandler {

    AuthRoleServiceApi roleApi;

    @Override
    public Object getEchoValue(MultipleHtml fieldDto, Object data, boolean override, Map<String, Object> lineData, String... paths) {
        boolean isMulti = fieldDto.getMultiple();
        Map<String, Object> roleMap = roleApi.getAll().getData().stream().collect(Collectors.toMap(SysRoleDto::getId, SysRoleDto::getRoleName));
        DataFieldHandler dataFieldHandler = SpringContextUtil.getBean(DataFieldHandler.class);
        return dataFieldHandler.joinFormItems(roleMap, data, isMulti, false);
    }

    @Override
    public Object getConversionKey(MultipleHtml dto, Object o, Map<String, Object> oldData) {
        if (ObjectNull.isNull(o)) {
            return o;
        }
        Map<String, String> roleMap = roleApi.getAll().getData().stream().collect(Collectors.toMap(SysRoleDto::getRoleName, SysRoleDto::getId));
        return Arrays.stream(o.toString().split(REGEX))
                .map(String::trim)
                .map(e -> roleMap.get(e))
                .collect(Collectors.toList());
    }

    @Override
    public Boolean whetherCoverValue() {
        return Boolean.FALSE;
    }

}
