package cn.bctools.design.data.service.impl;

import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.auth.api.dto.PersonnelDto;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.*;
import cn.bctools.common.utils.function.Get;
import cn.bctools.common.utils.sensitive.SensitiveInfoUtils;
import cn.bctools.design.common.OrderFormat;
import cn.bctools.design.common.OrderResetRuleEnum;
import cn.bctools.design.constant.DynamicDataConstant;
import cn.bctools.design.crud.entity.FormPo;
import cn.bctools.design.crud.mapper.FormMapper;
import cn.bctools.design.crud.utils.DesignUtils;
import cn.bctools.design.data.component.DataModelHandler;
import cn.bctools.design.data.dto.ButtonRtnVo;
import cn.bctools.design.data.entity.*;
import cn.bctools.design.data.fields.DataFieldHandler;
import cn.bctools.design.data.fields.IDataFieldHandler;
import cn.bctools.design.data.fields.dto.*;
import cn.bctools.design.data.fields.dto.enums.AggregationTypeEnum;
import cn.bctools.design.data.fields.dto.form.FormDesignHtml;
import cn.bctools.design.data.fields.dto.form.FormValueHtml;
import cn.bctools.design.data.fields.dto.form.html.TableFormItemHtml;
import cn.bctools.design.data.fields.dto.form.item.*;
import cn.bctools.design.data.fields.dto.page.PageAggregationQuery;
import cn.bctools.design.data.fields.dto.page.PageAggregationSetting;
import cn.bctools.design.data.fields.enums.*;
import cn.bctools.design.data.fields.impl.container.TabFieldHandler;
import cn.bctools.design.data.fields.impl.container.TableFormFieldHandler;
import cn.bctools.design.data.service.*;
import cn.bctools.design.data.util.DataModelUtil;
import cn.bctools.design.data.util.RoleUtils;
import cn.bctools.design.expression.EnvConstant;
import cn.bctools.design.project.entity.enums.AppLogTypeEnum;
import cn.bctools.design.project.handler.DesignHandler;
import cn.bctools.design.project.service.JvsAppLogService;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.design.util.OrderUtils;
import cn.bctools.design.workflow.enums.FlowDataFieldEnum;
import cn.bctools.design.workflow.service.FlowTaskService;
import cn.bctools.function.config.OcrConfig;
import cn.bctools.function.entity.dto.ExecDto;
import cn.bctools.function.entity.dto.TableType;
import cn.bctools.function.handler.ExpressionAfterHandler;
import cn.bctools.function.handler.ExpressionHandler;
import cn.bctools.function.utils.ExpressionParam;
import cn.bctools.function.utils.ExpressionUtils;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.oss.dto.BaseFile;
import cn.bctools.oss.template.OssTemplate;
import cn.bctools.rule.utils.html.RuleExecuteDto;
import cn.bctools.web.utils.WebUtils;
import cn.bctools.word.utils.Pdf2ImageUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONPath;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static cn.bctools.design.util.DynamicDataUtils.*;
import static cn.bctools.function.entity.dto.TableType.*;

/**
 * 动态数据
 *
 * @Author: GuoZi
 */
@Slf4j
@Service
@AllArgsConstructor
public class DynamicDataServiceImpl implements DynamicDataService, ExpressionAfterHandler {

    DataIdService dataIdService;
    DataLogService dataLogService;
    DataEventService dataEventService;
    DataFieldService dataFieldService;
    FormMapper formMapper;
    DataModelService dataModelService;
    DataFieldHandler dataFieldHandler;
    TableFormFieldHandler tableFormFieldHandler;
    TabFieldHandler tabFieldHandler;
    AuthUserServiceApi authUserServiceApi;
    Map<String, IDataFieldHandler> iDataFieldHandler;
    JvsAppLogService jvsAppLogService;
    DesignHandler designHandler;
    DataModelHandler dataModelHandler;
    FlowTaskService flowTaskService;
    OssTemplate ossTemplate;
    OcrConfig ocrConfig;

    static final String DATA_CHANGE = "dataChange";
    static final String RETRIVAL = "retrival";
    static final String QUERY_BY = "queryBy";

    private static final String DATA_LOG_VERSION_KEY = "dataLogVersion";

    @Override
    public DynamicDataPo getById(String modelId, String dataId) {
        try {
            return this.parseBean(this.getMap(modelId, dataId));
        } catch (Exception e) {
            log.info("模型数据查询异常: {}", e.getMessage());
        }
        return null;
    }

    @Override
    public List<Map> getByIds(String modelId, List<String> ids) {
        Criteria criteria = DynamicDataUtils.initCriteria(null).and(Get.name(DynamicDataPo::getId)).in(ids);
        Query query = this.getPermitQuery(criteria);
        // 排除MongoDB的id字段
        query.fields().exclude(MONGO_ID);
        return dataModelHandler.find(query, Map.class, modelId);
    }

    @Override
    public List<Map> getByIds(String modelId, List<String> ids, Set<String> field) {
        Criteria criteria = DynamicDataUtils.initCriteria(null).and(Get.name(DynamicDataPo::getId)).in(ids);
        Query query = this.getPermitQuery(criteria);
        // 排除MongoDB的id字段
        field.add("dataId");
        field.add("id");
        query.fields().exclude(MONGO_ID).include(field.toArray(new String[field.size()]));
        return dataModelHandler.find(query, Map.class, modelId);
    }

    /**
     * 避免网络请求嵌套，所有事件和数据存储分为两个处理
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public RuleExecuteDto save(String appId, String modelId, Map<String, Object> data) {
        RuleExecuteDto ruleExecuteDto = saveData(appId, modelId, data);
        data.remove(DATA_LOG_VERSION_KEY);
        return ruleExecuteDto;
    }

    /**
     * 数据
     * 保存
     *
     * @param modelId 模型id
     * @param data    数据
     * @param appId   应用id
     * @return
     */
    private RuleExecuteDto saveData(String appId, String modelId, Map<String, Object> data) {
        DataModelUtil.setCurrentSaveData();
        // true-数据已存在，false-数据不存在
        boolean exists = ObjectNull.isNotNull(data.get("dataId"));
        // 因为触发新增事件需要数据id, 所以这里需要手动生成一下
        String dataId = exists ? String.valueOf(data.get("dataId")) : IdGenerator.getIdStr();
        data.put("dataId", dataId);
        //逻辑的前置操作将数据为空的value和值都清空搞
        data.keySet().removeIf(e -> ObjectNull.isNull(data.get(e)));
        RuleExecuteDto callback = Optional.ofNullable(dataEventService.callback(modelId, dataId, DataEventType.DATA_NEW, data, true)).orElseGet(RuleExecuteDto::new);
        //如果返回信息被逻辑引擎定义,则直接返回
        if (ObjectNull.isNotNull(callback.getSyncMessageTips())) {
            return callback;
        }

        DataEventType dataEventType = DataEventType.DATA_NEW;
        Map<String, Object> oldData = null;
        // 数据存在则修改，否则新增
        if (exists) {
            dataEventType = DataEventType.DATA_UPDATE;
            DynamicDataPo dataPo = this.get(modelId, dataId);
            oldData = dataPo.getJsonData();
            updateTransactional(appId, modelId, dataId, data, dataPo);
        } else {
            saveTransactional(appId, modelId, data, dataId);
        }
        data.remove(MONGO_ID);

        callback = Optional.ofNullable(dataEventService.callback(modelId, dataId, DataEventType.DATA_NEW, data, false)).orElseGet(RuleExecuteDto::new);

        // 保存数据变更日志
        String version = saveDataLog(dataEventType, appId, modelId, dataId, data, oldData);
        data.put(DATA_LOG_VERSION_KEY, version);

        if (ObjectNull.isNotNull(callback.getSyncMessageTips())) {
            return callback;
        }

        return callback;
    }

    /**
     * 保存数据版本
     *
     * @param dataEventType 版本类型
     * @param appId         应用id
     * @param modelId       数据模型id
     * @param dataId        数据id
     * @param data          待保存的数据
     * @param oldData       已保存的数据
     * @return 数据版本号
     */
    private String saveDataLog(DataEventType dataEventType, String appId, String modelId, String dataId, Map<String, Object> data, Map<String, Object> oldData) {
        if (DataEventType.DATA_NEW.equals(dataEventType)) {
            ArrayList<Object> value = new ArrayList<>();
            value.add(new Dict().set("timestamp", DateUtil.now()).set("content", UserCurrentUtils.getRealName() + "添加了数据"));
            return dataLogService.saveLog(modelId, dataId, data, value, DataEventType.DATA_NEW);
        }
        if (DataEventType.DATA_UPDATE.equals(dataEventType)) {
            MapDifference<String, Object> difference = Maps.difference(oldData, data);
            List dataChange = null;
            if (!difference.areEqual()) {
                dataChange = saveDataChange(appId, modelId, oldData, difference, data);
            }
            return dataLogService.saveLog(modelId, dataId, data, dataChange, DataEventType.DATA_UPDATE);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveReturnVersion(String appId, String modelId, Map<String, Object> data) {
        saveData(appId, modelId, data);
        String dataVersion = (String) data.get(DATA_LOG_VERSION_KEY);
        data.remove(DATA_LOG_VERSION_KEY);
        return dataVersion;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveTransactional(String appId, String dataModelId, Map<String, Object> data) {
        String dataId = IdGenerator.getIdStr();
        data.put("dataId", dataId);
        saveTransactional(appId, dataModelId, data, dataId);
        data.remove(MONGO_ID);
        return dataId;
    }

    @Override
    public String saveTransactionalRule(String appId, String modelId, Map<String, Object> data) {
        String dataId = IdGenerator.getIdStr();
        data.put("dataId", dataId);
        //生成自动顺序键
        this.handleIncreasedIdFields(appId, modelId, Collections.singletonList(data));
        //保存数据
        this.onlySave(modelId, dataId, data);
        data.remove(MONGO_ID);
        return dataId;
    }

    @Override
    public List<String> saveBatchTransactionalRule(String appId, String dataModelId, List<Map<String, Object>> dataList) {
        if (StringUtils.isBlank(dataModelId) || ObjectUtils.isEmpty(dataList)) {
            return null;
        }
        List<String> idList = new ArrayList<>();
        this.handleIncreasedIdFields(appId, dataModelId, dataList);
        for (Map<String, Object> data : dataList) {
            String dataId = IdGenerator.getIdStr();
            if (!data.containsKey("dataId")) {
                data.put("dataId", dataId);
            }
            idList.add((String) data.get("dataId"));
        }
        this.onlySave(dataModelId, dataList);

        return idList;
    }

    private void saveTransactional(String appId, String modelId, Map<String, Object> data, String dataId) {
        DynamicDataUtils.clearEcho(data);
        this.handleIncreasedIdFields(appId, modelId, Collections.singletonList(data));
        //记录日志
        jvsAppLogService.savelog(modelId, AppLogTypeEnum.add, null, data);

        //检查同模型下是否存在关联字段是否也存在这几个key中
        Map<String, DataFieldPo> list = dataFieldService.list(Wrappers.query(new DataFieldPo().setModelId(modelId)).lambda().orderByDesc(DataFieldPo::getCreateTime).in(DataFieldPo::getFieldType, DataFieldType.CONTAINER))
                .stream()
                .filter(a -> {
                    if (DataFieldType.tab.equals(a.getFieldType())) {
                        return true;
                    }
                    return data.keySet().contains(a.getFieldKey());
                })
                .distinct().collect(Collectors.toMap(DataFieldPo::getFieldKey, Function.identity(), (t1, t2) -> t1));
        //保存或更新下级表格里的数据
        for (DataFieldPo dataFieldPo : list.values()) {
            FieldPublicHtml publicHtml = iDataFieldHandler.get(dataFieldPo.getFieldType().getDesc()).toHtml(dataFieldPo.getDesignJson());
            saveOrUpdateSubsetTable(appId, publicHtml, data, modelId, "");
        }
        this.onlySave(modelId, dataId, data);
    }

    /**
     * 修改或新增下级控件为表格的数据，获取数据修改数据库或新增数据库
     *
     * @param e          当前这个组件
     * @param data       数据集
     * @param parentPath
     */
    private void saveOrUpdateSubsetTable(String appId, FieldPublicHtml e, Map<String, Object> data, String modelId, String... parentPath) {
        List<String> strings = new ArrayList<String>(Arrays.asList(parentPath));
        strings.add(e.getProp());
        switch (e.getType()) {
            case tableForm:
                //递归获取下级的控件，判断是否是表格控件，如果是表格控件，并模型不为空，则把控件里面的数据，把控件数据获取出来进行保存或更新
                TableFormItemHtml tableFormItemHtml = tableFormFieldHandler.toHtml(e.getDesignJson());
                String tablePathKey = strings.stream().filter(ObjectNull::isNotNull).collect(Collectors.joining("."));
                //如果是表格组件,直接更新数据，根据路径获取数据
                Object o = JvsJsonPath.read((data), tablePathKey);
                //如果读取数据为空,需要将关联数据清空,当关联了模型的表格只最后一行数据也被删除后,数据库中的数据也需要被设置为空数组,否则下次加载会出现BUG
                if (ObjectNull.isNull(o)) {
                    JSONPath.set(data, tablePathKey, DynamicDataConstant.getEmpty(tableFormItemHtml.getType().getAClass()));
                }
                JSONPath.remove(data, tablePathKey + "_line");
                //判断有没有删除的字段
                Object del = JvsJsonPath.read(data, tablePathKey + "_del");
                //关联模型不为空的情况
                if (ObjectNull.isNotNull(tableFormItemHtml.getDataModelId())) {
                    if (o instanceof List) {
                        //类型转换为listMap
                        List<Map<String, Object>> box = ((List) o);

                        List<Map<String, Object>> rawBox = new ArrayList<>();
                        //如果已删除没有了,但还有未删除的
                        if (ObjectNull.isNotNull(box)) {
                            dataFieldService.getIncreasedIdFields(appId, modelId).forEach(s -> {
                                box.forEach(s1 -> s1.put(s.getFieldKey(), data.get(s.getFieldKey())));
                            });
                            //获取主模型的流水号 获取当前模型的流水号进行填充
                            this.handleIncreasedIdFields(appId, tableFormItemHtml.getDataModelId(), box);
                            rawBox = new ArrayList<>(box);
                            if (ObjectNull.isNotNull(e.getDataFilterGroupList())) {
                                handleSubTableCondition(tableFormItemHtml.getDataModelId(), e.getDataFilterGroupList(), data, box);
                            }
                        }
                        List<String> ids = saveOrUpdateTable(appId, tableFormItemHtml, box, del);
                        // 获取path 新数据id集合保存到主表
                        JSONPath.set(data, tablePathKey, rawBox);
                    }

                }
                break;
            case tab:
                TabItemHtml tabItemHtml = tabFieldHandler.toHtml(e.getDesignJson());
                //判断是否添加了数据脱离，数据添加了，操作数据就将其数据格式进行分开处理
                if (tabItemHtml.getDetachData()) {
                    //只处理下级的表格，其它的字段不需要处理
                    for (FormValueHtml dicDatum : tabItemHtml.getDicData()) {
                        //过滤出表格的操作
                        List<FieldBasicsHtml> fieldBasicsHtmls = tabItemHtml.getColumn().get(dicDatum.getName());
                        //某一个选项里面没有配置
                        if (ObjectNull.isNull(fieldBasicsHtmls)) {
                            continue;
                        }
                        List<FieldBasicsHtml> tableFields = fieldBasicsHtmls
                                .stream()
                                .filter(s -> s.getType().equals(DataFieldType.tableForm))
                                .collect(Collectors.toList());
                        for (FieldBasicsHtml tableField : tableFields) {
                            //如果属性不为空，获取的数据方式不一样
                            if (ObjectNull.isNotNull(dicDatum.getProp())) {
                                //获取表格的数据
                                LinkedHashMap tableData = (LinkedHashMap) data.get(dicDatum.getProp());
                                saveOrUpdateSubsetTable(appId, tableField, tableData, modelId);
                            } else {
                                //获取表格的数据
                                saveOrUpdateSubsetTable(appId, tableField, data, modelId);
                            }
                        }

                    }
                } else {
                    if (tabItemHtml.getNext()) {
                        //判断是否添加了脱离数据
                        //处理的数据结构就不一致,除表格外，其它都不处理
                        Map<String, List<FieldBasicsHtml>> column = tabItemHtml.getColumn();
                        for (String tabKey : column.keySet()) {
                            List<FieldBasicsHtml> fieldBasicsHtmls = column.get(tabKey);
                            if (ObjectNull.isNotNull(fieldBasicsHtmls)) {
                                List<FieldBasicsHtml> fieldPublicHtmls = fieldBasicsHtmls.stream().filter(s -> DataFieldType.CONTAINER.contains(s.getType())).collect(Collectors.toList());
                                if (ObjectNull.isNotNull(fieldPublicHtmls)) {
                                    for (FieldBasicsHtml fieldPublicHtml : fieldPublicHtmls) {
                                        List<String> collect = new ArrayList<>();
                                        if (tabItemHtml.getDetachData()) {
                                            //如果开启了数据脱离，path的路径需要调整
                                        } else {
                                            List<String> tableKeys = new ArrayList<String>(strings);
                                            tableKeys.add(tabKey);
                                            collect = tableKeys.stream().filter(ObjectNull::isNotNull).collect(Collectors.toList());
                                        }
                                        //将数据再传递至下级控件继续递归
                                        saveOrUpdateSubsetTable(appId, fieldPublicHtml, data, modelId, collect.toArray(new String[collect.size()]));
                                    }
                                }
                            }
                        }
                    }
                }
                //寻找下级
                break;
            case step:
                //寻找下级
                break;
            default:
        }
    }

    /**
     * 直接保存或更新表格组件
     *
     * @param dataFieldPo 当前控件字段
     * @param dels        删除的字段
     * @param box         当前控件的前端传递的数据
     * @return 返回保存后的所有Id的值
     */
    private List<String> saveOrUpdateTable(String appId, TableFormItemHtml dataFieldPo, List<Map<String, Object>> box, Object dels) {
        //一对多表格
        if (ObjectNull.isNotNull(dels)) {
            List<JSONObject> linkedHashMapList = JSONArray.parseArray(JSON.toJSONString(dels), JSONObject.class);
            linkedHashMapList.forEach(e -> {
                String id = Optional.ofNullable(e.get(Get.name(DataFieldPo::getId))).map(String::valueOf).orElse(null);
                //删除字段
                if (ObjectNull.isNotNull(id)) {
                    try {
                        this.onlyRemove(dataFieldPo.getDataModelId(), id);
                    } catch (BusinessException businessException) {
                        //屏蔽删除错误，可能数据已经被删除了
                    }
                }
            });

        }
        // 保存数据
        List<String> dataIds = new ArrayList<>();
        box.stream().forEach(s -> {
            //根据条件查询出历史数据，删除之前的数据
            String id = Optional.ofNullable(s.get(Get.name(DataFieldPo::getId))).map(String::valueOf).orElse(null);
            // 新增
            if (StringUtils.isEmpty(id)) {
                id = IdGenerator.getIdStr();
                s.put("dataId", id);
                //将ID和dataId保持一致
                s.put("id", id);
                //流水号添加
                handleIncreasedIdFields(appId, dataFieldPo.getDataModelId(), Collections.singletonList(s));
                onlySave(dataFieldPo.getDataModelId(), id, s);
            } else {
                // 修改
                DynamicDataUtils.freePermit();
                DynamicDataPo childDataPo = this.get(dataFieldPo.getDataModelId(), id);
                if (ObjectNull.isNotNull(childDataPo)) {
                    s.remove("_id");
                    onlyUpdate(dataFieldPo.getDataModelId(), id, childDataPo.getJsonData(), s);
                    SystemThreadLocal.set(DynamicDataUtils.KEY_AUTH_FREE, false);
                }
            }
            dataIds.add(id);
        });
        //删除历史的关联性

        return dataIds;

    }

    @Override
    public boolean saveBatch(List<DynamicDataPo> dataList, String dataModelId) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }
        List<Map<String, Object>> mapList = dataList.stream().map(this::paresMap).collect(Collectors.toList());
        return this.onlySave(dataModelId, mapList);
    }

    @Override
    public RuleExecuteDto saveBatch(String appId, String modelId, List<Map<String, Object>> dataList) {
        if (StringUtils.isBlank(modelId) || ObjectUtils.isEmpty(dataList)) {
            return null;
        }
        this.handleIncreasedIdFields(appId, modelId, dataList);
        RuleExecuteDto callback = null;
        for (Map<String, Object> data : dataList) {
            String dataId = IdGenerator.getIdStr();
            if (!data.containsKey("dataId")) {
                data.put("dataId", dataId);
            }
            callback = dataEventService.callback(modelId, dataId, DataEventType.DATA_IMPORT, data, true);
            if (ObjectNull.isNotNull(callback) && ObjectNull.isNotNull(callback.getStats()) && !callback.getStats()) {
                return callback;
            }
        }

        this.onlySave(modelId, dataList);
        // 导入后置处理
        for (Map<String, Object> data : dataList) {
            String dataId = String.valueOf(data.get("dataId"));
            data.remove(MONGO_ID);
            callback = Optional.ofNullable(dataEventService.callback(modelId, dataId, DataEventType.DATA_IMPORT, data, false)).orElseGet(RuleExecuteDto::new);
            if (ObjectNull.isNotNull(callback.getStats()) && !callback.getStats()) {
                return callback;
            }
        }
        return callback;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RuleExecuteDto update(String appId, String modelId, String dataId, Map<String, Object> data) {
        RuleExecuteDto ruleExecuteDto = updateData(appId, modelId, dataId, data);
        data.remove(DATA_LOG_VERSION_KEY);
        return ruleExecuteDto;
    }

    /**
     * 批量修改数据
     */
    @Override
    public long updateMulti(String modelId, Map<String, Object> filterData, Map<String, Object> setData) {
        List<QueryConditionDto> collect = filterData.keySet().stream().map(e -> new QueryConditionDto().setValue(filterData.get(e)).setFieldKey(e).setEnabledQueryTypes(DataQueryType.eq)).collect(Collectors.toList());
        Criteria criteria = DynamicDataUtils.buildDynamicCriteria(collect);
        // 构建更新条件
        Query query = this.getPermitQuery(criteria);
        // 设置更新操作
        Update update = new Update();
        //修改的值
        for (Map.Entry<String, Object> entry : setData.entrySet()) {
            if (ObjectNull.isNotNull(entry.getValue())) {
                update.set(entry.getKey(), entry.getValue());
            }
        }
        return dataModelHandler.updateMulti(query, update, modelId).getMatchedCount();
    }


    /**
     * 批量修改数据
     */
    @Override
    public long updateMulti(String modelId, List<QueryConditionDto> queryConditionDtos, Map<String, Object> setData) {
        Criteria criteria = DynamicDataUtils.buildDynamicCriteria(queryConditionDtos);
        // 构建更新条件
        Query query = this.getPermitQuery(criteria);
        // 设置更新操作
        Update update = new Update();
        //修改的值
        if (ObjectNull.isNull(setData.keySet())) {
            return 0;
        }
        for (Map.Entry<String, Object> entry : setData.entrySet()) {
            //可以修改值为空
            update.set(entry.getKey(), entry.getValue());
        }
        return dataModelHandler.updateMulti(query, update, modelId).getMatchedCount();
    }

    @Override
    public void updateBatchById(String modelId, List<Map<String, Object>> setData) {
        if (ObjectNull.isNull(setData)) {
            return;
        }
        List<Pair<Query, Update>> updateList = new ArrayList<>(setData.size());
        setData.forEach(data -> {
            String dataId = String.valueOf(data.get("dataId"));
            this.updateFill(modelId, dataId, data);
            Criteria criteria = DynamicDataUtils.initCriteria(null).and(Get.name(DynamicDataPo::getId)).is(dataId);
            Query query = new Query(criteria);
            // 设置更新操作
            Update update = new Update();
            data.forEach((key, value) -> {
                if (value != null) {
                    update.set(key, value);
                }
                if (ObjectNull.isNotNull(value) || "".equals(value)) {
                    if (value.equals(DynamicDataConstant.DATA_EMPTY)) {
                        value = null;
                        update.set(key, null);
                    } else {
                        update.set(key, value);
                    }
                }
            });
            Pair<Query, Update> updatePair = Pair.of(query, update);
            updateList.add(updatePair);
        });
        dataModelHandler.updateBatch(updateList, modelId);
    }

    /**
     * 批量删除数据
     */
    @Override
    public void removeMulti(String dataModelId, Map<String, Object> filterData) {
        List<QueryConditionDto> collect = filterData.keySet().stream().map(e -> new QueryConditionDto().setValue(filterData.get(e)).setFieldKey(e).setEnabledQueryTypes(DataQueryType.eq)).collect(Collectors.toList());
        Criteria criteria = DynamicDataUtils.buildDynamicCriteria(collect);
        // 构建更新条件
        Query query = this.getPermitQuery(criteria);
        dataModelHandler.remove(query, dataModelId);
    }


    /**
     * 批量删除数据
     *
     * @return
     */
    @Override
    public List<Object> removeMulti(String dataModelId, List<QueryConditionDto> queryConditionDtoList) {
        Criteria criteria = DynamicDataUtils.buildDynamicCriteria(queryConditionDtoList);
        // 构建更新条件
        Query query = this.getPermitQuery(criteria);
        List<Object> objects = dataModelHandler.findAllAndRemove(query, dataModelId);
        if (ObjectNull.isNotNull(objects)) {
            // 删除流程数据
            removeTask(Convert.toList(Map.class, objects));
        }
        return objects;
    }


    /**
     * 修改数据(会发送回调)
     * <p>
     * 1. 数据操作: 对原数据的Map做putAll操作, 而非数据覆盖
     *
     * @param modelId 模型id
     * @param dataId  数据id
     * @param data    数据
     * @param appId   应用id
     * @return 数据版本号
     */
    private RuleExecuteDto updateData(String appId, String modelId, String dataId, Map<String, Object> data) {
        DataModelUtil.setCurrentSaveData();
        DynamicDataPo dataPo = this.get(modelId, dataId);
        //清除空值
        Set<String> nullMap = new HashSet<>();
        data.keySet().removeIf(e -> {
            boolean equals = DynamicDataConstant.DATA_EMPTY.equals(data.get(e));
            if (equals) {
                nullMap.add(e);
            }
            return equals;
        });
        RuleExecuteDto callback = Optional.ofNullable(dataEventService.callback(modelId, dataId, DataEventType.DATA_UPDATE, data, true)).orElseGet(RuleExecuteDto::new);
        if (ObjectNull.isNotNull(callback.getStats()) && !callback.getStats()) {
            return callback;
        }
        nullMap.forEach(e -> data.put(e, DynamicDataConstant.DATA_EMPTY));
        //设置空值
        updateTransactional(appId, modelId, dataId, data, dataPo);
        data.remove(MONGO_ID);
        data.keySet().removeIf(e -> DynamicDataConstant.DATA_EMPTY.equals(data.get(e)));
        //清除空值
        callback = dataEventService.callback(modelId, dataId, DataEventType.DATA_UPDATE, data, false);

        String version = saveDataLog(DataEventType.DATA_UPDATE, appId, modelId, dataId, data, Optional.ofNullable(dataPo).map(DynamicDataPo::getJsonData).orElseGet(Collections::emptyMap));
        data.put(DATA_LOG_VERSION_KEY, version);
        return callback;
    }


    private void updateTransactional(String appId, String modelId, String dataId, Map<String, Object> data, DynamicDataPo dataPo) {
        DynamicDataUtils.clearEcho(data);

        //检查同模型下是否存在关联字段是否也存在这几个key中
        Map<String, DataFieldPo> list = dataFieldService.list(Wrappers.query(new DataFieldPo().setJvsAppId(appId).setModelId(modelId)
                                .setDesignId(getDesignId())).lambda()
                        .in(DataFieldPo::getFieldType, DataFieldType.CONTAINER))
                .stream()
                .filter(a -> {
                    if (DataFieldType.tab.equals(a.getFieldType())) {
                        return true;
                    }
                    return data.keySet().contains(a.getFieldKey());
                })
                .collect(Collectors.toMap(DataFieldPo::getFieldKey, Function.identity(), (t1, t2) -> t1));

        //保存或更新下级表格里的数据
        for (DataFieldPo dataFieldPo : list.values()) {
            FieldPublicHtml publicHtml = iDataFieldHandler.get(dataFieldPo.getFieldType().getDesc()).toHtml(dataFieldPo.getDesignJson());
            saveOrUpdateSubsetTable(appId, publicHtml, data, modelId, "");
        }
        //可能模型没有找到
        if (ObjectNull.isNull(dataPo)) {
            return;
        }
        Map<String, Object> jsonData = dataPo.getJsonData();
        jvsAppLogService.savelog(modelId, AppLogTypeEnum.update, jsonData, data);
        this.onlyUpdate(modelId, dataId, jsonData, data);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String updateReturnVersion(String appId, String modelId, String dataId, Map<String, Object> setData) {
        updateData(appId, modelId, dataId, setData);
        String dataVersion = (String) setData.get(DATA_LOG_VERSION_KEY);
        setData.remove(DATA_LOG_VERSION_KEY);
        return dataVersion;
    }

    /**
     * 记录修改日志
     *
     * @param appId      应用ID
     * @param modelId    模型ID
     * @param jsonData   原始数据
     * @param difference 修改数据
     * @param data       修改后的数据
     */
    private List saveDataChange(String appId, String modelId, Map<String, Object> jsonData, MapDifference<String, Object> difference, Map<String, Object> data) {
        Map<String, FieldBasicsHtml> map = dataFieldService.getFields(appId, modelId, true, false).stream().collect(Collectors.toMap(FieldBasicsHtml::getFieldKey, Function.identity()));
        //记录内容变化到一个字段中去
        List dataChange = new ArrayList();
        DataChangePo dataChangePo = new DataChangePo();
        UserDto userDto = UserCurrentUtils.getNullableUser();
        if (ObjectNull.isNotNull(userDto)) {
            dataChangePo.setUserName(userDto.getRealName()).setHeadImg(userDto.getHeadImg());
        }
        dataChangePo.setTimestamp(DateUtil.now());

        List<String> changes = new ArrayList<>();

        //新增的字段
        difference.entriesOnlyOnRight().keySet().stream().filter(e -> map.containsKey(e)).filter(e -> ObjectNull.isNotNull(difference.entriesOnlyOnRight().get(e))).forEach(e -> {
            Object echo = dataFieldHandler.echo(map.get(e), difference.entriesOnlyOnRight().get(e), data);
            String s = " 创建了 " + map.get(e).getFieldName() + "为 " + echo;
            changes.add(s);
        });

        //TODO 需要将修改记录,单独存放到一个数据表中
        difference.entriesDiffering().keySet().stream().filter(e -> !DATA_CHANGE.equalsIgnoreCase(e))
                //判断字段
                .filter(e -> map.containsKey(e)).map(e -> {
                    if (ObjectNull.isNull(difference.entriesDiffering().get(e))) {
                        return null;
                    }
                    if (ObjectNull.isNull(difference.entriesDiffering().get(e).leftValue(), difference.entriesDiffering().get(e).rightValue())) {
                        return null;
                    }
                    if (difference.entriesDiffering().get(e).leftValue().equals(difference.entriesDiffering().get(e).rightValue())) {
                        return null;
                    } else {
                        Object right = dataFieldHandler.echo(map.get(e), difference.entriesDiffering().get(e).rightValue(), data);
                        Object left = dataFieldHandler.echo(map.get(e), difference.entriesDiffering().get(e).leftValue(), data);
                        return "更新了" + map.get(e).getFieldName() + "   " + left + " 为  " + right;
                    }
                }).filter(ObjectNull::isNotNull).forEach(changes::add);
        dataChangePo.setContent(String.join("<br />", changes));
        dataChange.add(dataChangePo);
        return dataChange;
    }

    @Override
    public RuleExecuteDto remove(String modelId, String dataId) {
        DynamicDataPo dataPo = this.get(modelId, dataId);
        Map<String, Object> oldData = dataPo.getJsonData();
        RuleExecuteDto callback = dataEventService.callback(dataPo.getModelId(), dataId, DataEventType.DATA_DELETE, oldData, true);
        if (ObjectNull.isNotNull(callback) && !callback.getStats()) {
            return callback;
        }
        String version = removeTransactional(modelId, dataId);
        oldData.remove(MONGO_ID);
        callback = dataEventService.callback(dataPo.getModelId(), dataId, DataEventType.DATA_DELETE, oldData, false);
        return callback;
    }

    @Transactional(rollbackFor = Exception.class)
    public String removeTransactional(String modelId, String dataId) {
        this.onlyRemove(modelId, dataId);
        // 删除关联表单数据,默认不修改其它数据
        ArrayList<Object> value = new ArrayList<>();
        value.add(new Dict().set("timestamp", DateUtil.now()).set("content", UserCurrentUtils.getRealName() + "删除了数据"));
        return dataLogService.saveLog(modelId, dataId, Collections.emptyMap(), value, DataEventType.DATA_DELETE);
    }

    @Override
    public Page<Map<String, Object>> queryPage(String appId, Page<DynamicDataPo> page, String modelId, Map<String, String> combiningFieldFormulaContentMap, List<List<QueryConditionDto>> conditions, List<QueryOrderDto> sorts, List<String> fieldKeyList, boolean addButtonInfo, boolean echo, Boolean andOr, List<FieldBasicsHtml> fieldBasicsHtmls) {
        return queryPage(appId, page, modelId, combiningFieldFormulaContentMap, conditions, sorts, fieldKeyList, addButtonInfo, echo, andOr, fieldBasicsHtmls, new PageAggregationSetting());
    }

    @Override
    @SneakyThrows
    public Page<Map<String, Object>> queryPage(String appId, Page<DynamicDataPo> page, String modelId, Map<String, String> combiningFieldFormulaContentMap, List<List<QueryConditionDto>> conditions, List<QueryOrderDto> sorts, List<String> fieldKeyList, boolean addButtonInfo, boolean echo, Boolean andOr, List<FieldBasicsHtml> fieldBasicsHtmls, PageAggregationSetting pageAggregationSetting) {
        long size = page.getSize();
        long current = page.getCurrent();
        Page<Map<String, Object>> mapPage = new Page<>(current, size);
        //判断分页是否合理
        if (size < 1 || current < 1) {
            mapPage.setRecords(Collections.emptyList());
            return mapPage;
        }

        //设置数据权限
        List<Criteria> criteriaList = DynamicDataUtils.getAuthCriteria();

        //数据存在_id属性
        Criteria authCriteria = DynamicDataUtils.trueCriteria();

        //将查询条件和列表过滤条件进行分离
        if (ObjectNull.isNotNull(conditions)) {
            //列表过滤条件
            List<QueryConditionDto> collect = conditions.stream().flatMap(Collection::stream).filter(QueryConditionDto::getCrud).collect(Collectors.toList());
            //过滤出列表过滤的数据
            if (ObjectNull.isNotNull(collect)) {
                //将列表过滤和数据权限添加上组合查询
                List<Criteria> crud = buildDynamicCriteriaList(collect);
                if (ObjectNull.isNotNull(criteriaList)) {
                    authCriteria = DynamicDataUtils.trueCriteria().andOperator(crud).orOperator(criteriaList);
                } else {
                    //列表过滤设置“或查询”处理
                    Boolean pageDesignFilterType = getPageDesignFilterType();
                    if (Boolean.FALSE.equals(pageDesignFilterType) && ObjectNull.isNotNull(crud)) {
                        Criteria[] criteriaArray = crud.toArray(new Criteria[0]);
                        Criteria filterCriteria = new Criteria().orOperator(criteriaArray);
                        authCriteria = DynamicDataUtils.trueCriteria().andOperator(filterCriteria);
                    } else {
                        authCriteria = DynamicDataUtils.trueCriteria().andOperator(crud);
                    }
                }
            } else if (ObjectNull.isNotNull(criteriaList)) {
                if (criteriaList.size() == 1) {
                    authCriteria = DynamicDataUtils.trueCriteria().andOperator(criteriaList);
                } else {
                    authCriteria = DynamicDataUtils.trueCriteria().orOperator(criteriaList);
                }
            }
            conditions = conditions.stream().map(e -> e.stream().filter(s -> !s.getCrud()).collect(Collectors.toList())).collect(Collectors.toList());
        }

        //处理单位查询条件“全包含”的查询
        DynamicDataUtils.handleDeptConditions(conditions, fieldBasicsHtmls);

        //拼接查询条件
        List<Criteria> list = DynamicDataUtils.buildDynamicGroupCriteriaListToPageQueryController(conditions);

        //处理待审批情况只返回用户可审核数据，修改查询条件
        if (Boolean.TRUE.equals(SystemThreadLocal.get(LIMIT_PENDING_SEARCH_SCOPE))) {
            boolean flag = conditions.stream().flatMap(Collection::stream)
                    .anyMatch(e -> FlowDataFieldEnum.TASK_STATE.getFieldKey().equals(e.getFieldKey()) && "待审批".equals(e.getValue()));
            if (flag) {
                //获取当前用户该模型下待审批的数据
                List<String> taskDataIds = designHandler.getCurPendingTaskDataIds(appId, modelId);
                list.add(Criteria.where("id").in(taskDataIds));
            }
        }

        //总数
        long total;

        //排序条件
        Sort sort = null;
        //设置排序
        List<Sort.Order> sortSetting;
        if (ObjectNull.isNotNull(sorts) && ObjectNull.isNotNull(
                sortSetting = sorts.stream().filter(e -> ObjectNull.isNotNull(e.getDirection(), e.getFieldKey()))
                        .map(e -> new Sort.Order(e.getDirection(), e.getFieldKey())).collect(Collectors.toList())
        )) {
            sort = Sort.by(sortSetting);
        } else {
            // 默认按创建时间倒序排序  需要在库里面创建对应的索引,进行数据加速
            sort = Sort.by(Sort.Direction.DESC, Get.name(DynamicDataPo::getCreateTime))
                    //导入进来的数据时间一致.需要再通过ID排序
                    .and(Sort.by(Sort.Direction.ASC, "dataId"));
        }

        //结果列表
        List<Map> mapList;
        //跳过
        long skip = size * (current - 1);
        // 处理聚合配置
        List<String> aggregationFields = pageAggregationSetting.getAggregationFields();
        if (ObjectNull.isNotNull(aggregationFields)) {
            Criteria criteria = andOrCriteria(list, authCriteria, andOr);
            //统计聚合条数
            PageAggregationQuery countQuery = PageAggregationQuery.builder()
                    .criteria(criteria)
                    .aggregationFields(aggregationFields)
                    .build();
            total = countAggregation(modelId, countQuery);
            mapPage.setTotal(total);
            if (total == 0 || skip >= total) {
                mapPage.setRecords(Collections.emptyList());
                return mapPage;
            }

            Map<String, AggregationTypeEnum> aggregationHandleFieldsMap = pageAggregationSetting.getAggregationHandleFieldsMap();
            //设置聚合信息，如聚合字段，其他聚合处理
            PageAggregationQuery query = PageAggregationQuery.builder()
                    .criteria(criteria)
                    .sort(sort)
                    .skip(skip)
                    .limit(size)
                    .aggregationFields(aggregationFields)
                    .aggregationHandleFieldsMap(aggregationHandleFieldsMap)
                    .build();
            mapList = executeAggregation(modelId, query);
        }
        //非聚合配置
        else {
            Query query = DynamicDataUtils.andOr(list, authCriteria, andOr);

            //判断数据权限, 如果跳过数据权限，则查询数据
            Boolean isFree = SystemThreadLocal.get(DynamicDataUtils.KEY_AUTH_FREE);
            if (Boolean.TRUE.equals(isFree)) {
                total = dataModelHandler.estimatedCount(modelId);
            } else if (ObjectNull.isNull(list) && ObjectNull.isNotNull(isFree)) {
                //如果条件为空，跳过权限也为空
                total = dataModelHandler.estimatedCount(modelId);
            } else {
                //如果没有跳过
                // 查询总页数
                total = dataModelHandler.count(query, modelId);
            }
            mapPage.setTotal(total);
            if (total == 0 || skip >= total) {
                mapPage.setRecords(Collections.emptyList());
                return mapPage;
            }

            //限制查询字段
            query.fields().exclude(MONGO_ID);
            String[] fieldArray;
            if (ObjectUtils.isNotEmpty(fieldKeyList)) {
                Set<String> fieldKeys = new HashSet<>(fieldKeyList);
                // 默认加上数据id
                fieldKeys.add(Get.name(DynamicDataPo::getId));
                String[] fields = new String[fieldKeys.size()];
                fieldArray = fieldKeys.toArray(fields);
                query.fields().include(fieldArray);
            }
            query.with(sort);
            // 查询数据
            query.skip(skip).limit((int) size);
            mapList = dataModelHandler.find(query, Map.class, modelId);
        }

        List<Map<String, Object>> dataList = mapList.stream().map(e -> (Map<String, Object>) e).collect(Collectors.toList());
        // 获取设计字段
        DataModelPo dataModelPo = dataModelService.getById(modelId);

        //处理字段回显
        if (echo) {
            fieldBasicsHtmls.removeIf(e -> Boolean.TRUE.equals(e.getLimitEcho()));
            dataList = dataList.stream().map(e -> echo(e, fieldBasicsHtmls, false)).collect(Collectors.toList());
        }

        //处理按钮显示及流程按钮
        if (addButtonInfo) {
            designHandler.handleButtonInfo(dataList, EnvConstant.PAGE_BUTTON_DISPLAY);
        }

        //数据脱敏操作
        dataList.forEach(e -> encryptionData(e, dataModelPo));
        if (ObjectNull.isNotNull(combiningFieldFormulaContentMap)) {
            dataList.forEach(e -> combiningFieldFormulaContent(e, combiningFieldFormulaContentMap));
        }

        // 封装返回值
        mapPage.setRecords(dataList);
        return mapPage;
    }

    /**
     * 组合字段
     *
     * @param line                            某一行的数据
     * @param combiningFieldFormulaContentMap 可能需要进行转换的数据
     */
    private void combiningFieldFormulaContent(Map<String, Object> line, Map<String, String> combiningFieldFormulaContentMap) {
        for (String key : combiningFieldFormulaContentMap.keySet()) {
//            if (line.containsKey(key)) {
            Object o = designHandler.runFormula(combiningFieldFormulaContentMap.get(key), line, EnvConstant.PAGE_BUTTON_DISPLAY);
            line.put(key + DynamicDataUtils.SUFFIX_ECHO, o);
//            }
        }
    }

    @Override
    public List<DynamicDataPo> queryList(List<String> modelIds) {
        if (ObjectUtils.isEmpty(modelIds)) {
            return Collections.emptyList();
        }
        Query query = new Query();
        query.fields().exclude(MONGO_ID);
        List<DynamicDataPo> result = new ArrayList<>();
        for (String modelId : modelIds) {
            List<Map> dataList = dataModelHandler.find(query, Map.class, modelId);
            result.addAll(dataList.stream().map(e -> parseBean((Map<String, Object>) e)).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> queryList(String modelId, Criteria criteria, List<String> fieldKeyList, Sort sorts) {
        return queryList(modelId, criteria, fieldKeyList, sorts, null);
    }

    @Override
    public List<Map<String, Object>> queryList(String modelId, Criteria criteria, List<String> fieldKeyList, Sort sorts, Integer limit) {
        if (StringUtils.isBlank(modelId) || ObjectUtils.isEmpty(fieldKeyList)) {
            return Collections.emptyList();
        }
        Query query = this.getPermitQuery(criteria);

        query.with(sorts);
        if (ObjectNull.isNotNull(limit)) {
            query.limit(limit);
        }
        // 指定查询字段
        fieldKeyList.add(Get.name(DynamicDataPo::getId));
        fieldKeyList = fieldKeyList.stream().distinct().collect(Collectors.toList());
        // 指定查询字段
        String[] fields = new String[fieldKeyList.size()];
        query.fields().exclude(MONGO_ID).include(fieldKeyList.toArray(fields));
        List<Map> dataList = dataModelHandler.find(query, Map.class, modelId);
        List<Map<String, Object>> collect = dataList.stream().map(e -> (Map<String, Object>) e).collect(Collectors.toList());
        //如果是快速检索 需要去重数据
        try {
            if (RETRIVAL.equals(WebUtils.getRequest().getHeader(QUERY_BY))) {
                //只返回10条数据
                collect = collect.subList(0, 10);
            }
        } catch (Exception e) {
            // 定时任务无法获取请求头.
        }
        return collect;
    }

    @Override
    public List<Map<String, Object>> queryList(String modelId, Criteria criteria, List<String> fieldKeyList, Integer limit) {
        // 默认按创建时间倒序排序
        Sort sort = Sort.by(Get.name(DynamicDataPo::getCreateTime)).descending();
        return queryList(modelId, criteria, fieldKeyList, sort, limit);

    }

    @Override
    public List<Map<String, Object>> queryList(String modelId, Criteria criteria, List<String> fieldKeyList) {
        // 默认按创建时间倒序排序
        Sort sort = Sort.by(Get.name(DynamicDataPo::getCreateTime)).descending();
        return queryList(modelId, criteria, fieldKeyList, sort, null);

    }


    @Override
    public List<Map<String, Object>> queryList(String modelId, String... fieldKey) {
        return queryList(modelId, Arrays.stream(fieldKey).collect(Collectors.toList()));
    }

    public List<Map<String, Object>> queryList(String modelId, List<String> fieldKey) {
        if (StringUtils.isBlank(modelId) || ObjectNull.isNull(fieldKey)) {
            // 不传字段时, 数据量可能较大, 直接返回空集合
            return Collections.emptyList();
        }
        Criteria criteria = DynamicDataUtils.initCriteria(null);
        Query query = this.getPermitQuery(criteria);
        // 默认按创建时间倒序排序
        Sort sort = Sort.by(Get.name(DynamicDataPo::getCreateTime)).descending();
        query.with(sort);
        // 指定查询字段
        fieldKey.add(Get.name(DynamicDataPo::getId));
        query.fields().exclude(MONGO_ID).include(fieldKey.toArray(new String[fieldKey.size()]));
        List<Map> dataList = dataModelHandler.find(query, Map.class, modelId);
        if (ObjectNull.isNull(dataList)) {
            //如果查询为空,返回为空数组
            return new ArrayList<>();
        }
        return dataList.stream().map(e -> (Map<String, Object>) e).collect(Collectors.toList());
    }

    @Override
    public List<Map> queryList(String appId, String modelId, Criteria criteria, Sort sort, List<String> excludeFieldKeyList, List<String> fieldKeyList) {
        if (StringUtils.isBlank(modelId)) {
            return Collections.emptyList();
        }
        Query query = this.getPermitQuery(criteria);

        // 默认按创建时间倒序排序

        if (ObjectNull.isNull(sort)) {
            // 默认按创建时间倒序排序
            sort = Sort.by(Sort.Direction.DESC, Get.name(DynamicDataPo::getCreateTime)).descending();
            query.with(sort);
        } else {
            query.with(sort);
        }
        if (CollectionUtils.isNotEmpty(excludeFieldKeyList)) {
            String[] excludeFields = new String[excludeFieldKeyList.size()];
            query.fields().exclude(excludeFieldKeyList.toArray(excludeFields));
        }
        if (CollectionUtils.isNotEmpty(fieldKeyList)) {
            // 指定查询字段
            String[] fields = new String[fieldKeyList.size()];
            query.fields().include(fieldKeyList.toArray(fields));
        }
        List<Map> dataList = dataModelHandler.find(query, Map.class, modelId);
        return dataList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> querySingle(String appId, String modelId, String dataId) {
        return this.querySingle(appId, modelId, dataId, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> querySingle(String appId, String modelId, String dataId, boolean addButtonInfo) {
        if (StringUtils.isBlank(modelId) || StringUtils.isBlank(dataId)) {
            throw new BusinessException("数据查询异常, 查询条件为空");
        }
        Map<String, Object> dataMap = this.getMap(modelId, dataId);
        if (addButtonInfo) {
            designHandler.handleButtonInfo(dataMap, EnvConstant.FORM_BUTTON_DISPLAY);
        }
        DataModelPo byId = dataModelService.getById(modelId);
        //处理表单中的脱敏
        encryptionData(dataMap, byId);
        return dataMap;
    }

    @Override
    public ButtonRtnVo handleFormButton(Map<String, Object> dataMap) {
        designHandler.handleButtonInfo(dataMap, EnvConstant.FORM_BUTTON_DISPLAY);
        return new ButtonRtnVo().setJvsEnabledButtons((List) dataMap.get(DesignHandler.JVS_ENABLED_BUTTONS));
    }

    @Override
    public Map<String, Object> querySingleReplaceSourceFieldData(String appId, String modelId, String dataId, String designId, boolean addButtonInfo, boolean replace) {
        //查询单条数据
        Map<String, Object> stringObjectMap = querySingle(appId, modelId, appId, addButtonInfo);
        //此为替换结果数据，默认都要做转换
        Map<String, FieldBasicsHtml> fieldsMap = dataFieldService.getFields(appId, dataId, designId, true, true)
                .stream()
                .filter(e -> ObjectNull.isNotNull(e.getType()))
                .collect(Collectors.toMap(FieldBasicsHtml::getFieldKey, Function.identity()));

//        if (replace) {
//            //如果需要转换,判断是否替换，默认都要转换,递归对象，是否有_1的 key，如果有，覆盖原有 key
//            replaceSourceFieldData();
//        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object queryField(String appId, String modelId, String dataId, String fieldId) {
        Map<String, Object> map = this.querySingle(appId, modelId, dataId);
        if (!map.containsKey(fieldId)) {
            //没有数据直接返回为空
            return null;
//            throw new BusinessException("数据不存在");
        }
        return map.get(fieldId);
    }

    @Override
    public Map<String, Object> paresMap(DynamicDataPo data) {
        Map<String, Object> map = BeanToMapUtils.beanToMap(data);
        map.put(Get.name(DynamicDataPo::getCreateTime), LocalDateTimeUtil.formatNormal((LocalDateTime) map.get(Get.name(DynamicDataPo::getCreateTime))));
        map.put(Get.name(DynamicDataPo::getUpdateTime), LocalDateTimeUtil.formatNormal((LocalDateTime) map.get(Get.name(DynamicDataPo::getUpdateTime))));
        Map<String, Object> dynamicMap = data.getJsonData();
        map.remove(Get.name(DynamicDataPo::getJsonData));
        dynamicMap.putAll(map);
        return dynamicMap;
    }

    @Override
    public Map<String, Object> paresMap(DynamicDataPo data, Collection<String> fieldKeys) {
        Map<String, Object> map = BeanToMapUtils.beanToMap(data);
        Map<String, Object> dynamicMap = this.clear(data.getJsonData(), fieldKeys);
        map.remove(Get.name(DynamicDataPo::getJsonData));
        dynamicMap.putAll(map);
        return dynamicMap;
    }

    @Override
    public DynamicDataPo parseBean(Map<String, Object> data) {
        if (ObjectUtils.isEmpty(data)) {
            return null;
        }
        DynamicDataPo dataPo = BeanCopyUtil.copy(DynamicDataPo.class, data);
        Map<String, Object> beanMap = BeanToMapUtils.beanToMap(dataPo);
        for (String key : beanMap.keySet()) {
            data.remove(key);
        }
        dataPo.setJsonData(data);
        return dataPo;
    }

    /**
     * 查询数据对象
     *
     * @param dataId 数据id
     * @return 数据对象
     */
    private DynamicDataPo get(String modelId, String dataId) {
        return this.parseBean(this.getMap(modelId, dataId));
    }

    /**
     * 查询数据
     *
     * @param dataId 数据id
     * @return 数据
     */
    private Map<String, Object> getMap(String modelId, String dataId) {
        if (StringUtils.isBlank(dataId)) {
            throw new BusinessException("数据id为空");
        }
        Criteria criteria = DynamicDataUtils.initCriteria(null).and(Get.name(DynamicDataPo::getId)).is(dataId);
        Query query = this.getPermitQuery(criteria);
        // 排除MongoDB的id字段
        query.fields().exclude(MONGO_ID);
        Map<String, Object> dataMap = dataModelHandler.findOne(query, Map.class, modelId);
        if (Objects.isNull(dataMap)) {
            log.info("数据不存在, 模型id: {}, 数据id: {}", modelId, dataId);
            //不返回空数据,避免报错,直接返回空对象
            return new HashMap<>(1);
        }
        return dataMap;
    }

    /**
     * 查询数据
     *
     * @param dataId 数据id
     * @return 数据
     */
    private Map<String, Object> getFreePermissionMap(String modelId, String dataId) {
        if (StringUtils.isBlank(dataId)) {
            throw new BusinessException("数据id为空");
        }
        Criteria criteria = DynamicDataUtils.initCriteria(null).and(Get.name(DynamicDataPo::getId)).is(dataId);
        Query query = new Query(trueCriteria().andOperator(criteria));
        // 排除MongoDB的id字段
        query.fields().exclude(MONGO_ID);
        Map<String, Object> dataMap = dataModelHandler.findOne(query, Map.class, modelId);
        if (Objects.isNull(dataMap)) {
            log.info("数据不存在, 模型id: {}, 数据id: {}", modelId, dataId);
            //不返回空数据,避免报错,直接返回空对象
            return new HashMap<>(1);
        }
        return dataMap;
    }

    @Override
    public String onlySave(String modelId, Map<String, Object> data) {
        String dataId = IdGenerator.getIdStr();
        return onlySave(modelId, dataId, data);
    }

    /**
     * 保存数据
     *
     * @param modelId 数据模型id
     * @param data    数据内容
     * @return 新增后的数据id
     */
    private String onlySave(String modelId, String dataId, Map<String, Object> data) {
        if (ObjectUtils.isEmpty(data)) {
            data = new HashMap<>(1);
        }
        // 保存至MongoDB
        dataModelHandler.insert(this.insertFill(modelId, dataId, data), modelId);
        // todo 不计算数据大小,通过定时计算,或数据库获取 更新模型数据大小
//        dataModelService.updateModeSize(modelId);
        return dataId;
    }

    /**
     * 保存数据
     *
     * @param modelId  数据模型id
     * @param dataList 数据内容
     * @return 新增后的数据id
     */
    private boolean onlySave(String modelId, List<Map<String, Object>> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }
        // 保存至MongoDB
        List<Map<String, Object>> mapList = dataList.stream().map(data -> this.insertFill(modelId, String.valueOf(data.getOrDefault("dataId", "")), data)).collect(Collectors.toList());
        int batch = 2000;
        int size = mapList.size();
        for (int i = 0; i < size; i += batch) {
            List<Map<String, Object>> subList = mapList.subList(i, Math.min(i + batch, size));
            dataModelHandler.insertBatch(subList, modelId);
        }
        // 更新模型数据大小
        dataModelService.updateModeSize(modelId);
        return true;
    }

    /**
     * 删除数据
     *
     * @param dataId 数据id
     * @return 删除前的数据内容
     */
    @Override
    public Map<String, Object> onlyRemove(String modelId, String dataId) throws BusinessException {
        //逻辑删除
        Criteria criteria = Criteria.where(Get.name(DynamicDataPo::getId)).is(dataId);
        Query query = this.getPermitQuery(criteria);
        Map andRemove = dataModelHandler.findAndRemove(query, Map.class, modelId);
        if (ObjectNull.isNotNull(andRemove)) {
            // 设置更新时间
            this.updateFill(modelId, dataId, andRemove);

            jvsAppLogService.savelog(modelId, AppLogTypeEnum.remove, andRemove, null);

            dataModelHandler.insert(andRemove, DataModelUtil.buildRemoveCollectionName(modelId));
            // 更新模型数据大小
            dataModelService.updateModeSize(modelId);

            // 删除数据的流程任务
            removeTask(Collections.singletonList(andRemove));
        }
        return andRemove;
    }

    @Override
    public void onlyUpdate(String modelId, String dataId, Map<String, Object> data) {
        onlyUpdate(modelId, dataId, null, data);
    }

    /**
     * 修改数据
     *
     * @param dataId  数据id
     * @param oldData 原数据内容
     * @param newData 数据内容(key相同时会覆盖)
     * @return 修改后的数据内容
     */
    @Override
    public Map<String, Object> onlyUpdate(String modelId, String dataId, Map<String, Object> oldData, Map<String, Object> newData) {
        if (ObjectUtils.isEmpty(newData)) {
            newData = Collections.emptyMap();
        }
        //查看哪些字段是脱敏的，是否脱敏
        handleEncryptCover(modelId, dataId, oldData, newData);
        if (ObjectUtils.isEmpty(oldData)) {
            oldData = newData;
        } else {
            oldData.putAll(newData);
        }
        // 更新至MongoDB
        oldData = this.updateFill(modelId, dataId, oldData);
        // 构建更新条件
        Criteria criteria = DynamicDataUtils.initCriteria(null).and(Get.name(DynamicDataPo::getId)).is(dataId);
        Query query = this.getPermitQuery(criteria);
        // 设置更新操作
        Update update = new Update();
        for (Map.Entry<String, Object> entry : oldData.entrySet()) {
            if (entry.getValue() != null) {
                update.set(entry.getKey(), entry.getValue());
            }
            if (ObjectNull.isNotNull(entry.getValue()) || "".equals(entry.getValue())) {
                if (entry.getValue().equals(DynamicDataConstant.DATA_EMPTY)) {
                    entry.setValue(null);
                    update.set(entry.getKey(), null);
                } else {
                    update.set(entry.getKey(), entry.getValue());
                }
            }
        }
        dataModelHandler.updateFirst(query, update, modelId);
        // 更新模型数据大小
        dataModelService.updateModeSize(modelId);
        return oldData;
    }


    /**
     * 填充新增时的数据字段
     *
     * @param modelId 模型id
     * @param dataId  数据id
     * @param data    数据内容
     * @return 填充后的数据内容
     */
    private Map<String, Object> insertFill(String modelId, String dataId, Map<String, Object> data) {
        LocalDateTime now = LocalDateTime.now();
        UserDto user = UserCurrentUtils.getNullableUser();
        if (StringUtils.isBlank(dataId)) {
            // id默认为雪花算法
            dataId = IdWorker.getIdStr();
        }
        data = this.updateFill(modelId, dataId, data);
        // 逻辑删除字段
        Object delFlag = Optional.ofNullable(data.get(Get.name(DynamicDataPo::getDelFlag))).orElse(Boolean.FALSE);
        data.put(Get.name(DynamicDataPo::getDelFlag), delFlag);
        // BasePo
        if (ObjectNull.isNotNull(user)) {
            data.put(Get.name(DynamicDataPo::getJobId), user.getJobId());
            data.put(Get.name(DynamicDataPo::getDeptId), user.getDeptId());
            data.put(Get.name(DynamicDataPo::getCreateById), user.getId());
            data.put(Get.name(DynamicDataPo::getCreateBy), user.getRealName());
        }
        // BasalPo
        data.put(Get.name(DynamicDataPo::getCreateTime), DateUtil.now());
        return data;
    }

    /**
     * 填充更新时的数据字段
     *
     * @param modelId 模型id
     * @param dataId  数据id
     * @param data    数据内容
     * @return 填充后的数据内容
     */
    private Map<String, Object> updateFill(String modelId, String dataId, Map<String, Object> data) {
        UserDto user = UserCurrentUtils.getNullableUser();
        if (Objects.isNull(data)) {
            data = new HashMap<>(16);
        }
        String id = data.getOrDefault("dataId", dataId).toString();
        data.put(Get.name(DynamicDataPo::getId), id);
        if (StringUtils.isNotBlank(modelId)) {
            data.put(Get.name(DynamicDataPo::getModelId), modelId);
        }
        // BasalPo
        data.put(Get.name(DynamicDataPo::getUpdateTime), DateUtil.now());
        if (ObjectNull.isNotNull(user)) {
            data.put(Get.name(DynamicDataPo::getUpdateBy), user.getRealName());
            data.put("updateById", user.getId());
            if (ObjectNull.isNotNull(data.get(Get.name(DynamicDataPo::getCreateById)))
                    && data.get(Get.name(DynamicDataPo::getCreateById)).equals(user.getId())) {
                data.put(Get.name(DynamicDataPo::getDeptId), user.getDeptId());
            }
        }
        return data;
    }

    /**
     * 筛选指定key的数据
     *
     * @param originalData 原始数据(该集合的元素可能会被删除)
     * @param keys         需要的key集合
     * @return 筛选后的数据
     */
    private Map<String, Object> clear(Map<String, Object> originalData, Collection<String> keys) {
        if (ObjectUtils.isEmpty(originalData) || ObjectUtils.isEmpty(keys)) {
            return new HashMap<>(1);
        }
        int targetSize = keys.size();
        int size = originalData.size();
        if (targetSize > size) {
            originalData.keySet().removeIf(e -> !keys.contains(e));
            return originalData;
        }
        Map<String, Object> result = new HashMap<>(targetSize);
        for (String key : keys) {
            result.put(key, originalData.get(key));
        }
        return result;
    }

    @Override
    public Map<String, Object> paresMapWithEcho(String appId, Map<String, Object> data, String modelId, String designId, boolean override) {
        List<FieldBasicsHtml> fields = dataFieldService.getFields(appId, modelId, designId, true, false);
        // 数据类型回显处理
        return echo(data, fields, override);
    }

    @Override
    public Map<String, Object> paresMapWithEchoAndFields(Map<String, Object> data, List<FieldBasicsHtml> fields, boolean override) {
        // 数据类型回显处理
        return echo(data, fields, override);
    }

    public Map<String, Object> paresMapWithEcho(String appId, DynamicDataPo data, String modelId) {
        List<FieldBasicsHtml> fields = dataFieldService.getFields(appId, modelId, true, false);
        List<FieldBasicsHtml> relationFields = fields.stream().filter(f -> DataFieldType.tableForm.equals(f.getType())).collect(Collectors.toList());
        // 不是自己看字段中是否存在脱敏处理
        // 数据类型回显处理
        fields.removeAll(relationFields);
        Map<String, Object> echo = this.echo(this.paresMap(data), relationFields, true);
        DataModelPo byId = dataModelService.getById(modelId);
        this.paresMap(data);
        //处理表单中的脱敏
        encryptionData(echo, byId);
        // 关联数据类型回显处理
        return echo;
    }

    /**
     * 数据表单脱敏处理
     *
     * @param data
     * @param byId
     */
    private void encryptionData(Map<String, Object> data, DataModelPo byId) {
        if (ObjectNull.isNotNull(byId, byId.getSetting())) {
            if (ObjectNull.isNull(byId.getSetting().getEncryptionFields())) {
                return;
            }
            List<PersonnelDto> userList = byId.getSetting().getUserList();
            if (!RoleUtils.checkPersonnels(userList)) {
                //放行。不处理
                return;
            }
            if (ObjectNull.isNotNull(userList)) {
                List<EncryptionFieldsPo> encryptionFields = byId.getSetting().getEncryptionFields();
                encryptionFields.forEach(e -> {
                    try {
                        String o = String.valueOf(data.getOrDefault(e.getFieldKey(), ""));
                        String desensitized = SensitiveInfoUtils.getSensitiveKey().get(e.getEncryptionExpress()).apply(o);
                        //支持下拉转换后的脱敏
                        if (data.containsKey(e.getFieldKey() + "_1")) {
                            data.put(e.getFieldKey() + "_1", desensitized);
                        } else {
                            data.put(e.getFieldKey(), desensitized);
                        }
                    } catch (Exception exception) {
                        log.error("脱敏失败", exception);
                    }
                });
            }
        }
    }

    /**
     * 数据对象转Map结构数据
     * <p>
     * 并处理回显内容
     *
     * @param data     数据对象(Map)
     * @param fieldMap 字段信息集合
     * @param override 回显字段是否覆盖原字段
     * @return Map数据
     */
    @Override
    public Map<String, Object> echo(Map<String, Object> data, Map<String, FieldBasicsHtml> fieldMap, boolean override) {
        return echo(data, fieldMap, override, e -> a -> a);
    }

    @Override
    public Map<String, Object> echo(Map<String, Object> data, Map<String, FieldBasicsHtml> fieldMap, boolean override, Function<DataFieldType, Function<Object, Object>> a) {
        //数据库的数据，用于多层下级数据
        Map<String, Object> olddata = new HashMap<>(data);
        List<String> detachDataFieldList = new ArrayList<>();
        //因为这个循环是改变内部对象，所以需要创建一个新的
        fieldMap.values().stream().collect(Collectors.toList())
                .stream()
                .filter(e -> e.getType().equals(DataFieldType.tab))
                //判断是否开启了数据脱离， 将过滤出有多个数据脱离的选项卡
                .filter(e -> {
                    Object eval = JvsJsonPath.read(e.getDesignJson(), Get.name(TabItemHtml::getDetachData));
                    if (ObjectNull.isNotNull(eval) && eval instanceof Boolean) {
                        boolean flag = (boolean) eval;
                        if (Boolean.TRUE.equals(flag)) {
                            detachDataFieldList.add(e.getFieldKey());
                        }
                        return flag;
                    } else {
                        //兼容老的选项卡数据值
                        return true;
                    }
                })
                .forEach(e -> {
                    //将数据脱离的 key ， 组装为对象属性值
                    TabItemHtml html = (TabItemHtml) iDataFieldHandler.get(DataFieldType.tab.getDesc()).toHtml(e);
                    //判断是否配置了选项的 key名
                    for (FormValueHtml dicDatum : html.getDicData()) {
                        //获取 key名
                        String prop = dicDatum.getProp();
                        List<FieldBasicsHtml> fieldBasicsHtmls = html.getColumn().get(dicDatum.getName());
                        if (ObjectNull.isNull(fieldBasicsHtmls)) {
                            continue;
                        }
                        if (ObjectNull.isNull(prop)) {
                            //将这一级的所有字段类型匹配放入外层
                            fieldBasicsHtmls.forEach(s -> fieldMap.put(s.getProp(), s));
                        } else {
                            //这里需要创建一个生成组件，用于解析。
                            TabGenerateItemHtml tabGenerateItemHtml = new TabGenerateItemHtml(prop).setColumn(fieldBasicsHtmls);
                            //将组件解析器处理到map中在下级回显进行处理
                            fieldMap.put(prop, tabGenerateItemHtml);
                        }
                    }
                });

        // 处理回显数据
        data.entrySet().stream().collect(Collectors.toList())
                .stream()
                //有key的存在
//                .filter(e -> fieldMap.containsKey(e.getKey()))
                .forEach(entry -> {
                    try {
                        if (override && ObjectNull.isNull(entry.getValue())) {
                            entry.setValue("");
                        }
                        String fieldKey = entry.getKey();
                        Object value = entry.getValue();

                        // 如果tab开启数据脱离，则去除数据脱离的字段回显
                        if (detachDataFieldList.contains(fieldKey)) {
                            return;
                        }

                        FieldBasicsHtml fieldDto = fieldMap.get(fieldKey);
                        if (ObjectNull.isNull(fieldDto)) {
                            return;
                        }
                        IDataFieldHandler fieldHandler = iDataFieldHandler.get(fieldDto.getType().getDesc());
                        if (ObjectNull.isNotNull(fieldHandler, fieldDto.getDesignJson()) && !fieldKey.equals(FlowDataFieldEnum.TASK_STATE.getFieldKey()) && !fieldKey.equals(FlowDataFieldEnum.TASK_PROGRESS.getFieldKey())) {
                            FieldBasicsHtml html = fieldHandler.toHtml(fieldDto);
                            Object echoValue = fieldHandler.getEcho(html, value, override, olddata);
                            //如果不是文件类型的才进行特殊处理
                            //如果没有类型，则默认为输入框
                            if (ObjectNull.isNull(html.getType())) {
                                html.setType(DataFieldType.input);
                            }
                            switch (html.getType()) {
                                case file:
                                case fileUpload:
                                case image:
                                case signature:
                                case imageUpload:
                                case tabGenerate:
                                case tableForm:
                                case htmlEditor:
                                    //以上类型需要进行直接替换，不用判断是否需要替换
                                    data.put(fieldKey, echoValue);
                                    break;
                                case tab:
                                    //以上类型不处理替换
                                    //在节点处理类进行处理了
                                    break;
                                default:
                                    //根据传递的类型处理是否转换或替换
                                    if (override) {
                                        //直接覆盖
                                        data.put(fieldKey, echoValue);
                                    } else {
                                        // 非覆盖时, 添加后缀标识来区分原数据与显示数据('deptId'='1', 'deptId_xxx'='某某部门')
                                        fieldKey = DynamicDataUtils.getEchoFieldKey(fieldKey);
                                        data.put(fieldKey, echoValue);
                                    }
                            }
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
        return data;
    }

    @Override
    public List echo(List<Map> list, Collection<FieldBasicsHtml> fields, boolean override) {
        for (Map data : list) {
            echo(data, fields, override);
        }
        return list;
    }

    public Map<String, Object> echo(Map<String, Object> data, Collection<FieldBasicsHtml> fields, boolean override) {
        Map<String, FieldBasicsHtml> fieldMap = fields.stream().collect(Collectors.toMap(FieldBasicsHtml::getFieldKey, Function.identity()));
        return echo(data, fieldMap, override);
    }

    /**
     * 新增数据前处理自增序号
     *
     * @param modelId  数据模型id
     * @param dataList 数据集合
     */
    private void handleIncreasedIdFields(String appId, String modelId, List<Map<String, Object>> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return;
        }

        OrderFormat mockOrderFormat = new OrderFormat().setOrderDigit(10)
                .setOrderResetRule(OrderResetRuleEnum.n);
        DataIdPo itemOrderNextId = null;
        for (Map<String, Object> e : dataList) {
            // id 空，item_order 空  Y
            if (ObjectNull.isNull(e.get("id")) && ObjectNull.isNull(e.get("item_order"))) {
                if (ObjectNull.isNull(itemOrderNextId)) {
                    itemOrderNextId = dataIdService.nextId(DesignType.data, modelId + "_item_order", dataList.size());
                }
                e.put("item_order", OrderUtils.getOrderNumber("", mockOrderFormat, itemOrderNextId));
                itemOrderNextId.next();
            }
        }

        List<FieldBasicsHtml> increasedIdFields = dataFieldService.list(Wrappers.query(new DataFieldPo().setModelId(modelId).setJvsAppId(appId).setFieldType(DataFieldType.serialNumber))).stream().map(e -> BeanCopyUtil.copy(e, FieldBasicsHtml.class)).collect(Collectors.toList());
        increasedIdFields = increasedIdFields.stream().filter(f -> ObjectNull.isNotNull(f.getDesignJson())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(increasedIdFields)) {
            return;
        }

        // 根据字段设计获取序号前缀
        Map<String, String> prefixMap = this.parseOrderPrefix(increasedIdFields);
        int size = dataList.size();
        DataIdPo nextId = dataIdService.nextId(DesignType.data, modelId, size);
        for (Map<String, Object> data : dataList) {

            for (FieldBasicsHtml field : increasedIdFields) {
                String fieldKey = field.getFieldKey();
                String prefix = prefixMap.get(fieldKey);

                String orderNumber;

                // 根据字段设计获取序号
                if ("item_order".equals(fieldKey)) {
                    continue;
                } else {
                    orderNumber = this.getOrderNumber(field, prefix, nextId);
                }
                data.put(fieldKey, orderNumber);
            }
            nextId.next();
        }
    }

    /**
     * 根据字段设计获取序号前缀
     *
     * @param fields 字段数据集合
     * @return 序号前缀映射 <字段key, 序号前缀>
     */
    private Map<String, String> parseOrderPrefix(List<FieldBasicsHtml> fields) {
        DateTime now = new DateTime();
        Map<String, String> result = new HashMap<>(1);
        for (FieldBasicsHtml field : fields) {
            OrderFormat orderFormat = BeanCopyUtil.copy(OrderFormat.class, field.getDesignJson());
            // 获取流水号前缀
            String prefix = OrderUtils.parseOrderPrefix(orderFormat, now);
            result.put(field.getFieldKey(), prefix);
        }
        return result;
    }

    /**
     * 根据字段设计获取序号重置规则
     *
     * @param field  字段数据
     * @param nextId 序号数据
     * @return 流水号
     */
    private String getOrderNumber(FieldBasicsHtml field, String prefix, DataIdPo nextId) {
        Map<String, Object> designJson = field.getDesignJson();
        OrderFormat orderFormat = BeanCopyUtil.copy(OrderFormat.class, designJson);
        return OrderUtils.getOrderNumber(prefix, orderFormat, nextId);
    }

    /**
     * 处理数据查询权限
     *
     * @param criteria 查询条件构造类
     */
    private Query getPermitQuery(Criteria criteria) {
        List<Criteria> authCondition = DynamicDataUtils.getAuthCriteria();
        if (Boolean.TRUE.equals(SystemThreadLocal.get(KEY_AUTH_DATA_SCOPE_SET))) {
            if (ObjectNull.isNotNull(criteria)) {
                return new Query(trueCriteria().andOperator(criteria).orOperator(authCondition));
            } else {
                return new Query(trueCriteria().orOperator(authCondition));
            }
        }
        if (Objects.nonNull(criteria)) {
            authCondition.add(criteria);
        }
        return new Query(trueCriteria().andOperator(authCondition));
    }

    @Override
    public void checkDataModel(Map<String, Object> data, String modelId) {
        DataModelPo byId = dataModelService.getById(modelId);
        if (ObjectNull.isNull(byId)) {
            throw new BusinessException("数据错误");
        } else {
            DataSettingBo setting = byId.getSetting();
            //如果没有设置
            if (ObjectNull.isNull(setting)) {
                return;
            } else {
                if (setting.isOnce()) {
                    String userId = Optional.ofNullable(UserCurrentUtils.getUserId()).orElseThrow(() -> new BusinessException("用户未登录"));
                    long total = getTotalKey(modelId, "createById", userId);
                    if (total > 0) {
                        throw new BusinessException("数据已存在,不允许重复提交");
                    }
                }

                String formulaContent = setting.getFormulaContent();
                if (ObjectNull.isNotNull(formulaContent)) {
                    if (!formulaContent.trim().isEmpty()) {
                        String expression = setting.getFormulaContent();
                        List<ExpressionParam> expressionParams = ExpressionUtils.parsePostfixExpression(expression);
                        if (expressionParams.isEmpty()) {
                            return;
                        }
                        //处理特殊符
                        Object result = SpringContextUtil.getBean(ExpressionHandler.class).calculate(expression, data, EnvConstant.THIS_DATA_ITEM_VALUE);
                        if (ObjectNull.isNotNull(result)) {
                            //查询库里面是否存在这个值
                            //查询数据库保留字段
                            long total = getTotalKey(modelId, "DataReservedKey", result.toString());
                            if (total > 0) {
                                throw new BusinessException("数据已存在,不允许重复提交");
                            }
                            //如果没有重复，将此数据id存放进数据里面
                            data.put("DataReservedKey", result.toString());
                        }
                    }
                }
            }
        }
    }


    @Override
    public void refreshData(String modelId, DataModelPo dataModelPo) {
        DataModelPo byId = dataModelService.getById(modelId);
        if (ObjectNull.isNull(byId)) {
            throw new BusinessException("数据错误");
        } else {
            DataSettingBo setting = byId.getSetting();
            //如果没有设置
            if (ObjectNull.isNull(setting)) {
                return;
            } else {
                if (setting.isOnce()) {
                    String userId = Optional.ofNullable(UserCurrentUtils.getUserId()).orElseThrow(() -> new BusinessException("用户未登录"));
                    long total = getTotalKey(modelId, "createById", userId);
                    if (total > 0) {
                        dataModelHandler.remove(new Query(new Criteria()), modelId);
                    }
                }
            }
        }

    }

    private long getTotalKey(String modelId, String key, String value) {
        // 拼接查询条件
        List<QueryConditionDto> conditions = new ArrayList<>();
        //创建人等于自己
        conditions.add(new QueryConditionDto().setFieldKey(key).setEnabledQueryTypes(DataQueryType.eq).setValue(value));
        Criteria criteria = DynamicDataUtils.buildDynamicCriteria(conditions);
        criteria = DynamicDataUtils.initCriteria(criteria);
        Query query = this.getPermitQuery(criteria);
        // 限制查询字段
        query.fields().exclude(MONGO_ID);
        Set<String> fieldKeys = new HashSet<>();
        fieldKeys.add(key);
        // 默认加上数据id
        fieldKeys.add(Get.name(DynamicDataPo::getId));
        String[] fields = new String[fieldKeys.size()];
        query.fields().include(fieldKeys.toArray(fields));
        // 查询总页数
        long total = dataModelHandler.count(query, modelId);
        return total;
    }

    @Override
    public void deleteFields(List<String> fields, String dataModelId) {
        Update update = new Update();
        //将这个字段值设置为空
        fields.forEach(e -> update.set(e, null));
        dataModelHandler.updateMulti(new Query(new Criteria()), update, dataModelId);
    }

    @Override
    public void replaceSourceFieldData(String sourceFieldId, String replaceFieldKey, List<Map<String, Object>> data) {
        if (StringUtils.isBlank(sourceFieldId) || StringUtils.isBlank(replaceFieldKey) || CollectionUtils.isEmpty(data)) {
            return;
        }
        // 获取显示来源字段数据
        DataFieldPo dataFieldPo = dataFieldService.getById(sourceFieldId);
        if (ObjectNull.isNull(dataFieldPo)) {
            return;
        }
        // 目前只处理下拉框
        if (Boolean.FALSE.equals(DataFieldType.select.equals(dataFieldPo.getFieldType()))) {
            return;
        }
        SelectItemHtml selectItemHtml = BeanCopyUtil.copy(dataFieldPo.getDesignJson(), SelectItemHtml.class);
        String sourceModelId = selectItemHtml.getFormId();
        String sourceFieldKey = selectItemHtml.getProps().getLabel();
        String idFieldKey = Get.name(DynamicDataPo::getId);
        List<Object> dataIds = data.stream().map(d -> d.get(replaceFieldKey)).collect(Collectors.toList());
        List<QueryConditionDto> conditionDtos = Arrays.asList(new QueryConditionDto().setFieldKey(idFieldKey).setValue(dataIds).setEnabledQueryTypes(DataQueryType.in));
        Criteria criteria = DynamicDataUtils.buildDynamicCriteria(conditionDtos);
        criteria = DynamicDataUtils.initCriteria(criteria);
        List<Map<String, Object>> sourceData = queryList(sourceModelId, criteria, new ArrayList<String>() {{
            add(sourceFieldKey);
        }});
        if (CollectionUtils.isEmpty(sourceData)) {
            return;
        }
        // 替换数据
        data.forEach(d -> {
            Object fieldKeyValue = d.get(replaceFieldKey);
            Object replaceData = sourceData.stream().filter(sdata -> sdata.get(idFieldKey).equals(fieldKeyValue)).map(s -> s.get(sourceFieldKey)).findFirst().orElse(null);
            if (ObjectNull.isNotNull(replaceData)) {
                d.put(replaceFieldKey, replaceData);
            }
        });
    }

    @Override
    public void expandOtherData(Map<String, Object> data) {
        String createUserId = Optional.ofNullable(data.get(Get.name(DynamicDataPo::getCreateById))).orElse("").toString();
        if (StringUtils.isBlank(createUserId)) {
            return;
        }
        R<UserDto> userDtoRes = authUserServiceApi.getById(createUserId);
        if (ObjectNull.isNull(userDtoRes)) {
            return;
        }
        UserDto userDto = userDtoRes.getData();
        // 返回创建人头像
        data.put(Get.name(UserDto::getHeadImg), userDto.getHeadImg());
    }

    @Override
    public List<Map<String, Object>> postQueryList(String appId, String dataModelId, QueryListDto queryPageDto) {
        DynamicDataUtils.dataModelScope(dataModelId);
        List<List<QueryConditionDto>> conditions = CollectionUtils.isNotEmpty(queryPageDto.getGroupConditions()) ? queryPageDto.getGroupConditions() : Collections.singletonList(queryPageDto.getConditions());
        Criteria criteria = DynamicDataUtils.buildDynamicGroupCriteria(conditions);
        criteria = DynamicDataUtils.initCriteria(criteria);
        List<Map<String, Object>> data = queryList(dataModelId, criteria, queryPageDto.getFieldList());
        // 替换显示字段的值
        replaceSourceFieldData(appId, dataModelId, queryPageDto.getFieldList(), data);
        return data;
    }

    /**
     * 根据模型查询所有数据
     *
     * @param dataModelId 模型id
     * @param fieldList   字段名  末认会加Id
     * @param list        查询条件
     * @return
     */
    public List<Map<String, Object>> queryList(String dataModelId, List<String> fieldList, QueryConditionDto... list) {
        List<QueryConditionDto> queryConditions = Arrays.stream(list).collect(Collectors.toList());
        Criteria criteria = DynamicDataUtils.buildDynamicCriteria(queryConditions);
        criteria = DynamicDataUtils.initCriteria(criteria);
        return queryList(dataModelId, criteria, fieldList);
    }

    @Override
    public List<Map<String, Object>> postQueryList(String dataModelId, QueryListDto queryPageDto) {

        DynamicDataUtils.dataModelScope(dataModelId);
        List<List<QueryConditionDto>> conditions = CollectionUtils.isNotEmpty(queryPageDto.getGroupConditions()) ? queryPageDto.getGroupConditions() : Collections.singletonList(queryPageDto.getConditions());
        Criteria criteria = DynamicDataUtils.buildDynamicGroupCriteria(conditions);
        if (ObjectNull.isNull(queryPageDto.getFieldList())) {
            List<String> fieldKeys = dataFieldService.getFieldKeys(null, dataModelId);
            queryPageDto.setFieldList(fieldKeys);
        }
        List<Map<String, Object>> data = queryList(dataModelId, criteria, queryPageDto.getFieldList());
        // 替换显示字段的值
        replaceSourceFieldData(queryPageDto.getSourceFieldId(), queryPageDto.getFieldList(), data);
        return data;
    }

    @Override
    public void replaceSourceFieldDataMap(String appId, String dataModelId, Map<String, Object> data) {
        List<FieldBasicsHtml> collect = dataFieldService.getAllField(appId, dataModelId).stream().collect(Collectors.toList());
        echo(data, collect, true);
        // 扩展返回数据
        //判断Data中哪些是key带有_1的，通过_1进行分离并替换数据
        List<String> set = new ArrayList<>();
        data.keySet().forEach(e -> {
            int i = e.indexOf(DynamicDataUtils.SUFFIX_ECHO);
            if (i > 0) {
                data.put(e.substring(0, i), data.get(e));
                set.add(e);
            }
        });
        set.forEach(data::remove);
    }

    @Override
    public void replaceSourceFieldData(String appId, String dataModelId, List<String> fieldList, List<Map<String, Object>> data) {
        if (ObjectNull.isNull(fieldList) || ObjectNull.isNull(data)) {
            return;
        }
        List<FieldBasicsHtml> collect = dataFieldService.getAllField(appId, dataModelId).stream().filter(e -> fieldList.contains(e.getFieldKey())).collect(Collectors.toList());
        for (Map<String, Object> datum : data) {
            echo(datum, collect, true);
        }
    }

    @Override
    public void replaceSourceFieldData(String appId, String dataModelId, List<String> fieldList, Map<String, Object> data) {
        List<FieldBasicsHtml> collect = dataFieldService.getAllField(appId, dataModelId).stream().filter(e -> fieldList.contains(e.getFieldKey())).collect(Collectors.toList());
        echo(data, collect, true);
    }

    @Override
    public void enableDataItem(List<Map<String, Object>> dataList, List<List<QueryConditionDto>> disableConditions) {
        if (ObjectNull.isNull(dataList) || ObjectNull.isNull(disableConditions)) {
            return;
        }
        // 条件组之间关系为‘或’，组内条件关系为‘且’
        // 所有组条件都不满足，则标记为禁用
        dataList.forEach(data -> {
            // true-满足条件，false-不满足条件
            boolean pass = false;
            for (List<QueryConditionDto> disableConditionGroup : disableConditions) {
                // 组内条件是否全部满足：true-满足所有条件，false-不满足所有条件
                // 只要有一个条件不满足，就不需要再校验当前组的后续条件了
                boolean groupResult = true;
                for (QueryConditionDto queryCondition : disableConditionGroup) {
                    if (Boolean.FALSE.equals(groupResult)) {
                        break;
                    }
                    Object dataValue = data.get(queryCondition.getFieldKey());
                    Object conditionValue = queryCondition.getValue();
                    DataQueryType queryType = queryCondition.getEnabledQueryTypes();
                    // 条件判断
                    if (DataQueryType.isNull.equals(queryType)) {
                        groupResult = ObjectNull.isNull(dataValue);
                        continue;
                    }
                    // 条件值对比
                    if (ObjectNull.isNull(conditionValue) || ObjectNull.isNull(dataValue)) {
                        groupResult = Boolean.FALSE;
                        continue;
                    }
                    switch (queryCondition.getEnabledQueryTypes()) {
                        case eq:
                            groupResult = conditionValue.equals(dataValue);
                            break;
                        case like:
                            groupResult = String.valueOf(dataValue).contains(conditionValue.toString());
                            break;
                        case in:
                            List<Object> conditionValues = new ArrayList<>();
                            if (conditionValue instanceof List) {
                                conditionValues.addAll((List) conditionValue);
                            } else {
                                if (JSONUtil.isTypeJSONObject(conditionValue.toString())) {
                                    if (JSONUtil.isTypeJSONArray(conditionValue.toString())) {
                                        conditionValues = JSONArray.parseArray(JSONObject.toJSONString(conditionValue));
                                    }
                                } else {
                                    conditionValues.add(conditionValues);
                                }
                            }
                            groupResult = conditionValues.stream().anyMatch(condValue -> condValue.equals(dataValue));
                            break;
                        default:
                            log.info("不支持的查询类型");
                            break;
                    }
                }
                // 只要有一个满足条件，则结束判断
                pass = groupResult;
                if (pass) {
                    break;
                }
            }

            // 添加一个固定的字段，标识数据项是否禁用
            data.put("jvsDisableItem", Boolean.FALSE.equals(pass));
        });

    }

    /**
     * 替换显示字段的值
     *
     * @param sourceFieldId 用以替换的显示字段id
     * @param fieldList     查询结果的字段
     * @param data          数据
     */
    private void replaceSourceFieldData(String sourceFieldId, List<String> fieldList, List<Map<String, Object>> data) {
        // 约定fieldList的第一个值为需要替换的字段key
        if (ObjectNull.isNull(fieldList)) {
            return;
        }
        String fieldKey = fieldList.get(0);
        replaceSourceFieldData(sourceFieldId, fieldKey, data);
    }

    /**
     * 删除流程数据
     *
     * @param datas 数据集合
     */
    private void removeTask(List<Map> datas) {
        List<String> dataIds = datas.stream()
                // 筛选出启动过流程，且数据id不为空的数据
                .filter(data -> ObjectNull.isNotNull(data.get(FlowDataFieldEnum.TASK_PROGRESS.getFieldKey())) && ObjectNull.isNotNull(data.get("dataId"))).map(data -> String.valueOf(data.get("dataId"))).collect(Collectors.toList());
        if (ObjectNull.isNull(dataIds)) {
            return;
        }
        flowTaskService.removeTaskAllByDataId(dataIds);
    }

    /**
     * 聚合mongo指定设计地址
     *
     * @param queryConditions 组件的查询条件
     * @param collectionName  集合对象
     * @param groupBy         分组数据信息
     * @param type            类型，分组，求合，平均
     * @param aggregateField  聚合字段
     * @return
     */
    public List<Map> aggregate(List<QueryConditionDto> queryConditions, String collectionName, AggregateEnumType type, String groupBy, String aggregateField) {
        List conditions = Collections.singletonList(queryConditions);
        List<Criteria> list = DynamicDataUtils.buildDynamicGroupCriteriaList(conditions);
        List<Criteria> authCriteria = DynamicDataUtils.getAuthCriteria();
        authCriteria.addAll(list);
        Criteria criteria = DynamicDataUtils.trueCriteria().andOperator(authCriteria);
        return aggregate(criteria, collectionName, type, groupBy, aggregateField);
    }

    @Override
    public List<Map> aggregate(Criteria criteria, String collectionName, AggregateEnumType type, String groupBy, String aggregateField) {

        //拼装模型的数据权限
        List<AggregationOperation> operations = new ArrayList<>();
        //根据数据权限和查询条件过滤数据
        operations.add(Aggregation.match(criteria));
        GroupOperation groupOperation = ObjectNull.isNotNull(groupBy) ? Aggregation.group(groupBy) : Aggregation.group();

        //根据分组进行处理，是按计数，还是平均，还是求合
        switch (type) {
            case count:
                operations.add(groupOperation.count().as("value"));
                break;
            case ave:
                operations.add(groupOperation.avg(aggregateField).as("value"));
                break;
            case sum:
                operations.add(groupOperation.sum(aggregateField).as("value"));
        }
        Aggregation aggregation = Aggregation.newAggregation(operations);
        List<Map> mappedResults = dataModelHandler.aggregate(aggregation, collectionName);
        mappedResults.forEach(e -> {
            e.put("name", e.get("_id"));
            e.remove(("_id"));
        });
        return mappedResults;
    }


    /**
     * 所有表单第一次初始化的时候，存在大量的下拉或多选，需要进行转换显示，用于优化数据展示速度
     *
     * @param designId 设计 id
     * @param body     表单的数据
     * @return
     */

    @Override
    public Map<String, Object> handler(String designId, Boolean init, ExecDto body) {
        //获取表单的设计
        if (ObjectNull.isNull(designId)) {
            return body.getParams();
        }
        FormPo formPo = formMapper.selectById(designId);
        if (ObjectNull.isNull(formPo)) {
            return body.getParams();
        }
        DynamicDataUtils.dataModelScope(formPo.getDataModelId());
        // 扩展返回数据
        //获取所有的字段
        Map<String, FieldBasicsHtml> fieldsMap =
                dataFieldService.getFields(formPo.getJvsAppId(), formPo.getDataModelId(), designId, true, true)
                        .stream()
                        .filter(e -> ObjectNull.isNotNull(e.getType()))
                        .collect(Collectors.toMap(FieldBasicsHtml::getFieldKey, Function.identity()));
        Map<String, Object> params = body.getParams();
        return getStringObjectMap(init, body, params, fieldsMap, formPo);
    }

    @Override
    public Map<String, Object> getStringObjectMap(Boolean init, ExecDto body, Map<String, Object> params, Map<String, FieldBasicsHtml> fieldsMap, FormPo formPo) {
        //将转换后的数据进行返回给公式
        //表示用户第一次打开表单渲染
        params = echo(params, fieldsMap, false);
        if (init) {
            return params;
        } else {
            //判断是否是表格操作。如果不是表格操作，直接返回
            if (ObjectNull.isNull(body.getIndex())) {
                return params;
            }
            FormDesignHtml formDesignHtml = DesignUtils.parseForm(formPo.getViewJson());
            if (ObjectNull.isNotNull(formDesignHtml.getTablePath())) {
                //返回的数据需要对其进行优化数据处理,避免返回的数据量大导致前端渲染卡顿
                //需要先做转换，再做删除，此处不能直接拿转换的数据进行处理
                //获取公式触发表格操作的这一行数据

                TableType tableType = SystemThreadLocal.get("tableType");
                if (ObjectNull.isNull(tableType)) {
                    return params;
                }
                if (ObjectNull.isNull(body.getParentKey())) {
                    //如果上级为空，则使用触发的值
                    ArrayList<String> parentKey = new ArrayList<String>();
                    parentKey.add(body.getModifiedField());
                    body.setParentKey(parentKey);
                }
                String collect = String.join(".", body.getParentKey());
                Object tableLineList = null;
                //如果是删除只删除，直接返回
                if (add.equals(tableType) || line.equals(tableType)) {
                    String path = collect + "[" + SystemThreadLocal.get("index") + "]";
                    tableLineList = JvsJsonPath.read(params, path);
                }
                Object read = JvsJsonPath.read(params, collect);
                //删除和，行级操作。都直接操作。
                for (String tablePath : formDesignHtml.getTablePath()) {
                    //删除表格的数据
                    if (tablePath.equals(collect)) {
                        JSONPath.remove(params, tablePath);
                    }
                    //处理选择卡里面的数据
                    if (!tablePath.contains(".")) {
                        JSONPath.remove(params, tablePath);
                    }
                }
                if (ObjectNull.isNotNull(tableLineList)) {
                    //如果是表格内部操作，只返回某一行的数据，其它行级数据，不返回。 其它的表格数据不返回。
                    //如果是表格添加操作，只返回这个表格新增的那一条数据 。 其它的表格数据不返回。
                    //获取设置的路径
                    JSONPath.set(params, collect + "[0]", tableLineList);
                    if (ObjectNull.isNotNull(read) && read instanceof List) {
                        if (((List) read).size() == 1) {
                            //如果只有一行数据时，则需要进行多返回一个字段，用于前端展示效果
                            JSONPath.set(params, collect + "_line[0]", tableLineList);
                        } else if (collect.contains(".")) {
                            //处理选择卡里面的数据
                            JSONPath.set(params, collect + "_line[0]", tableLineList);
                        }
                        JSONPath.set(params, collect + "_line[0]", tableLineList);
                    }
                }
            }
            //直接返回数据
            return params;
        }
    }

    @Override
    public void ocr(HttpServletResponse response, String appId, String dataModelId, String field, String name, String tempFileName) {
        generate(response, appId, dataModelId, field, name, tempFileName);
    }

    public void generate(HttpServletResponse response, String appId, String dataModelId, String field, String name, String tempFileName) {
        File zipFile = new File("ocr识别" + ".zip");
//        File tempDir = new File("tempDir" + RandomUtil.randomString(8));
        File tempDir = new File(tempFileName);
        File fileDir = new File(tempDir, "allPdf");
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }
        if (!fileDir.exists()) {
            // 创建子文件夹
            fileDir.mkdir();
        }
        String ocrUrl = ocrConfig.getOcrUrl();
        String url = ocrUrl + "/api/tr-run/";
        String[] split = name.split("-");

        try {
            List<String> fieldKeys = dataFieldService.getFieldKeys(appId, dataModelId);
            Criteria criteria = new Criteria();
//            List<Map<String, Object>> dataMap = queryList(dataModelId, DynamicDataUtils.initCriteria(criteria), fieldKeys, 10);
            List<Map<String, Object>> dataMap = queryList(dataModelId, fieldKeys);
            List<Map<String, Object>> collect = dataMap.stream().filter(e -> {
                Object read = JvsJsonPath.read(e, field);
                return ObjectNull.isNotNull(read) && (read instanceof List);
            }).map(e -> {
                Object read = JvsJsonPath.read(e, field);
                Map<String, Object> map = new HashMap<>();
                map.put("fileList", read);
                List<String> fileConNames = new ArrayList<>();
                for (int i = 0; i < split.length; i++) {
                    Object value = JvsJsonPath.read(e, split[i]);
                    if (ObjectNull.isNotNull(value)) {
                        fileConNames.add(value.toString());
                    }
                }
                map.put("name", String.join("+", fileConNames));
                return map;
            }).collect(Collectors.toList());

            collect.parallelStream().forEach(entity -> {
                String dirName = entity.get("name").toString();
                List<String> pdfUrls = new ArrayList<>();
                Object fileListObj = entity.get("fileList");
                if (fileListObj instanceof List) {
                    List<BaseFile> files = BeanCopyUtil.copys((List) fileListObj, BaseFile.class);
                    files.stream()
                            .filter(e -> e.getFileName().toLowerCase().endsWith(".pdf"))
                            .forEach(file -> {
                                pdfUrls.add(ossTemplate.fileLink(file.getFileName(), file.getBucketName()));
                            });

                    List<String> resultString = new ArrayList<>();

                    int index = 0;
                    boolean pdfFlag = false;
                    for (String attr : pdfUrls) {
                        //将pdf转换成图片
                        byte[] pdfBytes = HttpUtil.downloadBytes(attr);
                        String pdfName = files.get(index).getName().replaceAll("/", "-").toLowerCase();
                        index++;
                        File pdfFile = new File(fileDir, pdfName);
                        File[] existFiles = fileDir.listFiles();
                        if (ObjectUtils.isNotEmpty(existFiles)) {
                            pdfFlag = Arrays.stream(existFiles).anyMatch(e -> pdfName.equals(e.getName()));
                        }

                        if (pdfFlag) {
                            continue;
                        }

                        try {
                            FileOutputStream fos = new FileOutputStream(pdfFile);
                            System.out.println("PDF文件名称：" + fileDir + "/" + pdfName);
                            fos.write(pdfBytes);

                            List<byte[]> imageBytes = Pdf2ImageUtil.pdfToImage(pdfBytes);
                            StringBuilder builder = new StringBuilder();
                            //ocr识别
                            imageBytes.forEach(e -> {
                                String encode = Base64.encode(e);
                                String body = HttpUtil.createPost(url).form("compress", 1600).form("img", encode).execute().body();
                                JSONArray objects = JSONObject.parseObject(body).getJSONObject("data").getJSONArray("raw_out");
                                objects.stream().map(x -> (JSONArray) (x)).map(x -> x.get(1)).forEach(x ->
                                {
                                    if (x instanceof String) {
                                        builder.append(x).append("\r\n");
                                    }
                                });
                            });

                            resultString.add(builder.toString());
                        } catch (Exception ignored) {
                        }
                    }

                    // 将resultString中的内容写入以数字命名的txt文件
                    boolean txtFlag = false;
                    for (int i = 0; i < resultString.size(); i++) {
                        String allFileName = files.get(i).getName();
                        String originFileName = allFileName.substring(0, allFileName.lastIndexOf("."));
                        String fileName = new String((dirName + "+" + originFileName).getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8).replace(" ", "").replace("/", "-");
                        File[] existFiles = tempDir.listFiles();
                        if (ObjectUtils.isNotEmpty(existFiles)) {
                            txtFlag = Arrays.stream(existFiles).anyMatch(e -> (fileName + ".txt").equals(e.getName()));
                        }
                        if (txtFlag) {
                            continue;
                        }
                        String content = resultString.get(i);
                        File txtFile = new File(tempDir, fileName + ".txt");
                        try {
                            FileUtils.writeStringToFile(txtFile, content, "UTF-8");
                        } catch (IOException x) {
                            throw new BusinessException("写入txt文件失败");
                        }
                    }
                }

            });
            try {
                FileOutputStream fos = new FileOutputStream(zipFile);
                ZipOutputStream zos = new ZipOutputStream(fos);
                zipDirectory(tempDir, tempDir.getName(), zos);
                zos.close();
                fos.close();
            } catch (IOException e) {
                throw new BusinessException("压缩失败");
            }

            IOUtils.copy(Files.newInputStream(zipFile.toPath()), response.getOutputStream());
            response.flushBuffer();
//            outputStream.write(FileUtil.readBytes(zipFile));
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(URLUtil.encode("ocr", StandardCharsets.UTF_8)));
            response.setStatus(HttpStatus.OK.value());

        } catch (Exception e) {
            throw new BusinessException("处理失败");
        } finally {
            FileUtil.del(tempDir);
            FileUtil.del(zipFile);
        }
    }

    private void zipDirectory(File directory, String baseName, ZipOutputStream zos) throws IOException {
        File[] files = directory.listFiles();
        byte[] buffer = new byte[1024];
        int length;

        for (File file : files) {
            if (file.isDirectory()) {
                zipDirectory(file, baseName + File.separator + file.getName(), zos);
            } else {
                FileInputStream fis = new FileInputStream(file);
                BufferedInputStream bis = new BufferedInputStream(fis);
                zos.putNextEntry(new ZipEntry(baseName + File.separator + file.getName()));
                while ((length = bis.read(buffer)) > 0) {
                    zos.write(buffer, 0, length);
                }
                bis.close();
                fis.close();
            }
        }
    }

    @Override
    public void handleEncryptCover(String modelId, String dataId, Map<String, Object> oldData, Map<String, Object> newData) {
        DataModelPo model = dataModelService.getModel(modelId);
        Optional<Boolean> encryption = Optional.ofNullable(model)
                .map(DataModelPo::getSetting)
                .map(DataSettingBo::getEncryption);
        if (encryption.isPresent() && model.getSetting().getEncryption()) {
            Map<String, Object> oldDataCopy = new HashMap<>();
            if (ObjectNull.isNotNull(oldData)) {
                oldDataCopy = new HashMap<>(oldData);
            }
            if (ObjectNull.isNull(oldDataCopy)) {
                oldDataCopy = getFreePermissionMap(modelId, dataId);
            }
            //判断加密字段
            Map<String, Object> objectMap = new HashMap<>(model.getSetting().getEncryptionFields().size());
            for (EncryptionFieldsPo e : model.getSetting().getEncryptionFields()) {
                try {
                    String o = String.valueOf(oldDataCopy.getOrDefault(e.getFieldKey(), ""));
                    String desensitized = SensitiveInfoUtils.getSensitiveKey().get(e.getEncryptionExpress()).apply(o);
                    objectMap.put(e.getFieldKey(), desensitized);
                } catch (Exception exception) {
                }
            }
            //判断脱敏数据 和原始字段是否一致，如果和加密的结果一致则删除历史的 key
            for (String e : objectMap.keySet()) {
                if (objectMap.get(e).equals(newData.get(e))) {
                    // 若加密结果一致，则设置原始值
                    newData.put(e, String.valueOf(oldDataCopy.getOrDefault(e, "")));
                }
            }

        }
    }

    private void handleSubTableCondition(String modelId, List<List<FilterHtml>> dataFilterGroupList, Map<String, Object> data, List<Map<String, Object>> newData) {
        List<List<QueryConditionDto>> queryConditionDtos = dataFilterGroupList.stream().map(filterGroup -> filterGroup.stream().map(s -> {
            try {
                TypeHtml type = s.getType();
                QueryConditionDto queryConditionDto = new QueryConditionDto().setEnabledQueryTypes(s.getEnabledQueryTypes()).setFieldKey(s.getFieldKey());
                switch (type) {
                    case value:
                        return queryConditionDto.setValue(s.getValue());
                    case prop:
                        //获取字段路径
                        String path = s.getValue().toString();
                        if (JSONUtil.isTypeJSONArray(s.getValue().toString())) {
                            path = ((JSONArray) s.getValue()).stream().map(String::valueOf).collect(Collectors.joining("."));
                        }
                        Object read = JvsJsonPath.read(JSONUtil.toJsonStr(data), path);
                        return queryConditionDto.setValue(read);
                    default:
                        return null;
                }
            } catch (Exception exception) {
                return null;
            }
        }).filter(ObjectNull::isNotNull).collect(Collectors.toList())).collect(Collectors.toList());
        Criteria criteria = DynamicDataUtils.buildDynamicGroupCriteria(queryConditionDtos);
        criteria = DynamicDataUtils.initCriteria(criteria);
        Map<String, Object> maps = queryList(modelId, criteria, new ArrayList<>(Arrays.asList("dataId", "id")))
                .stream()
                .filter(e -> ObjectNull.isNotNull(e.get("dataId")))
                .collect(Collectors.toMap(k -> k.get("dataId").toString(),
                        Function.identity()));
        List<Map<String, Object>> update = newData.stream().filter(e -> {
            String dataId = e.getOrDefault("id", "").toString();
            if (dataId.isEmpty()) {
                dataId = e.getOrDefault("dataId", "").toString();
            }
            if (ObjectNull.isNotNull(dataId)) {
                Object o = maps.get(dataId);
                return ObjectNull.isNotNull(o);
            }
            //没有则默认为新增
            return true;
        }).collect(Collectors.toList());
        newData.clear();
        newData.addAll(update);

    }

    /**
     * 执行动态聚合查询，结合查询条件
     *
     * @param modelId 模型id
     * @param query   分组聚合查询条件
     * @return 聚合结果
     */
    public List<Map> executeAggregation(String modelId, PageAggregationQuery query) {
        if (ObjectNull.isNull(query.getAggregationFields())) {
            return new ArrayList<>();
        }

        List<AggregationOperation> operations = new ArrayList<>();
        // 查询条件 $match 阶段
        if (ObjectNull.isNotNull(query.getCriteria())) {
            MatchOperation matchOperation = Aggregation.match(query.getCriteria());
            operations.add(matchOperation);
        }

        // 构建分组操作 $group
        GroupOperation groupOperation =
                buildGroupOperation(query.getAggregationFields(), query.getAggregationHandleFieldsMap(), true);
        operations.add(groupOperation);

        // 构建排序 $sort
        if (ObjectNull.isNotNull(query.getSort())) {
            //去除未被聚合字段的排序设置
            List<Sort.Order> sortSettings = query.getSort().stream()
                    .filter(e -> query.getAggregationFields().contains(e.getProperty()))
                    .collect(Collectors.toList());
            if(!sortSettings.isEmpty()){
                SortOperation sortOperation = Aggregation.sort(Sort.by(sortSettings));
                operations.add(sortOperation);
            }
        }

        // 构建跳过 $skip
        if (ObjectNull.isNotNull(query.getSkip())) {
            SkipOperation skipOperation = Aggregation.skip(query.getSkip());
            operations.add(skipOperation);
        }

        //构建限制数量 $limit
        if (ObjectNull.isNotNull(query.getLimit())) {
            LimitOperation limitOperation = Aggregation.limit(query.getLimit());
            operations.add(limitOperation);
        }


        // $project ，处理聚合字段和排除字段查询
        ProjectionOperation projectionOperation = Aggregation.project()
                // 包含聚合字段
//                .andInclude(query.getAggregationFields().toArray(new String[0]))
                // 排除 _id 字段
                .andExclude(MONGO_ID);
        operations.add(projectionOperation);

        // 构建聚合管道
        Aggregation aggregation = Aggregation.newAggregation(operations);


        // 执行聚合查询
        return dataModelHandler.aggregate(aggregation, modelId);
    }


    /**
     * @param modelId 模型id
     * @param query   聚合查询条件
     * @return long
     */
    private long countAggregation(String modelId, PageAggregationQuery query) {
        if (ObjectNull.isNull(query.getAggregationFields())) {
            return 0;
        }

        List<AggregationOperation> operations = new ArrayList<>();
        // 查询条件 $match 阶段
        if (ObjectNull.isNotNull(query.getCriteria())) {
            MatchOperation matchOperation = Aggregation.match(query.getCriteria());
            operations.add(matchOperation);
        }

        // 构建分组操作 $group
        GroupOperation groupOperation = buildGroupOperation(query.getAggregationFields(), query.getAggregationHandleFieldsMap(), false);
        operations.add(groupOperation);

        //构建计数操作 $count
        CountOperation totalGroupCount = Aggregation.count().as("totalGroupCount");
        operations.add(totalGroupCount);

        // 构建聚合管道
        Aggregation aggregation = Aggregation.newAggregation(operations);
        List<Map> aggregate = dataModelHandler.aggregate(aggregation, modelId);
        if (!aggregate.isEmpty()) {
            return Long.parseLong(aggregate.get(0).get("totalGroupCount").toString());
        }
        return 0;
    }

    /**
     * 聚合操作-动态构建分组操作
     *
     * @param aggregationFields          分组字段集合
     * @param aggregationHandleFieldsMap 聚合操作配置
     * @param attr                       是否包含分组属性
     * @return GroupOperation 分组聚合操作
     */
    private GroupOperation buildGroupOperation(List<String> aggregationFields, Map<String, AggregationTypeEnum> aggregationHandleFieldsMap, Boolean attr) {
        // 初始化分组操作
        GroupOperation groupOperation = Aggregation.group(aggregationFields.toArray(new String[0]));

        if (attr) {
            // 保留每个分组字段
            for (String field : aggregationFields) {
                groupOperation = groupOperation.first(field).as(field);
            }
        }

        // 遍历聚合操作配置，动态添加聚合操作
        if (ObjectNull.isNotNull(aggregationHandleFieldsMap)) {
            for (Map.Entry<String, AggregationTypeEnum> entry : aggregationHandleFieldsMap.entrySet()) {
                String field = entry.getKey();
                AggregationTypeEnum type = entry.getValue();

                switch (type) {
                    case SUM:
                        groupOperation = groupOperation.sum(field).as(field);
                        break;
                    case COUNT:
                        groupOperation = groupOperation.count().as(field);
                        break;
                    case AVERAGE:
                        groupOperation = groupOperation.avg(field).as(field);
                        break;
                    case MAX:
                        groupOperation = groupOperation.max(field).as(field);
                        break;
                    case MIN:
                        groupOperation = groupOperation.min(field).as(field);
                        break;
                    default:
                        break;
                }
            }
        }


        return groupOperation;
    }
}