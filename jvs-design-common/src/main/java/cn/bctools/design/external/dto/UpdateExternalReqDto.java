package cn.bctools.design.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Author: <PERSON><PERSON>iaoKang
 * @Description: 修改外部页面配置入参
 */
@Data
@Accessors(chain = true)
@ApiModel("修改外部页面配置入参")
public class UpdateExternalReqDto extends CreateExternalReqDto {

    @ApiModelProperty(value = "id", required = true)
    @NotBlank(message = "id不能为空")
    private String id;
}
