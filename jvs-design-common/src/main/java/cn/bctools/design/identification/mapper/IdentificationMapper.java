package cn.bctools.design.identification.mapper;

import cn.bctools.database.interceptor.cache.JvsRedisCache;
import cn.bctools.design.identification.entity.Identification;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 */
@Mapper
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
public interface IdentificationMapper extends BaseMapper<Identification> {
}
