package cn.bctools.design.identification.service;

import cn.bctools.design.identification.entity.Identification;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 自定义设计标识
 * <p>
 * 自定义页面开发，可根据自定义标识符访问设计|数据；
 * 租户级全局唯一标识
 */
public interface IdentificationService extends IService<Identification> {


    /**
     * 租户下是否已存在指定标识符
     *
     * @param identifier 标识符
     */
    void checkExistsIdentifier(String identifier);

    /**
     * 租户下是否已存在指定标识符
     *
     * @param identifiers 标识符集合
     * @return true-有标识已存在，false-所有标识都不存在
     */
    boolean checkExistsIdentifier(List<String> identifiers);

    /**
     * 根据标识符查询标识
     *
     * @param identifier 标识符
     * @return 标识
     */
    Identification getByIdentifier(String identifier);

    /**
     * 修改设计名称
     *
     * @param designId   设计id
     * @param designName 设计名称
     */
    void updateDesignName(String designId, String designName);

    /**
     * 获取逻辑的标识，匹配url地址
     *
     * @param ruleIdentification
     * @param appId
     * @param map
     * @return
     */
    Identification getByIdentifierRule(String ruleIdentification, String appId, Map<String, Object> map);

    /**
     * 获取应用标识
     *
     * @param appIdentification
     * @return
     */
    Identification getByIdentifierApp(String appIdentification);

    /**
     * 获取当前模式下所有的标识,不同模式下标识是不一样的
     *
     * @param identifiers 所有的标识
     * @return the identification model
     */
    List<Identification> getIdentificationModel(String... identifiers);
}