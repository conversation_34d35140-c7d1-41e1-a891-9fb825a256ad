package cn.bctools.design.identification.service.impl;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.data.fields.enums.DesignType;
import cn.bctools.design.identification.entity.Identification;
import cn.bctools.design.identification.mapper.IdentificationMapper;
import cn.bctools.design.identification.service.IdentificationService;
import cn.bctools.design.project.entity.JvsApp;
import cn.bctools.design.project.service.JvsAppService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class IdentificationServiceImpl extends ServiceImpl<IdentificationMapper, Identification> implements IdentificationService {

    private final JvsAppService appService;
    private static final PathMatcher PATH_MATCHER = new AntPathMatcher();

    @Override
    public void checkExistsIdentifier(String identifier) {
        Identification identification = getByIdentifier(identifier);
        if (ObjectNull.isNull(identification)) {
            return;
        }
        JvsApp jvsApp = appService.getAppById(identification.getJvsAppId());
        throw new BusinessException("该标识符在应用[{}]中已存在", jvsApp.getName());
    }

    @Override
    public boolean checkExistsIdentifier(List<String> identifiers) {
        return getBaseMapper().exists(Wrappers.<Identification>lambdaQuery().in(Identification::getIdentifier, identifiers));
    }

    @Override
    public Identification getByIdentifier(String identifier) {
        return getOne(Wrappers.<Identification>lambdaQuery().eq(Identification::getIdentifier, identifier));
    }

    @Override
    public void updateDesignName(String designId, String designName) {
        if (ObjectNull.isNull(designId)) {
            return;
        }
        update(Wrappers.<Identification>lambdaUpdate()
                .set(Identification::getDesignName, designName)
                .eq(Identification::getDesignId, designId));
    }

    @Override
    public Identification getByIdentifierRule(String ruleIdentification, String appId, Map<String, Object> map) {
        List<Identification> list = list(Wrappers.query(new Identification().setDesignType(DesignType.rule).setJvsAppId(appId)));
        for (Identification e : list) {
            if (PATH_MATCHER.match(e.getIdentifier(), ruleIdentification)) {
                //将数据替换到参数中
                map.putAll(PATH_MATCHER.extractUriTemplateVariables(e.getIdentifier(), ruleIdentification));
                return e;
            }
        }
        return null;
    }

    /**
     * 如果查询出来是多个，只取第一个
     *
     * @param appIdentification
     * @return
     */
    @Override
    public Identification getByIdentifierApp(String appIdentification) {
        return getOne(Wrappers.query(new Identification().setIdentifier(appIdentification)));
    }
}
