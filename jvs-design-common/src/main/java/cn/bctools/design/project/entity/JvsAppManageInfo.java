package cn.bctools.design.project.entity;

import cn.bctools.database.entity.po.BasePo;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("应用后台管理")
@EqualsAndHashCode(callSuper = false)
@TableName(value = "jvs_app_manage_info", autoResultMap = true)
public class JvsAppManageInfo extends BasePo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    @ApiModelProperty("应用id")
    private String appId;
    @ApiModelProperty("注册审核状态(false:手动审核,true:自动审核)")
    private Boolean registerAuditStatus;
    @ApiModelProperty("注册显示状态(false:不显示,true:显示)")
    private Boolean registerShowStatus;
    @ApiModelProperty("排序")
    private Integer sort = 0;
    @TableLogic
    @ApiModelProperty("逻辑删除")
    private Boolean delFlag;
}
