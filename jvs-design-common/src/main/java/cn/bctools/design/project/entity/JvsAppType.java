package cn.bctools.design.project.entity;

import cn.bctools.database.entity.po.BasePo;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("应用分类")
@EqualsAndHashCode(callSuper = false)
@TableName(value = "jvs_app_type", autoResultMap = true)
public class JvsAppType extends BasePo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    @ApiModelProperty("应用分类名称")
    @NotEmpty(message = "应用分类名称不能为空")
    private String name;
    @ApiModelProperty("图标")
    private String logo;
    @ApiModelProperty("描述")
    private String description;
    @TableLogic
    @ApiModelProperty("逻辑删除")
    private Boolean delFlag;
}
