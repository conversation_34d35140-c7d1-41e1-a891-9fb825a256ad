package cn.bctools.design.project.entity.vo;

import cn.bctools.design.project.dto.AppDto;
import cn.bctools.design.project.entity.JvsAppType;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @date 2024/06/19
 */
@Accessors(chain = true)
@Data
public class AppTypeListRtnVo {

    @ApiModelProperty("应用列表")
    private Page<AppDto> appPage;

    @ApiModelProperty("应用分类信息")
    private JvsAppType typeInfo;
}
