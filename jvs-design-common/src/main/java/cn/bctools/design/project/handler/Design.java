package cn.bctools.design.project.handler;

import cn.bctools.design.data.fields.enums.DesignType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 设计类型
 *
 * @Author: GuoZi
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface Design {

    /**
     * 标记设计类型
     */
    DesignType value();

}
