package cn.bctools.design.project.service;

import cn.bctools.design.project.dto.ProgressDto;
import cn.bctools.design.project.entity.JvsAppTemplate;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR> Generator
 */
public interface JvsAppTemplateService extends IService<JvsAppTemplate> {

    /**
     * 根据前端用户选择的无租户的模板ID  创建应用
     * 应用发布到模板，模板是无租户的，方便一些数据的演示
     *
     * @param templateId 模板id
     * @return 应用数据
     */
    ProgressDto create(String templateId);

    /**
     * 根据模板创建应用
     *
     * @param jvsAppTemplate 应用ID
     * @return 应用数据
     */
    String getDesignData(JvsAppTemplate jvsAppTemplate);

    /**
     * 根据模板ID获取数据集
     *
     * @param templateId
     * @return
     */
    String getData(String templateId);

    /**
     * 更新应用
     *
     * @param templateId 模板id
     * @return 应用数据
     */
    ProgressDto update(String templateId);

}
