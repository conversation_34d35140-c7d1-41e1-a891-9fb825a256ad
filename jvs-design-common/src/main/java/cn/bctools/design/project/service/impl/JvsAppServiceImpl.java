package cn.bctools.design.project.service.impl;

import cn.bctools.auth.api.dto.PersonnelDto;
import cn.bctools.auth.api.enums.PersonnelTypeEnum;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.entity.vo.IdsVo;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.function.Get;
import cn.bctools.database.util.SqlFunctionUtil;
import cn.bctools.design.menu.component.AppMenuHandler;
import cn.bctools.design.menu.entity.AppMenuType;
import cn.bctools.design.menu.service.AppMenuTypeService;
import cn.bctools.design.project.dto.DesignRoleSettingDto;
import cn.bctools.design.project.entity.JvsApp;
import cn.bctools.design.project.entity.JvsAppManageInfo;
import cn.bctools.design.project.entity.vo.*;
import cn.bctools.design.project.mapper.JvsAppMapper;
import cn.bctools.design.project.service.JvsAppManageInfoService;
import cn.bctools.design.project.service.JvsAppService;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.web.utils.WebUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.fop.fonts.type1.AdobeStandardEncoding.e;

/**
 * <AUTHOR> Generator
 */
@Service
@Slf4j
public class JvsAppServiceImpl extends ServiceImpl<JvsAppMapper, JvsApp> implements JvsAppService {

    @Autowired
    AppMenuTypeService appMenuTypeService;
    @Autowired
    AppMenuHandler appMenuHandler;
    @Autowired
    JvsAppManageInfoService manageInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deploy(String appId) {
        JvsApp jvsApp = this.get(appId);
        DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(appId).setJvsAppName(jvsApp.getName()));
        return this.update(Wrappers.<JvsApp>lambdaUpdate().set(JvsApp::getIsDeploy, true).eq(JvsApp::getId, appId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unload(String appId) {
        this.get(appId);
        return this.update(Wrappers.<JvsApp>lambdaUpdate().set(JvsApp::getIsDeploy, false).eq(JvsApp::getId, appId));
    }

    @Override
    public boolean existType(String appId, String type) {
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(type)) {
            return false;
        }
        JvsApp app = this.getOne(Wrappers.<JvsApp>lambdaQuery().select(JvsApp::getId).eq(JvsApp::getId, appId));
        if (Objects.isNull(app)) {
            throw new BusinessException("该应用不存在");
        }
        AppMenuType appMenuType = appMenuTypeService.getOne(Wrappers.<AppMenuType>lambdaQuery().eq(AppMenuType::getJvsAppId, app.getId()).eq(AppMenuType::getId, type));
        return ObjectNull.isNotNull(appMenuType);
    }

    @Override
    public void addType(String appId, String type, String icon, String parentId) {
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(type)) {
            return;
        }
        JvsApp app = this.getOne(Wrappers.<JvsApp>lambdaQuery().select(JvsApp::getId).eq(JvsApp::getId, appId));
        if (Objects.isNull(app)) {
            throw new BusinessException("该应用不存在");
        }
        appMenuHandler.addType(appId, type, icon, parentId);
    }

    @Override
    public void removeType(String appId, String typeId) {
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(typeId)) {
            return;
        }
        JvsApp app = this.getOne(Wrappers.<JvsApp>lambdaQuery().select(JvsApp::getId).eq(JvsApp::getId, appId));
        if (Objects.isNull(app)) {
            throw new BusinessException("该应用不存在");
        }
        appMenuTypeService.removeType(appId, typeId);
    }

    @Override
    public void updateType(String id, String appId, String oldType, String newType, String icon, String parentId) {
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(oldType) || StringUtils.isBlank(newType)) {
            return;
        }
        JvsApp app = this.getOne(Wrappers.<JvsApp>lambdaQuery().select(JvsApp::getId).eq(JvsApp::getId, appId));
        if (Objects.isNull(app)) {
            throw new BusinessException("该应用不存在");
        }

        appMenuTypeService.updateType(id, appId, oldType, newType, icon, parentId);
    }

    /**
     * 获取应用信息, 并校验是否存在
     *
     * @param appId 应用id
     * @return 应用信息
     */
    private JvsApp get(String appId) {
        if (StringUtils.isBlank(appId)) {
            throw new BusinessException("应用id为空");
        }
        JvsApp app = this.getById(appId);
        if (Objects.isNull(app)) {
            throw new BusinessException("该应用不存在");
        }
        return app;
    }

    @Override
    public JvsApp getAppById(String appId) {
        return get(appId);
    }

    @Override
    public Boolean checkRole(List<PersonnelDto> personnels, UserDto userDto) {
        return Optional.ofNullable(personnels).orElseGet(ArrayList::new)
                .stream()
                .anyMatch(role -> PersonnelTypeEnum.user.equals(role.getType()) && userDto.getId().equals(role.getId()));
    }

    @Override
    public Boolean checkViewStatus(List<PersonnelDto> personnels, UserDto userDto) {
        return Optional.ofNullable(personnels).orElseGet(ArrayList::new)
                .stream()
                .anyMatch(role -> PersonnelTypeEnum.role.equals(role.getType()) && userDto.getRoleIds().contains(role.getId()));
    }

    @Override
    public List<JvsApp> getRegisterAppList(RegisterAppListReqVo reqVo) {
        List<String> appIds = manageInfoService.list(new LambdaQueryWrapper<JvsAppManageInfo>()
                        .eq("register".equals(reqVo.getType()), JvsAppManageInfo::getRegisterShowStatus, true)
                        .select(JvsAppManageInfo::getAppId)
                        .orderByAsc(JvsAppManageInfo::getSort)
                        .orderByDesc(JvsAppManageInfo::getCreateTime))
                .stream().map(JvsAppManageInfo::getAppId).collect(Collectors.toList());
        if (ObjectNull.isNull(appIds)) {
            return new ArrayList<>();
        }
        //填充应用名称
        String joinStr = appIds.stream().collect(Collectors.joining("', '", "'", "'"));
        return list(new LambdaQueryWrapper<JvsApp>()
                .eq(ObjectNull.isNotNull(reqVo.getAppId()), JvsApp::getId, reqVo.getAppId())
                .in(JvsApp::getId, appIds)
                .select(JvsApp::getId, JvsApp::getName)
                .last("ORDER BY FIELD(id, " + joinStr + ")"));
    }

    @Override
    public Boolean checkIdentification(CheckAppIdentificationVo vo) {
        if (ObjectNull.isNull(vo.getIdentification())) {
            return true;
        }
        long count = count(new LambdaQueryWrapper<JvsApp>()
                .ne(ObjectNull.isNotNull(vo.getAppId()), JvsApp::getId, vo.getAppId())
                .eq(ObjectNull.isNotNull(vo.getIdentification()), JvsApp::getIdentification, vo.getIdentification()));
        //true 无重复标识
        return count == 0;
    }

    @Override
    public Page<AppManageInfoVo> getManagePage(Page<AppManageInfoVo> page, AppManagePageReqVo vo) {
        List<String> appIds = new ArrayList<>();
        // 应用查询标识
        boolean searchFlag = false;
        LambdaQueryWrapper<JvsApp> jvsAppLambdaQueryWrapper = Wrappers.lambdaQuery(JvsApp.class);
        if (ObjectNull.isNotNull(vo.getPublishStatus())) {
            jvsAppLambdaQueryWrapper.eq(JvsApp::getIsDeploy, vo.getPublishStatus());
        }
        if (ObjectNull.isNotNull(vo.getKeyword())) {
            jvsAppLambdaQueryWrapper.and(e ->
                    e.apply(SqlFunctionUtil.jsonExtract(Get.name(JvsApp::getRole),
                                    "appMember") + " like {0}", "%" + vo.getKeyword() + "%")
                            .or()
                            .like(JvsApp::getName, vo.getKeyword()));
        }
        if (!jvsAppLambdaQueryWrapper.isEmptyOfNormal()) {
            searchFlag = true;
            appIds = list(jvsAppLambdaQueryWrapper).stream()
                    .map(JvsApp::getId).collect(Collectors.toList());
            if (ObjectNull.isNull(appIds)) {
                return page;
            }
        }
        LambdaQueryWrapper<JvsAppManageInfo> jvsAppManageInfoLambdaQueryWrapper = Wrappers.lambdaQuery(JvsAppManageInfo.class);
        jvsAppManageInfoLambdaQueryWrapper.orderByAsc(JvsAppManageInfo::getSort);
        jvsAppManageInfoLambdaQueryWrapper.orderByDesc(JvsAppManageInfo::getCreateTime);
        jvsAppManageInfoLambdaQueryWrapper.in(searchFlag, JvsAppManageInfo::getAppId, appIds);
        jvsAppManageInfoLambdaQueryWrapper.eq(ObjectNull.isNotNull(vo.getRegisterAuditStatus()), JvsAppManageInfo::getRegisterAuditStatus, vo.getRegisterAuditStatus());
        jvsAppManageInfoLambdaQueryWrapper.eq(ObjectNull.isNotNull(vo.getRegisterShowStatus()), JvsAppManageInfo::getRegisterShowStatus, vo.getRegisterShowStatus());
        Page<JvsAppManageInfo> jvsAppManageInfoPage = new Page<>(page.getCurrent(), page.getSize());
        manageInfoService.page(jvsAppManageInfoPage, jvsAppManageInfoLambdaQueryWrapper);
        if (ObjectNull.isNull(jvsAppManageInfoPage.getRecords())) {
            return page;
        }
        Set<String> appInfoIds = jvsAppManageInfoPage.getRecords().stream().map(JvsAppManageInfo::getAppId).collect(Collectors.toSet());
        Map<String, JvsApp> appInfoMap = list(new LambdaQueryWrapper<JvsApp>().in(JvsApp::getId, appInfoIds)
                .select(JvsApp::getId, JvsApp::getName, JvsApp::getIsDeploy, JvsApp::getRole, JvsApp::getIdentification))
                .stream().collect(Collectors.toMap(JvsApp::getId, Function.identity()));
        page.setTotal(jvsAppManageInfoPage.getTotal());
        page.setRecords(BeanCopyUtil.copys(jvsAppManageInfoPage.getRecords(), AppManageInfoVo.class));
        String referer = WebUtils.getRequest().getHeader("Referer");
        page.getRecords().parallelStream().forEach(e -> {
            JvsApp app = appInfoMap.get(e.getAppId());
            if (app == null) {
                return;
            }
            e.setAppLink(referer + "#/login?backlink=jvs/application/" + app.getId());
           fillManageAppInfo(e,app);
        });
        return page;

    }

    @Override
    public Boolean saveManage(AppManageSaveReqVo vo) {
        JvsAppManageInfo byId = manageInfoService.getById(vo.getAppId());
        JvsApp app = getById(vo.getAppId());
        if (ObjectNull.isNull(app)) {
            throw new BusinessException("应用不存在");
        }
        if (ObjectNull.isNull(byId)) {
            app.getRole().setAdminMember(vo.getAdminMember());
            updateById(app);
            return manageInfoService.save(new JvsAppManageInfo()
                    .setAppId(vo.getAppId())
                    .setRegisterAuditStatus(vo.getRegisterAuditStatus())
                    .setRegisterShowStatus(vo.getRegisterShowStatus())
                    .setSort(vo.getSort()));
        }
        return true;
    }

    @Override
    public Boolean delManage(IdsVo ids) {
        return manageInfoService.removeByIds(ids.getIds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editManage(AppManageEditReqVo vo) {
        JvsApp app = getById(vo.getAppId());
        if (ObjectNull.isNull(app)) {
            throw new BusinessException("应用不存在");
        }
        app.getRole().setAdminMember(vo.getAdminMember());
        updateById(app);

        return manageInfoService.update(new LambdaUpdateWrapper<JvsAppManageInfo>()
                .set(JvsAppManageInfo::getRegisterAuditStatus, vo.getRegisterAuditStatus())
                .set(JvsAppManageInfo::getRegisterShowStatus, vo.getRegisterShowStatus())
                .set(JvsAppManageInfo::getSort, vo.getSort())
                .set(JvsAppManageInfo::getUpdateBy, UserCurrentUtils.getRealName())
                .set(JvsAppManageInfo::getUpdateTime, LocalDateTime.now())
                .eq(JvsAppManageInfo::getId, vo.getId()));
    }

    @Override
    public AppManageInfoVo getManageInfo(String id) {
        JvsAppManageInfo one = manageInfoService.getOne(new LambdaQueryWrapper<JvsAppManageInfo>()
                .eq(JvsAppManageInfo::getAppId, id)
                .last("limit 1"));
        if (ObjectNull.isNotNull(one)) {
            JvsApp app = getById(id);
            if (ObjectNull.isNotNull(app)) {
                String referer = WebUtils.getRequest().getHeader("Referer");
                AppManageInfoVo rtn = BeanCopyUtil.copy(one, AppManageInfoVo.class);
                rtn.setAppLink(referer + "#/login?backlink=jvs/application/" + app.getId());
                fillManageAppInfo(rtn,app);
                return rtn;
            }
        }
        return null;
    }

    private Map<String, String> getHeaderMap() {
        HttpServletRequest request = WebUtils.getRequest();
        Enumeration<String> headerNames = request.getHeaderNames();
        Map<String, String> headerMap = new HashMap<>();
        while (headerNames.hasMoreElements()) {
            String key = headerNames.nextElement();
            String value = request.getHeader(key);
            headerMap.put(key, value);
        }
        return headerMap;
    }

    @Override
    public List<AppManageInfoVo> manageAppList() {
        List<String> existAppIds = manageInfoService.list().stream().map(JvsAppManageInfo::getAppId).collect(Collectors.toList());
        List<JvsApp> list = list(new LambdaQueryWrapper<JvsApp>().
                notIn(ObjectNull.isNotNull(existAppIds), JvsApp::getId, existAppIds));
        String referer = WebUtils.getRequest().getHeader("Referer");
        List<AppManageInfoVo> result = new ArrayList<>();
        list.forEach(e -> {
            AppManageInfoVo vo = new AppManageInfoVo();
            vo.setAppId(e.getId());
            vo.setAppName(e.getName());
            vo.setRegisterDeployStatus(e.getIsDeploy());
            vo.setAppLink(referer + "#/login?backlink=jvs/application/" + e.getId());
            vo.setAppIdentification(e.getIdentification());
            vo.setRole(e.getRole());
            result.add(vo);
        });
        return result;
    }

    private void fillManageAppInfo(AppManageInfoVo info,JvsApp app){
        info.setAppIdentification(app.getIdentification());
        info.setAppName(app.getName());
        info.setRegisterDeployStatus(app.getIsDeploy());
        info.setRole(app.getRole());
        info.setAppAccessRoles(Optional.ofNullable(app.getRole().getAppMember())
                .orElse(new ArrayList<>()).stream()
                .map(PersonnelDto::getName)
                .filter(ObjectNull::isNotNull)
                .collect(Collectors.joining(",")));

    }
}