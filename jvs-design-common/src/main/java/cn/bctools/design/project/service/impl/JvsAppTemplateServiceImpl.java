package cn.bctools.design.project.service.impl;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.*;
import cn.bctools.common.utils.function.Get;
import cn.bctools.design.crud.entity.*;
import cn.bctools.design.crud.service.*;
import cn.bctools.design.crud.utils.DesignUtils;
import cn.bctools.design.data.entity.*;
import cn.bctools.design.data.fields.dto.form.FormDataHtml;
import cn.bctools.design.data.fields.dto.form.FormDesignHtml;
import cn.bctools.design.data.fields.dto.form.FormSettingHtml;
import cn.bctools.design.data.fields.dto.page.ButtonDesignHtml;
import cn.bctools.design.data.fields.dto.page.PageDesignHtml;
import cn.bctools.design.data.fields.enums.DesignType;
import cn.bctools.design.data.service.*;
import cn.bctools.design.external.entity.ExternalPage;
import cn.bctools.design.external.service.ExternalPageService;
import cn.bctools.design.identification.entity.Identification;
import cn.bctools.design.identification.service.IdentificationService;
import cn.bctools.design.menu.entity.AppMenu;
import cn.bctools.design.menu.entity.AppMenuType;
import cn.bctools.design.menu.service.AppMenuService;
import cn.bctools.design.menu.service.AppMenuTypeService;
import cn.bctools.design.notice.entity.DataNoticePo;
import cn.bctools.design.notice.service.DataNoticeService;
import cn.bctools.design.project.dto.DesignRoleSettingDto;
import cn.bctools.design.project.dto.ProgressDto;
import cn.bctools.design.project.entity.*;
import cn.bctools.design.project.mapper.JvsAppTemplateDataMapper;
import cn.bctools.design.project.mapper.JvsAppTemplateMapper;
import cn.bctools.design.project.service.JvsAppManageInfoService;
import cn.bctools.design.project.service.JvsAppService;
import cn.bctools.design.project.service.JvsAppTemplateService;
import cn.bctools.design.project.utils.JvsAppTemplateUtils;
import cn.bctools.design.rule.RuleRunService;
import cn.bctools.design.rule.component.XxlJobComponent;
import cn.bctools.design.rule.entity.RuleDesignPo;
import cn.bctools.design.rule.service.RuleDesignService;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.design.workflow.entity.*;
import cn.bctools.design.workflow.service.*;
import cn.bctools.function.entity.po.FunctionBusinessPo;
import cn.bctools.function.mapper.FunctionBusinessMapper;
import cn.bctools.function.service.FunctionBusinessService;
import cn.bctools.message.push.api.InsideNotificationApi;
import cn.bctools.message.push.dto.messagepush.InsideNotificationDto;
import cn.bctools.message.push.dto.messagepush.ReceiversDto;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.redis.utils.RedisUtils;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.DES;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Generator
 */
@Slf4j
@Service
@AllArgsConstructor
public class JvsAppTemplateServiceImpl extends ServiceImpl<JvsAppTemplateMapper, JvsAppTemplate> implements JvsAppTemplateService {
    JvsAppTemplateDataMapper templateDataMapper;
    FormService formService;
    JvsAppService jvsAppService;
    AppUrlService appUrlService;
    CrudPageService crudPageService;

    FunctionBusinessMapper functionBusinessMapper;
    FlowDesignService flowDesignService;
    FlowPurviewService flowPurviewService;
    FlowTaskService flowTaskService;
    FlowTaskApprovalRecordService flowTaskApprovalRecordService;
    FlowTaskCarbonCopyService flowTaskCarbonCopyService;
    FlowTaskNodeService flowTaskNodeService;
    FlowTaskParallelService flowTaskParallelService;
    FlowTaskPathService flowTaskPathService;
    FlowTaskPersonService flowTaskPersonService;
    RuleRunService ruleRunService;
    XxlJobComponent xxlJobComponent;
    RuleDesignService ruleDesignService;
    DataFieldService dataFieldService;
    DataModelService dataModelService;
    DynamicDataService dynamicDataService;
    DataIdService dataIdService;
    RedisUtils redisUtils;
    JvsCrudAssociationService associationService;
    DataEventService dataEventService;
    DataNoticeService dataNoticeService;
    PrintTemplateService printTemplateService;
    ExternalPageService externalPageService;
    InsideNotificationApi insideNotificationApi;
    AppMenuTypeService appMenuTypeService;
    AppMenuService appMenuService;
    IdentificationService identificationService;
    JvsAppManageInfoService manageInfoService;

    FunctionBusinessService functionBusinessService;

    /**
     * key：DES模式下，key必须为8位
     */
    static String key = "jvs1jvs2";
    /**
     * iv：偏移量，ECB模式不需要，CBC模式下必须为8位
     */
    static String iv = "12345678";

    static DES des = new DES(Mode.CBC, Padding.PKCS5Padding, key.getBytes(), iv.getBytes());
    static String redisKey = "jvsAppId::sync:progress";

    @Override
    public ProgressDto create(String templateId) {
        String key = redisKey + UserCurrentUtils.getUserId() + UserCurrentUtils.getCurrentUser().getTenantId();
        ProgressDto progressDto = new ProgressDto();
        redisUtils.set(key + templateId, progressDto.setIsEnd(false).setMessage("正在创建应用").setProportion(0), Long.valueOf(600 * 3 * 3));
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        //区别本地和服务器处理逻辑
        JvsAppTemplate template = this.getById(templateId);
        if (ObjectNull.isNull(template)) {
            //如果本地没有，说明不是模板文件上传的，通过在线获取模板信息
            Object data = JvsAppTemplateUtils.get(HttpUtil.createGet(JvsAppTemplateUtils.DOMAIN + "/JvsAppTemplate/data/" + templateId)).getData();
            //将返回的数据进行转换
            try {
                template = JSONObject.parseObject(JSONObject.toJSONString(data), JvsAppTemplate.class);
            } catch (Exception e) {
                //如果是收费应用
                throw new BusinessException("该模板异常, 请重试");
            }
            if (!template.getFree()) {
                throw new BusinessException("付费应用请联系商务安装");
            }
        } else {
            //不是空，走本地，是空走远程
            template.setData(getData(templateId));
        }
        if (Objects.isNull(template)) {
            log.info("应用模板不存在, 模板id: {}, ", templateId);
            throw new BusinessException("该模板不存在, 请重试");
        }
        //todo 后续修改为同步， 返回相关联的信息， 初始化加快安装和体验
        JvsAppTemplate finalTemplate = template;
        Thread thread = new Thread(() -> {
            SecurityContextHolder.getContext().setAuthentication(authentication);
            redisUtils.set(key + templateId, progressDto.setIsEnd(false).setMessage("正在准备创建数据").setProportion(5), Long.valueOf(600 * 3 * 3));

            try {
                //设置租户
                TenantContextHolder.setTenantId(UserCurrentUtils.getCurrentUser().getTenantId());
                SpringContextUtil.getBean(JvsAppTemplateServiceImpl.class).sync(finalTemplate, progressDto, key + templateId);
                redisUtils.set(key + templateId, progressDto.setIsEnd(true).setMessage("创建完成").setProportion(100), Long.valueOf(600 * 3 * 3));
            } catch (Exception e) {
                sendCreateAppNotice("轻应用创建失败", e.getMessage());
                log.error("创建应用失败", e);
                redisUtils.set(key + templateId, progressDto.setIsEnd(true).setErrorMessage("创建失败" + e.getMessage()).setProportion(0), Long.valueOf(600 * 3 * 3));
            }

        });
        thread.start();
        return progressDto;
    }


    @Transactional(rollbackFor = Exception.class)
    public void sync(JvsAppTemplate template, ProgressDto progressDto, String redisKey) {
        redisUtils.del(redisKey);
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在创建应用").setProportion(7), Long.valueOf(600 * 3 * 3));

        // 创建应用
        LocalDateTime now = LocalDateTime.now();
        String nowStr = LocalDateTimeUtil.format(now, DatePattern.NORM_DATETIME_PATTERN);
        JvsApp jvsApp = JSONObject.parseObject(template.getData(), JvsApp.class);
        String data = jvsApp.getData();
        // 解密模板内容
        data = des.decryptStr(data);
        // 替换创建时间、结束时间为当前时间
        data = data.replaceAll("createTime\":\".*?\"", "createTime\":\"" + nowStr + "\"");
        data = data.replaceAll("updateTime\":\".*?\"", "updateTime\":\"" + nowStr + "\"");
        TemplateIdsBo templateIdsBo = JSONObject.parseObject(data, TemplateIdsBo.class);

        //修改：这里不进行自定义标识校验，因为每次生成应用自定义标识会带上当前的时间戳，确保不会重复。
        // 若有自定义标识，需要校验标识在当前租户下是否已存在，若已存在，则不允许创建应用
        //checkCanCreateApp(data);

        // 旧的APPid
        String oldJvsAppId = templateIdsBo.getIds().get(0);
        String newJvsAppId = IdWorker.get32UUID();
        // 替换新的ID
        data = data.replaceAll(oldJvsAppId, newJvsAppId);

        jvsApp.setId(newJvsAppId);
        jvsApp.setName(template.getName());
        jvsApp.setDescription(template.getDescription());
        jvsApp.setLogo(template.getLogo());
        //获取系统名称
        String shortName = UserCurrentUtils.getCurrentUser().getTenant().getShortName();
        jvsApp.setPlatform(shortName);
        jvsApp.setFree(template.getFree());
        jvsApp.setPlatform(template.getPlatform());
        jvsApp.setPrice(template.getPrice());
        jvsApp.setAuthorizationKey(authorizationKey(jvsApp.getFree()));
        jvsApp.setSecret(JvsAppSecretUtils.getAppSecret(IdGenerator.getIdStr()));
        jvsApp.setCreateTime(now);
        //模板应用的发布状态为也false
        jvsApp.setIsDeploy(false);
        // 设置默认权限
        jvsApp.setDefaultRole();
        jvsAppService.save(jvsApp);
        // 新增应用管理信息
        manageInfoService.save(new JvsAppManageInfo().setAppId(jvsApp.getId()));
        DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(jvsApp.getId()).setJvsAppName(jvsApp.getName()));

        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在拆分模块,可能会等待时间比较长").setProportion(9), Long.valueOf(600 * 3 * 3));
        List<String> ids = templateIdsBo.getIds().stream().filter(ObjectNull::isNotNull).collect(Collectors.toList());
        for (int i = 0; i < ids.size(); i++) {
            String idStr = IdGenerator.getIdStr();
            data = StringUtils.replace(data, ids.get(i), idStr);
        }

        // 替换自定义标识（这里的顺序必须在id替换之后）
        String curVer = RandomUtil.randomString(6);
        List<Identification> identificationList = buildAppIdentifier(data, curVer);
        //正确不会重复，这里为了严谨，再做一次校验
        checkCanCreateApp(identificationList);

        // 向上兼容(兼容2.1.7的模板，转为2.1.8支持的数据结构)
        data = upwardCompatibilityMenu(jvsApp.getId(), template, data);
        TemplateBo templateBo = JSONObject.parseObject(data, TemplateBo.class);
        //替换成新的App标识符
        templateBo.setIdentifications(identificationList);
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在创建自定义页面").setProportion(10), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getAppUrlPos())) {
            appUrlService.saveBatch(templateBo.getAppUrlPos());
        }
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在创建图表结构").setProportion(20), Long.valueOf(600 * 3 * 3));

        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在创建列表结构").setProportion(30), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getCrudPageList())) {
            crudPageService.saveBatch(templateBo.getCrudPageList().stream().peek(e -> e.setIsDeploy(true)).collect(Collectors.toList()));
        }
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在创建表单").setProportion(40), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getFormPoList())) {
            formService.saveBatch(templateBo.getFormPoList().stream().peek(e -> e.setIsDeploy(true)).collect(Collectors.toList()));
        }
        if (ObjectNull.isNotNull(templateBo.getCrudAssociationPos())) {
            associationService.saveBatch(templateBo.getCrudAssociationPos());
        }
        if (ObjectNull.isNotNull(templateBo.getDataEventPos())) {
            dataEventService.saveBatch(templateBo.getDataEventPos());
        }
        if (ObjectNull.isNotNull(templateBo.getPrintTemplates())) {
            printTemplateService.saveBatch(templateBo.getPrintTemplates());
        }
        if (ObjectNull.isNotNull(templateBo.getExternalPages())) {
            externalPageService.saveBatch(templateBo.getExternalPages());
        }
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在创建数据").setProportion(50), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getDynamicDataPos())) {
            List<DynamicDataPo> dynamicDataPos = templateBo.getDynamicDataPos();
            Map<String, List<DynamicDataPo>> collect = dynamicDataPos.stream().filter(e -> ObjectNull.isNotNull(e)).filter(e -> ObjectNull.isNotNull(e.getModelId())).collect(Collectors.groupingBy(DynamicDataPo::getModelId));
            for (String dataModelId : collect.keySet()) {
                dynamicDataService.saveBatch(collect.get(dataModelId), dataModelId);
            }
            //保存 id关系
            dataIdService.saveBatch(templateBo.getDataIdPoList());
        }
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在创建数据结构").setProportion(60), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getDataFieldPos())) {
            // 旧版本可能有应用id为空的情况，需要排除掉
            dataFieldService.saveBatch(templateBo.getDataFieldPos()
                    .stream()
                    .filter(field -> ObjectNull.isNotNull(field.getJvsAppId()))
                    .collect(Collectors.toList())
            );
        }
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在创建数据模型").setProportion(70), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getDataModelPoList())) {
            //处理多租户数据
            templateBo.getDataModelPoList().forEach(e -> e.setTenantId(null));
            dataModelService.createBatch(templateBo.getDataModelPoList());
        }
        if (ObjectNull.isNotNull(templateBo.getDataNoticePos())) {
            dataNoticeService.saveBatch(templateBo.getDataNoticePos());
        }
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在创建工作流").setProportion(80), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getFlowDesigns())) {
            flowDesignService.saveBatch(templateBo.getFlowDesigns());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowPurviews())) {
            flowPurviewService.saveBatch(templateBo.getFlowPurviews());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowTasks())) {
            flowTaskService.saveBatch(templateBo.getFlowTasks());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowTaskApprovalRecords())) {
            flowTaskApprovalRecordService.saveBatch(templateBo.getFlowTaskApprovalRecords());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowTaskCarbonCopies())) {
            flowTaskCarbonCopyService.saveBatch(templateBo.getFlowTaskCarbonCopies());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowTaskNodes())) {
            flowTaskNodeService.saveBatch(templateBo.getFlowTaskNodes());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowTaskParallels())) {
            flowTaskParallelService.saveBatch(templateBo.getFlowTaskParallels());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowTaskPaths())) {
            flowTaskPathService.saveBatch(templateBo.getFlowTaskPaths());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowTaskPersons())) {
            flowTaskPersonService.saveBatch(templateBo.getFlowTaskPersons());
        }
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在创建集成&自动化").setProportion(90), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getRuleDesignPos())) {
            templateBo.getRuleDesignPos().forEach(e -> {
                e.setTenantId(null);
            });
            ruleRunService.importCheck(templateBo.getRuleDesignPos());
            ruleDesignService.saveBatch(templateBo.getRuleDesignPos());
            templateBo.getRuleDesignPos().forEach(ruleDesignPo -> {
                //1、请求有，库里面没有
                if (ruleDesignPo.getOnTask() & ObjectNull.isNotNull(ruleDesignPo.getTask())) {
                    ruleDesignPo.getTask().setId(null);
                    //处理生成定时任务或删除定时任务
                    xxlJobComponent.saveOrUpdateJob(ruleDesignPo.getTask(), ruleDesignPo.getOnTask(), jvsApp.getName(), ruleDesignPo.getName(), ruleDesignPo.getSecret());
                    //重新保存定时任务配置
                    ruleDesignService.lambdaUpdate().eq(RuleDesignPo::getId,ruleDesignPo.getId()).set(RuleDesignPo::getTask,JSON.toJSONString(ruleDesignPo.getTask())).update();
                }
            });
        }

        if (ObjectNull.isNotNull(templateBo.getFunctionBusinessPos())) {
            templateBo.getFunctionBusinessPos().forEach(e -> functionBusinessMapper.insert(e));
        }

        // 创建目录
        if (ObjectNull.isNotNull(templateBo.getAppMenuTypes())) {
            appMenuTypeService.saveBatch(templateBo.getAppMenuTypes());
        }
        if (ObjectNull.isNotNull(templateBo.getAppMenus())) {
            templateBo.getAppMenus().forEach(e -> {
                if ("[]".equals(e.getRole().toString())) {
                    //兼容体验默认权限数据
                    e.setRole(JSONArray.parseArray("[{\"operation\": [], \"scopeList\": [], \"personType\": \"all\", \"personnels\": [], \"conditionList\": [], \"treeOperation\": []}]"));
                }
            });
            appMenuService.saveBatch(templateBo.getAppMenus());
        }

        // 保存自定义标识
        if (ObjectNull.isNotNull(templateBo.getIdentifications())) {
            identificationService.saveBatch(templateBo.getIdentifications());
        }

        // 发送消息
        sendCreateAppNotice(jvsApp.getName() + "轻应用创建成功", jvsApp.getLongText());
    }

    /**
     * 校验是否可以生成应用
     *
     * @param data 模板数据
     */
    private void checkCanCreateApp(String data) {
        TemplateIdentificationBo templateBo = JSONObject.parseObject(data, TemplateIdentificationBo.class);
        if (ObjectNull.isNull(templateBo.getIdentifications())) {
            return;
        }
        List<String> identifiers = templateBo.getIdentifications().stream().map(Identification::getIdentifier).collect(Collectors.toList());
        boolean exists = identificationService.checkExistsIdentifier(identifiers);
        if (exists) {
            throw new BusinessException("当前租户存在与该模板中的自定义标识相同的标识符,不能生成新应用");
        }
    }


    /**
     * 校验是否可以生成应用
     *
     * @param identificationList 标识符集合
     */
    private void checkCanCreateApp(List<Identification> identificationList) {
        if (ObjectNull.isNull(identificationList)) {
            return;
        }
        List<String> identifiers = identificationList.stream().map(Identification::getIdentifier).collect(Collectors.toList());
        boolean exists = identificationService.checkExistsIdentifier(identifiers);
        if (exists) {
            throw new BusinessException("当前租户存在与该模板中的自定义标识相同的标识符,不能生成新应用");
        }
    }

    /**
     * 创建App的标识符，避免重复
     *
     * @param data    模板数据
     * @param version 版本号
     */
    private List<Identification> buildAppIdentifier(String data, String version) {
        TemplateIdentificationBo templateBo = JSONObject.parseObject(data, TemplateIdentificationBo.class);
        List<Identification> identifications = templateBo.getIdentifications();
        if (ObjectNull.isNull(identifications)) {
            return identifications;
        }

        for (Identification identification : identifications) {
            identification.setIdentifier(identification.getIdentifier() + "_v" + version);
        }
        return identifications;
    }

    /**
     * 向上兼容(兼容2.1.7的模板，转为2.1.8支持的数据结构)
     *
     * @param appId    应用id
     * @param template 应用模板
     * @param data     模板数据
     * @return 新的模板数据
     */
    private String upwardCompatibilityMenu(String appId, JvsAppTemplate template, String data) {
        JSONObject dataJson = JSON.parseObject(data);
        // 模板中目录不为空，表示生成模板的版本高于2.1.8，不需要向上兼容
        if (ObjectNull.isNotNull(dataJson.getJSONArray(Get.name(TemplateBo::getAppMenuTypes)))) {
            return data;
        }
        // 目录
        List<AppMenuType> appMenuTypes = new ArrayList<>();
        int typeSize = Optional.ofNullable(template.getTypes()).map(List::size).orElseGet(() -> 0);
        for (int i = 0; i < typeSize; i++) {
            if (ObjectNull.isNull(template.getTypes())) {
                continue;
            }
            AppMenuType appMenuType = new AppMenuType().setId(IdGenerator.getIdStr()).setType(template.getTypes().get(i)).setJvsAppId(appId).setSort(i);
            if (template.getTypes().size() == Optional.ofNullable(template.getIcon()).map(List::size).orElse(0)) {
                appMenuType.setIcon(template.getIcon().get(i));
            }
            appMenuTypes.add(appMenuType);
        }
        Map<String, String> appMenuTypeMap = appMenuTypes.stream().collect(Collectors.toMap(AppMenuType::getType, AppMenuType::getId));

        // 菜单权限
        List<AppMenu> appMenus = new ArrayList<>();
        // 列表
        List<JSONObject> crudPageList = dataJson.getList(Get.name(TemplateBo::getCrudPageList), JSONObject.class);
        if (ObjectNull.isNotNull(crudPageList)) {
            crudPageList.forEach(page -> {
                AppMenu appMenu = BeanCopyUtil.copy(page, AppMenu.class);
                appMenu.setId(null).setDesignType(DesignType.page).setDesignId(page.getString(Get.name(CrudPage::getId)));
                if (ObjectNull.isNotNull(page.get(Get.name(CrudPage::getType)))) {
                    appMenu.setType(appMenuTypeMap.get(appMenu.getType()));
                }
                String viewJson = page.getString(Get.name(CrudPage::getViewJson));
                if (ObjectNull.isNotNull(viewJson)) {
                    PageDesignHtml pageDesignHtml = DesignUtils.parsePage(viewJson);
                    pageDesignHtml.getButtons().addAll(Optional.ofNullable(pageDesignHtml.getLeftTreeButton()).orElseGet(ArrayList::new));
                    appMenu.setPermissionJson(JSONArray.parseArray(JSON.toJSONString(pageDesignHtml.getButtons())));
                }
                appMenus.add(appMenu);
            });
        }
        // 表单
        List<JSONObject> formList = dataJson.getList(Get.name(TemplateBo::getFormPoList), JSONObject.class);
        if (ObjectNull.isNotNull(formList)) {
            formList.forEach(form -> {
                AppMenu appMenu = BeanCopyUtil.copy(form, AppMenu.class);
                appMenu.setId(null).setDesignType(DesignType.form).setDesignId(form.getString(Get.name(FormPo::getId)));
                if (ObjectNull.isNotNull(form.get(Get.name(FormPo::getType)))) {
                    appMenu.setType(appMenuTypeMap.get(appMenu.getType()));
                }
                String viewJson = form.getString(Get.name(FormPo::getViewJson));
                if (ObjectNull.isNotNull(viewJson)) {
                    FormDesignHtml formDesignHtml = Optional.of(DesignUtils.parseForm(viewJson)).orElseGet(FormDesignHtml::new);
                    FormDataHtml formDataHtml = Optional.ofNullable(formDesignHtml.getFormdata()).map(formDesign -> formDesign.get(0)).orElseGet(FormDataHtml::new);
                    List<ButtonDesignHtml> btnSetting = Optional.ofNullable(formDataHtml.getFormsetting()).map(FormSettingHtml::getBtnSetting).orElseGet(ArrayList::new);
                    appMenu.setPermissionJson(JSONArray.parseArray(JSON.toJSONString(btnSetting)));
                }
                appMenus.add(appMenu);
            });
        }

        // 自定义页面
        List<JSONObject> appUrlList = dataJson.getList(Get.name(TemplateBo::getAppUrlPos), JSONObject.class);
        if (ObjectNull.isNotNull(appUrlList)) {
            appUrlList.forEach(url -> {
                AppMenu appMenu = BeanCopyUtil.copy(url, AppMenu.class);
                appMenu.setId(null).setType(appMenuTypeMap.get(appMenu.getType())).setDesignType(DesignType.URL).setDesignId(url.getString(Get.name(AppUrlPo::getId)));
                appMenus.add(appMenu);
            });
        }

        dataJson.put(Get.name(TemplateBo::getAppMenuTypes), appMenuTypes);
        dataJson.put(Get.name(TemplateBo::getAppMenus), appMenus);
        return dataJson.toJSONString();
    }

    /**
     * 创建应用成功，发送消息
     *
     * @param title 标题
     * @param text  内容
     */
    private void sendCreateAppNotice(String title, String text) {
        try {
            //发送站内信
            InsideNotificationDto interiorMessage = new InsideNotificationDto();
            //拼装数据
            Dict set = Dict.create().set("title", title).set("content", text);
            interiorMessage.setContent(JSONObject.toJSONString(set));
            List<ReceiversDto> receiversDtoList = new ArrayList<>();
            receiversDtoList.add(new ReceiversDto().setUserName(UserCurrentUtils.getRealName()).setUserId(UserCurrentUtils.getUserId()));
            interiorMessage.setDefinedReceivers(receiversDtoList);
            insideNotificationApi.send(interiorMessage);
        } catch (Exception e) {
            log.error("发送创建应用成功消息失败：" + e.getMessage());
        }
    }

    /**
     * 根据key生成是否是收费和免费的凭证，每一台服务器都不一样,确定是否付费解除授权凭证
     *
     * @param free
     * @return
     */
    private String authorizationKey(Boolean free) {
        return IdGenerator.getIdStr(34);
    }

    @Override
    public String getDesignData(JvsAppTemplate jvsAppTemplate) {
        String jvsAppId = jvsAppTemplate.getId();

        List<String> ids = new ArrayList<>(20);
        //将应用ID也添加进去
        ids.add(jvsAppId);
        //查询模型
        List<DataModelPo> dataModelList = dataModelService.list(Wrappers.query(new DataModelPo().setAppId(jvsAppId)));
        //得到模型id集合，并清空数据集名称
        List<String> modelIds = dataModelList.stream().peek(model -> model.setCollectionName(null)).map(DataModelPo::getId).collect(Collectors.toList());
        if (ObjectNull.isNull(modelIds)) {
            throw new BusinessException("发布到模板需要有真实的设计");
        }
        ids.addAll(modelIds);
        //列表页
        List<CrudPage> pageList = getCrudPageList(jvsAppId, ids);
        //表单
        List<FormPo> formList = getFormList(jvsAppId, ids);

        //字段
        List<DataFieldPo> dataFieldPoList = getFieldList(modelIds, ids);
        //数据
        List<DynamicDataPo> dynamicDataPoList = jvsAppTemplate.getDeployData() ? getDynamicDataList(modelIds, ids) : null;

        List<DataIdPo> dataIdPoList = jvsAppTemplate.getDeployData() ? getDataIdService(modelIds, ids) : null;

        // TODO 图表改为API调用，暂不支持发布到模板
//        List<Chart> chartPage = getChartPage(jvsAppId, ids);
        //url
        List<AppUrlPo> jvsAppUrl = getJvsAppUrl(jvsAppId, ids);
        // 工作流相关
        List<FlowDesign> flowDesignList = getFlowDesignList(jvsAppId, ids);
        List<FlowPurview> flowPurviewList = getFlowPurviewList(jvsAppId, ids);
        List<FlowTask> flowTasks = jvsAppTemplate.getDeployData() ? getFlowTaskList(jvsAppId, ids) : null;
        List<FlowTaskApprovalRecord> flowTaskApprovalRecords = jvsAppTemplate.getDeployData() ? getFlowTaskApprovalRecordList(jvsAppId, ids) : null;
        List<FlowTaskCopied> flowTaskCarbonCopies = jvsAppTemplate.getDeployData() ? getFlowTaskCarbonCopyList(jvsAppId, ids) : null;
        List<FlowTaskNode> flowTaskNodes = jvsAppTemplate.getDeployData() ? getFlowTaskNodeList(jvsAppId, ids) : null;
        List<FlowTaskParallel> flowTaskParallel = jvsAppTemplate.getDeployData() ? getFlowTaskParallelList(jvsAppId, ids) : null;
        List<FlowTaskPath> flowTaskPaths = jvsAppTemplate.getDeployData() ? getFlowTaskPathList(jvsAppId, ids) : null;
        List<FlowTaskPerson> flowTaskPersons = jvsAppTemplate.getDeployData() ? getFlowTaskPersonList(jvsAppId, ids) : null;
        // 业务关联规则
        List<CrudAssociationPo> crudAssociationPoList = getCurlAssociationList(jvsAppId, ids);

        List<RuleDesignPo> ruleDesignList = getRuleList(jvsAppId, ids);
        List<FunctionBusinessPo> functionBusiness = getFunctionBusiness(jvsAppId, ids);

        // DataEventService
        List<DataEventPo> dataEventPoList = getDataEventList(jvsAppId, ids);
        // 消息通知
        List<DataNoticePo> dataNoticePoList = getDataNoticeList(jvsAppId, ids);
        // 打印模板
        List<PrintTemplate> printTemplateList = getPrintTemplateList(jvsAppId, ids);
        // 外部页面接入
        List<ExternalPage> externalPageList = getExternalPageList(jvsAppId, ids);
        // 目录
        List<AppMenuType> appMenuTypes = getAppMenuTypes(jvsAppId, ids);
        // 轻应用菜单权限
        List<AppMenu> appMenus = getAppMenus(jvsAppId, ids);
        // 标识
        List<Identification> identifications = getIdentifications(jvsAppId, ids);

        TemplateBo templateBo = new TemplateBo();
        templateBo.setAppUrlPos(jvsAppUrl);
        templateBo.setIds(ids);
        templateBo.setFormPoList(formList);
        templateBo.setCrudPageList(pageList);
        templateBo.setDataFieldPos(dataFieldPoList);
        templateBo.setDataIdPoList(dataIdPoList);
        templateBo.setDynamicDataPos(dynamicDataPoList);
        templateBo.setDataModelPoList(dataModelList);
        templateBo.setFlowDesigns(flowDesignList);
        templateBo.setFlowPurviews(flowPurviewList);
        templateBo.setFlowTasks(flowTasks);
        templateBo.setFlowTaskApprovalRecords(flowTaskApprovalRecords);
        templateBo.setFlowTaskCarbonCopies(flowTaskCarbonCopies);
        templateBo.setFlowTaskNodes(flowTaskNodes);
        templateBo.setFlowTaskParallels(flowTaskParallel);
        templateBo.setFlowTaskPaths(flowTaskPaths);
        templateBo.setFlowTaskPersons(flowTaskPersons);
        templateBo.setRuleDesignPos(ruleDesignList);
        templateBo.setFunctionBusinessPos(functionBusiness);
        templateBo.setCrudAssociationPos(crudAssociationPoList);
        templateBo.setDataEventPos(dataEventPoList);
        templateBo.setDataNoticePos(dataNoticePoList);
        templateBo.setPrintTemplates(printTemplateList);
        templateBo.setExternalPages(externalPageList);
        templateBo.setAppMenuTypes(appMenuTypes);
        templateBo.setAppMenus(appMenus);
        templateBo.setIdentifications(identifications);

        ObjectMapper objectMapper = new ObjectMapper();
        try {
            SimpleModule simpleModule = new SimpleModule();
            //  LocalDateTime时间格式化
            simpleModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            objectMapper.registerModule(simpleModule);
            String s = objectMapper.writeValueAsString(templateBo);
            return des.encryptBase64(s);
        } catch (JsonProcessingException e) {
            log.error("发布模板失败", e);
            throw new BusinessException("发布模板失败" + e);
        }
    }

    /**
     * 模型的自增 id数据
     *
     * @param modelIds 所有的模型 id
     * @param ids      所有的 id
     * @return
     */
    private List<DataIdPo> getDataIdService(List<String> modelIds, List<String> ids) {
        if (ObjectNull.isNull(modelIds)) {
            return Collections.emptyList();
        }
        dataIdService.syncUpdateDataId(DesignType.data, modelIds);
        List<DataIdPo> list = dataIdService.list(new LambdaQueryWrapper<DataIdPo>().in(DataIdPo::getModelId, modelIds));
        return list;
    }

    private List<FunctionBusinessPo> getFunctionBusiness(String jvsAppId, List<String> ids) {
        List<FunctionBusinessPo> list = functionBusinessMapper.selectList(Wrappers.query(new FunctionBusinessPo().setJvsAppId(jvsAppId)));
        {
            List<String> collect = list.stream().map(FunctionBusinessPo::getId).collect(Collectors.toList());
            ids.addAll(collect);
        }
        return list;
    }

    private List<CrudAssociationPo> getCurlAssociationList(String jvsAppId, List<String> ids) {
        List<CrudAssociationPo> associationPoList = associationService.list(Wrappers.query(new CrudAssociationPo().setJvsAppId(jvsAppId)));
        if (CollectionUtils.isEmpty(associationPoList)) {
            return Collections.emptyList();
        }
        List<String> listIds = associationPoList.stream().map(CrudAssociationPo::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return associationPoList;
    }

    private List<RuleDesignPo> getRuleList(String jvsAppId, List<String> ids) {
        List<RuleDesignPo> list = ruleDesignService.list(Wrappers.query(new RuleDesignPo().setJvsAppId(jvsAppId)));
        {
            List<String> collect = list.stream().map(RuleDesignPo::getId).collect(Collectors.toList());
            List<String> collect2 = list.stream().map(RuleDesignPo::getSecret).collect(Collectors.toList());
            ids.addAll(collect);
            ids.addAll(collect2);
        }
        return list;
    }

    private List<DynamicDataPo> getDynamicDataList(List<String> modelIds, List<String> ids) {
        //数据ID不做替换
        List<DynamicDataPo> dynamicDataPoList = dynamicDataService.queryList(modelIds);
        return dynamicDataPoList;
    }

    private List<DataFieldPo> getFieldList(List<String> modelIds, List<String> ids) {
        List<DataFieldPo> dataFieldPoList = dataFieldService.list(new LambdaQueryWrapper<DataFieldPo>().in(DataFieldPo::getModelId, modelIds));
        {
            List<String> collect = dataFieldPoList.stream().map(DataFieldPo::getId).collect(Collectors.toList());
            ids.addAll(collect);
        }
        return dataFieldPoList;
    }

    private List<AppUrlPo> getJvsAppUrl(String jvsAppId, List<String> ids) {
        List<AppUrlPo> list = appUrlService.list(Wrappers.query(new AppUrlPo().setJvsAppId(jvsAppId)));
        List<String> listIds = list.stream().map(AppUrlPo::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return list;
    }

    // TODO 图表改为API调用，暂不支持发布到模板
   /* private List<ChartPage> getChartPage(String jvsAppId, List<String> ids) {
        List<ChartPage> list = chartPageService.list(Wrappers.query(new ChartPage().setJvsAppId(jvsAppId)))
                .stream()
                .peek(e -> e.setRole(Collections.emptyList()).setIsDeploy(true).setRoleType(true))
                .peek(e -> e.setChartComponents(chartComponentService.list(Wrappers.query(new ChartComponent().setPageId(e.getId())))))
                .collect(Collectors.toList());
        List<String> listIds = list.stream().map(ChartPage::getId).collect(Collectors.toList());
        List<String> chartComponentIds = list.stream().flatMap(e -> e.getChartComponents().stream().map(ChartComponent::getId)).collect(Collectors.toList());
        ids.addAll(listIds);
        //添加组件ID
        ids.addAll(chartComponentIds);
        return list;
    }*/

    private List<CrudPage> getCrudPageList(String jvsAppId, List<String> ids) {
        List<CrudPage> pageList = crudPageService.list(Wrappers.query(new CrudPage().setJvsAppId(jvsAppId))).stream().peek(e -> {
            e.setIsDeploy(true);
        }).collect(Collectors.toList());
        List<String> collect = pageList.stream().map(CrudPage::getId).collect(Collectors.toList());
        ids.addAll(collect);
        return pageList;
    }

    private List<FormPo> getFormList(String jvsAppId, List<String> ids) {
        List<FormPo> formList = formService.list(Wrappers.query(new FormPo().setJvsAppId(jvsAppId))).stream().peek(e -> {
            e.setIsDeploy(true);
        }).collect(Collectors.toList());
        List<String> collect = formList.stream().map(FormPo::getId).collect(Collectors.toList());
        ids.addAll(collect);
        return formList;
    }

    private List<FlowDesign> getFlowDesignList(String jvsAppId, List<String> ids) {
        List<FlowDesign> flowDesignList = flowDesignService.list(Wrappers.query(new FlowDesign().setJvsAppId(jvsAppId)));
        if (CollectionUtils.isEmpty(flowDesignList)) {
            return Collections.emptyList();
        }
        List<String> listIds = flowDesignList.stream().map(FlowDesign::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return flowDesignList;
    }

    private List<FlowTask> getFlowTaskList(String jvsAppId, List<String> ids) {
        List<FlowTask> flowTaskList = flowTaskService.list(Wrappers.query(new FlowTask().setJvsAppId(jvsAppId)));
        if (CollectionUtils.isEmpty(flowTaskList)) {
            return Collections.emptyList();
        }
        List<String> listIds = flowTaskList.stream().map(FlowTask::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return flowTaskList;
    }

    private List<FlowTaskApprovalRecord> getFlowTaskApprovalRecordList(String jvsAppId, List<String> ids) {
        List<FlowTaskApprovalRecord> flowTaskApprovalRecordList = flowTaskApprovalRecordService.list(Wrappers.query(new FlowTaskApprovalRecord().setJvsAppId(jvsAppId)));
        if (CollectionUtils.isEmpty(flowTaskApprovalRecordList)) {
            return Collections.emptyList();
        }
        List<String> listIds = flowTaskApprovalRecordList.stream().map(FlowTaskApprovalRecord::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return flowTaskApprovalRecordList;
    }

    private List<FlowTaskCopied> getFlowTaskCarbonCopyList(String jvsAppId, List<String> ids) {
        List<FlowTaskCopied> flowTaskCarbonCopyList = flowTaskCarbonCopyService.list(Wrappers.query(new FlowTaskCopied().setJvsAppId(jvsAppId)));
        if (CollectionUtils.isEmpty(flowTaskCarbonCopyList)) {
            return Collections.emptyList();
        }
        List<String> listIds = flowTaskCarbonCopyList.stream().map(FlowTaskCopied::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return flowTaskCarbonCopyList;
    }

    private List<FlowTaskNode> getFlowTaskNodeList(String jvsAppId, List<String> ids) {
        List<FlowTaskNode> flowTaskNodeList = flowTaskNodeService.list(Wrappers.query(new FlowTaskNode().setJvsAppId(jvsAppId)));
        if (CollectionUtils.isEmpty(flowTaskNodeList)) {
            return Collections.emptyList();
        }
        List<String> listIds = flowTaskNodeList.stream().map(FlowTaskNode::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return flowTaskNodeList;
    }

    private List<FlowTaskParallel> getFlowTaskParallelList(String jvsAppId, List<String> ids) {
        List<FlowTaskParallel> flowTaskParallelList = flowTaskParallelService.list(Wrappers.query(new FlowTaskParallel().setJvsAppId(jvsAppId)));
        if (CollectionUtils.isEmpty(flowTaskParallelList)) {
            return Collections.emptyList();
        }
        List<String> listIds = flowTaskParallelList.stream().map(FlowTaskParallel::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return flowTaskParallelList;
    }

    private List<FlowTaskPath> getFlowTaskPathList(String jvsAppId, List<String> ids) {
        List<FlowTaskPath> flowTaskPathList = flowTaskPathService.list(Wrappers.query(new FlowTaskPath().setJvsAppId(jvsAppId)));
        if (CollectionUtils.isEmpty(flowTaskPathList)) {
            return Collections.emptyList();
        }
        List<String> listIds = flowTaskPathList.stream().map(FlowTaskPath::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return flowTaskPathList;
    }

    private List<FlowTaskPerson> getFlowTaskPersonList(String jvsAppId, List<String> ids) {
        List<FlowTaskPerson> flowTaskPersonList = flowTaskPersonService.list(Wrappers.query(new FlowTaskPerson().setJvsAppId(jvsAppId)));
        if (CollectionUtils.isEmpty(flowTaskPersonList)) {
            return Collections.emptyList();
        }
        List<String> listIds = flowTaskPersonList.stream().map(FlowTaskPerson::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return flowTaskPersonList;
    }

    private List<FlowPurview> getFlowPurviewList(String jvsAppId, List<String> ids) {
        List<FlowPurview> flowPurviewList = flowPurviewService.list(Wrappers.query(new FlowPurview().setJvsAppId(jvsAppId)));
        if (CollectionUtils.isEmpty(flowPurviewList)) {
            return Collections.emptyList();
        }
        List<String> listIds = flowPurviewList.stream().map(FlowPurview::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return flowPurviewList;
    }


    private List<DataEventPo> getDataEventList(String jvsAppId, List<String> ids) {
        List<DataEventPo> dataEventPoList = dataEventService.list(Wrappers.<DataEventPo>lambdaQuery().eq(DataEventPo::getAppId, jvsAppId));
        if (CollectionUtils.isEmpty(dataEventPoList)) {
            return Collections.emptyList();
        }
        List<String> listIds = dataEventPoList.stream().map(DataEventPo::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return dataEventPoList;
    }

    private List<DataNoticePo> getDataNoticeList(String jvsAppId, List<String> ids) {
        List<DataNoticePo> dataNoticePoList = dataNoticeService.list(Wrappers.<DataNoticePo>lambdaQuery().eq(DataNoticePo::getJvsAppId, jvsAppId));
        if (CollectionUtils.isEmpty(dataNoticePoList)) {
            return Collections.emptyList();
        }
        List<String> listIds = dataNoticePoList.stream().map(DataNoticePo::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return dataNoticePoList;
    }

    private List<PrintTemplate> getPrintTemplateList(String jvsAppId, List<String> ids) {
        List<PrintTemplate> printTemplateList = printTemplateService.list(Wrappers.<PrintTemplate>lambdaQuery().eq(PrintTemplate::getJvsAppId, jvsAppId));
        if (CollectionUtils.isEmpty(printTemplateList)) {
            return Collections.emptyList();
        }
        List<String> listIds = printTemplateList.stream().map(PrintTemplate::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return printTemplateList;
    }


    private List<ExternalPage> getExternalPageList(String jvsAppId, List<String> ids) {
        List<ExternalPage> externalPageList = externalPageService.list(Wrappers.<ExternalPage>lambdaQuery().eq(ExternalPage::getJvsAppId, jvsAppId));
        if (CollectionUtils.isEmpty(externalPageList)) {
            return Collections.emptyList();
        }
        List<String> listIds = externalPageList.stream().map(ExternalPage::getId).collect(Collectors.toList());
        ids.addAll(listIds);
        return externalPageList;
    }

    private List<AppMenuType> getAppMenuTypes(String jvsAppId, List<String> ids) {
        List<AppMenuType> appMenuTypesList = appMenuTypeService.list(Wrappers.query(new AppMenuType().setJvsAppId(jvsAppId)));
        List<String> collect = appMenuTypesList.stream().map(AppMenuType::getId).collect(Collectors.toList());
        ids.addAll(collect);
        return appMenuTypesList;
    }

    private List<AppMenu> getAppMenus(String jvsAppId, List<String> ids) {
        List<AppMenu> appMenuList = appMenuService.list(Wrappers.query(new AppMenu().setJvsAppId(jvsAppId))).stream().peek(e -> {
            // 设置默认权限
//            DesignRole role = new DesignRole().setPersonType(PersonnelTypeEnum.all);
//            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(role));
//            e.setRole(JSONArray.of(jsonObject)).setRoleType(true);
            //同一物理环境下，用户数据一致，保留权限，方便测试
        }).filter(po -> {
            if (po.getDesignType().equals(DesignType.chart) || po.getDesignType().equals(DesignType.report) || po.getDesignType().equals(DesignType.screen)) {
                //不处理chart、report，screen类型的菜单
                return false;
            } else {
                return true;
            }
        }).collect(Collectors.toList());
        List<String> collect = appMenuList.stream().map(AppMenu::getId).collect(Collectors.toList());
        ids.addAll(collect);
        return appMenuList;
    }

    private List<Identification> getIdentifications(String jvsAppId, List<String> ids) {
        List<Identification> identificationList = identificationService.list(Wrappers.query(new Identification().setJvsAppId(jvsAppId)));
        List<String> collect = identificationList.stream().map(Identification::getId).collect(Collectors.toList());
        ids.addAll(collect);
        return identificationList;
    }

    /**
     * 根据应用ID获取详情
     *
     * @param templateId
     * @return
     */
    @Override
    public String getData(String templateId) {
        String collect = templateDataMapper.selectList(Wrappers.<JvsAppTemplateData>lambdaQuery().eq(JvsAppTemplateData::getTemplateId, templateId).orderByAsc(JvsAppTemplateData::getSort)).stream().filter(Objects::nonNull).map(JvsAppTemplateData::getData).collect(Collectors.joining());
        return collect;
    }


    @Override
    public ProgressDto update(String templateId) {
        String key = redisKey + UserCurrentUtils.getUserId() + UserCurrentUtils.getCurrentUser().getTenantId();
        ProgressDto progressDto = new ProgressDto();
        redisUtils.set(key + templateId, progressDto.setIsEnd(false).setMessage("正在更新应用").setProportion(0), Long.valueOf(600 * 3 * 3));
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        //区别本地和服务器处理逻辑
        JvsAppTemplate template = this.getById(templateId);
        if (ObjectNull.isNull(template)) {
            //如果本地没有，说明不是模板文件上传的，通过在线获取模板信息
            Object data = JvsAppTemplateUtils.get(HttpUtil.createGet(JvsAppTemplateUtils.DOMAIN + "/JvsAppTemplate/data/" + templateId)).getData();
            //将返回的数据进行转换
            try {
                template = JSONObject.parseObject(JSONObject.toJSONString(data), JvsAppTemplate.class);
            } catch (Exception e) {
                //如果是收费应用
                throw new BusinessException("该模板异常, 请重试");
            }
            if (!template.getFree()) {
                throw new BusinessException("付费应用请联系商务安装");
            }
        } else {
            //不是空，走本地，是空走远程
            template.setData(getData(templateId));
        }
        if (Objects.isNull(template)) {
            log.info("应用模板不存在, 模板id: {}, ", templateId);
            throw new BusinessException("该模板不存在, 请重试");
        }
        //todo 后续修改为同步， 返回相关联的信息， 初始化加快安装和体验
        JvsAppTemplate finalTemplate = template;
        SecurityContextHolder.getContext().setAuthentication(authentication);
        redisUtils.set(key + templateId, progressDto.setIsEnd(false).setMessage("正在准备更新数据").setProportion(5), Long.valueOf(600 * 3 * 3));

        try {
            //设置租户
            TenantContextHolder.setTenantId(UserCurrentUtils.getCurrentUser().getTenantId());
            SpringContextUtil.getBean(JvsAppTemplateServiceImpl.class).syncUpdate(finalTemplate, progressDto, key + templateId);
            redisUtils.set(key + templateId, progressDto.setIsEnd(true).setMessage("更新完成").setProportion(100), Long.valueOf(600 * 3 * 3));
        } catch (Exception e) {
            sendCreateAppNotice("轻应用更新失败", e.getMessage());
            log.error("更新应用失败", e);
            redisUtils.set(key + templateId, progressDto.setIsEnd(true).setErrorMessage("更新失败" + e.getMessage()).setProportion(0), Long.valueOf(600 * 3 * 3));
        }

        return progressDto;
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncUpdate(JvsAppTemplate template, ProgressDto progressDto, String redisKey) {
        redisUtils.del(redisKey);
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在更新应用").setProportion(7), Long.valueOf(600 * 3 * 3));

        // 创建应用
        LocalDateTime now = LocalDateTime.now();
        JvsApp jvsApp = JSONObject.parseObject(template.getData(), JvsApp.class);
        String data = jvsApp.getData();
        // 解密模板内容
        data = des.decryptStr(data);
        TemplateIdsBo templateIdsBo = JSONObject.parseObject(data, TemplateIdsBo.class);

        String jvsAppId = templateIdsBo.getIds().get(0);
        if (StringUtils.isEmpty(jvsAppId)) {
            throw new BusinessException("appId不能为空");
        }
        jvsApp.setId(jvsAppId);
        //根据jvsAppId查询系统是否存在该APP
        JvsApp dbJvsApp = jvsAppService.getById(jvsAppId);
        if (null == dbJvsApp) {
            //如果系统没有这个APP，那么进行新增
            jvsApp.setName(template.getName());
            jvsApp.setDescription(template.getDescription());
            jvsApp.setLogo(template.getLogo());
            //获取系统名称
            String shortName = UserCurrentUtils.getCurrentUser().getTenant().getShortName();
            jvsApp.setPlatform(shortName);
            jvsApp.setFree(template.getFree());
            jvsApp.setPlatform(template.getPlatform());
            jvsApp.setPrice(template.getPrice());
            jvsApp.setAuthorizationKey(authorizationKey(jvsApp.getFree()));
            jvsApp.setSecret(JvsAppSecretUtils.getAppSecret(IdGenerator.getIdStr()));
            jvsApp.setCreateTime(now);
            //模板应用的发布状态为false
            jvsApp.setIsDeploy(false);
            // 设置默认权限
            jvsApp.setDefaultRole();
            jvsAppService.save(jvsApp);

            manageInfoService.save(new JvsAppManageInfo().setAppId(jvsAppId));
        } else {
            //如果系统存在这个APP，则不更新APP，防止覆盖已经配置好的基本信息,如名称、封面、权限等
            jvsApp.setName(dbJvsApp.getName());
        }


        DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(jvsApp.getId()).setJvsAppName(jvsApp.getName()));

        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在拆分模块,可能会等待时间比较长").setProportion(9), Long.valueOf(600 * 3 * 3));

        // 向上兼容(兼容2.1.7的模板，转为2.1.8支持的数据结构)
        data = upwardCompatibilityMenu(jvsApp.getId(), template, data);
        TemplateBo templateBo = JSONObject.parseObject(data, TemplateBo.class);

        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在更新自定义页面").setProportion(10), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getAppUrlPos())) {
            List<String> templateIds = templateBo.getAppUrlPos().stream().map(AppUrlPo::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<AppUrlPo> dbList = appUrlService.list(Wrappers.query(new AppUrlPo().setJvsAppId(jvsAppId)).select("id"));
            for (AppUrlPo appUrlPo : dbList) {
                //查出数据库现有的id，对比导入的id，不存在的即删除
                if (!templateIds.contains(appUrlPo.getId())) {
                    removeIds.add(appUrlPo.getId());
                }
            }
            appUrlService.removeBatchByIds(removeIds);
            appUrlService.saveOrUpdateBatch(templateBo.getAppUrlPos());
        }
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在更新图表结构").setProportion(20), Long.valueOf(600 * 3 * 3));

        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在更新列表结构").setProportion(30), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getCrudPageList())) {
            List<String> templateIds = templateBo.getCrudPageList().stream().map(CrudPage::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<CrudPage> dbList = crudPageService.list(Wrappers.query(new CrudPage().setJvsAppId(jvsAppId)).select("id"));
            for (CrudPage crudPage : dbList) {
                //查出数据库现有的id，对比导入的id，不存在的即删除
                if (!templateIds.contains(crudPage.getId())) {
                    removeIds.add(crudPage.getId());
                }
            }
            crudPageService.removeBatchByIds(removeIds);
            crudPageService.saveOrUpdateBatch(templateBo.getCrudPageList().stream().peek(e -> e.setIsDeploy(true)).collect(Collectors.toList()));
        }
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("更新表单").setProportion(40), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getFormPoList())) {
            List<String> templateIds = templateBo.getFormPoList().stream().map(FormPo::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<FormPo> dbList = formService.list(Wrappers.query(new FormPo().setJvsAppId(jvsAppId)).select("id"));
            for (FormPo formPo : dbList) {
                //查出数据库现有的id，对比导入的id，不存在的即删除
                if (!templateIds.contains(formPo.getId())) {
                    removeIds.add(formPo.getId());
                }
            }
            formService.removeBatchByIds(removeIds);
            formService.saveOrUpdateBatch(templateBo.getFormPoList().stream().peek(e -> e.setIsDeploy(true)).collect(Collectors.toList()));
        }
        if (ObjectNull.isNotNull(templateBo.getCrudAssociationPos())) {
            List<String> templateIds = templateBo.getCrudAssociationPos().stream().map(CrudAssociationPo::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<CrudAssociationPo> dbList = associationService.list(Wrappers.query(new CrudAssociationPo().setJvsAppId(jvsAppId)).select("id"));
            for (CrudAssociationPo crudAssociationPo : dbList) {
                //查出数据库现有的id，对比导入的id，不存在的即删除
                if (!templateIds.contains(crudAssociationPo.getId())) {
                    removeIds.add(crudAssociationPo.getId());
                }
            }
            associationService.removeBatchByIds(removeIds);
            associationService.saveOrUpdateBatch(templateBo.getCrudAssociationPos());
        }
        if (ObjectNull.isNotNull(templateBo.getDataEventPos())) {
            List<String> templateIds = templateBo.getDataEventPos().stream().map(DataEventPo::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<DataEventPo> dbList = dataEventService.list(Wrappers.query(new DataEventPo().setAppId(jvsAppId)).select("id"));
            for (DataEventPo dataEventPo : dbList) {
                //查出数据库现有的id，对比导入的id，不存在的即删除
                if (!templateIds.contains(dataEventPo.getId())) {
                    removeIds.add(dataEventPo.getId());
                }
            }
            dataEventService.removeBatchByIds(removeIds);
            dataEventService.saveOrUpdateBatch(templateBo.getDataEventPos());
        }
        if (ObjectNull.isNotNull(templateBo.getPrintTemplates())) {
            List<String> templateIds = templateBo.getPrintTemplates().stream().map(PrintTemplate::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<PrintTemplate> dbList = printTemplateService.list(Wrappers.query(new PrintTemplate().setJvsAppId(jvsAppId)).select("id"));
            //如果存在打印设置记录，说明不是第一次安装，不进行更新，防止打印文件被覆盖
            if (dbList.size() == 0) {
                for (PrintTemplate printTemplate : dbList) {
                    //查出数据库现有的id，对比导入的id，不存在的即删除
                    if (!templateIds.contains(printTemplate.getId())) {
                        removeIds.add(printTemplate.getId());
                    }
                }
                printTemplateService.removeBatchByIds(removeIds);
                printTemplateService.saveOrUpdateBatch(templateBo.getPrintTemplates());
            }
        }
        if (ObjectNull.isNotNull(templateBo.getExternalPages())) {
            List<String> templateIds = templateBo.getExternalPages().stream().map(ExternalPage::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<ExternalPage> dbList = externalPageService.list(Wrappers.query(new ExternalPage().setJvsAppId(jvsAppId)).select("id"));
            for (ExternalPage externalPage : dbList) {
                //查出数据库现有的id，对比导入的id，不存在的即删除
                if (!templateIds.contains(externalPage.getId())) {
                    removeIds.add(externalPage.getId());
                }
            }
            externalPageService.removeBatchByIds(removeIds);
            externalPageService.saveOrUpdateBatch(templateBo.getExternalPages());
        }
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在更新数据").setProportion(50), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getDynamicDataPos())) {
            List<DynamicDataPo> dynamicDataPos = templateBo.getDynamicDataPos();
            Map<String, List<DynamicDataPo>> collect = dynamicDataPos.stream().filter(e -> ObjectNull.isNotNull(e)).filter(e -> ObjectNull.isNotNull(e.getModelId())).collect(Collectors.groupingBy(DynamicDataPo::getModelId));
            for (String dataModelId : collect.keySet()) {
                dynamicDataService.saveBatch(collect.get(dataModelId), dataModelId);
            }
            //保存 id关系
            dataIdService.saveBatch(templateBo.getDataIdPoList());
        }
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在更新数据结构").setProportion(60), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getDataFieldPos())) {
            List<String> templateIds = templateBo.getDataFieldPos().stream().map(DataFieldPo::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<DataFieldPo> dbList = dataFieldService.list(Wrappers.query(new DataFieldPo().setJvsAppId(jvsAppId)).select("id"));
            for (DataFieldPo po : dbList) {
                //查出数据库现有的id，对比导入的id，不存在的即删除
                if (!templateIds.contains(po.getId())) {
                    removeIds.add(po.getId());
                }
            }
            dataFieldService.removeBatchByIds(removeIds);
            dataFieldService.saveOrUpdateBatch(templateBo.getDataFieldPos());
        }
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在更新数据模型").setProportion(70), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getDataModelPoList())) {
            //处理多租户数据
            templateBo.getDataModelPoList().forEach(e -> e.setTenantId(null));
            List<String> templateIds = templateBo.getDataModelPoList().stream().map(DataModelPo::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<DataModelPo> dbList = dataModelService.list(Wrappers.query(new DataModelPo().setAppId(jvsAppId)).select("id"));
            for (DataModelPo po : dbList) {
                //查出数据库现有的id，对比导入的id，不存在的即删除
                if (!templateIds.contains(po.getId())) {
                    removeIds.add(po.getId());
                }
            }
            dataModelService.removeBatchByIds(removeIds);
            dataModelService.saveOrUpdateBatch(templateBo.getDataModelPoList());
        }
        if (ObjectNull.isNotNull(templateBo.getDataNoticePos())) {
            List<String> templateIds = templateBo.getDataNoticePos().stream().map(DataNoticePo::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<DataNoticePo> dbList = dataNoticeService.list(Wrappers.query(new DataNoticePo().setJvsAppId(jvsAppId)).select("id"));
            for (DataNoticePo po : dbList) {
                //查出数据库现有的id，对比导入的id，不存在的即删除
                if (!templateIds.contains(po.getId())) {
                    removeIds.add(po.getId());
                }
            }
            dataNoticeService.removeBatchByIds(removeIds);
            dataNoticeService.saveOrUpdateBatch(templateBo.getDataNoticePos());
        }
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在更新工作流").setProportion(80), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getFlowDesigns())) {
            flowDesignService.saveOrUpdateBatch(templateBo.getFlowDesigns());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowPurviews())) {
            flowPurviewService.saveOrUpdateBatch(templateBo.getFlowPurviews());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowTasks())) {
            flowTaskService.saveOrUpdateBatch(templateBo.getFlowTasks());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowTaskApprovalRecords())) {
            flowTaskApprovalRecordService.saveOrUpdateBatch(templateBo.getFlowTaskApprovalRecords());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowTaskCarbonCopies())) {
            flowTaskCarbonCopyService.saveOrUpdateBatch(templateBo.getFlowTaskCarbonCopies());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowTaskNodes())) {
            flowTaskNodeService.saveOrUpdateBatch(templateBo.getFlowTaskNodes());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowTaskParallels())) {
            flowTaskParallelService.saveOrUpdateBatch(templateBo.getFlowTaskParallels());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowTaskPaths())) {
            flowTaskPathService.saveOrUpdateBatch(templateBo.getFlowTaskPaths());
        }
        if (ObjectNull.isNotNull(templateBo.getFlowTaskPersons())) {
            flowTaskPersonService.saveOrUpdateBatch(templateBo.getFlowTaskPersons());
        }
        redisUtils.set(redisKey, progressDto.setIsEnd(false).setMessage("正在更新集成&自动化").setProportion(90), Long.valueOf(600 * 3 * 3));

        if (ObjectNull.isNotNull(templateBo.getRuleDesignPos())) {
            templateBo.getRuleDesignPos().forEach(e -> e.setTenantId(null));
            ruleRunService.importCheck(templateBo.getRuleDesignPos());
            List<String> templateIds = templateBo.getRuleDesignPos().stream().map(RuleDesignPo::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<RuleDesignPo> dbList = ruleDesignService.list(Wrappers.query(new RuleDesignPo().setJvsAppId(jvsAppId)).select("id"));
            for (RuleDesignPo po : dbList) {
                //查出数据库现有的id，对比导入的id，不存在的即删除
                if (!templateIds.contains(po.getId())) {
                    removeIds.add(po.getId());
                }
            }
            ruleDesignService.removeBatchByIds(removeIds);
            ruleDesignService.saveOrUpdateBatch(templateBo.getRuleDesignPos());
            templateBo.getRuleDesignPos().forEach(ruleDesignPo -> {
                //1、请求有，库里面没有
                if (ruleDesignPo.getOnTask() & ObjectNull.isNotNull(ruleDesignPo.getTask())) {
                    //处理生成定时任务或删除定时任务
                    xxlJobComponent.saveOrUpdateJob(ruleDesignPo.getTask(), ruleDesignPo.getOnTask(), jvsApp.getName(), ruleDesignPo.getName(), ruleDesignPo.getSecret());
                }
            });
        }

        if (ObjectNull.isNotNull(templateBo.getFunctionBusinessPos())) {
            List<String> templateIds = templateBo.getFunctionBusinessPos().stream().map(FunctionBusinessPo::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<FunctionBusinessPo> dbList = functionBusinessService.list(Wrappers.query(new FunctionBusinessPo().setJvsAppId(jvsAppId)).select("id"));
            for (FunctionBusinessPo po : dbList) {
                //查出数据库现有的id，对比导入的id，不存在的即删除
                if (!templateIds.contains(po.getId())) {
                    removeIds.add(po.getId());
                }
            }
            functionBusinessService.removeBatchByIds(removeIds);
            functionBusinessService.saveOrUpdateBatch(templateBo.getFunctionBusinessPos());
        }

        // 创建目录
        if (ObjectNull.isNotNull(templateBo.getAppMenuTypes())) {
            List<String> templateIds = templateBo.getAppMenuTypes().stream().map(AppMenuType::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<AppMenuType> dbList = appMenuTypeService.list(Wrappers.query(new AppMenuType().setJvsAppId(jvsAppId)).select("id"));
            for (AppMenuType po : dbList) {
                //查出数据库现有的id，对比导入的id，不存在的即删除
                if (!templateIds.contains(po.getId())) {
                    removeIds.add(po.getId());
                }
            }
            appMenuTypeService.removeBatchByIds(removeIds);
            appMenuTypeService.saveOrUpdateBatch(templateBo.getAppMenuTypes());
        }
        if (ObjectNull.isNotNull(templateBo.getAppMenus())) {
            templateBo.getAppMenus().forEach(e -> {
                if ("[]".equals(e.getRole().toString())) {
                    //兼容体验默认权限数据
                    e.setRole(JSONArray.parseArray("[{\"operation\": [], \"scopeList\": [], \"personType\": \"all\", \"personnels\": [], \"conditionList\": [], \"treeOperation\": []}]"));
                }
            });
            List<String> templateIds = templateBo.getAppMenus().stream().map(AppMenu::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<AppMenu> dbList = appMenuService.list(Wrappers.query(new AppMenu().setJvsAppId(jvsAppId)).select("id", "design_type"));
            for (AppMenu po : dbList) {
                //查出数据库现有的id，对比导入的id，不存在的即删除
                if (!templateIds.contains(po.getId())) {
                    if (po.getDesignType().equals(DesignType.chart) || po.getDesignType().equals(DesignType.report) || po.getDesignType().equals(DesignType.screen)) {
                        //不处理chart、report，screen类型的菜单
                    } else {
                        removeIds.add(po.getId());
                    }
                }
            }
            appMenuService.removeBatchByIds(removeIds);
            appMenuService.saveOrUpdateBatch(templateBo.getAppMenus());
        }

        // 保存自定义标识
        if (ObjectNull.isNotNull(templateBo.getIdentifications())) {
            List<String> templateIds = templateBo.getIdentifications().stream().map(Identification::getId).collect(Collectors.toList());
            List<String> removeIds = new ArrayList<>();
            List<Identification> dbList = identificationService.list(Wrappers.query(new Identification().setJvsAppId(jvsAppId)).select("id"));
            for (Identification po : dbList) {
                //查出数据库现有的id，对比导入的id，不存在的即删除
                if (!templateIds.contains(po.getId())) {
                    removeIds.add(po.getId());
                }
            }
            identificationService.removeBatchByIds(removeIds);
            identificationService.saveOrUpdateBatch(templateBo.getIdentifications());
        }

        // 发送消息
        sendCreateAppNotice(jvsApp.getName() + "轻应用更新成功", jvsApp.getLongText());
    }

}