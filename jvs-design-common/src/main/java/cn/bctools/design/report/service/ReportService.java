package cn.bctools.design.report.service;

import cn.bctools.design.report.dto.ReportDto;
import cn.bctools.design.report.entity.Report;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>iaoKang
 * @Description: 报表
 */
public interface ReportService extends IService<Report> {

    /**
     * 新建报表
     *
     * @param appId 应用id
     * @param report 入参
     * @return
     */
    void createReport(String appId, Report report);

    /**
     * 获取所有报表
     *
     * @param appId 应用id
     * @return
     */
    List<ReportDto> getAll(String appId);
}
