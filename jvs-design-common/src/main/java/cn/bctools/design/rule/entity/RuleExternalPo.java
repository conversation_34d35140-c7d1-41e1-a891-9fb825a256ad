package cn.bctools.design.rule.entity;

import cn.bctools.rule.dto.RuleFunctionDtoParameter;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.hutool.http.Method;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.bctools.database.handler.Fastjson2TypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 逻辑引擎分组扩展
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "jvs_rule_external", autoResultMap = true)
public class RuleExternalPo implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty("id")
    private String id;
    private String url;
    @ApiModelProperty("方法的图片")
    private String icon;
    private String explainInfo;
    @ApiModelProperty("状态是否可用")
    private Boolean status;
    private RuleGroup ruleGroup;
    private String name;
    @ApiModelProperty("请求方法")
    @TableField("method_type")
    private Method method;
    @ApiModelProperty("自定义解析处理类")
    private String handler;
    @ApiModelProperty("字典数据")
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List fieldList;

    public List<RuleFunctionDtoParameter> getFieldLists() {

        return JSONArray.parseArray(JSONObject.toJSONString(fieldList), RuleFunctionDtoParameter.class)
                .stream().map(s -> s.setSupportFunction(true).setInfo(s.getExplain()))
                .collect(Collectors.toList());
    }
}
