package cn.bctools.design.workflow.dto;

import cn.bctools.design.workflow.entity.FlowDesign;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: Zhu<PERSON>iaoKang
 * @Description: 工作流设计详情
 */
@Data
@Accessors(chain = true)
@ApiModel("工作流设计详情")
public class FlowDesignDetailDto extends FlowDesign {

    @ApiModelProperty(value = "工作流列表页挂载的菜单id")
    private String menuId;

}
