package cn.bctools.design.workflow.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: Zhu<PERSON>ia<PERSON><PERSON><PERSON>
 * @Description: 终止流程入参
 */
@Data
@Accessors(chain = true)
@ApiModel("终止流程入参")
public class StopTaskReqDto {

    @ApiModelProperty(value = "终止任务原因")
    private String reason;
}
