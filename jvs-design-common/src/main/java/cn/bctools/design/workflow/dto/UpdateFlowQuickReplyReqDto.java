package cn.bctools.design.workflow.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("修改工作流快捷回复入参")
public class UpdateFlowQuickReplyReqDto extends CreateFlowQuickReplyReqDto{
    @ApiModelProperty(value = "id", required = true)
    @NotBlank(message = "id不能为空")
    private String id;
}
