package cn.bctools.design.workflow.dto.progress;

import cn.bctools.design.workflow.model.Node;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: Zhu<PERSON>iaoKang
 * @Description: 任务进度响应-工作流设计JSON(含处理过程)
 */

@Data
@Accessors(chain = true)
@ApiModel("任务进度响应-工作流设计JSON(含处理过程)")
public class FlowDesignProgressDto extends Node {

    @ApiModelProperty(value = "当前节点")
    private Boolean currentNode = Boolean.FALSE;
}
