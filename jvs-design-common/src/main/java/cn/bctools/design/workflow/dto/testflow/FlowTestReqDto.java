package cn.bctools.design.workflow.dto.testflow;

import cn.bctools.design.workflow.dto.FlowReqDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Zhu<PERSON>iaoKang
 * @Description: 测试工作流处理操作信息入参
 */
@Data
@ApiModel("测试工作流处理操作信息入参")
public class FlowTestReqDto extends FlowReqDto {

    @ApiModelProperty(value = "执行用户id")
    private String userId;
}
