package cn.bctools.design.workflow.entity;

import cn.bctools.database.entity.po.BasalPo;
import cn.bctools.database.handler.Fastjson2TypeHandler;
import cn.bctools.design.workflow.entity.dto.FlowExtendDto;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @description 工作流设计
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("工作流设计")
@TableName(value = "jvs_flow_design", autoResultMap = true)
public class FlowDesign extends BasalPo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "工作流名称")
    @TableField("name")
    private String name;

    @ApiModelProperty("工作流流程设计JSON(发布的版本)")
    @TableField("design")
    private String design;

    @ApiModelProperty("工作流流程设计JSON(设计中的版本)")
    @TableField("designing")
    private String designing;

    @ApiModelProperty("数据模型id")
    @TableField("data_model_id")
    private String dataModelId;

    @ApiModelProperty("分类")
    @TableField("design_group")
    private String designGroup;

    @ApiModelProperty("图标")
    @TableField("icon")
    private String icon;

    @ApiModelProperty("扩展")
    @TableField(value = "extend", typeHandler = Fastjson2TypeHandler.class)
    private FlowExtendDto extend;

    @ApiModelProperty("描述")
    @TableField("description")
    private String description;

    @ApiModelProperty("应用ID")
    private String jvsAppId;

    @ApiModelProperty("是否已发布：0-未发布，1-已发布")
    @TableField("published")
    private Boolean published;

    @ApiModelProperty("流程设计是否已变更：0-未变更，1-已变更")
    @TableField("design_changed")
    private Boolean designChanged;

    @ApiModelProperty(value = "发起人表单id")
    @TableField(value = "form_id", updateStrategy = FieldStrategy.IGNORED)
    private String formId;

    @ApiModelProperty(value = "发起人表单版本")
    @TableField(value = "form_version", updateStrategy = FieldStrategy.IGNORED)
    private String formVersion;

    @ApiModelProperty("关联的逻辑设计id集合")
    @TableField(value = "rule_keys", typeHandler = Fastjson2TypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private List<String> ruleKeys;

    @ApiModelProperty("是否删除 0未删除  1已删除")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;

    @ApiModelProperty("列表设计iid")
    @TableField(value = "page_id", updateStrategy = FieldStrategy.IGNORED)
    private String pageId;
}
