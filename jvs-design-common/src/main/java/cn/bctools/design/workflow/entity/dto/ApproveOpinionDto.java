package cn.bctools.design.workflow.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 审批意见
 */
@Data
@Accessors(chain = true)
@ApiModel("审批意见")
public class ApproveOpinionDto {

    @ApiModelProperty(value = "意见内容")
    private String content;
}
