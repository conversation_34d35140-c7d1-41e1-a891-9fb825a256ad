package cn.bctools.design.workflow.entity.handler;

import cn.bctools.design.workflow.entity.dto.ParallelBranchDto;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Zhu<PERSON>iaoKang
 * @Description:  自定义typeHandler，支持List<ParallelBranchDto>
 */
public class ParallelBranchTypeHandler extends AbstractJsonTypeHandler<List<ParallelBranchDto>> {

    @Override
    protected List<ParallelBranchDto> parse(String json) {
        List<ParallelBranchDto> list = JSON.parseArray(json, ParallelBranchDto.class);
        return new ArrayList(list);
    }

    @Override
    protected String toJson(List<ParallelBranchDto> obj) {
        return JSON.toJSONString(obj, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.WriteNullListAsEmpty, JSONWriter.Feature.WriteNullStringAsEmpty);
   }
}
