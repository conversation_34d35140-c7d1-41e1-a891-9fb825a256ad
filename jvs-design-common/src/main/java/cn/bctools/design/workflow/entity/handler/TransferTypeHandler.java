package cn.bctools.design.workflow.entity.handler;

import cn.bctools.design.workflow.entity.dto.TransferDto;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;

import java.util.LinkedList;
import java.util.List;

/**
 * @Author: Zhu<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 自定义typeHandler，支持LinkedList<TransferDto>
 */
public class TransferTypeHandler  extends AbstractJsonTypeHandler<LinkedList<TransferDto>> {
    @Override
    protected LinkedList<TransferDto> parse(String json) {
        List<TransferDto> list = JSON.parseArray(json, TransferDto.class);
        return new LinkedList<>(list);
    }

    @Override
    protected String toJson(LinkedList<TransferDto> obj) {
        return JSON.toJSONString(obj, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.WriteNullListAsEmpty, JSONWriter.Feature.WriteNullStringAsEmpty);
    }

}
