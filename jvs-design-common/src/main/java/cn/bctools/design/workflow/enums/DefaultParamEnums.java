package cn.bctools.design.workflow.enums;

import cn.bctools.function.enums.JvsParamType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <PERSON><PERSON>ia<PERSON>K<PERSON>
 * @Description: 工作流默认公式参数
 */
@Getter
@AllArgsConstructor
public enum DefaultParamEnums {

    TASK_ID("defTaskId", "流程实例id", "流程实例id",JvsParamType.text),
    CREATE_TIME("defCreateTime", "流程发起时间", "流程发起时间", JvsParamType.date),
    CREATE_BY("defCreateBy", "流程发起人", "流程发起人", JvsParamType.text),
    ;

    private final String id;
    private final String name;
    private final String info;
    private final JvsParamType jvsParamType;

    public static DefaultParamEnums getById(String id) {
        for (DefaultParamEnums value : DefaultParamEnums.values()) {
            if (value.getId().equals(id)) {
                return value;
            }
        }
        return null;
    }
}
