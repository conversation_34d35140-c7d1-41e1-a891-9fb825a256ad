package cn.bctools.design.workflow.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <PERSON><PERSON>ia<PERSON><PERSON><PERSON>
 * @Description: 工作流在数据模型中的字段。
 *  不是所有字段都需要保存到模型中，部分字段只在查询时填充到填充到数据中
 */
@Getter
@AllArgsConstructor
public enum FlowDataFieldEnum {

    TASK_STATE("jvsFlowTaskState", "工作流状态"),
    TASK_PROGRESS("jvsFlowTaskProgress", "工作流进度"),
    TASK("jvsFlowTask", "工作流任务"),
    /**
     * 存储数据的所有工作流任务信息
     */
    TASK_HISTORY("jvsFlowTaskHistory", "历史工作流任务"),
    RULE_KEY("ruleKey", "逻辑key"),
    /**
     *  false-非测试数据，true-测试数据
     */
    JVS_MODEL_TEST("jvsModelTest", "是否是测试数据"),

    TASK_PERSON_IDS("jvsTaskPersonIds", "流程参与者id集合"),
    ;

    private String fieldKey;
    private String fieldName;
}
