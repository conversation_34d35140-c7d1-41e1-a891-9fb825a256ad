package cn.bctools.design.workflow.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: Zhu<PERSON>iaoKang
 * @Description: 代理状态
 */
@Getter
@AllArgsConstructor
public enum FlowTaskProxyStatusEnum implements IEnum {

    PENDING(1, "待生效"),
    EFFECTIVE(2, "代理中"),
    EXPIRED(3, "已过期"),
    REVOKED(4, "已撤销"),
    ;

    @EnumValue
    @JsonValue
    private final Integer value;
    private String desc;
}
