package cn.bctools.design.workflow.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 流转节点操作类型枚举
 */

@Getter
@AllArgsConstructor
public enum NodeOperationTypeEnum {

    /**
     * 审批处理类型
     */
    PASS("PASS", "同意"),
    //    REFUSE("REFUSE", "拒绝"),
    REFUSE("REFUSE", "不通过"),
    BACK("BACK", "回退"),
    SAVE("SAVE", "保存"),
    TRANSFER("TRANSFER", "转交"),
    APPEND("APPEND", "加签"),
    TERMINATED("TERMINATED", "终止"),
    SKIP("SKIP", "跳过"),
    ;

    @JsonValue
    private String value;
    private String desc;

}
