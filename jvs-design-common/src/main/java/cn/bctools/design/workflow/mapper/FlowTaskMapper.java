package cn.bctools.design.workflow.mapper;

import cn.bctools.design.workflow.dto.PendingApprovesResDto;
import cn.bctools.design.workflow.entity.FlowTask;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 工作流任务 Mapper 接口
 */
@Mapper
public interface FlowTaskMapper extends BaseMapper<FlowTask> {

    /**
     * 待我审批分页查询
     *
     * @param page
     * @param wrapper
     * @return
     */
    @Options(useCache = false)
    @Select(" SELECT t.*, taskPerson.node_id" +
            " FROM jvs_flow_task_person taskPerson inner JOIN jvs_flow_task t ON taskPerson.flow_task_id = t.id " +
            " ${ew.customSqlSegment} " +
            " ORDER BY t.create_time DESC")
    IPage<PendingApprovesResDto> pendingApprovePage(Page page, @Param("ew") Wrapper wrapper);

    /**
     * 待我审批查询
     *
     * @param wrapper
     * @return
     */
//    @Options(useCache = false)
    @Select(" SELECT t.*, taskPerson.node_id" +
            " FROM jvs_flow_task_person taskPerson inner JOIN jvs_flow_task t ON taskPerson.flow_task_id = t.id " +
            " ${ew.customSqlSegment} ")
    List<PendingApprovesResDto> pendingApproves(@Param("ew") Wrapper wrapper);

}
