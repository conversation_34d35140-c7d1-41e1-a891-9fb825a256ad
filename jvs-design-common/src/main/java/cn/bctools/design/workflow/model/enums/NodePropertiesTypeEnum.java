package cn.bctools.design.workflow.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 待审批人类型
 */
@Getter
@AllArgsConstructor
public enum NodePropertiesTypeEnum {

    /**
     * 待审批人类型
     */
    ASSIGN_USER("ASSIGN_USER", "指定人员"),
    SELF_SELECT("SELF_SELECT", "发起人自选"),
    LEADER_TOP("LEADER_TOP", "连续多级主管"),
    LEADER("LEADER", "主管"),
    ROLE("ROLE", "角色"),
    SELF("SELF", "发起人自己"),
    JOB("JOB", "岗位"),
    USER_FIELD("USER_FIELD", "成员字段"),
    ;

    private String value;
    private String desc;

}
