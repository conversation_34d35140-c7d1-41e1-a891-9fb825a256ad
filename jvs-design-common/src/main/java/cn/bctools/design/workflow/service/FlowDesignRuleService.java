package cn.bctools.design.workflow.service;

import cn.bctools.design.workflow.dto.rule.FlowDesignRuleCreateDto;

import javax.validation.Valid;

/**
 * @Author: ZhuXiaoKang
 * @Description: 提供给逻辑引擎调用的工作流设计服务
 */
public interface FlowDesignRuleService {


    /**
     * 生成或修改工作流设计
     *
     * @param design 工作流设计
     * @return 工作流设计id
     */
    String saveOrUpdateFlowDesign(@Valid FlowDesignRuleCreateDto design);

    /**
     * 删除工作流设计
     *
     * @param id 工作流设计id
     * @param appId 应用id
     */
    void deleteFlowDesign(String id, String appId);
}
