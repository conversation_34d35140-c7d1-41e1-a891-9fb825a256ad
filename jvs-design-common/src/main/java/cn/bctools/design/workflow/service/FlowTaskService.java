package cn.bctools.design.workflow.service;

import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.design.workflow.dto.FlowReqDto;
import cn.bctools.design.workflow.dto.PendingApprovesReqDto;
import cn.bctools.design.workflow.dto.PendingApprovesResDto;
import cn.bctools.design.workflow.dto.progress.ProgressDetailResDto;
import cn.bctools.design.workflow.dto.progress.ProgressPrintResDto;
import cn.bctools.design.workflow.entity.FlowDesign;
import cn.bctools.design.workflow.entity.FlowTask;
import cn.bctools.design.workflow.entity.FlowTaskProxy;
import cn.bctools.design.workflow.model.Node;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 工作流任务 服务类
 */
public interface FlowTaskService extends IService<FlowTask> {

    /**
     * 构造保存工作流任务数据
     *
     * @param flowDesignModelId 工作流设计原本的模型id
     * @param flowDesign        设计信息
     * @param design            要保存的工作流设计JSON
     * @param dataId            数据id
     * @param sendFormId        指定发起人节点表单id
     * @return 工作流任务信息
     */
    FlowTask buildSaveFlowTask(String flowDesignModelId, FlowDesign flowDesign, String design, String dataId, String sendFormId);

    /**
     * 保存工作流任务
     *
     * @param flowDesignModelId 工作流设计原本的模型id
     * @param flowDesign        工作流设计信息
     * @param design            工作流设计JSON
     * @param dataId            数据id
     * @return 工作流任务
     */
    FlowTask save(String flowDesignModelId, FlowDesign flowDesign, String design, String dataId);

    /**
     * 保存工作流任务
     *
     * @param userDto    用户信息
     * @param flowDesign 工作流设计信息
     * @param design     工作流设计JSON
     * @param dataId     数据id
     * @return 工作流任务
     */
    FlowTask saveTest(UserDto userDto, FlowDesign flowDesign, String design, String dataId);

    /**
     * 任务进度
     *
     * @param id     任务id
     * @param nodeId 节点id
     * @return
     */
    ProgressDetailResDto getProgressDetail(String id, String nodeId);

    /**
     * 获取任务进度集合
     *
     * @param id 任务id
     * @return
     */
    List<ProgressPrintResDto> getProgressPrint(String id);

    /**
     * 用户创建的任务数量
     *
     * @param userDto 用户
     * @return 用户创建的任务数量
     */
    Integer selfCreateCount(UserDto userDto);

    /**
     * 催办
     *
     * @param userDto
     * @param taskId
     */
    void urge(UserDto userDto, String taskId);

    /**
     * 分页查询待我审批的工作流任务
     *
     * @param page    分页
     * @param userDto 当前登陆人
     * @param dto     查询条件
     */
    void pendingApprovePage(Page<PendingApprovesResDto> page, UserDto userDto, PendingApprovesReqDto dto);

    /**
     * 查询指定模型待我审批的工作流任务
     *
     * @param userDto 当前登陆人
     * @param dto     查询条件
     * @return
     */
    List<PendingApprovesResDto> pendingApproves(UserDto userDto, PendingApprovesReqDto dto,String dataModelId);


    /**
     * 离职代理
     *
     * @param flowTaskProxy 代理配置
     */
    void departTransfer(FlowTaskProxy flowTaskProxy);

    /**
     * 修改流转中的工作流任务流程设计
     *
     * @param flowTask 工作流任务
     * @param flowDto  流转入参
     */
    void updateDesignApprover(FlowTask flowTask, FlowReqDto flowDto);

    /**
     * 工作流设计运行中的流程数量
     *
     * @param designId 工作流设计id
     * @return 工作流设计运行中的流程数量
     */
    int countPendingByDesignId(String designId);

    /**
     * 检查是否可显示重启按钮
     *
     * @param userId     用户Id
     * @param flowTaskId 任务id
     * @return TRUE-可显示，FALSE-不可显示
     */
    boolean checkCanRestart(String userId, String flowTaskId);

    /**
     * 检查是否可显示取消按钮
     *
     * @param userId     用户Id
     * @param flowTaskId 任务id
     * @return TRUE-可显示，FALSE-不可显示
     */
    boolean checkCancel(String userId, String flowTaskId);

    /**
     * 清除工作流执行过程数据
     *
     * @param taskId 工作流任务id
     */
    void cleanTaskExecutiveProcess(String taskId);

    /**
     * 是否有未结束的任务
     *
     * @param dataId 数据id
     * @return true-有未结束的任务，false-任务都已结束
     */
    Boolean havePendingTask(String dataId);

    /**
     * 查询未结束的任务
     *
     * @param dataId 业务id
     * @return 任务
     */
    FlowTask getPendingTask(String dataId);

    /**
     * 根据数据id获取任务
     *
     * @param dataIds 数据id集合
     * @return 任务集合
     */
    List<FlowTask> listByDataIds(List<String> dataIds);

    /**
     * 根据数据id删除任务
     *
     * @param dataIds 数据id集合
     */
    void removeTaskAllByDataId(List<String> dataIds);

    /**
     * 更新工作流可审核角色（包括曾经可审核）
     *
     * @param flowTaskId 工作流任务id
     * @param node 流转执行节点
     */
    void updateDesignAuditRoles(String flowTaskId, Node node);

    Map<String, Set<String>> queryAuditRoleByTaskId(List<String> taskIds);
}
