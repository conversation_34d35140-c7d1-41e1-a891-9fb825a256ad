package cn.bctools.design.workflow.service.impl;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.design.workflow.dto.rule.FlowDesignRuleCreateDto;
import cn.bctools.design.workflow.entity.FlowDesign;
import cn.bctools.design.workflow.entity.dto.FlowExtendDto;
import cn.bctools.design.workflow.model.Node;
import cn.bctools.design.workflow.service.FlowDesignRuleService;
import cn.bctools.design.workflow.service.FlowDesignService;
import cn.bctools.design.workflow.utils.FlowDynamicUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author: Zhu<PERSON>iaoKang
 * @Description: 提供给逻辑引擎调用的工作流设计服务
 */
@Deprecated
@Validated
@Service
@AllArgsConstructor
public class FlowDesignRuleServiceImpl implements FlowDesignRuleService {

    private final FlowDesignService flowDesignService;

    @Override
    public String saveOrUpdateFlowDesign(@Valid FlowDesignRuleCreateDto design) {
        // 构造基础数据
        FlowDesign flowDesign = BeanCopyUtil.copy(design, FlowDesign.class);
        if (StringUtils.isBlank(design.getId())) {
            flowDesign.setCreateBy(design.getUserName());
            flowDesign.setCreateById(design.getUserId());
        }
        flowDesign.setUpdateBy(design.getUserName());
        flowDesign.setCreateById(design.getUserId());
        flowDesign.setExtend(new FlowExtendDto());
        // 根据节点生成工作流设计
        List<Node> nodes = BeanCopyUtil.copys(design.getNodeObjs(), Node.class);
        String designing = FlowDynamicUtil.buildSerial(nodes);
        // 新增或修改工作流设计
        flowDesign.setDesigning(designing);
        flowDesignService.saveOrUpdate(flowDesign);
        // 发布设计
        flowDesignService.published(flowDesign.getId(), flowDesign.getJvsAppId());
        return flowDesign.getId();
    }

    @Override
    public void deleteFlowDesign(String id, String appId) {
        flowDesignService.delete(id, appId);
    }

}
