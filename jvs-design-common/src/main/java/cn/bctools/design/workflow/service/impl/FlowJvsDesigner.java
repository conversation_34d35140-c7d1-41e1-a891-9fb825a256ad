package cn.bctools.design.workflow.service.impl;

import cn.bctools.design.project.handler.IJvsDesigner;
import cn.bctools.design.workflow.entity.*;
import cn.bctools.design.workflow.service.*;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @Author: Zhu<PERSON>iaoKang
 * @Description: 工作流设计套件实现
 */
@Service
@AllArgsConstructor
public class FlowJvsDesigner implements IJvsDesigner {

    private final FlowDesignService flowDesignService;
    private final FlowTaskService flowTaskService;
    private final FlowTaskCarbonCopyService flowTaskCarbonCopyService;
    private final FlowTaskNodeService flowTaskNodeService;
    private final FlowTaskPersonService flowTaskPersonService;
    private final FlowTaskApprovalRecordService flowTaskApprovalRecordService;


    @Override
    public void delete(String appId, String designId) {
        flowDesignService.remove(Wrappers.<FlowDesign>lambdaQuery().eq(FlowDesign::getJvsAppId, appId).eq(FlowDesign::getId, designId));
    }

    @Override
    public void beforeAppDeleted(String appId) {
        // 删除应用下工作流相关所有数据
        flowDesignService.remove(Wrappers.<FlowDesign>lambdaQuery().eq(FlowDesign::getJvsAppId, appId));
        flowTaskPersonService.remove(Wrappers.<FlowTaskPerson>lambdaQuery().eq(FlowTaskPerson::getJvsAppId, appId));
        flowTaskNodeService.remove(Wrappers.<FlowTaskNode>lambdaQuery().eq(FlowTaskNode::getJvsAppId, appId));
        flowTaskCarbonCopyService.remove(Wrappers.<FlowTaskCopied>lambdaQuery().eq(FlowTaskCopied::getJvsAppId, appId));
        flowTaskService.remove(Wrappers.<FlowTask>lambdaQuery().eq(FlowTask::getJvsAppId, appId));
        flowTaskApprovalRecordService.remove(Wrappers.<FlowTaskApprovalRecord>lambdaQuery().eq(FlowTaskApprovalRecord::getJvsAppId, appId));
    }
}
