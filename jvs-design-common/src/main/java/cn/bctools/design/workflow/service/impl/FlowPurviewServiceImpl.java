package cn.bctools.design.workflow.service.impl;

import cn.bctools.auth.api.dto.SearchUserDto;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.database.util.SqlFunctionUtil;
import cn.bctools.design.project.entity.JvsApp;
import cn.bctools.design.project.service.JvsAppService;
import cn.bctools.design.workflow.entity.FlowDesign;
import cn.bctools.design.workflow.entity.FlowPurview;
import cn.bctools.design.workflow.mapper.FlowPurviewMapper;
import cn.bctools.design.workflow.model.Node;
import cn.bctools.design.workflow.model.enums.PurviewGroupEnum;
import cn.bctools.design.workflow.model.enums.PurviewPersonTypeEnum;
import cn.bctools.design.workflow.model.enums.TargetObjectTypeEnum;
import cn.bctools.design.workflow.model.properties.Purview;
import cn.bctools.design.workflow.service.FlowPurviewService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 工作流权限配置 服务实现类
 */
@Service
@AllArgsConstructor
public class FlowPurviewServiceImpl extends ServiceImpl<FlowPurviewMapper, FlowPurview> implements FlowPurviewService {

    private final JvsAppService jvsAppService;


    @Override
    public void save(FlowDesign flowDesign, Node node) {
        String flowDesignId = flowDesign.getId();
        List<Purview> purviews = Optional.ofNullable(node.getProps().getPurviews()).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(purviews)) {
            // 未配置权限，则设置默认权限
            purviews.add(Purview.defaultPurview());
        }
        List<FlowPurview> flowPurviews = new ArrayList<>();
        purviews.forEach(purview -> {
            List<String> userIds = getStartPurviewIds(purview, TargetObjectTypeEnum.user);
            List<String> deptIds = getStartPurviewIds(purview, TargetObjectTypeEnum.dept);
            List<String> roleIds = getStartPurviewIds(purview, TargetObjectTypeEnum.role);
            List<String> jobIds = getStartPurviewIds(purview, TargetObjectTypeEnum.job);
            flowPurviews.add(new FlowPurview()
                    .setFlowDesignId(flowDesignId)
                    .setJvsAppId(flowDesign.getJvsAppId())
                    .setPurviewGroup(purview.getGroup())
                    .setPersonType(purview.getPersonType())
                    .setUsers(userIds)
                    .setDepts(deptIds)
                    .setRoles(roleIds)
                    .setJobs(jobIds));
        });
        // 删除旧的权限配置
        remove(Wrappers.<FlowPurview>lambdaQuery().eq(FlowPurview::getFlowDesignId, flowDesignId));
        // 保存新的权限配置
        saveBatch(flowPurviews);
    }

    /**
     * 解析工作流设计，得到可发起人id/部门id集合
     *
     * @param type 可发起人对象类型
     * @return
     */
    private List<String> getStartPurviewIds(Purview purview, TargetObjectTypeEnum type) {
        List<String> ids = purview.getPersonnelIdByType(type);
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        return ids;
    }

    /**
     * 构造查询用户指定权限组权限的基础sql
     *
     * @param purviewGroupEnum
     * @param user
     * @return
     */
    private LambdaQueryWrapper<FlowPurview> basePermissionCondition(PurviewGroupEnum purviewGroupEnum, UserDto user) {
        // 查询发起人信息
        SearchUserDto search = new SearchUserDto();
        search.setUserIds(Collections.singletonList(user.getId()));
        String roleIdSql = roleIdsConditionSql(user.getRoleIds());
        return Wrappers.<FlowPurview>lambdaQuery()
                .eq(FlowPurview::getPurviewGroup, purviewGroupEnum)
                .and(wrapper -> wrapper.eq(FlowPurview::getPersonType, PurviewPersonTypeEnum.all)
                        .or(orUser -> orUser.apply(SqlFunctionUtil.jsonContains("users", user.getId(), "$")))
                        .or(StringUtils.isNotBlank(user.getDeptId()), orDept -> orDept.apply(SqlFunctionUtil.jsonContains("depts", user.getDeptId(), "$")))
                        .or(StringUtils.isNotBlank(roleIdSql), orRole -> orRole.apply(roleIdSql))
                        .or(StringUtils.isNotBlank(user.getJobId()), orJob -> orJob.apply(SqlFunctionUtil.jsonContains("jobs", user.getJobId(), "$")))
                );
    }

    @Override
    public List<String> havePermissionDesign(PurviewGroupEnum purviewGroupEnum, UserDto userDto) {
        // 查询发起人信息
        LambdaQueryWrapper<FlowPurview> lambdaQueryWrapper = basePermissionCondition(purviewGroupEnum, userDto);
        lambdaQueryWrapper.select(FlowPurview::getFlowDesignId, FlowPurview::getJvsAppId);
        List<FlowPurview> flowPurviews = list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(flowPurviews)) {
            return Collections.emptyList();
        }
        // 查询已发布的应用
        List<String> jvsAppIds = flowPurviews.stream().map(FlowPurview::getJvsAppId).distinct().collect(Collectors.toList());
        List<String> deployAppIds = jvsAppService.list(Wrappers.<JvsApp>lambdaQuery()
                .in(JvsApp::getId, jvsAppIds)
                .eq(JvsApp::getIsDeploy, Boolean.TRUE)
                .select(JvsApp::getId)).stream().map(JvsApp::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deployAppIds)) {
            return Collections.emptyList();
        }
        return flowPurviews.stream()
                .filter(p -> deployAppIds.contains(p.getJvsAppId()))
                .map(FlowPurview::getFlowDesignId)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 封装角色id查询sql
     *
     * @param roleIds
     * @return
     */
    private String roleIdsConditionSql(List<String> roleIds) {
        StringBuilder queryRoleIdStr = new StringBuilder();
        if (ObjectNull.isNotNull(roleIds)) {
            for (int i = 0; i < roleIds.size(); i++) {
                queryRoleIdStr.append(SqlFunctionUtil.jsonContains("roles", roleIds.get(i), "$"));
                if (i != roleIds.size() - 1) {
                    queryRoleIdStr.append(" OR ");
                }
            }
        }
        return queryRoleIdStr.toString();
    }

    @Override
    public void delete(String flowDesignId) {
        remove(Wrappers.<FlowPurview>lambdaQuery().eq(FlowPurview::getFlowDesignId, flowDesignId));
    }
}
