package cn.bctools.design.workflow.service.impl;

import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.auth.api.dto.SearchUserDto;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.workflow.dto.DataDto;
import cn.bctools.design.workflow.dto.FlowReqDto;
import cn.bctools.design.workflow.entity.FlowTask;
import cn.bctools.design.workflow.entity.FlowTaskNode;
import cn.bctools.design.workflow.entity.FlowTaskPath;
import cn.bctools.design.workflow.entity.FlowTaskPerson;
import cn.bctools.design.workflow.entity.dto.FlowPathNodeDto;
import cn.bctools.design.workflow.entity.enums.FlowTaskStatusEnum;
import cn.bctools.design.workflow.entity.enums.ProcessStatusEnum;
import cn.bctools.design.workflow.enums.FlowDataFieldEnum;
import cn.bctools.design.workflow.model.Node;
import cn.bctools.design.workflow.model.enums.NodePropertiesEndConditionEnum;
import cn.bctools.design.workflow.model.enums.NodePropertiesModeEnum;
import cn.bctools.design.workflow.model.enums.TargetObjectTypeEnum;
import cn.bctools.design.workflow.model.properties.AutoApproval;
import cn.bctools.design.workflow.model.properties.Leader;
import cn.bctools.design.workflow.model.properties.Target;
import cn.bctools.design.workflow.service.*;
import cn.bctools.design.workflow.support.FlowResult;
import cn.bctools.design.workflow.support.RuntimeData;
import cn.bctools.design.workflow.support.context.FlowContext;
import cn.bctools.design.workflow.support.enums.FlowNextTypeEnum;
import cn.bctools.design.workflow.support.function.dto.AutoApprovalDto;
import cn.bctools.design.workflow.support.function.impl.AutoApprovalFunction;
import cn.bctools.design.workflow.support.function.impl.TaskPersonFunction;
import cn.bctools.design.workflow.support.impl.CompositeFlowHandler;
import cn.bctools.design.workflow.support.process.ProcessResult;
import cn.bctools.design.workflow.support.valid.SpNodeValidated;
import cn.bctools.design.workflow.utils.FlowContextUtil;
import cn.bctools.design.workflow.utils.FlowPathUtil;
import cn.bctools.design.workflow.utils.FlowTaskNodeUtil;
import cn.bctools.design.workflow.utils.FlowUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class FlowTaskRePendServiceImpl implements FlowTaskRePendService {

    private final FlowTaskService flowTaskService;
    private final FlowTaskNodeService flowTaskNodeService;
    private final FlowTaskPersonService flowTaskPersonService;
    private final FlowTaskPathService flowTaskPathService;
    private final AuthUserServiceApi authUserServiceApi;
    private final FlowDynamicDataService flowDynamicDataService;
    private final TaskPersonFunction taskPersonFunction;
    private final AutoApprovalFunction autoApprovalFunction;
    private final CompositeFlowHandler compositeFlowHandler;

    @Override
    public void toPending(FlowReqDto flowDto) {
        FlowTask flowTask = flowTaskService.getById(flowDto.getId());
        if (!FlowTaskStatusEnum.PASSED.equals(flowTask.getTaskStatus())) {
            throw new BusinessException("任务未结束");
        }

        String nodeId = flowTask.getCourses().getLast().getNodeId();
        Node node = Optional.ofNullable(FlowUtil.findNode(flowTask.getFlowDesign(), nodeId)).orElseThrow(() -> new BusinessException("获取节点信息失败"));

        //修改mongodb任务状态
        flowDto.getData().put(FlowDataFieldEnum.TASK_STATE.getFieldKey(), "待审批");
        flowDto.getData().put(FlowDataFieldEnum.TASK_PROGRESS.getFieldKey(), node.getName() + "处理中");
        DataDto dataDto = flowDynamicDataService.updateModelData(flowTask.getJvsAppId(), flowDto.getData(), flowTask.getDataModelId(), flowTask.getDataId());

        //新增jvs_flow_task_path数据
        List<List<Node>> paths = FlowPathUtil.getNodePaths(flowTask.getFlowDesign());
        List<FlowTaskPath> flowTaskPaths = paths.stream()
                .map(path -> new FlowTaskPath().setFlowTaskId(flowTask.getId()).setPath(BeanCopyUtil.copys(path, FlowPathNodeDto.class)).setJvsAppId(flowTask.getJvsAppId()))
                .collect(Collectors.toList());
        flowTaskPathService.saveBatch(flowTaskPaths);

        RuntimeData runtimeData = new RuntimeData();
        runtimeData.setFlowTask(flowTask);
        runtimeData.setData(flowDto.getData());
        runtimeData.setNodeId(node.getId());
        runtimeData.setCurrentNode(node);

        FlowUtil.parseNodeJsonAndCache(flowTask.getFlowDesign());

        //更新flow_task状态为待审批
        flowTask.setTaskStatus(FlowTaskStatusEnum.PENDING);

        //获取可审批人员
        List<UserDto> userDtos = taskPersonFunction.invoke(node, runtimeData);

        //自动执行
        Boolean flag = whetherSavePerson(node, runtimeData, userDtos);
        if (!flag) {
            FlowTaskNode flowTaskNode = flowTaskNodeService.saveNextNode(node, flowTask);
            runtimeData.setFlowTaskNode(flowTaskNode);
            FlowContextUtil.context().init();
            FlowContextUtil.refreshContext(runtimeData);
            FlowContextUtil.refreshContext(runtimeData.getFlowTaskNode());
            FlowContextUtil.refreshContext(ProcessResult.buildEndProcess());
            FlowContextUtil.refreshContext(flowTaskNode);
            //设置流程结果，因为是最后一个节点，所以默认结束
            FlowContextUtil.refreshContext(new FlowResult()
                    .setFlowNextTypeEnum(FlowNextTypeEnum.END)
                    .setNode(null)
                    .setOver(true)
                    .setPassRate(0L));
            autoApproval();
        } else {
            //非自动执行
            if (ObjectNull.isNull(userDtos)) {
                throw new BusinessException("未找到可审批人员");
            }

            if (ObjectNull.isNotNull(userDtos) && userDtos.size() > 100) {
                throw new BusinessException("审核流程异常，请联系系统管理员");
            }

            //保存流程信息
            flowTask.getCourses().removeLast();
            flowTask.getFlowManualNodes().removeLast();
            flowTaskService.updateById(flowTask);

            //更新jvs_flow_task_node
            flowTaskNodeService.saveNextNode(node, flowTask, userDtos);

            //更新jvs_flow_task_node
            // 保存下级节点待审批人
            List<FlowTaskPerson> flowTaskPersons = new ArrayList<>();
            int i = 0;
            for (UserDto user : userDtos) {
                FlowTaskPerson person = new FlowTaskPerson();
                person.setNodeId(node.getId());
                person.setFlowTaskId(flowTask.getId());
                person.setUserId(user.getId());
                person.setUserName(user.getRealName());
                person.setProcessStatus(ProcessStatusEnum.PENDING);
                person.setTest(flowTask.getTest());
                // 依次审批，则第一个用户设为待处理状态，其它用户设为准备中状态
                if (NodePropertiesModeEnum.NEXT.equals(node.getProps().getMode()) && i != 0) {
                    person.setProcessStatus(ProcessStatusEnum.PREPARE);
                }
                person.setNumber(i++);
                person.setJvsAppId(flowTask.getJvsAppId());

                flowTaskPersons.add(person);
            }

            flowTaskPersonService.saveBatch(flowTaskPersons);
        }
    }

    private void autoApproval() {

        FlowContext flowContext = FlowContextUtil.context().getContext();
        AutoApprovalDto autoApprovalDto = autoApprovalFunction.invoke(flowContext.getRuntimeData().getCurrentNode(), flowContext.getFlowResult());
        if (Boolean.FALSE.equals(autoApprovalDto.getEnable())) {
            return;
        }
        autoApprovalDto.getAutoTasks().forEach(autoTask -> {
            compositeFlowHandler.execute(autoTask.getCurrentNode(), autoTask);
        });
    }

    /**
     * 是否需要新增审批人
     *
     * @param node        下一节点
     * @param runtimeData 运行时数据
     * @param userDtos    用户信息列表
     */
    private Boolean whetherSavePerson(Node node, RuntimeData runtimeData, List<UserDto> userDtos) {
        if (ObjectNull.isNull(userDtos)) {
            userDtos = taskPersonFunction.invoke(node, runtimeData);
        }
        AutoApproval autoApproval = FlowUtil.getAutoApproval(node, runtimeData);
        if (ObjectNull.isNull(autoApproval)) {
            return true;
        }
        boolean savePerson = true;
        if (autoApproval.getSelfAuto() || autoApproval.getAdjacentNode()) {
        } else if (ObjectNull.isNull(userDtos) && autoApproval.getNoApproverAutoPass()) {
            savePerson = false;
        } else if (autoApproval.getSkipNode()) {
            savePerson = false;
        }
        return savePerson;
    }
}
