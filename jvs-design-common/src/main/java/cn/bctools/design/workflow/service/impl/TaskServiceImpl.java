package cn.bctools.design.workflow.service.impl;

import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.function.Get;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.design.workflow.dto.CanBackNodeDto;
import cn.bctools.design.workflow.dto.DataDto;
import cn.bctools.design.workflow.dto.FlowReqDto;
import cn.bctools.design.workflow.dto.startflow.StartFlowReqDto;
import cn.bctools.design.workflow.dto.startflow.StartFlowResDto;
import cn.bctools.design.workflow.dto.startflow.StartFlowVariables;
import cn.bctools.design.workflow.entity.FlowDesign;
import cn.bctools.design.workflow.entity.FlowTask;
import cn.bctools.design.workflow.entity.FlowTaskNode;
import cn.bctools.design.workflow.entity.dto.FlowExtendDto;
import cn.bctools.design.workflow.entity.dto.FlowManualNode;
import cn.bctools.design.workflow.entity.enums.FlowTaskStatusEnum;
import cn.bctools.design.workflow.enums.BackScopeEnum;
import cn.bctools.design.workflow.model.Node;
import cn.bctools.design.workflow.model.NodeForm;
import cn.bctools.design.workflow.model.NodeProperties;
import cn.bctools.design.workflow.model.enums.NodeTypeEnum;
import cn.bctools.design.workflow.model.enums.NodeTypeGroupEnum;
import cn.bctools.design.workflow.model.properties.BackProperties;
import cn.bctools.design.workflow.service.*;
import cn.bctools.design.workflow.support.ExecuteTask;
import cn.bctools.design.workflow.support.RuntimeData;
import cn.bctools.design.workflow.support.RuntimeService;
import cn.bctools.design.workflow.support.StartTask;
import cn.bctools.design.workflow.utils.FlowPathUtil;
import cn.bctools.design.workflow.utils.FlowUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @Author: ZhuXiaoKang
 * @Description:
 */
@Component
@AllArgsConstructor
public class TaskServiceImpl implements TaskService {

    private final FlowDesignService flowDesignService;
    private final FlowTaskService flowTaskService;
    private final FlowTaskNodeService flowTaskNodeService;
    private final FlowTaskPersonService flowTaskPersonService;
    private final RuntimeService runtimeService;
    private final FlowDynamicDataService flowDynamicDataService;
    private final FlowTaskPathService flowTaskPathService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public StartFlowResDto start(UserDto userDto, StartFlowVariables variables) {
        // 1. 准备可发起工作流的设计
        FlowDesign flowDesign = Optional.ofNullable(flowDesignService.getOne(Wrappers.<FlowDesign>lambdaQuery()
                .eq(FlowDesign::getId, variables.getId())
                .eq(FlowDesign::getPublished, Boolean.TRUE))
        ).orElseThrow(() -> new BusinessException("工作流不存在或未发布"));
        String flowDesignModelId = flowDesign.getDataModelId();
        String dataId = variables.getDataId();
        String modelId = variables.getModelId();
        String dataVersion = variables.getDataVersion();

        // 默认使用工作流设计的模型id，若调用方传递了模型id，则使用该指定模型
        String dataModelId = StringUtils.isNotBlank(modelId) ? modelId : flowDesign.getDataModelId();
        flowDesign.setDataModelId(dataModelId);
        // 发起人自选审核人修改配置
        String design = FlowUtil.setSelfSelectApprover(true, flowDesign.getDesign(), flowDesign.getExtend(), variables.getApprovers());

        // 2. 保存业务数据
        JSONObject data = Optional.ofNullable(variables.getData()).orElse(new JSONObject());
        if (ObjectNull.isNull(dataId)) {
            DataDto dataDto = flowDynamicDataService.saveModelData(flowDesign.getJvsAppId(), flowDesign.getDataModelId(), data);
            dataId = dataDto.getDataId();
            dataVersion = dataDto.getVersion();
            data = dataDto.getData();
        }

        FlowTask flowTask = flowTaskService.buildSaveFlowTask(flowDesignModelId, flowDesign, design, dataId, variables.getSendFormId());
        flowTask.setCreateTime(LocalDateTime.now());
        flowTask.setCreateBy(userDto.getRealName());
        flowTask.setCreateById(userDto.getId());

        // 3. 执行流程
        StartTask startTask = new StartTask();
        startTask.setUser(userDto);
        startTask.setFlowTask(flowTask);
        startTask.setFlowExtend(flowDesign.getExtend());
        startTask.setAddNode(variables.getNode());
        startTask.setData(data);
        startTask.setDataVersion(dataVersion);
        RuntimeData runtimeData = runtimeService.start(startTask);
        return new StartFlowResDto().setFlowTaskId(flowTask.getId()).setData(runtimeData.getData()).setFlowTask(flowTask);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public FlowTask execute(FlowReqDto flowDto, UserDto userDto) {
        // 1. 必要的校验
        // 得到任务正在处理的任务节点
        FlowTaskNode currentTaskNode = flowTaskNodeService.getCurrentPendingNode(flowDto.getId(), flowDto.getNodeId());
        if (ObjectUtil.isNull(currentTaskNode)) {
            throw new BusinessException("任务不存在或已结束");
        }
        // 得到工作流任务
        FlowTask flowTask = Optional.ofNullable(flowTaskService.getOne(Wrappers.<FlowTask>lambdaQuery()
                .eq(FlowTask::getId, flowDto.getId())
                .eq(FlowTask::getTaskStatus, FlowTaskStatusEnum.PENDING))
        ).orElseThrow(() -> new BusinessException("任务不存在或已结束"));
        // 当前处理的节点id
        String nodeId = currentTaskNode.getNodeId();
        // 当前处理的节点id与应处理的节点id不同，直接返回
        if (Boolean.FALSE.equals(nodeId.equals(flowDto.getNodeId()))) {
            throw new BusinessException("任务不存在或已结束");
        }
        // 获取节点信息
        Node node = Optional.ofNullable(FlowUtil.findNode(flowTask.getFlowDesign(), currentTaskNode.getNodeId())).orElseThrow(() -> new BusinessException("获取节点信息失败"));

        // 判断审核时间限制
        if (Optional.ofNullable(node.getProps())
                .map(NodeProperties::getAllowTimeRange)
                .filter(tr -> tr.size() == 2)
                .map(tr -> {
                    LocalDateTime now = LocalDateTimeUtil.now();
                    LocalDateTime startTime = tr.get(0);
                    LocalDateTime endTime = tr.get(1);
                    return now.isBefore(startTime) || now.isAfter(endTime);
                })
                .orElse(false)){
            throw new BusinessException("不在可审核时间范围内，不可操作");
        }

        // 若是人工节点，校验审核权限
        if (NodeTypeGroupEnum.MANUAL.equals(node.getType().getGroup())) {
            // 校验用户是否有指定任务的待审批的任务
            checkPendingTask(flowDto.getId(), nodeId, userDto.getId());
        }

        DynamicDataUtils.clearEcho(flowDto.getData());
        // 2. 不需要流转的处理
        FlowExtendDto flowExtend = flowDesignService.getFlowExtend(flowTask.getFlowDesignId());
        ExecuteTask executeTask = new ExecuteTask();
        executeTask.setUser(userDto);
        executeTask.setNodeId(nodeId);
        executeTask.setCurrentNode(node);
        executeTask.setFlowDto(flowDto);
        executeTask.setData(flowDto.getData());
        executeTask.setFlowTaskNode(currentTaskNode);
        executeTask.setFlowExtend(flowExtend);
        executeTask.setAddNode(flowDto.getNode());
        executeTask.setFlowTask(flowTask);

        // 不处理任务流转的操作,直接结束
        if (noCirculation(executeTask)) {
            return flowTask;
        }

        // 修改数据
        if (ObjectNull.isNotNull(flowDto.getData())) {
            flowDto.getData().put(Get.name(FlowReqDto::getNodeOperationType), flowDto.getNodeOperationType());
            DataDto dataDto = flowDynamicDataService.updateModelData(flowTask.getJvsAppId(), flowDto.getData(), flowTask.getDataModelId(), flowTask.getDataId());
            executeTask.setDataVersion(dataDto.getVersion());
            executeTask.setData(dataDto.getData());
        }

        // 3. 流转处理
        // 修改流转中的工作流任务流程设计中的审批人
        flowTaskService.updateDesignApprover(flowTask, flowDto);
        executeTask.setFlowTask(flowTask);
        // 执行流程
        runtimeService.execute(executeTask);
        return flowTask;
    }

    @Transactional(rollbackFor = Exception.class)
//    @Async
    @Override
    public Future<Boolean> batchExecute(FlowReqDto flowReqDto, UserDto userDto) {
        FlowTask flowTask = Optional.ofNullable(flowTaskService.getOne(Wrappers.<FlowTask>lambdaQuery()
                .eq(FlowTask::getId, flowReqDto.getId()))
        ).orElseThrow(() -> new BusinessException("任务不存在或已结束"));

        // 因为更新数据时，需要根据设计id得到字段，而相同的字段名可能存在不同的设计，导致找不到当前审批表单对应的字段
        String formId = "";
        NodeForm node = FlowUtil.getNodeForm(flowTask.getFlowDesign(), flowReqDto.getNodeId());
        if (node.getSendUserForm()) {
            NodeForm rootNode = FlowUtil.getNodeForm(flowTask.getFlowDesign(), NodeTypeEnum.ROOT.getDefaultNodeId());
            formId = rootNode.getFormId();
        } else {
            formId = node.getFormId();
        }
        FlowUtil.clearNodeCache();
        if (ObjectNull.isNotNull(formId)) {
            DynamicDataUtils.setDesignId(formId);
        }
        // 获取数据
        Map<String, Object> data = flowDynamicDataService.querySingle(flowTask.getDataModelId(), flowTask.getDataId());
        flowReqDto.setData(Convert.convert(JSONObject.class, data));
        // 执行流程
        execute(flowReqDto, userDto);
        return new AsyncResult(Boolean.TRUE);
    }

    /**
     * 校验用户是否有指定任务的待审批的任务
     *
     * @param flowTaskId 任务id
     * @param nodeId     节点id
     * @param userId     用户id
     */
    private void checkPendingTask(String flowTaskId, String nodeId, String userId) {
        if (Boolean.FALSE.equals(flowTaskPersonService.checkPendingTask(flowTaskId, nodeId, userId))) {
            throw new BusinessException("已审核或无权审核");
        }
    }

    /**
     * 不流转任务但要保存数据的处理
     *
     * @param executeTask 任务流转参数
     * @return TRUE-不继续流转， FALSE-继续流转
     */
    private Boolean noCirculation(ExecuteTask executeTask) {
        if (ObjectNull.isNull(executeTask.getFlowDto().getNodeOperationType())) {
            return Boolean.FALSE;
        }
        switch (executeTask.getFlowDto().getNodeOperationType()) {
            case SAVE:
                // 保存数据
                FlowTask flowTask = executeTask.getFlowTask();
                FlowReqDto flowDto = executeTask.getFlowDto();
                DataDto dataDto = flowDynamicDataService.updateModelData(flowTask.getJvsAppId(), flowDto.getData(), flowTask.getDataModelId(), flowTask.getDataId());
                // 修改当前节点的数据版本
                FlowTaskNode flowTaskNode = executeTask.getFlowTaskNode();
                flowTaskNode.getCourse().setDataVersion(dataDto.getVersion());
                flowTaskNodeService.updateById(flowTaskNode);
                return Boolean.TRUE;
            default:
                return Boolean.FALSE;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public StartFlowResDto restartTask(StartFlowReqDto dto, UserDto userDto) {
        // 未结束的任务不可重启
        FlowTask flowTask = Optional.ofNullable(flowTaskService.getById(dto.getId())).orElseThrow(() -> new BusinessException("任务不存在"));
        if (FlowTaskStatusEnum.PENDING.equals(flowTask.getTaskStatus())) {
            throw new BusinessException("未结束的任务不能重启");
        }
        if (Boolean.FALSE.equals(userDto.getId().equals(flowTask.getCreateById()))) {
            throw new BusinessException("只能重启自己发起的任务");
        }
        FlowDesign flowDesign = Optional.ofNullable(flowDesignService.getById(flowTask.getFlowDesignId())).orElseThrow(() -> new BusinessException("工作流已不存在"));
        // 修改任务数据
        // 发起人自选审核人修改配置
        String design = FlowUtil.setSelfSelectApprover(false, flowTask.getFlowDesign(), flowDesign.getExtend(), dto.getApprovers());
        flowTask.setTaskStatus(FlowTaskStatusEnum.PENDING);
        flowTask.setFlowDesign(design);
        flowTaskService.updateById(flowTask);

        // 修改数据
        DataDto dataDto = flowDynamicDataService.updateModelData(flowTask.getJvsAppId(), dto.getData(), flowTask.getDataModelId(), flowTask.getDataId());

        // 保存开始节点任务
        FlowUtil.parseNodeJsonAndCache(flowTask.getFlowDesign());
        Node rootNode = FlowUtil.findNode(null, Boolean.TRUE);
        flowTaskNodeService.saveNextNode(rootNode, flowTask);
        // 执行流程
        ExecuteTask executeTask = new ExecuteTask();
        executeTask.setUser(userDto);
        executeTask.setFlowTask(flowTask);
        executeTask.setData(dataDto.getData());
        executeTask.setDataVersion(dataDto.getVersion());
        executeTask.setNodeId(rootNode.getId());
        RuntimeData runtimeData = runtimeService.execute(executeTask);
        return new StartFlowResDto().setFlowTaskId(flowTask.getId()).setData(runtimeData.getData()).setFlowTask(flowTask);
    }

    @Override
    public List<CanBackNodeDto> getCanBackNode(String taskId, String nodeId) {
        // 获取已处理的人工节点
        FlowTask flowTask = flowTaskService.getById(taskId);
        LinkedList<FlowManualNode> manualNodes = flowTask.getFlowManualNodes();
        if (CollectionUtils.isEmpty(manualNodes)) {
            // 没有已处理的人工节点，不处理
            return Collections.emptyList();
        }
        // 得到回退按钮配置的回退范围
        Node currentNode = FlowUtil.findNode(flowTask.getFlowDesign(), nodeId);
        BackProperties backProperties = FlowUtil.getNodeBackProps(currentNode);
        BackScopeEnum backScope = backProperties.getScope();

        // 根据回退范围返回回退节点
        // 从数据库获取路径，若数据库中没有则从设计中获取路径（兼容已启动且未结束的工作流任务）
        List<List<Node>> currentNodePaths = flowTaskPathService.getNodePaths(flowTask, nodeId);

        // 返回发起人节点
        if (BackScopeEnum.ROOT.equals(backScope)) {
            return Collections.singletonList(BeanCopyUtil.copy(currentNodePaths.get(0).get(0), CanBackNodeDto.class));
        }

        // 当前节点的可执行路径，包含已处理的人工节点可回退
        if (BackScopeEnum.APPROVED.equals(backScope)) {
            return currentNodePaths.stream()
                    .flatMap(path ->
                            manualNodes.stream().filter(manualNode -> path.stream().anyMatch(pathNode -> manualNode.getId().equals(pathNode.getId())))
                    )
                    .distinct()
                    .map(node -> BeanCopyUtil.copy(node, CanBackNodeDto.class))
                    .collect(Collectors.toList());
        }

        // 返回当前节点的上一个人工审批节点
        if (BackScopeEnum.PREVIOUS.equals(backScope)) {
            List<Node> nodes = FlowPathUtil.getPreviousManualNodes(currentNodePaths, nodeId);
            return nodes.stream()
                    .filter(node ->
                            manualNodes.stream().anyMatch(manualNode -> manualNode.getId().equals(node.getId()))
                    )
                    .map(node -> BeanCopyUtil.copy(node, CanBackNodeDto.class))
                    .collect(Collectors.toList());

        }

        return Collections.emptyList();
    }

    @Override
    public Boolean havePendingTask(String businessId) {
        return flowTaskService.havePendingTask(businessId);
    }

    @Override
    public FlowTask getPendingTask(String businessId) {
        return flowTaskService.getPendingTask(businessId);
    }
}