package cn.bctools.design.workflow.service.impl;

import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.design.workflow.dto.FlowTaskStatisticResDto;
import cn.bctools.design.workflow.service.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @Author: Zhu<PERSON>iao<PERSON><PERSON>
 * @Description: 工作流任务统计
 */
@Service
@AllArgsConstructor
public class TaskStatisticServiceImpl implements TaskStatisticService {
    private final FlowTaskPersonService flowTaskPersonService;
    private final FlowTaskCarbonCopyService flowTaskCarbonCopyService;
    private final FlowTaskService flowTaskService;
    private final FlowTaskApprovalRecordService flowTaskApprovalRecordService;

    @Override
    public FlowTaskStatisticResDto statistic(UserDto userDto) {
        FlowTaskStatisticResDto resDto = new FlowTaskStatisticResDto();
        // 我的待办任务数量
        resDto.setPendingCount(flowTaskPersonService.pendingApprovesCount(userDto));
        // 我创建的任务数量
        resDto.setSelfCreateCount(flowTaskService.selfCreateCount(userDto));
        // 抄送给我的任务数量
        resDto.setCarbonCopyCount(flowTaskCarbonCopyService.carbonCopyCount(userDto));
        // 我参与审批的任务数量
        resDto.setSelfApproveCount(flowTaskApprovalRecordService.selfApproveCount(userDto));
        return resDto;
    }
}
