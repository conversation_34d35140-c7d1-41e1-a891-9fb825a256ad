package cn.bctools.design.workflow.service.impl;

import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.rule.RuleRunService;
import cn.bctools.design.workflow.dto.StopTaskReqDto;
import cn.bctools.design.workflow.entity.FlowTask;
import cn.bctools.design.workflow.entity.FlowTaskApprovalRecord;
import cn.bctools.design.workflow.entity.FlowTaskNode;
import cn.bctools.design.workflow.entity.dto.*;
import cn.bctools.design.workflow.entity.enums.FlowTaskStatusEnum;
import cn.bctools.design.workflow.entity.enums.TerminatedTypeEnum;
import cn.bctools.design.workflow.enums.NodeOperationTypeEnum;
import cn.bctools.design.workflow.model.Node;
import cn.bctools.design.workflow.model.enums.NodeTypeEnum;
import cn.bctools.design.workflow.service.*;
import cn.bctools.design.workflow.support.listener.asynctask.AsyncTaskDynamicDataEvent;
import cn.bctools.design.workflow.support.listener.taskend.TaskEndEvent;
import cn.bctools.design.workflow.utils.FlowPathUtil;
import cn.bctools.rule.error.MessageTipsDto;
import cn.bctools.rule.utils.html.RuleExecuteDto;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: ZhuXiaoKang
 * @Description: 结束任务服务
 */
@Service
@AllArgsConstructor
public class TaskStopServiceImpl implements TaskStopService {

    private final FlowTaskService flowTaskService;
    private final FlowDesignService flowDesignService;
    private final FlowTaskNodeService flowTaskNodeService;
    private final FlowTaskApprovalRecordService flowTaskApprovalRecordService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final RuleRunService ruleRunService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public FlowTask withdrawTask(UserDto userDto, String taskId, StopTaskReqDto stopTaskDto) {
        // 查询工作流任务信息
        FlowTask flowTask = Optional.ofNullable(flowTaskService.getOne(Wrappers.<FlowTask>lambdaQuery()
                .eq(FlowTask::getId, taskId)
                .eq(FlowTask::getCreateById, userDto.getId())
        )).orElseThrow(() -> new BusinessException("只能取消自己发起的任务"));
        if (Boolean.FALSE.equals(FlowTaskStatusEnum.PENDING.equals(flowTask.getTaskStatus()))) {
            throw new BusinessException("任务已结束");
        }
        //todo: 新增工作流设计，判断是否允许起始节点可以撤回
        LinkedList<CourseDto> courseDtos = Optional.ofNullable(flowTask.getCourses()).orElse(new LinkedList<>());
        if (CollectionUtil.isNotEmpty(courseDtos)) {
            CourseDto courseDto = courseDtos.getLast();
            if (!NodeTypeEnum.ROOT.getDefaultNodeId().equals(courseDto.getNodeType().getDefaultNodeId())) {
                throw new BusinessException("当前节点不允许撤回");
            }
        }

        // 查询工作流设计，判断是否允许发起人终止流程（若流程设计已删除，则默认不可人为终止）
        FlowExtendDto extend = flowDesignService.getFlowExtend(flowTask.getFlowDesignId());
        if (Boolean.FALSE.equals(extend.getEnableCancel())) {
            throw new BusinessException("不允许发起人终止流程");
        }
        //判断是否有前置撤回逻辑（需要开启撤回逻辑）
        Optional<TaskEndTriggerEventDto> taskEndTriggerEvent = Optional.of(extend)
                .map(FlowExtendDto::getTaskEndTriggerEvent);

        boolean isTerminatedEventEnabled = taskEndTriggerEvent
                .map(TaskEndTriggerEventDto::getTerminatedEvent)
                .map(TaskEndTriggerEventSetting::getEnableEvent)
                .orElse(false);

        boolean isTerminatedBeforeEventEnabled = taskEndTriggerEvent
                .map(TaskEndTriggerEventDto::getTerminatedBeforeEvent)
                .map(TaskEndTriggerEventSetting::getEnableEvent)
                .orElse(false);

        if (isTerminatedEventEnabled && isTerminatedBeforeEventEnabled) {
            taskEndTriggerEvent
                    .map(TaskEndTriggerEventDto::getTerminatedBeforeEvent)
                    .map(TaskEndTriggerEventSetting::getRuleKey)
                    .ifPresent(ruleKey -> beforeTerminatedCallback(ruleKey, new HashMap<>()));
        }

        // 结束任务
        stopTask(userDto, flowTask, stopTaskDto, TerminatedTypeEnum.WITHDRAW, extend);
        // 发布事件：同步流程信息到业务数据
        applicationEventPublisher.publishEvent(new AsyncTaskDynamicDataEvent(this, flowTask));
        return flowTask;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public FlowTask terminationTask(UserDto userDto, FlowTask flowTask, StopTaskReqDto stopTaskDto) {
        if (Boolean.FALSE.equals(FlowTaskStatusEnum.PENDING.equals(flowTask.getTaskStatus()))) {
            throw new BusinessException("任务已结束");
        }
        FlowExtendDto extend = flowDesignService.getFlowExtend(flowTask.getFlowDesignId());
        // 结束任务
        stopTask(userDto, flowTask, stopTaskDto, TerminatedTypeEnum.TERMINATION, extend);
        return flowTask;
    }

    /**
     * 结束任务
     *
     * @param userDto        用户
     * @param flowTask       任务
     * @param stopTaskDto    参数
     * @param terminatedType 终止任务类型
     * @param extend         流程设计扩展配置
     */
    @Transactional(rollbackFor = Exception.class)
    public void stopTask(UserDto userDto, FlowTask flowTask, StopTaskReqDto stopTaskDto, TerminatedTypeEnum terminatedType, FlowExtendDto extend) {
        LinkedList<CourseDto> courseDtos = Optional.ofNullable(flowTask.getCourses()).orElse(new LinkedList<>());
        // 得到未审批结束的节点审批进度
        List<CourseDto> currentNodeCourses = flowTaskNodeService.getCurrentNodesByTaskId(flowTask.getId()).stream()
                .map(FlowTaskNode::getCourse)
                .filter(courseDto -> ObjectNull.isNotNull(courseDto.getApproveResultDtos()))
                .collect(Collectors.toList());
        if (ObjectNull.isNotNull(currentNodeCourses)) {
            courseDtos.addAll(currentNodeCourses);
        }
        // 保存处理结果
        String time = LocalDateTimeUtil.format(LocalDateTimeUtil.now(), DatePattern.NORM_DATETIME_PATTERN);
        CourseDto courseDto = new CourseDto()
                .setTerminated(Boolean.TRUE)
                .setTerminatedType(terminatedType)
                .setTerminatedReason(stopTaskDto.getReason())
                .setApproveResultDtos(Collections.singletonList(new ApproveResultDto()
                        .setUserId(userDto.getId())
                        .setUserName(userDto.getRealName())
                        .setTime(time)
                        .setNodeOperationTypeEnum(NodeOperationTypeEnum.TERMINATED)
                        .setOver(Boolean.TRUE)))
                .setTime(time);
        courseDtos.add(courseDto);
        flowTask.setCourses(courseDtos);
        flowTaskService.updateById(flowTask);
        // 终止任务
        endTask(flowTask, FlowTaskStatusEnum.TERMINATED, stopTaskDto.getReason());
        // 发布事件：任务结束
        applicationEventPublisher.publishEvent(new TaskEndEvent(this, flowTask, extend));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void endTask(FlowTask task, FlowTaskStatusEnum status, String reason) {
        // 修改任务状态
        task.setTaskStatus(status);
        task.setStopReason(reason);
        flowTaskService.updateById(task);
        // 清除工作流执行过程数据
        flowTaskService.cleanTaskExecutiveProcess(task.getId());
    }

    private void beforeTerminatedCallback(String ruleKey, Map<String, Object> dataMap) {
        //事件回调.如果是成功,则返回成功状态,如果是失败返回失败状态和message
        RuleExecuteDto dto = Optional.ofNullable(ruleRunService.run(ruleKey, JSONUtil.parseObj(dataMap))).orElseGet(RuleExecuteDto::new);
        if (!dto.getStats()) {
            throw new BusinessException(dto.getSyncMessageTips());
        }
        try {
            //处理前后置,如果发生异常后，可能在开始线后，直接返回了，并没有执行或设计
            if (ObjectNull.isNotNull(dto.getEndResult())) {
                if (ObjectNull.isNotNull(dto.getEndResult().getValue())) {
                    if (dto.getEndResult().getValue() instanceof MessageTipsDto) {
                        if (!((MessageTipsDto) dto.getEndResult().getValue()).getOnOff()) {
                            throw new BusinessException(((MessageTipsDto) dto.getEndResult().getValue()).getMessage());
                        }
                    }
                }
            }
        } catch (BusinessException e) {
            throw new BusinessException(e.getMessage());
        }
    }
}
