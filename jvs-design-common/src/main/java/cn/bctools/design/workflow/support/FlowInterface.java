package cn.bctools.design.workflow.support;


import cn.bctools.design.workflow.model.enums.NodeTypeEnum;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Description: 工作流流转处理. 所有工作流节点类型都需要实现此接口
 */

public interface FlowInterface {

    /**
     * 节点类型
     * @return
     */
    NodeTypeEnum getType();

    /**
     * 流转处理
     *
     * @return 流转结果
     */
    void execute();
}
