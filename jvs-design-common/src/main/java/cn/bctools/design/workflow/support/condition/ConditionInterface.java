package cn.bctools.design.workflow.support.condition;


import cn.bctools.design.workflow.model.ConditionProperties;
import cn.bctools.design.workflow.support.RuntimeData;

/**
 * @Author: <PERSON><PERSON>ia<PERSON>K<PERSON>
 * @Description: 工作流支持的各种条件实现接口
 */
public interface ConditionInterface {

    /**
     * 条件类型
     *
     * @return
     */
    String getType();

    /**
     * 条件验证
     *
     * @param conditionProperties 条件
     * @param runtimeData
     * @return
     */
    Boolean verify(ConditionProperties conditionProperties, RuntimeData runtimeData);
}
