package cn.bctools.design.workflow.support.function.dto;

import cn.bctools.design.workflow.support.RuntimeData;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author: Zhu<PERSON>iaoKang
 * @Description: 任务转交运行时参数
 */
@Data
@Accessors(chain = true)
public class TransferRuntimeDto {

    /**
     * 运行时参数
     */
    private RuntimeData runtimeData;

    /**
     * 待自动选择代理人的被代理人id集合
     */
    private List<String> userIds;
}
