package cn.bctools.design.workflow.support.process;


import cn.bctools.design.workflow.support.FlowResult;
import cn.bctools.design.workflow.support.enums.FlowNextTypeEnum;

/**
 * @Author: <PERSON><PERSON>iaoKang
 * @Description: 流转处理。对工作流节点执行结果进行处理
 */
public interface ProcessInterface {

    /**
     * 处理类型
     * @return
     */
    FlowNextTypeEnum getType();

    /**
     * 流转处理
     * @param flowResult 节点处理结果
     */
    void execute(FlowResult flowResult);
}
