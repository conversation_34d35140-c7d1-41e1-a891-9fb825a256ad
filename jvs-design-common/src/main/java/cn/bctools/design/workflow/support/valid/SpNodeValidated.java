package cn.bctools.design.workflow.support.valid;

import cn.bctools.auth.api.dto.PersonnelDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.workflow.entity.dto.FlowExtendDto;
import cn.bctools.design.workflow.enums.NodeOperationTypeEnum;
import cn.bctools.design.workflow.model.Node;
import cn.bctools.design.workflow.model.NodeProperties;
import cn.bctools.design.workflow.model.enums.NodePropertiesTypeEnum;
import cn.bctools.design.workflow.model.enums.NodeTypeEnum;
import cn.bctools.design.workflow.model.enums.TargetObjectTypeEnum;
import cn.bctools.design.workflow.model.properties.AppendApprovalProperties;
import cn.bctools.design.workflow.model.properties.FlowButton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: ZhuXiaoKang
 * @Description: 审批节点设计校验
 */
@Slf4j
public class SpNodeValidated {

    public static final String APPROVAL_PERSON_IS_EMPTY_ERR = "[{}]环节未设置审批人,请检查设计";
    public static final String COPIES_PERSON_IS_EMPTY_ERR = "[{}]环节未设置抄送人,请检查设计";

    /**
     * 需要校验的节点类型
     */
    private static final List<NodeTypeEnum> VALID_NODE_TYPES = new ArrayList<NodeTypeEnum>(){{
        add(NodeTypeEnum.SP);
        add(NodeTypeEnum.CS);
    }};

    private SpNodeValidated() {
    }

    public static void valid(FlowExtendDto extend, Node node) {
        // 跳过不需要校验的节点
        if (Boolean.FALSE.equals(VALID_NODE_TYPES.contains(node.getType()))) {
            return;
        }
        // 审批节点 | 抄送节点审批人不能为空
        checkPendingUser(extend, node);
        // 节点需要配置按钮
        checkButton(node);
        // 加签配置校验
        checkAppendApproval(node);
    }

    /**
     * 节点人员不能为空
     * @param node
     */
    private static void checkPendingUser(FlowExtendDto extend, Node node) {
        if (NodeTypeEnum.SP.equals(node.getType())) {
            checkPendingUserSp(extend, node.getName(), node.getProps());
            return;
        }
        if (NodeTypeEnum.CS.equals(node.getType())) {
            checkPendingUserCs(node.getName(), node.getProps());
        }
    }

    /**
     * 校验审批节点审批人
     * @param nodeName
     * @param props
     * @return
     */
    private static void checkPendingUserSp(FlowExtendDto extend, String nodeName, NodeProperties props) {
        // 需要检验审批人的类型
        boolean checkType = NodePropertiesTypeEnum.ASSIGN_USER.equals(props.getType()) ||
                NodePropertiesTypeEnum.ROLE.equals(props.getType()) ||
                NodePropertiesTypeEnum.JOB.equals(props.getType()) ||
                NodePropertiesTypeEnum.USER_FIELD.equals(props.getType());
        if (Boolean.FALSE.equals(checkType)) {
            return;
        }

        if (NodePropertiesTypeEnum.USER_FIELD.equals(props.getType())) {
            checkPendingUserSp(nodeName, props, TargetObjectTypeEnum.user_field);
        }

        // 允许动态选择审批人，在发布时不需要校验审批人必填
        if (extend.getEnableDynamicApprover()) {
            return;
        }
        switch (props.getType()) {
            case ASSIGN_USER:
                // 指定人员，可选“人员”|"部门"，所以只要其中一个类型有数据就通过校验
                checkPendingUserSp(nodeName, props, TargetObjectTypeEnum.user);
                break;
            case ROLE:
                checkPendingUserSp(nodeName, props, TargetObjectTypeEnum.role);
                break;
            case JOB:
                checkPendingUserSp(nodeName, props, TargetObjectTypeEnum.job);
                break;
            default:
                // 其它类型不需要发布时校验
                break;
        }
    }

    private static void checkPendingUserSp(String nodeName, NodeProperties props, TargetObjectTypeEnum type) {
        List<String> ids = props.getTargetObj().getPersonnelIdByType(type);
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(APPROVAL_PERSON_IS_EMPTY_ERR, nodeName);
        }
    }

    /**
     * 校验抄送节点抄送人
     * @param nodeName
     * @param props
     * @return
     */
    private static void checkPendingUserCs(String nodeName, NodeProperties props) {
        List<String> ids = Optional.ofNullable(props.getTargetObj().getPersonnels()).orElseGet(ArrayList::new)
                .stream().filter(r -> StringUtils.isNotBlank(r.getId()))
                .map(PersonnelDto::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(COPIES_PERSON_IS_EMPTY_ERR, nodeName);
        }
    }

        /**
         * 节点需要配置按钮
         * @param node
         */
    private static void checkButton(Node node) {
        if (NodeTypeEnum.SP.equals(node.getType())) {
            if (node.getProps().getBtn().stream().noneMatch(FlowButton::getEnable)) {
                throw new BusinessException("["+ node.getName() + "]环节未启用按钮,请检查设计");
            }
            AppendApprovalProperties appendApprovalProperties = node.getProps().getAppendApproval();
            if (ObjectNull.isNotNull(appendApprovalProperties) && appendApprovalProperties.getBtn().stream().noneMatch(FlowButton::getEnable)) {
                throw new BusinessException("["+ node.getName() + "]环节加签未启用按钮,请检查设计");
            }
        }
    }

    /**
     * 加签配置校验
     * @param node
     */
    private static void checkAppendApproval(Node node) {
        boolean enable = Optional.ofNullable(node.getProps().getBtn()).orElse(new ArrayList<>()).stream().anyMatch(btn -> NodeOperationTypeEnum.APPEND.equals(btn.getOperation()) && Boolean.TRUE.equals(btn.getEnable()));
        // 加签按钮未启用，则不校验
        if (Boolean.FALSE.equals(enable)) {
            return;
        }
        // 加签按钮已启用校验
        // 加签位置必选
        if (CollectionUtils.isEmpty(node.getProps().getAppendApproval().getPoint())) {
            throw new BusinessException("["+ node.getName() + "]环节已启用加签按钮,未设置加签位置,请检查设计");
        }
    }
}
