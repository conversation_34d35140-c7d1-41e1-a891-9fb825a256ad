package cn.bctools.design.workflow.support.valid;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.workflow.entity.FlowDesign;
import cn.bctools.design.workflow.entity.dto.FlowExtendDto;
import cn.bctools.design.workflow.model.Node;
import cn.bctools.design.workflow.utils.FlowUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @Author: Zhu<PERSON>iaoKang
 * @Description: 工作流设计校验
 */
public class ValidatedFlowDesign {

    private ValidatedFlowDesign() {
    }

    /**
     * 校验工作流设计是否可发布
     * @param flowDesign 工作流设计
     */
    public static void valid(FlowDesign flowDesign) {
        if (ObjectNull.isNull(flowDesign) || StringUtils.isBlank(flowDesign.getDesigning())) {
            throw new BusinessException("未设计工作流");
        }
        FlowUtil.parseNodeJsonAndCache(flowDesign.getDesigning());
        validNode(flowDesign.getExtend(), FlowUtil.getNodeCache());
    }

    /**
     * 递归校验所有节点
     * @param extend 工作流高级配置
     * @param nodeMap 所有节点
     */
    private static void validNode(FlowExtendDto extend, Map<String, Node> nodeMap) {
        nodeMap.forEach((k, node) ->  SpNodeValidated.valid(extend, node));
    }


}
