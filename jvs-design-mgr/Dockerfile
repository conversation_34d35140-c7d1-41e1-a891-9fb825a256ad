FROM registry.cn-hangzhou.aliyuncs.com/glg/sky-agent:8.8.0
#FROM arm64v8/ubuntu:latest
MAINTAINER jvs <www.bctools.com>

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' > /etc/timezone
ADD ./target/jvs-design-mgr.jar /app/app.jar
# 安装字体库
# todo 无法打包处理
#RUN yum -y install fontconfig mkfontscale
# 挂载文字库
#VOLUME ["/usr/share/fonts","/usr/share/fonts"]
# 添加缺少的字体文件
#ADD fonts/* /usr/share/fonts/new/

#RUN cd /usr/share/fonts/zh_CN
ADD fonts /usr/share/fonts/zh_CN
RUN chmod 775 /usr/share/fonts/zh_CN
# RUN mkfontscale
#RUN mkfontdir
#RUN fc-cache -fv
#RUN fc-list
ENV LANG C.UTF-8
ENV skyname="jvs-design-mgr"
ENV JAVA_OPTS=""
ENV skyip="localhost:11800"
ENV authentication=""
#镜像默认地址为 -javaagent:/skywalking-agent/skywalking-agent.jar
ENV skywalkingPath=""
ENV nacosAddr="cloud-nacos:8848"
ENV namespace=""
ENTRYPOINT ["sh","-c","java $skywalkingPath  -Dskywalking.agent.service_name=$skyname -Dskywalking.agent.authentication=$authentication -Dskywalking.collector.backend_service=$skyip -Dspring.cloud.nacos.discovery.server-addr=$nacosAddr -Dspring.cloud.nacos.discovery.namespace=$namespace  $JAVA_OPTS -jar /app/app.jar"]

