<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.bctools</groupId>
        <artifactId>jvs-design</artifactId>
        <version>2.1.8</version>
    </parent>

    <groupId>cn.bctools</groupId>
    <artifactId>jvs-design-mgr</artifactId>

    <dependencies>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-design-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-web</artifactId>
        </dependency>

        <!-- 生成二维码 -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.3.3</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-function-tools</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-function-base</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-function-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-function-system</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-function-data</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-function-encryption</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-function-api-open-platform</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-oauth2</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.alibaba</groupId>-->
        <!--            <artifactId>easyexcel</artifactId>-->
        <!--            <version>3.1.1</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>cn.bctools</groupId>-->
        <!--            <artifactId>jvs-function-service</artifactId>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-function-api-tencent-ess-plug</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-function-dingding-plug</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-function-tools</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-function-discriminate</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>cn.bctools</groupId>-->
        <!--            <artifactId>jvs-function-aliyun-plug</artifactId>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->

        <!-- quartz ：quartz-2.2.3/c3p0-0.9.1.1/slf4j-api-1.6.6 -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.3.1</version>
        </dependency>

        <!-- mqtt -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-integration</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-stream</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-mqtt</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.bctools.custom</groupId>
            <artifactId>survey-app</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>5.2.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>5.3.18</version>
        </dependency>
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.12.2</version>
        </dependency>
        <dependency>
            <groupId>io.github.draco1023</groupId>
            <artifactId>poi-tl-ext</artifactId>
            <version>0.4.18-poi5</version>
        </dependency>

        <!--爬虫相关依赖(webmagic)-->
        <dependency>
            <groupId>us.codecraft</groupId>
            <artifactId>webmagic-core</artifactId>
            <version>0.8.0</version>
        </dependency>
        <dependency>
            <groupId>us.codecraft</groupId>
            <artifactId>webmagic-extension</artifactId>
            <version>0.8.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.openai/openai-java -->
        <dependency>
            <groupId>com.openai</groupId>
            <artifactId>openai-java</artifactId>
            <version>0.34.1</version>
        </dependency>

        <dependency>
            <groupId>cn.bctools</groupId>
            <artifactId>jvs-starter-protection</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <!--        统一管理kotlin版本，防止缺少必须类型的时候报错-->
        <dependencies>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>1.9.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.4</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <!-- 配置信息 -->
                    <skip>true</skip>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>