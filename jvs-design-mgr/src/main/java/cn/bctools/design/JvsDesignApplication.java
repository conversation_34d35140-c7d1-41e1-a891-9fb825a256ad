package cn.bctools.design;

import cn.bctools.oauth2.annotation.EnableJvsMgrResourceServer;
import cn.bctools.rule.entity.enums.InputTypeTransformInterface;
import cn.bctools.rule.utils.RuleDesignUtils;
import com.xxl.job.core.config.annotation.EnableXxlJobExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.stereotype.Indexed;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
@Indexed
@EnableDiscoveryClient
@SpringBootApplication
@EnableXxlJobExecutor
@ComponentScan(basePackages = {"cn.bctools.design", "cn.bctools.rule", "cn.bctools.*.service", "cn.bctools.*.*.service"})
@EnableJvsMgrResourceServer
@Slf4j
public class JvsDesignApplication {

    public static void main(String[] args) {
        long startTime = System.currentTimeMillis();
        log.info("🚀 低代码平台启动中...");

        try {
            // 启动Spring应用
            SpringApplication.run(JvsDesignApplication.class, args);

            long endTime = System.currentTimeMillis();
            log.info("✅ 低代码启动完成，耗时: {}ms", endTime - startTime);

        } catch (Exception e) {
            log.error("❌ 低代码启动失败", e);
            System.exit(1);
        }

    }

}