package cn.bctools.design.component;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import cn.bctools.design.constant.DynamicDataConstant;
import cn.bctools.design.data.cache.EchoCache;
import cn.bctools.design.data.component.DataModelHandler;
import cn.bctools.design.data.entity.DataModelPo;
import cn.bctools.design.data.fields.dto.FieldBasicsHtml;
import cn.bctools.design.data.fields.dto.enums.DataConditionType;
import cn.bctools.design.data.service.DataFieldService;
import cn.bctools.design.data.service.DataModelService;
import cn.bctools.design.data.service.DynamicDataService;
import cn.bctools.design.project.entity.JvsApp;
import cn.bctools.design.project.service.JvsAppService;
import cn.bctools.design.use.api.DataModelApi;
import cn.bctools.design.use.api.dto.DataFiledDto;
import cn.bctools.design.use.api.dto.DataModelDto;
import cn.bctools.design.use.api.dto.DataModelSearchDto;
import cn.bctools.design.use.api.enums.DataModelQueryType;
import cn.bctools.design.util.DynamicDataUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据模型
 *
 * @Author: GuoZi
 */
@Slf4j
@RestController
@Api(tags = "[Feign]数据模型")
public class DataModelComponent implements DataModelApi {
    @Autowired
    DataModelService dataModelService;
    @Autowired
    DataFieldService dataFieldService;
    @Autowired
    JvsAppService appService;
    @Autowired
    DataModelHandler dataModelHandler;
    @Autowired
    DynamicDataService dynamicDataService;

    @Override
    public R<Long> getCount(String id) {
        Query query = new Query(Criteria.where(DynamicDataService.MONGO_ID).exists(true).andOperator(Criteria.where("delFlag").is(false)));
        long count = dataModelHandler.count(query, Map.class, id);
        return R.ok(count);
    }

    @Override
    public R<List> list(String id, long size, long current) {
        Query query = new Query(Criteria.where(DynamicDataService.MONGO_ID).exists(true).andOperator(Criteria.where("delFlag").is(false)));
        //这里的数量如果为0 就表示获取全部数据
        if (size > BigDecimal.ROUND_UP) {
            long skip = size * (current - 1);
            query.skip(skip).limit((int) size);
        }
        List mapList = dataModelHandler.find(query, Map.class, id);
        return R.ok(mapList);
    }

    @Override
    public R<List> search(DataModelSearchDto searchDto) {
        Criteria criteria = buildQuery(searchDto);
        Query query = new Query(criteria);
        //这里的数量如果为0 就表示获取全部数据
        if (searchDto.getSize() > BigDecimal.ROUND_UP) {
            long skip = searchDto.getSize() * (searchDto.getCurrent() - 1);
            query.skip(skip).limit((int) searchDto.getSize());
        }
        List mapList = dataModelHandler.find(query, Map.class, searchDto.getId());
        return R.ok(mapList);
    }

    @Override
    public R<Long> countBySearch(DataModelSearchDto searchDto) {
        Criteria criteria = buildQuery(searchDto);
        Query query = new Query(criteria);
        long count = dataModelHandler.count(query, Map.class, searchDto.getId());
        return R.ok(count);
    }

    @Override
    public R<List<DataFiledDto>> fieldMap(String appId, String id) {
        List<DataFiledDto> collect = dataFieldService.getAllField(appId, id).stream()
                .map(e -> new DataFiledDto().setCls(e.getFieldType().getAClass()).setColumnCount(e.getFieldName()).setColumnName(e.getFieldKey()).setModelId(e.getModelId()))
                .collect(Collectors.toList());
        return R.ok(collect);
    }

    @Override
    public R<List<DataModelDto>> dataModelList(String id) {
        JvsApp byId = appService.getById(id);
        List<DataModelDto> collect = dataModelService.list(Wrappers.query(new DataModelPo().setAppId(id)))
                .stream()
                .map(e -> new DataModelDto().setId(e.getId()).setAppId(e.getAppId()).setAppName(byId.getName()).setTableName(e.getName()).setTableNameDesc(e.getName()))
                .collect(Collectors.toList());
        return R.ok(collect);
    }

    private Criteria buildQuery(DataModelSearchDto searchDto) {
        Criteria criteria = Criteria.where(DynamicDataService.MONGO_ID)
                .exists(true);
        Criteria childCriteria = Criteria.where("delFlag").is(false);

        List<DataModelSearchDto.SearchGroup> group = searchDto.getGroup();

        List<Criteria> and = new ArrayList<>();
        List<Criteria> or = new ArrayList<>();
        if (ObjectNull.isNotNull(group)) {
            for (DataModelSearchDto.SearchGroup searchGroup : group) {
                List<DataModelSearchDto.SearchItem> items = searchGroup.getItems();
                Criteria currentCriteria = buildDynamicCriteriaList(items);
                if (searchGroup.getAndOr()) {
                    and.add(currentCriteria);
                } else {
                    or.add(currentCriteria);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(and)) {
            childCriteria.andOperator(and);
        }
        if (CollectionUtil.isNotEmpty(or)) {
            childCriteria.orOperator(or);
        }
        criteria.andOperator(childCriteria);
        return criteria;
    }

    /**
     * 动态查询条件
     *
     * @param conditions
     * @return
     */
    private Criteria buildDynamicCriteriaList(List<DataModelSearchDto.SearchItem> conditions) {
        if (ObjectUtils.isEmpty(conditions)) {
            return null;
        }
        conditions = conditions.stream().filter(e -> ObjectNull.isNotNull(e.getKey(), e.getQueryType())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(conditions)) {
            return null;
        }
        Criteria criteria = new Criteria();

        List<Criteria> and = new ArrayList<>();
        List<Criteria> or = new ArrayList<>();
        for (DataModelSearchDto.SearchItem condition : conditions) {
            Criteria criteria1 = buildCriteria(condition);
            if (condition.getAndOr()) {
                and.add(criteria1);
            } else {
                or.add(criteria1);
            }
        }

        if (CollectionUtil.isNotEmpty(and)) {
            criteria.andOperator(and);
        }
        if (CollectionUtil.isNotEmpty(or)) {
            criteria.orOperator(or);
        }
        return criteria;
    }

    private Criteria buildCriteria(DataModelSearchDto.SearchItem condition) {
        String key = condition.getKey();
        Object queryValue = condition.getValue();
        DataModelQueryType queryType = condition.getQueryType();
        if (!DataModelQueryType.isNull.equals(queryType) && !DataModelQueryType.between.equals(queryType)) {
            if (StringUtils.isBlank(key) || ObjectUtils.isEmpty(queryValue) || Objects.isNull(queryType)) {
                return null;
            }
            if (queryValue instanceof List) {
                //有间隔查询,
                //下拉多选查询,
                queryType = DataModelQueryType.in;
                //如果是多值为查询条件,则直接改变查询的类型为in 或 not in
            } else {
                queryValue = DataConditionType.get(queryValue);
            }
        }

        Criteria lower = Criteria.where(key);
        switch (queryType) {
            case eq:
                //做字符串拼接处理，如果是包含,则进行分隔处理
                if (queryValue instanceof String) {
                    if (DynamicDataConstant.DATA_EMPTY.equals(queryValue)) {
                        lower.isNull();
                    } else if (queryValue.toString().contains(",")) {
                        String[] split = queryValue.toString().split(",");
                        lower.in(new ArrayList<String>(Arrays.asList(split)));
                        break;
                    } else {
                        lower.is(queryValue);
                    }
                } else {
                    if (queryValue instanceof List) {
                        lower.in((Collection<?>) queryValue);
                    } else {
                        lower.is(queryValue);
                    }
                }
                break;
            case ne:
                if (DynamicDataConstant.DATA_EMPTY.equals(queryValue)) {
                    lower.ne(null);
                } else {
                    lower.ne(queryValue);
                }
                break;
            case gt:
                lower.gt(queryValue);
                break;
            case ge:
                lower.gte(queryValue);
                break;
            case lt:
                lower.lt(queryValue);
                break;
            case le:
                lower.lte(queryValue);
                break;
            case allin:
                if (queryValue instanceof List) {
                    List value = (List) condition.getValue();
                    lower.regex(DynamicDataUtils.parseRegular(value.get(0).toString()));
                    for (int i = 1; i < value.size(); i++) {
                        //添加其它的
                        Criteria regex = Criteria.where(key).regex(DynamicDataUtils.parseRegular(value.get(i).toString()));
                        lower.andOperator(regex);
                    }
                } else {
                    //不是数组不处理
                    lower.regex(DynamicDataUtils.parseRegular(queryValue.toString()));
                }
                break;
            case in:
                if (queryValue instanceof List) {
                    lower.in((List) queryValue);
                } else {
                    if (JSONUtil.isTypeJSONObject(condition.getValue().toString())) {
                        if (JSONUtil.isTypeJSONArray(condition.getValue().toString())) {
                            lower.in(JSONArray.parseArray(JSONObject.toJSONString(condition.getValue())));
                        }
                    } else {
                        lower.is(condition.getValue());
                    }
                }
                break;
            case notIn:
                if (queryValue instanceof List) {
                    lower.nin(queryValue);
                } else {
                    if (JSONUtil.isTypeJSONObject(queryValue.toString())) {
                        if (JSONUtil.isTypeJSONArray(queryValue.toString())) {
                            lower.nin(JSONArray.parseArray(JSONObject.toJSONString(queryValue)));
                        }
                    } else {
                        lower.ne(queryValue);
                    }
                }
                break;
            case like:
                if (queryValue instanceof List) {
                    List<String> list = (List<String>) queryValue;
                    for (String e : list) {
                        lower.regex(DynamicDataUtils.parseRegular(e.toString()));
                    }
                    break;
                }
                // 模糊查询, mongoTemplate只支持正则匹配
                lower.regex(DynamicDataUtils.parseRegular(queryValue.toString()));
                break;
            case isNull:
                lower = new Criteria().orOperator(
                        Criteria.where(key).isNull(),
                        Criteria.where(key).is("")
                );
                break;
            case between:
                // 范围查询可参考官方文档: https://www.mongodb.com/docs/manual/reference/operator/query/elemMatch/
                List queryValues = null;
                if (queryValue instanceof List) {
                    queryValues = (List) condition.getValue();
                } else {
                    if (JSONUtil.isTypeJSON(condition.getValue().toString())) {
                        if (JSONUtil.isTypeJSONArray(condition.getValue().toString())) {
                            queryValues = JSONArray.parseArray(JSONObject.toJSONString(condition.getValue()));
                        }
                    } else {
                        queryValues = Collections.emptyList();
                    }
                }
                if (ObjectNull.isNotNull(queryValues) && queryValues.size() == 2) {
                    lower = new Criteria().andOperator(
                            Criteria.where(key).gte(queryValues.get(0)).lte(queryValues.get(1))
                    );
                }
                break;
            default:
                break;
        }
        return lower;
    }

    @Override
    public R<List<Map<String, Object>>> searchWithEcho(DataModelSearchDto searchDto) {
        Criteria criteria = buildQuery(searchDto);
        Query query = new Query(criteria);
        //size=0 获取全部数据
        if (searchDto.getSize() > BigDecimal.ROUND_UP) {
            long skip = searchDto.getSize() * (searchDto.getCurrent() - 1);
            query.skip(skip).limit((int) searchDto.getSize());
        }
        List<Map<String, Object>> mapList = dataModelHandler.find(query, Map.class, searchDto.getId())
                .stream()
                .map(e -> (Map<String, Object>) e)
                .collect(Collectors.toList());

        DataModelPo model = dataModelService.getModel(searchDto.getId());
        List<FieldBasicsHtml> fields = dataFieldService
                .getAllField(model.getAppId(), searchDto.getId(), e -> false);

        //做数据转换
        EchoCache.recordMergeSourceType(mapList, fields);
        DynamicDataUtils.initEchoCacheMerge(true);
        mapList.forEach(e -> {
            e = dynamicDataService.paresMapWithEchoAndFields(e, fields, false);
        });

        DynamicDataUtils.initEchoCacheMerge(false);
        return R.ok(mapList);
    }

    @Override
    public R<String> getModelName(String id) {
        DataModelPo model = dataModelService.getById(id);
        if (ObjectNull.isNotNull(model)) {
            return R.ok(model.getCollectionName() != null ? model.getCollectionName() : model.getId());
        }
        return null;
    }

    @Override
    public Map<String, Object> getMap(String modelId, String dataId, List<String> fieldKeyList) {
        Query query = new Query();
        query.addCriteria(Criteria.where("delFlag").is(false))
                .addCriteria(Criteria.where("id").is(dataId));
        if (CollectionUtils.isNotEmpty(fieldKeyList)) {
            // 指定查询字段
            String[] fields = new String[fieldKeyList.size()];
            query.fields().include(fieldKeyList.toArray(fields));
        }
        Map<String, Object> dataMap = dataModelHandler.findOne(query, Map.class, modelId);

        DataModelPo model = dataModelService.getModel(modelId);
        List<FieldBasicsHtml> fields = dataFieldService.getAllField(model.getAppId(), modelId, e -> false);
        //做数据转换
        dataMap = dynamicDataService.paresMapWithEchoAndFields(dataMap, fields, false);
        return dataMap;
    }

    @Override
    public String saveData(String modelId, Map<String, Object> data) {
        return dynamicDataService.onlySave(modelId,data);
    }

    @Override
    public void updateData(String appId,String modelId, String designId ,String dataId, Map<String, Object> data) {
        Map<String, Object> oldData = dynamicDataService.querySingle(appId, modelId, dataId);
        oldData.putAll(data);
        if(StrUtil.isNotEmpty(designId)){
            DynamicDataUtils.setDesignId(designId);
        }
        dynamicDataService.update(appId,modelId,dataId,data);
    }

    @Override
    public void deleteData(String modelId, String dataId) {
        dynamicDataService.onlyRemove(modelId,dataId);
    }
}