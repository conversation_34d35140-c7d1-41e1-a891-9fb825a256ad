package cn.bctools.design.component;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.R;
import cn.bctools.design.use.api.FlowTaskApi;
import cn.bctools.design.use.api.dto.FlowTaskDto;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.design.workflow.dto.FlowReqDto;
import cn.bctools.design.workflow.dto.startflow.SelfSelectApprover;
import cn.bctools.design.workflow.entity.FlowTask;
import cn.bctools.design.workflow.entity.dto.ApproveOpinionDto;
import cn.bctools.design.workflow.enums.NodeOperationTypeEnum;
import cn.bctools.design.workflow.service.FlowTaskService;
import cn.bctools.design.workflow.service.TaskService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @project jvs-backend
 * @date 2024/7/29
 * @description
 * @package cn.bctools.design.component
 */
@Slf4j
@RestController
public class FlowTaskComponent implements FlowTaskApi {
    @Autowired
    private TaskService taskService;
    @Autowired
    private FlowTaskService flowTaskService;
    @Override
    public void execute(FlowTaskDto flowTaskDto) {
        FlowTask flowTask = flowTaskService.getOne(new LambdaQueryWrapper<FlowTask>().eq(FlowTask::getDataId, flowTaskDto.getDataId()).last("limit 1"));
        if(ObjectUtil.isNull(flowTask)){
            return;
        }
        String flowTaskId = flowTask.getId();
        FlowReqDto flowDto = new FlowReqDto();
        flowDto.setId(flowTaskId);
        if(StrUtil.isNotEmpty(flowTaskDto.getNodeOperationType())){
            flowDto.setNodeOperationType(NodeOperationTypeEnum.valueOf(flowTaskDto.getNodeOperationType()));
        }
        if(StrUtil.isNotEmpty(flowTaskDto.getOpinion())){
            flowDto.setOpinion(new ApproveOpinionDto().setContent(flowTaskDto.getOpinion()));
        }

        flowDto.setNodeId(flowTaskDto.getNodeId());
        flowDto.setData(flowTaskDto.getData());
        List<Map<String, Object>> approverList = flowTaskDto.getApprovers();
        if(!CollectionUtils.isEmpty(approverList)){
            List<SelfSelectApprover> approvers = BeanCopyUtil.copys(approverList, SelfSelectApprover.class);
            flowDto.setApprovers(approvers);
        }
        String backNodeId = flowTaskDto.getBackNodeId();
        if(StrUtil.isNotEmpty(backNodeId)){
            flowDto.setBackNodeId(backNodeId);
        }
        if(StrUtil.isNotEmpty(flowTaskDto.getDesignId())){
            DynamicDataUtils.setDesignId(flowTaskDto.getDesignId());
        }
        taskService.execute(flowDto,flowTaskDto.getUserDto());
    }

    @Override
    public R<String> getFlowTaskIdByDataId(String dataId) {
        FlowTask flowTask = flowTaskService.getOne(Wrappers.query(new FlowTask().setDataId(dataId)));
        return R.ok(flowTask.getId());
    }
}
