package cn.bctools.design.config;

import cn.bctools.design.filter.AppInterceptor;
import cn.bctools.design.filter.BaseInterceptor;
import cn.bctools.design.permission.AppPermissionHandler;
import cn.bctools.design.permission.DesignPermissionHandler;
import cn.bctools.design.permission.ResourcePermissionHandler;
import cn.bctools.design.project.service.JvsAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class JvsAppConfig {
    /**
     * 添加权限检查
     *
     * @param appPermissionHandler
     * @param designPermissionHandler
     * @param resourcePermissionHandler
     * @return
     */
    @Bean
    AppInterceptor appInterceptor(AppPermissionHandler appPermissionHandler,
                                  DesignPermissionHandler designPermissionHandler,
                                  ResourcePermissionHandler resourcePermissionHandler) {
        return new AppInterceptor()
                .addHandler(appPermissionHandler)
                .addHandler(designPermissionHandler)
                .addHandler(resourcePermissionHandler);
    }

    @Bean
    BaseInterceptor baseInterceptor(JvsAppService jvsAppService) {
        return new BaseInterceptor(jvsAppService);
    }
}