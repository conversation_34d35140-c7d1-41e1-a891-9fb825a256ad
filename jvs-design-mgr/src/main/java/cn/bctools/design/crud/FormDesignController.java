package cn.bctools.design.crud;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import cn.bctools.common.utils.function.Get;
import cn.bctools.database.entity.po.BasalPo;
import cn.bctools.design.crud.entity.CrudAssociationPo;
import cn.bctools.design.crud.entity.FormPo;
import cn.bctools.design.crud.service.CrudPageService;
import cn.bctools.design.crud.service.FormService;
import cn.bctools.design.crud.service.JvsCrudAssociationService;
import cn.bctools.design.crud.utils.DesignUtils;
import cn.bctools.design.data.entity.DataFieldPo;
import cn.bctools.design.data.fields.dto.FieldPublicHtml;
import cn.bctools.design.data.fields.dto.FormDto;
import cn.bctools.design.data.fields.dto.FormSelectItemDto;
import cn.bctools.design.data.fields.dto.form.FormDataHtml;
import cn.bctools.design.data.fields.dto.form.FormDesignHtml;
import cn.bctools.design.data.fields.dto.page.ButtonDesignHtml;
import cn.bctools.design.data.fields.enums.DataFieldType;
import cn.bctools.design.data.fields.enums.DesignType;
import cn.bctools.design.data.service.DataFieldService;
import cn.bctools.design.data.service.DataModelService;
import cn.bctools.design.jvslog.service.impl.JvsLogServiceImpl;
import cn.bctools.design.menu.entity.AppMenu;
import cn.bctools.design.menu.service.AppMenuService;
import cn.bctools.design.project.dto.DesignRoleSettingDto;
import cn.bctools.design.rule.entity.RuleDesignPo;
import cn.bctools.design.rule.service.RuleDesignService;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.function.entity.po.FunctionBusinessPo;
import cn.bctools.function.mapper.FunctionBusinessMapper;
import cn.bctools.log.annotation.Log;

import cn.bctools.protection.ratelimiter.core.annotation.RateLimiter;
import cn.bctools.protection.ratelimiter.core.keyresolver.impl.ExpressionRateLimiterKeyResolver;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.bctools.design.data.fields.enums.DataFieldType.RESERVED_WORDS;

/**
 * 表单设计
 *
 * <AUTHOR>
 */
@Api(tags = "表单设计")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/app/design/{appId}/form")
public class FormDesignController {

    FormService formService;
    CrudPageService pageService;
    DataModelService dataModelService;
    DataFieldService dataFieldService;
    JvsCrudAssociationService jvsCrudAssociationService;
    RuleDesignService ruleDesignService;
    FunctionBusinessMapper execMapper;
    AppMenuService appMenuService;

    @Log
    @ApiOperation(value = "表单-分页")
    @GetMapping("/page")
    public R<Page<FormPo>> page(Page<FormPo> page, FormPo formPo, @PathVariable String appId) {
        LambdaQueryWrapper<FormPo> wrapper = this.handleOrder(page);
        String type = formPo.getType();
        String name = StrUtil.trimToEmpty(formPo.getName());
        String desc = StrUtil.trimToEmpty(formPo.getDescription());
        formService.page(page, wrapper
                //查询非view_json的字段
                .select(FormPo.class, tableFieldInfo -> !tableFieldInfo.getProperty().equalsIgnoreCase(Get.name(FormPo::getViewJson)))
                //按表单类
                .eq(ObjectUtil.isNotNull(type), FormPo::getType, type)
                //应用表单只能引用当前应用下的单表
                .eq(ObjectUtil.isNotNull(formPo.getJvsAppId()), FormPo::getJvsAppId, appId)
                //按名称
                .like(StrUtil.isNotBlank(name), FormPo::getName, name)
                //按描述
                .like(StrUtil.isNotBlank(desc), FormPo::getDescription, desc));
        return R.ok(page);
    }

    @Log
    @ApiOperation(value = "下拉框选择")
    @GetMapping("/list")
    public R<List<FormSelectItemDto>> list(@ApiParam("名称(模糊搜索)") @RequestParam(name = "name", required = false) String name, @PathVariable String appId) {
        name = StrUtil.trimToEmpty(name);
        // 直接设计的表单
        List<FormPo> formPoList = formService.list(Wrappers.<FormPo>lambdaQuery()
                .select(FormPo::getId, FormPo::getName, FormPo::getDataModelId, FormPo::getJvsAppId)
                .eq(FormPo::getJvsAppId, appId)
                .like(StrUtil.isNotBlank(name), FormPo::getName, name)
                .orderByDesc(FormPo::getCreateTime));
        List<FormSelectItemDto> itemList = BeanCopyUtil.copys(formPoList, FormSelectItemDto.class);
        // 列表页中的表单
        List<FormSelectItemDto> pageFormList = pageService.getAllForm(name, appId);
        itemList.addAll(pageFormList);
        return R.ok(itemList);
    }

    @Log
    @ApiOperation("根据权限获取预览和使用的结果")
    @GetMapping("/{id}")
    @Transactional(rollbackFor = Exception.class)
    public R<FormPo> getById(@PathVariable("id") String id, @PathVariable String appId) {
        FormPo formPo = formService.getOne(Wrappers.query(new FormPo().setJvsAppId(appId).setId(id)));
        if (ObjectNull.isNull(formPo)) {
            return R.ok(formPo);
        }
        AppMenu appMenu = appMenuService.getDesignMenu(formPo.getId(), formPo.getJvsAppId());
        formPo.setAppMenu(appMenu);
        return R.ok(formPo);
    }

    @Log(callBackClass = JvsLogServiceImpl.class)
    @ApiOperation("新增")
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    @RateLimiter(time = 3, count = 1, keyResolver = ExpressionRateLimiterKeyResolver.class,
            keyArg = "'formCreate-' + #appId")
    public R<FormPo> create(@RequestBody @Valid FormPo design, @PathVariable String appId) {
        DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(appId));
        design.setJvsAppId(appId);
        return R.ok(formService.create(design));
    }

    @Log
    @ApiOperation("检查字段是否为类型变更")
    @PutMapping("/check")
    public R checkFields(@RequestBody @Valid FormDto design, @PathVariable String appId) {
        design.setJvsAppId(appId);
        if (ObjectNull.isNotNull(design.getViewJson())) {
            //TODO 是否有数据类型发生改化
            FormDesignHtml form = JSON.parseObject(design.getViewJson(), FormDesignHtml.class);
            if (Objects.isNull(form)) {
                //如果所有都是输入旷
                return R.ok();
            }
            List<FormDataHtml> formData = form.getFormdata();
            if (ObjectUtils.isEmpty(formData)) {
                return R.ok();
            }
            FormDataHtml formDataHtml = formData.get(0);
            List<Map<String, Object>> forms = formDataHtml.getForms();
            if (ObjectUtils.isEmpty(forms)) {
                return R.ok();
            }

            // 保存字段信息
            // 保存字段信息
            List<DataFieldPo> fieldPos = DesignUtils.getFields(forms, design.getDataModelId(), design.getId());
            List<String> collect = fieldPos.stream().filter(e -> RESERVED_WORDS.contains(e.getFieldKey())).map(DataFieldPo::getFieldKey).collect(Collectors.toList());
            if (ObjectNull.isNotNull(collect)) {
                throw new BusinessException("字段名不允许使用" + collect);
            }
            Map<String, DataFieldPo> fields = fieldPos
                    .stream()
                    .filter(e -> !e.getFieldType().equals(DataFieldType.input))
                    .collect(Collectors.toMap(DataFieldPo::getFieldKey, Function.identity()));

            Map<String, List<DataFieldPo>> allFieldGroup = dataFieldService.list(Wrappers.query(new DataFieldPo()
                                    .setDesignType(DesignType.form)
                                    .setModelId(design.getDataModelId()))
                            .lambda().select(DataFieldPo::getFieldKey, DataFieldPo::getDesignJson, DataFieldPo::getDesignId, DataFieldPo::getFieldType))
                    .stream()
                    .filter(e -> ObjectNull.isNotNull(e.getFieldType()))
                    .collect(Collectors.groupingBy(DataFieldPo::getFieldKey));
            //如果库里面没有其它类型
            if (ObjectNull.isNull(allFieldGroup)) {
                return R.ok();
            }
            //根据类型排序

            //查询出数据库中所有的类型，校验能不能和前端修改的类型进行处理，如果确定
            List<FieldPublicHtml> updateField = allFieldGroup.keySet()
                    .stream()
                    .peek(e -> {
                        //添加现有的数据设计
                        List<DataFieldPo> dataFieldPos = allFieldGroup.get(e);
                        if (fields.containsKey(e)) {
                            dataFieldPos.add(BeanCopyUtil.copy(fields.get(e), DataFieldPo.class));
                        }
                    })
                    .filter(fields::containsKey)
                    .map(e -> {
                        //获取 库里面的类型
                        List<DataFieldPo> dataFieldPos = allFieldGroup.get(e);
                        //获取前端要修改的类型
                        DataFieldPo dataFieldDto = fields.get(e);
                        DataFieldType dataFieldType = dataFieldDto.getFieldType();
                        for (DataFieldPo dataFieldPo : dataFieldPos) {
                            if (dataFieldPo.getFieldType().equals(dataFieldType)) {
                                continue;
                            }
                            if (!dataFieldPo.getFieldType().getTransformationList().contains(dataFieldType)) {
                                //TODO 有索引可能导致无法删除字段
                                FieldPublicHtml fieldBasicsHtml = new FieldPublicHtml();
                                FormPo one = formService.getOne(new LambdaQueryWrapper<FormPo>().eq(FormPo::getId, dataFieldPo.getDesignId()).select(FormPo::getName, FormPo::getId));
                                if (ObjectNull.isNull(one)) {
                                    return null;
                                }
                                fieldBasicsHtml.setFieldName(dataFieldDto.getFieldName() + "组件变更" + one.getName() + "设计[" + dataFieldPo.getFieldType().getDesc() + "]改变为[" + dataFieldDto.getFieldType().getDesc() + "]").setFieldKey(dataFieldDto.getFieldKey()).setType(dataFieldDto.getFieldType());
                                return fieldBasicsHtml;
                            }
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (ObjectNull.isNotNull(updateField)) {
                return R.ok(updateField);
            }
        }
        return R.ok();
    }

    @Log(callBackClass = JvsLogServiceImpl.class)
    @ApiOperation("更新")
    @PutMapping
    @Transactional(rollbackFor = Exception.class)
    @RateLimiter(time = 3, count = 1, keyResolver = ExpressionRateLimiterKeyResolver.class,
            keyArg = "'formUpdate-' + #appId + '-' + #designDto['id']")
    public R<FormPo> update(@RequestBody @Valid FormDto designDto, @PathVariable String appId) {
        DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(appId));
        FormPo design = BeanCopyUtil.copy(designDto, FormPo.class);
        //检查当前设计是否过期，防止覆盖其他人的修改
//        FormPo dbpo = formService.get(design.getId());
//        if (null != design.getUpdateTime()) {
//            if (design.getUpdateTime().isBefore(dbpo.getUpdateTime())) {
//                throw new BusinessException("当前设计不是最新版本");
//            }
//        }
        design.setJvsAppId(appId);
        String id = design.getId();
        String name = design.getName();
        String type = design.getType();
        String jvsAppId = design.getJvsAppId();
        String viewJson = design.getViewJson();
        String dataModelId = design.getDataModelId();
        List<DataFieldPo> fields = Collections.emptyList();

        // 修改模型名称
        dataModelService.updateName(dataModelId, name);
        AppMenu appMenu = new AppMenu()
                .setName(design.getName())
                .setDesignType(DesignType.form)
                .setJvsAppId(appId)
                .setDataModelId(dataModelId)
                .setDesignId(design.getId());
        appMenuService.update(appMenu);
        if (ObjectNull.isNotNull(viewJson)) {
            FormDesignHtml formDesignHtml = JSON.parseObject(viewJson, FormDesignHtml.class);
            if (Objects.isNull(formDesignHtml)) {
                //如果所有都是输入旷
                return R.ok();
            }
            //判断是否有表单引用 ，需要替换
            getLinkFormHtml(formDesignHtml, id, viewJson);
            List<FormDataHtml> formData = formDesignHtml.getFormdata();
            if (ObjectUtils.isEmpty(formData)) {
                return R.ok();
            }
            FormDataHtml formDataHtml = formData.get(0);
            //获取所有的按钮
            List<ButtonDesignHtml> btnSetting = formDataHtml.getFormsetting().getBtnSetting();
            appMenu.setPermissionJson(JSONArray.parseArray(JSON.toJSONString(btnSetting)))
                    .setRole(JSONArray.parseArray(JSON.toJSONString(designDto.getRole())))
                    .setRoleType(designDto.getRoleType())
                    .setIcon(StringUtils.defaultString(designDto.getIcon(), ""));
            appMenuService.update(appMenu);

            List<Map<String, Object>> forms = formDataHtml.getForms();
//            if (ObjectUtils.isEmpty(forms)) {
//                return R.ok();
//            }

            DesignUtils.checkFormButton(formDesignHtml);
            // 保存字段信息
            fields = DesignUtils.getFields(formDesignHtml, dataModelId, id);
            formService.associationSettingsFields(design, formDesignHtml, fields);

            //获取按钮里面的规则
            //删除多余的数据
            jvsCrudAssociationService.remove(Wrappers.query(new CrudAssociationPo().setDataModelId(design.getDataModelId()).setJvsAppId(design.getJvsAppId()).setDesignId(design.getId())));
            //处理规则
            List<CrudAssociationPo> collect = btnSetting
                    .stream()
                    .filter(e -> e.getEnable() && ObjectNull.isNotNull(e.getAssociation()))
                    .map(e -> new CrudAssociationPo()
                            .setDataModelId(design.getDataModelId())
                            .setDesignId(design.getId())
                            .setJvsAppId(design.getJvsAppId())
                            .setName(e.getName())
                            .setPermissionFlag(e.getPermissionFlag())
                            .setData(e.getAssociation())
                    )
                    .collect(Collectors.toList());
            if (ObjectNull.isNotNull(collect)) {
                jvsCrudAssociationService.saveBatch(collect);
            }
        }
        formService.updateById(design);
        // 处理数据模型
        dataFieldService.updateFields(appId, id, DesignType.form, dataModelId, fields);
        //TODO 将变化数据，返回给前端后然后直接强制更新数据库所有数据
//        if (ObjectNull.isNotNull(designDto.getDeleteFieldsKey())) {
//            List<String> collect = designDto.getDeleteFieldsKey().stream().map(FieldBasicsHtml::getFieldKey).collect(Collectors.toList());
//            dynamicDataService.deleteFields(collect, dataModelId);
//        }
        return R.ok(design);
    }

    private void getLinkFormHtml(FormDesignHtml formDesignHtml, String id, String viewJson) {
        if (ObjectNull.isNotNull(formDesignHtml.getLinkFormId())) {
            //根据公式的 Id进行替换数据
            List<FunctionBusinessPo> functionBusinessPos = execMapper.selectList(Wrappers.query(new FunctionBusinessPo().setDesignId(formDesignHtml.getLinkFormId())));
            for (FunctionBusinessPo e : functionBusinessPos) {
                //获取所有公式如果有公式则直接替换，并生成新的公式
                if (viewJson.contains(e.getId())) {
                    String businessId = e.getId();
                    e.setId(null);
                    e.setDesignId(id);
                    execMapper.insert(e);
                    viewJson = viewJson.replace(businessId, e.getId());
                }
            }

        }
        //查询当前这个设计的全部公式，删除多余的
        List<FunctionBusinessPo> functionBusinessPos = execMapper.selectList(Wrappers.query(new FunctionBusinessPo().setDesignId(id)));
        for (FunctionBusinessPo e : functionBusinessPos) {
            if (!viewJson.contains(e.getId())) {
                execMapper.deleteById(e.getId());
            }
        }
        //给设计重新赋值
        formDesignHtml = JSON.parseObject(viewJson, FormDesignHtml.class);
    }

    @Log(callBackClass = JvsLogServiceImpl.class)
    @ApiOperation("发布")
    @PutMapping("/deploy/{id}")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> deploy(@PathVariable("id") String id, @PathVariable String appId) {
        DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(appId));
        formService.deploy(appId, id);
        return R.ok(true, "发布成功");
    }

    @Log(callBackClass = JvsLogServiceImpl.class)
    @PostMapping("/unload/{id}")
    @ApiOperation("卸载")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> unload(@ApiParam("设计id") @PathVariable("id") String id, @PathVariable String appId) {
        DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(appId));
        formService.unload(appId, id);
        return R.ok(true, "卸载成功");
    }

    @Deprecated
    @Log(callBackClass = JvsLogServiceImpl.class)
    @DeleteMapping("/del/{id}")
    @ApiOperation("删除")
    public R<Boolean> del(@PathVariable("id") String id, @PathVariable String appId) {
        DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(appId));
        formService.delete(appId, id);
        return R.ok(true, "删除成功");
    }

    /**
     * 处理排序字段
     *
     * @param page 分页查询条件
     * @param <T>  查询对象
     * @return 查询条件对象
     */
    private <T extends BasalPo> LambdaQueryWrapper<T> handleOrder(Page<T> page) {
        QueryWrapper<T> wrapper = Wrappers.query();
        List<OrderItem> orders = page.orders();
        if (ObjectUtils.isEmpty(orders)) {
            wrapper.orderByDesc("create_time");
            return wrapper.lambda();
        }
        for (OrderItem order : orders) {
            String column = order.getColumn();
            boolean isAsc = order.isAsc();
            wrapper.orderBy(StringUtils.isNotBlank(column), isAsc, column);
        }
        return wrapper.lambda();
    }


    @GetMapping("/clear/{id}")
    @ApiOperation(value = "清空设计", notes = "清空设计，包括公式，逻辑")
    @Transactional(rollbackFor = Exception.class)
    public R clear(@PathVariable String id, @PathVariable String appId) {
        //清空公式
        execMapper.delete(Wrappers.query(new FunctionBusinessPo().setJvsAppId(appId).setDesignId(id)));
        //清空逻辑
        ruleDesignService.remove(Wrappers.query(new RuleDesignPo().setJvsAppId(appId).setComponentId(id).setComponentType(DesignType.form)));
        return R.ok();
    }

    @Log
    @ApiOperation(value = "获取指定模型的所有表单")
    @GetMapping("/model/{modelId}/all")
    public R<List<FormPo>> getModelAllForm(@PathVariable String appId, @PathVariable String modelId) {
        return R.ok(formService.list(Wrappers.<FormPo>lambdaQuery()
                .eq(FormPo::getDataModelId, modelId)
                .eq(FormPo::getJvsAppId, appId)
                //查询非view_json的字段
                .select(FormPo.class, tableFieldInfo -> !tableFieldInfo.getProperty().equalsIgnoreCase(Get.name(FormPo::getViewJson)))
                .orderByDesc(FormPo::getCreateTime)));
    }
}