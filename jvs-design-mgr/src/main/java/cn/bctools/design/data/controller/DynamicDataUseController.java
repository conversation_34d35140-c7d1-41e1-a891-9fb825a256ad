package cn.bctools.design.data.controller;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.*;
import cn.bctools.common.utils.function.Get;
import cn.bctools.database.entity.po.BasePo;
import cn.bctools.design.constant.DesignConstant;
import cn.bctools.design.constant.DynamicDataConstant;
import cn.bctools.design.crud.entity.CrudPage;
import cn.bctools.design.crud.service.CrudPageService;
import cn.bctools.design.crud.service.FormService;
import cn.bctools.design.crud.utils.Decimal128ListConvert;
import cn.bctools.design.crud.utils.DesignUtils;
import cn.bctools.design.data.component.DataModelHandler;
import cn.bctools.design.data.dto.ButtonRtnVo;
import cn.bctools.design.data.dto.SaveRelationAndRunRuleDto;
import cn.bctools.design.data.entity.DataFieldPo;
import cn.bctools.design.data.entity.DataModelPo;
import cn.bctools.design.data.entity.DynamicDataPo;
import cn.bctools.design.data.fields.IDataFieldHandler;
import cn.bctools.design.data.fields.dto.*;
import cn.bctools.design.data.fields.dto.enums.AggregationTypeEnum;
import cn.bctools.design.data.fields.dto.enums.ButtonTypeEnum;
import cn.bctools.design.data.fields.dto.form.FormValueHtml;
import cn.bctools.design.data.fields.dto.form.MultipleHtml;
import cn.bctools.design.data.fields.dto.form.item.TabGenerateItemHtml;
import cn.bctools.design.data.fields.dto.form.item.TabItemHtml;
import cn.bctools.design.data.fields.dto.page.*;
import cn.bctools.design.data.fields.enums.DataFieldType;
import cn.bctools.design.data.fields.enums.DataQueryType;
import cn.bctools.design.data.fields.enums.FormComponentType;
import cn.bctools.design.data.fields.impl.container.TabFieldHandler;
import cn.bctools.design.data.fields.impl.container.TabGenerateFieldHandler;
import cn.bctools.design.data.fields.impl.container.TableFormFieldHandler;
import cn.bctools.design.data.service.DataFieldService;
import cn.bctools.design.data.service.DataModelService;
import cn.bctools.design.data.service.DynamicDataService;
import cn.bctools.design.expression.EnvConstant;
import cn.bctools.design.notice.handler.DataNoticeHandler;
import cn.bctools.design.notice.handler.enums.TriggerTypeEnum;
import cn.bctools.design.notice.handler.util.NoticeVariableUtils;
import cn.bctools.design.project.dto.ButtonSettingDto;
import cn.bctools.design.project.handler.DesignHandler;
import cn.bctools.design.rule.RuleRunService;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.design.workflow.service.FlowDynamicDataService;
import cn.bctools.design.workflow.service.impl.FlowDynamicDataServiceImpl;
import cn.bctools.function.entity.dto.ExecDto;
import cn.bctools.function.entity.dto.TableType;
import cn.bctools.function.handler.ExpressionAfterHandler;
import cn.bctools.function.handler.IJvsFunction;
import cn.bctools.function.mapper.FunctionBusinessMapper;
import cn.bctools.log.annotation.Log;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.oss.template.OssTemplate;
import cn.bctools.rule.error.MessageTipsDto;
import cn.bctools.rule.utils.html.ResultDto;
import cn.bctools.rule.utils.html.RuleExecuteDto;
import cn.bctools.web.excel.ArrayListConvert;
import cn.bctools.web.excel.LocalDateTimeConvert;
import cn.bctools.web.utils.HttpRequestUtils;
import cn.bctools.web.utils.WebUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Nullable;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.bctools.design.util.DynamicDataUtils.*;

/**
 * 动态数据管理
 *
 * @Author: GuoZi
 */
@Slf4j
@Api(tags = "[data]动态数据")
@RestController
@AllArgsConstructor
@RequestMapping("/app/use/{appId}/dynamic/data")
public class DynamicDataUseController {
    public static final String FILE_TYPE = ".xlsx";
    public static final String FILE_NAME_EXPORT = "导出数据-";
    public static final String FILE_NAME_TEMPLATE = "导入模板-";
    private static final String FORM_ID = "formId";
    private static final Integer EXCEL_DATA_MIN_SIZE = 2;

    FormService formService;
    CrudPageService pageService;
    FunctionBusinessMapper functionBusinessMapper;
    DataFieldService dataFieldService;
    OssTemplate ossTemplate;
    DynamicDataService dynamicDataService;
    ExpressionAfterHandler expressionAfterHandler;
    DataModelHandler dataModelHandler;
    DataModelService dataModelService;
    Map<String, IDataFieldHandler> fieldHandlerMap;
    TabFieldHandler tabFieldHandler;
    DesignHandler designHandler;
    ArrayListConvert arrayListConvert;
    Decimal128ListConvert decimal128ListConvert;
    LocalDateTimeConvert localDateTimeConvert;
    DataNoticeHandler dataNoticeHandler;
    RuleRunService ruleRunService;
    FlowDynamicDataService flowDynamicDataService;

    /**
     * 获取字段列表
     *
     * @param modelId 模型id
     * @return 字段信息集合
     */
    @ApiOperation("获取字段列表")
    @GetMapping("/fields/{modelId}")
    public R<List<FieldBasicsHtml>> getFields(@PathVariable("modelId") String modelId, @PathVariable String appId) {
        List<FieldBasicsHtml> fields = dataFieldService.getFields(appId, modelId, true, true);
        // 不返回容器组件
        fields.removeIf(field -> FormComponentType.container.equals(field.getFieldType().getItemType()));
        return R.ok(fields);
    }

    @Log
    @ApiOperation("获取模型名称")
    @GetMapping("/modelName/{modelId}")
    public R<DataModelPo> getDataModelName(@PathVariable("modelId") String modelId, @PathVariable String appId) {
        DataModelPo byId = dataModelService.getById(modelId);
        if (!byId.getAppId().equals(appId)) {
            return R.failed(DesignConstant.APP_ERROR_MSG);
        }
        return R.ok(byId);
    }

    @Log
    @ApiOperation("获取自定义按钮的组件信息重定向")
    @PostMapping("/{modelId}/button/{dataId}")
    public R getButton(HttpServletRequest request, @PathVariable("dataId") String dataId, @PathVariable("modelId") String modelId, @PathVariable String appId, @RequestHeader(value = "Authorization", required = false) String token, @RequestBody Map<String, String> body) {
        String address = body.get("address");
        String permissionFlag = body.get("permissionFlag");
        DynamicDataPo byId = dynamicDataService.getById(modelId, dataId);
        Map<String, Object> jsonData = new HashMap<>();
        if (!ObjectNull.isNull(byId)) {
            jsonData = byId.getJsonData();
        }

        jsonData.put("realName", UserCurrentUtils.getRealName());
        jsonData.put("userId", UserCurrentUtils.getUserId());
        jsonData.put("token", token);
        jsonData.put("id", dataId);
        jsonData.put("dataId", dataId);
        jsonData.put("domain", HttpRequestUtils.getHostAndPort(request));

        Document document = NoticeVariableUtils.replacement(address.replaceAll("&nbsp;", ""), jsonData);
        StringBuilder text = new StringBuilder();
        document.getElementsByTag("p").forEach(p -> text.append(p.text()));

        return R.ok(text.toString());
    }

    /**
     * 数据联动触发器处理
     *
     * @param modelId  模型id
     * @param designId 表单设计id
     * @return 字段信息集合
     */
    @ApiOperation("数据联动触发器处理")
    @PostMapping("/DataModelTriggering/{designId}/{modelId}")
    public R<Map<String, Object>> getFields(@PathVariable String appId, @PathVariable("modelId") String modelId, @ApiParam("是否对公式进行扩展化处理") @RequestHeader(value = "init", required = false, defaultValue = "false") Boolean init, @ApiParam("表格操作") @RequestHeader(value = "tableType", required = false, defaultValue = "line") TableType tableType, @PathVariable String designId, @RequestBody ExecDto body) {

        //获取参与的公式如果
        // 记录设计id
        SystemThreadLocal.set(IJvsFunction.KEY_DESIGN_ID, designId);
        //添加当前的表格的操作行数据
        SystemThreadLocal.set("index", body.getIndex());
        //操作的设计 id
        SystemThreadLocal.set("designId", designId);
        //表格的操作类型， 如果是表可的操作， 有行级操作，或新增，或删除
        SystemThreadLocal.set("tableType", tableType);
        //跳过模拟用户操作,让公式可以自己进行一次更新。
        SystemThreadLocal.set("designSkip", init);
        //获取所有的字段
        List<FieldBasicsHtml> collect = dataFieldService.getFields(appId, modelId, designId, true, true).stream().filter(e -> ObjectNull.isNotNull(e.getType())).collect(Collectors.toList());
        Map<String, FieldBasicsHtml> fieldsMap = collect.stream().collect(Collectors.toMap(FieldBasicsHtml::getFieldKey, Function.identity()));
        //判断组件中是否有选项卡，如果有将选项卡选择了脱离数据，将脱离数据的 Key 放到字段解析中
        new ArrayList<>(collect)
                //因为这个循环是改变内部对象，所以需要创建一个新的
                .stream()
                .filter(e -> e.getType().equals(DataFieldType.tab))
                .filter(e -> {
                    Object eval = JvsJsonPath.read(e.getDesignJson(), Get.name(TabItemHtml::getDetachData));
                    if (ObjectNull.isNotNull(eval)) {
                        return (boolean) eval;
                    } else {
                        //兼容老的选项卡数据值
                        return true;
                    }
                })
                .forEach(e -> {
                    //如果有将所有的 key字段添加到字段解析中
                    TabItemHtml html = tabFieldHandler.toHtml(e.getDesignJson());
                    for (FormValueHtml dicDatum : html.getDicData()) {
                        List<FieldBasicsHtml> fieldBasicsHtmls = html.getColumn().get(dicDatum.getName());

                        if (ObjectNull.isNotNull(dicDatum.getProp())) {
                            //如果有配置，需要组件为一个生成组件
                            TabGenerateItemHtml tabGenerateItemHtml = new TabGenerateItemHtml(dicDatum.getProp()).setColumn(fieldBasicsHtmls);
                            collect.add(tabGenerateItemHtml);
                        } else {
                            if (ObjectNull.isNotNull(fieldBasicsHtmls)) {
                                //将tab组件全部放到处理里面
                                collect.addAll(fieldBasicsHtmls);
                            }
                        }
                    }
                });
        //1、寻找结构。获取关联
        //2、寻找字段
        //根据触发key寻找数据关联的所有字段
        collect.forEach(e -> {
            IDataFieldHandler iDataFieldHandler = fieldHandlerMap.get(e.getType().getDesc());
            if (ObjectNull.isNotNull(e.getDesignJson(), iDataFieldHandler)) {
                FieldBasicsHtml publicHtml = iDataFieldHandler.toHtml(e);

                if (init) {
                    //如果是表格类型,或选项卡类型，需要进行数据筛选进行处理
                    if (iDataFieldHandler instanceof TableFormFieldHandler || iDataFieldHandler instanceof TabFieldHandler || iDataFieldHandler instanceof TabGenerateFieldHandler) {
                        iDataFieldHandler.tableSetData(fieldHandlerMap, fieldsMap, publicHtml, body.getParams(), publicHtml.getProp());
                    }
                }
                if (ObjectNull.isNotNull(body.getParentKey())) {
                    String[] parentPath = body.getParentKey().toArray(new String[body.getParentKey().size()]);
                    //如果是有父级，需要判断是是否容器组件类型，如果是容器组件类型，需要将父级处理了再进行下调。
                    if (ObjectNull.isNotNull(body.getModifiedField())) {

                        if (iDataFieldHandler instanceof TableFormFieldHandler || iDataFieldHandler instanceof TabFieldHandler || iDataFieldHandler instanceof TabGenerateFieldHandler) {
                            iDataFieldHandler.filterOrDataLinkage(appId, fieldsMap, body.getModifiedField(), publicHtml, body.getParams(), body.getIndex(), parentPath);
                        } else {
                            iDataFieldHandler.filterOrDataLinkage(appId, fieldsMap, body.getModifiedField(), publicHtml, body.getParams(), body.getIndex(), "");
                        }
                    }
                } else {
                    String[] parentPath = new String[]{publicHtml.getProp()};
                    iDataFieldHandler.filterOrDataLinkage(appId, fieldsMap, body.getModifiedField(), publicHtml, body.getParams(), body.getIndex(), parentPath);
                }
            }
        });


        Map<String, Object> handler = expressionAfterHandler.handler(designId, init, body);
        //清除结果为空数组的字段
        Map<String, Object> parse = JSON.parseObject(JSON.toJSONString(handler, JSONWriter.Feature.LargeObject, JSONWriter.Feature.NotWriteEmptyArray));
        return R.ok(parse);
    }

    /**
     * 新增数据
     *
     * @param modelId 模型id
     * @param data    数据内容
     * @return 保存后的数据id
     */
    @Log
    @ApiOperation("新增数据")
    @PostMapping("/save/{modelId}")
    public R saveDynamicData(@PathVariable String appId, @PathVariable("modelId") String modelId, @RequestBody Map<String, Object> data) {
        DynamicDataUtils.checkPermit();
        DynamicDataUtils.clearEcho(data);
        dynamicDataService.checkDataModel(data, modelId);
        RuleExecuteDto executeDto = dynamicDataService.save(appId, modelId, data);
        if (ObjectNull.isNotNull(executeDto) && ObjectNull.isNotNull(executeDto.getStats())) {
            if (executeDto.getStats()) {
                dataNoticeHandler.sendNotify(TenantContextHolder.getTenantId(), appId, TriggerTypeEnum.CREATED, modelId, String.valueOf(data.get("dataId")), data);
                return R.ok().setMsg(executeDto.getSyncMessageTips()).setData(executeDto.getMessageResult());
            } else {
                //返回逻辑定义的错误信息
                return R.failed(executeDto.getSyncMessageTips());
            }
        }
        return R.ok().setMsg("新增成功");
    }


    /**
     * 删除数据
     *
     * @param modelId 模型id
     * @param dataId  数据id
     * @return 数据版本号
     */
    @Log
    @ApiOperation("删除数据")
    @DeleteMapping("/delete/{modelId}/{dataId}")
    public R deleteDynamicData(@PathVariable String appId, @PathVariable("modelId") String modelId, @PathVariable("dataId") String dataId) {
        Map<String, Object> data = dynamicDataService.querySingle(appId, modelId, dataId);
        DynamicDataUtils.checkPermit();
        RuleExecuteDto executeDto = dynamicDataService.remove(modelId, dataId);
        dataNoticeHandler.sendNotify(TenantContextHolder.getTenantId(), appId, TriggerTypeEnum.DELETED, modelId, dataId, data);
        if (ObjectNull.isNotNull(executeDto) && ObjectNull.isNotNull(executeDto.getStats())) {
            if (executeDto.getStats()) {
                dataNoticeHandler.sendNotify(TenantContextHolder.getTenantId(), appId, TriggerTypeEnum.CREATED, modelId, String.valueOf(data.get("dataId")), data);
                return R.ok(executeDto.getSyncMessageTips());
            } else {
                //返回逻辑定义的错误信息
                return R.failed(executeDto.getSyncMessageTips());
            }
        }
        return R.ok().setMsg("删除成功");
    }

    /**
     * 删除数据
     *
     * @param modelId 模型id
     * @return 数据版本号
     */
    @Log
    @ApiOperation("批量删除数据")
    @PostMapping("/batch/delete/{modelId}")
    public R batchDeleteDynamicData(@PathVariable String appId, @PathVariable("modelId") String modelId, @RequestBody List<String> ids) {
        List<Map> maps = dynamicDataService.getByIds(modelId, ids);
        for (Map<String, Object> map : maps) {
            String id = String.valueOf(map.get("id"));
            dynamicDataService.remove(modelId, id);
            dataNoticeHandler.sendNotify(TenantContextHolder.getTenantId(), appId, TriggerTypeEnum.DELETED, modelId, id, map);
        }
        return R.ok().setMsg("删除成功");
    }

    @Log
    @ApiOperation("删除数据")
    @DeleteMapping("/delete/list/{modelId}")
    public R deleteDynamicData(@PathVariable String appId, @PathVariable("modelId") String modelId, @RequestParam("dataIds") List<String> dataIds) {
        for (String dataId : dataIds) {
            Map<String, Object> data = dynamicDataService.querySingle(appId, modelId, dataId);
            DynamicDataUtils.checkPermit();
            RuleExecuteDto executeDto = dynamicDataService.remove(modelId, dataId);
            if (ObjectNull.isNotNull(executeDto) && ObjectNull.isNotNull(executeDto.getStats())) {
                //如果是失败了、直接返回错误信息
                if (!executeDto.getStats()) {
                    return R.failed(executeDto.getSyncMessageTips());
                }
                dataNoticeHandler.sendNotify(TenantContextHolder.getTenantId(), appId, TriggerTypeEnum.DELETED, modelId, dataId, data);
            }
        }
        ;
        return R.ok().setMsg("删除成功");
    }

    /**
     * 修改数据
     *
     * @param modelId 模型id
     * @param dataId  数据id
     * @param data    数据内容
     * @return 数据版本号
     */
    @Log
    @ApiOperation("修改数据")
    @PostMapping("/update/{modelId}/{dataId}")
    public R updateDynamicData(@PathVariable String appId, @PathVariable("modelId") String modelId, @PathVariable("dataId") String dataId, @RequestBody Map<String, Object> data) {
        DynamicDataUtils.checkPermit();
        DynamicDataUtils.clearEcho(data);

        // 为允许为空，且未传递数据的字段设置默认值，使其可以置空
        String designId = DynamicDataUtils.getDesignId();
        Map<String, FieldBasicsHtml> fieldsMap = dataFieldService.getFields(appId, modelId, designId, true, true).stream().filter(e -> ObjectNull.isNotNull(e.getType())).collect(Collectors.toMap(FieldBasicsHtml::getFieldKey, Function.identity()));
        for (FieldBasicsHtml e : fieldsMap.values()) {
            IDataFieldHandler iDataFieldHandler = fieldHandlerMap.get(e.getType().getDesc());
            // 没有找到实现类，直接跳过
            if (ObjectNull.isNull(iDataFieldHandler)) {
                continue;
            }
            //判断为空，跳过，目前不知道为什么，偶然试出来的
            if (e.getDesignJson() == null) {
                continue;
            }
            FieldPublicHtml publicHtml = iDataFieldHandler.toHtml(e);
            //如果key不为空,并提交值为空,并允许值为空才设置为空占位
            boolean setEmpty = ObjectNull.isNotNull(publicHtml.getProp()) && Boolean.TRUE.equals(publicHtml.getEmptyEnable()) && ObjectNull.isNull(data.get(publicHtml.getProp()));
            if (setEmpty) {
                //处理分割线和小标题，不属于数据
                if (ObjectNull.isNotNull(publicHtml.getType().getAClass()) && !publicHtml.getType().equals(DataFieldType.tab)) {
                    data.put(publicHtml.getProp(), DynamicDataConstant.getEmpty(publicHtml.getType().getAClass()));
                }
            }
        }

        Map<String, Object> oldData = dynamicDataService.querySingle(appId, modelId, dataId);
        DynamicDataUtils.checkDataCover(oldData.get("updateTime"), data.get("updateTime"),
                () -> log.error("数据更新-当前数据版本落后于最新数据，请重新打开表单填写数据，数据入参为：{}，旧数据为{}", JSON.toJSONString(data), JSON.toJSONString(oldData)));
        // 修改数据
        RuleExecuteDto executeDto = dynamicDataService.update(appId, modelId, dataId, data);
        if (ObjectNull.isNotNull(executeDto) && ObjectNull.isNotNull(executeDto.getStats())) {
            if (!executeDto.getStats()) {
                //成功直接返回
                return R.failed(executeDto.getSyncMessageTips());
            }
            return R.ok().setMsg(executeDto.getSyncMessageTips());
        }

        MapDifference<String, Object> difference = Maps.difference(oldData, data);
        dataNoticeHandler.sendNotify(TenantContextHolder.getTenantId(), appId, TriggerTypeEnum.EDITED, modelId, dataId, data, difference.entriesDiffering().keySet());
        return R.ok().setMsg("编辑成功");
    }

    /**
     * 保存关联数据id集合并调用逻辑引擎
     *
     * @param modelId 模型id
     * @param dataId  数据id
     * @param dto     入参
     * @return 保存后的数据id
     */
    @Log
    @ApiOperation("保存关联数据id集合并调用逻辑引擎")
    @PostMapping("/update/relation/{modelId}/{dataId}")
    public R saveRelationAndRunRule(@PathVariable String appId, @PathVariable("modelId") String modelId, @PathVariable("dataId") String dataId, @RequestBody SaveRelationAndRunRuleDto dto, HttpServletResponse response) {
        Map<String, Object> data = dto.getData();
        DynamicDataUtils.clearEcho(data);
        Map<String, Object> oldData = dynamicDataService.querySingle(appId, modelId, dataId);
        Map<String, Object> relationTagMap = new HashMap<>();
        Optional<Object> relationOptional = Optional.ofNullable(oldData.get(DataFieldService.FIELD_RELATION_TAG));
        if (relationOptional.isPresent()) {
            relationTagMap = (Map<String, Object>) relationOptional.get();
        }
        Object currentRelationIds = data.get(dto.getRelationTag());
        if (ObjectNull.isNotNull(currentRelationIds)) {
            relationTagMap.put(dto.getRelationTag(), currentRelationIds);
        }
        if (ObjectNull.isNotNull(relationTagMap)) {
            data.put(DataFieldService.FIELD_RELATION_TAG, relationTagMap);
        }
        // 修改数据
        dynamicDataService.update(appId, modelId, dataId, data);
        // 调用逻辑引擎。 将关联标识改为固定标识后传递到逻辑引擎
        data.put("ids", currentRelationIds);
        data.remove(DataFieldService.FIELD_RELATION_TAG);
        data.remove(dto.getRelationTag());
        //查询数据将行级数据全部给逻辑引擎
        DynamicDataPo byId = dynamicDataService.getById(modelId, dataId);
        Map<String, Object> jsonData = new HashMap<>();
        if (ObjectNull.isNotNull(byId)) {
            jsonData.putAll(byId.getJsonData());
        }
        jsonData.putAll(data);
        RuleExecuteDto ruleExecDto = ruleRunService.run(dto.getRuleKey(), jsonData);
        if (ObjectNull.isNull(ruleExecDto)) {
            return R.ok();
        }
        R r = R.ok().setMsg("");
        //如果最后一个节点为消息节点
        if (ruleExecDto.getStats() && ObjectNull.isNotNull(ruleExecDto.getMessageResult())) {
            //成功的返回消息
            r = R.ok(ruleExecDto.getMessageResult()).setMsg(ruleExecDto.getSyncMessageTips());
        } else if (!ruleExecDto.getStats()) {
            //写入消息状态,末认所有都是返回成功状态
            response.setHeader("output_status", ruleExecDto.getStats().toString());
            ResultDto endResult = ruleExecDto.getEndResult();
            if (endResult.getValue() instanceof MessageTipsDto) {
                MessageTipsDto value = (MessageTipsDto) endResult.getValue();
                response.setHeader("message_close", String.valueOf(value.getOff()));
                r = R.ok().setMsg(ruleExecDto.getSyncMessageTips()).setData(value.getData());
            } else {
                r = R.ok().setMsg(ruleExecDto.getErrorMessage());
            }
        } else if (ObjectNull.isNotNull(ruleExecDto.getErrorMessage())) {
            response.setHeader("output_status", String.valueOf(false));
            r = R.ok().setMsg(ruleExecDto.getErrorMessage());
        } else if (ObjectNull.isNotNull(ruleExecDto.getEndResult())) {
            Object value = ruleExecDto.getEndResult().getValue();
            if (value instanceof MessageTipsDto) {
                response.setHeader("output_status", ((MessageTipsDto) value).getOnOff().toString());
                response.setHeader("message_close", ((MessageTipsDto) value).getOff().toString());
                r.setData(((MessageTipsDto) value).getData()).setMsg(((MessageTipsDto) value).getMessage());
            } else {
                r.setData(ruleExecDto.getEndResult().getValue());
            }
        }
        return r;
    }

    /**
     * 分页查询数据
     *
     * @param modelId 模型id
     * @param formId  设计ID
     * @param fieldId 组件ID  如果组件ID携带有， 则表示是弹框如果，弹窗查看是否跳过设计数据权限
     * @return 数据集合
     */
    @Log
    @ApiOperation("分页查询数据")
    @PostMapping("/query/page/{modelId}")
    public R<Page<Map<String, Object>>> queryPage(@PathVariable String appId, @PathVariable("modelId") String modelId, @RequestHeader(value = "formId", required = false) String formId, @RequestHeader(value = "fieldId", required = false) String fieldId, @RequestHeader(value = "notification", required = false, defaultValue = "") Object notification, @RequestBody QueryPageDto queryPageDto) {
        //将设计的列表页字段进行拼装

        //使用关键字搜索
        boolean keyWordFlag = ObjectNull.isNotNull(queryPageDto.getKeywords());
        if (keyWordFlag) {
            queryPageDto.setKeywords(queryPageDto.getKeywords().trim());
        }
        //当前这个设计所使用到的字段有哪一些
        Set<String> queryField = new LinkedHashSet<>();
        if (ObjectNull.isNull(queryPageDto.getGroupConditions())) {
            //如果设计中包含
            if (ObjectNull.isNull(formId) || ObjectNull.isNull(fieldId)) {
                //5数据权限
                queryField.addAll(DynamicDataUtils.dataModelScope(modelId));
            }
        }
        //将查询条件的字段添加进去
        if (ObjectNull.isNotNull(queryPageDto.getFieldList())) {
            queryField.addAll(queryPageDto.getFieldList());
        }
        List<QueryConditionDto> queryConditions = queryPageDto.getConditions();
        List<QueryConditionDto> keywordConditions = new ArrayList<>();
        Map<String, String> combiningFieldFormulaContentMap = new HashMap<>();
        Map<String, FieldBasicsHtml> collectMap = dataFieldService.getAllField(appId, modelId, true, true, e -> false).stream().collect(Collectors.toMap(FieldBasicsHtml::getFieldKey,
                Function.identity()));

        //根据设计获取默认地址
        String designId = DynamicDataUtils.getDesignId();
        //显示字段
        List<String> showFields = new ArrayList<>();
        //聚合字段
        List<String> aggregationFields = new ArrayList<>();
        //聚合配置
        Map<String, AggregationTypeEnum> aggregationHandleFieldsMap = new HashMap<>();
        if (StringUtils.isNotBlank(designId)) {
            CrudPage crudPage = pageService.getById(designId);
            if (ObjectNull.isNotNull(crudPage)) {
                PageDesignHtml pageDesignHtml = DesignUtils.parsePage(crudPage.getViewJson());
                DynamicDataUtils.setPageDesignFilterType(pageDesignHtml.getAndFilterType());

                //设置是否限制待审批查询范围
                SystemThreadLocal.set(DynamicDataUtils.LIMIT_PENDING_SEARCH_SCOPE, pageDesignHtml.getLimitPendingSearchScope());

                //获得显示字段数组
                for (DataTableFieldDesignHtml field : pageDesignHtml.getDataPage().getAutoTableFields()) {
                    if (BooleanUtils.isTrue(field.getShow())) {
                        showFields.add(field.getAliasColumnName());
                    }

                    if (BooleanUtils.isTrue(field.getAggregation())) {
                        aggregationFields.add(field.getAliasColumnName());
                    }

                    if (ObjectNull.isNotNull(field.getAggregationType())) {
                        aggregationHandleFieldsMap.put(field.getAliasColumnName(), field.getAggregationType());
                    }

                    //如果关键字查询条件不为空的时候
                    if (keyWordFlag) {
                        QueryConditionDto keywordFieldCondition = new QueryConditionDto().setFieldKey(field.getAliasColumnName())
                                .setEnabledQueryTypes(DataQueryType.like)
                                .setValue(queryPageDto.getKeywords().trim());
                        keywordConditions.add(keywordFieldCondition);
                    }

                    //表示组合显示字段
                    if (ObjectNull.isNotNull(field.getAdvancedSettings())) {
                        if (ObjectNull.isNotNull(field.getAdvancedSettings().getCombiningFieldFormulaContent())) {
                            combiningFieldFormulaContentMap.put(field.getAliasColumnName(), field.getAdvancedSettings().getCombiningFieldFormulaContent());
                        }
                    }
                    //1将字段添加起来
                    queryField.add(field.getAliasColumnName());
                }

                //2排序字段
                if (ObjectNull.isNotNull(pageDesignHtml.getSorts())) {
                    pageDesignHtml.getSorts().forEach(e -> queryField.add(e.getFieldKey()));
                }
                //3按钮公式字段 移动端的按钮
                List<String> formula = pageDesignHtml.getButtons().stream().map(ButtonSettingDto::getFormula).collect(Collectors.toList());
                //添加 公式
                formula.addAll(pageDesignHtml.getButtons().stream().map(ButtonSettingDto::getMobileFormula).collect(Collectors.toList()));
                if (ObjectNull.isNotNull(formula)) {
                    functionBusinessMapper.selectBatchIds(formula).forEach(e -> queryField.addAll(e.getRelatedIds()));
                }
                //列表页筛选条件，与数据权限不同，是直接进行数据筛选
                if (ObjectNull.isNotNull(pageDesignHtml.getParameters())) {
                    pageDesignHtml.getParameters().stream().filter(e -> ObjectNull.isNotNull(e.getValue(), e.getKey(), e.getOperator()))
                            .map(e -> new QueryConditionDto().setValue(e.getOperator().equals(DataQueryType.in) ? e.getValue() : e.getValue().get(0)).setFieldKey(e.getKey()).setEnabledQueryTypes(e.getOperator()))
                            .peek(e -> e.setCrud(true))
                            .forEach(e -> queryPageDto.getConditions().add(e));
                    //4列表过滤
                    pageDesignHtml.getParameters().forEach(e -> queryField.add(e.getKey()));
                }
                // 如果没有指定排序字段，则使用列表的排序设计
                if (CollectionUtils.isEmpty(queryPageDto.getSorts()) && CollectionUtils.isNotEmpty(pageDesignHtml.getSorts())) {
                    queryPageDto.setSorts(BeanCopyUtil.copys(pageDesignHtml.getSorts(), QueryOrderDto.class));
                }
            }
        }
        //删除多余的字段
        collectMap.keySet().removeIf(e -> {
                    if (ObjectNull.isNull(queryField)) {
                        return false;
                    } else {
                        return !queryField.contains(e);
                    }
                }
        );
        List<String> collect = new ArrayList<>(collectMap.keySet());

        boolean andOr = true;
        List<List<QueryConditionDto>> queryGroupConditions = new ArrayList<>();
        if (ObjectNull.isNull(queryConditions) && ObjectNull.isNull(keywordConditions)) {
            queryGroupConditions = CollectionUtils.isNotEmpty(queryPageDto.getGroupConditions()) ? queryPageDto.getGroupConditions() : Collections.singletonList(queryPageDto.getConditions());
            queryGroupConditions = getQueryConditions(queryGroupConditions, collectMap, notification);
        } else {
            if (keywordConditions.isEmpty()) {
                queryGroupConditions.add(queryConditions);
            } else {
                queryGroupConditions.add(keywordConditions);
                queryGroupConditions.add(queryConditions);
            }
        }

        //限制字段回显，需要考虑聚合字段，非聚合字段不回显
        if (ObjectNull.isNotNull(aggregationFields)) {
            showFields = CollUtil.distinct(CollUtil.intersection(showFields, aggregationFields));
        }

        //设置聚合配置
        PageAggregationSetting pageAggregationSetting = new PageAggregationSetting(aggregationFields, aggregationHandleFieldsMap);

        //设置不回显字段
        for (Map.Entry<String, FieldBasicsHtml> entry : collectMap.entrySet()) {
            if (!showFields.contains(entry.getKey())) {
                entry.getValue().setLimitEcho(true);
            }
        }

        Page<DynamicDataPo> page = new Page<>(queryPageDto.getCurrent(), queryPageDto.getSize());
        collect.addAll(dataFieldService.getDoNotShowFields().stream().map(DataFieldPo::getFieldKey).collect(Collectors.toList()));
        return R.ok(dynamicDataService.queryPage(appId, page, modelId, combiningFieldFormulaContentMap, queryGroupConditions, queryPageDto.getSorts(), collect, true, true, andOr,
                new ArrayList<>(collectMap.values()), pageAggregationSetting));
    }

    private List<List<QueryConditionDto>> getQueryConditions(List<List<QueryConditionDto>> conditions, Map<String, FieldBasicsHtml> collectMap, Object notification) {
        //过滤空的条件
        List<List<QueryConditionDto>> queryConditions = conditions.stream().map(groupConditions -> groupConditions.stream().filter(e -> ObjectNull.isNotNull(e.getValue(), e.getEnabledQueryTypes())).peek(e -> {
            //排除查询条件中带_的字段
            if (e.getFieldKey().endsWith(DynamicDataUtils.SUFFIX_ECHO)) {
                e.setFieldKey(null);
                return;
            }
            if (!collectMap.containsKey(e.getFieldKey())) {
                return;
            }
            FieldBasicsHtml fieldBasicsHtml = collectMap.get(e.getFieldKey());
            // 判断请求头是否是弹窗搜索,如果有值，直接放开
            if (fieldBasicsHtml.getType().equals(DataFieldType.input)) {
                if (ObjectNull.isNotNull(notification)) {
                    e.setEnabledQueryTypes(DataQueryType.like);
                }
            }
            //关联了数据模型的下拉选择框查询,优先使用模糊查询
            //根据下拉数据组件,和单选 组件,只要是多选类,或单选类,进行数据二次转换处理,保证保证列表页查询条件,支持模糊搜索
            if (ObjectNull.isNull(fieldBasicsHtml.getDesignJson())) {
                //如果组件设计为空,则直接返回
                return;
            }
            //如果没有关联直接退出
            if (ObjectNull.isNull(fieldBasicsHtml.getDesignJson().get(FORM_ID))) {
                return;
            }
            //如果是下拉选择
            if (fieldBasicsHtml.getType().equals(DataFieldType.select)) {
                if (e.getEnabledQueryTypes().equals(DataQueryType.in)) {
                    String formId1 = fieldBasicsHtml.getDesignJson().get("formId").toString();
                    Map<String, Object> props = (Map<String, Object>) fieldBasicsHtml.getDesignJson().get("props");
                    String label = props.get("label").toString();
                    //使用数据id作为传递值
                    if ("id".equals(props.get("value"))) {
                        Object o1 = dynamicDataService.getById(formId1, String.valueOf(e.getValue())).getJsonData().get(label);
                        Criteria haoCaiMingChen = DynamicDataUtils.like(new Criteria(), label, o1);
                        DynamicDataUtils.freePermit();
                        ArrayList<String> objects = new ArrayList<>();
                        objects.add(label);
                        List<String> ids = dynamicDataService.queryList(formId1, haoCaiMingChen, objects).stream().map(s -> s.get("id").toString()).collect(Collectors.toList());
                        e.setValue(ids);
                    }
                }
            }
            //没有关联数据模型的下拉选择框 ,复选框查询条件
            //复选
            if (fieldBasicsHtml.getType().equals(DataFieldType.select) || fieldBasicsHtml.getType().equals(DataFieldType.checkbox)) {
                e.setEnabledQueryTypes(DataQueryType.in);
                String formId1 = fieldBasicsHtml.getDesignJson().get("formId").toString();
                Map<String, Object> props = (Map<String, Object>) fieldBasicsHtml.getDesignJson().get("props");
                String label = props.get("label").toString();
                //使用数据id作为传递值
                if ("id".equals(props.get("value"))) {
                    Object o1 = dynamicDataService.getByIds(formId1, (List<String>) e.getValue()).stream().map(m -> m.get(label)).collect(Collectors.toList());
                    Criteria haoCaiMingChen = DynamicDataUtils.like(new Criteria(), label, o1);
                    DynamicDataUtils.freePermit();
                    ArrayList<String> objects = new ArrayList<>();
                    objects.add(label);
                    List<String> ids = dynamicDataService.queryList(formId1, haoCaiMingChen, objects).stream().map(s -> s.get("id").toString()).collect(Collectors.toList());
                    e.setValue(ids);
                }
            }
        }).collect(Collectors.toList())).collect(Collectors.toList());
        return queryConditions;
    }

    /**
     * 查询所有数据
     *
     * @param modelId  模型id
     * @param fieldKey 数据字段
     * @return 数据集合
     */
    @ApiOperation("查询所有数据")
    @GetMapping("/query/list/{modelId}")
    @Transactional(rollbackFor = Exception.class)
    public R<List<Map<String, Object>>> queryList(@PathVariable String appId, @PathVariable("modelId") String modelId, @ApiParam(name = "需要查询的字段", required = true) @RequestParam("fieldKey") String fieldKey) {
        DataModelPo one = dataModelService.getOne(Wrappers.query(new DataModelPo().setAppId(appId).setId(modelId)));
        if (ObjectNull.isNull(one)) {
            return R.failed(DesignConstant.APP_ERROR_MSG);
        }
        //判断模型
        DynamicDataUtils.dataModelScope(modelId);
        List<String> fieldKey1 = null;
        try {
            fieldKey1 = JSONArray.parseArray(URLDecoder.decode(fieldKey, "UTF-8"), String.class);
            //兼容2.1.4的关联数据
        } catch (Exception e) {
            fieldKey1 = new ArrayList<String>() {{
                add(fieldKey);
            }};
        }
        List<Map<String, Object>> result = dynamicDataService.queryList(modelId, fieldKey1);
        //如果不为空,转树形
        return R.ok(result);
    }

    /**
     * 查询树形数据
     *
     * @param modelId 模型id
     * @return 数据集合
     */
    @ApiOperation("查询树形数据")
    @PostMapping("/query/tree/{modelId}")
    @Transactional(rollbackFor = Exception.class)
    public R<List> queryList(@PathVariable String appId, @PathVariable("modelId") String modelId, @RequestBody FieldTreeDto fieldTreeDto) {
        DataModelPo one = dataModelService.getOne(Wrappers.query(new DataModelPo().setAppId(appId).setId(modelId)));
        if (ObjectNull.isNull(one)) {
            return R.failed(DesignConstant.APP_ERROR_MSG);
        }
        //根据设计获取默认地址
        List<String> fieldList = new ArrayList<>();
        fieldList.add(fieldTreeDto.getValue());
        fieldList.add(fieldTreeDto.getLabel());
        fieldList.add(fieldTreeDto.getSecTitle());
        String createTime = Get.name(BasePo::getCreateTime);
        fieldList.add(createTime);

        // 条件筛选
        Criteria criteria = null;
        List<List<QueryConditionDto>> conditions = new ArrayList<>();
        if (ObjectNull.isNotNull(fieldTreeDto.getFilter())) {
            if (ObjectNull.isNotNull(fieldTreeDto.getFilter().getGroupConditions())) {
                conditions = fieldTreeDto.getFilter().getGroupConditions();
            } else {
                List<QueryConditionDto> conditionDtos = fieldTreeDto.getFilter().getConditions();
                conditions.add(conditionDtos);
            }
        }
        if (ObjectNull.isNotNull(fieldTreeDto.getQuery()) && ObjectNull.isNotNull(fieldTreeDto.getQuery().getConditions())) {
            conditions.add(fieldTreeDto.getQuery().getConditions());
        }
        //判断条件不能为空
        if (ObjectNull.isNotNull(conditions)) {
            criteria = DynamicDataUtils.buildDynamicGroupCriteria(conditions);
        }
        List<Map<String, Object>> result = dynamicDataService.queryList(modelId, criteria, fieldList);
        // 根据树形结构查询递归反推查询整体树型
        if (ObjectNull.isNotNull(criteria) && ObjectNull.isNotNull(fieldTreeDto.getSecTitle()) && ObjectNull.isNotNull(result)) {
            Map<String, Map<String, Object>> map = result.stream().collect(Collectors.toMap(e -> e.get("id").toString(), e -> e));
            List<String> parentValues = result.stream().filter(e -> ObjectNull.isNotNull(e.get(fieldTreeDto.getSecTitle()))).map(e -> e.get(fieldTreeDto.getSecTitle()).toString()).collect(Collectors.toList());
            getParentList(modelId, fieldList, fieldTreeDto.getValue(), fieldTreeDto.getSecTitle(), parentValues, map);
            result = new ArrayList<>(map.values());
        }
        // 需要控制权限，并将权限的信息返回在数据按钮上面，进行处理
        designHandler.handleButtonInfo(result, EnvConstant.LEFT_TREE_BUTTON_DISPLAY);
        List<Tree<Object>> build = TreeUtil.build(result, null, new TreeNodeConfig(), (treeNode, tree) -> {
            tree.setId(treeNode.get("id").toString());
            tree.setParentId(treeNode.get(fieldTreeDto.getSecTitle()));
            tree.setName(treeNode.get(fieldTreeDto.getLabel()).toString());
            //排序是根据时间毫秒排序
            tree.setWeight(LocalDateTime.parse(treeNode.get(createTime).toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toEpochSecond(ZoneOffset.UTC));
            tree.put("value", treeNode.get(fieldTreeDto.getValue()).toString());
            tree.put("extend", treeNode);
        });
        return R.ok(build);
    }

    /**
     * 获取指定数据的所有父级数据
     *
     * @param modelId         模型id
     * @param fieldList       查询字段
     * @param parentFieldName 父字段名
     * @param parentValues    父字段值
     * @param map             数据
     */
    private void getParentList(String modelId, List<String> fieldList, String parentValueName, String parentFieldName, List<String> parentValues, Map<String, Map<String, Object>> map) {
        if (ObjectNull.isNull(parentValues)) {
            return;
        }
        QueryConditionDto queryCondition = new QueryConditionDto().setFieldKey(parentValueName).setValue(parentValues).setEnabledQueryTypes(DataQueryType.in);
        Criteria criteria = DynamicDataUtils.buildDynamicCriteria(Collections.singletonList(queryCondition));
        List<Map<String, Object>> results = dynamicDataService.queryList(modelId, criteria, fieldList);
        if (ObjectNull.isNotNull(results)) {
            parentValues.clear();
            results.forEach(e -> {
                if (ObjectNull.isNotNull(e.get(parentFieldName))) {
                    parentValues.add(e.get(parentFieldName).toString());
                }
                map.put(e.get("id").toString(), e);
            });
            getParentList(modelId, fieldList, parentValueName, parentFieldName, parentValues, map);
        }
    }

    @ApiOperation("post查询所有数据")
    @PostMapping("/query/list/{modelId}")
    @Transactional(rollbackFor = Exception.class)
    public R<List<Map<String, Object>>> postQueryList(@PathVariable String appId, @PathVariable("modelId") String dataModelId, @RequestBody QueryListDto queryPageDto) {
        List<Map<String, Object>> body = getBody(appId, dataModelId, queryPageDto);
        return R.ok(body);
    }

    private List<Map<String, Object>> getBody(String appId, String dataModelId, QueryListDto queryPageDto) {
        if (ObjectNull.isNull(queryPageDto.getFieldList())) {
            List<String> fieldKeys = dataFieldService.getFieldKeys(appId, dataModelId);
            queryPageDto.setFieldList(fieldKeys);
        }
        List<Map<String, Object>> data = dynamicDataService.postQueryList(appId, dataModelId, queryPageDto);
        // 数据项禁用
        dynamicDataService.enableDataItem(data, queryPageDto.getEnableConditions());
        return data;
    }


    /**
     * 查询单条数据
     *
     * @param dataModelId 模型id
     * @param dataId      数据id
     * @return 数据内容
     */
    @Log
    @ApiOperation("查询单条转换后的数据")
    @GetMapping("/query/single/transformation/{modelId}/{designId}/{dataId}")
    @Transactional(rollbackFor = Exception.class)
    public R<Map<String, Object>> querySinglePrint(@PathVariable String appId, @PathVariable("modelId") String dataModelId, @PathVariable String designId, @PathVariable("dataId") String dataId) {
        //设置数据权限
        DynamicDataUtils.dataModelScope(dataModelId);
        //查询单条数据，并处理数据权限，并添加默认按钮,不做转换
        Map<String, Object> data = dynamicDataService.querySingle(appId, dataModelId, dataId, true);
        //做数据转换操作
        data = dynamicDataService.paresMapWithEcho(appId, data, dataModelId, designId, true);
        return R.ok(data);
    }


    /**
     * 查询单条数据并进行数据转换，包含按钮，和数据脱敏,数据权限
     *
     * @param dataModelId 模型id
     * @param dataId      数据id
     * @return 数据内容
     */
    @Log
    @ApiOperation("查询单条数据")
    @GetMapping("/query/single/{modelId}/{dataId}")
    @Transactional(rollbackFor = Exception.class)
    public R<Map<String, Object>> querySingle(@PathVariable String appId, @PathVariable("modelId") String dataModelId, @PathVariable("dataId") String dataId,
                                              @RequestHeader(value = "Designid", required = false) String designId) {
        //设置数据权限
        DynamicDataUtils.dataModelScope(dataModelId);
        //查询单条数据，并处理数据权限，并添加默认按钮,不做转换
        Map<String, Object> data = dynamicDataService.querySingle(appId, dataModelId, dataId, true);
        //做数据转换操作
        data = dynamicDataService.paresMapWithEcho(appId, data, dataModelId, designId, false);
        // 扩展返回数据
        dynamicDataService.expandOtherData(data);

        return R.ok(data);
    }

    /**
     * 处理菜单表单按钮
     *
     * @param dataModelId 模型id
     * @param dataMap     数据
     * @return 数据内容
     */
    @Log
    @ApiOperation("处理菜单表单按钮")
    @PostMapping("/handle/form/button/{modelId}")
    public R<ButtonRtnVo> handleFormButton(@PathVariable("modelId") String dataModelId, @RequestBody Map<String, Object> dataMap) {
        //设置数据权限
        DynamicDataUtils.dataModelScope(dataModelId);
        //查询单条数据，并处理数据权限，并添加默认按钮,不做转换
        ButtonRtnVo button = dynamicDataService.handleFormButton(dataMap);

        return R.ok(button);
    }

    /**
     * 查询单个字段的值
     *
     * @param modelId 模型id
     * @param dataId  数据id
     * @param fieldId 字段key
     * @return 单个字段的值
     */
    @ApiOperation("查询单个字段的值")
    @GetMapping("/query/field/{modelId}/{dataId}/{fieldId}")
    @Transactional(rollbackFor = Exception.class)
    public R<Object> queryField(@PathVariable String appId, @PathVariable("modelId") String modelId, @PathVariable("dataId") String dataId, @PathVariable("fieldId") String fieldId) {
        DynamicDataUtils.dataModelScope(modelId);
        return R.ok(dynamicDataService.queryField(appId, modelId, dataId, fieldId));
    }

    /**
     * 导入数据
     *
     * @param file     数据文件
     * @param modelId  模型id
     * @param designId 设计id
     * @return 操作结果
     */
    @Log
    @ApiOperation("导入")
    @PostMapping("/import/{modelId}")
    @Transactional(rollbackFor = Exception.class)
    public R importDesign(@PathVariable String appId, @RequestParam("file") MultipartFile file, @PathVariable("modelId") String modelId, @RequestParam(name = "designId", required = false) String designId) {
        DynamicDataUtils.checkPermit();
        List<List<Object>> excelData;
        try {
            excelData = ExcelUtil.getReader(file.getInputStream()).read();
        } catch (Exception ex) {
            log.error("数据导入异常", ex);
            return R.failed("数据导入异常");
        }
        if (ObjectUtils.isEmpty(excelData) || excelData.size() < EXCEL_DATA_MIN_SIZE) {
            return R.ok(false, "导入数据为空");
        }
        // 处理Excel表头
        List<Object> header = excelData.get(0);
        CrudPage crudPage = pageService.getOne(Wrappers.query(new CrudPage().setDataModelId(modelId).setId(designId).setJvsAppId(appId)));
        PageDesignHtml pageDesignHtml = DesignUtils.parsePage(crudPage.getViewJson());
        Optional<ButtonDesignHtml> first = pageDesignHtml.getButtons().stream().filter(e -> e.getType().equals(ButtonTypeEnum.btn_export)).findFirst();
        //转换使用导出模板的别名为转换规则。
        List<String> fieldKeyList = new ArrayList<>();
        ButtonDesignHtml buttonDesignHtml = first.get();
        if (CollectionUtils.isEmpty(buttonDesignHtml.getExportFields())) {
            //默认所有数据字段
            fieldKeyList = pageDesignHtml.getDataPage().getAutoTableFields().stream().map(DataTableFieldDesignHtml::getAliasColumnName).collect(Collectors.toList());
        } else {
            List<String> fieldlist = pageDesignHtml.getDataPage().getAutoTableFields().stream().map(DataTableFieldDesignHtml::getAliasColumnName).collect(Collectors.toList());
            List<DataTableFieldDesignHtml> exportFields = buttonDesignHtml.getExportFields();
            //调整字段的导入顺序，避免字段转换时会有前后关联顺序导致
            for (String s : fieldlist) {
                for (DataTableFieldDesignHtml exportField : exportFields) {
                    if (s.equals(exportField.getAliasColumnName())) {
                        fieldKeyList.add(exportField.getAliasColumnName());
                    }
                }
            }
        }

        List<FieldBasicsHtml> fields = dataFieldService.getFields(appId, modelId, true, false);
        // 处理Excel表数据
        List<Map<String, Object>> mapDataList = this.parseExcelData2MapData(excelData, fieldKeyList, modelId);
        Map<String, String> ids = new HashMap<>();
        String linkFieldKey = null;
        Map<String, FieldBasicsHtml> typeMaps = fields.stream().filter(e -> DataFieldType.SELECT_CONVERSION.contains(e.getType())).collect(Collectors.toMap(e -> e.getFieldKey(), Function.identity()));
        if (ObjectNull.isNotNull(typeMaps)) {
            //遍历数据转换
            for (Map<String, Object> map : mapDataList) {
                Set<String> collect = fieldKeyList.stream().filter(typeMaps::containsKey).collect(Collectors.toSet());
                for (String key : collect) {
                    FieldBasicsHtml fieldBasicsHtml = typeMaps.get(key);
                    IDataFieldHandler iDataFieldHandler = fieldHandlerMap.get(fieldBasicsHtml.getType().getDesc());
                    Object o = map.get(key);
                    if (ObjectNull.isNotNull(o, fieldBasicsHtml.getDesignJson())) {
                        //处理是否自己关联自己， 如果是需要自动生成id
                        FieldBasicsHtml html = iDataFieldHandler.toHtml(fieldBasicsHtml);
                        Object obj = iDataFieldHandler.getConversionKey(html, o, map);
                        if (html instanceof MultipleHtml) {
                            String linkModelId = ((MultipleHtml) iDataFieldHandler.toHtml(fieldBasicsHtml)).getFormId();
                            String idStr = IdWorker.getIdStr();
                            //如果是顶级为空
                            if (ObjectNull.isNull(o)) {
                                map.put("id", idStr);
                            } else if ((modelId.equals(linkModelId) && o.equals(obj))) {
                                //设置自己关联自己的Key 然后将所有不为空的key给添加上id
                                linkFieldKey = ((MultipleHtml) html).getProps().getLabel();
                                //转换时判断是否为空
                                ids.put(o.toString(), idStr);
                                obj = idStr;
                                map.put(key, idStr);
                            }
                        }
                        map.put(key, obj);
                    }
                }
            }
            if (ObjectNull.isNotNull(linkFieldKey)) {
                for (Map<String, Object> map : mapDataList) {
                    Object o = map.get(linkFieldKey);
                    //如果创建了新的id就将id放进去
                    if (ids.containsKey(o)) {
                        map.put("id", ids.get(o));
                        map.put("dataId", ids.get(o));
                    }
                }
            }
        }

        RuleExecuteDto executeDto = dynamicDataService.saveBatch(appId, modelId, mapDataList);
        if (ObjectNull.isNotNull(executeDto) && ObjectNull.isNotNull(executeDto.getStats())) {
            if (executeDto.getStats()) {
                return R.ok().setMsg(executeDto.getSyncMessageTips());
            } else {
                return R.failed(executeDto.getSyncMessageTips());
            }
        }
        return R.ok().setMsg("导入成功");
    }

    /**
     * 支持移动端小程序导出功能
     */
    @ApiOperation("导出")
    @GetMapping("/export/{modelId}")
    public void exportDesign(@PathVariable String appId, @RequestParam(value = "keywords", required = false) String keywords, @PathVariable("modelId") String modelId, @RequestParam(name = "designId", required = false) String designId,
                             @RequestParam(name = "query", required = false) String query, HttpServletResponse response) {
        exportDesign(appId, modelId, keywords, designId, query, null, response);
    }

    /**
     * 导出数据
     *
     * @param modelId  模型id
     * @param designId 设计id
     * @param ids      选中的ids数据,如果不为空,以id为准
     * @param response Http响应对象
     */
    @Log
    @ApiOperation("导出")
    @PostMapping("/export/{modelId}")
    @SneakyThrows
    public void exportDesign(@PathVariable String appId, @PathVariable("modelId") String modelId, @RequestParam(value = "keywords", required = false) String keywords,
                             @RequestParam(name = "designId", required = false) String designId,
                             @RequestParam(name = "query", required = false) String query, @RequestBody List<String> ids, HttpServletResponse response) {
        DynamicDataUtils.setDataModelId(modelId);
        DynamicDataUtils.setPageDesignId(designId);
        DynamicDataUtils.dataModelScope(modelId);
        CrudPage crudPage = pageService.getOne(Wrappers.query(new CrudPage().setDataModelId(modelId).setId(designId).setJvsAppId(appId)));
        if (ObjectNull.isNull(crudPage)) {
            log.error("数据导出-找不到页面设计,页面设计id为: {}", designId);
            throw new BusinessException("找不到页面设计");
        }
        PageDesignHtml pageDesignHtml = DesignUtils.parsePage(crudPage.getViewJson());
        Optional<ButtonDesignHtml> first = pageDesignHtml.getButtons().stream().filter(e -> e.getType().equals(ButtonTypeEnum.btn_export)).findFirst();
        if (first.isPresent()) {
            List<String> fieldsDesign;
            //聚合字段
            List<String> aggregationFields = new ArrayList<>();
            //聚合相关导出字段
            List<String> aggregationExportFields = new ArrayList<>();
            //聚合配置
            Map<String, AggregationTypeEnum> aggregationHandleFieldsMap = new HashMap<>();
            List<DataTableFieldDesignHtml> exportFields = first.get().getExportFields();
            if (CollectionUtils.isEmpty(exportFields)) {
                //默认所有数据字段
                fieldsDesign = pageDesignHtml.getDataPage().getAutoTableFields().stream()
                        .map(e -> {
                            if (Boolean.TRUE.equals(e.getAggregation())) {
                                //记录聚合字段
                                aggregationFields.add(e.getAliasColumnName());
                                //记录聚合相关字段
                                aggregationExportFields.add(e.getAliasColumnName());
                            } else if (ObjectNull.isNotNull(e.getAggregationType())) {
                                //记录聚合相关字段
                                aggregationExportFields.add(e.getAliasColumnName());
                                //记录聚合配置
                                aggregationHandleFieldsMap.put(e.getAliasColumnName(), e.getAggregationType());
                            }
                            return e.getAliasColumnName();
                        })
                        .collect(Collectors.toList());
            } else {
                fieldsDesign = exportFields.stream()
                        .map(e -> {
                            if (Boolean.TRUE.equals(e.getAggregation())) {
                                //记录聚合字段
                                aggregationFields.add(e.getAliasColumnName());
                                //记录聚合相关字段
                                aggregationExportFields.add(e.getAliasColumnName());
                            } else if (ObjectNull.isNotNull(e.getAggregationType())) {
                                //记录聚合相关字段
                                aggregationExportFields.add(e.getAliasColumnName());
                                //记录聚合配置
                                aggregationHandleFieldsMap.put(e.getAliasColumnName(), e.getAggregationType());
                            }
                            return e.getAliasColumnName();
                        })
                        .collect(Collectors.toList());
            }

            //判断是否有聚合分配聚合字段
            if (!aggregationFields.isEmpty()) {
                fieldsDesign = CollUtil.distinct(CollUtil.intersection(fieldsDesign, aggregationExportFields));
            }


            List<FieldBasicsHtml> fields = dataFieldService.getAllField(appId, modelId, true, true, e -> false);
            List<String> finalFieldsDesign = fieldsDesign;
            Map<String, FieldBasicsHtml> collectMap = fields.stream()
                    //排除文件类型的下载，不然会导致 easyExcel转换失效
                    .peek(e -> {
                        if ((!DataFieldType.input.equals(e.getType())) && e.getType().getTransformationList().contains(e.getType())) {
                            finalFieldsDesign.remove(e.getFieldKey());
                        }
                    }).collect(Collectors.toMap(FieldBasicsHtml::getFieldKey, Function.identity()));
            fieldsDesign = finalFieldsDesign;
            List<QueryOrderDto> sorts = null;
            List<List<QueryConditionDto>> queryGroupConditions = new ArrayList<>();
            List<Criteria> criteriaList = SystemThreadLocal.get(KEY_AUTH_CRITERIA);
            Criteria authCriteria = DynamicDataUtils.trueCriteria();

            if (ObjectNull.isNotNull(ids)) {
                //直接使用ids进行查询
                List<QueryConditionDto> queryConditions = Collections.singletonList(new QueryConditionDto().setEnabledQueryTypes(DataQueryType.in).setFieldKey("id").setValue(ids));
                queryGroupConditions.add(queryConditions);
            } else if (ObjectNull.isNotNull(query)) {
                try {
                    query = URLDecoder.decode(query, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    throw new BusinessException(e.getMessage());
                }
                QueryListDto queryListDto = JSONObject.parseObject(query, QueryListDto.class);
                //列表页筛选条件，与数据权限不同，是直接进行数据筛选
                if (ObjectNull.isNotNull(pageDesignHtml.getParameters())) {
                    pageDesignHtml.getParameters().stream().filter(e -> ObjectNull.isNotNull(e.getValue(), e.getKey(), e.getOperator()))
                            .map(e -> new QueryConditionDto().setValue(e.getOperator().equals(DataQueryType.in) ? e.getValue() : e.getValue().get(0)).setFieldKey(e.getKey()).setEnabledQueryTypes(e.getOperator()))
                            .peek(e -> e.setCrud(true))
                            .forEach(e -> queryListDto.getConditions().add(e));
                }

                //如果关键字查询条件不为空的时候
                if (ObjectNull.isNotNull(keywords)) {
                    List<QueryConditionDto> list = pageDesignHtml.getDataPage().getAutoTableFields().stream()
                            .map(e -> new QueryConditionDto().setFieldKey(e.getAliasColumnName()).setEnabledQueryTypes(DataQueryType.like).setValue(keywords)).collect(Collectors.toList());
                    queryListDto.getConditions().addAll(list);
                }

                queryGroupConditions = getQueryConditions(Collections.singletonList(queryListDto.getConditions()), collectMap, null);

                sorts = queryListDto.getSorts();
                // 如果没有指定排序字段，则使用列表的排序设计
                if (CollectionUtils.isEmpty(sorts) && CollectionUtils.isNotEmpty(pageDesignHtml.getSorts())) {
                    sorts = (BeanCopyUtil.copys(pageDesignHtml.getSorts(), QueryOrderDto.class));
                }
                //将查询条件和列表过滤条件进行分离
                if (ObjectNull.isNotNull(queryGroupConditions)) {
                    //列表过滤条件
                    List<QueryConditionDto> collect = queryGroupConditions.stream().flatMap(Collection::stream).filter(QueryConditionDto::getCrud).collect(Collectors.toList());
                    //过滤出列表过滤的数据
                    if (ObjectNull.isNotNull(collect)) {
                        //将列表过滤和数据权限添加上组合查询
                        List<Criteria> crud = DynamicDataUtils.buildDynamicCriteriaList(collect);
                        if (ObjectNull.isNotNull(criteriaList)) {
                            authCriteria = DynamicDataUtils.trueCriteria().andOperator(crud).orOperator(criteriaList);
                        } else {
                            //列表过滤设置“或查询”处理
                            Boolean pageDesignFilterType = pageDesignHtml.getAndFilterType();
                            if (Boolean.FALSE.equals(pageDesignFilterType) && ObjectNull.isNotNull(crud)) {
                                Criteria[] criteriaArray = crud.toArray(new Criteria[0]);
                                Criteria filterCriteria = new Criteria().orOperator(criteriaArray);
                                authCriteria = DynamicDataUtils.trueCriteria().andOperator(filterCriteria);
                            } else {
                                authCriteria = DynamicDataUtils.trueCriteria().andOperator(crud);
                            }
                        }
                    } else if (ObjectNull.isNotNull(criteriaList)) {
                        if (criteriaList.size() == 1) {
                            authCriteria = DynamicDataUtils.trueCriteria().andOperator(criteriaList);
                        } else {
                            authCriteria = DynamicDataUtils.trueCriteria().orOperator(criteriaList);
                        }
                    }
                    queryGroupConditions = queryGroupConditions.stream().map(e -> e.stream().filter(s -> !s.getCrud()).collect(Collectors.toList())).collect(Collectors.toList());
                    List<FieldBasicsHtml> typeFields = dataFieldService.getFields(appId, modelId, null, true, false);

                    //处理单位查询条件“全包含”的查询
                    DynamicDataUtils.handleDeptConditions(queryGroupConditions, typeFields);
                }
            }

            //调整excel导出顺序
//            List<List<String>> fieldNameList = fieldsDesign.stream().filter(collectMap::containsKey).map(e -> Collections.singletonList(collectMap.get(e).getFieldName())).collect(Collectors.toList());

            //根据列表页字段顺序控制导出字段排序
            Map<String, Integer> sortIndexMap = IntStream.range(0, pageDesignHtml.getDataPage().getAutoTableFields().size())
                    .boxed()
                    .collect(Collectors.toMap(
                            i -> pageDesignHtml.getDataPage().getAutoTableFields().get(i).getAliasColumnName(),
                            i -> i,(a,b)->a
                    ));
            fieldsDesign.sort(Comparator.comparingInt(sortIndexMap::get));
            Map<String, String> nameMap = exportFields.stream().collect(Collectors.toMap(DataTableFieldDesignHtml::getAliasColumnName, DataTableFieldDesignHtml::getShowChinese));
            List<List<String>> fieldNameList = fieldsDesign.stream().filter(collectMap::containsKey).map(e -> Collections.singletonList(nameMap.get(e))).collect(Collectors.toList());

            //导出数据
            List<Map> queryList;
            //生成查询条件list
            List<Criteria> list = DynamicDataUtils.buildDynamicGroupCriteriaList(queryGroupConditions);
            //处理排序规则
            Sort sort = Sort.by(Sort.Direction.DESC, Get.name(DynamicDataPo::getCreateTime))
                    //导入进来的数据时间一致.需要再通过ID排序
                    .and(Sort.by(Sort.Direction.ASC, "dataId"));
            if (ObjectNull.isNotNull(sorts)) {
                List<Sort.Order> collect = sorts.stream()
                        .filter(e -> ObjectNull.isNotNull(e.getDirection(), e.getFieldKey()))
                        .map(e -> new Sort.Order(e.getDirection(), e.getFieldKey()))
                        .collect(Collectors.toList());
                if (ObjectNull.isNotNull(collect)) {
                    sort = Sort.by(collect);
                }
            }

            //非聚合处理
            if (aggregationFields.isEmpty()) {
                Query querymongo = DynamicDataUtils.andOr(list, authCriteria, ObjectNull.isNull(keywords));
                querymongo.with(sort);
                queryList = dataModelHandler.find(querymongo, Map.class, modelId);
            } else {
                //聚合处理
                Criteria criteria = andOrCriteria(list, authCriteria, ObjectNull.isNull(keywords));
                PageAggregationQuery aggregationQuery = PageAggregationQuery.builder()
                        .criteria(criteria)
                        .sort(sort)
                        .aggregationFields(aggregationFields)
                        .aggregationHandleFieldsMap(aggregationHandleFieldsMap)
                        .build();
                queryList = dynamicDataService.executeAggregation(modelId, aggregationQuery);
            }

            //筛选需要回显的字段
            List<FieldBasicsHtml> fieldBasicsHtmls = dataFieldService.getFields(appId, modelId, null, true, false);
            List<String> lambdaFieldsDesign = fieldsDesign;
            Map<String, FieldBasicsHtml> fieldMap = fieldBasicsHtmls.stream().filter(e -> lambdaFieldsDesign.contains(e.getFieldKey())).collect(Collectors.toMap(FieldBasicsHtml::getFieldKey, Function.identity()));

            // excel对应数据
            List<List<Object>> excelData = new ArrayList<>();

            //是否包含序号
            Boolean exportDataNum = pageDesignHtml.getExportDataNum();
            int num = 1;

            for (Map<String, Object> data : queryList) {
                //将数据做转换
                data = dynamicDataService.echo(data, fieldMap, true, a -> b -> b);
                //根据字段顺序调整excel顺序
                List<Object> oneRowData = new ArrayList<>(fieldsDesign.size());
                if (exportDataNum) {
                    oneRowData.add(num++);
                }
                for (String key : fieldsDesign) {
                    oneRowData.add(data.getOrDefault(key, ""));
                }
                excelData.add(oneRowData);
            }

            // 响应数据
            if (pageDesignHtml.getExportDataNum()) {
                fieldNameList.add(0, Collections.singletonList("序号"));
            }
            String fileName = this.getFileName("", designId);
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(URLUtil.encode(FILE_NAME_EXPORT + fileName + FILE_TYPE, StandardCharsets.UTF_8)));
            response.setStatus(HttpStatus.OK.value());
            try {
                //导出数据
                ServletOutputStream outputStream = response.getOutputStream();
                new ExcelWriterBuilder().file(outputStream).registerWriteHandler(new EasyExcelCustomCellWriteHandler()).head(fieldNameList).registerConverter(decimal128ListConvert).registerConverter(arrayListConvert).registerConverter(localDateTimeConvert).sheet(fileName).doWrite(excelData);
            } catch (Exception ex) {
                log.error("数据导出异常", ex);
                throw new BusinessException("数据导出异常");
            }
        }
    }


    /**
     * 下载数据导入的模板
     *
     * @param modelId  模型id
     * @param designId 设计id
     * @param response Http响应对象
     */
    @Log
    @ApiOperation("下载模板")
    @GetMapping("/download/excel/template/{modelId}")
    public void excelTemplate(@PathVariable String appId, @PathVariable("modelId") String modelId, @RequestParam(name = "designId", required = false) String designId, HttpServletResponse response) {
        CrudPage crudPage = pageService.getOne(Wrappers.query(new CrudPage().setId(designId).setJvsAppId(appId)));
        if (ObjectNull.isNull(crudPage)) {
            log.error("没有找到此设计。designId: {}", designId);
            throw new BusinessException("设计页面不存在");
        }
        PageDesignHtml pageDesignHtml = DesignUtils.parsePage(crudPage.getViewJson());
        Optional<ButtonDesignHtml> first = pageDesignHtml.getButtons().stream().filter(e -> e.getType().equals(ButtonTypeEnum.btn_download_template)).findFirst();
        if (first.isPresent()) {

            List<String> fieldsDesign;
            if (CollectionUtils.isEmpty(first.get().getImportFields())) {
                //默认所有数据字段
                fieldsDesign = pageDesignHtml.getDataPage().getAutoTableFields().stream().map(DataTableFieldDesignHtml::getAliasColumnName).collect(Collectors.toList());
            } else {
                fieldsDesign = new ArrayList<>(first.get().getImportFields().stream().map(DataTableFieldDesignHtml::getAliasColumnName).collect(Collectors.toList()));
            }
            if (ObjectNull.isNull(fieldsDesign)) {
                //没有设计下载模板字段
                return;
            }
            // 字段数据
            Map<String, FieldBasicsHtml> fields = dataFieldService.getFields(appId, modelId, designId, false, false).stream().collect(Collectors.toMap(FieldPublicHtml::getFieldKey, Function.identity()));
            Map<String, String> fieldMap = new HashMap<>();

            List<DataTableFieldDesignHtml> importFields = first.get().getImportFields();
            //如果没有设计导入模板信息，则直接使用字段信息
            if (ObjectNull.isNull(importFields)) {
                importFields = fields.values().stream().map(e -> new DataTableFieldDesignHtml().setShowChinese(e.getFieldName()).setDescription("").setShowChineseAlias(e.getFieldName()).setComponentType(e.getType())).collect(Collectors.toList());
            }
            //屏蔽掉描述字段
            importFields.stream()
                    //使用别名返回到前端数据
                    .peek(e -> {
                        if (ObjectNull.isNotNull(e.getShowChineseAlias())) {
                            fieldMap.put(e.getShowChinese(), e.getShowChineseAlias());
                        } else {
                            fieldMap.put(e.getShowChinese(), e.getShowChinese());
                        }
                    }).map(e -> (e.getComponentType().getDesc() + "\n" + (ObjectNull.isNotNull(e.getDescription()) ? e.getDescription() : ""))).collect(Collectors.toList());
            List<List<String>> fieldNameList = fieldsDesign.stream().map(fields::get).map(e -> Collections.singletonList(fieldMap.get(e.getFieldName()))).collect(Collectors.toList());
            // 响应数据
            String fileName = this.getFileName("", designId);
            String sheelName = first.get().getSheelName();
            if (ObjectNull.isNull(sheelName)) {
                sheelName = FILE_NAME_TEMPLATE + fileName;
            }
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(URLUtil.encode(FILE_NAME_TEMPLATE + fileName + FILE_TYPE, StandardCharsets.UTF_8)));
            response.setStatus(HttpStatus.OK.value());
            try {
                ServletOutputStream outputStream = response.getOutputStream();
                if (ObjectNull.isNotNull(first.get().getTemplateFileLink())) {
                    String s = ossTemplate.fileLink(first.get().getTemplateFileLink().replaceAll("/jvs-public", ""), "jvs-public");
                    String url = URLDecoder.decode(s, "UTF-8");
                    byte[] bytes = HttpUtil.downloadBytes(url);
                    try {
                        outputStream.write(bytes);
                        outputStream.flush();
                    } finally {
                        outputStream.close();
                    }
//                    ButtonDesignHtml buttonDesignHtml = first.get();
//                    String prefix = FileUtil.extName(buttonDesignHtml.getTemplateFileName());
//                    ExcelTypeEnum excelTypeEnum = ExcelTypeEnum.valueOf(prefix.toUpperCase());
//                    ExcelWriter writer = EasyExcel.write(outputStream).head(fieldNameList).excelType(excelTypeEnum).withTemplate(new ByteArrayInputStream(bytes)).registerWriteHandler(new EasyExcelCustomCellWriteHandler()).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
//
//                    WriteSheet writeSheet = EasyExcel.writerSheet(0, sheelName).useDefaultStyle(true).build();
//                    writer.fill(new ArrayList<>(), writeSheet).finish();
                } else {
                    //导出数据
                    new ExcelWriterBuilder().file(outputStream).head(fieldNameList).registerWriteHandler(new EasyExcelCustomCellWriteHandler()).registerConverter(arrayListConvert).registerConverter(localDateTimeConvert).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet(sheelName).useDefaultStyle(true).doWrite(Collections.singletonList(new ArrayList()));
                }

            } catch (Exception ex) {
                log.error("模板下载异常", ex);
                throw new BusinessException("模板下载异常");
            }
        }
    }

    @Log
    @ApiOperation("查询单条数据所有工作流任务id")
    @GetMapping("/query/single/flow/task/ids/{modelId}/{dataId}")
    @Transactional(rollbackFor = Exception.class)
    public R<List<String>> querySingleFlowTaskIds(@PathVariable String appId, @PathVariable("modelId") String dataModelId, @PathVariable("dataId") String dataId) {
        JSONObject data = flowDynamicDataService.getFlowTaskDataObj(dataId);
        List<String> flowTaskIds = designHandler.getDataFlowTasks(data).stream().map(FlowDynamicDataServiceImpl.FlowTaskModelData::getId).collect(Collectors.toList());
        return R.ok(flowTaskIds);
    }


    @ApiOperation("ocr识别")
    @PostMapping("/ocr/{modelId}")
    public void ocr(HttpServletResponse response, @PathVariable String appId, @PathVariable("modelId") String dataModelId, @RequestParam String field, @RequestParam String name, @RequestParam String tempFileName) {
        dynamicDataService.ocr(response, appId, dataModelId, field, name, tempFileName);
    }

    /**
     * 获取查询条件
     *
     * @param page 用于设置分页条件
     * @return 查询条件
     */
    private List<QueryConditionDto> getQueryConditions(@Nullable Page<DynamicDataPo> page, String... ignoredParams) {
        HttpServletRequest request = WebUtils.getRequest();
        Map<String, String[]> parameters = request.getParameterMap();
        List<QueryConditionDto> queryConditions = Collections.emptyList();
        if (ObjectUtils.isNotEmpty(parameters)) {
            Map<String, String> params = parameters.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()[0]));
            if (ObjectUtils.isNotEmpty(ignoredParams)) {
                for (String ignoredParam : ignoredParams) {
                    params.remove(ignoredParam);
                }
            }
            queryConditions = new ArrayList<>(params.size());
            if (Objects.nonNull(page)) {
                page.setSize(this.peak(params, "size", 10));
                page.setCurrent(this.peak(params, "current", 1));
            }
            for (Map.Entry<String, String> entry : params.entrySet()) {
                QueryConditionDto query = new QueryConditionDto();
                query.setFieldKey(entry.getKey());
                query.setValue(entry.getValue());
                query.setEnabledQueryTypes(DataQueryType.eq);
                queryConditions.add(query);
            }
        }
        return queryConditions;
    }

    /**
     * 获取文件名称
     *
     * @param defaultName  默认名称
     * @param pageDesignId 列表页id
     * @return 文件名称
     */
    private String getFileName(String defaultName, String pageDesignId) {
        CrudPage page = pageService.getOne(Wrappers.<CrudPage>lambdaQuery().select(CrudPage::getName).eq(CrudPage::getId, pageDesignId));
        if (Objects.isNull(page)) {
            return defaultName;
        }
        String name = page.getName();
        if (StringUtils.isBlank(name)) {
            return defaultName;
        }
        return defaultName + name;
    }

    /**
     * 获取参数
     *
     * @param data         Map类型的数据
     * @param key          键
     * @param defaultValue 默认值
     * @return Map中的参数
     */
    private int peak(Map<String, String> data, String key, int defaultValue) {
        if (ObjectUtils.isEmpty(data)) {
            return defaultValue;
        }
        String value = data.remove(key);
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * Excel单元格数据转数据模型数据
     *
     * @param excelData    Excel单元格数据
     * @param fieldKeyList 数据模型数据
     * @param modelId      数据模型id
     * @return 数据模型数据集合
     */
    private List<Map<String, Object>> parseExcelData2MapData(List<List<Object>> excelData, List<String> fieldKeyList, String modelId) {
        List<Map<String, Object>> allData = new ArrayList<>(excelData.size());
        //因为有描述行，所以需要从第二行开始做数据处理
        for (int i = 1; i < excelData.size(); i++) {
            List<Object> rowExcelData = excelData.get(i);
            int size = rowExcelData.size();
            Map<String, Object> rowData = new HashMap<>(size);
            for (int j = 0; j < size; j++) {
                Object unitExcelData = rowExcelData.get(j);
                if (fieldKeyList.size() < size) {
                    continue;
                }
                String fieldKey = fieldKeyList.get(j);
                if (Objects.nonNull(unitExcelData) && StringUtils.isNotBlank(fieldKey)) {
                    try {
                        String data = unitExcelData.toString();
                        if (data.startsWith("{") && data.endsWith("}")) {
                            unitExcelData = JSONUtil.parseObj(unitExcelData);
                        } else if (data.startsWith("[") && data.endsWith("]")) {
                            unitExcelData = JSONUtil.parseArray(unitExcelData);
                        }
                    } catch (JSONException e) {
                        log.warn("导入时数据格式异常", e);
                    }
                    rowData.put(fieldKey, unitExcelData);
                }
            }
            allData.add(rowData);
        }
        return allData;
    }

}