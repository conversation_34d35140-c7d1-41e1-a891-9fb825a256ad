package cn.bctools.design.filter;

import cn.bctools.design.data.cache.EchoCache;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.rule.utils.RuleSystemThreadLocal;
import cn.hutool.core.util.URLUtil;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 设计套件统一处理
 *
 * @Author: GuoZi
 */
@Order
@Component
public class DesignFilter extends GenericFilterBean {

    public static final String HEADER_DESIGN_ID = "designId";
    public static final String HEADER_DESIGN_OPERATOR = "operator";
    public static final String HEADER_PAGE_DESIGN_ID = "pageDesignId";

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String designId = URLUtil.decode(request.getHeader(HEADER_DESIGN_ID));
        String operator = URLUtil.decode(request.getHeader(HEADER_DESIGN_OPERATOR));
        String pageDesignId = URLUtil.decode(request.getHeader(HEADER_PAGE_DESIGN_ID));
        // 记录设计id与操作类型
        DynamicDataUtils.setDesignId(designId);
        DynamicDataUtils.setOperator(operator);
        DynamicDataUtils.setPageDesignId(pageDesignId);
        filterChain.doFilter(servletRequest, servletResponse);
        //清理回显缓存
        EchoCache.removeThreadLocalCache();
        //清理逻辑线程池变量
        RuleSystemThreadLocal.clear();
    }

}