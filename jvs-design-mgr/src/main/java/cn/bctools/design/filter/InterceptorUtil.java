package cn.bctools.design.filter;


import cn.bctools.common.utils.R;
import cn.bctools.common.utils.SystemThreadLocal;
import cn.bctools.design.project.entity.JvsApp;
import com.alibaba.fastjson2.JSONObject;

import javax.servlet.ServletOutputStream;

/**
 * 拦截器工具
 */
public class InterceptorUtil {

    /**
     * 应用信息
     */
    private static final String INTERCEPTOR_APP = "app";

    /**
     * 设置应用
     *
     * @param jvsApp 应用
     */
    public static void setApp(JvsApp jvsApp) {
        SystemThreadLocal.set(INTERCEPTOR_APP, jvsApp);
    }

    /**
     * 获取应用
     *
     * @return 应用
     */
    public static JvsApp getApp() {
        return SystemThreadLocal.get(INTERCEPTOR_APP);
    }

    /**
     * 抛异常
     *
     * @param outputStream
     * @param errorMessage
     * @throws Exception
     */
    public static void throwException(ServletOutputStream outputStream, String errorMessage) throws Exception {
        outputStream.write(JSONObject.toJSONString(R.failed(errorMessage)).getBytes());
        outputStream.flush();
        outputStream.close();
    }

}
