package cn.bctools.design.local.cms.entity.vo;

import cn.bctools.common.entity.vo.BaseSignVo;
import cn.bctools.design.workflow.entity.dto.ApproveOpinionDto;
import cn.bctools.design.workflow.enums.NodeOperationTypeEnum;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class FlowExecuteVo extends BaseSignVo {

    private Long catalogId;

    private Long contentId;

    private String flowTaskId;

    private String accountName;

    private ContentVO content;

    private String id;

    private String nodeId;

    private NodeOperationTypeEnum nodeOperationType;

    private ApproveOpinionDto opinion;

}
