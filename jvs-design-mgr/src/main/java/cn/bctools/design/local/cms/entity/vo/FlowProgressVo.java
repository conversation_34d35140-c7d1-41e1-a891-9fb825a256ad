package cn.bctools.design.local.cms.entity.vo;

import cn.bctools.common.entity.vo.BaseSignVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class FlowProgressVo extends BaseSignVo {

    private String flowTaskId;

    private String dataId;

    private Long catalogId;

    private Long contentId;

    private String accountName;

}
