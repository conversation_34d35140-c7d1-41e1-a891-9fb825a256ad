package cn.bctools.design.local.cms.service;

import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.entity.dto.UserInfoDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.SystemThreadLocal;
import cn.bctools.common.utils.TenantContextHolder;
import cn.bctools.design.crud.entity.FormPo;
import cn.bctools.design.data.entity.DataLogPo;
import cn.bctools.design.data.service.DataLogService;
import cn.bctools.design.local.cms.entity.constant.ModelConstant;
import cn.bctools.design.local.cms.entity.data.FlowManage;
import cn.bctools.design.local.cms.entity.vo.*;
import cn.bctools.design.workflow.dto.FlowReqDto;
import cn.bctools.design.workflow.dto.StopTaskReqDto;
import cn.bctools.design.workflow.dto.progress.ProgressDetailResDto;
import cn.bctools.design.workflow.dto.startflow.StartFlowResDto;
import cn.bctools.design.workflow.dto.startflow.StartFlowVariables;
import cn.bctools.design.workflow.entity.FlowDesign;
import cn.bctools.design.workflow.entity.FlowTask;
import cn.bctools.design.workflow.entity.dto.CourseDto;
import cn.bctools.design.workflow.entity.enums.FlowTaskStatusEnum;
import cn.bctools.design.workflow.enums.NodeOperationTypeEnum;
import cn.bctools.design.workflow.model.enums.NodeTypeEnum;
import cn.bctools.design.workflow.service.FlowDesignService;
import cn.bctools.design.workflow.service.FlowTaskService;
import cn.bctools.design.workflow.service.TaskService;
import cn.bctools.design.workflow.service.TaskStopService;
import cn.bctools.oauth2.dto.CustomUser;
import cn.bctools.oss.dto.BaseFile;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 门户审批流程管理服务类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FlowManageService {

    private final MongoTemplate mongoTemplate;

    private final AuthUserServiceApi userServiceApi;

    private final TaskService taskService;

    private final TaskStopService stopService;

    private final FlowTaskService flowTaskService;

    private final FormHandler formHandler;

    private final DataLogService dataLogService;


    /**
     * 工作流设计列表
     *
     * @return 工作流设计列表
     */
    public List<FlowDesign> workFlowList(Long siteId) {
        List<Map> maps = mongoTemplate.find(new Query(
                        Criteria.where("siteId").is(siteId))
                        .with(Sort.by(Sort.Direction.ASC, "updateTime")),
                Map.class, ModelConstant.FLOW_MANAGE);
        List<FlowManage> list = BeanCopyUtil.copys(maps, FlowManage.class);
        return list.stream().filter(e->ObjectNull.isNotNull(e.getFlowDesignId()))
                .map(e->new FlowDesign().setId(e.getDataId()).setName(e.getFlowName()))
                .collect(Collectors.toList());
    }

    public StartFlowResDto start(FlowStartVo vo) {
        UserDto userDto = getUserDto(vo.getAccountName());
        setCurrentUser(userDto);
        StartFlowVariables startFlowVariables = new StartFlowVariables();
        startFlowVariables.setId(getFlowDesignId(vo.getFlowDesignId()));
        startFlowVariables.setModelId(ModelConstant.FLOW_CONTENT);
        startFlowVariables.setData(JSONObject.parseObject(JSON.toJSONString(vo.getContent())));
        handleData(startFlowVariables.getData());
        startFlowVariables.setSendFormId(ModelConstant.FLOW_FORM);
        return taskService.start(userDto, startFlowVariables);
    }

    public FlowTask execute(FlowExecuteVo vo) {
        UserDto userDto = getUserDto(vo.getAccountName());
        setCurrentUser(userDto);
        FlowReqDto flowReqDto = new FlowReqDto();
        flowReqDto.setId(vo.getId());
        flowReqDto.setNodeId(vo.getNodeId());
        flowReqDto.setNodeOperationType(vo.getNodeOperationType());
        flowReqDto.setData(JSONObject.parseObject(JSON.toJSONString(vo.getContent())));
        handleData(flowReqDto.getData());
        return taskService.execute(flowReqDto,userDto);
    }

    public StartFlowResDto restart(FlowRestartVo vo) {
        UserDto userDto = getUserDto(vo.getAccountName());
        setCurrentUser(userDto);
        StartFlowVariables startFlowVariables = new StartFlowVariables();
        startFlowVariables.setId(vo.getFlowTaskId());
        startFlowVariables.setModelId(ModelConstant.FLOW_CONTENT);

        startFlowVariables.setData(JSONObject.parseObject(JSON.toJSONString(vo.getContent())));
        handleData(startFlowVariables.getData());
        return taskService.restartTask(startFlowVariables, userDto);
    }

    public FlowTask withdraw(FlowStopVo vo) {
        UserDto userDto = getUserDto(vo.getAccountName());
        setCurrentUser(userDto);
        return stopService.withdrawTask(userDto, vo.getFlowTaskId(), new StopTaskReqDto().setReason(vo.getReason()));
    }

    public ProgressDetailVo progress(FlowProgressVo vo) {
        ProgressDetailResDto detail = flowTaskService.getProgressDetail(vo.getFlowTaskId(), null);
        ProgressDetailVo progressDetail = BeanCopyUtil.copy(detail, ProgressDetailVo.class);
        if (ObjectNull.isNull(detail)) {
            throw new BusinessException("任务流程不存在");
        }
        Map data = mongoTemplate.findOne(new Query(Criteria.where("dataId").is(vo.getDataId())), Map.class, ModelConstant.FLOW_CONTENT);
        //处理封面
        List<BaseFile> list = new ArrayList<>();
        Object iconUrl = data.get("iconUrl");
        if (ObjectNull.isNotNull(iconUrl)) {
            list.add(new BaseFile().setUrl(iconUrl.toString()));
        }
        data.put("iconUrl", list);
        progressDetail.setData(data);
        return progressDetail;
    }

    public FormPo flowForm() {
        return formHandler.form(ModelConstant.FLOW_FORM, ModelConstant.FLOW_APP);
    }

    private UserDto getUserDto(String accountName) {
        UserDto userDto = userServiceApi.getByIdAndAccountNameAndPhone("", accountName, "").getData();
        if (ObjectNull.isNull(userDto)) {
            throw new BusinessException("用户不存在");
        }
        return userDto;
    }

    public Map<String, Object> getLogData(String dataId, String version) {
        DataLogPo logPo = dataLogService.getLog(ModelConstant.FLOW_CONTENT, dataId, version);
        if (Objects.isNull(logPo)) {
            log.warn("历史数据查询异常, 数据不存在, 数据id: {}, 版本: {}", dataId, version);
            throw new BusinessException("历史数据查询异常, 数据不存在");
        }
        Map<String, Object> content = logPo.getContent();

        content = dataLogService.echoLogFile(ModelConstant.FLOW_APP, ModelConstant.FLOW_CONTENT, content);
        return content;
    }


    public void getTaskPage(Page<FlowTask> page, FlowTaskPageVo vo) {
        LocalDateTime updateTime = null;
        if (ObjectNull.isNotNull(vo.getUpdateTimeStamp())) {
            updateTime = Instant.ofEpochMilli(vo.getUpdateTimeStamp()).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
        }
        flowTaskService.page(page, new LambdaQueryWrapper<FlowTask>()
                .eq(FlowTask::getDataModelId, ModelConstant.FLOW_CONTENT)
                .ge(ObjectNull.isNotNull(updateTime), FlowTask::getUpdateTime, updateTime));

        page.getRecords().parallelStream().forEach(e -> {
            //待审批节点处理
            if (FlowTaskStatusEnum.PENDING.equals(e.getTaskStatus())) {
                Optional.ofNullable(e.getCourses())
                        .flatMap(courseDtos -> courseDtos.isEmpty() ? Optional.empty() : courseDtos.stream().reduce((a, b) -> b))
                        .map(CourseDto::getApproveResultDtos)
                        .flatMap(dto -> dto.isEmpty() ? Optional.empty() : dto.stream().filter(x -> NodeOperationTypeEnum.BACK.equals(x.getNodeOperationTypeEnum())
                                && NodeTypeEnum.ROOT.getDefaultNodeId().equals(x.getBackNodeId())).findFirst())
                        .ifPresent(y -> e.setTaskStatus(FlowTaskStatusEnum.BACKED));
            }
            e.setCourses(null);
            e.setAuditRoles(null);
            e.setFlowDesign(null);
            e.setFlowManualNodes(null);
        });
    }

    private void setCurrentUser(UserDto userDto) {
        CustomUser principal = new CustomUser();
        principal.setUserDto(userDto);
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(principal, null, new HashSet<>());
        SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        SecurityContextHolder.getContext().setAuthentication(authentication);
        SystemThreadLocal.set("user", (UserInfoDto<UserDto>) principal);
    }

    private void handleData(JSONObject data) {
        String tenantId = TenantContextHolder.getTenantId();
        if (ObjectNull.isNull(tenantId)) {
            tenantId = "1";
        }
        data.put("tenantId", tenantId);
    }

    private String getFlowDesignId(String dataId){
        Map data = mongoTemplate.findOne(new Query(Criteria.where("dataId").is(dataId)), Map.class, ModelConstant.FLOW_MANAGE);
        FlowManage flowMange = BeanCopyUtil.copy(data, FlowManage.class);
        return Optional.ofNullable(flowMange).map(FlowManage::getFlowDesignId)
                .orElseThrow(() -> new BusinessException("栏目关联流程设计不存在"));
    }
}
