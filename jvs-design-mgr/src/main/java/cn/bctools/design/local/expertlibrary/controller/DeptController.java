package cn.bctools.design.local.expertlibrary.controller;

import cn.bctools.common.utils.R;
import cn.bctools.design.local.expertlibrary.service.DeptService;
import cn.bctools.log.annotation.Log;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/8/1
 */
@Slf4j
@Api(value = "同步单位到MongoDB", tags = "同步单位到MongoDB接口")
@RestController
@RequestMapping("/local/dept/api")
public class DeptController {
    @Autowired
    private DeptService deptService;

    @Log
    @ApiOperation(value = "同步", notes = "同步省内单位到MongoDB")
    @PostMapping(value = "/sync")
    public R<Integer> sync() {
        return R.ok(deptService.syncDeptToMongo(LocalDateTime.now().minusYears(1)));
    }
}
