package cn.bctools.design.local.expertlibrary.controller;

import cn.bctools.common.utils.R;
import cn.bctools.design.local.expertlibrary.entity.ExpertDeptQueryVo;
import cn.bctools.design.local.expertlibrary.entity.ExpertDeptVo;
import cn.bctools.mongodb.core.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import cn.bctools.design.local.expertlibrary.service.ExpertService;

import java.util.List;

/**
 * @Author: Aaron2
 */
@RestController
@AllArgsConstructor
@RequestMapping("/application/expert")
@Api(tags = "专家库接口")
public class ExpertController {

    private final ExpertService expertService;

    @GetMapping("/dept/page")
    @ApiOperation(value = "全国单位查询", notes = "全国单位查询")
    public R<Page<ExpertDeptVo>> page(Page<ExpertDeptVo> page, ExpertDeptQueryVo deptQueryVo) {
        page = expertService.deptPage(page, deptQueryVo);
        return R.ok(page);
    }

    @ApiOperation(value = "根据全国单位org_id数组获取单位信息", notes = "根据全国单位org_id数组获取单位信息")
    @PostMapping("/dept/listByIds")
    public R<List<ExpertDeptVo>> listByIds(@RequestBody List<String> deptIds, @RequestParam(required = false, value = "showPath", defaultValue = "false") Boolean showPath) {
        List<ExpertDeptVo> list = expertService.listByIds(deptIds, showPath);
        return R.ok(list);
    }
}
