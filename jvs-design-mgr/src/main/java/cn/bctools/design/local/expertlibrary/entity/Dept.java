package cn.bctools.design.local.expertlibrary.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 专家库应用的省外单位管理数据模型
 *
 * <AUTHOR>
 * @since 2024/7/31
 */
@Data
@Document("1816010353880862722")
public class Dept {

    @Id
    private String id;

    /**
     * 状态
     */
    @Field("status")
    private String status;

    /**
     * 单位名称
     */
    @Field("name")
    private String name;

    /**
     * 机构代码
     */
    @Field("code")
    private String code;

    /**
     * 所属地区
     */
    @Field("area")
    private String area;

    /**
     * 父级id
     */
    @Field("parentId")
    private String parentId;

    /**
     * 机构类型
     */
    @Field("typeDict")
    private String typeDict;

    /**
     * 层级
     */
    @Field("level")
    private Integer level;

    /**
     * 县区号
     */
    @Field("xqCode")
    private String xqCode;

    /**
     * 地市号
     */
    @Field("dsCode")
    private String dsCode;

    /**
     * 省号
     */
    @Field("shCode")
    private String shCode;

    /**
     * 类型
     */
    @Field("type")
    private String type;

    /**
     * 单位id
     */
    @Field("org_id")
    private String orgId;

    /**
     * 县区
     */
    @Field("xqName")
    private String xqName;

    /**
     * 地市
     */
    @Field("dsName")
    private String dsName;

    /**
     * 省份
     */
    @Field("shName")
    private String shName;

    /**
     * dataId
     */
    @Field("dataId")
    private String dataId;

    /**
     * id，跟dataId同值
     */
    @Field("id")
    private String modelDataId;

    /**
     * delFlag
     */
    @Field("delFlag")
    private Boolean delFlag;
}
