package cn.bctools.design.local.expertlibrary.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: Aaron2
 */
@Data
@Accessors(chain = true)
public class ExpertDeptQueryVo {

    @ApiModelProperty(value = "区域代码")
    private String areaCode;
    @ApiModelProperty(value = "关键字")
    private String keyword;
    @ApiModelProperty(value = "机构类型")
    private String typeDict;
    @ApiModelProperty(value = "是否包含省外单位")
    private Boolean hasOther;
    @ApiModelProperty(value = "禁用状态:\"1\"-启用,\"0\"-禁用")
    private String status;
}
