package cn.bctools.design.local.expertlibrary.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * @Author: Aaron2
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "专家库单位")
public class ExpertDeptVo {

    @ApiModelProperty(value = "机构代码")
    private String code;
    @ApiModelProperty(value = "机构ID")
    @Field("org_id")
    private String orgId;
    @ApiModelProperty(value = "单位名称")
    private String name;
    @ApiModelProperty(value = "省号")
    private String shCode;
    @ApiModelProperty(value = "省份名称")
    private String shName;
    @ApiModelProperty(value = "地市号")
    private String dsCode;
    @ApiModelProperty(value = "地市名称")
    private String dsName;
    @ApiModelProperty(value = "县区号")
    private String xqCode;
    @ApiModelProperty(value = "县区名称")
    private String xqName;
    @ApiModelProperty(value = "机构类型")
    private String typeDict;
    @ApiModelProperty(value = "状态（0停用1 启用）")
    private Integer status;
    @ApiModelProperty(value = "父级ID")
    private String parentId;

}
