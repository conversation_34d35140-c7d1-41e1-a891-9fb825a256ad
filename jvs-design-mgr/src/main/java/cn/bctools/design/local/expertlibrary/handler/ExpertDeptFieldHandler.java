package cn.bctools.design.local.expertlibrary.handler;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.data.cache.EchoCache;
import cn.bctools.design.data.fields.impl.PathCachedMultipleTypeHandler;
import cn.bctools.design.local.expertlibrary.entity.ExpertDeptVo;
import cn.bctools.design.data.fields.DataFieldHandler;
import cn.bctools.design.data.fields.DesignField;
import cn.bctools.design.data.fields.IDataFieldHandler;
import cn.bctools.design.data.fields.dto.form.MultipleHtml;
import cn.bctools.design.data.fields.enums.DataFieldType;
import cn.bctools.design.data.fields.impl.ISelectorDataHandler;
import cn.bctools.design.local.expertlibrary.service.ExpertService;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 专家库单位选择
 *
 * @Author: Aaron2
 */
@Slf4j
@Component
@DesignField(value = "专家库单位选择", type = DataFieldType.expertDept)
public class ExpertDeptFieldHandler extends PathCachedMultipleTypeHandler implements IDataFieldHandler<MultipleHtml>, ISelectorDataHandler {

    @Resource
    ExpertService expertService;

    @Resource
    @Lazy
    DataFieldHandler dataFieldHandler;

    /**
     * 省外单位管理mongodb数据库id
     */
    private static final String EXPERT_DEPT = "1816010353880862722";

    @Override
    public Object getEchoValue(MultipleHtml html, Object data, boolean override, Map<String, Object> lineData, String... paths) {
        return echoValueWithCache(html, data, lineData, getFieldType(), this::getConfigKey,
                () -> loadDataSource(html));
    }

    @Override
    protected Object echoValueFallback(MultipleHtml html, Object data, Map<String, Object> lineData) {
        boolean isMulti = html.isMultiple();
        boolean showPath = html.isShowPath();
        List<String> deptIdList;
        if (data instanceof List) {
            deptIdList = (List<String>) data;
        } else {
            deptIdList = new ArrayList<>(1);
            deptIdList.add(String.valueOf(data));
        }
        if (CollectionUtil.isEmpty(deptIdList)) {
            return null;
        }
        //获取当前部门和所有父级部门
        List<ExpertDeptVo> deptList = expertService.getAllParentsAndSelf(deptIdList);
        if (ObjectNull.isNull(deptList)) {
            return null;
        }

        //显示路径会去掉list的第一项，部门/单位构造虚拟的第一项数据，防止无法显示顶级部门
        ExpertDeptVo first = new ExpertDeptVo().setOrgId("1").setName("temp");
        deptList.add(0, first);
        Map<String, Object> deptMap = deptList.stream().collect(Collectors.toMap(ExpertDeptVo::getOrgId, ExpertDeptVo::getName));
        data = dataFieldHandler.handlePathId(data, isMulti, showPath, deptList, ExpertDeptVo::getOrgId, ExpertDeptVo::getParentId);

        return dataFieldHandler.joinFormItems(deptMap, data, isMulti, showPath);
    }

    @Override
    public Boolean whetherCoverValue() {
        return Boolean.FALSE;
    }

    @Override
    protected DataFieldType getFieldType() {
        return DataFieldType.expertDept;
    }

    @Override
    protected String getConfigKey(MultipleHtml html) {
        return getFieldType().name();
    }

    @Override
    public Map<String, Object> loadDataSource(MultipleHtml html) {
        List<String> expertDeptIds = EchoCache.getMergeSource(getFieldType());
        if (ObjectNull.isNull(expertDeptIds)) {
            return new HashMap<>();
        }
        //获取当前部门和所有父级部门(为了防止有多个组件针对路径有不同的设置，默认查出父级)
        List<ExpertDeptVo> deptList = expertService.getAllParentsAndSelf(expertDeptIds);
        if (ObjectNull.isNull(deptList)) {
            return new HashMap<>();
        }
        Map<String, Object> map = new HashMap<>();
        Boolean showPath = html.isShowPath();
        if (showPath) {
            ExpertDeptVo first = new ExpertDeptVo().setOrgId("1").setName("temp");
            deptList.add(0, first);
        }
        map = deptList.stream()
                .collect(Collectors.toMap(ExpertDeptVo::getOrgId, ExpertDeptVo::getName));
        map.put(PATH_SOURCE_KEY, deptList);
        return map;
    }


    @SuppressWarnings("unchecked")
    @Override
    protected String getShowPathValue(Object dataList, MultipleHtml html, String configKey) {
        boolean isMultiple = html.isMultiple();
        Map<String, Object> cachedMapping = EchoCache.getCachedMapping(getFieldType(), configKey);
        if (Objects.isNull(cachedMapping)) {
            return "";
        }
        Object o = cachedMapping.get(PATH_SOURCE_KEY);
        if (ObjectNull.isNull(o)) {
            return "";
        }
        List<ExpertDeptVo> deptList = (List<ExpertDeptVo>) o;

        Object path = dataFieldHandler.handlePathId(dataList, isMultiple, true, deptList,
                ExpertDeptVo::getOrgId, ExpertDeptVo::getParentId);
        return dataFieldHandler.joinFormItems(cachedMapping, path, isMultiple, true);

    }
}