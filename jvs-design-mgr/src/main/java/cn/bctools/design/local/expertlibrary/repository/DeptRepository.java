package cn.bctools.design.local.expertlibrary.repository;

import cn.bctools.design.local.expertlibrary.entity.Dept;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2024/7/31
 */
@Repository
public interface DeptRepository extends MongoRepository<Dept, String> {

    @Nullable
    Dept findByOrgId(String orgId);
}
