package cn.bctools.design.local.expertlibrary.schedule.handler;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.local.expertlibrary.service.DeptService;
import cn.bctools.redis.utils.RedisUtils;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/7/31
 */
@Component
@AllArgsConstructor
@Slf4j
public class SyncDeptToMongoDbHandler {

    private final DeptService deptService;
    private final RedisUtils redisUtils;

    private static final String LAST_RUN_TIME_KEY = "jvs:app-design-mgr:sync-dept-to-mongodb:last-run-time";

    /**
     * 同步部门数据到专家库MongoDB
     */
    @XxlJob("sync-dept-to-mongodb")
    public void run() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时任务【同步部门数据到专家库MongoDB】开始执行，入参为:{}", param);
        JSONObject json = compatibilityJsonCheck(param);

        Boolean syncAll = json.getBoolean("all");

        LocalDateTime startRunTime = LocalDateTime.now();

        LocalDateTime lastRunTime = redisUtils.getTimeValue(LAST_RUN_TIME_KEY);
        LocalDateTime lastUpdateTime = lastRunTime;

        if (lastRunTime == null || syncAll) {
            lastUpdateTime = LocalDateTime.now().minusYears(10);
        }

        log.info(
                "sync-dept-to-mongodb is running, last run time is {}, startRunTime is {}, lastUpdateTime is {}",
                lastRunTime, startRunTime, lastUpdateTime
        );

        deptService.syncDeptToMongo(lastUpdateTime);

        redisUtils.setTimeValue(LAST_RUN_TIME_KEY, startRunTime.minusSeconds(1));
    }

    /**
     * 参数兼容json格式
     *
     * @param param 入参
     * @return JSONObject
     */
    private JSONObject compatibilityJsonCheck(String param) {
        XxlJobHelper.log("调用逻辑的数据为{}", param);
        if (ObjectNull.isNull(param)) {
            XxlJobHelper.log("数据传递参数为空,{},执行执行错误", param);
            throw new BusinessException("执行错误");
        }
        try {
            //做json格式兼容
            JSONObject jsonObject = JSONObject.parseObject(param);
            XxlJobHelper.log("数据格式为json格式,兼容检测成功,调用参数为,{}", jsonObject.toString());
            return jsonObject;
        } catch (Exception e) {
            XxlJobHelper.log("定时任务请求参数异常 不是json ，请以 【{\"key\":\"xxx\",\"startTime\":\"xxxx\",\"xxx\":\"xxxx\"}】 ，值为,{}", param);
            throw new BusinessException("逻辑定时任务参数不正确,请使用Json格式");
        }
    }
}
