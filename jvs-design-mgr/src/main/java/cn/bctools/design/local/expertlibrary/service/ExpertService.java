package cn.bctools.design.local.expertlibrary.service;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.data.component.DataModelHandler;
import cn.bctools.design.local.expertlibrary.entity.ExpertDeptQueryVo;
import cn.bctools.design.local.expertlibrary.entity.ExpertDeptVo;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.mongodb.core.Page;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Aaron2
 */
@Service
@AllArgsConstructor
public class ExpertService {

    private final DataModelHandler dataModelHandler;

    /**
     * 省外单位管理mongodb数据库id
     */
    private static final String EXPERT_DEPT = "1816010353880862722";

    public Page<ExpertDeptVo> deptPage(Page<ExpertDeptVo> page, ExpertDeptQueryVo deptQueryVo) {
        Criteria criteria = DynamicDataUtils.trueCriteria();
        List<Criteria> criteriaList = new ArrayList<>();
        if (ObjectNull.isNotNull(deptQueryVo.getAreaCode())) {
            int length = StrUtil.length(deptQueryVo.getAreaCode());
            switch (length) {
                case 11:
                    criteriaList.add(Criteria.where("shCode").is(deptQueryVo.getAreaCode()));
                    break;
                case 13:
                    criteriaList.add(Criteria.where("dsCode").is(deptQueryVo.getAreaCode()));
                    break;
                case 15:
                    criteriaList.add(Criteria.where("xqCode").is(deptQueryVo.getAreaCode()));
                    break;
                default:
                    break;
            }
        }

        if (ObjectNull.isNotNull(deptQueryVo.getKeyword())) {
            criteriaList.add(Criteria.where("name").regex(DynamicDataUtils.parseRegular(deptQueryVo.getKeyword())));
        }

        if (ObjectNull.isNotNull(deptQueryVo.getTypeDict())) {
            criteriaList.add(Criteria.where("typeDict").is(deptQueryVo.getTypeDict()));
        }

        if (ObjectNull.isNotNull(deptQueryVo.getHasOther()) && !deptQueryVo.getHasOther()) {
            //排除单位"外省或其他单位"
            criteriaList.add(Criteria.where("code").ne("449900000001"));
        }

        if (ObjectNull.isNotNull(deptQueryVo.getStatus())){
            criteriaList.add(Criteria.where("status").is(deptQueryVo.getStatus()));
        }

        if (!criteriaList.isEmpty()) {
            criteria.andOperator(criteriaList.toArray(new Criteria[0]));
        }

        Sort sort = Sort.by(Sort.Direction.ASC, "shCode")
                .and(Sort.by(Sort.Direction.ASC, "org_id")
                        .and(Sort.by(Sort.Direction.ASC, "typeDict")));

        dataModelHandler.page(new Query(criteria).with(sort), page, ExpertDeptVo.class, EXPERT_DEPT);
        return page;
    }

    public List<ExpertDeptVo> getAllParentsAndSelf(List<String> deptIdList) {
        List<String> parentIdList = new ArrayList<>();
        parentIdList.addAll(deptIdList);
        for (String deptId : deptIdList) {
            Query query = new Query(Criteria.where("org_id").is(deptId));
            query.fields().include("parentId");
            ExpertDeptVo one = dataModelHandler.findOne(query, ExpertDeptVo.class, EXPERT_DEPT);
            if (ObjectNull.isNotNull(one)){
                parentIdList.add(one.getParentId());
            }
        }
        return dataModelHandler.find(new Query(Criteria.where("org_id").in(parentIdList)),
                ExpertDeptVo.class, EXPERT_DEPT);
    }

    public List<ExpertDeptVo> listByIds(List<String> deptIds, Boolean showPath) {
        Query query = new Query();
//        query.fields().include("id","org_id","name");
        query.addCriteria(Criteria.where("org_id").in(deptIds));
        List<ExpertDeptVo> list = dataModelHandler.find(query, ExpertDeptVo.class, EXPERT_DEPT);
        return list;
    }
}
