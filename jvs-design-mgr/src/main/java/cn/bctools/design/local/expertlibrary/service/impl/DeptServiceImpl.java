package cn.bctools.design.local.expertlibrary.service.impl;

import cn.bctools.auth.api.api.AuthDeptServiceApi;
import cn.bctools.auth.api.dto.SysDeptDto;
import cn.bctools.design.local.expertlibrary.entity.Dept;
import cn.bctools.design.local.expertlibrary.repository.DeptRepository;
import cn.bctools.design.local.expertlibrary.service.DeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/31
 */
@Service
public class DeptServiceImpl implements DeptService {

    @Autowired
    private DeptRepository deptRepository;

    @Autowired
    private AuthDeptServiceApi authDeptServiceApi;

    // 每次处理的数据量
    private static final int BATCH_SIZE = 1000;
    private static final String DEFAULT_STATUS = "1";

    @Override
    public Integer syncDeptToMongo(LocalDateTime lastUpdatedDateTime) {

        // 字典前缀，拼接在地市号、县区号和省号前面
        String dictPrefix = "932255983";

        List<SysDeptDto> deptList = authDeptServiceApi.listByUpdateTime(lastUpdatedDateTime).getData();

        List<String> toDeleteIds = new ArrayList<>();
        List<Dept> toSaveDeptList = new ArrayList<>();

        for (SysDeptDto dept : deptList) {
            if (dept.getDelFlag()) {
                toDeleteIds.add(dept.getId());
                continue;
            }

            Dept updatedDept = new Dept();

            if (dept.getStatus() == null) {
                updatedDept.setStatus(DEFAULT_STATUS);
            } else {
                updatedDept.setStatus(dept.getStatus().toString());
            }

            Dept existDept = deptRepository.findByOrgId(dept.getId());
            if (existDept != null) {
                updatedDept = existDept;
            }

            if (updatedDept.getDataId() == null) {
                updatedDept.setDataId(dept.getId());
                updatedDept.setModelDataId(dept.getId());
            }
            updatedDept.setOrgId(dept.getId());
            updatedDept.setType("2");
            updatedDept.setName(dept.getName());
            updatedDept.setCode(dept.getDeptCode());
            updatedDept.setDsCode(dictPrefix + dept.getDsCode());
            updatedDept.setDsName(dept.getDsName());
            updatedDept.setXqCode(dictPrefix + dept.getXqCode());
            updatedDept.setXqName(dept.getXqName());
            updatedDept.setShCode(dictPrefix + dept.getShCode());
            updatedDept.setShName(dept.getShName());
            updatedDept.setTypeDict(dept.getTypeDict());
            updatedDept.setLevel(dept.getLevel());
            updatedDept.setParentId(dept.getParentId());
            updatedDept.setDelFlag(false);

            toSaveDeptList.add(updatedDept);

            if (toSaveDeptList.size() >= BATCH_SIZE) {
                deptRepository.saveAll(toSaveDeptList);
                toSaveDeptList.clear();
            }
            if (toDeleteIds.size() >= BATCH_SIZE) {
                deptRepository.deleteAllById(toDeleteIds);
                toDeleteIds.clear();
            }
        }

        if (!toDeleteIds.isEmpty()) {
            deptRepository.deleteAllById(toDeleteIds);
        }
        if (!toSaveDeptList.isEmpty()) {
            deptRepository.saveAll(toSaveDeptList);
        }

        return deptList.size();
    }
}
