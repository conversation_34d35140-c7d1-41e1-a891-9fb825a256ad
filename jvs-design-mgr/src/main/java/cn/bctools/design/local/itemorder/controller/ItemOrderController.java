package cn.bctools.design.local.itemorder.controller;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import cn.bctools.design.component.DataModelComponent;
import cn.bctools.design.crud.entity.CrudPage;
import cn.bctools.design.crud.service.CrudPageService;
import cn.bctools.design.crud.utils.DesignUtils;
import cn.bctools.design.data.component.DataModelHandler;
import cn.bctools.design.data.entity.DynamicDataPo;
import cn.bctools.design.data.fields.dto.QueryOrderDto;
import cn.bctools.design.data.fields.dto.QueryPageDto;
import cn.bctools.design.data.fields.dto.page.PageAggregationSetting;
import cn.bctools.design.data.fields.dto.page.PageDesignHtml;
import cn.bctools.design.data.service.DynamicDataService;
import cn.bctools.design.local.itemorder.entity.ItemOrderBatchUpdateDto;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.log.annotation.Log;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Api(value = "列表数据排序", tags = "列表数据排序接口")
@RestController
@RequestMapping("/local/item-order")
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class ItemOrderController {

    DataModelComponent dataModelComponent;
    CrudPageService pageService;
    DynamicDataService dynamicDataService;

    @Log
    @ApiOperation(value = "更新列表数据排序", notes = "更新列表数据排序")
    @PostMapping("/batch-update")
    public R<String> batchUpdate(@RequestBody ItemOrderBatchUpdateDto data) {
        if (data.getList().size() < 2) {
            return R.failed("至少需要传入两条数据");
        }

        List<String> itemOrderList = data.getList().stream().map(ItemOrderBatchUpdateDto.ListItemDto::getItemOrder).collect(Collectors.toList());
        List<String> uniqueItemOrderList = itemOrderList.stream().distinct().collect(Collectors.toList());
        if (itemOrderList.size() != uniqueItemOrderList.size()) {
            return R.failed("排序字段值不能重复");
        }

        for (int i = 0; i < data.getList().size(); i++) {

            ItemOrderBatchUpdateDto.ListItemDto listItemDto = data.getList().get(i);
            Map<String, Object> updatedData = new HashMap<>();

            if (ObjectNull.isNull(listItemDto.getItemOrder())) {
                return R.failed("传入的排序字段不能为空");
            }

            updatedData.put("item_order", listItemDto.getItemOrder());

            dataModelComponent.updateData(data.getJvsAppId(), data.getModelId(), null, data.getList().get(i).getDataId(), updatedData);
        }

        return R.ok("已更新 " + data.getList().size() + " 条数据");
    }

    @Log
    @ApiOperation(value = "重置列表旧数据排序", notes = "重置列表旧数据排序")
    @GetMapping("/reset")
    public R<String> reset(
            @RequestParam String appId,
            @RequestParam String modelId,
            @RequestParam String designId,
            @RequestParam String newDataThere,
            @RequestParam(defaultValue = "10") String length
    ) {
        if (ObjectNull.isNull(appId) || ObjectNull.isNull(modelId)) {
            return R.failed("参数错误");
        }

        if (ObjectNull.isNull(appId)) {
            return R.failed("请传入 appId");
        }

        if (ObjectNull.isNull(modelId)) {
            return R.failed("请传入 modelId");
        }

        if (StringUtils.isBlank(designId)) {
            return R.failed("请传入 designId");
        }

        if (StringUtils.isBlank(newDataThere)) {
            return R.failed("请传入 newDataThere）");
        }

        if (!"beginning".equals(newDataThere) && !"end".equals(newDataThere)) {
            return R.failed("newDataThere 只能是 beginning 或 end");
        }

        CrudPage crudPage = pageService.getById(designId);
        if (ObjectNull.isNull(crudPage)) {
            return R.failed("没有 crudPage");
        }

        PageDesignHtml pageDesignHtml = DesignUtils.parsePage(crudPage.getViewJson());
        DynamicDataUtils.setPageDesignFilterType(pageDesignHtml.getAndFilterType());

        QueryPageDto queryPageDto = new QueryPageDto();

        queryPageDto.setSorts(BeanCopyUtil.copys(pageDesignHtml.getSorts(), QueryOrderDto.class));

        com.baomidou.mybatisplus.extension.plugins.pagination.Page<DynamicDataPo> page = new Page<>(1, 9999999);

        List<String> collect = new ArrayList<>();
        collect.add("id");
        collect.add("dataId");
        collect.add("item_order");

        Page<Map<String, Object>> list = dynamicDataService.queryPage(
                appId, page,
                modelId, new HashMap<>(),
                new ArrayList<>(),
                queryPageDto.getSorts(),
                collect,
                true,
                true,
                true,
                new ArrayList<>(),
                new PageAggregationSetting()
        );

        int total = (int) list.getTotal();

        List<Map<String, Object>> records = list.getRecords();

        if ("beginning".equals(newDataThere)) {
            Collections.reverse(records);
        }

        int itemOrderLength = 10;
        if (StringUtils.isNotBlank(length)) {
            itemOrderLength = Integer.parseInt(length);
        }
        int i = 1;
        for (Map<String, Object> record : records) {
            String itemOrder = String.valueOf(i);
            itemOrder = StringUtils.leftPad(itemOrder, itemOrderLength, '0');
            record.put("item_order", itemOrder);

            dynamicDataService.updateBatchById(modelId, records);

            i++;
        }

        return R.ok("已重置 " + total + " 条数据");
    }
}