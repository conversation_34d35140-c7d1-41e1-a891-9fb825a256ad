package cn.bctools.design.local.itemorder.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Accessors(chain = true)
public class ItemOrderBatchUpdateDto {

    @ApiModel("单个排序数据")
    @Data
    @Accessors(chain = true)
    public static class ListItemDto {

        @ApiModelProperty("数据 id")
        private String dataId;

        @ApiModelProperty("排序字段")
        private String itemOrder;
    }

    @ApiModelProperty(value = "jvsAppId", required = true)
    @NotNull(message = "jvsAppId 不能为空")
    private String jvsAppId;

    @ApiModelProperty(value = "modelId", required = true)
    @NotNull(message = "modelId 不能为空")
    private String modelId;

    @ApiModelProperty(value = "list", required = true)
    @NotNull(message = "list 不能为空")
    private List<ListItemDto> list;

}
