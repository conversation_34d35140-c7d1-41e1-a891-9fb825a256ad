package cn.bctools.design.local.jyjg.excels;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.design.local.kcjx.vo.SyncAnalysisModelVo;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Builder;
import lombok.Data;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.BiConsumer;

@Data
@Builder
public class StatDetail {

    @ExcelProperty(value = {"区域", "省份"}, index = 0)
    private String shName;

    @ExcelProperty(value = {"区域", "地市"}, index = 1)
    private String dsName;

    @ExcelProperty(value = {"区域", "县区"}, index = 2)
    private String xqName;

    @ExcelProperty(value = {"区域", "总数"}, index = 3)
    private Integer total;

    @ExcelProperty(value = {"岗位情况", "在编在岗"}, index = 4)
    private Long zbzg;

    @ExcelProperty(value = {"岗位情况", "在编不在岗"}, index = 5)
    private Long zbbzg;

    @ExcelProperty(value = {"岗位情况", "在岗不在编"}, index = 6)
    private Long zgbzb;

    // 赋值数据中的岗位情况
    public void updateJobAttribute(List<HashMap> data) {
        Map<String, Long> defaultValues = new HashMap<>();
        defaultValues.put("在编在岗", 0L);
        defaultValues.put("在编不在岗", 0L);
        defaultValues.put("在岗不在编", 0L);

        // 映射属性名到处理方法
        Map<String, BiConsumer<StatDetail, Long>> attrHandlers = new HashMap<>();
        attrHandlers.put("在编在岗", StatDetail::setZbzg);
        attrHandlers.put("在编不在岗", StatDetail::setZbbzg);
        attrHandlers.put("在岗不在编", StatDetail::setZgbzb);

        // 应用默认值
        defaultValues.forEach((key, value) -> attrHandlers.get(key).accept(this, value));
        updateAttr(data, attrHandlers);
    }

    @ExcelProperty(value = {"学科覆盖情况", "全学段学科"}, index = 7)
    private Long xkqxdxk;

    @ExcelProperty(value = {"学科覆盖情况", "小学"}, index = 8)
    private Long xkxx;

    @ExcelProperty(value = {"学科覆盖情况", "初中"}, index = 9)
    private Long xkcz;

    @ExcelProperty(value = {"学科覆盖情况", "高中"}, index = 10)
    private Long xkgz;

    @ExcelProperty(value = {"学科覆盖情况", "学前"}, index = 11)
    private Long xkxq;

    @ExcelProperty(value = {"学科覆盖情况", "劳动教育"}, index = 12)
    private Long xkldjy;

    @ExcelProperty(value = {"学科覆盖情况", "特殊教育"}, index = 13)
    private Long xktxjy;

    @ExcelProperty(value = {"学科覆盖情况", "心理健康教育"}, index = 14)
    private Long xkxljkjy;

    @ExcelProperty(value = {"职称情况", "高级"}, index = 15)
    private Long zcgj;

    @ExcelProperty(value = {"职称情况", "中级"}, index = 16)
    private Long zczj;

    @ExcelProperty(value = {"职称情况", "中级及以下"}, index = 17)
    private Long zczjjyx;

    // 赋值数据中的职称情况
    public void updatePositionalTitleRemarks(List<HashMap> data) {
        Map<String, Long> defaultValues = new HashMap<>();
        defaultValues.put("level1", 0L);
        defaultValues.put("level2", 0L);
        defaultValues.put("level3", 0L);

        // 映射属性名到处理方法
        Map<String, BiConsumer<StatDetail, Long>> attrHandlers = new HashMap<>();
        attrHandlers.put("level1", StatDetail::setZcgj);
        attrHandlers.put("level2", StatDetail::setZczj);
        attrHandlers.put("level3", StatDetail::setZczjjyx);

        // 应用默认值
        defaultValues.forEach((key, value) -> attrHandlers.get(key).accept(this, value));
        updateAttr(data, attrHandlers);
    }

    @ExcelProperty(value = {"学历情况", "博士研究生"}, index = 18)
    private Long xlbsyjs;

    @ExcelProperty(value = {"学历情况", "硕士研究生"}, index = 19)
    private Long xlssyjs;

    @ExcelProperty(value = {"学历情况", "大学本科"}, index = 20)
    private Long xldxbk;

    @ExcelProperty(value = {"学历情况", "大专及以下"}, index = 21)
    private Long xldzjyx;

    // 赋值数据中的职称情况
    public void updateHighestQualification(List<HashMap> data) {
        Map<String, Long> defaultValues = new HashMap<>();
        defaultValues.put("博士研究生", 0L);
        defaultValues.put("硕士研究生", 0L);
        defaultValues.put("大学本科", 0L);
        defaultValues.put("大专及以下", 0L);

        // 映射属性名到处理方法
        Map<String, BiConsumer<StatDetail, Long>> attrHandlers = new HashMap<>();
        attrHandlers.put("博士研究生", StatDetail::setXlbsyjs);
        attrHandlers.put("硕士研究生", StatDetail::setXlssyjs);
        attrHandlers.put("大学本科", StatDetail::setXldxbk);
        attrHandlers.put("大专及以下", StatDetail::setXldzjyx);

        // 应用默认值
        defaultValues.forEach((key, value) -> attrHandlers.get(key).accept(this, value));
        updateAttr(data, attrHandlers);
    }

    @ExcelProperty(value = {"年龄情况", "平均年龄"}, index = 22)
    private String pjnl;

    @ExcelProperty(value = {"年龄情况", "平均教龄"}, index = 23)
    private String pjjl;

    @ExcelProperty(value = {"荣誉", "南粤优秀教师"}, index = 24)
    private Long rynyyxjs;

    @ExcelProperty(value = {"荣誉", "省特级教师"}, index = 25)
    private Long rystjjs;

    @ExcelProperty(value = {"荣誉", "南粤优秀教育工作者"}, index = 26)
    private Long rynyyxjygzz;

    // 赋值数据中荣誉
    public void updateReward(List<HashMap> data) {
        Map<String, Long> defaultValues = new HashMap<>();
        defaultValues.put("南粤优秀教师", 0L);
        defaultValues.put("省特级教师", 0L);
        defaultValues.put("南粤优秀教育工作者", 0L);

        // 映射属性名到处理方法
        Map<String, BiConsumer<StatDetail, Long>> attrHandlers = new HashMap<>();
        attrHandlers.put("南粤优秀教师", StatDetail::setRynyyxjs);
        attrHandlers.put("省特级教师", StatDetail::setRystjjs);
        attrHandlers.put("南粤优秀教育工作者", StatDetail::setRynyyxjygzz);

        // 应用默认值
        defaultValues.forEach((key, value) -> attrHandlers.get(key).accept(this, value));
        updateAttr(data, attrHandlers);
    }


    private <T> void updateAttr(List<HashMap> data,  Map<String, BiConsumer<StatDetail, Long>> attrHandlers) {
        // 遍历并更新属性值
        data.forEach(attr -> {
            String key = (String) attr.get("name");
            Object valueObj = attr.getOrDefault("count", 0);
            // 安全类型转换
            Long value;
            if (valueObj instanceof Long) {
                value = (Long) valueObj;
            } else if (valueObj instanceof String) {
                try {
                    value = Long.parseLong((String) valueObj);
                } catch (NumberFormatException e) {
                    value = 0L; // 或者抛出异常，根据业务需求决定
                }
            } else {
                value = 0L; // 默认值
            }
            attrHandlers.getOrDefault(key, (sd, v) -> {}).accept(this, value);
        });
    }

    // 构建写入excel的列表
    public static List<Object> buildStatDetailList(List<Map> stats) {

        List<Object> list = new ArrayList<>();

        stats.forEach(stat -> {
            Map<String, Object> statDetail = (Map<String, Object>) stat;

            // 对每个Stat对象进行处理，这里只是一个示例操作
            StatDetail statDetail1 = StatDetail.builder()
                    .shName((String) statDetail.get("shName"))
                    .dsName((String) statDetail.get("dsName"))
                    .xqName((String) statDetail.get("xqName"))
                    .total((Integer) statDetail.get("total"))
                    .xkqxdxk((Long) statDetail.get("xk_all"))
                    .xkxx((Long) statDetail.get("xk_xx"))
                    .xkcz((Long) statDetail.get("xk_cz"))
                    .xkgz((Long) statDetail.get("xk_gz"))
                    .xkxq((Long) statDetail.get("xk_xq"))
                    .xkldjy((Long) statDetail.get("xk_ld"))
                    .xktxjy((Long) statDetail.get("xk_ts"))
                    .xkxljkjy((Long) statDetail.get("xk_xl"))
                    .pjnl((String) statDetail.get("avgAge"))
                    .pjjl((String) statDetail.get("avgExperience"))
                    .build();

            statDetail1.updateJobAttribute((List<HashMap>) statDetail.get("jobAttributesStat"));
            statDetail1.updatePositionalTitleRemarks((List<HashMap>) statDetail.get("positionalTitleStat"));
            statDetail1.updateHighestQualification((List<HashMap>) statDetail.get("highestQualificationStat"));
            statDetail1.updateReward((List<HashMap>) statDetail.get("honorStat"));

            list.add(statDetail1);
        });

      if (!list.isEmpty()) {
          Map<String, Object> totalRowMap = new HashMap<>();
          List<String> expectTotal = Arrays.asList("shName", "dsName", "xqName");

          list.forEach(sd -> {
              for (Field field : StatDetail.class.getDeclaredFields()) {
                  try {
                      if (expectTotal.contains(field.getName())) {
                          continue;
                      }
                      // 统计合计
                      Object oldValueObj = totalRowMap.getOrDefault(field.getName(), "0");
                      Number oldNumber = convertToNumber(oldValueObj);
                      double old = oldNumber.doubleValue();

                      // 获取新值并尝试转换为 Number 类型
                      Object newValueObj = field.get(sd);
                      Number newNumber = convertToNumber(newValueObj);
                      double newValue = newNumber.doubleValue();
                      totalRowMap.put(field.getName(), old + newValue);
                  } catch (IllegalAccessException e) {
                      throw new RuntimeException(e);
                  }
              }
          });
          double pjnl = (double)totalRowMap.getOrDefault("pjnl", 0) / list.size();
          double pjjl = (double)totalRowMap.getOrDefault("pjjl", 0) / list.size();
          // 保留两位小数
          String formattedNumber1 = String.format("%.2f", pjnl);
          String formattedNumber2 = String.format("%.2f", pjjl);
          totalRowMap.put("pjjl", formattedNumber2);
          totalRowMap.put("pjnl", formattedNumber1);
          StatDetail totalRow = BeanCopyUtil.copy(totalRowMap, StatDetail.class);
          totalRow.setXqName("合计");
          list.add(totalRow);
      }

        return list;
    }

    private static Number convertToNumber(Object obj) {
        if (obj instanceof Number) {
            return (Number) obj;
        } else if (obj instanceof String) {
            try {
                String str = (String) obj;
                if (str.contains(".")) {
                    return Double.parseDouble(str);
                } else {
                    return Long.parseLong(str);
                }
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("无法将字符串转换为数字: " + obj);
            }
        } else {
            throw new IllegalArgumentException("不支持的类型: " + obj.getClass());
        }
    }


}
