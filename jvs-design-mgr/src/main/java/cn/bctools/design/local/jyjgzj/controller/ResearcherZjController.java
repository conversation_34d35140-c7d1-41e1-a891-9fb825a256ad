package cn.bctools.design.local.jyjgzj.controller;

import cn.bctools.common.utils.R;
import cn.bctools.design.local.jyjgzj.entity.ResearcherZjDTO;
import cn.bctools.design.local.jyjgzj.service.ResearcherZjDetailExportService;
import cn.bctools.design.local.jyjgzj.service.ResearcherZjExportService;
import cn.bctools.design.local.jyjgzj.service.ResearcherZjService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @Project: jvs-backend
 * @Package: cn.bctools.design.local.jyjg.controller
 * @Description: 教研员统计相关接口
 * @Author: libo
 * @Email: <EMAIL>
 * @Date: 2024/5/9 21:43
 */
@Api(tags = "[local-jyjg]中级教研员统计相关接口")
@RestController
@RequestMapping("/local/jyjgzj/researcher")
@AllArgsConstructor
public class ResearcherZjController {
    @Autowired
    private ResearcherZjService researcherZjService;

    @Autowired
    private ResearcherZjExportService researcherZjExportService;

    @Autowired
    private ResearcherZjDetailExportService researcherZjDetailExportService;

    @PostMapping("/stat")
    public R<Page> getResearcherStat(@RequestBody Map<String, String> params) {
        return R.ok(researcherZjService.getStatDetails(params));
    }

    @PostMapping("/stat_detail")
    public R<Page> getResearcherDetail(
            @RequestBody Map<String, String> params) {
        //org.springframework.data.domain.Page page=0为第一页
        org.springframework.data.domain.Page<ResearcherZjDTO> researcherDTOPage = researcherZjService.findStatDetails(params);
        //转成com.baomidou.mybatisplus.extension.plugins.pagination.Page，和其他接口统一返回格式，1为第一页
        Page page = Page.of(researcherDTOPage.getNumber() + 1, researcherDTOPage.getSize(), researcherDTOPage.getTotalElements());
        page.setRecords(researcherDTOPage.getContent());
        return R.ok(page);
    }

    @PostMapping("/stat_export")
    public void exportResearcherStat(
            HttpServletResponse response,
            @RequestBody Map<String, String> params) {
        researcherZjExportService.exportResearcherStat(response, params);
    }

    @PostMapping("/stat_detail_export")
    public void exportResearcherDetailStat(
            HttpServletResponse response,
            @RequestBody Map<String, String> params) {
        researcherZjDetailExportService.exportUserList(response, params);
    }

    @PostMapping("/stat_report")
    public  R<Map<String, Object>> getResearcherStatReport(@RequestBody Map<String, String> params) {
        Map<String, Object> map =  researcherZjService.getResearcherStatReport(params);
        return R.ok(map);
    }
}
