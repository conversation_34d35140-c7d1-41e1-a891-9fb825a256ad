package cn.bctools.design.local.jyjgzj.entity;

import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class ResearcherZjDTO {

    @Id
    private String id;
    private String realName;
    // 年龄
    private Integer age;

    public Integer getAge() {
        return dealValue(age, 0);
    }

    // 教龄
    private Integer experience;
    public Integer getExperience() {
        return dealValue(experience, 0);
    }

    // 担任教员年限
    private Integer drjyynx;
    public Integer getDrjyynx() {
        return dealValue(drjyynx, 0);
    }

    private String sex;
    public String getSex() {
        return dealValue(sex,"未选择");
    }

    // 中小学从教年限
    private Integer zxxcjnx;
    public Integer getZxxcjnx() {
        return dealValue(zxxcjnx, 0);
    }

    // 年份
    private String year;
    // 季度
    private String quarter;

    // 最高学位
    private String highestDegree;
    public String getHighestDegree() {
        return dealValue(highestDegree,"未选择");
    }

    // 最高学历
    private String highestQualification;
    public String getHighestQualification() {
        return dealValue(highestQualification,"未选择");
    }

    private String majorBackground;
    public String getMajorBackground() {
        return dealValue(majorBackground,"");
    }

    // 职称
    private String positionalTitle;
    public String getPositionalTitle() {
        return dealValue(positionalTitle,"未选择");
    }

    // 职称级别
    private String positionalTitleRemarks;
    public String getPositionalTitleRemarks() {return dealValue(positionalTitleRemarks,"");}

    // 职务
    private String position;
    public String getPosition() {
        return dealValue(position,"");
    }

    // 岗位属性
    private String jobAttributes;
    public String getJobAttributes() {
        return dealValue(jobAttributes,"未选择");
    }

    // 荣誉
    private List<String> honor;
    public List<String> getHonor() {
        return honor != null ? honor : new ArrayList<>();
    }

    // 手机
    @Field("phone_num")
    private String phoneNum;
    public String getPhoneNum() {
        return dealValue(phoneNum,"");
    }

    // 邮箱
    private String email;
    public String getEmail() {
        return dealValue(email,"");
    }

    // 是否特级教师
    private String specialTeacher;
    public String getSpecialTeacher() {
        return dealValue(specialTeacher,"否");
    }

    // 省号
    private String shCode;
    private String shName;
    public String getShName() {
        return dealValue(shName,"");
    }

    // 地市号
    private String dsCode;
    private String dsName;
    public String getDsName() {
        return dealValue(dsName,"");
    }

    // 县区号
    private String xqCode;
    private String xqName;
    public String getXqName() {
        return dealValue(xqName,"");
    }

    // 街道号
    private String jdCode;
    private String jdName;
    public String getJdName() {
        return dealValue(jdName,"");
    }

    // 单位
    private String orgCode;
    private String orgName;
    public String getOrgName() {
        return dealValue(orgName,"");
    }


    // 学科1-5
    private String subject1;
    public String getSubject1() {
        return dealValue(subject1,"");
    }

    private String subject2;
    public String getSubject2() {
        return dealValue(subject2,"");
    }

    private String subject3;
    public String getSubject3() {
        return dealValue(subject3,"");
    }

    private String subject4;
    public String getSubject4() {
        return dealValue(subject4,"");
    }

    private String subject5;
    public String getSubject5() {
        return dealValue(subject5,"");
    }

    // 学段1-5
    private String period1;
    public String getPeriod1() {
        return dealValue(period1,"");
    }

    private String period2;
    public String getPeriod2() {
        return dealValue(period2,"");
    }

    private String period3;
    public String getPeriod3() {
        return dealValue(period3,"");
    }

    private String period4;
    public String getPeriod4() {
        return dealValue(period4,"");
    }

    private String period5;
    public String getPeriod5() {
        return dealValue(period5,"");
    }

    // 备注
    private String remark;
    public String getRemark() {
        return dealValue(remark,"");
    }

    private Integer workYear;
    public Integer getWorkYear() {
        return dealValue(workYear, 0);
    }


    private String birthMonth;
    public String getBirthMonth() {
        return dealValue(birthMonth,"");
    }

    // 毕业院校
    private String graduateSchool;
    public String getGraduateSchool() {
        return dealValue(graduateSchool,"");
    }

    // 任教专业
    private String teachingMajor;
    public String getTeachingMajor() {
        return dealValue(teachingMajor,"");
    }

    // 中职从教年限
    private Integer zzcjnx;
    public Integer getZzcjnx() {
        return dealValue(zzcjnx, 0);
    }

    private <T> T dealValue(T value, T defaultValue) {
        return value == null ? defaultValue : value;
    }

    private ResearcherZjDTO researcherToResearcherDTO(ResearcherZj researcherZj) {
        if (researcherZj == null) {
            return null;
        }

        try {
            ResearcherZjDTO dto = new ResearcherZjDTO();
            BeanUtils.copyProperties(researcherZj, dto);
            return dto;
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert Researcher to ResearcherDTO", e);
        }
    }

    public List<ResearcherZjDTO> researcherToResearcherDTO(List<ResearcherZj> researcherZjs) {
        return  researcherZjs.stream()
                .map(this::researcherToResearcherDTO)
                .collect(Collectors.toList());
    }

    public Page<ResearcherZjDTO> researcherToResearcherDTO(Page<ResearcherZj> researchersPage) {

        List<ResearcherZjDTO> dtos = this.researcherToResearcherDTO(researchersPage.getContent());

        return new PageImpl<>(dtos, researchersPage.getPageable(), researchersPage.getTotalElements());
    }


}
