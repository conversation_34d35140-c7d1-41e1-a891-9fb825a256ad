package cn.bctools.design.local.jyjgzj.excels;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

@Data
@Builder
public class StatDetailCity {

    @ExcelProperty(value = {"区域", "省份"}, index = 0)
    private String shName;

    @ExcelProperty(value = {"区域", "地市"}, index = 1)
    private String dsName;

    @ExcelProperty(value = {"区域", "县区"}, index = 2)
    private String xqName;

    @ExcelProperty(value = {"区域", "总数"}, index = 3)
    private Integer total;

    @ExcelProperty(value = {"岗位情况", "市级专职"}, index = 4)
    private Long sjzj;

    @ExcelProperty(value = {"岗位情况", "市级兼职"}, index = 5)
    private Long sjjj;

    @ExcelProperty(value = {"岗位情况", "县级专职"}, index = 6)
    private Long xjzj;

    @ExcelProperty(value = {"岗位情况", "县级兼职"}, index = 7)
    private Long xjjj;

    @ExcelProperty(value = {"岗位情况", "校级"}, index = 8)
    private Long xj;

    // 赋值数据中的岗位情况
    public void updateJobAttribute(List<HashMap> data) {
        Map<String, Long> defaultValues = new HashMap<>();
        defaultValues.put("市级专职", 0L);
        defaultValues.put("市级兼职", 0L);
        defaultValues.put("县级专职", 0L);
        defaultValues.put("县级兼职", 0L);
        defaultValues.put("校级", 0L);

        // 映射属性名到处理方法
        Map<String, BiConsumer<StatDetailCity, Long>> attrHandlers = new HashMap<>();
        attrHandlers.put("市级专职", StatDetailCity::setSjzj);
        attrHandlers.put("市级兼职", StatDetailCity::setSjjj);
        attrHandlers.put("县级专职", StatDetailCity::setXjzj);
        attrHandlers.put("县级兼职", StatDetailCity::setXjjj);
        attrHandlers.put("校级", StatDetailCity::setXj);

        // 应用默认值
        defaultValues.forEach((key, value) -> attrHandlers.get(key).accept(this, value));
        updateAttr(data, attrHandlers);
    }

    @ExcelProperty(value = {"职称情况", "高级"}, index = 9)
    private Long zcgj;

    @ExcelProperty(value = {"职称情况", "中级"}, index = 10)
    private Long zczj;

    @ExcelProperty(value = {"职称情况", "中级及以下"}, index = 11)
    private Long zczjjyx;

    // 赋值数据中的职称情况
    public void updatePositionalTitleRemarks(List<HashMap> data) {
        Map<String, Long> defaultValues = new HashMap<>();
        defaultValues.put("level1", 0L);
        defaultValues.put("level2", 0L);
        defaultValues.put("level3", 0L);

        // 映射属性名到处理方法
        Map<String, BiConsumer<StatDetailCity, Long>> attrHandlers = new HashMap<>();
        attrHandlers.put("level1", StatDetailCity::setZcgj);
        attrHandlers.put("level2", StatDetailCity::setZczj);
        attrHandlers.put("level3", StatDetailCity::setZczjjyx);

        // 应用默认值
        defaultValues.forEach((key, value) -> attrHandlers.get(key).accept(this, value));
        updateAttr(data, attrHandlers);
    }

    @ExcelProperty(value = {"学历情况", "博士研究生"}, index = 12)
    private Long xlbsyjs;

    @ExcelProperty(value = {"学历情况", "硕士研究生"}, index = 13)
    private Long xlssyjs;

    @ExcelProperty(value = {"学历情况", "大学本科"}, index = 14)
    private Long xldxbk;

    @ExcelProperty(value = {"学历情况", "大专及以下"}, index = 15)
    private Long xldzjyx;

    // 赋值数据中的职称情况
    public void updateHighestQualification(List<HashMap> data) {
        Map<String, Long> defaultValues = new HashMap<>();
        defaultValues.put("博士研究生", 0L);
        defaultValues.put("硕士研究生", 0L);
        defaultValues.put("大学本科", 0L);
        defaultValues.put("大专及以下", 0L);

        // 映射属性名到处理方法
        Map<String, BiConsumer<StatDetailCity, Long>> attrHandlers = new HashMap<>();
        attrHandlers.put("博士研究生", StatDetailCity::setXlbsyjs);
        attrHandlers.put("硕士研究生", StatDetailCity::setXlssyjs);
        attrHandlers.put("大学本科", StatDetailCity::setXldxbk);
        attrHandlers.put("大专及以下", StatDetailCity::setXldzjyx);

        // 应用默认值
        defaultValues.forEach((key, value) -> attrHandlers.get(key).accept(this, value));
        updateAttr(data, attrHandlers);
    }

    @ExcelProperty(value = {"年龄情况", "平均年龄"}, index = 16)
    private String pjnl;

    @ExcelProperty(value = {"年龄情况", "平均教龄"}, index = 17)
    private String pjjl;


    private <T> void updateAttr(List<HashMap> data, Map<String, BiConsumer<StatDetailCity, Long>> attrHandlers) {
        // 遍历并更新属性值
        data.forEach(attr -> {
            String key = (String) attr.get("name");
            Long value = (Long) attr.get("count");
            attrHandlers.getOrDefault(key, (sd, v) -> {
            }).accept(this, value);
        });
    }

    // 构建写入excel的列表
    public static List<Object> buildStatDetailList(List<Map> stats) {

        List<Object> list = new ArrayList<>();

        stats.forEach(stat -> {
            Map<String, Object> statDetail = (Map<String, Object>) stat;

            // 对每个Stat对象进行处理，这里只是一个示例操作
            StatDetailCity statDetail1 = StatDetailCity.builder()
                    .shName((String) statDetail.get("shName"))
                    .dsName((String) statDetail.get("dsName"))
                    .xqName((String) statDetail.get("xqName"))
                    .total((Integer) statDetail.get("total"))
                    .pjnl((String) statDetail.get("avgAge"))
                    .pjjl((String) statDetail.get("avgExperience"))
                    .build();

            statDetail1.updateJobAttribute((List<HashMap>) statDetail.get("jobAttributesStat"));
            statDetail1.updatePositionalTitleRemarks((List<HashMap>) statDetail.get("positionalTitleStat"));
            statDetail1.updateHighestQualification((List<HashMap>) statDetail.get("highestQualificationStat"));

            list.add(statDetail1);
        });

        return list;
    }


}
