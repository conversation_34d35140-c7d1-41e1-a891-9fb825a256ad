package cn.bctools.design.local.jyjgzj.service;

import cn.bctools.design.local.jyjgzj.entity.ResearcherZjDTO;
import cn.bctools.design.local.jyjgzj.excels.UserList;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * @Project: jvs-backend
 * @Package: cn.bctools.design.local.jyjg.service
 * @Description:
 * @Author: libo
 * @Email: <EMAIL>
 * @Date: 2024/5/9 21:46
 */
@Slf4j
@Service
public class ResearcherZjDetailExportService {

    @Autowired
    private ResearcherZjService researcherZjService;

    public void exportUserList(HttpServletResponse response, Map<String, String> criteria) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String filename = URLEncoder.encode(LocalDate.now() + " 教研员列表.xlsx",
                    StandardCharsets.UTF_8.toString()).replace("+", "%20");
            filename = new String(filename.getBytes("UTF-8"), "ISO-8859-1");
            response.setHeader("Content-Disposition", "attachment;filename=" + filename);
            response.setHeader("Content-Type", "application/octet-stream; charset=UTF-8");
            response.addHeader("DownloadFileName", URLEncoder.encode(filename, "utf-8"));

            ServletOutputStream outputStream = response.getOutputStream();

            // 创建ExcelWriter对象
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            // 创建Sheet对象
            WriteSheet writeSheet = EasyExcel.writerSheet("全部").build();
            // 设置excel模板寻求首行标题的类
            writeSheet.setClazz(UserList.class);
            // 向Excel中写入数据
            org.springframework.data.domain.Page<ResearcherZjDTO> researcherDTOPage = researcherZjService.findStatDetails(criteria);
            List<Object> data = UserList.buildStatDetailList(researcherDTOPage.getContent());
            excelWriter.write(data, writeSheet);
            // 关闭流
            excelWriter.finish();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
