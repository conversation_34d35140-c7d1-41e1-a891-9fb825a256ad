package cn.bctools.design.local.jyjgzj.service;

import cn.bctools.auth.api.api.AuthDeptServiceApi;
import cn.bctools.auth.api.dto.SysDeptDto;
import cn.bctools.design.crud.service.JvsTreeService;
import cn.bctools.design.local.jyjgzj.entity.ResearcherZj;
import cn.bctools.design.local.jyjgzj.entity.ResearcherZjDTO;
import cn.bctools.design.local.jyjgzj.repository.ResearcherZjRepository;
import cn.bctools.oauth2.utils.AuthorityManagementUtils;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Project: jvs-backend
 * @Package: cn.bctools.design.local.jyjg.service
 * @Description:
 * @Author: libo
 * @Email: <EMAIL>
 * @Date: 2024/5/9 21:46
 */
@Service
public class ResearcherZjService {
    @Autowired
    private ResearcherZjRepository researcherZjRepository;
    @Autowired
    private ResearcherZjStatService researcherZjStatService;
    @Autowired
    private ResearcherZjSnapshotService researcherZjSnapshotService;
    @Autowired
    JvsTreeService treeService;
    @Autowired
    AuthDeptServiceApi authDeptServiceApi;
    @Autowired
    MongoTemplate mongoTemplate;

    private static final Set<String> ALLOWED_FIELDS_DETAIL = Collections.unmodifiableSet(new HashSet<>(Arrays.asList(
            "shCode", "dsCode", "xqCode", "jdCode", "orgCode", "year", "quarter", "jobAttributes", "highestQualification", "specialTeacher"
    )));

    private String positionalTitle;
    private int page;
    private int pageSize;
    private int all;
    private int total = 0;

    public com.baomidou.mybatisplus.extension.plugins.pagination.Page getStatDetails(Map<String, String> criteria) {

        Page<Map> researcherStatPage = new PageImpl<>(this.getStatDetailObject(criteria));
        //转成com.baomidou.mybatisplus.extension.plugins.pagination.Page，和其他接口统一返回格式，1为第一页
        com.baomidou.mybatisplus.extension.plugins.pagination.Page page = com.baomidou.mybatisplus.extension.plugins.pagination.Page.of(this.page + 1, this.pageSize, this.total);
        page.setRecords(researcherStatPage.getContent());
        return page;
    }

    // 全部： type可以不传
    // 按市统计， type 传 ds, 可传 shCode
    // 市属。 type 传 ss
    // 各地市统计， type可以不传, 需传 dsCode
    public List<Map> getStatDetailObject(Map<String, String> criteria) {

        String type = extractAndRemoveString(criteria, "type", "all");
        int page = extractAndRemoveInt(criteria, "current", 1) - 1;
        int pageSize = extractAndRemoveInt(criteria, "size", 20);
        int all = extractAndRemoveInt(criteria, "all", 0);

        Query query = validateAndApplyCriteria(criteria);
        // 通用数据
        String currentUserDeptId = UserCurrentUtils.getDeptId();
        Integer deptLevel = UserCurrentUtils.getCurrentUser().getDeptLevel();

        if (deptLevel > 1) {
            List<String> childDeptIdList = AuthorityManagementUtils.getAllChildDeptIdList(currentUserDeptId);
            if (!childDeptIdList.isEmpty()) {
                query.addCriteria(Criteria.where("orgCode").in(childDeptIdList));
            }
        }
        List<ResearcherZj> researcherZjs = mongoTemplate.find(query, ResearcherZj.class);

        //获取各种字典的值
        Map<String, Map<String, String>> allAttributesMap = this.getAllAttributesMap(researcherZjs);
        //设置每个教研员的信息，将字典key翻译成字典值
        researcherZjs.parallelStream().forEach(r -> r.setAttributes(r, allAttributesMap, "OrgName", "OrgLevel", "PositionalTitleReMark"));

        Map<String, List<ResearcherZj>> researchers2 = researcherZjs
                .stream()
                .filter(r -> {
                    if (type.equals("ss")) {
                        if (r.getDsName().equals("东莞市") || r.getDsName().equals("中山市")) {
                            return r.getOrgLevel().equals("2");
                        }
                        return r.getXqCode().length() == 6 && r.getXqCode().endsWith("01");
                    } else {
                        return true;
                    }
                })
                .collect(Collectors.groupingBy(r -> {
                    // 根据type控制分组条件
                    switch (type) {
                        case "sh":
                            return r.getShCode();
                        case "ds":
                            return r.getShCode() + "-" + r.getDsCode();
                        case "xq":
                        case "ss":
                            return r.getShCode() + "-" + r.getDsCode() + "-" + r.getXqCode();
                        case "jd":
                            return r.getShCode() + "-" + r.getDsCode() + "-" + r.getXqCode() + "-" + r.getJdCode();
                        default:
                            return r.getOrgCode();
                    }
                }));

        if (all < 1) {
            total = researchers2.size();
            researchers2 = researchers2.entrySet().stream()
                    .skip((long) page * pageSize)
                    .limit(pageSize)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }
        List<Map> stats = new ArrayList<>();

        researchers2.forEach((key, value) -> {

            Map<String, Object> statDetails = new HashMap<>();

            statDetails.put("year", value.get(0).getYear());
            statDetails.put("quarter", value.get(0).getQuarter());
            statDetails.put("shCode", value.get(0).getShCode());
            statDetails.put("dsCode", value.get(0).getDsCode());
            statDetails.put("xqCode", value.get(0).getXqCode());
            statDetails.put("jdCode", value.get(0).getJdCode());
            statDetails.put("orgCode", value.get(0).getOrgCode());
            statDetails.put("orgName", value.get(0).getOrgName());
            statDetails.put("shName", value.get(0).getShName());
            statDetails.put("dsName", value.get(0).getDsName());
            statDetails.put("xqName", type.equals("ds") ? "全市" : value.get(0).getXqName());
            statDetails.put("jdName", value.get(0).getJdName());

            // 总数
            int total = value.size();
            statDetails.put("total", total);

            // 岗位属性
            List<HashMap<String, Object>> jobAttributesStat = researcherZjStatService.groupByFieldAndGetSum(value, ResearcherZj::getJobAttributes,
                    allAttributesMap.get("job_attributes"));
            statDetails.put("jobAttributesStat", jobAttributesStat);

            Map<String, String> honorList = new HashMap<>();
            honorList.put("省特级教师", "省特级教师");
            honorList.put("南粤优秀教师", "南粤优秀教师");
            honorList.put("南粤优秀教育工作者", "南粤优秀教育工作者");
            Map<String, Long> honorStat = new HashMap<>();
            honorList.forEach((k, v) -> honorStat.put(k, 0L));
            value.forEach(r -> {
                if (r.getHonor() != null) {
                    r.getHonor().forEach(h -> {
                        if (!h.isEmpty() && honorStat.containsKey(h)) {
                            honorStat.put(h, honorStat.get(h) + 1);
                        }
                    });
                }
            });
            statDetails.put("honorStat", researcherZjStatService.setDefaultValues(honorStat, honorList));

            // 职称
            List<HashMap<String, Object>> positionalTitleStat = researcherZjStatService.groupByPositionalTitleRemarksAndGetSum(value);
            statDetails.put("positionalTitleStat", positionalTitleStat);

            // 学历
            List<HashMap<String, Object>> highestQualificationStat = researcherZjStatService.groupByFieldAndGetSum(value, ResearcherZj::getHighestQualification,
                    allAttributesMap.get("education_background"));
            statDetails.put("highestQualificationStat", highestQualificationStat);

            DecimalFormat df = new DecimalFormat("#.##");

            //平均年龄
            IntSummaryStatistics ageStats = researcherZjStatService.groupByFieldAndGetAverage(value, ResearcherZj::getAge);
            Double avgAge = ageStats.getAverage();
            statDetails.put("avgAge", df.format(avgAge));

            //平均教龄
            value.forEach(r -> {if (r.getExperience() == null) {r.setExperience(0);}});
            IntSummaryStatistics experienceStats = researcherZjStatService.groupByFieldAndGetAverage(value, ResearcherZj::getExperience);
            Double avgExperience = experienceStats.getAverage();
            statDetails.put("avgExperience", df.format(avgExperience));

            // 特级教师数量
            Long tjjs = researcherZjStatService.countResearchersWithSpecialTeacher(value, "是");
            statDetails.put("tjjs", tjjs);

            stats.add(statDetails);

        });

        // 按 dsCode 升序
        return stats.stream()
                .sorted(Comparator.comparing(map -> {
                    HashMap<String, Object> map1 = (HashMap<String, Object>) map;
                    return (String) map1.get("dsCode");
                }))
                .collect(Collectors.toList());
    }

    public Page<ResearcherZjDTO> findStatDetails(Map<String, String> criteria) {

        int all = extractAndRemoveInt(criteria, "all", 0);
        //0为第一页
        int page = extractAndRemoveInt(criteria, "current", 1) - 1;
        int pageSize = extractAndRemoveInt(criteria, "size", 20);

        Query query = validateAndApplyCriteria(criteria);
        Pageable pageable = PageRequest.of(page, pageSize);
        long totalElements = mongoTemplate.count(query, ResearcherZj.class);
        if (all < 1) {
            query.with(pageable);
        }
        List<ResearcherZj> researcherZjRes = mongoTemplate.find(query, ResearcherZj.class);
        Page<ResearcherZj> pageresearchers = new PageImpl<>(researcherZjRes, pageable, totalElements);

        processFetchedResearchers(pageresearchers);

        return new ResearcherZjDTO().researcherToResearcherDTO(pageresearchers);
    }

    // 提取参数（null时赋予默认值）， 并移出请求body
    private int extractAndRemoveInt(Map<String, String> criteria, String key, int defaultValue) {
        String valueStr = criteria.remove(key);
        return valueStr != null ? Integer.parseInt(valueStr) : defaultValue;
    }

    // 提取参数（null时赋予默认值）， 并移出请求body
    private String extractAndRemoveString(Map<String, String> criteria, String key, String defaultValue) {
        String valueStr = criteria.remove(key);
        return valueStr != null ? valueStr : defaultValue;
    }

    // 构建查询条件
    public void validateAndApplyCriteria(Map<String, String> criteria, ResearcherZj researcherZj) {
        criteria.keySet().removeIf(key -> !ALLOWED_FIELDS_DETAIL.contains(key));
        criteria.forEach((key, value) -> {
            if (value != null) {
                try {
                    Method setterMethod = ResearcherZj.class.getMethod("set" + capitalizeFirstLetter(key), String.class);
                    setterMethod.invoke(researcherZj, value);
                } catch (Exception e) {
                    throw new IllegalArgumentException("Invalid set field: " + key, e);
                }
            }
        });
    }

    public Query validateAndApplyCriteria(Map<String, String> criteria) {
        String positionalTitleReMarkParam = extractAndRemoveString(criteria, "positionalTitle", "");

        criteria.keySet().removeIf(key -> !ALLOWED_FIELDS_DETAIL.contains(key));
        Query query = new Query();
        if (!positionalTitleReMarkParam.isEmpty()) {
            Map<String, String> positionalTitleReMarkMap = this.getPositionalTitleByLevel(positionalTitleReMarkParam);
            Criteria criteriaPositionalTitle = new Criteria();
            // 筛选空值
            if (positionalTitleReMarkParam.equals("中级以下")) {
                criteriaPositionalTitle.orOperator(
                        Criteria.where("positionalTitle").in(positionalTitleReMarkMap.keySet()),
                        Criteria.where("positionalTitle").isNull(),
                        Criteria.where("positionalTitle").is("")
                );
            }  else {
                criteriaPositionalTitle.orOperator(
                        Criteria.where("positionalTitle").in(positionalTitleReMarkMap.keySet())
                );
            }
            query.addCriteria(criteriaPositionalTitle);
        }

        // 构建查询条件
        criteria.forEach((key, value) -> {
            if (ALLOWED_FIELDS_DETAIL.contains(key)) {
                query.addCriteria(Criteria.where(key).is(value));
            }
        });
        return query;
    }

    // 首字母大写
    private String capitalizeFirstLetter(String str) {
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    // 处理返回结果，赋予默认值
    private void processFetchedResearchers(Page<ResearcherZj> pageresearchers) {

        Map<String, Map<String, String>> allAttributesMap = this.getAllAttributesMap(pageresearchers.getContent());


        pageresearchers.getContent().parallelStream().forEach(r ->
                r.setAttributes(r, allAttributesMap,
                        "JobAttributes", "HighestQualification", "HighestDegree",
                        "Subject1", "Subject2", "Subject3", "Subject4", "Subject5",
                        "Period1", "Period2", "Period3", "Period4", "Period5",
                        "PositionalTitle", "OrgName", "Sex", "majorBackground", "position",
                        "graduateSchool", "teachingMajor"
                )
        );
    }

    // 获取所有属性
    public Map<String, Map<String, String>> getAllAttributesMap() {
        Map<String, String> allJobAttributes = treeService.getTreeNameByType("vocational_job_attributes");
        Map<String, String> allHighestQualification = treeService.getTreeNameByType("education_background");
        Map<String, String> allDegree = treeService.getTreeNameByType("degree");
        Map<String, String> allSubject = treeService.getTreeNameByType("basic_edu_subject");
        Map<String, String> allPeriod = treeService.getTreeNameByType("basic_edu_period");
        Map<String, String> allSex = treeService.getTreeNameByType("sex");
        Map<String, String> allMajorBackground = treeService.getTreeNameByType("major_background");
        Map<String, String> allPosition = treeService.getTreeNameByType("position");
        Map<String, String> allPositionalTitle = treeService.getTreeRemarkByType("positional_title");
        // 毕业院校
        Map<String, String> allSchool = treeService.getTreeNameByType("school");
        // 任教专业
        Map<String, String> allMajor = treeService.getTreeNameByType("major");

        Map<String, Map<String, String>> allAttributesMap = new HashMap<>();
        allAttributesMap.put("job_attributes", allJobAttributes);
        allAttributesMap.put("education_background", allHighestQualification);
        allAttributesMap.put("degree", allDegree);
        allAttributesMap.put("subject", allSubject);
        allAttributesMap.put("period", allPeriod);
        allAttributesMap.put("positional_title", allPositionalTitle);
        allAttributesMap.put("sex", allSex);
        allAttributesMap.put("major_background", allMajorBackground);
        allAttributesMap.put("position", allPosition);
        allAttributesMap.put("school", allSchool);
        allAttributesMap.put("major", allMajor);

        return allAttributesMap;
    }

    // 根据级别筛选职称
    private Map<String, String> getPositionalTitleByLevel(String level) {
        Map<String, String> allPositionalTitleRes = new HashMap<>();

        Map<String, String> allPositionalTitle = treeService.getTreeRemarkByType("positional_title");
        allPositionalTitle.forEach((key, value) -> {
            if (value == null) {
                value = "中级以下";
            }
            if (level.equals("中级以下")) {
                if (!value.equals("高级") && !value.equals("中级")) {
                    allPositionalTitleRes.put(key, value);
                }
            } else if (value.equals(level)) {
                allPositionalTitleRes.put(key, value);
            }

        });
        return allPositionalTitleRes;
    }

    // 获取所有属性
    public Map<String, Map<String, String>> getAllAttributesMap(List<ResearcherZj> researcherZjs) {
        Map<String, Map<String, String>> allAttributesMap = this.getAllAttributesMap();

        // 先查出所有orgCode， 然后查出对应的单位名
        List<String> allOrgCode = researcherZjs.stream().map(ResearcherZj::getOrgCode).distinct().collect(Collectors.toList());
        Map<String, String> allOrgs = new HashMap<>();
        Map<String, String> allOrgLevel = new HashMap<>();
        if (!allOrgCode.isEmpty()) {
            List<SysDeptDto> sysDeptDtoList = authDeptServiceApi.getByIds(allOrgCode).getData();
            sysDeptDtoList.stream().forEach(e -> {
                allOrgs.put(e.getId(), e.getName());

                if (e.getLevel() == null) {e.setLevel(1);}
                allOrgLevel.put(e.getId(), e.getLevel().toString());
            });
            allAttributesMap.put("allOrgs", allOrgs);
            allAttributesMap.put("allOrgLevel", allOrgLevel);
        }
        return allAttributesMap;
    }

    public Map<String, Object> getResearcherStatReport(Map<String, String> criteria) {
        String year = extractAndRemoveString(criteria, "year", "0");
        String quarter = extractAndRemoveString(criteria, "quarter", "0");

        ResearcherZj tmpResearcherZj = new ResearcherZj();
        tmpResearcherZj.setYear(year);
        tmpResearcherZj.setQuarter(quarter);
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withIgnorePaths("_class"); // 忽略_class字段
        Example<ResearcherZj> example = Example.of(tmpResearcherZj, exampleMatcher);

        List<ResearcherZj> researcherZjs = researcherZjRepository.findAll(example);

        Map<String, Map<String, String>> allAttributesMap = this.getAllAttributesMap(researcherZjs);
        //设置每个教研员的信息，将字典key翻译成字典值
        researcherZjs.parallelStream().forEach(r ->
                r.setAttributes(r, allAttributesMap,
                        "OrgName", "OrgLevel", "PositionalTitleReMark"));

        Map<String, Object> report = new HashMap<>();

        // 基本标准
        int bp = 43;
        // 最低标准
        int lp = 20;
        report.put("basePip", bp);
        report.put("lowPip", lp);

        // 按orglevel分组计算数量
        Map<String, String> orgLevelList = new HashMap<>();
        orgLevelList.put("1", "province");
        orgLevelList.put("2", "city");
        orgLevelList.put("3", "area");
        orgLevelList.put("4", "street");

        // 获取所有orgCode
        List<String> allOrgCode = researcherZjs.stream().map(ResearcherZj::getOrgCode).distinct().collect(Collectors.toList());
        // 根据orgCode分组，获取orgLevel数量
        Map<String, Long> orgLevelCount = allOrgCode.stream()
                .collect(Collectors.groupingBy(
                        orgCode -> allAttributesMap.get("allOrgLevel").get(orgCode),
                        Collectors.counting()
                ));
        List<HashMap<String, Object>> orgLevelStats = researcherZjStatService
                .setDefaultValues(orgLevelCount,orgLevelList);
        // 机构类型
        report.put("org_type",orgLevelStats);


        // 按orgCode分组计算数量
        Map<String, Long> orgLevelCount2 = researcherZjs.stream().collect(Collectors.groupingBy(ResearcherZj::getOrgCode, Collectors.counting()));
        report.put("orgLevelCount2",orgLevelCount2);

        Long fitBp = orgLevelCount2.entrySet().stream().filter(e -> e.getValue() >= bp).count();
        Long fitLow = orgLevelCount2.entrySet().stream().filter(e -> e.getValue() >= lp).count();
        Long unFitLow = orgLevelCount2.entrySet().stream().filter(e -> e.getValue() < lp).count();
        // 达到基本标准
        report.put("fitBp",fitBp);
        // 达到最低标准
        report.put("fitLow",fitLow);
        // 未达到最低标准
        report.put("unFitLow",unFitLow);

        // 地级以上市教研员编制
        Long dsJyyNum = researcherZjs.stream().filter(r -> Integer.parseInt(r.getOrgLevel()) <= 2).count();
        report.put("dsJyyNum",dsJyyNum);
        
        // 机构总数
        Long orgLevelSum = orgLevelCount.values().stream().mapToLong(Long::longValue).sum();
        report.put("org_num",orgLevelSum);

        // 按岗位属性分组
        Map<String, Long> jobAttributesCount = researcherZjs.stream()
                .collect(Collectors.groupingBy(
                        ResearcherZj::getJobAttributes,
                        Collectors.counting()
                ));
        List<HashMap<String, Object>> jobAttributesStats = researcherZjStatService
                .setDefaultValues(jobAttributesCount, allAttributesMap.get("job_attributes"));
        // 编制数量
        report.put("jobAttributes", jobAttributesStats);
        // 总人数
        report.put("total", researcherZjs.size());

        // 对比上一年的数据
        Integer lastTotal = getLastQuarterResearcherNum(year, quarter);
        report.put("lastTotal", lastTotal);

        // 在岗数量
        Map<String, List<ResearcherZj>> researchers2 = researcherZjs
                .stream()

                .collect(Collectors.groupingBy(ResearcherZj::getOrgLevel));

        Map<String, Object> reportTmp = new HashMap<>();
        researchers2.forEach((k, v) -> {
            // 按岗位属性分组
            List<HashMap<String, Object>> jobAttributesTmpStats = researcherZjStatService
                    .groupByFieldAndGetSum(v, ResearcherZj::getJobAttributes, allAttributesMap.get("job_attributes"));
            reportTmp.put(orgLevelList.get(k), jobAttributesTmpStats);
        });
        report.put("jobAttributesByOrgLevel", reportTmp);

        // 职称
        List<HashMap<String, Object>> positionalTitleStat = researcherZjStatService.groupByPositionalTitleRemarksAndGetSum(researcherZjs);
        report.put("positionalTitleStat", positionalTitleStat);

        // 学历
        List<HashMap<String, Object>> highestQualificationStat = researcherZjStatService.
                groupByFieldAndGetSum(researcherZjs, ResearcherZj::getHighestQualification, allAttributesMap.get("education_background"));
        report.put("highestQualificationStat", highestQualificationStat);

        // 学位
        List<HashMap<String, Object>> highestDegreeStat = researcherZjStatService.
                groupByFieldAndGetSum(researcherZjs, ResearcherZj::getHighestDegree, allAttributesMap.get("degree"));
        report.put("highestDegreeStat", highestDegreeStat);

        researcherZjs.forEach(r -> {
            if (r.getSpecialTeacher() == null || r.getSpecialTeacher().isEmpty()) {
                r.setSpecialTeacher("否");
            }
        });

        // 特级教师
        Map<String, String> specialTeacherList = new HashMap<>();
        specialTeacherList.put("是", "1");
        specialTeacherList.put("否", "0");
        List<HashMap<String, Object>> specialTeacherStat = researcherZjStatService.
                groupByFieldAndGetSum(researcherZjs, ResearcherZj::getSpecialTeacher, specialTeacherList);
        report.put("specialTeacherStat", specialTeacherStat);

        // 第一学科
        List<HashMap<String, Object>> subjectStat = researcherZjStatService.
                groupByFieldAndGetSum(researcherZjs, ResearcherZj::getSubject1, allAttributesMap.get("subject"));

        // 特殊教育
        List<Map<String, Object>> specialSubject = new ArrayList<>();
        List<Map<String, Object>> subjectStat2 = new ArrayList<>();
        subjectStat.forEach(s -> {
            if (s.get("nameKey").equals("935929074134999753")
                    || s.get("nameKey").equals("935929074133719731")
                    || s.get("nameKey").equals("935929074134999295")) {
                specialSubject.add(s);
            } else {
                subjectStat2.add(s);
            }
        });

        report.put("subjectStat", subjectStat2);
        report.put("specialSubject", specialSubject);

        // 第一学科学段
        List<HashMap<String, Object>> periodStat = researcherZjStatService.
                groupByFieldAndGetSum(researcherZjs, ResearcherZj::getPeriod1, allAttributesMap.get("period"));
        report.put("periodStat", periodStat);

        // 根据中小学从教年限按指定区间统计
        List<HashMap<String, Object>> zxxcjnxStat = researcherZjStatService.groupByZxxcjnx(researcherZjs);
        report.put("zxxcjnxStat", zxxcjnxStat);


        // 根据担任教员年限
        List<HashMap<String, Object>> drjynxStat = researcherZjStatService.groupByDyjynx(researcherZjs);
        report.put("drjynxStat", drjynxStat);

        return  report;
    }


    public Integer getLastQuarterResearcherNum(String year, String quarter) {
        // 获取上一季度
        if (quarter.equals("1")) {
            quarter = "4";
            year = String.valueOf(Integer.parseInt(year) - 1);
        } else {
            quarter = String.valueOf(Integer.parseInt(quarter) - 1);
        }
        ResearcherZj tmpResearcherZj = new ResearcherZj();
        tmpResearcherZj.setYear(year);
        tmpResearcherZj.setQuarter(quarter);
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withIgnorePaths("_class"); // 忽略_class字段
        Example<ResearcherZj> example = Example.of(tmpResearcherZj, exampleMatcher);

        List<ResearcherZj> researcherZjs = researcherZjRepository.findAll(example);

        return researcherZjs.size();
    }


}
