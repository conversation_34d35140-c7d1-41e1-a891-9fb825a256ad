package cn.bctools.design.local.kcjx.constant;

public interface Constant {
    String PROJECT_COLLECTION_NAME = "1803245626576650242";
    String REPORT_COLLECTION_NAME = "1803245077135409154";
    String DATA_SOURCE_COLLECTION_NAME = "1805068685195350017";
    String DATA_SOURCE_TITLE_COLLECTION_NAME = "1826194516095873026";
    String DATA_SOURCE_ANSWER_COLLECTION_NAME = "1807966647328182273";
    String INDICATOR_CONTENT_COLLECTION_NAME = "1803607391625854978";
    String ANALYSIS_MODEL_COLLECTION_NAME = "1803245472918323202";
    String DATA_CLEAN = "1826464285131845633";
    String DATA_CLEAN_QUESTION = "1831536296354951170";
    String DATA_CLEAN_RULE = "1831136839067021313";

    String PROCESSED_DATA = "1803245349366710273";
    // 数据处理_问卷关联设置 （单份问卷）
    String PROCESSED_DATA_SINGLE = "1826441722708144130";

    // 样本
    String DATA_EXAMPLE = "1843479982918676481";

    // 变量设置
    String VARIABLE_SETTING = "1854337648830263297";

    String APP_ID = "80bcedc53ecaca2c6bb7c3f842738625";

}
