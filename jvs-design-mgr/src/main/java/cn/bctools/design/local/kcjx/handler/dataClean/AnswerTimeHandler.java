package cn.bctools.design.local.kcjx.handler.dataClean;

import cn.bctools.design.local.kcjx.constant.DataCleanType;
import cn.bctools.design.local.kcjx.entity.DataCleanResultTemp;
import cn.bctools.design.local.kcjx.entity.DataSourceAnswer;
import org.springframework.data.domain.Page;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

public class AnswerTimeHandler extends DataCleanHandler {

    public AnswerTimeHandler(DataCleanHandler handler) {
        super(handler);
    }

    @Override
    public Map<String, Object> clean() {
        Map<Integer, Integer> stat = new HashMap<>();
            AtomicReference<Integer> errorNum = new AtomicReference<>(0);
            List<String> errorIds = new ArrayList<>();
            for (int i = 0; i <= this.pageNum; i++) {
                Page<DataSourceAnswer> page = kcjxDataSourceAnswerService.getAnswerPage(this.cleanId, this.dataType, this.currentVersion, i, this.pageSize);
                page.getContent().forEach(x -> {
                    long duration = x.getAnswerSeconds();
                    if (duration > 0) {
                        double m = (duration / 60000) % 60;
                        double s = (double) duration / 1000;
                        if (s <= 60) {
                            stat.put(0, stat.getOrDefault(0, 0) + 1);
                        } else if (s < (60 * 30)) {
                            // 判断余数是否为0
                            if (s % 60 > 0) {
                                stat.put((int) m, stat.getOrDefault((int) m, 0) + 1);
                            } else {
                                stat.put((int) (m - 1), stat.getOrDefault((int) (m - 1), 0) + 1);
                            }
                        } else if (s <= (60 * 60)) {
                            stat.put(30, stat.getOrDefault(30, 0) + 1);
                        } else if (s <= (60 * 60 * 24)) {
                            stat.put(31, stat.getOrDefault(31, 0) + 1);
                        } else {
                            stat.put(32, stat.getOrDefault(32, 0) + 1);
                        }
                    } else {
                        errorNum.getAndSet(errorNum.get() + 1);
                        errorIds.add(x.getId());
                    }
                });
            }

            List<Map<String, String>> rtnList = new ArrayList<>();
            for (int i = 0; i <= 32; i++) {
                double rate = 0;
                if (stat.getOrDefault(i,0) != 0) {
                    rate = (double) stat.getOrDefault(i, 0) / this.totalRecords;
                    rate = rate * 100;
                }
                Map<String, String> tmp1 = new HashMap<>();
                tmp1.put("index", String.valueOf(i));
                tmp1.put("value", String.valueOf(stat.getOrDefault(i, 0)));

                if (rate > 0) {
                    tmp1.put("rate", String.format("%.2f", rate)+ "%");
                } else {
                    tmp1.put("rate", "0");
                }
                rtnList.add(tmp1);
            }

            Map<String, String> errorMap = new HashMap<>();
            errorMap.put("index", String.valueOf(-1));
            errorMap.put("value", String.valueOf(errorNum.get()));
            if (errorNum.get() > 0) {
                double rate = ((double) errorNum.get() / this.totalRecords) * 100;
                errorMap.put("rate", String.format("%.2f", rate) + "%");
            } else {
                errorMap.put("rate", "0");
            }
            rtnList.add(errorMap);

            this.rtn.put("data", rtnList);
            DataCleanResultTemp resTemp = new DataCleanResultTemp();
            resTemp.setCleanId(this.cleanId);
            resTemp.setTempId(this.tempId);
            resTemp.setType(DataCleanType.ANSWERTIME.getType());
            resTemp.setResult(rtnList);
            resTemp.setErrorIds(errorIds);
            mongoTemplate.save(resTemp, "kcjx_data_clean_res_temp");
            return this.rtn;
    }
}
