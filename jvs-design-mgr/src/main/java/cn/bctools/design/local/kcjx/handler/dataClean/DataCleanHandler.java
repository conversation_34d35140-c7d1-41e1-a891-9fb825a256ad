package cn.bctools.design.local.kcjx.handler.dataClean;

import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.design.local.kcjx.constant.DataCleanType;
import cn.bctools.design.local.kcjx.entity.DataClean;
import cn.bctools.design.local.kcjx.entity.DataCleanSetting;
import cn.bctools.design.local.kcjx.entity.DataSourceTitle;
import cn.bctools.design.local.kcjx.service.*;
import cn.bctools.design.local.kcjx.vo_new.DataCleanVo;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.*;

@Data
public class DataCleanHandler {


    public final KcjxDataSourceAnswerService kcjxDataSourceAnswerService = SpringContextUtil.getBean(KcjxDataSourceAnswerService.class);

    public final KcjxDataSourceTitleService kcjxDataSourceTitleService = SpringContextUtil.getBean(KcjxDataSourceTitleService.class);

    public final KcjxDataCleanService kcjxDataCleanService = SpringContextUtil.getBean(KcjxDataCleanService.class);

    public final KcjxDataCleanQuestionService kcjxDataCleanQuestionService = SpringContextUtil.getBean(KcjxDataCleanQuestionService.class);

    public final KcjxDataCleanRuleService kcjxDataCleanRuleService = SpringContextUtil.getBean(KcjxDataCleanRuleService.class);

    String dataType = "2";

    private DataCleanHandler rootHandler;

    protected String sourceId;

    protected String cleanId;

    protected DataClean dataClean;

    protected DataCleanType dataCleanType;

    protected DataCleanVo dataCleanVo;

    protected Map<String, Object> rtn = new HashMap<>();

    protected Long totalRecords = 0L;

    protected Integer pageNum = 0;

    protected Integer pageSize = 5000;

    protected Integer currentVersion;

    protected String tempId;

    public final MongoTemplate mongoTemplate = SpringContextUtil.getBean(MongoTemplate.class);


    public DataCleanHandler(DataCleanVo dataCleanVo) {
        this.dataCleanVo = dataCleanVo;
        this.sourceId = dataCleanVo.getSourceId();
        this.cleanId = dataCleanVo.getCleanId();
        this.dataCleanType = dataCleanVo.getCleanType();
        this.init(dataCleanVo);
    }

    public DataCleanHandler(DataCleanHandler handler) {
        this.rootHandler = handler;
        this.init(handler.dataCleanVo);
    }

    public DataCleanHandler init(DataCleanVo dataCleanVo) {
        // 随机生成一段字符串
        this.tempId = UUID.randomUUID().toString();
        this.rtn.put("tempId", this.tempId);
        this.dataCleanVo = dataCleanVo;
        this.sourceId = dataCleanVo.getSourceId();
        this.cleanId = dataCleanVo.getCleanId();
        this.dataCleanType = dataCleanVo.getCleanType();
        this.dataClean = kcjxDataCleanService.getCleanInfo(this.getDataCleanVo().getCleanId());

        Integer newVersion = kcjxDataCleanRuleService.getNewVersion(this.cleanId);
        this.currentVersion = newVersion - 1;

        long answerNum = kcjxDataSourceAnswerService.getAnswerNum(this.getDataCleanVo().getCleanId(), this.dataType, this.currentVersion);
        if (answerNum == 0) {
            throw new RuntimeException("没有需要清理的答案");
        }
        this.totalRecords = answerNum;
        // 获取页数
        this.pageNum = (Integer) (int) Math.ceil(answerNum / this.pageSize);
        return this;
    }

    public Map<String, Object> cleanData() {
        switch (this.dataCleanType) {
            case SCREEN:
                return new ScreenHandler(this).clean();
            case ANSWERTIME:
                return new AnswerTimeHandler(this).clean();
            case OPTION:
                return new OptionCodeHandler(this).clean();
            case JUMPCHECK:
                return new JumpCheckHandler(this).clean();
            case SAMEOPTION:
                return new SameOptionHandler(this).clean();
            case MISSOPTION:
                return new MissOptionHandler(this).clean();
            default:
                throw new RuntimeException("暂不支持该数据清洗类型");
        }
    }

    public Map<String, Object> clean() {
        return this.rtn;
    }

    public boolean checkIsValid(String type, Map<String, Boolean> validMap) {
        if (type.equals("and")) {
            return validMap.values().stream().allMatch(x -> x);
        } else if (type.equals("or")) {
            return validMap.values().stream().anyMatch(x -> x);
        }
        return false;
    }

    // 检查答案是否正确
    public boolean checkAnswer(Map<String, String> answerMap, DataCleanSetting.DataCleanSettingData data, DataSourceTitle question) {
        String answer = answerMap.get(question.getQuestionId());

        Map<String, String> optionMap = (Map<String, String>) question.getOriginOptionIndex();

        switch (data.getQuestionType()) {
            case "选中":
                if (answer != null) {
                    if (optionMap.containsKey(data.getQuestionContain())) {
                        return answer.equals(optionMap.get(data.getQuestionContain()));
                    }
                }
                break;
            case "未选中":
                if (answer != null) {
                    if (optionMap.containsKey(data.getQuestionContain())) {
                        return !answer.equals(optionMap.get(data.getQuestionContain()));
                    }
                }
                break;
            case "已答":
                return answer != null && !answer.isEmpty();
            case "未答":
                return answer == null || answer.isEmpty();
        }

        return false;
    }

    // 检查分组的答案是否正确
    public Map<String, Boolean> checkGroupAnswer(Map<String, String> answerMap, List<DataCleanSetting> children, Map<String, DataSourceTitle> titles) {

        Map<String, Boolean> finalValidMap = new HashMap<>();
        children.forEach(data -> {
            Map<String, Boolean> validMap = new HashMap<>();
            data.getData().forEach(data2 -> {
                DataSourceTitle question = titles.get(data2.getQuestionName());
                if (question != null) {
                    validMap.put(data2.getQuestionName(), checkAnswer(answerMap, data2, question));
                }
            });
            if (data.getChildren().size() > 0) {
                Map<String, Boolean> childValidMap = checkGroupAnswer(answerMap, data.getChildren(), titles);
                validMap.putAll(childValidMap);
            }
            finalValidMap.put(data.getId(), checkIsValid(data.getType(), validMap));
        });
        return finalValidMap;
    }


    // 获取所有子级的题目id
    public void getChildrenTitleIds(DataCleanSetting data, List<String> titleIds) {
        if (!data.getChildren().isEmpty()) {
            data.getChildren().forEach(x -> {
                x.getData().forEach(y -> {
                    if (!titleIds.contains(y.getQuestionName())) {
                        titleIds.add(y.getQuestionName());
                    }
                });
                getChildrenTitleIds(x, titleIds);
            });
        }
    }

}
