package cn.bctools.design.local.kcjx.handler.questions;

import cn.bctools.design.local.kcjx.handler.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.Streams;
import lombok.Data;

import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.bctools.design.local.kcjx.handler.SchemaHelper.trimHtmlTag;

/**
 * <AUTHOR>
 * @date 2023/8/13
 */
@Data
public class QuestionHandler {

    public static final String openidColumnName = "自定义字段";

    private QuestionHandler rootHandler;

    protected SurveySchema schema;

    AnswerView answerInfo;

    /**
     * 当前答案行索引
     */
    private long rowIndex;

    private int rowStartIndex;

    List<QuestionHandler> handlerList;

    DownloadDataTypeEnum dataType = DownloadDataTypeEnum.text;

    /**
     * 用于数据导入，标题和文本答案的映射
     */
    Map<String, Object> questionTitle2text;

    /**
     * 答案
     */
    LinkedHashMap<String, Object> answer;

    /**
     * 保存问题id和列索引的映射
     */
    Map<String, Integer> qId2ColumnIndexMapper = new LinkedHashMap<>();

    /**
     * 问题id和答案的映射
     */
    Map<String, Object> qId2Text;


    public QuestionHandler() {

    }

    public QuestionHandler(SurveySchema schema) {
        this.schema = schema;
        this.init(schema);
    }

    public QuestionHandler(SurveySchema schema, DownloadDataTypeEnum dataType) {
        this.dataType = dataType;
        this.schema = schema;
        this.init(schema);
    }

    protected QuestionHandler(QuestionHandler handler) {
        this.rootHandler = handler;
    }

    public QuestionHandler init(SurveySchema schema) {
        this.schema = schema;
        this.handlerList = SchemaHelper.flatSurveySchema(this.schema).stream().map(x -> {
            QuestionHandler handler = new QuestionHandler(this);
            switch (x.getType()) {
                case FillBlank:
                case MultipleBlank:
                case HorzBlank:
                case Textarea:
                case Score:
                case Nps:
                    handler = new FillBlankHandler(this);
                    break;
                case Sort:
                    handler = new SortHandler(this);
                    break;
                case Radio:
                case Select:
                    handler = new RadioHandler(this);
                    break;
                case Checkbox:
                    handler = new CheckboxHandler(this);
                    break;
                case Cascader:
                    handler = new CascaderHandler(this);
                    break;
                case MatrixAuto:
                    handler = new MatrixAutoHandler(this);
                    break;
                case Upload:
                case Signature:
                case Media:
                    handler = new UploadHandler(this);
                    break;
                case Dept:
                    handler = new DeptHandler(this);
                    break;
                case User:
                    handler = new UserHandler(this);
                    break;
                case MatrixRadio:
                    handler = new MatrixRadioHandler(this);
                    break;
                case MatrixCheckbox:
                    handler = new MatrixCheckboxHandler(this);
                    break;
                case MatrixFillBlank:
                case MatrixNps:
                    handler = new MatrixFillBlankHandler(this);
                    break;
                case Address:
                    handler = new AddressHandler(this);
                    break;
                case RichText:
                    handler = new RichTextHandler(this);
                    break;
            }
            handler.setSchema(x);
            return handler;
        }).collect(Collectors.toList());

        return this;
    }

    /**
     * 获取当前问题的答案
     *
     * @return
     */
    protected Object getCurrentQuestionAnswer() {
        if (this.rootHandler.answerInfo != null) {
            return ((Map<String, Object>) this.rootHandler.answerInfo.getAnswer()).get(this.schema.getId());
        }
        if (this.rootHandler.getAnswer() != null) {
            return this.rootHandler.getAnswer().get(this.schema.getId());
        }
        return null;
    }

    public LinkedHashMap<String, Double> questionScore() {
        LinkedHashMap<String, Double> questionScore = Optional
                .ofNullable(Optional.ofNullable(this.rootHandler.answerInfo.getExamInfo())
                        .orElse(new AnswerExamInfo())
                        .getQuestionScore())
                .orElse(new LinkedHashMap<>());
        return questionScore;
    }

    /**
     * 获取导出变量的行头，参考问卷网的格式。
     *
     * @return
     */
    public List<String> getCodeHeader() {
        if (this.handlerList != null) {
            List<String> result = new ArrayList<>();
//            result.add("序号");
            // 变量
            result.addAll(this.handlerList.stream()
                    .map(x -> x.getCodeHeader())
                    .reduce(new ArrayList<String>(), (prev, curr) -> {
                        prev.addAll(curr);
                        return prev;
                    }));
            // 通用 header
//            List<String> metaHeaders = getCommonHeaders();
//            result.addAll(metaHeaders);
            return result;
        }
        String questionIndex = "Q" + this.getCurrentOrder();
        if (this.schema.getChildren().size() == 1) {
            return Collections.singletonList(questionIndex);
        }
        return Streams.mapWithIndex(this.schema.getChildren().stream(), (e, i) -> questionIndex + "|" + (i + 1))
                .collect(Collectors.toList());

    }

    public List<String> getTextHeader() {
        if (this.handlerList != null) {
            List<String> result = new ArrayList<>();
            result.addAll(this.handlerList.stream()
                    .map(x -> x.getTextHeader())
                    .reduce(new ArrayList<String>(), (prev, curr) -> {
                        prev.addAll(curr);
                        return prev;
                    }));
            return result;
        }
        String title = this.schema.getTitle();
        return Collections.singletonList(title);
    }

    /**
     * 导出变量，由于变量是按顺序导出的，所以每个格子不能跳过
     *
     * @return
     */
    public List getRowCode() {
        List rowData = new ArrayList();
        if (this.handlerList != null) {
//            rowData.add(this.rowIndex + 1);
            // 表示是根 schema
            rowData
                    .addAll(this.handlerList.stream().map(x -> x.getRowCode()).reduce(new ArrayList<>(), (prev, curr) -> {
                        prev.addAll(curr);
                        return prev;
                    }));
//            getCommonRow(rowData);
        }
        return rowData;
    }

    /**
     * 获取导出模式为 text 时的行头
     *
     * @return
     */
    public Map<String, Integer> createExcelTextHeader() {
        if (this.handlerList != null) {
            List<SurveySchema> schemaDataTypes = SchemaHelper.flatSurveySchema(schema);
            boolean shouldMerge = shouldMergeHeader();
            int columnIndex = 0;
            columnIndex++;
            // 判断是否需要合并单元格
            String[] chnIndex = {"一", "二", "三", "四", "五"};
            for (SurveySchema qSchema : schemaDataTypes) {
                String qId = qSchema.getId();
                if (shouldMerge) {
                    if (qSchema.getType() == SurveySchema.QuestionType.MultipleBlank
                            || qSchema.getType() == SurveySchema.QuestionType.Cascader
                            || qSchema.getType() == SurveySchema.QuestionType.Address
                            || qSchema.getType() == SurveySchema.QuestionType.HorzBlank) {
                        if (qSchema.getType() == SurveySchema.QuestionType.Address) {
                            for (int i = 1; i <= 3; i++) {
                                qId2ColumnIndexMapper.put(qId + "_" + i, columnIndex);
                                columnIndex++;
                            }
                        } else {
                            int optionIndex = 1;
                            for (SurveySchema child : qSchema.getChildren()) {
                                qId2ColumnIndexMapper.put(qId + "_" + child.getId(), columnIndex);
                                columnIndex++;
                                optionIndex++;
                            }
                        }

                    } else if (qSchema.getType() == SurveySchema.QuestionType.MatrixFillBlank) {
                        // 矩阵填空
                        for (SurveySchema.Row row : qSchema.getRow()) {
                            for (SurveySchema option : qSchema.getChildren()) {
                                // 添加
                                qId2ColumnIndexMapper.put(qId + "_" + row.getId() + "_" + option.getId(), columnIndex);
                                columnIndex++;
                            }
                        }
                    } else if (qSchema.getType() == SurveySchema.QuestionType.MatrixRadio
                            || qSchema.getType() == SurveySchema.QuestionType.MatrixCheckbox
                            || qSchema.getType() == SurveySchema.QuestionType.MatrixNps) {
                        // 矩阵单选/矩阵多选
                        for (SurveySchema.Row row : qSchema.getRow()) {
                            qId2ColumnIndexMapper.put(qId + "_" + row.getId(), columnIndex);
                            columnIndex++;
                        }
                    } else if (qSchema.getType() == SurveySchema.QuestionType.Radio) {
                        // 单选 添加标题列
                        qId2ColumnIndexMapper.put(qId, columnIndex);
                        columnIndex++;
                        // 选项里面的填空
                        // 多项填空合并列
                        int i = 0;
                        for (SurveySchema option : qSchema.getChildren()) {
                            if (CollUtil.isNotEmpty(option.getChildren()) &&
                                    option.getAttribute().getDataType() == SurveySchema.DataType.horzBlank) {
                                for (SurveySchema subOption : option.getChildren()) {
                                    qId2ColumnIndexMapper.put(qId + "_" + option.getId() + "_" + subOption.getId(), columnIndex);
                                    columnIndex++;
                                    i++;
                                }
                            }
                        }
                    } else if (qSchema.getType() == SurveySchema.QuestionType.Checkbox) {
                        int i = 0;
                        for (SurveySchema option : qSchema.getChildren()) {
                            // 单选 添加标题列
                            qId2ColumnIndexMapper.put(qId + "_" + option.getId(), columnIndex);
                            columnIndex++;

                            if (CollUtil.isNotEmpty(option.getChildren()) &&
                                    option.getAttribute().getDataType() == SurveySchema.DataType.horzBlank) {
                                for (SurveySchema subOption : option.getChildren()) {
                                    qId2ColumnIndexMapper.put(qId + "_" + option.getId() + "_" + subOption.getId(), columnIndex);
                                    columnIndex++;
                                    i++;
                                }
                            }
                        }
                    } else {
                        qId2ColumnIndexMapper.put(qId, columnIndex);
                        columnIndex++;
                    }
                } else if (qSchema.getType() == SurveySchema.QuestionType.Checkbox) {
                    for (SurveySchema option : qSchema.getChildren()) {
                        // 单选 添加标题列
                        qId2ColumnIndexMapper.put(qId + "_" + option.getId(), columnIndex);
                        columnIndex++;
                    }
                } else {
                    qId2ColumnIndexMapper.put(qId, columnIndex);
                    columnIndex++;
                }
            }
            return qId2ColumnIndexMapper;
        }
        return null;
    }

    public List<QuestionHeaderTextVO> getTextHeader2() {
        if (this.handlerList != null) {
            List<SurveySchema> schemaDataTypes = SchemaHelper.flatSurveySchema(schema);
            boolean shouldMerge = shouldMergeHeader();
            int columnIndex = 0;
            columnIndex++;

            List<QuestionHeaderTextVO> headerList = new ArrayList<>();

            for (SurveySchema qSchema : schemaDataTypes) {
                String qId = qSchema.getId();
                if (shouldMerge) {
                    QuestionHeaderTextVO tmp = new QuestionHeaderTextVO();
                    tmp.setId(qId);
                    tmp.setTitle(qSchema.getTitle());
                    tmp.setType(qSchema.getType());
                    tmp.setChildren(new ArrayList<>());
                    if (qSchema.getType() == SurveySchema.QuestionType.MultipleBlank
                            || qSchema.getType() == SurveySchema.QuestionType.Cascader
                            || qSchema.getType() == SurveySchema.QuestionType.Address
                            || qSchema.getType() == SurveySchema.QuestionType.HorzBlank) {
                        if (qSchema.getType() == SurveySchema.QuestionType.Address) {
                            for (int i = 1; i <= 3; i++) {
                                qId2ColumnIndexMapper.put(qId + "_" + i, columnIndex);
                                columnIndex++;
                            }
                        } else {
                            int optionIndex = 1;
                            for (SurveySchema child : qSchema.getChildren()) {
                                qId2ColumnIndexMapper.put(qId + "_" + child.getId(), columnIndex);
                                columnIndex++;
                                optionIndex++;
                                QuestionHeaderTextVO tmp2 = new QuestionHeaderTextVO();
                                tmp2.setId(child.getId());
                                tmp2.setTitle(child.getTitle());
                                tmp.getChildren().add(tmp2);
                            }
                        }

                    } else if (qSchema.getType() == SurveySchema.QuestionType.MatrixFillBlank) {
                        // 矩阵填空
                        for (SurveySchema.Row row : qSchema.getRow()) {
                            for (SurveySchema option : qSchema.getChildren()) {
                                // 添加
                                qId2ColumnIndexMapper.put(qId + "_" + row.getId() + "_" + option.getId(), columnIndex);
                                columnIndex++;
                            }
                        }
                    } else if (qSchema.getType() == SurveySchema.QuestionType.MatrixRadio
                            || qSchema.getType() == SurveySchema.QuestionType.MatrixCheckbox
                            || qSchema.getType() == SurveySchema.QuestionType.MatrixNps) {
                        // 矩阵单选/矩阵多选
                        for (SurveySchema.Row row : qSchema.getRow()) {
                            qId2ColumnIndexMapper.put(qId + "_" + row.getId(), columnIndex);
                            columnIndex++;
                            QuestionHeaderTextVO tmp2 = new QuestionHeaderTextVO();
                            tmp2.setId(row.getId());
                            tmp2.setTitle(row.getTitle());
                            tmp.getChildren().add(tmp2);
                        }
                    } else if (qSchema.getType() == SurveySchema.QuestionType.Radio) {
                        // 单选 添加标题列
                        qId2ColumnIndexMapper.put(qId, columnIndex);
                        columnIndex++;
                        // 选项里面的填空
                        // 多项填空合并列
                        int i = 0;
                        for (SurveySchema option : qSchema.getChildren()) {
                            if (CollUtil.isNotEmpty(option.getChildren()) &&
                                    option.getAttribute().getDataType() == SurveySchema.DataType.horzBlank) {
                                for (SurveySchema subOption : option.getChildren()) {
                                    qId2ColumnIndexMapper.put(qId + "_" + option.getId() + "_" + subOption.getId(), columnIndex);
                                    columnIndex++;
                                    i++;
                                }
                            }
                        }
                    } else if (qSchema.getType() == SurveySchema.QuestionType.Checkbox) {
                        int i = 0;
                        for (SurveySchema option : qSchema.getChildren()) {
                            // 单选 添加标题列
                            qId2ColumnIndexMapper.put(qId + "_" + option.getId(), columnIndex);
                            columnIndex++;

                            if (CollUtil.isNotEmpty(option.getChildren()) &&
                                    option.getAttribute().getDataType() == SurveySchema.DataType.horzBlank) {
                                for (SurveySchema subOption : option.getChildren()) {
                                    qId2ColumnIndexMapper.put(qId + "_" + option.getId() + "_" + subOption.getId(), columnIndex);
                                    columnIndex++;
                                    i++;
                                }
                            }
                        }
                    } else {
                        qId2ColumnIndexMapper.put(qId, columnIndex);
                        columnIndex++;
                    }

                    headerList.add(tmp);
                } else if (qSchema.getType() == SurveySchema.QuestionType.Checkbox) {
                    for (SurveySchema option : qSchema.getChildren()) {
                        // 单选 添加标题列
                        qId2ColumnIndexMapper.put(qId + "_" + option.getId(), columnIndex);
                        columnIndex++;
                    }
                } else {
                    qId2ColumnIndexMapper.put(qId, columnIndex);
                    columnIndex++;
                }
            }
            return headerList;
        }
        return null;
    }

    private List<String> getCommonHeaders() {
        List<String> metaHeaders = ListUtil.toList("开始时间", "提交时间", "答题时长", "提交人", "IP地址", "浏览器", "设备类型", "平台类型", "ID",
                "openid");
        // 考试模式添加分数
        if (schema.getAttribute().getExamScore() != null) {
            metaHeaders.add(0, "分数");
        }
        return metaHeaders;
    }

    private void getCommonRow(List rowData) {
        AnswerMetaInfo.AnswerInfo answerMetaInfo = Optional.ofNullable(answerInfo.getMetaInfo())
                .orElse(new AnswerMetaInfo())
                .getAnswerInfo();
        // 转换答卷元数据
        if (answerMetaInfo == null) {
            return;
        }
        // 添加分数
        if (schema.getAttribute().getExamScore() != null) {
            rowData.add(answerInfo.getExamScore());
        }
        Format formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        rowData.add(formatter.format(new Date(answerMetaInfo.getStartTime())));
        rowData.add(formatter.format(answerInfo.getCreateTime()));
        rowData.add(AnswerUtil.parseHumanReadableDuration(answerInfo));
        rowData.add(answerInfo.getCreateByName());

        if (answerInfo.getMetaInfo() == null || answerInfo.getMetaInfo().getClientInfo() == null) {
            rowData.addAll(Arrays.asList("", "", "", "", ""));
        } else {
            rowData.add(answerInfo.getMetaInfo().getClientInfo().getRemoteIp());
            rowData.add(answerInfo.getMetaInfo().getClientInfo().getBrowser());
            rowData.add(answerInfo.getMetaInfo().getClientInfo().getDeviceType());
            rowData.add(answerInfo.getMetaInfo().getClientInfo().getPlatform());
            rowData.add(answerInfo.getId());
            // 添加 openid
            if (this.answerInfo.getAnswer().containsKey("openid")) {
                rowData.add(this.answerInfo.getAnswer().get("openid"));
            }
        }
        AnswerUtil.avoidFormulaInjection(rowData);
    }

    /**
     * Excel 导出解析行数据
     *
     * @return
     */
    public List parseRowText() {
        List rowData = new ArrayList();
        if (this.handlerList != null) {
//            rowData.add(this.rowIndex + 1);
            // 表示是根 schema
            rowData.addAll(this.handlerList.stream()
                    .map(x -> x.parseRowText())
                    .reduce(new ArrayList<Object>(), (prev, curr) -> {
                        prev.addAll(curr);
                        return prev;
                    }));
//            getCommonRow(rowData);
            return rowData;
        }

        // 需要将数字类型转换成字符串
        // 通过 valueObj 遍历可能会导致选项的顺序乱掉，所以得按照 children schema 的顺序来构建答案
        List<String> questionValue = this.schema.getChildren().stream().map(optionSchema -> {
            Object optionValue = ((Map<?, ?>) Optional.ofNullable(this.getCurrentQuestionAnswer())
                    .orElse(new LinkedHashMap<>())).get(optionSchema.getId());
            if (optionValue == null) {
                return null;
            }
            if (optionValue != null && optionValue instanceof Boolean) {
                // 单选、多选题，选中的话，答案会是 true，需要转换成标题
                return trimHtmlTag(optionSchema.getTitle());
            }
            Object value = parseFillBlankOptionValue(optionSchema, optionValue);
            if (value != null) {
                return value.toString();
            }
            // 如果是单选填空，需要同时显示选项标题和答案
            if (this.schema.getType().equals(SurveySchema.QuestionType.Radio)
                    || this.schema.getType().equals(SurveySchema.QuestionType.Checkbox)) {
                return String.format("%s(%s)", trimHtmlTag(optionSchema.getTitle()), optionValue);
            }
            return optionValue.toString();
        }).collect(Collectors.toList());
        // 需要分列导出
        if (SurveySchema.QuestionType.splitCellType().contains(this.questionType())) {
            if (this.schema.getAttribute().getExamScore() != null) {
                // 需要导出问题分值
                rowData.add(this.questionScore().get(this.schema.getId()));
                for (int optionIndex = 1; optionIndex < questionValue.size(); optionIndex++) {
                    rowData.add("");
                }
            } else {
                // 需要合并列导出
                rowData.addAll(questionValue);
            }
        } else {
            if (this.schema.getAttribute().getExamScore() != null) {
                // 需要导出问题分值
                rowData.add(this.questionScore().get(this.schema.getId()));
            } else {
                // 需要合并列导出
                rowData.add(questionValue.stream().filter(x -> x != null).collect(Collectors.joining(",")));
            }
        }

        return rowData;
    }

    protected SurveySchema.QuestionType questionType() {
        return this.schema.getType();
    }

    /**
     * 获取当前题目的顺序
     *
     * @return
     */
    protected int getCurrentOrder() {
        return this.rootHandler.handlerList.indexOf(this) + 1;
    }

    protected Object parseFillBlankOptionValue(SurveySchema optionSchema, Object optionValue) {
        if (optionValue == null) {
            return null;
        }
        // 数据字典
        if (SurveySchema.DataType.selectDict.equals(optionSchema.getAttribute().getDataType())) {
            String[] dictValueAndLabel = optionValue.toString().split("\\|", 2);
            if (dictValueAndLabel.length > 1) {
                return dictValueAndLabel[1];
            }
            // 兼容历史版本
            return dictValueAndLabel[0];
        }
        // 下拉题
        if (SurveySchema.DataType.select.equals(optionSchema.getAttribute().getDataType())) {
            Optional<SurveySchema.DataSource> findDataSource = optionSchema.getDataSource()
                    .stream()
                    .filter(x -> x.getValue().equals(optionValue))
                    .findFirst();
            if (findDataSource.isPresent()) {
                return findDataSource.get().getLabel();
            }
            return optionValue.toString();
        }
        // 选项类型为横向填空
//        if (SurveySchema.DataType.horzBlank.equals(optionSchema.getAttribute().getDataType())) {
//            return optionSchema.getChildren()
//                    .stream()
//                    .map(subOption -> parseFillBlankOptionValue(subOption,
//                            ((Map<String, String>) optionValue).get(subOption.getId())))
//                    .map(x -> {
//                        if (x == null) {
//                            return "";
//                        } else {
//                            return x.toString();
//                        }
//                    })
//                    .collect(Collectors.joining(","));
//        }

        // 此处只处理 fillBlank 类型，其他类型返回空，由程序继续处理
        return null;
    }

    protected List<Object> parseMatrixData() {
        List<Object> result = new ArrayList<>();
        // 矩阵单选，多列
        // 矩阵多选，多列
        // 矩阵填空，行*列
        Map<String, Map<String, Object>> mapAnswer = Optional
                .ofNullable(((Map<String, Map<String, Object>>) this.getCurrentQuestionAnswer()))
                .orElse(new LinkedHashMap<>());
        for (SurveySchema.Row row : this.schema.getRow()) {
            String rowId = row.getId();
            Map<String, Object> rowValue = Optional.ofNullable(mapAnswer.get(rowId)).orElse(new LinkedHashMap<>());
            if (this.questionType() == SurveySchema.QuestionType.MatrixFillBlank) {
                this.schema.getChildren().forEach(option -> {
                    result.add(rowValue.get(option.getId()));
                });
            } else if (this.questionType() == SurveySchema.QuestionType.MatrixNps) {
                this.schema.getChildren().forEach(option -> {
                    result.add(rowValue.get(option.getId()));
                });
            } else {
                String columnValue = this.schema.getChildren()
                        .stream()
                        .filter(x -> rowValue.containsKey(x.getId()))
                        .map(x -> trimHtmlTag(x.getTitle()))
                        .collect(Collectors.joining(","));
                result.add(columnValue);
            }
        }
        return result;
    }

    public List getRowData() {
        if (this.getDataType() == DownloadDataTypeEnum.code) {
            return this.getRowCode();
        }
        return this.parseRowText();
    }


    private boolean shouldMergeHeader() {
        List<SurveySchema> schemaDataTypes = SchemaHelper.flatSurveySchema(schema);
        boolean shouldMerge = schemaDataTypes.stream()
                .anyMatch(x -> x.getType() == SurveySchema.QuestionType.HorzBlank
                        || x.getType() == SurveySchema.QuestionType.Cascader
                        || x.getType() == SurveySchema.QuestionType.Address
                        || x.getType() == SurveySchema.QuestionType.MultipleBlank
                        || x.getType().name().startsWith("Matrix")
                        || ((x.getType() == SurveySchema.QuestionType.Radio || x.getType() == SurveySchema.QuestionType.Checkbox)
                        && CollUtil.isNotEmpty(x.getChildren())
                        && x.getChildren().stream().anyMatch(opt -> SurveySchema.DataType.horzBlank.equals(opt.getAttribute().getDataType()))));
        return shouldMerge;
    }

    /**
     * 获取当前问题对应的处理器
     *
     * @param questionId
     * @return
     */
    public QuestionHandler getHandlerById(String questionId) {
        if (this.handlerList != null) {
            return this.handlerList.stream()
                    .filter(x -> x.getSchema().getId().equals(questionId))
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }

    protected Object parseText(String title, Object text) {
        return null;
    }

    public LinkedHashMap<String, Object> parseRowText2Answer() {
        for (QuestionHandler questionHandler : this.getHandlerList()) {
            String questionId = questionHandler.getSchema().getId();
            String questionTitle = trimHtmlTag(questionHandler.getSchema().getTitle());
            this.getQuestionTitle2text().forEach((title, text) -> {
                if (title.startsWith(questionTitle)) {
                    Object questionValue = questionHandler.parseText(title, text);
                    if (questionValue == null) {
                        return;
                    }
                    if (this.answer.get(questionId) == null) {
                        this.answer.put(questionId, questionValue);
                    } else if (questionValue instanceof Map) {
                        ((Map<String, Object>) (this.answer.get(questionId))).putAll((Map) questionValue);
                    }
                }
            });
        }
        return this.answer;
    }

}
