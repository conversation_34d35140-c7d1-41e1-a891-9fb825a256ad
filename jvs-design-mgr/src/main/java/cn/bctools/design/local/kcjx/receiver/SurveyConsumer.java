package cn.bctools.design.local.kcjx.receiver;

import cn.bctools.design.local.kcjx.component.AsyncComponent;
import cn.bctools.design.local.kcjx.util.RabbitMqUtils;
import cn.bctools.design.local.kcjx.vo_new.*;

import cn.bctools.redis.utils.RedisUtils;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Lazy(false)
public class SurveyConsumer {

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    AsyncComponent asyncComponent;

    /**
     * 并发量
     */
    private static final Integer CONCURRENCY_QUANTITY = 1;


    public void sendSyncSurvey(SurveyMqDto surveyMqDto) {
        try {
            rabbitTemplate.convertAndSend(RabbitMqUtils.QUEUE_SURVEY_ANSWER_TO_MG_TASK, surveyMqDto);
        } catch (Exception exception) {
            log.error("同步问卷消息发送失败:", exception);
        }
    }

    public void sendCopyDataSource(CopyDataSourceVO copyDataSourceVO) {
        try {
            rabbitTemplate.convertAndSend(RabbitMqUtils.QUEUE_COPY_DATA_SOURCE_TO_MG_TASK, copyDataSourceVO);
        } catch (Exception exception) {
            log.error("同步问卷消息发送失败:", exception);
        }
    }


    @SneakyThrows
    @RabbitListener(queues = RabbitMqUtils.QUEUE_SURVEY_ANSWER_TO_MG_TASK)
    public void updateSurvey(Message message, Channel channel) {
        try {
            SurveyMqDto surveyMqDto = JSONObject.parseObject(message.getBody(), SurveyMqDto.class);
            log.info("-----接收到消息为:{}", surveyMqDto);
            asyncComponent.asyncUpdateSurvey(surveyMqDto);
        } catch (Exception e) {
            log.error("消费消息发生异常", e);
        } finally {
            //手动ack
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }
    }

    @SneakyThrows
    @RabbitListener(queues = RabbitMqUtils.QUEUE_COPY_DATA_SOURCE_TO_MG_TASK)
    public void copyDataSource(Message message, Channel channel) {
        try {
            CopyDataSourceVO copyDataSourceVO = JSONObject.parseObject(message.getBody(), CopyDataSourceVO.class);
            log.info("-----接收到消息为:{}", copyDataSourceVO);
            asyncComponent.copyDataSource(copyDataSourceVO);
        } catch (Exception e) {
            log.error("消费消息发生异常", e);
        } finally {
            //手动ack
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }
    }
}
