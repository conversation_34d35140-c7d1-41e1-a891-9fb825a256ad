package cn.bctools.design.local.kcjx.service;

import cn.bctools.design.local.kcjx.entity.DataCleanQuestion;
import cn.bctools.design.local.kcjx.entity.DataCleanRule;
import cn.bctools.design.local.kcjx.vo_new.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface KcjxDataSourceService {

    void syncDataSource(String sourceId) throws IOException, InterruptedException;
    void copyDataSource(CopyDataSourceVO copyDataSourceVO);

    Object viewDataSourceDetail(DataSourceDetailVO dataSourceDetailVO);
    Object getAllDataSourceDetail(DataSourceDetailVO dataSourceDetailVO);

    Map<String, Object> cleanDataSourceAnswer(DataCleanVo dataCleanVo);
    void syncCleanDataSourceAnswer(DataCleanVo dataCleanVo);

    List<DataCleanRule> getCleanRules(String cleanId);
    List<DataCleanQuestion> getCleanQuestions(String cleanId);

    void saveDataSourceClean(DataCleanSaveVo dataCleanSaveVo);

    void rollbackDataSourceClean(DataCleanRollbackVo dataCleanRollbackVO);

}
