package cn.bctools.design.local.kcjx.service.impl;


import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.design.local.kcjx.constant.Constant;
import cn.bctools.design.local.kcjx.entity.DataExample;
import cn.bctools.design.local.kcjx.entity.DataExampleSetting;
import cn.bctools.design.local.kcjx.service.KcjxDataExampleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class KcjxDataExampleServiceImpl implements KcjxDataExampleService {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Override
    public List<DataExample> getExampleList(String projectId, String dataId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("delFlag").is(false))
                .addCriteria(Criteria.where("process_id").is(projectId))
                .addCriteria(Criteria.where("qnr_id").is(dataId));
        query.with(Sort.by(Sort.Direction.ASC, "item_order"));
        return mongoTemplate.find(query, DataExample.class, Constant.DATA_EXAMPLE);
    }

    @Override
    public String getAnswerAreaString(String dataId, String area) {
        Query query = new Query();
        query.addCriteria(Criteria.where("delFlag").is(false))
                .addCriteria(Criteria.where("name").is(area))
                .addCriteria(Criteria.where("qnr_id").is(dataId));
        DataExample dataExample = mongoTemplate.findOne(query, DataExample.class, Constant.DATA_EXAMPLE);

        String rtn = "";
        if (dataExample != null) {
            List<DataExampleSetting> dataExampleSettings = BeanCopyUtil.copys(dataExample.getSetting(), DataExampleSetting.class);
            DataExampleSetting dataExampleSettingOne = dataExampleSettings.get(0);
            if (dataExampleSettingOne != null) {
                rtn = dataExampleSettingOne.getOption();
            }
        }
        return rtn;
    }

}
