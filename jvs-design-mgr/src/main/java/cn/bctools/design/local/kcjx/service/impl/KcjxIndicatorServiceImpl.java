package cn.bctools.design.local.kcjx.service.impl;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.design.local.kcjx.constant.Constant;
import cn.bctools.design.local.kcjx.service.KcjxIndicatorService;
import cn.bctools.design.local.kcjx.service.KcjxReportGenerateService;
import cn.bctools.design.local.kcjx.vo.*;
import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.data.Rows;
import com.deepoove.poi.data.TableRenderData;
import com.deepoove.poi.data.Tables;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class KcjxIndicatorServiceImpl implements KcjxIndicatorService {
    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    KcjxReportGenerateService kcjxReportGenerateService;

    @Override
    public List<IndicatorsVo> getIndicators(String id) {

        Query query = new Query();
        query.addCriteria(Criteria.where("delFlag").is(false))
                .addCriteria(Criteria.where("system_id").is(id));

        List<Map> contents = mongoTemplate.find(query, Map.class, Constant.INDICATOR_CONTENT_COLLECTION_NAME);
        if (contents.size() == 0) {
            throw new RuntimeException("没有指标");
        }

        List<SyncKcjxIndicatorContentVo> syncIndicators = new ArrayList<>();
        contents.forEach(r -> {
            SyncKcjxIndicatorContentVo syncIndicator = BeanCopyUtil.copy(r, SyncKcjxIndicatorContentVo.class);
            syncIndicators.add(syncIndicator);
        });
        // 递归处理成树形结构
        List<IndicatorsVo> indicators = new ArrayList<>();
        buildTree(syncIndicators, indicators, "0");

        Map<Integer, String> orderMap = new HashMap<>();
        orderMap.put(1, "二");
        orderMap.put(2, "三");
        orderMap.put(3, "四");
        orderMap.put(4, "五");
        orderMap.put(5, "六");
        orderMap.put(6, "七");
        orderMap.put(7, "八");
        orderMap.put(8, "九");
        orderMap.put(9, "十");
        indicators.forEach(r -> {
            if (orderMap.containsKey(r.getItemOrder())) {
                r.setItemOrderText(orderMap.get(r.getItemOrder()) );
            } else {
                r.setItemOrderText(String.valueOf(r.getItemOrder()));
            }
        });

        return indicators;
    }

    @Override
    public List<SyncKcjxIndicatorContentVo> getChildren(String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("delFlag").is(false))
                .addCriteria(Criteria.where("parent_id").is(id));

        return mongoTemplate.find(query, SyncKcjxIndicatorContentVo.class, Constant.INDICATOR_CONTENT_COLLECTION_NAME);
    }

    @Override
    public String getParentId(String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("delFlag").is(false))
                .addCriteria(Criteria.where("id").is(id));
        SyncKcjxIndicatorContentVo syncIndicator = mongoTemplate.findOne(query, SyncKcjxIndicatorContentVo.class, Constant.INDICATOR_CONTENT_COLLECTION_NAME);
        if (syncIndicator == null) {
            return "0";
        }
        return syncIndicator.getParentId();
    }

    // 处理树形结构
    private static void buildTree(List<SyncKcjxIndicatorContentVo> syncIndicators, List<IndicatorsVo> indicators, String parentId) {
        Map<String, IndicatorsVo> indicatorMap = new HashMap<>();
        for (IndicatorsVo indicator : indicators) {
            indicatorMap.put(indicator.getId(), indicator);
        }

        for (SyncKcjxIndicatorContentVo r : syncIndicators) {
            if (r.getParentId().equals(parentId)) {
                IndicatorsVo indicatorsVo = BeanCopyUtil.copy(r, IndicatorsVo.class);
                indicatorsVo.setChildren(new ArrayList<>());
                indicators.add(indicatorsVo);
                buildTree(syncIndicators, indicatorsVo.getChildren(), r.getId());
                indicatorsVo.setChildrenText(indicatorsVo.getChildren().stream().map(IndicatorsVo::getName).collect(Collectors.joining("、")));
                indicatorsVo.setChildrenNum(indicatorsVo.getChildren().size());
            } else {
                IndicatorsVo parent = indicatorMap.get(r.getParentId());
                if (parent != null) {
                    IndicatorsVo indicatorsVo = BeanCopyUtil.copy(r, IndicatorsVo.class);
                    indicatorsVo.setChildren(new ArrayList<>());
                    parent.getChildren().add(indicatorsVo);
                    buildTree(syncIndicators, indicatorsVo.getChildren(), r.getId());
                    indicatorsVo.setChildrenText(indicatorsVo.getChildren().stream().map(IndicatorsVo::getName).collect(Collectors.joining("、")));
                    indicatorsVo.setChildrenNum(indicatorsVo.getChildren().size());
                }
            }
        }
    }

    @Override
    public void setHighOrLowText(List<IndicatorsVo> indicators, Map<String, Map<String, Float>> data3, SyncReportVo syncReportVo, ReportVo reportVo ) {
        List<String> highIndicator = new ArrayList<>();
        List<String> lowIndicator = new ArrayList<>();
        indicators.forEach(r -> {
            // 设置默认值
            Float score = data3.get(syncReportVo.getReportName()).get(r.getName());
            Float allScore = data3.get("全样本").get(r.getName());
            if (score != null && allScore != null) {
                if (score >= allScore) {
                    highIndicator.add(r.getName());
                } else {
                    lowIndicator.add(r.getName());
                }
            } else {
                lowIndicator.add(r.getName());
            }
        });
        if (highIndicator.size() > 0) {
            reportVo.setHighIndicatorText(String.join("、", highIndicator));
        }
        if (lowIndicator.size() > 0) {
            reportVo.setLowIndicatorText(String.join("、", lowIndicator));
        }
    }

    @Override
    public void setTable1(
        List<IndicatorsVo> indicators,
        List<SyncProjectSurveyVo> projectSurveyVos,
        Map<String, Map<String, Float>> data3,
        Map<String, Map<String, Map<String, Float>>> data4,
        SyncReportVo syncReportVo,
        ReportVo reportVo
    ) {
        List<String> headerRows = new ArrayList<>();
        headerRows.add("重点内容");
        projectSurveyVos.forEach(r -> {
            headerRows.add(r.getSurveyName());
        });
        headerRows.add("整体评价");
        RowRenderData header = Rows.of(headerRows.toArray(new String[0])).textBold().center().create();
        TableRenderData table = Tables.ofAutoWidth().addRow(header).center().create();
        indicators.forEach(r -> {
            List<String> rowData = new ArrayList<>();
            rowData.add(r.getName());
            projectSurveyVos.forEach(r1 -> {
                if (data4.get(syncReportVo.getReportName()).get(r1.getId()).containsKey(r.getName())) {
                    rowData.add(String.valueOf(data4.get(syncReportVo.getReportName()).get(r1.getId()).get(r.getName())));
                } else {
                    rowData.add("");
                }
            });
            // 保留两位
            rowData.add(String.valueOf(data3.get(syncReportVo.getReportName()).get(r.getName())));
            RowRenderData row = Rows.of(rowData.toArray(new String[0])).center().create();
            table.addRow(row);
        });
        reportVo.setTable1(table);
    }

    @Override
    public void setChart1(List<IndicatorsVo> indicators, Map<String, Map<String, Float>> data3, SyncReportVo syncReportVo, ReportVo reportVo) {
        Map<String, List<Number>> chartData1 = new HashMap<>();
        chartData1.put(reportVo.getArea(), new ArrayList<>());
        chartData1.put("全样本", new ArrayList<>());
        List<String> chart1Title = new ArrayList<>();
        indicators.forEach(r -> {
            Float score = data3.get(syncReportVo.getReportName()).get(r.getName());
            Float allScore = data3.get("全样本").get(r.getName());
            if (score != null || allScore != null) {
                chartData1.get("全样本").add(allScore);
                chartData1.get(reportVo.getArea()).add(score);
                chart1Title.add(r.getName());
            }
        });
        reportVo.setOverallEvaluation(kcjxReportGenerateService.getChart1Data(chart1Title, chartData1));
        if (chartData1.get(reportVo.getArea()).size() > 0) {
            reportVo.setHaveOverallEvaluation(true);
        }
    }

    @Override
    public void setIndicatorsList(
            List<IndicatorsVo> indicators,
            List<SyncProjectSurveyVo> projectSurveyVos,
            Map<String, Map<String, Float>> data3,
            Map<String, Map<String, Map<String, Float>>> data4,
            SyncReportVo syncReportVo,
            ReportVo reportVo
    ) {
        indicators.forEach(r -> {
            Map<String, List<Number>> chartData2 = new HashMap<>();
            chartData2.put(reportVo.getArea(), new ArrayList<>());
            chartData2.put("全样本", new ArrayList<>());
            List<String> chart2Title = new ArrayList<>();
            r.getChildren().forEach(r2 -> {
                if (r2.getChildren() != null && r2.getChildren().size() > 0) {
                    Map<String, List<Number>> chartData3 = new HashMap<>();
                    chartData3.put(reportVo.getArea(), new ArrayList<>());
                    chartData3.put("全样本", new ArrayList<>());
                    List<String> chart3Title = new ArrayList<>();
                    r2.getChildren().forEach(r3 -> {
                        Float score = data3.get(syncReportVo.getReportName()).get(r3.getName());
                        Float allScore = data3.get("全样本").get(r3.getName());
                        if (score != null || allScore != null) {
                            chartData3.get("全样本").add(allScore);
                            chartData3.get(reportVo.getArea()).add(score);
                            chart3Title.add(r3.getName());
                        }
                    });
                    r2.setOverallEvaluation3(kcjxReportGenerateService.getChart1Data(chart3Title, chartData3));
                    if (chartData3.get(reportVo.getArea()).size() > 0) {
                        r2.setHaveOverallEvaluation3(true);
                    }

                    // 三级指标问卷的数据
                    r2.getChildren().forEach(r4 -> {
                        Map<String, List<Number>> chartData4 = new HashMap<>();
                        chartData4.put(syncReportVo.getReportName(), new ArrayList<>());
                        chartData4.put("全样本", new ArrayList<>());
                        List<String> chart4Title = new ArrayList<>();
                        projectSurveyVos.forEach(p -> {
                            Float score = data4.get(syncReportVo.getReportName()).get(p.getId()).get(r4.getName());
                            Float allScore = data4.get("全样本").get(p.getId()).get(r4.getName());
                            if (score != null || allScore != null) {
                                chartData4.get("全样本").add(allScore);
                                chartData4.get(syncReportVo.getReportName()).add(score);
                                chart4Title.add(p.getSurveyName());
                            }
                        });
                        if (chart4Title.size() > 0) {
                            r4.setOverallEvaluation4(kcjxReportGenerateService.getChart1Data(r4.getName(), chart4Title, chartData4));
                        }
                        if (chartData4.get(syncReportVo.getReportName()).size() > 0) {
                            r4.setHaveOverallEvaluation4(true);
                        }
                    });

                }

                Float score = data3.get(syncReportVo.getReportName()).get(r2.getName());
                Float allScore = data3.get("全样本").get(r2.getName());
                if (score != null || allScore != null) {
                    chartData2.get("全样本").add(allScore);
                    chartData2.get(reportVo.getArea()).add(score);
                    chart2Title.add(r2.getName());
                }
            });
            r.setOverallEvaluation2(kcjxReportGenerateService.getChart1Data(chart2Title, chartData2));
            if (chartData2.get(reportVo.getArea()).size() > 0) {
                r.setHaveOverallEvaluation2(true);
            }

            List<String> headerRows2 = new ArrayList<>();
            headerRows2.add("评价对象");
            projectSurveyVos.forEach(p -> {
                headerRows2.add(p.getSurveyName());
            });
            RowRenderData header2 = Rows.of(headerRows2.toArray(new String[0])).textBold().center().create();
            TableRenderData table2 = Tables.ofAutoWidth().addRow(header2).center().create();

            r.getChildren().forEach(r1 -> {
                r1.getChildren().forEach(r2 -> {
                    List<String> rowData = new ArrayList<>();
                    rowData.add(r2.getName());
                    projectSurveyVos.forEach(p -> {
                        if (data4.get(syncReportVo.getReportName()).get(p.getId()).containsKey(r2.getName())) {
                            rowData.add("√");
                        } else {
                            rowData.add("");
                        }
                    });
                    RowRenderData row = Rows.of(rowData.toArray(new String[0])).center().create();
                    table2.addRow(row);
                });
            });

            r.setTable2(table2);
        });

        reportVo.setIndicators(indicators);
    }

    // 设置区域名称
    private void setArea(List<IndicatorsVo> indicators, ReportVo reportVo) {
        // 设置区域名称
        indicators.forEach(r -> {
            r.setArea(reportVo.getArea());
            if (r.getChildren() != null && r.getChildren().size() > 0) {
                setArea(r.getChildren(), reportVo);
            }
        });
    }

    @Override
    public void calculate(List<IndicatorsVo> indicators, List<SyncProjectSurveyVo> projectSurveyVos, Map<String, Map<String, Float>> data3, Map<String, Map<String, Map<String, Float>>> data4, SyncReportVo syncReportVo, ReportVo reportVo) {

        setArea(indicators, reportVo);

        indicators.forEach(r -> {
            calculate_1(r, data3, syncReportVo);
            r.getChildren().forEach(r1 -> {
                calculate_2(r1, projectSurveyVos, data4, syncReportVo);
                r1.getChildren().forEach(r2 -> {
                    calculate_2(r2, projectSurveyVos, data4, syncReportVo);
                });
            });
        });
    }


    private void calculate_1(IndicatorsVo indicators, Map<String, Map<String, Float>> data3, SyncReportVo syncReportVo) {
        // 高于
        List<String> gy = new ArrayList<>();
        // 等于
        List<String> dy = new ArrayList<>();
        // 小于
        List<String> xy = new ArrayList<>();

        indicators.getChildren().forEach(r1 -> {
            float score1 = data3.get(syncReportVo.getReportName()).get(r1.getName());
            float allScore1 = data3.get("全样本").get(r1.getName());
            if (score1 > allScore1) {
                gy.add(r1.getName());
            } else if (score1 == allScore1) {
                dy.add(r1.getName());
            } else if (score1 < allScore1) {
                xy.add(r1.getName());
            }
        });

        String analysis = "";
        if (gy.size() > 0) {
            analysis += String.join("、", gy) + "得分较高" ;
        }
        if (dy.size() > 0) {
            if(!analysis.isEmpty()) {
                analysis += "，";
            }
            analysis += String.join("、", dy) + "得分相等" ;
        }
        if (xy.size() > 0) {
            if(!analysis.isEmpty()) {
                analysis += "，";
            }
            analysis += String.join("、", xy) + "得分略低" ;
        }
        if (!analysis.isEmpty()) {
            analysis += "。";
            indicators.setAnalysis(analysis);
        }
    }
    private void calculate_2(IndicatorsVo indicators,  List<SyncProjectSurveyVo> projectSurveyVos, Map<String, Map<String, Map<String, Float>>> data4, SyncReportVo syncReportVo) {
        List<String> surveyCollection = new ArrayList<>();
        // 明显高于
        List<String> mxgy = new ArrayList<>();
        // 略高于
        List<String> lgy = new ArrayList<>();
        // 略低于
        List<String> ldy = new ArrayList<>();
        // 低于
        List<String> dy = new ArrayList<>();
        projectSurveyVos.forEach(p -> {
            if (data4.get(syncReportVo.getReportName()).get(p.getId()).containsKey(indicators.getName())) {
                float score = data4.get(syncReportVo.getReportName()).get(p.getId()).get(indicators.getName());
                float allScore = data4.get("全样本").get(p.getId()).get(indicators.getName());
                if ((score - allScore) > 0) {
                    if (score - allScore >= 3) {
                        mxgy.add(p.getSurveyName());
                    } else {
                        lgy.add(p.getSurveyName());
                    }
                } else if ((allScore - score) > 0) {
                    if (allScore - score >= 3) {
                        ldy.add(p.getSurveyName());
                    } else {
                        dy.add(p.getSurveyName());
                    }
                }
                surveyCollection.add(p.getSurveyName());
            }
        });
        String analysis = "";
        if (mxgy.size() > 0) {
            analysis += String.join("、", mxgy) + "明显高于全样本平均水平。" ;
        }
        if (lgy.size() > 0) {
            analysis += String.join("、", lgy) + "略高于全样本平均水平。" ;
        }
        if (ldy.size() > 0) {
            analysis += String.join("、", ldy) + "略低于全样本平均水平。" ;
        }
        if (dy.size() > 0) {
            analysis += String.join("、", dy) + "低于全样本平均水平。" ;
        }
        if (!analysis.isEmpty()) {
            indicators.setAnalysis(analysis);
        }
        if (surveyCollection.size() > 0) {
            indicators.setSurveyNames(String.join("、", surveyCollection));
        }
    }
}
