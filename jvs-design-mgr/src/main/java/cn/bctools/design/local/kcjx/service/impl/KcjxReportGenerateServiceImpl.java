package cn.bctools.design.local.kcjx.service.impl;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.design.local.kcjx.service.KcjxReportGenerateService;
import cn.bctools.design.local.kcjx.vo.*;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.data.ChartMultiSeriesRenderData;
import com.deepoove.poi.data.Charts;
import com.deepoove.poi.data.SeriesRenderData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class KcjxReportGenerateServiceImpl implements KcjxReportGenerateService {
    @Autowired
    private MongoTemplate mongoTemplate;


    @Override
    public List<SurveyCollection> getSurveyCollection(String area, List<SyncProjectSurveyVo> surveyList, List<SyncProjectAnswerVo> answerList) {
        List<SurveyCollection> surveyCollectionList = new java.util.ArrayList<>();
        surveyList.forEach(r -> {
            SurveyCollection surveyCollection = new SurveyCollection();
            surveyCollection.setId(r.getId());
            surveyCollection.setSurveyName(r.getSurveyName());
            long collectNum = answerList.stream().filter(a -> a.getParentId().equals(r.getId()) && a.getArea().equals(area)).count();

            surveyCollection.setNum((int) collectNum);
            surveyCollection.setEffective((int) collectNum);
            if (collectNum == 0) {
                surveyCollection.setEfficient("0%");
            } else {
                surveyCollection.setEfficient("100%");
            }
            surveyCollectionList.add(surveyCollection);
        });
        return surveyCollectionList;
    }

    @Override
    public ChartMultiSeriesRenderData getChart1Data(List<String> title, Map<String, List<Number>> data) {
        // 多系列图表
        ChartMultiSeriesRenderData chart = Charts
                // 设置问卷
                .ofMultiSeries("", title.toArray(new String[0]))
                .create();

        List<SeriesRenderData> seriesRenderDataList = new java.util.ArrayList<>();
        data.forEach((k, v) -> {
            SeriesRenderData seriesRenderData = new SeriesRenderData();
            seriesRenderData.setName(k);
            seriesRenderData.setValues(v.toArray(new Number[0]));
            seriesRenderDataList.add(seriesRenderData);
        });
        chart.setSeriesDatas(seriesRenderDataList);
        return chart;
    }

    @Override
    public ChartMultiSeriesRenderData getChart1Data(String title, List<String> headers, Map<String, List<Number>> data) {
        // 多系列图表
        ChartMultiSeriesRenderData chart = Charts
                // 设置问卷
                .ofMultiSeries(title, headers.toArray(new String[0]))
                .create();

        List<SeriesRenderData> seriesRenderDataList = new java.util.ArrayList<>();
        data.forEach((k, v) -> {
            SeriesRenderData seriesRenderData = new SeriesRenderData();
            seriesRenderData.setName(k);
            seriesRenderData.setValues(v.toArray(new Number[0]));
            seriesRenderDataList.add(seriesRenderData);
        });
        chart.setSeriesDatas(seriesRenderDataList);
        return chart;
    }

    @Override
    public Map<String, Map<String, Float>> getData3(SyncProjectVo syncProjectVo, SyncReportVo syncReportVo, List<SyncProjectSurveyVo> projectSurveyVos, List<SyncProjectAnswerVo> projectAnswerVos) {

        // 计分方式
        Map<String, Map<String, String>> surveyScoreFormula = new HashMap<>();
        Map<String, Map<String, SyncDataSourceContent>> surveyContent = new HashMap<>();
        projectSurveyVos.forEach(r -> {
            surveyScoreFormula.put(r.getId(), new HashMap<>());
            surveyContent.put(r.getId(), new HashMap<>());

            // 获取问卷题目
            Query query = new Query();
            query.addCriteria(Criteria.where("project_id").is(syncProjectVo.getDataSource()))
                    .addCriteria(Criteria.where("parent_id").is(r.getId()))
                    .addCriteria(Criteria.where("delFlag").is(false));
            List<Map> contents = mongoTemplate.find(query, Map.class, "1009_kcjx_data_source_content");
            List<SyncDataSourceContent> contentsVos = BeanCopyUtil.copys(contents, SyncDataSourceContent.class);
            // 去除不计分的
            List<SyncDataSourceContent> dataSourceContents = contentsVos.stream()
                    .filter(r1 -> r1.getScoreFormula().equals("正向计分") || r1.getScoreFormula().equals("反向计分")).collect(Collectors.toList());

            dataSourceContents.forEach(r1 -> {
                if (!r1.getQuestionId().isEmpty()) {
                    surveyScoreFormula.get(r1.getParentId()).put(r1.getQuestionId(), r1.getScoreFormula());
                    surveyContent.get(r1.getParentId()).put(r1.getQuestionId(), r1);
                }
            });
        });

        Map<Integer, Integer> fxScore = new HashMap<>();
        fxScore.put(1, 5);
        fxScore.put(2, 4);
        fxScore.put(3, 3);
        fxScore.put(4, 2);
        fxScore.put(5, 1);


        Map<String, Map<String, Map<String, Float>>> surveyAnswerScoreList = new HashMap<>();
        surveyAnswerScoreList.put("全样本", new HashMap<>());
        surveyAnswerScoreList.put(syncReportVo.getReportName(), new HashMap<>());

        // 计算每道题的平均分
        projectSurveyVos.forEach(r -> {
            Map<String, Integer> score = new HashMap<>();
            Map<String, Integer> areaScore = new HashMap<>();
            projectAnswerVos.stream().filter(r1 -> r1.getParentId().equals(r.getId())).forEach(r1 -> {
                JSONObject jsonObject = JSONObject.parseObject(r1.getContent().toString());
                jsonObject.forEach((k, v) -> {
                    if (!score.containsKey(k)) {
                        score.put(k, 0);
                    }
                    if (!v.toString().isEmpty()) {
                        String scoreFormula = surveyScoreFormula.get(r1.getParentId()).get(k);
                        if (scoreFormula != null) {
                            Integer scoreTmp = 0;
                            if (scoreFormula.equals("正向计分")) {
                                scoreTmp = Integer.parseInt(v.toString());
                            } else if (scoreFormula.equals("反向计分")) {
                                scoreTmp = fxScore.get(Integer.parseInt(v.toString()));
                            }
                            if (r1.getArea().equals(syncReportVo.getAreaName())) {
                                if (!areaScore.containsKey(k)) {
                                    areaScore.put(k, 0);
                                }
                                areaScore.put(k, areaScore.get(k) + scoreTmp);
                            }
                            score.put(k, score.get(k) + scoreTmp);
                        }
                    }
                });
            });
            long answerNum = projectAnswerVos.stream().filter(r1 -> r1.getParentId().equals(r.getId())).count();
            long answerAreaNum = projectAnswerVos.stream().filter(r1 -> r1.getParentId().equals(r.getId())
                    && r1.getArea().equals(syncReportVo.getAreaName())).count();
            // 获取每道题的平均得分
            Map<String, Float> avgScore = new HashMap<>();
            Map<String, Float> avgAreaScore = new HashMap<>();

            score.forEach((k, v) -> {
                if (v > 0) {
                    avgScore.put(k, (float) (v / answerNum));
                }
            });
            areaScore.forEach((k, v) -> {
                if (v > 0) {
                    avgAreaScore.put(k, (float) (v / answerAreaNum));
                }
            });
            surveyAnswerScoreList.get("全样本").put(r.getId(), avgScore);
            surveyAnswerScoreList.get(syncReportVo.getReportName()).put(r.getId(), avgAreaScore);
        });

        Map<String, Map<String, Float>> indicatorScore = new HashMap<>();
        indicatorScore.put("全样本", new HashMap<>());
        indicatorScore.put(syncReportVo.getReportName(), new HashMap<>());

        Map<String, Integer> indicatorCount = new HashMap<>();
        Map<String, Integer> indicatorAreaCount = new HashMap<>();
        projectSurveyVos.forEach(r -> {
            Map<String, Float> score = surveyAnswerScoreList.get("全样本").get(r.getId());
            Map<String, Float> scoreArea = surveyAnswerScoreList.get(syncReportVo.getReportName()).get(r.getId());

            surveyContent.get(r.getId()).forEach((k, v) -> {
                if (!v.getLevelThird().isEmpty()) {
                    if (!indicatorCount.containsKey(v.getLevelThird())) {
                        indicatorCount.put(v.getLevelThird(), 0);
                    }
                    indicatorCount.put(v.getLevelThird(), (indicatorCount.get(v.getLevelThird()) + 1));
                    if (!indicatorAreaCount.containsKey(v.getLevelThird())) {
                        indicatorAreaCount.put(v.getLevelThird(), 0);
                    }
                    indicatorAreaCount.put(v.getLevelThird(), (indicatorAreaCount.get(v.getLevelThird()) + 1));


                    if (!indicatorScore.get("全样本").containsKey(v.getLevelThird())) {
                        indicatorScore.get("全样本").put(v.getLevelThird(), 0f);
                    }
                    score.forEach((k1, v1) -> {
                        if (v.getQuestionId().equals(k1)) {
                            Float old = indicatorScore.get("全样本").get(v.getLevelThird());
                            indicatorScore.get("全样本").put(v.getLevelThird(), old + v1);
                        }
                    });
                    if (!indicatorScore.get(syncReportVo.getReportName()).containsKey(v.getLevelThird())) {
                        indicatorScore.get(syncReportVo.getReportName()).put(v.getLevelThird(), 0f);
                    }
                    scoreArea.forEach((k1, v1) -> {
                        if (v.getQuestionId().equals(k1)) {
                            Float old = indicatorScore.get(syncReportVo.getReportName()).get(v.getLevelThird());
                            indicatorScore.get(syncReportVo.getReportName()).put(v.getLevelThird(), old + v1);
                        }
                    });
                }
                if (!v.getLevelSecond().isEmpty()) {
                    if (!indicatorCount.containsKey(v.getLevelSecond())) {
                        indicatorCount.put(v.getLevelSecond(), 0);
                    }
                    indicatorCount.put(v.getLevelSecond(), (indicatorCount.get(v.getLevelSecond()) + 1));
                    if (!indicatorAreaCount.containsKey(v.getLevelSecond())) {
                        indicatorAreaCount.put(v.getLevelSecond(), 0);
                    }
                    indicatorAreaCount.put(v.getLevelSecond(), (indicatorAreaCount.get(v.getLevelSecond()) + 1));


                    if (!indicatorScore.get("全样本").containsKey(v.getLevelSecond())) {
                        indicatorScore.get("全样本").put(v.getLevelSecond(), 0f);
                    }
                    score.forEach((k1, v1) -> {
                        if (v.getQuestionId().equals(k1)) {
                            Float old = indicatorScore.get("全样本").get(v.getLevelSecond());
                            indicatorScore.get("全样本").put(v.getLevelSecond(), old + v1);
                        }
                    });
                    if (!indicatorScore.get(syncReportVo.getReportName()).containsKey(v.getLevelSecond())) {
                        indicatorScore.get(syncReportVo.getReportName()).put(v.getLevelSecond(), 0f);
                    }
                    scoreArea.forEach((k1, v1) -> {
                        if (v.getQuestionId().equals(k1)) {
                            Float old = indicatorScore.get(syncReportVo.getReportName()).get(v.getLevelSecond());
                            indicatorScore.get(syncReportVo.getReportName()).put(v.getLevelSecond(), old + v1);
                        }
                    });
                }
                if (!v.getLevelFirst().isEmpty()) {
                    if (!indicatorCount.containsKey(v.getLevelFirst())) {
                        indicatorCount.put(v.getLevelFirst(), 0);
                    }
                    indicatorCount.put(v.getLevelFirst(), (indicatorCount.get(v.getLevelFirst()) + 1));
                    if (!indicatorAreaCount.containsKey(v.getLevelFirst())) {
                        indicatorAreaCount.put(v.getLevelFirst(), 0);
                    }
                    indicatorAreaCount.put(v.getLevelFirst(), (indicatorAreaCount.get(v.getLevelFirst()) + 1));


                    if (!indicatorScore.get("全样本").containsKey(v.getLevelFirst())) {
                        indicatorScore.get("全样本").put(v.getLevelFirst(), 0f);
                    }
                    score.forEach((k1, v1) -> {
                        if (v.getQuestionId().equals(k1)) {
                            Float old = indicatorScore.get("全样本").get(v.getLevelFirst());
                            indicatorScore.get("全样本").put(v.getLevelFirst(), old + v1);
                        }
                    });
                    if (!indicatorScore.get(syncReportVo.getReportName()).containsKey(v.getLevelFirst())) {
                        indicatorScore.get(syncReportVo.getReportName()).put(v.getLevelFirst(), 0f);
                    }
                    scoreArea.forEach((k1, v1) -> {
                        if (v.getQuestionId().equals(k1)) {
                            Float old = indicatorScore.get(syncReportVo.getReportName()).get(v.getLevelFirst());
                            indicatorScore.get(syncReportVo.getReportName()).put(v.getLevelFirst(), old + v1);
                        }
                    });
                }
            });
        });

        indicatorScore.forEach((k, v) -> {
            v.forEach((k1, v1) -> {
                long count;
                if (k.equals("全样本")) {
                    count = indicatorCount.get(k1);
                } else {
                    count = indicatorAreaCount.get(k1);
                }
                String score = String.format("%.2f", v1 / count);
                v.put(k1, Float.valueOf(score));
            });
        });

        return indicatorScore;
    }

    @Override
    // 获取问卷维度的分数
    public Map<String, Map<String, Map<String, Float>>> getData4(SyncProjectVo syncProjectVo, SyncReportVo syncReportVo, List<SyncProjectSurveyVo> projectSurveyVos, List<SyncProjectAnswerVo> projectAnswerVos) {
        // 计分方式
        Map<String, Map<String, String>> surveyScoreFormula = new HashMap<>();
        Map<String, Map<String, SyncDataSourceContent>> surveyContent = new HashMap<>();
        projectSurveyVos.forEach(r -> {
            surveyScoreFormula.put(r.getId(), new HashMap<>());
            surveyContent.put(r.getId(), new HashMap<>());

            // 获取问卷题目
            Query query = new Query();
            query.addCriteria(Criteria.where("project_id").is(syncProjectVo.getDataSource()))
                    .addCriteria(Criteria.where("parent_id").is(r.getId()))
                    .addCriteria(Criteria.where("delFlag").is(false));
            List<Map> contents = mongoTemplate.find(query, Map.class, "1009_kcjx_data_source_content");
            List<SyncDataSourceContent> contentsVos = BeanCopyUtil.copys(contents, SyncDataSourceContent.class);
            // 去除不计分的
            List<SyncDataSourceContent> dataSourceContents = contentsVos.stream()
                    .filter(r1 -> r1.getScoreFormula().equals("正向计分") || r1.getScoreFormula().equals("反向计分")).collect(Collectors.toList());

            dataSourceContents.forEach(r1 -> {
                if (!r1.getQuestionId().isEmpty()) {
                    surveyScoreFormula.get(r1.getParentId()).put(r1.getQuestionId(), r1.getScoreFormula());
                    surveyContent.get(r1.getParentId()).put(r1.getQuestionId(), r1);
                }
            });
        });

        Map<Integer, Integer> fxScore = new HashMap<>();
        fxScore.put(1, 5);
        fxScore.put(2, 4);
        fxScore.put(3, 3);
        fxScore.put(4, 2);
        fxScore.put(5, 1);


        Map<String, Map<String, Map<String, Float>>> surveyAnswerScoreList = new HashMap<>();
        surveyAnswerScoreList.put("全样本", new HashMap<>());
        surveyAnswerScoreList.put(syncReportVo.getReportName(), new HashMap<>());

        // 计算每道题的平均分
        projectSurveyVos.forEach(r -> {
            Map<String, Integer> score = new HashMap<>();
            Map<String, Integer> areaScore = new HashMap<>();
            projectAnswerVos.stream().filter(r1 -> r1.getParentId().equals(r.getId())).forEach(r1 -> {
                JSONObject jsonObject = JSONObject.parseObject(r1.getContent().toString());
                jsonObject.forEach((k, v) -> {
                    if (!score.containsKey(k)) {
                        score.put(k, 0);
                    }
                    if (!v.toString().isEmpty()) {
                        String scoreFormula = surveyScoreFormula.get(r1.getParentId()).get(k);
                        if (scoreFormula != null) {
                            Integer scoreTmp = 0;
                            if (scoreFormula.equals("正向计分")) {
                                scoreTmp = Integer.parseInt(v.toString());
                            } else if (scoreFormula.equals("反向计分")) {
                                scoreTmp = fxScore.get(Integer.parseInt(v.toString()));
                            }
                            if (r1.getArea().equals(syncReportVo.getAreaName())) {
                                if (!areaScore.containsKey(k)) {
                                    areaScore.put(k, 0);
                                }
                                areaScore.put(k, areaScore.get(k) + scoreTmp);
                            }
                            score.put(k, score.get(k) + scoreTmp);
                        }
                    }
                });
            });
            long answerNum = projectAnswerVos.stream().filter(r1 -> r1.getParentId().equals(r.getId())).count();
            long answerAreaNum = projectAnswerVos.stream().filter(r1 -> r1.getParentId().equals(r.getId())
                    && r1.getArea().equals(syncReportVo.getAreaName())).count();
            // 获取每道题的平均得分
            Map<String, Float> avgScore = new HashMap<>();
            Map<String, Float> avgAreaScore = new HashMap<>();

            score.forEach((k, v) -> {
                if (v > 0) {
                    avgScore.put(k, (float) (v / answerNum));
                }
            });
            areaScore.forEach((k, v) -> {
                if (v > 0) {
                    avgAreaScore.put(k, (float) (v / answerAreaNum));
                }
            });
            surveyAnswerScoreList.get("全样本").put(r.getId(), avgScore);
            surveyAnswerScoreList.get(syncReportVo.getReportName()).put(r.getId(), avgAreaScore);
        });

        Map<String, Map<String, Map<String, Float>>> indicatorScore = new HashMap<>();
        Map<String, Map<String, Map<String, Integer>>> indicatorCount = new HashMap<>();
        surveyAnswerScoreList.forEach((k, v) -> {
            if (!indicatorScore.containsKey(k)) {
                indicatorScore.put(k, new HashMap<>());
            }
            if (!indicatorCount.containsKey(k)) {
                indicatorCount.put(k, new HashMap<>());
            }
            v.forEach((k1, v1) -> {
                if (!indicatorScore.get(k).containsKey(k1)) {
                    indicatorScore.get(k).put(k1, new HashMap<>());
                }
                if (!indicatorCount.get(k).containsKey(k1)) {
                    indicatorCount.get(k).put(k1, new HashMap<>());
                }
                Map<String, SyncDataSourceContent> questionData = surveyContent.get(k1);
                v1.forEach((k2, v2) -> {
                    if (questionData.containsKey(k2)) {
                        SyncDataSourceContent question = questionData.get(k2);
                        if (!question.getLevelThird().isEmpty()) {
                            if (!indicatorScore.get(k).get(k1).containsKey(question.getLevelThird())) {
                                indicatorScore.get(k).get(k1).put(question.getLevelThird(), 0f);
                            }
                            indicatorScore.get(k).get(k1).put(question.getLevelThird(), indicatorScore.get(k).get(k1).get(question.getLevelThird()) + v2);
                            if (!indicatorCount.get(k).get(k1).containsKey(question.getLevelThird())) {
                                indicatorCount.get(k).get(k1).put(question.getLevelThird(), 0);
                            }
                            indicatorCount.get(k).get(k1).put(question.getLevelThird(), indicatorCount.get(k).get(k1).get(question.getLevelThird()) + 1);
                        }
                        if (!question.getLevelSecond().isEmpty()) {
                            if (!indicatorScore.get(k).get(k1).containsKey(question.getLevelSecond())) {
                                indicatorScore.get(k).get(k1).put(question.getLevelSecond(), 0f);
                            }
                            indicatorScore.get(k).get(k1).put(question.getLevelSecond(), indicatorScore.get(k).get(k1).get(question.getLevelSecond()) + v2);
                            if (!indicatorCount.get(k).get(k1).containsKey(question.getLevelSecond())) {
                                indicatorCount.get(k).get(k1).put(question.getLevelSecond(), 0);
                            }
                            indicatorCount.get(k).get(k1).put(question.getLevelSecond(), indicatorCount.get(k).get(k1).get(question.getLevelSecond()) + 1);
                        }
                        if (!question.getLevelFirst().isEmpty()) {
                            if (!indicatorScore.get(k).get(k1).containsKey(question.getLevelFirst())) {
                                indicatorScore.get(k).get(k1).put(question.getLevelFirst(), 0f);
                            }
                            indicatorScore.get(k).get(k1).put(question.getLevelFirst(), indicatorScore.get(k).get(k1).get(question.getLevelFirst()) + v2);
                            if (!indicatorCount.get(k).get(k1).containsKey(question.getLevelFirst())) {
                                indicatorCount.get(k).get(k1).put(question.getLevelFirst(), 0);
                            }
                            indicatorCount.get(k).get(k1).put(question.getLevelFirst(), indicatorCount.get(k).get(k1).get(question.getLevelFirst()) + 1);
                        }
                    }
                });
            });
        });
        indicatorScore.forEach((k, v) -> {
            v.forEach((k1, v1) -> {
                v1.forEach((k2, v2) -> {
                    if (indicatorCount.get(k).get(k1).containsKey(k2)) {
                        String res = String.format("%.2f", v2 / indicatorCount.get(k).get(k1).get(k2));
                        v1.put(k2, Float.valueOf(res));
                    }
                });
            });
        });

        return indicatorScore;
    }

}
