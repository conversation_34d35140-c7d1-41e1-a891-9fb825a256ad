package cn.bctools.design.local.kcjx.vo_new;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "mq队列的队列消息体")
public class SurveyMqDto {

    @ApiModelProperty(value = "项目id")
    private String sourceId;

    @ApiModelProperty(value = "问卷id")
    private String surveyId;

}
