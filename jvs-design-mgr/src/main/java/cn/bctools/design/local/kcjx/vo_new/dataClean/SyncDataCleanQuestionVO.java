package cn.bctools.design.local.kcjx.vo_new.dataClean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
public class SyncDataCleanQuestionVO {

    private String id;

    // 题目id
    private String titleId;

    // 数据清洗id
    private String cleanId;

    private DataCleanSetting setting = new DataCleanSetting();

    private Boolean delFlag;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataCleanSetting implements Serializable {

        private List<String> answer;

        private String revealId;
    }

}
