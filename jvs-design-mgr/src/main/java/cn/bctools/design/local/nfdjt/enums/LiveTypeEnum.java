package cn.bctools.design.local.nfdjt.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *  直播平台枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Getter
@AllArgsConstructor
public enum LiveTypeEnum {

    YZB("YZB", "研直播"),
    WZ("WZ", "微赞"),
    CDXW("CDXW", "触电新闻"),
    YJXY("YJXY", "粤教翔云"),
    HJY("HJY", "和教育"),
    unknown("unknown", "未知"),;

    @EnumValue
    @JsonEnumDefaultValue
    public final String value;
    public final String desc;

    @Override
    public String toString() {
        return desc;
    }

    public static LiveTypeEnum getByDesc(String desc) {
        for (LiveTypeEnum value : values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        return unknown;
    }

}
