package cn.bctools.design.local.ruledesign.controller;

import cn.bctools.common.constant.SysConstant;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import cn.bctools.design.local.ruledesign.entity.RuleDesignClearCacheDto;
import cn.bctools.design.project.entity.JvsApp;
import cn.bctools.design.project.service.JvsAppService;
import cn.bctools.design.rule.entity.RuleDesignPo;
import cn.bctools.design.rule.service.RuleDesignService;
import cn.bctools.log.annotation.Log;
import cn.bctools.redis.utils.RedisUtils;
import cn.bctools.rule.entity.enums.RunType;
import cn.bctools.rule.utils.dto.RuleExecDto;
import cn.bctools.rule.utils.html.HtmlGraph;
import cn.bctools.rule.utils.html.RuleExecuteDto;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Api(value = "自定义逻辑设计", tags = "自定义逻辑设计接口")
@RestController
@RequestMapping("/local/rule-design")
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class RuleDesignController {

    RuleDesignService designService;
    JvsAppService jvsAppService;
    RedisUtils redisUtils;

    @Log
    @ApiOperation(value = "清除逻辑缓存", notes = "清除逻辑缓存")
    @PostMapping("/clear-cache")
    public R<String> clearCache(@RequestBody RuleDesignClearCacheDto dto) {
        JvsApp app = jvsAppService.getById(dto.getJvsAppId());
        if (app == null) {
            return R.failed("未找到应用");
        }

        RuleDesignPo po = designService.getOne(Wrappers.query(new RuleDesignPo().setJvsAppId(dto.getJvsAppId()).setSecret(dto.getId())));
        if (po == null) {
            return R.failed("未找到逻辑设计");
        }

        RuleExecuteDto data = new RuleExecuteDto();
        data.setReqVariableMap(po.getParameterPos()).setVariableMap(po.getParameterPos());
        RuleExecDto ruleExecDto = new RuleExecDto()
                .setExecuteDto(data)
                .setType(RunType.TEST)
                .setSecret(po.getSecret())
                .setGraph(JSONObject.parseObject(po.getDesignDrawingJson(), HtmlGraph.class));

        String ruleKeyPrefix = SysConstant.redisKey("rule:cache:" + ruleExecDto.getSecret(), "*");

        Set<String> keySet = redisUtils.keys(ruleKeyPrefix);

        if (ObjectNull.isNull(keySet)) {
            return R.ok("已清除 0 条缓存数据");
        }

        // 删除 key 的前缀
        keySet = keySet.stream().map(e -> e.replace(RedisUtils.prefix, "")).collect(Collectors.toSet());

        redisUtils.del(keySet);

        return R.ok("已清除 " + keySet.size() + " 条缓存数据");
    }
}