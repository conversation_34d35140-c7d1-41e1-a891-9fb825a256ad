package cn.bctools.design.local.ruledesign.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
public class RuleDesignClearCacheDto {
    @ApiModelProperty(value = "jvsAppId", required = true)
    @NotNull(message = "jvsAppId 不能为空")
    private String jvsAppId;

    @ApiModelProperty(value = "id", required = true)
    @NotNull(message = "id 不能为空")
    private String id;;

}
