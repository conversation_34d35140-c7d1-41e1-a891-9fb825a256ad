package cn.bctools.design.local.survey.service.impl;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.survey.service.SurveyService;
import cn.bctools.design.local.survey.config.SurveyProperties;
import cn.bctools.design.local.survey.service.SurveySearchApiService;
import cn.bctools.design.local.survey.vo.SurveyApiQueryVO;
import cn.bctools.design.local.survey.vo.SurveyApiViewVO;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @project jvs-backend
 * @date 2024/6/23
 * @description
 * @package cn.bctools.design.local.survey.service.impl
 */
@Slf4j
@Service
public class SurveySearchApiServiceImpl implements SurveySearchApiService{
    @Autowired
    SurveyService surveyService;

    @Autowired
    SurveyProperties surveyProperties;
    @Override
    public Page<SurveyApiViewVO> getSurveyApiViewVOPage(Page page,SurveyApiQueryVO surveyApiQueryVO) {
        long current = page.getCurrent();
        long size = page.getSize();
        JSONObject surveyListObject = new JSONObject();
        try {
            Map<String,Object> query = new LinkedHashMap<>();
            if(StrUtil.isNotEmpty(surveyApiQueryVO.getMode())){
                query.put("mode",surveyApiQueryVO.getMode());
            }
            if(!ObjectUtils.isEmpty(surveyApiQueryVO.getQueryShared())){
                query.put("queryShared",surveyApiQueryVO.getQueryShared());
            }
            if(ObjectNull.isNotNull(surveyApiQueryVO.getQueryTotal())){
                query.put("queryTotal",surveyApiQueryVO.getQueryTotal());
            }
            surveyListObject = surveyService.getSurveyList(UserCurrentUtils.getUserId(),
                    surveyProperties.getClientId(), surveyProperties.getClientSecret(), surveyProperties.getHost(), (int) current, (int) size,
                    query);
        }catch (Exception e){
            log.error("获取问卷数据失败{}",e.getMessage());
            return new Page<>();
        }

        JSONObject data = surveyListObject.getJSONObject("data");
        Page<SurveyApiViewVO> surveyApiViewVOPage = convertToPage(data);
        List<SurveyApiViewVO> records = surveyApiViewVOPage.getRecords();

//        for (SurveyApiViewVO record : records) {
//            record.setClientId(surveyProperties.getClientId());
////            record.setViewButton(String.format(surveyProperties.getHost()+surveyProperties.getViewButton(),record.getId()));
////            record.setStatisticButton(String.format(surveyProperties.getHost()+surveyProperties.getStatisticButton(),record.getId()));
//            //文件夹名称
//            if(record.getMode().equals("folder")){
//                List<SurveyApiViewVO> children = record.getChildren();
//                if(!CollectionUtils.isEmpty(children)){
//                    for (SurveyApiViewVO child : children) {
//                        child.setFolder(record.getName());
//                    }
//                    record.setChildren(children);
//                }
//            }
//        }
        surveyApiViewVOPage.setRecords(records);
        return surveyApiViewVOPage;
    }

    @Override
    public SurveyApiViewVO getSurveyById(SurveyApiQueryVO surveyApiQueryVO) {
        JSONObject surveyJsonObject = surveyService.getSurveyById(UserCurrentUtils.getUserId(), surveyApiQueryVO.getSurveyId(),
                surveyProperties.getClientId(), surveyProperties.getClientSecret(), surveyProperties.getHost());
        if(ObjectUtils.isEmpty(surveyJsonObject)){
            return null;
        }
        if(surveyJsonObject.getInt("code") != 0){
            return null;
        }
        JSONObject data = surveyJsonObject.getJSONObject("data");
        SurveyApiViewVO surveyApiViewVO = data.toBean(SurveyApiViewVO.class);
        return surveyApiViewVO;
    }


    public Page<SurveyApiViewVO> convertToPage(JSONObject data) {
        Page<SurveyApiViewVO> surveyPageResult = JSONUtil.toBean(data, Page.class);

        JSONArray recordsArray = data.getJSONArray("records");
        List<SurveyApiViewVO> records = recordsArray.toList(SurveyApiViewVO.class);
        surveyPageResult.setRecords(records);

        return surveyPageResult;

    }

    @Override
    public JSONObject getSurveyQuestion(String surveyId) {
        JSONObject questions = new JSONObject();
        try {
            questions = surveyService.getSurveyQuestion(surveyId, surveyProperties.getHost());
        }catch (Exception e){
            log.error("获取问卷题目数据失败{}",e.getMessage());
        }
        return questions;
    }

    @Override
    public JSONObject getSurveyInfo(String surveyId) {
        JSONObject project = new JSONObject();
        try {
            project = surveyService.getSurveyInfo(surveyId, surveyProperties.getHost());
        }catch (Exception e){
            log.error("获取问卷答案数据失败{}",e.getMessage());
        }
        return project;
    }

    @Override
    public JSONObject getSurveyAnswer(String surveyId, Integer pageNo, Integer pageSize) {
        JSONObject answers = new JSONObject();
        try {
            answers = surveyService.getSurveyAnswer(surveyId, pageNo, pageSize, surveyProperties.getHost());
        }catch (Exception e){
            log.error("获取问卷答案数据失败{}",e.getMessage());
        }
        return answers;
    }

    @Override
    public JSONObject getSurveyAnswerPageInfo(String surveyId, Integer pageSize) {
        JSONObject answers = new JSONObject();
        try {
            answers = surveyService.getSurveyAnswerPageInfo(surveyId, pageSize, surveyProperties.getHost());
        }catch (Exception e){
            log.error("获取问卷答案数据失败{}",e.getMessage());
        }
        return answers;
    }

}
