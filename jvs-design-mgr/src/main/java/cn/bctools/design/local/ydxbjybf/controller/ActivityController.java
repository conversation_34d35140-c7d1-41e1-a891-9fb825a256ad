package cn.bctools.design.local.ydxbjybf.controller;

import cn.bctools.common.utils.R;
import cn.bctools.design.local.ydxbjybf.handle.Template1Handle;
import cn.bctools.log.annotation.Log;
import cn.bctools.oss.dto.FileNameDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.FileNotFoundException;

@Slf4j
@Api(value = "粤东西北教研帮扶", tags = "粤东西北教研帮扶")
@RestController
@RequestMapping("/local/ydxbjybf/")
public class ActivityController {


    @Log
    @ApiOperation(value = "下载活动指引", notes = "下载活动指引")
    @GetMapping("/download/{activityId}")
    public R<FileNameDto> download(@PathVariable String activityId) throws FileNotFoundException {
        Template1Handle template1Handle = new Template1Handle();
        return R.ok(template1Handle.create(activityId));
    }


}
