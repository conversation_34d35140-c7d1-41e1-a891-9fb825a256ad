package cn.bctools.design.local.ydxbjybf.dto;

/**
 * 粤东西北教研帮扶-活动管理
 **/

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Document("1801511978264530945")
public class Activity extends Base {

    @Field("id")
    private String id;

    @Field("activity_name")
    private String activityName;

    private String year;

    // 帮扶内容
    @Field("activity_content")
    private String activityContent;

    // 指导单位
    @Field("guide_unit")
    private String guideUnit;

    // 活动时间
    private List<String> time;

    // 主办单位
    private String organizer;

    // 承办单位
    @Field("company_name")
    private String companyName;

    // 协办单位
    @Field("assist_unit_list")
    private List<String> assistUnitList;

    // 联系人id
    @Field("contact_ids")
    private List<String> contactIds;


}
