package cn.bctools.design.local.ydxbjybf.dto;

/**
 * 粤东西北教研帮扶-活动地区
 **/

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Document("1802526581241987074")
public class ActivityArea {

    @Field("activity_id")
    private String activityId;

    // 活动地区
    @Field("activity_place")
    private String activityPlace;

    // 学科
    private List<String> subject;

    // 领队id
    @Field("leader_id")
    private String leaderId;

    // 领队电话
    @Field("leader_phone")
    private String leaderPhone;

    // 联系人id
    @Field("contact_id")
    private String contactId;

    // 联系人电话
    @Field("contact_phone")
    private String contactPhone;

    // 工作人员id
    @Field("worker_id")
    private String workerIds;

    // 工作人员电话
    @Field("worker_phone")
    private String workerPhones;

    // 活动日期
    private List<String> time;


}
