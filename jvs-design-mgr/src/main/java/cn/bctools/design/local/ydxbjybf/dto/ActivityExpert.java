package cn.bctools.design.local.ydxbjybf.dto;
/*
帮扶活动专家
 */

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Document("1813854051377352706")
public class ActivityExpert extends Base {

    @Field("activity_id")
    private String activityId;

    @Field("userid")
    private String userId;

    @Field("real_name")
    private String realName;

    // 职务
    @Field("duties")
    private String duties;

    // 职称
    @Field("post_title")
    private String postTitle;

    // 单位id
    @Field("org")
    private String org;

    // 简介
    @Field("summary")
    private String summary;

    @Field("avatar")
    private Object avatar;

    @Field("activity_per_and_sub")
    private String activityPerAndSub;

    @Field("activity_per_and_sub_place")
    private String activityPerAndSubPlace;

    @Field("item_order")
    private String itemOrder;

}
