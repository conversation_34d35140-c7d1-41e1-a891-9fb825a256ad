package cn.bctools.design.local.ydxbjybf.dto;
/*
活动领导专家
 */

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;


@Data
@EqualsAndHashCode(callSuper = false)
@Document("1802604651093213186")
public class ActivityExpertLeader extends Base {

    @Field("activity_id")
    private String activityId;

    // 活动地点
    @Field("activity_place")
    private String activityPlace;

    @Field("user_id")
    private String userId;

    @Field("real_name")
    private String realName;

    @Field("phone")
    private String phone;

    // 职务
    @Field("duties")
    private String duties;

    // 职称
    @Field("post_title")
    private String postTitle;

    // 单位id
    @Field("org")
    private String org;

    // 简介
    @Field("summary")
    private String summary;

    @Field("avatar")
    private Object avatar;

    @Field("activity_per_and_sub")
    private String activityPerAndSub;

    @Field("activity_per_and_sub_place")
    private String activityPerAndSubPlace;

    @Field("other_duties_post_title")
    private String otherDutiesPostTitle;

    @Field("item_order")
    private String itemOrder;

}
