package cn.bctools.design.local.ydxbjybf.dto;
/*
活动指引
 */

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Document("1803315195701936129")
public class ActivityGuide extends Base {

    @Field("activity_id")
    private String activityId;

    private String title;

    private List<String> time;

    // 地点id
    @Field("activity_place_id")
    private String activityPlaceId;

    private String describe;

    // 内容
    private String content;

}
