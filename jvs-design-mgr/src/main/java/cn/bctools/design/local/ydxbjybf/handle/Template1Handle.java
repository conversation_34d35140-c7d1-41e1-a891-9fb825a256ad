package cn.bctools.design.local.ydxbjybf.handle;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.R;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.design.local.ydxbjybf.HtmlUtil;
import cn.bctools.design.local.ydxbjybf.dto.Activity;
import cn.bctools.design.local.ydxbjybf.dto.ActivityGuide;
import cn.bctools.design.local.ydxbjybf.dto.ActivityPlan;
import cn.bctools.design.local.ydxbjybf.service.*;
import cn.bctools.design.local.ydxbjybf.vo.GuideVo;
import cn.bctools.design.local.ydxbjybf.vo.report.ActivityExpertLeaderVo;
import cn.bctools.design.local.ydxbjybf.vo.report.ActivityExpertVo;
import cn.bctools.oss.cons.OssSystemCons;
import cn.bctools.oss.dto.BaseFile;
import cn.bctools.oss.dto.FileNameDto;
import cn.bctools.oss.template.OssTemplate;
import cn.bctools.web.utils.WebUtils;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import org.apache.poi.xwpf.usermodel.BreakType;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.ddr.poi.html.HtmlRenderPolicy;

import java.io.*;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

public class Template1Handle {


    private final ActivityService activityService = SpringContextUtil.getBean(ActivityService.class);
    private final ActivityAreaService activityAreaService = SpringContextUtil.getBean(ActivityAreaService.class);
    private final ActivityGuideService activityGuideService = SpringContextUtil.getBean(ActivityGuideService.class);
    private final ActivityPlanService activityPlanService = SpringContextUtil.getBean(ActivityPlanService.class);
    private final ActivityExpertService activityExpertService = SpringContextUtil.getBean(ActivityExpertService.class);
    private final ActivityExpertLeaderService activityExpertLeaderService = SpringContextUtil.getBean(ActivityExpertLeaderService.class);
    private final OssTemplate ossTemplate = SpringContextUtil.getBean(OssTemplate.class);

    public FileNameDto create(String activityId) throws FileNotFoundException {
        Activity activity = activityService.getActivityById(activityId);

        if (activity == null) {
            throw new RuntimeException("活动不存在");
        }
        GuideVo guideVo = new GuideVo();
        // 第一页
        String title1 = activity.getYear() + " 年" + activity.getOrganizer();
        String title2 = activity.getActivityName();
        guideVo.setTitle1(title1);
        guideVo.setTitle2(title2);
        guideVo.setGuideUnit(activity.getGuideUnit());
        guideVo.setOrganizer(activity.getOrganizer());
        // 承办单位
        guideVo.setCompanyName(activity.getCompanyName().replace("、", "\n"));
        String time = "";
        if (activity.getTime() != null) {
            String[] time1Data = activity.getTime().get(0).split("-");
            time += time1Data[0] + "年" + time1Data[1] + "月" + time1Data[2] + "日";
            String time2 = activity.getTime().get(1);
            if (time2 != null) {
                String[] time2Data = time2.split("-");
                time += "—" + time2Data[1] + "月" + time2Data[2] + "日";
            }
        }
        guideVo.setTime(time);


        // 活动安排
        List<ActivityPlan> activityPlans = activityPlanService.getActivityPlan(activityId);
        String host = WebUtils.getRequest().getHeader("host");
        activityPlans.forEach(a -> {
            if (a.getContent() != null) {
                List<String> images = HtmlUtil.regexMatchPicture(a.getContent());
                images.forEach(img -> {
                    a.setContent(a.getContent().replace(img, host + img));
                });
            }
        });
        guideVo.setPlans(activityPlans);

        // 帮扶活动专家
        List<ActivityExpertVo> activityExperts = activityExpertService.getActivityExpertReportList(activityId);
        guideVo.setExperts(activityExperts);

        // 领导和专家名单
        List<ActivityExpertLeaderVo> activityExpertLeaders = activityExpertLeaderService.getActivityExpertLeaderReportList(activityId);
        guideVo.setExpert1(activityExpertLeaders);

        // 活动指引
        List<ActivityGuide> activityGuides = activityGuideService.getActivityGuide(activityId);
        guideVo.setGuides(activityGuides);

        return createWord(guideVo);

    }

    private FileNameDto createWord(GuideVo guideVo) throws FileNotFoundException {

        InputStream inputStream = getClass().getClassLoader().getResourceAsStream("ydxbjybf_1.docx");
        HtmlRenderPolicy htmlRenderPolicy = new HtmlRenderPolicy();
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();

        Configure config = Configure.builder()
                .bind("content", htmlRenderPolicy)
                .bind("expert1", policy)
                .bind("experts", policy)
                .useSpringEL()
                .build();

        XWPFTemplate render = XWPFTemplate.compile(inputStream, config).render(guideVo);

        List<XWPFParagraph> paragraphs = render.getXWPFDocument().getParagraphs();
        for (XWPFParagraph p : paragraphs) {
            List<XWPFRun> runs = p.getRuns();
            if (runs != null) {
                for (XWPFRun r : runs) {
                    String text = r.getText(0);
                    if (text != null && text.contains("分页")) {
                        text = text.replace("分页", "");
                        r.setText(text, 0);
                        r.addBreak(BreakType.PAGE);
                    }
                }
            }
        }


        String fileName = guideVo.getTitle1() + ".docx";
        File word = new File(fileName);
        try {
            render.writeToFile(word.getAbsolutePath());
            inputStream.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        InputStream input = new FileInputStream(word);
        String module = "/jvs-ui/ydxbbf/guide/";
        BaseFile source = ossTemplate.put(OssSystemCons.OSS_BUCKET_NAME, module, input, fileName, true);
        FileNameDto target = BeanCopyUtil.copy(source, FileNameDto.class);
        target.setOriginalFileName(fileName);
        target.setFileLink(ossTemplate.fileLink(target.getFileName(), target.getBucketName()));
        target.setFileSize(source.getSize());
        //用打印模板的文件名称覆盖返回的文件名称
        target.setFileName(fileName);
        return target;

    }



}
