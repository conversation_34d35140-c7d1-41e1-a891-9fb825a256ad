package cn.bctools.design.local.ydxbjybf.service.impl;


import cn.bctools.design.local.ydxbjybf.dto.ActivityArea;
import cn.bctools.design.local.ydxbjybf.service.ActivityAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ActivityAreaServiceImpl implements ActivityAreaService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public List<ActivityArea> getActivityAreas(String activityId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("activity_id").is(activityId));
        query.addCriteria(Criteria.where("delFlag").is(false));
        return mongoTemplate.find(query, ActivityArea.class);
    }

}
