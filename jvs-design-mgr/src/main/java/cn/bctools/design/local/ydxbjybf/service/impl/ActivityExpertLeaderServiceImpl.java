package cn.bctools.design.local.ydxbjybf.service.impl;


import cn.bctools.auth.api.api.AuthDeptServiceApi;
import cn.bctools.auth.api.dto.SysDeptDto;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.design.crud.service.JvsTreeService;
import cn.bctools.design.local.ydxbjybf.dto.ActivityExpertLeader;
import cn.bctools.design.local.ydxbjybf.service.ActivityExpertLeaderService;
import cn.bctools.design.local.ydxbjybf.vo.ExpertAvatar;
import cn.bctools.design.local.ydxbjybf.vo.report.ActivityExpertLeaderVo;
import cn.bctools.design.use.api.TreeApi;
import cn.bctools.oss.template.OssTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ActivityExpertLeaderServiceImpl implements ActivityExpertLeaderService {

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    TreeApi treeApi;
    @Autowired
    AuthDeptServiceApi authDeptServiceApi;
    @Autowired
    OssTemplate ossTemplate;

    @Override
    public List<ActivityExpertLeader> getActivityExpertLeaderList(String activityId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("activity_id").is(activityId));
        query.addCriteria(Criteria.where("delFlag").is(false));
        query.with(Sort.by(Sort.Direction.ASC, "activity_per_and_sub"));
        query.with(Sort.by(Sort.Direction.DESC, "item_order"));
        return mongoTemplate.find(query, ActivityExpertLeader.class);
    }

    @Override
    public List<ActivityExpertLeaderVo> getActivityExpertLeaderReportList(String activityId) {
        List<ActivityExpertLeaderVo> rtn = new ArrayList<>();
        List<ActivityExpertLeader> activityExpertLeaders = getActivityExpertLeaderList(activityId);
        if (activityExpertLeaders == null) {
            return rtn;
        }
        List<String> orgIds = activityExpertLeaders.stream().map(ActivityExpertLeader::getOrg).distinct().collect(Collectors.toList());
        List<String> dutiesIds = activityExpertLeaders.stream().map(ActivityExpertLeader::getDuties).distinct().collect(Collectors.toList());
        List<String> postTitleIds = activityExpertLeaders.stream().map(ActivityExpertLeader::getPostTitle).distinct().collect(Collectors.toList());
        List<String> perAndSubIds = activityExpertLeaders.stream().map(ActivityExpertLeader::getActivityPerAndSub).distinct().collect(Collectors.toList());

        Map<String, String> orgMap = new HashMap<>();
        Map<String, String> dutiesMap = new HashMap<>();
        Map<String, String> postTitleMap = new HashMap<>();
        Map<String, String> periodMap = new HashMap<>();
        Map<String, String> subjectMap = new HashMap<>();
        if (!orgIds.isEmpty()) {
            List<SysDeptDto> sysDeptDtoList = authDeptServiceApi.getByIds(orgIds).getData();
            sysDeptDtoList.forEach(e -> {
                orgMap.put(e.getId(), e.getName());
            });
        }
        if (!dutiesIds.isEmpty()) {
            dutiesMap = treeApi.getNameByUniqueNameList("position", dutiesIds).getData();
        }
        if (!postTitleIds.isEmpty()) {
            postTitleMap = treeApi.getNameByUniqueNameList("positional_title", postTitleIds).getData();
        }
        if (!perAndSubIds.isEmpty()) {
            List<String> periodIds = new ArrayList<>();
            List<String> subjectIds = new ArrayList<>();
            perAndSubIds.forEach(perAndSubId -> {
                if (perAndSubId == null || perAndSubId.isEmpty()) {
                    return;
                }
                String[] split = perAndSubId.split("-");
                periodIds.add(split[0]);
                if (split.length == 2) {
                    subjectIds.add(split[1]);
                }
            });
            periodMap = treeApi.getNameByUniqueNameList("ydxb_period", periodIds).getData();
            subjectMap = treeApi.getNameByUniqueNameList("ydxb_subject", subjectIds).getData();
        }

        Map<String, String> finalDutiesMap = dutiesMap;
        Map<String, String> finalPostTitleMap = postTitleMap;
        Map<String, String> finalPeriodMap = periodMap;
        Map<String, String> finalSubjectMap = subjectMap;
        activityExpertLeaders.forEach(a -> {
           ActivityExpertLeaderVo vo = new ActivityExpertLeaderVo();
           if (a.getAvatar() != null && a.getAvatar() instanceof List) {
               List<ExpertAvatar> expertAvatars = (List) a.getAvatar();
               ExpertAvatar expertAvatar = BeanCopyUtil.copy(expertAvatars.get(0), ExpertAvatar.class);
               if (expertAvatar.getFileName() != null && expertAvatar.getBucketName() != null) {
                   String imgUrl = ossTemplate.fileLink(expertAvatar.getFileName(), expertAvatar.getBucketName());
                   vo.setImage(imgUrl);
               }
           }
           String periodAndSubject = "";
           if (a.getActivityPerAndSub() != null && !a.getActivityPerAndSub().isEmpty()) {
               String[] split = a.getActivityPerAndSub().split("-");
               String period = split[0];
               String subject = "";
               if (split.length == 2) {
                   subject = split[1];
               }
               if (finalPeriodMap.containsKey(period)) {
                   periodAndSubject += finalPeriodMap.get(period);
               }
               if (finalSubjectMap.containsKey(subject)) {
                   periodAndSubject += finalSubjectMap.get(subject);
               }
           }
           vo.setPeriodAndSubject(periodAndSubject);

           String positionAndTitle = "";
           if (finalDutiesMap.containsKey(a.getDuties())) {
               positionAndTitle += finalDutiesMap.get(a.getDuties());
               positionAndTitle += "/";
           }
           if (finalPostTitleMap.containsKey(a.getPostTitle())) {
               positionAndTitle += finalPostTitleMap.get(a.getPostTitle());
           }
           String title2 = positionAndTitle;

           vo.setRealName(a.getRealName());
           vo.setOrgName(a.getOrg());
           if (orgMap.containsKey(a.getOrg())) {
               vo.setOrgName(orgMap.get(a.getOrg()));
               title2 = orgMap.get(a.getOrg()) + "，" + title2;
           }
           if (a.getOtherDutiesPostTitle() != null) {
               positionAndTitle = a.getOtherDutiesPostTitle() + "/" + positionAndTitle;
           }
           vo.setTitle(positionAndTitle);
           vo.setTitle2(title2);
           vo.setSummary(a.getSummary());
           rtn.add(vo);
       });
       return rtn;
    }

}
