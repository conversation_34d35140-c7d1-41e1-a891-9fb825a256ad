package cn.bctools.design.local.ydxbjybf.service.impl;

import cn.bctools.design.local.ydxbjybf.dto.ActivityGuide;
import cn.bctools.design.local.ydxbjybf.service.ActivityGuideService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ActivityGuideServiceImpl implements ActivityGuideService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public List<ActivityGuide> getActivityGuide(String activityId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("activity_id").is(activityId));
        query.addCriteria(Criteria.where("delFlag").is(false));
        return mongoTemplate.find(query, ActivityGuide.class);
    }

}
