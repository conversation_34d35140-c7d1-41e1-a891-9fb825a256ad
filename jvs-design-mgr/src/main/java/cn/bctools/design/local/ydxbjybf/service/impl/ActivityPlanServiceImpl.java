package cn.bctools.design.local.ydxbjybf.service.impl;

import cn.bctools.design.local.ydxbjybf.dto.ActivityPlan;
import cn.bctools.design.local.ydxbjybf.service.ActivityPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ActivityPlanServiceImpl implements ActivityPlanService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public List<ActivityPlan> getActivityPlan(String activityId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("activity_id").is(activityId));
        query.addCriteria(Criteria.where("delFlag").is(false));
        query.with(Sort.by(Sort.Direction.DESC, "activity_place_id"));
        return mongoTemplate.find(query, ActivityPlan.class);
    }

}
