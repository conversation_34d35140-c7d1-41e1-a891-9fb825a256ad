package cn.bctools.design.local.ydxbjybf.service.impl;


import cn.bctools.design.local.ydxbjybf.dto.Activity;
import cn.bctools.design.local.ydxbjybf.service.ActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ActivityServiceImpl implements ActivityService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public List<Activity> getActivityList() {
        Query query = new Query();
        query.addCriteria(Criteria.where("delFlag").is(false));
        return mongoTemplate.find(query, Activity.class);
    }


    @Override
    public Activity getActivityById(String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("id").is(id));
        query.addCriteria(Criteria.where("delFlag").is(false));
        return mongoTemplate.findOne(query, Activity.class);
    }

}
