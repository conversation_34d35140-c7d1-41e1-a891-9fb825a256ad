package cn.bctools.design.local.ydxbjybf.vo;

import cn.bctools.design.local.ydxbjybf.dto.ActivityGuide;
import cn.bctools.design.local.ydxbjybf.dto.ActivityPlan;
import cn.bctools.design.local.ydxbjybf.vo.report.ActivityExpertLeaderVo;
import cn.bctools.design.local.ydxbjybf.vo.report.ActivityExpertVo;
import lombok.Data;

import java.util.List;

@Data
public class GuideVo {

    private String title1;

    private String title2;

    // 指导单位
    private String guideUnit;

    // 主办单位
    private String organizer;

    // 承办单位
    private String companyName;

    // 活动时间
    private String time;

    private List<ActivityPlan> plans;

    private List<ActivityExpertLeaderVo> expert1;

    private List<ActivityExpertVo> experts;

    private List<ActivityGuide> guides;


}
