package cn.bctools.design.platform;

import cn.bctools.common.utils.R;
import cn.bctools.design.rule.entity.RuleExternalPo;
import cn.bctools.design.rule.service.RuleExternalService;
import cn.bctools.log.annotation.Log;
import cn.bctools.rule.config.SystemInit;
import cn.bctools.rule.entity.enums.InputType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.hutool.core.lang.Dict;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Api(tags = "逻辑引擎扩展")
@RestController
@RequestMapping("/platform/rule/extend")
public class RuleExtendController {

    RuleExternalService ruleExternalService;

    @ApiOperation(value = "获取扩展")
    @GetMapping
    public R all() {
        List<RuleExternalPo> list = ruleExternalService.list();
        return R.ok(list);
    }

    @ApiOperation(value = "获取分组设置信息")
    @GetMapping("/group")
    public R ruleGroup() {
        List<Dict> collect = Arrays.stream(RuleGroup.values()).filter(RuleGroup::isExternal).map(e -> Dict.create().set("key", e.name()).set("value", e.name()).set("url", e.getUrl())).collect(Collectors.toList());
        return R.ok(collect);
    }

    @ApiOperation(value = "获取类型")
    @GetMapping("/types")
    public R types() {
        List<Dict> collect = Arrays.stream(InputType.values()).filter(InputType::getExtend).map(e -> new Dict().set("key", e.get()).set("value", e.get())).collect(Collectors.toList());
        return R.ok(collect);
    }

    @ApiOperation(value = "获取系统默认组件")
    @GetMapping("/default")
    public R defaultRule() {
        List<RuleExternalPo> collect = SystemInit.getFunctionsMaps().stream()
                .map(e -> new RuleExternalPo().setIcon(e.getIcon()).setExplainInfo(e.getExplain()).setName(e.getFunctionName()).setRuleGroup(e.getGroup()).setFieldList(e.getParameters()))
                .collect(Collectors.toList());
        return R.ok(collect);
    }

    @Log
    @ApiOperation(value = "新增扩展")
    @PostMapping
    public R save(@RequestBody RuleExternalPo ruleExternalPo) {
        ruleExternalPo.setRuleGroup(RuleGroup.接口扩展);
        ruleExternalService.save(ruleExternalPo);
        return R.ok("新增成功");
    }

    @Log
    @ApiOperation(value = "修改扩展")
    @PutMapping
    public R put(@RequestBody RuleExternalPo ruleExternalPo) {
        ruleExternalService.updateById(ruleExternalPo);
        return R.ok("新增成功");
    }

    @Log
    @ApiOperation(value = "删除扩展")
    @DeleteMapping("/{id}")
    public R delete(@PathVariable String id) {
        ruleExternalService.removeById(id);
        return R.ok("新增成功");
    }

}