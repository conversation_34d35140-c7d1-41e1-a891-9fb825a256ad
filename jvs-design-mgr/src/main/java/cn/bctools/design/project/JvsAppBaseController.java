package cn.bctools.design.project;

import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.entity.vo.IdsVo;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.*;
import cn.bctools.common.utils.function.Get;
import cn.bctools.database.util.SqlFunctionUtil;
import cn.bctools.design.jvslog.service.impl.JvsLogServiceImpl;
import cn.bctools.design.menu.entity.AppMenuType;
import cn.bctools.design.menu.service.AppMenuTypeService;
import cn.bctools.design.project.dto.AppDto;
import cn.bctools.design.project.dto.DesignRoleSettingDto;
import cn.bctools.design.project.dto.JvsAppSaveDto;
import cn.bctools.design.project.dto.ProgressDto;
import cn.bctools.design.project.entity.JvsApp;
import cn.bctools.design.project.entity.JvsAppManageInfo;
import cn.bctools.design.project.entity.JvsAppType;
import cn.bctools.design.project.entity.dto.AppRoleDto;
import cn.bctools.design.project.entity.vo.*;
import cn.bctools.design.project.service.JvsAppManageInfoService;
import cn.bctools.design.project.service.JvsAppService;
import cn.bctools.design.project.service.JvsAppTemplateService;
import cn.bctools.design.project.service.JvsAppTypeService;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.log.annotation.Log;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.oss.template.OssTemplate;
import cn.bctools.redis.utils.RedisUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Generator
 */
@Slf4j
@Api(tags = "应用基础信息")
@RestController
@AllArgsConstructor
@RequestMapping("/base/JvsApp")
public class JvsAppBaseController {

    JvsAppService service;
    RedisUtils redisUtils;
    JvsAppTemplateService templateService;
    AppMenuTypeService appMenuTypeService;
    JvsAppTypeService appTypeService;
    JvsAppManageInfoService manageInfoService;
    OssTemplate ossTemplate;

    final static String CLICK_KEY = "jvs:design:app:user_click:";

    @ApiOperation("应用-获取名称")
    @GetMapping("/name/{appId}")
    public R page(@PathVariable String appId) {
        JvsApp one = service.getOne(Wrappers.query(new JvsApp().setId(appId)).lambda().select(JvsApp::getName));
        return R.ok(one);
    }

    @ApiOperation("应用-进度查询")
    @GetMapping("/schedule/{templateId}")
    public R schedule(@PathVariable String templateId) {
        ProgressDto o = (ProgressDto) redisUtils.get("jvsAppId::sync:progress" + UserCurrentUtils.getUserId() + UserCurrentUtils.getCurrentUser().getTenantId() + templateId);
        return R.ok(o);
    }

    @Log
    @ApiOperation("应用-新增")
    @PostMapping("/save")
    public R save(@RequestBody JvsAppSaveDto saveDto) {
        //检验唯一标识是否重复
        if (ObjectNull.isNotNull(saveDto.getIdentification())) {
            Boolean flag = service.checkIdentification(new CheckAppIdentificationVo().setIdentification(saveDto.getIdentification()));
            if (!flag) {
                return R.failed("唯一标识重复");
            }
        }
        //如果模板ID不为空，则从模板ID创建应用
        String templateId = saveDto.getTemplateId();
        TenantContextHolder.setTenantId(UserCurrentUtils.getCurrentUser().getTenantId());
        if (ObjectNull.isNotNull(templateId)) {
            //直接解析
            return R.ok(templateService.create(templateId));
        }

        JvsApp app = BeanCopyUtil.copy(saveDto, JvsApp.class);
        app.setSecret(JvsAppSecretUtils.getAppSecret(IdGenerator.getIdStr()));
        //创建应用的时候默认为false
        app.setIsDeploy(false);
        // 设置默认权限
        app.setDefaultRole();

        // 开放平台上架应用（发布状态）
        if (saveDto.getIsLogicApply()) {
            if (ObjectNull.isNull(saveDto.getClientId())) {
                throw new BusinessException("应用授权appKey不能为空");
            }
            app.setAuthorizationType((short) 2);
            app.setClientId(saveDto.getClientId());
            app.setIsDeploy(true);
        }

        service.save(app);
        //创建应用管理信息
        manageInfoService.save(new JvsAppManageInfo().setAppId(app.getId()));
        // 创建默认目录
        appMenuTypeService.save(new AppMenuType().setType("未命名目录").setSort(0).setJvsAppId(app.getId()));
        DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(app.getId()).setJvsAppName(app.getName()));
        //TODO 远程调用新增sys_apply表记录
        return R.ok(app);
    }

    @ApiOperation("应用-分页")
    @GetMapping("/page")
    public R<Page<AppDto>> page(Page<JvsApp> page, JvsApp dto) {

        List<JvsApp> pagedApps = getPage(page, dto, false);

        return handlePageRtn(page, pagedApps);
    }

    @NotNull
    private R<Page<AppDto>> handlePageRtn(Page<JvsApp> page, List<JvsApp> pagedApps) {
        Page<AppDto> pageDto = new Page<>(page.getCurrent(), page.getSize());
        if (ObjectNull.isNull(pagedApps)) {
            return R.ok(pageDto);
        }
        List<String> typeIds = pagedApps.stream().map(JvsApp::getType).collect(Collectors.toList());
        Map<String, JvsAppType> typeMap = new HashMap<>();
        if (ObjectNull.isNotNull(typeIds)) {
            List<JvsAppType> jvsAppTypes = appTypeService.listByIds(typeIds);
            typeMap = jvsAppTypes.stream().collect(Collectors.toMap(JvsAppType::getId, Function.identity()));
        }
        Map<String, JvsAppType> finalTypeMap = typeMap;
        List<AppDto> pageDtos = pagedApps.stream().map(app -> {
            AppDto appDto = convertAppRole(app);
            appDto.setRole(null);
            JvsAppType appType = finalTypeMap.get(app.getType());
            return handleAppDto(app, appDto, appType);
        }).collect(Collectors.toList());

        pageDto.setRecords(pageDtos);
        pageDto.setTotal(page.getTotal());
        return R.ok(pageDto);
    }

    @NotNull
    private AppDto handleAppDto(JvsApp app, AppDto appDto, JvsAppType appType) {
        if (ObjectNull.isNotNull(appType)) {
            appDto.setJvsAppType(appType);
            appDto.setTypeTxt(appType.getName());
        } else {
            appDto.setType(null);
            appDto.setTypeTxt("");
        }
        String logo = ossTemplate.handlePublicFileUrl(app.getLogo());
        appDto.setLogo(logo);
        return appDto;
    }

    //获取点击次数map
    private Map<String, Double> getUserClickCounts(String redisKey, List<JvsApp> apps) {
        Map<String, Double> clickCounts = new HashMap<>();
        List<String> appIds = apps.stream().map(JvsApp::getId).collect(Collectors.toList());
        List<Double> zSetScores = redisUtils.getZSetScores(redisKey, appIds);
        for (int i = 0; i < appIds.size(); i++) {
            clickCounts.put(appIds.get(i), zSetScores.get(i));
        }
        return clickCounts;
    }

    @ApiOperation("应用-详情")
    @GetMapping("/{appId}/detail")
    public R<AppDto> detail(@PathVariable String appId) {
        JvsApp one = service.getById(appId);
        AppDto appDto = convertAppRole(one);
        appDto.setSecret(null);
        appDto.setLogo(ossTemplate.handlePublicFileUrl(one.getLogo()));
        return R.ok(appDto);
    }

    private AppDto convertAppRole(JvsApp app) {
        UserDto userDto = UserCurrentUtils.getCurrentUser();
        AppDto appDto = BeanCopyUtil.copy(app, AppDto.class);
        AppRoleDto appRole = Optional.ofNullable(appDto.getRole()).orElseGet(AppRoleDto::new);
        // 当前用户是否是应用管理员
        if (userDto.getId().equals(appDto.getCreateById()) ||
                userDto.getAdminFlag() ||
                service.checkRole(appRole.getAdminMember(), UserCurrentUtils.getCurrentUser())) {
            appDto.getAppRoles().add(Get.name(AppRoleDto::getAdminMember));
            appDto.setViewStatus(true);
        }
        // 当前用户是否是开发人员
        if (service.checkRole(appRole.getDevMember(), UserCurrentUtils.getCurrentUser())) {
            appDto.getAppRoles().add(Get.name(AppRoleDto::getDevMember));
            appDto.setViewStatus(true);
        }
        // 当前用户是否应用可访问角色
        if (service.checkViewStatus(appRole.getAppMember(), UserCurrentUtils.getCurrentUser())) {
            appDto.setViewStatus(true);
        }
        return appDto;
    }

    @ApiOperation("应用-列表")
    @GetMapping("/list")
    public R<Page<JvsApp>> detail(Page<JvsApp> page) {
        LambdaQueryWrapper<JvsApp> wrapper = new LambdaQueryWrapper<JvsApp>()
                .select(JvsApp::getId, JvsApp::getName).orderByDesc(JvsApp::getCreateTime);
        service.page(page, wrapper);
        //刷新logo外链
        page.getRecords().forEach(e -> {
            e.setLogo(ossTemplate.handlePublicFileUrl(e.getLogo()));
        });
        return R.ok(page);
    }

    @Log(back = false)
    @ApiOperation("应用-记录用户点击次数")
    @PostMapping("/click/{appId}")
    public R recordUserClickApp(@PathVariable(value = "appId") String appId) {
        String userId = UserCurrentUtils.getUserId();
        String redisKey = CLICK_KEY + userId;

        // 增加点击次数
        redisUtils.incrZsetScore(redisKey, appId, 1.0);

        return R.ok();
    }

    @ApiOperation("用户应用-分页")
    @GetMapping("/userPage")
    public R<Page<AppDto>> userPage(Page<JvsApp> page, JvsApp dto) {
        List<JvsApp> pagedApps = getPage(page, dto, true);

        Page<AppDto> pageDto = new Page<>(page.getCurrent(), page.getSize());
        if (ObjectNull.isNull(pagedApps)) {
            return R.ok(pageDto);
        }

        List<AppDto> pageDtos = pagedApps.stream().map(app -> {
            AppDto appDto = convertAppRole(app);
            String logo = ossTemplate.handlePublicFileUrl(app.getLogo());
            appDto.setLogo(logo);
            return appDto;
        }).collect(Collectors.toList());

        pageDto.setRecords(pageDtos);
        pageDto.setTotal(page.getTotal());
        return R.ok(pageDto);
    }

    @ApiOperation("应用分类-应用列表")
    @GetMapping("/appTypeList/{appTypeId}")
    public R<AppTypeListRtnVo> appTypeList(Page<JvsApp> page, @PathVariable(value = "appTypeId") String appTypeId) {
        JvsAppType appType = appTypeService.getById(appTypeId);
        if (ObjectNull.isNull(appType)) {
            throw new BusinessException("分类不存在");
        }

        AppTypeListRtnVo appTypeListRtnVo = new AppTypeListRtnVo();
        appTypeListRtnVo.setTypeInfo(appType);
        Page<AppDto> pageDto = new Page<>(page.getCurrent(), page.getSize());
        pageDto.setTotal(0);
        appTypeListRtnVo.setAppPage(pageDto);

        List<JvsApp> pagedApps = getPage(page, new JvsApp().setType(appTypeId), false);

        if (ObjectNull.isNull(pagedApps)) {
            return R.ok(appTypeListRtnVo);
        }

        List<AppDto> pageDtos = pagedApps.stream().map(app -> {
            AppDto appDto = convertAppRole(app);
            appDto.setTypeTxt(appType.getName());
            appDto.setRole(null);
            appDto.setLogo(ossTemplate.handlePublicFileUrl(app.getLogo()));
            return appDto;
        }).collect(Collectors.toList());

        pageDto.setRecords(pageDtos);
        pageDto.setTotal(page.getTotal());
        return R.ok(appTypeListRtnVo);
    }


    private List<JvsApp> getPage(Page<JvsApp> page, JvsApp dto, Boolean flag) {
        // 必须是应用管理员
        LambdaQueryWrapper<JvsApp> queryWrapper = new LambdaQueryWrapper<JvsApp>()
                .select(JvsApp::getId,
                        JvsApp::getName,
                        JvsApp::getCreateTime,
                        JvsApp::getCreateById,
                        JvsApp::getIsDeploy,
                        JvsApp::getSecret,
                        JvsApp::getDescription,
                        JvsApp::getLogo,
                        JvsApp::getRole,
                        JvsApp::getType,
                        JvsApp::getAuthorizationType,
                        JvsApp::getClientId,
                        JvsApp::getIdentification
                )
                .like(ObjectNull.isNotNull(dto.getName()), JvsApp::getName, dto.getName())
                .eq(ObjectNull.isNotNull(dto.getIsDeploy()), JvsApp::getIsDeploy, dto.getIsDeploy())
                .eq(ObjectNull.isNotNull(dto.getType()), JvsApp::getType, dto.getType())
                .orderByDesc(JvsApp::getCreateTime);

        //不管是不是超级管理员,都只能看自己有权限的,其它的不能查看
        List<String> roleIds = UserCurrentUtils.getRole();
        // 根据用户id查询
        JSONObject conditionUser = new JSONObject();
        conditionUser.put("type", "user");
        conditionUser.put("id", UserCurrentUtils.getUserId());
        String conditionUserJson = JSON.toJSONString(conditionUser);
        queryWrapper.and(wrapper -> wrapper.eq(JvsApp::getCreateById, UserCurrentUtils.getUserId())
                // 查询当前用户作为应用管理员的应用
                .or(orUser -> orUser.apply(SqlFunctionUtil.jsonContainsObject(Get.name(JvsApp::getRole), "$.adminMember", conditionUserJson)))
                // 查询当前用户作为应用开发人员的应用
                .or(orUser -> orUser.apply(SqlFunctionUtil.jsonContainsObject(Get.name(JvsApp::getRole), "$.devMember", conditionUserJson)))
                .or(flag, orUser -> orUser.nested(i -> {
                    for (String roleId : roleIds) {
                        JSONObject conditionRole = new JSONObject();
                        conditionRole.put("type", "role");
                        conditionRole.put("id", roleId);
                        String conditionRoleJson = JSON.toJSONString(conditionRole);
                        i.or(app -> app.apply(SqlFunctionUtil.jsonContainsObject(Get.name(JvsApp::getRole), "$.appMember", conditionRoleJson)));
                    }
                }))
        );


        List<JvsApp> apps = service.list(queryWrapper);

        if (ObjectNull.isNull(apps)) {
            return new ArrayList<>();
        }

        // 获取当前用户点击次数
        String userId = UserCurrentUtils.getUserId();
        String redisKey = CLICK_KEY + userId;
        Map<String, Double> clickCounts = getUserClickCounts(redisKey, apps);

        //排序
        List<JvsApp> sortedApps = apps.stream()
                .sorted((app1, app2) -> clickCounts.getOrDefault(app2.getId(), 0.0)
                        .compareTo(clickCounts.getOrDefault(app1.getId(), 0.0)))
                .collect(Collectors.toList());

        // 分页处理
        int start = (int) ((page.getCurrent() - 1) * page.getSize());
        int end = Math.min(start + (int) page.getSize(), sortedApps.size());
        page.setTotal(sortedApps.size());
        return sortedApps.subList(start, end);
    }

    @ApiOperation("获取可注册应用列表")
    @GetMapping("/register/appList")
    public R<List<JvsApp>> getRegisterAppList(RegisterAppListReqVo reqVo) {
        return R.ok(service.getRegisterAppList(reqVo));
    }

    @Log
    @ApiOperation("应用管理-新增应用")
    @PostMapping("/manage")
    public R<Boolean> saveManage(@RequestBody AppManageSaveReqVo vo) {
        return R.ok(service.saveManage(vo));
    }

    @Log
    @ApiOperation("应用管理-删除应用")
    @DeleteMapping("/manage")
    public R<Boolean> managePage(@RequestBody IdsVo ids) {
        return R.ok(service.delManage(ids));
    }

    @Log(back = false)
    @ApiOperation("应用管理-分页列表")
    @GetMapping("/manage/page")
    public R<Page<AppManageInfoVo>> managePage(Page<AppManageInfoVo> page, AppManagePageReqVo vo) {
        return R.ok(service.getManagePage(page, vo));
    }

    @Log
    @ApiOperation("应用管理-编辑应用")
    @PutMapping("/manage")
    public R<Boolean> editManage(@RequestBody AppManageEditReqVo vo) {
        return R.ok(service.editManage(vo));
    }

    @Log(back = false)
    @ApiOperation("应用管理-详情")
    @GetMapping("/manage/info/{appId}")
    public R<AppManageInfoVo> manageInfo(@PathVariable("appId") String id) {
        return R.ok(service.getManageInfo(id));
    }

    @ApiOperation("应用管理-应用选择列表")
    @GetMapping("/manage")
    public R<List<AppManageInfoVo>> manageAppList() {
        return R.ok(service.manageAppList());
    }

    @Log(back = false)
    @ApiOperation("根据clientId获取应用")
    @GetMapping("/getByClientId")
    public R<Boolean> getByClientId(@RequestParam String clientId) {
        JvsApp one = service.getOne(new LambdaQueryWrapper<JvsApp>().eq(JvsApp::getClientId, clientId)
                .last("LIMIT 1"));
        if (ObjectNull.isNull(one)) {
            return R.failed(false, "应用不存在");
        }
        AppDto appDto = convertAppRole(one);
        appDto.setSecret(null);
        return R.ok(true);
    }

    @Log(callBackClass = JvsLogServiceImpl.class)
    @ApiOperation(value = "新增应用-同步开放平台", notes = "应用不存在时新增，存在则更新应用信息")
    @PostMapping("/syncFromOpenPlatform")
    public R syncFromOpenPlatform(@RequestBody JvsAppSaveDto saveDto) {
        //使用identification作为id，identification的值对应开发平台的应用id
        JvsApp one = service.getOne(new LambdaQueryWrapper<JvsApp>().eq(JvsApp::getId, saveDto.getIdentification())
                .last("LIMIT 1"));
        if (ObjectNull.isNull(one)) {
            //新增应用
            //如果模板ID不为空，则从模板ID创建应用
            String templateId = saveDto.getTemplateId();
            TenantContextHolder.setTenantId(UserCurrentUtils.getCurrentUser().getTenantId());
            if (ObjectNull.isNotNull(templateId)) {
                //直接解析
                return R.ok(templateService.create(templateId));
            }

            JvsApp app = BeanCopyUtil.copy(saveDto, JvsApp.class);
            app.setId(saveDto.getIdentification());
            //创建应用的时候默认为false
            app.setIsDeploy(false);
            // 设置默认权限
            app.setDefaultRole();

            // 开放平台上架应用（发布状态）
            if (saveDto.getIsLogicApply()) {
                if (ObjectNull.isNull(saveDto.getClientId())) {
                    throw new BusinessException("应用授权appKey不能为空");
                }
                app.setAuthorizationType((short) 2);
                app.setClientId(saveDto.getClientId());
                app.setSecret(saveDto.getClientSecret());
                app.setIsDeploy(true);
            }
            service.save(app);
            //创建应用管理信息
            manageInfoService.save(new JvsAppManageInfo().setAppId(app.getId()));
            // 创建默认目录
            appMenuTypeService.save(new AppMenuType().setType("未命名目录").setSort(0).setJvsAppId(app.getId()));
            DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(app.getId()).setJvsAppName(app.getName()));
            return R.ok(app);
        } else {
            //更新应用
            JvsApp app = new JvsApp();
            app.setId(saveDto.getIdentification());
            app.setIsDeploy(true);
            service.updateById(app);
            return R.ok();
        }
    }
}