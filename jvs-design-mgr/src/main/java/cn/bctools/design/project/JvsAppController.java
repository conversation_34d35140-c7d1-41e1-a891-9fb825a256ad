package cn.bctools.design.project;

import cn.bctools.auth.api.api.AuthRoleServiceApi;
import cn.bctools.auth.api.dto.RoleChargeDto;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import cn.bctools.design.chart.service.ChartService;
import cn.bctools.design.crud.service.AppUrlService;
import cn.bctools.design.crud.service.CrudPageService;
import cn.bctools.design.crud.service.FormService;
import cn.bctools.design.h5.service.H5DesignService;
import cn.bctools.design.identification.entity.Identification;
import cn.bctools.design.identification.service.IdentificationService;
import cn.bctools.design.jvslog.entity.JvsLog;
import cn.bctools.design.jvslog.service.JvsLogService;
import cn.bctools.design.jvslog.service.impl.JvsLogServiceImpl;
import cn.bctools.design.menu.component.AppMenuHandler;
import cn.bctools.design.menu.service.AppMenuService;
import cn.bctools.design.menu.service.AppMenuTypeService;
import cn.bctools.design.project.dto.DesignRoleSettingDto;
import cn.bctools.design.project.dto.SortDto;
import cn.bctools.design.project.entity.JvsApp;
import cn.bctools.design.project.entity.JvsAppManageInfo;
import cn.bctools.design.project.entity.vo.CheckAppIdentificationVo;
import cn.bctools.design.project.handler.DesignHandler;
import cn.bctools.design.project.service.JvsAppManageInfoService;
import cn.bctools.design.project.service.JvsAppService;
import cn.bctools.design.project.service.JvsAppTemplateService;
import cn.bctools.design.screen.service.ScreenService;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.log.annotation.Log;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.redis.utils.RedisUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> Generator
 */
@Slf4j
@Api(tags = "应用管理")
@RestController
@AllArgsConstructor
@RequestMapping("/app/manage/{appId}")
public class JvsAppController {
    AuthRoleServiceApi roleServiceApi;
    JvsAppService service;
    DesignHandler designHandler;
    IdentificationService identificationService;
    JvsLogService jvsLogService;
    AppMenuHandler appMenuHandler;
    JvsAppManageInfoService manageInfoService;


    @Log(callBackClass = JvsLogServiceImpl.class)
    @ApiOperation("应用-发布")
    @PutMapping("/deploy")
    public R<Boolean> deploy(@PathVariable("appId") String appId) {
        service.deploy(appId);
        return R.ok(true);
    }

    @Log(callBackClass = JvsLogServiceImpl.class)
    @ApiOperation("应用-卸载")
    @PutMapping("/unload")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> unload(@PathVariable("appId") String appId) {
        JvsApp byId = service.getById(appId);
        DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(appId).setJvsAppName(byId.getName()));
        service.unload(appId);
        return R.ok(true);
    }

    @ApiOperation("应用-详情")
    @GetMapping("/detail")
    public R<JvsApp> detail(@PathVariable String appId) {
        JvsApp one = service.getById(appId);
        one.setSecret(null);
        return R.ok(one);
    }

    @Log(back = false)
    @ApiOperation("修改详细描述")
    @PutMapping("/text")
    public R<JvsApp> text(@PathVariable String appId, @RequestBody Map<String, String> body) {
        JvsApp byId = service.getById(appId);
        byId.setLongText(body.getOrDefault("body", ""));
        service.updateById(byId);
        return R.ok(byId);
    }

    @ApiOperation("应用-获取密钥")
    @GetMapping("/secret")
    public R<String> secret(@PathVariable("appId") String appId) {
        JvsApp jvsApp = service.getById(appId);
        if (UserCurrentUtils.getCurrentUser().getAdminFlag()) {
            return R.ok(jvsApp.getSecret());
        }
        return R.failed("权限不足,请联系管理员获取");
    }

    @Log(callBackClass = JvsLogServiceImpl.class)
    @ApiOperation("应用-修改")
    @PutMapping("/edit")
    public R<JvsApp> edit(@RequestBody JvsApp jvsApp, @PathVariable String appId) {
        jvsApp.setId(appId);
        Boolean flag = service.checkIdentification(new CheckAppIdentificationVo().setAppId(appId)
                .setIdentification(jvsApp.getIdentification()));
        if (!flag) {
            return R.failed("唯一标识重复");
        }
        // 主应用管理员至少有一个
        if (ObjectNull.isNotNull(jvsApp.getRole()) && Optional.ofNullable(jvsApp.getRole().getAdminMember()).orElseGet(ArrayList::new).isEmpty()) {
            jvsApp.setRole(null);
        }
        if (ObjectNull.isNull(jvsApp.getIdentification())){
            jvsApp.setIdentification("");
        }
        if ("EMPTY_TYPE".equals(jvsApp.getType())) {
            jvsApp.setType("");
        }
        service.updateById(jvsApp);
        //更新角色范围表
        if (ObjectNull.isNotNull(jvsApp.getRole())) {
            roleServiceApi.updateRoleCharge(new RoleChargeDto().setAppId(appId).setRoles(jvsApp.getRole().getAppMember()));
        }
        DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(jvsApp.getId()).setJvsAppName(jvsApp.getName()));
        return R.ok(jvsApp);
    }

    @Log(back = false)
    @ApiOperation("应用-日志")
    @GetMapping("/jvsLog/page")
    public R<Page<JvsLog>> page(Page<JvsLog> page, JvsLog dto, @PathVariable String appId) {
        jvsLogService.page(page, Wrappers.query(dto.setJvsAppId(appId)).orderBy(true, false, "create_time"));
        return R.ok(page);
    }

    @Log(callBackClass = JvsLogServiceImpl.class)
    @ApiOperation("应用-删除")
    @DeleteMapping("/del")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> remove(@PathVariable("appId") String appId) {
        JvsApp jvsApp = service.getById(appId);
        String userId = UserCurrentUtils.getUserId();
        boolean withPermission = userId.equals(jvsApp.getCreateById()) ||
                UserCurrentUtils.getCurrentUser().getAdminFlag() ||
                (ObjectNull.isNotNull(jvsApp.getRole()) && Optional.ofNullable(jvsApp.getRole().getAdminMember())
                        .orElseGet(ArrayList::new)
                        .stream()
                        .anyMatch(m -> userId.equals(m.getId())));
        if (withPermission) {
            designHandler.beforeDeleted(appId);
            DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(jvsApp.getId()).setJvsAppName(jvsApp.getName()));
            //删除标识
            identificationService.remove(Wrappers.query(new Identification().setJvsAppId(jvsApp.getId())));
            //删除应用管理信息
            manageInfoService.remove(new LambdaQueryWrapper<JvsAppManageInfo>().eq(JvsAppManageInfo::getAppId, jvsApp.getId()));
            //删除应用对应角色范围
            roleServiceApi.delRoleCharge(appId);
            return R.ok(service.removeById(appId));
        } else {
            return R.failed("没有权限无法删除应用");
        }
    }

    @Log
    @ApiOperation("应用-排序")
    @PostMapping("/sort")
    public R<String> sort(@PathVariable("appId") String appId, @Validated @RequestBody SortDto dto) {
        appMenuHandler.sort(appId, dto.getToMenuCode(), dto.getSortList());
        return R.ok();
    }

}