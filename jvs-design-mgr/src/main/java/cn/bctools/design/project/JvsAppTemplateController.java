package cn.bctools.design.project;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import cn.bctools.common.utils.function.Get;
import cn.bctools.design.jvslog.service.impl.JvsLogServiceImpl;
import cn.bctools.design.project.dto.DesignRoleSettingDto;
import cn.bctools.design.project.entity.JvsAppTemplate;
import cn.bctools.design.project.entity.JvsAppTemplateData;
import cn.bctools.design.project.mapper.JvsAppTemplateDataMapper;
import cn.bctools.design.project.service.JvsAppTemplateService;
import cn.bctools.design.project.utils.JvsAppTemplateUtils;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.log.annotation.Log;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "应用模板")
@RestController
@RequestMapping("/base/JvsAppTemplate")
public class JvsAppTemplateController {

    /**
     * 返回推荐应用数量上限
     */
    private static final Integer RECOMMEND_MAX = 20;

    @Value("${version}")
    String version;

    JvsAppTemplateService templateService;

    JvsAppTemplateDataMapper templateDataMapper;

    public JvsAppTemplateController(JvsAppTemplateService templateService, JvsAppTemplateDataMapper templateDataMapper) {
        this.templateService = templateService;
        this.templateDataMapper = templateDataMapper;
    }

    @ApiOperation("获取banner")
    @GetMapping("/banner")
    public R<List<JvsAppTemplate>> banner() {
        List<JvsAppTemplate> list = getList(new LambdaQueryWrapper<JvsAppTemplate>()
                .isNotNull(JvsAppTemplate::getBanner)
                .eq(JvsAppTemplate::getDeploy, true)
                .select(JvsAppTemplate::getId, JvsAppTemplate::getBanner));
        return R.ok(list);
    }

    @ApiOperation("推荐应用")
    @GetMapping("/recommend")
    public R<List<JvsAppTemplate>> recommend(@RequestParam("size") int size) {
        if (size >= RECOMMEND_MAX) {
            size = 20;
        }
        List<JvsAppTemplate> list = getList(new LambdaQueryWrapper<JvsAppTemplate>()
                .eq(JvsAppTemplate::getDeploy, true)
                .eq(JvsAppTemplate::getRecommend, true)
                .select(JvsAppTemplate::getId, JvsAppTemplate::getName, JvsAppTemplate::getLogo, JvsAppTemplate::getBriefDescription)
                .orderByDesc(JvsAppTemplate::getCreateTime)
                //只显示前10个
                .last(" limit " + size));
        if (ObjectNull.isNotNull(list)) {
            //如果没有则默认返回 10个
            list = getList(new LambdaQueryWrapper<JvsAppTemplate>()
                    .select(JvsAppTemplate::getId, JvsAppTemplate::getName, JvsAppTemplate::getLogo, JvsAppTemplate::getBriefDescription)
                    .orderByDesc(JvsAppTemplate::getCreateTime)
                    //只显示前10个
                    .last(" limit " + size));
        }
        return R.ok(list);
    }

    private List<JvsAppTemplate> getList(LambdaQueryWrapper<JvsAppTemplate> size) {
        return templateService.list(size);
    }

    @ApiOperation("获取类型")
    @GetMapping("/types")
    public R types() {
        Boolean equals = true;
        List<String> collect = getList(new LambdaQueryWrapper<JvsAppTemplate>().select(JvsAppTemplate::getType))
                .stream()
                .map(JvsAppTemplate::getType)
                .distinct()
                .collect(Collectors.toList());
        return R.ok(collect);
    }

    @ApiOperation("查询模板详情")
    @GetMapping("/detail/{templateId}")
    public R getDetail(@PathVariable("templateId") String templateId) {
        Boolean equals = true;
        if (equals) {
            JvsAppTemplate template = templateService.getOne(new LambdaQueryWrapper<JvsAppTemplate>().select(JvsAppTemplate.class, e -> !e.getColumn().equals(Get.name(JvsAppTemplate::getData))).eq(JvsAppTemplate::getId, (templateId)));
            return R.ok(template);
        } else {
            //获取体验环境所有的类型
            return JvsAppTemplateUtils.get(HttpUtil.createGet(JvsAppTemplateUtils.DOMAIN + "/JvsAppTemplate/detail/" + templateId));
        }
    }

    @ApiOperation("获取模板数据")
    @GetMapping("/data/{templateId}")
    public R getData(@PathVariable("templateId") String templateId) {
        Boolean equals = true;
        if (equals) {
            JvsAppTemplate template = templateService.getOne(Wrappers.query(new JvsAppTemplate().setId(templateId)));
            if (Objects.isNull(template)) {
                return R.ok(null);
            }
            //如果是免费的直接返回，收费的不返回
            if (template.getFree()) {
                return R.ok(template);
            }
            //获取数据
            template.setData(templateService.getData(template.getId()));
            return R.ok(template);
        } else {
            //获取体验环境所有的类型
            return JvsAppTemplateUtils.get(HttpUtil.createGet(JvsAppTemplateUtils.DOMAIN + "/JvsAppTemplate/detail/" + templateId));
        }
    }

    @ApiOperation("查询所有模板")
    @GetMapping("/list")
    public Object page(Page<JvsAppTemplate> page, JvsAppTemplate dto) {
        dto.setSize(null);
        LambdaQueryWrapper<JvsAppTemplate> jvsAppTemplateLambdaQueryWrapper = new LambdaQueryWrapper<JvsAppTemplate>()
                .eq(ObjectNull.isNotNull(dto.getType()), JvsAppTemplate::getType, dto.getType())
                .like(ObjectNull.isNotNull(dto.getName()), JvsAppTemplate::getName, dto.getName());
        jvsAppTemplateLambdaQueryWrapper.select(JvsAppTemplate::getId,
                JvsAppTemplate::getName,
                JvsAppTemplate::getCreateTime,
                JvsAppTemplate::getType,
                JvsAppTemplate::getSize,
                JvsAppTemplate::getIcon,
                JvsAppTemplate::getDescription,
                JvsAppTemplate::getImgs,
                JvsAppTemplate::getLogo,
                JvsAppTemplate::getBanner,
                JvsAppTemplate::getVersion);

        jvsAppTemplateLambdaQueryWrapper.orderByDesc(JvsAppTemplate::getCreateTime);
        Boolean equals = true;
        if (!equals) {
            //如果不是平台管理员，只看已经发布的。是平台管理员看所有的
            //获取体验平台的应用数据
            jvsAppTemplateLambdaQueryWrapper.eq(JvsAppTemplate::getDeploy, true);
        }
        templateService.page(page, jvsAppTemplateLambdaQueryWrapper);
        return R.ok(page);

    }

    @ApiOperation("发布原生应用到模板")
    @PostMapping("/deploy/primitive")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> primitive(@RequestBody JvsAppTemplate jvsAppTemplate) {
        jvsAppTemplate.setPrimitive(true);
        templateService.save(jvsAppTemplate);
        return R.ok(true);
    }

    @SneakyThrows
    @ApiOperation("发布到模板")
    @PostMapping("/deploy")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> deploy(@RequestBody JvsAppTemplate jvsAppTemplate) {
        DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(jvsAppTemplate.getId()).setJvsAppName(jvsAppTemplate.getName()));
        //只支持在线服务
        //获取这个应用下所有的数据
        String designData = templateService.getDesignData(jvsAppTemplate);
        jvsAppTemplate.setData(designData);
        jvsAppTemplate.setCreateTime(LocalDateTime.now());
        jvsAppTemplate.setId(null);
        jvsAppTemplate.setVersion(version);

        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule simpleModule = new SimpleModule();
        //  LocalDateTime时间格式化
        simpleModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        objectMapper.registerModule(simpleModule);
        String json = objectMapper.writeValueAsString(jvsAppTemplate);
        jvsAppTemplate.setSize(json.getBytes().length);
        String[] split = StrUtil.split(json, 1024 * 100 * 2);
        jvsAppTemplate.setData(null);
        templateService.save(jvsAppTemplate);
        for (int i = 0; i < split.length; i++) {
            //将数据拆分入库，避免太大了
            templateDataMapper.insert(new JvsAppTemplateData().setData(split[i]).setSort(i).setTemplateId(jvsAppTemplate.getId()));
        }
        return R.ok(true);
    }

    @ApiOperation("上传模板中心")
    @PostMapping("/client/template")
    @Transactional(rollbackFor = Exception.class)
    public R client(@RequestBody JvsAppTemplate jvsAppTemplate) {
        String body = jvsAppTemplate.getData();
        jvsAppTemplate.setSize(body.getBytes().length);
        jvsAppTemplate.setDeploy(false);
        jvsAppTemplate.setData(null);
        String[] split = StrUtil.split(body, 1024 * 100 * 2);
        templateService.save(jvsAppTemplate);
        for (int i = 0; i < split.length; i++) {
            //将数据拆分入库，避免太大了
            templateDataMapper.insert(new JvsAppTemplateData().setData(split[i]).setSort(i).setTemplateId(jvsAppTemplate.getId()));
        }
        return R.ok(true, "编辑成功");
    }

    @ApiOperation("模板-编辑")
    @PutMapping
    @Transactional(rollbackFor = Exception.class)
    public R put(@RequestBody JvsAppTemplate jvsAppTemplate) {
        templateService.updateById(jvsAppTemplate);
        return R.ok(true, "编辑成功");
    }

    @Log(callBackClass = JvsLogServiceImpl.class)
    @ApiOperation("模板-删除")
    @DeleteMapping("/del/{templateId}")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> delete(@PathVariable("templateId") String templateId) {
        JvsAppTemplate jvsAppTemplate = templateService.getById(templateId);
        if (ObjectNull.isNull(jvsAppTemplate)) {
            return R.ok();
        }
        DynamicDataUtils.setDto(new DesignRoleSettingDto().setJvsAppId(jvsAppTemplate.getId()).setJvsAppName(jvsAppTemplate.getName()));
        templateService.removeById(templateId);
        templateDataMapper.delete(Wrappers.query(new JvsAppTemplateData().setTemplateId(templateId)));
        return R.ok(true, "删除成功");
    }

    static String[] list = new String[]{"id", "name", "type", "appIcon", "version", "banner", "imgs", "data"};

    @SneakyThrows
    @ApiOperation("下载模板")
    @GetMapping("/download/{id}")
    public void download(@PathVariable String id, HttpServletResponse response) {
        JvsAppTemplate jvsAppTemplate = templateService.getById(id);
        if (ObjectNull.isNull(jvsAppTemplate)) {
            throw new BusinessException("没有找到模板");
        }
        jvsAppTemplate.setId(null);
        String data = templateService.getData(id);
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=".concat(URLUtil.encode(jvsAppTemplate.getName() + version + ".jvs", StandardCharsets.UTF_8)));
        response.setStatus(HttpStatus.OK.value());
        {
            Map<String, Object> map = BeanCopyUtil.beanToMap(jvsAppTemplate);
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                ServletOutputStream outputStream = response.getOutputStream();
                JsonGenerator jsonGenerator = objectMapper.getFactory().createGenerator(outputStream);
                jsonGenerator.writeStartObject();
                for (int i = 1; i < list.length; i++) {
                    String name = list[i];
                    jsonGenerator.writeFieldName(name);
                    if (name.equals("data")) {
                        jsonGenerator.writeString(data);
                    } else {
                        jsonGenerator.writeObject(map.get(name));
                    }
                }
                jsonGenerator.writeEndObject();
                jsonGenerator.flush();
                jsonGenerator.close();
            } catch (IOException e) {
            }
        }
    }

    @SneakyThrows
    @ApiOperation("上传模板")
    @PostMapping("/fileUpload")
    @Transactional(rollbackFor = Exception.class)
    public R fileUpload(@RequestParam("file") MultipartFile file) {
        //如果是平台管理，则直接将模板上传到应用中心，如果是其它平台，则直接将其上传的模板文件，创建为应用
        byte[] bytes = IoUtil.readBytes(file.getInputStream());
        String s = new String(bytes);
        JvsAppTemplate jvsAppTemplate = null;
        try {
            jvsAppTemplate = JSONObject.parseObject(s, JvsAppTemplate.class);
        } catch (Exception e) {
            return R.failed("文件异常，请使用.jvs的应用文件");
        }
        String body = jvsAppTemplate.getData();
        jvsAppTemplate.setSize(body.getBytes().length);
        jvsAppTemplate.setDeploy(false);
        jvsAppTemplate.setData(null);
        String[] split = StrUtil.split(body, 1024 * 100 * 2);
        templateService.save(jvsAppTemplate);
        for (int i = 0; i < split.length; i++) {
            //将数据拆分入库，避免太大了
            templateDataMapper.insert(new JvsAppTemplateData().setData(split[i]).setSort(i).setTemplateId(jvsAppTemplate.getId()));
        }
        return R.ok();
    }


    @SneakyThrows
    @ApiOperation("上传模板导入应用")
    @PostMapping("/fileUpload/app")
    @Transactional(rollbackFor = Exception.class)
    public R fileUploadApp(@RequestParam("file") MultipartFile file) {
        //如果是平台管理，则直接将模板上传到应用中心，如果是其它平台，则直接将其上传的模板文件，创建为应用
        byte[] bytes = IoUtil.readBytes(file.getInputStream());
        String s = new String(bytes);
        JvsAppTemplate jvsAppTemplate = null;
        try {
            jvsAppTemplate = JSONObject.parseObject(s, JvsAppTemplate.class);
        } catch (Exception e) {
            return R.failed("文件异常，请使用.jvs的应用文件");
        }
        String body = jvsAppTemplate.getData();
        jvsAppTemplate.setSize(body.getBytes().length);
        jvsAppTemplate.setData(null);
        String[] split = StrUtil.split(body, 1024 * 100 * 2);
        templateService.save(jvsAppTemplate);
        for (int i = 0; i < split.length; i++) {
            //将数据拆分入库，避免太大了
            templateDataMapper.insert(new JvsAppTemplateData().setData(split[i]).setSort(i).setTemplateId(jvsAppTemplate.getId()));
        }
        templateService.updateById(jvsAppTemplate);

        //如果是超级管理员，不自动创建应用
        //创建应用，删除模板
        templateService.create(jvsAppTemplate.getId());
        templateService.removeById(jvsAppTemplate.getId());
        templateDataMapper.delete(Wrappers.query(new JvsAppTemplateData().setTemplateId(jvsAppTemplate.getId())));
        return R.ok();
    }

    @SneakyThrows
    @ApiOperation("上传模板更新应用")
    @PostMapping("/fileUpload/appUpdate")
    @Transactional(rollbackFor = Exception.class)
    public R fileUploadAppUpdate(@RequestParam("file") MultipartFile file) {
        //如果是平台管理，则直接将模板上传到应用中心，如果是其它平台，则直接将其上传的模板文件，创建为应用
        byte[] bytes = IoUtil.readBytes(file.getInputStream());
        String s = new String(bytes);
        JvsAppTemplate jvsAppTemplate = null;
        try {
            jvsAppTemplate = JSONObject.parseObject(s, JvsAppTemplate.class);
        } catch (Exception e) {
            return R.failed("文件异常，请使用.jvs的应用文件");
        }
        String body = jvsAppTemplate.getData();
        jvsAppTemplate.setSize(body.getBytes().length);
        jvsAppTemplate.setData(null);
        String[] split = StrUtil.split(body, 1024 * 100 * 2);
        //保存到模板中，相当于备份
        templateService.save(jvsAppTemplate);
        for (int i = 0; i < split.length; i++) {
            //将数据拆分入库，避免太大了
            templateDataMapper.insert(new JvsAppTemplateData().setData(split[i]).setSort(i).setTemplateId(jvsAppTemplate.getId()));
        }
        templateService.updateById(jvsAppTemplate);
        //根据jvsAppId进行判断创建或者更新应用
        templateService.update(jvsAppTemplate.getId());
        return R.ok();
    }
}
