package cn.bctools.design.project;

import cn.bctools.common.utils.R;
import cn.bctools.design.project.entity.JvsAppType;
import cn.bctools.design.project.service.JvsAppTypeService;
import cn.bctools.log.annotation.Log;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "应用分类管理")
@RestController
@AllArgsConstructor
@RequestMapping("/base/JvsApp/type")
public class JvsAppTypeController {
    JvsAppTypeService jvsAppTypeService;

    @ApiOperation("应用分类-列表")
    @GetMapping("/list")
    public R<List<JvsAppType>> list() {
        List<JvsAppType> list = jvsAppTypeService.list();
        return R.ok(list);
    }

    @Log
    @ApiOperation("应用分类-新增")
    @PostMapping("/save")
    public R<Boolean> save(@Validated @RequestBody JvsAppType jvsAppType) {
        return R.ok(jvsAppTypeService.save(jvsAppType));
    }


    @Log
    @ApiOperation("应用分类-编辑")
    @PostMapping("/update")
    public R<Boolean> update(@Validated @RequestBody JvsAppType jvsAppType) {
        return R.ok(jvsAppTypeService.updateById(jvsAppType));
    }

    @Log
    @ApiOperation("应用分类-删除")
    @DeleteMapping("/delete/{id}")
    public R<Boolean> delete(@PathVariable("id") String id) {
        return R.ok(jvsAppTypeService.removeById(id));
    }
}