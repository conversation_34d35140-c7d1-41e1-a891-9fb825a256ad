package cn.bctools.design.report;

import cn.bctools.common.utils.R;
import cn.bctools.design.jvslog.service.impl.JvsLogServiceImpl;
import cn.bctools.design.report.entity.Report;
import cn.bctools.design.report.service.ReportService;
import cn.bctools.log.annotation.Log;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @Author: Zhu<PERSON>iaoKang
 * @Description:
 */
@Api(tags = "[report]报表")
@RestController
@Slf4j
@AllArgsConstructor
@RequestMapping("/app/design/{appId}/report/design")
public class ReportController {

    private final ReportService reportService;

    @Log(callBackClass = JvsLogServiceImpl.class)
    @ApiOperation("创建报表")
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public R<Report> save(@PathVariable String appId, @RequestBody @Valid Report report) {
        reportService.createReport(appId, report);
        return R.ok(report);
    }
}
