package cn.bctools.design.rule.impl.cms;

import cn.bctools.design.local.cms.service.SiteService;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.entity.enums.TestShowEnum;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Order(2)
@Service
@AllArgsConstructor
@Rule(
        value = "站点详情",
        group = RuleGroup.外门户模块,
        test = true,
        testShowEnum = TestShowEnum.TEXT,
        returnType = ClassType.对象,
        explain = "获取外门户站点详情信息"
)
public class SiteInfoServiceImpl implements BaseCustomFunctionInterface<SiteInfoDto> {

    private final SiteService siteService;

    @Override
    public Object execute(SiteInfoDto siteInfoDto, Map<String, Object> params) {
        return siteService.getSiteInfo(siteInfoDto.getSiteId());
    }
}
