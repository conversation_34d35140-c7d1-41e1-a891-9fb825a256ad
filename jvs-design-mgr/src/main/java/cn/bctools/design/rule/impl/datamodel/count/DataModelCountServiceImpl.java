package cn.bctools.design.rule.impl.datamodel.count;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.data.fields.dto.QueryConditionDto;
import cn.bctools.design.data.service.DynamicDataService;
import cn.bctools.design.rule.impl.datamodel.list.DataModelListDto;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.entity.enums.TestShowEnum;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Order(1)
@Service
@AllArgsConstructor
@Rule(
        value = "统计条数",
        group = RuleGroup.模型插件,
        test = true,
        testShowEnum = TestShowEnum.JSON,
        returnType = ClassType.数字,
//        iconUrl = "rule-cishupanduan",
        explain = "数据模型条数"
)
public class DataModelCountServiceImpl implements BaseCustomFunctionInterface<DataModelCountDto> {

    DynamicDataService dynamicDataService;

    @Override
    @SneakyThrows
    public Object execute(DataModelCountDto dataModelDto, Map<String, Object> params) {
        String dataModelId = dataModelDto.getDataModelId();

        DynamicDataUtils.freePermit();
        List<String> fieldList = new ArrayList<>();
        fieldList.add("id");
        List<QueryConditionDto> queryConditions = dataModelDto.getBody();
        Criteria criteria = DynamicDataUtils.buildDynamicCriteria(queryConditions);
        //排除逻辑删除数据
        criteria = DynamicDataUtils.initCriteria(criteria);
        return dynamicDataService.queryList(dataModelId, criteria, fieldList).size();

    }



    @Override
    public void removeKey(Map<String, Object> body) {
        //不做清空操作。直接
    }
}
