package cn.bctools.design.rule.impl.datamodel.delete;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.TenantContextHolder;
import cn.bctools.design.data.fields.enums.DataQueryType;
import cn.bctools.design.data.service.DataModelService;
import cn.bctools.design.data.service.DynamicDataService;
import cn.bctools.design.rule.impl.datamodel.list.DataModelListDto;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.design.notice.handler.DataNoticeHandler;
import cn.bctools.design.notice.handler.enums.TriggerTypeEnum;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.entity.enums.TestShowEnum;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @describe 执行一个查询方法
 */
@Slf4j
@Order(1)
@Service
@AllArgsConstructor
@Rule(
        value = "删除数据",
        group = RuleGroup.模型插件,
        test = true,
        testShowEnum = TestShowEnum.JSON,
        returnType = ClassType.未识别,
//        iconUrl = "rule-piliangshanchu",
        explain = "数据模型删除数据"
)
public class DataModelDeleteServiceImpl implements BaseCustomFunctionInterface<DataModelDeleteDto> {

    DynamicDataService dynamicDataService;
    DataNoticeHandler dataNoticeHandler;
    DataModelService dataModelService;

    @Override
    public void inspect(DataModelDeleteDto o) {
        if (ObjectUtil.isNotNull(o.getIds())){
            o.setIds(o.getIds().stream().filter(StrUtil::isNotBlank).collect(Collectors.toList()));
        }
        if (ObjectNull.isNull(o.getBody()) && ObjectNull.isNull(o.getIds())) {
            throw new BusinessException("删除数据查询条件和ids不能同时为空");
        }
        if (ObjectNull.isNull(o.getIds())&& ObjectNull.isNotNull(o.getBody())){
            o.getBody().forEach(e -> {
                if (!DataQueryType.isNull.equals(e.getEnabledQueryTypes()) && (ObjectNull.isNull(e.getValue()) || (e.getValue() instanceof String && StrUtil.isBlank((String) e.getValue())))) {
                    throw new BusinessException("删除数据查询值不能为空");
                }
            });
        }
    }

    @Override
    @SneakyThrows
    public Object execute(DataModelDeleteDto dataModelDto, Map<String, Object> params) {
        DynamicDataUtils.freePermit();
        String appId = dataModelService.getById(dataModelDto.getDataModelId()).getAppId();
        if (ObjectNull.isNotNull(dataModelDto.getIds())) {
            for (String id : dataModelDto.getIds()) {
                //多条删除
                dynamicDataService.onlyRemove(dataModelDto.getDataModelId(), id);
            }
        } else {
            //查詢所有要删除的数据，发通知,再删除
            List<Object> objects = dynamicDataService.removeMulti(dataModelDto.getDataModelId(), dataModelDto.getBody());
            if (ObjectNull.isNotNull(objects)) {
                objects.forEach(e -> {
                    try {
                        dataNoticeHandler.sendNotify(TenantContextHolder.getTenantId(), appId, TriggerTypeEnum.DELETED, dataModelDto.getDataModelId(), null, (Map) e);
                    } catch (Exception ex) {
                        log.error("通知异常",ex);
                    }
                });
            }
        }
        return "删除成功";
    }

    @Override
    public void removeKey(Map<String, Object> body) {
        //不做清空操作。直接
    }

}