package cn.bctools.design.rule.impl.datamodel.save;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.data.service.DataFieldService;
import cn.bctools.design.data.service.DataModelService;
import cn.bctools.design.data.service.DynamicDataService;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.entity.enums.TestShowEnum;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @author: Aaron2
 */
@Slf4j
@Order(1)
@Service
@AllArgsConstructor
@Rule(
        value = "批量新增数据",
        group = RuleGroup.模型插件,
        test = true,
        testShowEnum = TestShowEnum.JSON,
        returnType = ClassType.文本,
//        iconUrl = "rule-xinzengshujufuwu",
        explain = "数据模型批量新增数据"
)
public class DataModelBatchSaveServiceImpl implements BaseCustomFunctionInterface<DataModelBatchSaveDto> {
    DynamicDataService dynamicDataService;
    DataFieldService fieldService;
    DataModelService dataModelService;

    @Override
    public Object execute(DataModelBatchSaveDto dataModelBatchSaveDto, Map<String, Object> params) {
        String appId = dataModelService.getById(dataModelBatchSaveDto.getDataModelId()).getAppId();
        if (ObjectNull.isNotNull(appId)) {
            List<String> fields = fieldService.getFieldKeys(appId, dataModelBatchSaveDto.getDataModelId());
            if (ObjectNull.isNotNull(dataModelBatchSaveDto.getBody(), fields)) {
                dataModelBatchSaveDto.getBody().forEach(e -> {
                    DynamicDataUtils.clearEchoRecursion(e);
                    e.keySet().removeIf(key -> !fields.contains(key));
                });
            }
            return dynamicDataService.saveBatchTransactionalRule(appId, dataModelBatchSaveDto.getDataModelId(), dataModelBatchSaveDto.getBody());
        }
        return null;
    }
}
