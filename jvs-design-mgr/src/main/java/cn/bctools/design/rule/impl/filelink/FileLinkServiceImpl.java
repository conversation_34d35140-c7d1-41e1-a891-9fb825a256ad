package cn.bctools.design.rule.impl.filelink;

import cn.bctools.oss.template.OssTemplate;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.entity.enums.TestShowEnum;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Order(1)
@Service
@AllArgsConstructor
@Rule(
        value = "获取文件外链",
        group = RuleGroup.常用插件,
        test = true,
        testShowEnum = TestShowEnum.JSON,
        returnType = ClassType.文本,
        explain = "获取文件外链url"
)
public class FileLinkServiceImpl implements BaseCustomFunctionInterface<FileLinkDto> {
    OssTemplate ossTemplate;

    @Override
    public Object execute(FileLinkDto fileLinkDto, Map<String, Object> params) {
        if (Objects.isNull(fileLinkDto.bucketName) || Objects.isNull(fileLinkDto.fileName)) {
            return null;
        }
        return ossTemplate.fileLink(fileLinkDto.fileName, fileLinkDto.bucketName);
    }
}
