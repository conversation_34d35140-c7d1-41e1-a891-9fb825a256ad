package cn.bctools.design.rule.impl.major;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.crud.entity.JvsTree;
import cn.bctools.design.crud.service.JvsTreeService;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.entity.enums.TestShowEnum;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Order(1)
@Service
@AllArgsConstructor
@Rule(
        value = "获取当前专业所属专业大类的唯一标识",
        group = RuleGroup.字典模块,
        test = true,
        testShowEnum = TestShowEnum.JSON,
        returnType = ClassType.文本,
        explain = "获取当前专业所属专业大类的唯一标识"
)
public class MajorGetParentServiceImpl implements BaseCustomFunctionInterface<MajorGetParentDto> {
    JvsTreeService treeService;

    public final static String TYPE = "major";

    @Override
    public Object execute(MajorGetParentDto majorGetParentDto, Map<String, Object> params) {
        if (ObjectNull.isNull(majorGetParentDto.getUniqueName())) {
            return null;
        }

        JvsTree tree = treeService.getOne(new LambdaQueryWrapper<JvsTree>()
                .eq(JvsTree::getUniqueName, majorGetParentDto.getUniqueName())
                .eq(JvsTree::getType, TYPE));
        if (ObjectNull.isNull(tree)) {
            throw new BusinessException("该专业不存在");
        }
        JvsTree parent = treeService.getOne(new LambdaQueryWrapper<JvsTree>().eq(JvsTree::getUniqueName, tree.getParentId()));
        if (ObjectNull.isNotNull(parent)) {
            while (!JvsTree.DICT_ID_ROOT.equals(parent.getParentId())) {
                tree = parent;
                parent = treeService.getOne(new LambdaQueryWrapper<JvsTree>().eq(JvsTree::getUniqueName, tree.getParentId()));
            }
        }
        return tree.getUniqueName();
    }


}
