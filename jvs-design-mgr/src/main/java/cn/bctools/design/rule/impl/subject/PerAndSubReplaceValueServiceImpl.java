package cn.bctools.design.rule.impl.subject;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.crud.aop.TreeCache;
import cn.bctools.design.crud.entity.JvsTree;
import cn.bctools.design.crud.entity.enums.TreeCacheTypeEnum;
import cn.bctools.design.crud.service.JvsTreeService;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.entity.enums.TestShowEnum;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Order(11)
@Service
@AllArgsConstructor
@Rule(
        value = "学段学科字典值转换",
        group = RuleGroup.字典模块,
        test = true,
        testShowEnum = TestShowEnum.JSON,
        returnType = ClassType.对象,
        explain = "不同类型的学段学科字典值转换，若对应字典不存在则转化失败"
)
public class PerAndSubReplaceValueServiceImpl implements BaseCustomFunctionInterface<PerAndSubReplaceValueDto> {

    private final JvsTreeService treeService;

    @Override
    @TreeCache(type = TreeCacheTypeEnum.rule,clazz = String.class)
    public Object execute(PerAndSubReplaceValueDto vo, Map<String, Object> params) {
        if (ObjectNull.isNull(vo.getPerAndSub())) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<JvsTree> queryWrapper = new LambdaQueryWrapper<JvsTree>()
                .select(JvsTree::getUniqueName, JvsTree::getValue, JvsTree::getType)
                .in(JvsTree::getType,
                        Arrays.asList(
                                vo.getPeriodType(),
                                vo.getSubjectType(),
                                vo.getReplacePeriodType(),
                                vo.getReplaceSubjectType()
                        ));

        // 解析数组
        List<String[]> parsedUniqs = vo.getPerAndSub().stream()
                .map(uniq -> uniq.split("-"))
                .filter(uniq -> uniq.length == 2)
                .collect(Collectors.toList());

        List<String> rawType = Arrays.asList(vo.getPeriodType(), vo.getSubjectType());
        ConcurrentMap<String, Map<String, String>> map = treeService.list(queryWrapper)
                .stream()
                .filter(e -> rawType.contains(e.getType()))
                .collect(Collectors.groupingByConcurrent(
                        JvsTree::getType,
                        Collectors.toMap(JvsTree::getUniqueName, JvsTree::getValue)
                ));

        List<String > replaceTypes = Arrays.asList(vo.getReplacePeriodType(), vo.getReplaceSubjectType());
        ConcurrentMap<String, Map<String, String>> replaceMap = treeService.list(queryWrapper)
                .stream()
                .filter(e -> replaceTypes.contains(e.getType()))
                .collect(Collectors.groupingByConcurrent(
                        JvsTree::getType,
                        Collectors.toMap(JvsTree::getValue, JvsTree::getUniqueName)
                ));

        List<String> result = new ArrayList<>(parsedUniqs.size());
        for (String[] uniq : parsedUniqs) {
            String periodValue = map.get(vo.getPeriodType()).get(uniq[0]);
            String subjectValue = map.get(vo.getSubjectType()).get(uniq[1]);
            String replacePeriod = replaceMap.get(vo.getReplacePeriodType()).get(periodValue);
            String replaceSubject = replaceMap.get(vo.getReplaceSubjectType()).get(subjectValue);
            if (replacePeriod != null && replaceSubject != null) {
                result.add(replacePeriod + "-" + replaceSubject);
            }
        }
        return result;
    }

}

