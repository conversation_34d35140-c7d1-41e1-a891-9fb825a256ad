package cn.bctools.design.rule.impl.subject;

import cn.bctools.design.crud.service.JvsTreeService;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.entity.enums.TestShowEnum;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Order(1)
@Service
@AllArgsConstructor
@Rule(
        value = "学段年级映射map",
        group = RuleGroup.字典模块,
        test = true,
        testShowEnum = TestShowEnum.JSON,
        returnType = ClassType.对象,
        explain = "学段年级映射map"
)
public class PeriodGradeMapServiceImpl implements BaseCustomFunctionInterface<PeriodGradeDto> {

    JvsTreeService jvsTreeService;

    @Override
    public Object execute(PeriodGradeDto periodGradeDto, Map<String, Object> params) {

        if (periodGradeDto.getPerAndSubId() == null) {
            return null;
        }

        String[] periodAndSub = periodGradeDto.getPerAndSubId().split("-");
        if (periodAndSub.length != 2) {
            return null;
        }

        return jvsTreeService.getGradeMap(periodAndSub[0]);
    }

}
