package cn.bctools.design.rule.impl.survey;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.design.local.survey.service.SurveySearchApiService;
import cn.bctools.design.local.survey.vo.SurveyApiQueryVO;
import cn.bctools.design.local.survey.vo.SurveyApiViewVO;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.entity.enums.TestShowEnum;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Map;

@Slf4j
@Order(1)
@Service
@AllArgsConstructor
@Rule(
        value = "获取当前用户创建的问卷信息列表",
        group = RuleGroup.问卷模块,
        test = true,
        testShowEnum = TestShowEnum.JSON,
        returnType = ClassType.对象,
        explain = "获取当前用户创建的问卷信息列表"
)
public class SurveyApiListServiceImpl implements BaseCustomFunctionInterface<SurveyApiDTO> {
    SurveySearchApiService surveySearchApiService;

    @Override
    public Object execute(SurveyApiDTO surveyApiDTO, Map<String, Object> params) {
        SurveyApiQueryVO surveyApiQueryVO = new SurveyApiQueryVO();
        if(StrUtil.isNotEmpty(surveyApiDTO.getMode())){
            surveyApiQueryVO.setMode(surveyApiDTO.getMode());
        }
        if(!ObjectUtils.isEmpty(surveyApiDTO.getQueryShared())){
            surveyApiQueryVO.setQueryShared(surveyApiDTO.getQueryShared());
        }
        if(ObjectNull.isNotNull(surveyApiDTO.getQueryTotal())){
            surveyApiQueryVO.setQueryTotal(surveyApiDTO.getQueryTotal());
        }

        surveyApiQueryVO.setMode(surveyApiDTO.getMode());
        Page<SurveyApiViewVO> surveyApiViewVOPage = surveySearchApiService.getSurveyApiViewVOPage(new Page(surveyApiDTO.getPageNo(), surveyApiDTO.getPageSize()),surveyApiQueryVO);
        return surveyApiViewVOPage.getRecords();
    }
}
