package cn.bctools.design.rule.impl.userInfo;

import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.entity.enums.TestShowEnum;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Rule(value = "给指定用户添加角色",
        group = RuleGroup.用户角色模块,
        test = true,
        returnType = ClassType.对象,
        testShowEnum = TestShowEnum.JSON,
        order = 4,
        explain = "给指定用户添加角色"
)
public class AddUserRoleServiceImpl implements BaseCustomFunctionInterface<AddUserRoleDto> {
    @Resource
    AuthUserServiceApi authUserServiceApi;

    @Override
    public void inspect(AddUserRoleDto o) {
        if (ObjectNull.isNull(o.getRoles())){
            throw new BusinessException("请传入角色数组");
        }

        if (ObjectNull.isNull(o.getUserId())){
            throw new BusinessException("请传入用户id");
        }
    }

    @Override
    public Object execute(AddUserRoleDto addUserRoleDto, Map<String, Object> params) {
        if (ObjectNull.isNull(addUserRoleDto.getRoles()) || ObjectNull.isNull(addUserRoleDto.getUserId())) {
            return false;
        }
        R<List<String>> r = authUserServiceApi.addUserRoles(addUserRoleDto.getUserId(), addUserRoleDto.getFlag(), addUserRoleDto.getRoles());

        return r.getData();
    }
}
