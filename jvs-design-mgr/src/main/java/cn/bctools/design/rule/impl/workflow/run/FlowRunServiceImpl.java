package cn.bctools.design.rule.impl.workflow.run;

import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.entity.dto.UserInfoDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.IdGenerator;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.SystemThreadLocal;
import cn.bctools.design.data.fields.enums.DataEventType;
import cn.bctools.design.data.service.DataLogService;
import cn.bctools.design.data.util.DataModelUtil;
import cn.bctools.design.util.DynamicDataUtils;
import cn.bctools.design.workflow.dto.StartFlowTaskDto;
import cn.bctools.design.workflow.dto.startflow.StartFlowResDto;
import cn.bctools.design.workflow.dto.startflow.StartFlowVariables;
import cn.bctools.design.workflow.model.Node;
import cn.bctools.design.workflow.service.TaskService;
import cn.bctools.oauth2.dto.CustomUser;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.HashSet;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Rule(value = "启动流程",
        group = RuleGroup.服务插件,
        returnType = ClassType.对象,
        order = 2,
//        iconUrl = "rule-jituanOAliuchengxinzengxiudingshangxianshenpiliucheng",
        explain = "选择要执行的OA工作流,选择工作流时自动创建工作流,选择自动节点时当用户业务执行到此节点时继续向下扭转,返回实例id值"
)
@AllArgsConstructor
public class FlowRunServiceImpl implements BaseCustomFunctionInterface<FlowRunDto> {

    private static final String MODEL_ID = "modelId";
    private static final String DATA_MODEL_ID = "dataModelId";
    private static final String ID = "id";
    private static final String NULL = "null";

    TaskService taskService;
    DataLogService dataLogService;

    @Override
    public Object execute(FlowRunDto flowRunDto, Map<String, Object> map) {
        if (ObjectNull.isNull(flowRunDto.getData())) {
            throw new BusinessException("未配置流程参数值");
        }
        if (!flowRunDto.getData().containsKey(ID)) {
            flowRunDto.getData().put(ID, map.get(ID));
        }
        if (!flowRunDto.getData().containsKey(MODEL_ID)) {
            flowRunDto.getData().put(MODEL_ID, map.get(MODEL_ID));
        }

        // 模拟用户
        UserDto userDto;
        if (ObjectNull.isNotNull(flowRunDto.getUserDto())) {
            userDto = BeanCopyUtil.copy(flowRunDto.getUserDto(),UserDto.class);
            CustomUser principal = new CustomUser();
            principal.setUserDto(userDto);
            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(principal, null, new HashSet<>());                    SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            SecurityContextHolder.getContext().setAuthentication(authentication);
            SystemThreadLocal.set("user", (UserInfoDto<UserDto>)principal);
        }else {
            userDto = UserCurrentUtils.getCurrentUser();
        }

        //启动流程, 只需要流程ID, 或流程指定参数即可
        StartFlowTaskDto copy = new StartFlowTaskDto().setData(flowRunDto.getData());
        copy.setId(flowRunDto.getWorkflow());
        StartFlowVariables startFlowDto = BeanCopyUtil.copy(copy, StartFlowVariables.class);
        if (MapUtils.isNotEmpty(copy.getData())) {
            startFlowDto.setData(JSONObject.parseObject(JSON.toJSONString(copy.getData())));
            if (flowRunDto.getData().containsKey(MODEL_ID)) {
                if(ObjectNull.isNotNull(flowRunDto.getData().get(MODEL_ID))){
                    startFlowDto.setModelId(String.valueOf(flowRunDto.getData().get(MODEL_ID)));
                }
            }
            if (flowRunDto.getData().containsKey(DATA_MODEL_ID)) {
                startFlowDto.setModelId(String.valueOf(flowRunDto.getData().get(DATA_MODEL_ID)));
            }
            // 将数据版本号设置到上下文
            String dataVersion = IdGenerator.getIdStr();
            DataModelUtil.setCurrentDataVersion(dataVersion);
            startFlowDto.setDataVersion(dataVersion);
            if (copy.getData().containsKey(ID)) {
                String dataId = String.valueOf(copy.getData().get(ID));
                //处理兼容问题
                copy.getData().put("dataId", dataId);
                if (!NULL.equals(dataId)) {
                    startFlowDto.setDataId(dataId);
                    // 不是保存数据前置后置触发逻辑调用的工作流，要保存数据版本
                    if (Boolean.FALSE.equals(DataModelUtil.whetherCurrentSaveData())) {
                        dataLogService.saveLog(startFlowDto.getModelId(), startFlowDto.getDataId(), copy.getData(), null, DataEventType.DATA_UPDATE);
                    }
                }
            }
        }
        //设置审核人
        startFlowDto.setApprovers(flowRunDto.getApprovers());
        if (ObjectNull.isNotNull(flowRunDto.getNode())) {
            String s = JSONObject.toJSONString(flowRunDto.getNode());
            Node node = JSONObject.parseObject(s, Node.class);
            startFlowDto.setNode(node);
        }

        // 设置发起人表单
        startFlowDto.setSendFormId(flowRunDto.getSendFormId());

        // 启动工作流
        Boolean startFlow = Boolean.FALSE.equals(taskService.havePendingTask(startFlowDto.getDataId()));
        StartFlowResDto resDto = null;
        if (startFlow) {
            //执行之前将上下文对象保存一下。
            //现在循环调用，只支持一次，不能支持第二次，第二次会导致变量冲突
            String uuid = UUID.randomUUID().toString();
            DynamicDataUtils.saveTmpRuleContext(uuid);

            try{
                resDto = taskService.start(userDto, startFlowDto);
                // 将处理后的数据返回到params
                if (ObjectNull.isNotNull(flowRunDto.getData())) {
                    flowRunDto.getData().putAll(resDto.getData());
                } else {
                    flowRunDto.setData(resDto.getData());
                }
                return resDto.getFlowTaskId();
            }catch (BusinessException e){
                throw new BusinessException(e.getMessage());
            }catch (Exception e){
                throw new BusinessException("执行错误");
            }finally {
                //不管是什么情况返回执行时都需要将上下文设置回去
                DynamicDataUtils.recoverTmpRuleContext(uuid);
            }

        }

       /* StartFlowResDto resDto = null;
        // 任务已结束或未启动过工作流，才能启动工作流
        Object flowTaskStatusMsg = flowRunDto.getData().get(FlowDataFieldEnum.TASK_STATE.getFieldKey());
        if (ObjectNull.isNull(flowTaskStatusMsg)) {
            resDto = taskService.start(UserCurrentUtils.getCurrentUser(), startFlowDto);
            // 将处理后的数据返回到params
            flowRunDto.getData().putAll(resDto.getData());
            return resDto.getFlowTaskId();
        }

        // 任务已结束，可启动流程
        if (Boolean.FALSE.equals(FlowTaskStatusEnum.PENDING.getDesc().equals(flowTaskStatusMsg))) {
            resDto = taskService.start(UserCurrentUtils.getCurrentUser(), startFlowDto);
            // 将处理后的数据返回到params
            flowRunDto.getData().putAll(resDto.getData());
            return resDto.getFlowTaskId();
        }

        // 任务状态code不为空，且已结束，则可启动流程
        FlowDynamicDataServiceImpl.FlowTaskModelData jvsFlowTask = BeanCopyUtil.copy(flowRunDto.getData().get(FlowDataFieldEnum.TASK.getFieldKey()), FlowDynamicDataServiceImpl.FlowTaskModelData.class);
        Integer flowTaskStatusCode = jvsFlowTask.getTaskStatus();
        if (ObjectNull.isNotNull(flowTaskStatusCode) && Boolean.FALSE.equals(FlowTaskStatusEnum.PENDING.getValue().equals(flowTaskStatusCode))) {
            resDto = taskService.start(UserCurrentUtils.getCurrentUser(), startFlowDto);
            // 将处理后的数据返回到params
            flowRunDto.getData().putAll(resDto.getData());
            return resDto.getFlowTaskId();
        }*/

        throw new BusinessException("任务未结束,不能启动新任务");
    }

//    @Override
//    public Object execute(WorkflowDto dto, Map<String, Object> params) {
//        //启动流程, 只需要流程ID, 或流程指定参数即可
//        TaskService taskService = SpringContextUtil.getBean(TaskService.class);
//        FlowTaskNodeService flowTaskNodeService = SpringContextUtil.getBean(FlowTaskNodeService.class);
//
//        //获取逻辑执行上下文
//        List<String> workflow = dto.getWorkflow();
//        int size = workflow.size();
//        //启动一个工作流
//        if (size == 1) {
//            //判断是否有未结束的工作流任务，如果有，则直接返回当前工作流任务id
//            Object flowTaskStatus = params.get(FlowDataFieldEnum.TASK_STATE.getFieldKey());
//            if (FlowTaskStatusEnum.PENDING.getDesc().equals(flowTaskStatus)) {
//                Object task = Optional.ofNullable(params.get(FlowDataFieldEnum.TASK.getFieldKey())).orElse(new Object());
//                FlowDynamicDataServiceImpl.FlowTaskModelData flowTaskModelData = JSON.parseObject(JSON.toJSONString(task), FlowDynamicDataServiceImpl.FlowTaskModelData.class);
//                if (StringUtils.isNotBlank(flowTaskModelData.getId())) {
//                    return flowTaskModelData.getId();
//                }
//            }
//
//            // 启动工作流
//            String flowId = workflow.get(0);
//            log.info("创建工作流");
//            //启动时不能传递此数据
//            params.remove("ruleKey");
//            //todo 是否查询数据模型中已经有的字段，进行数据传递，不存在的字段 传递， 可能是运行过程中的其它数据 。
//            //将数据发送到工作流进行执行
//            StartFlowTaskDto copy = BeanCopyUtil.copy(params, StartFlowTaskDto.class);
//            copy.setId(flowId);
//            StartFlowReqDto startFlowDto = BeanCopyUtil.copy(copy, StartFlowReqDto.class);
//            if (MapUtils.isNotEmpty(copy.getData())) {
//                startFlowDto.setData(JSONObject.parseObject(JSON.toJSONString(copy.getData())));
//            }
//
//            if (params.containsKey("id")) {
//                String dataId = String.valueOf(params.get("id"));
//                if (!dataId.equals("null")) {
//                    startFlowDto.setDataId(dataId);
//                    if (params.containsKey("modelId")) {
//                        startFlowDto.setModelId(String.valueOf(params.get("modelId")));
//                    }
//                    startFlowDto.setData(JSONObject.parseObject(JSONObject.toJSONString(params)));
//                }
//            }
//            if (ObjectNull.isNotNull(dto.getApprovers())) {
//                List<String> userIds = (List<String>) dto.getApprovers()
//                        .stream()
//                        .filter(e -> ObjectNull.isNotNull(e.getValue()))
//                        .flatMap(e -> {
//                            if (e.getValue() instanceof Collection) {
//                                return ((List) e.getValue()).stream();
//                            }
//                            return Stream.of(e.getValue());
//                        })
//                        .map(String::valueOf).distinct()
//                        .collect(Collectors.toList());
//                if (ObjectNull.isNotNull(userIds)) {
//                    AuthUserServiceApi bean = SpringContextUtil.getBean(AuthUserServiceApi.class);
//                    //查询用户对象
//                    Map<String, List<PersonnelDto>> userDtoMap = bean.getByIds(userIds).getData().stream().collect(Collectors.toMap(UserDto::getId
//                            , e -> {
//                                List<PersonnelDto> personnelDtos = new ArrayList<>();
//                                PersonnelDto personnelDto = new PersonnelDto().setName(e.getRealName()).setType(PersonnelTypeEnum.user).setId(e.getId());
//                                personnelDtos.add(personnelDto);
//                                return personnelDtos;
//                            }));
//
//                    //根据节点 设置用户的值
//                    List<SelfSelectApprover> collect = dto.getApprovers().stream()
//                            .filter(e -> ObjectNull.isNotNull(e.getValue()))
//                            .map(e -> {
//                                Stream<Object> valueStream = Stream.of(e.getValue());
//                                if (e.getValue() instanceof Collection) {
//                                    valueStream = ((List) e.getValue()).stream();
//                                }
//                                List<PersonnelDto> approvers = valueStream.flatMap(s -> userDtoMap.get(s).stream()).collect(Collectors.toList());
//                                SelfSelectApprover selfSelectApprover = new SelfSelectApprover().setNodeId(e.getFieldKey()).setApprovers(approvers);
//                                return selfSelectApprover;
//                            })
//                            .collect(Collectors.toList());
//                    startFlowDto.setApprovers(collect);
//                }
//            }
//            StartFlowResDto resDto = taskService.startTask(UserCurrentUtils.getCurrentUser(), startFlowDto);
//            // 将处理后的数据返回到params
//            params.putAll(resDto.getData());
//            //返回实例ID值
//            return resDto.getFlowTaskId();
//        }
//        //执行下一步  ， 判断是否有两个选择的值。因为是树型选择，前端传递的值为数组值
//        if (size == 2) {
//            String flowId = workflow.get(0);
//            String nodeId = workflow.get(1);
//
//            //检查是否是执行到这个节点中
//            String taskId = dto.getTaskId();
//            Boolean isTask = flowTaskNodeService.whetherCurrentNode(taskId, nodeId);
//            //判断指定节点是否是目标工作流任务当前处理的节点
//            if (isTask) {
//                ExecuteFlowTaskReqDto executeFlowTaskDto = new ExecuteFlowTaskReqDto().setNodeId(nodeId).setTaskId(taskId).setData(new HashMap<String, Object>() {{
//                    put("ruleKey", params.get("ruleKey"));
//                }});
//
//                FlowReqDto flowDto = BeanCopyUtil.copy(executeFlowTaskDto, FlowReqDto.class);
//                flowDto.setId(executeFlowTaskDto.getTaskId());
//                if (MapUtils.isNotEmpty(executeFlowTaskDto.getData())) {
//                    flowDto.setData(JSONObject.parseObject(JSON.toJSONString(executeFlowTaskDto.getData())));
//                }
//                UserDto userDto = UserCurrentUtils.getCurrentUser();
//                taskService.execute(flowDto, userDto);
//                return "执行工作流成功";
//            }
//            return "没有执行到此节点，或已执行结束";
//        }
//        throw new BusinessException("工作流节点执行异常");
//    }

}
