package cn.bctools.design.rule.impl.zss_jyjg.stat;

import cn.bctools.rule.annotations.ParameterValue;
import cn.bctools.rule.entity.enums.InputType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class StatPerSubDto {
    @ParameterValue(info = "快照信息列表", type = InputType.input, necessity = false)
    public List<SnapShotPerSubVo> shotList;
}