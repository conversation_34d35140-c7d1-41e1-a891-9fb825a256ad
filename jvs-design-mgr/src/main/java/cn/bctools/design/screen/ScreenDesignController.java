package cn.bctools.design.screen;


import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.IdGenerator;
import cn.bctools.common.utils.R;
import cn.bctools.design.data.fields.enums.DesignType;
import cn.bctools.design.menu.component.AppMenuHandler;
import cn.bctools.design.screen.entity.ScreenPo;
import cn.bctools.design.screen.service.ScreenService;
import cn.bctools.log.annotation.Log;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "[design]大屏设计")
@RestController
@AllArgsConstructor
@RequestMapping("/app/design/{appId}/screen")
public class ScreenDesignController {

    ScreenService service;
    AppMenuHandler appMenuHandler;

    @Log
    @ApiOperation("删除大屏数据")
    @DeleteMapping("/{id}")
    public R delDetail(@PathVariable String id, @PathVariable String appId) {
        service.remove(Wrappers.query(new ScreenPo().setJvsAppId(appId).setId(id)));
        return R.ok();
    }

    @Log
    @ApiOperation("编辑大屏数据")
    @PutMapping("/{id}")
    public R putDetail(@PathVariable String id, @RequestBody ScreenPo model, @PathVariable String appId) {
        model.setId(id).setJvsAppId(appId);
        service.updateById(model);
        return R.ok();
    }

    @Log
    @ApiOperation("保存大屏数据")
    @PostMapping("/save")
    public R save(@RequestBody ScreenPo model, @PathVariable String appId) {
        //目前版本暂时不支持大屏功能
        if (1 == 1) {
            throw new BusinessException("目前不支持大屏功能");
        }
        model.setJvsAppId(appId);
        model.setCode(IdGenerator.getIdStr(30));
        service.save(model);
        appMenuHandler.addMenu(DesignType.screen, model.getId(), model.getJvsAppId(), null, model.getName(), model.getType());
        return R.ok(model);
    }

}