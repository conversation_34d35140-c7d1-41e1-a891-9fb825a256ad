package cn.bctools.design.use;

import cn.bctools.auth.api.api.AuthRoleServiceApi;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import cn.bctools.design.chart.service.ChartService;
import cn.bctools.design.constant.DesignConstant;
import cn.bctools.design.crud.entity.CrudPage;
import cn.bctools.design.crud.entity.DesignRole;
import cn.bctools.design.crud.entity.FormPo;
import cn.bctools.design.crud.service.CrudPageService;
import cn.bctools.design.crud.service.FormService;
import cn.bctools.design.crud.utils.DesignUtils;
import cn.bctools.design.data.fields.DataFieldHandler;
import cn.bctools.design.data.fields.IDataFieldHandler;
import cn.bctools.design.data.fields.dto.FieldBasicsHtml;
import cn.bctools.design.data.fields.dto.form.FormDesignHtml;
import cn.bctools.design.data.fields.dto.page.ButtonDesignHtml;
import cn.bctools.design.data.fields.dto.page.PageDesignHtml;
import cn.bctools.design.data.fields.enums.DataFieldType;
import cn.bctools.design.data.service.DataFieldService;
import cn.bctools.design.data.service.DataModelService;
import cn.bctools.design.data.service.DynamicDataService;
import cn.bctools.design.data.util.RoleUtils;
import cn.bctools.design.h5.entity.H5Design;
import cn.bctools.design.h5.service.H5DesignService;
import cn.bctools.design.menu.entity.AppMenu;
import cn.bctools.design.menu.service.AppMenuService;
import cn.bctools.design.project.entity.JvsApp;
import cn.bctools.design.project.service.JvsAppService;
import cn.bctools.design.screen.entity.ScreenPo;
import cn.bctools.design.screen.service.ScreenService;
import cn.bctools.function.entity.po.FunctionBusinessPo;
import cn.bctools.function.mapper.FunctionBusinessMapper;
import cn.bctools.log.annotation.Log;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.web.utils.IpUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.tree.Tree;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Api(tags = "[设计套件]获取设计渲染结构")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/app/use/{appId}")
public class AppUseController {

    AuthRoleServiceApi roleServiceApi;
    UseComponent useComponent;
    FormService formService;
    ScreenService screenService;
    DataModelService dataModelService;
    JvsAppService jvsAppService;
    CrudPageService crudPageService;
    H5DesignService h5DesignService;
    ChartService chartService;
    DataFieldService dataFieldService;
    DynamicDataService dynamicDataService;
    DataFieldHandler dataFieldHandler;
    FunctionBusinessMapper businessMapper;
    Map<String, IDataFieldHandler> handlerMap;
    AppMenuService appMenuService;

    @GetMapping
    @ApiOperation("获取的应用菜单信息")
    public R trees(@PathVariable String appId) {
        //只返回目录
        String userId = UserCurrentUtils.getUserId();
        boolean mobile = IpUtil.isMobile();
        List<Tree<Object>> tree = useComponent.menu(appId, userId, mobile);
        if (ObjectNull.isNull(tree)) {
            return R.ok();
        }
        Tree trees = tree
                .stream()
                .findFirst()
                .orElseThrow(() -> new BusinessException("没有找到应用"));
        return R.ok(trees);
    }

    @Log(back = false)
    @GetMapping("/crudPage/{id}")
    @ApiOperation("列表页设计数据")
    public R<CrudPage> crud(@PathVariable("id") String id, @PathVariable("appId") String appId) {
        CrudPage po = crudPageService.getById(id);
        if (Objects.isNull(po)) {
            return R.failed("设计页面未找到, 请联系管理员");
        }
        if (!po.getJvsAppId().equals(appId)) {
            return R.failed(DesignConstant.APP_ERROR_MSG);
        }

        JvsApp byId = jvsAppService.getById(po.getJvsAppId());
        boolean isAdmin = UserCurrentUtils.getCurrentUser().getAdminFlag() || byId.getCreateById().equals(UserCurrentUtils.getUserId()) || po.getCreateById().equals(UserCurrentUtils.getUserId());
        PageDesignHtml design = DesignUtils.parsePage(po.getViewJson());
        if (!isAdmin) {
//            return R.ok(po);
            //判断按钮权限进行解析排除
            AppMenu designMenu = appMenuService.getDesignMenu(po.getId(), po.getJvsAppId());
            List<DesignRole> designRoles = designMenu.getRoles();
            if (ObjectNull.isNotNull(designRoles)) {
                boolean check = RoleUtils.hasPermit(designRoles);
                if (!check) {
                    return R.failed("没有权限, 请联系管理员");
                }
            }
            // 操作按钮权限过滤
            if (ObjectNull.isNotNull(design)) {
                List<ButtonDesignHtml> buttons = design.getButtons();
                if (ObjectNull.isNotNull(buttons)) {
                    Set<String> permit = RoleUtils.getPermitOperation(designRoles, buttons);
                    buttons.removeIf(button -> !permit.contains(button.getName()));
                    //移动端过滤
                    if (IpUtil.isMobile()) {
                        buttons.removeIf(e -> {
                            //如果移动端标识为空,取PC端标识
                            return ObjectNull.isNull(e.getMobileEnable()) ? !e.getEnable() : !e.getMobileEnable();
                        });
                    }
                }
            }
        }

        List<ButtonDesignHtml> defaultButtonDesignHtmls = crudPageService.getSystemDefaultButtons();
        defaultButtonDesignHtmls.forEach(button -> button.setPosition("line"));
        if (ObjectNull.isNotNull(design)) {
            if (ObjectNull.isNotNull(design.getButtons())) {
                design.getButtons().addAll(defaultButtonDesignHtmls);
            } else {
                design.setButtons(new ArrayList<>());
                design.getButtons().addAll(defaultButtonDesignHtmls);
            }
        }
        //TODO 对属性字段 进行类型自适应处理
        crudPageService.convertDesign(po, design);
        return R.ok(po.setViewJson(JSONObject.toJSONString(design)));
    }

    @GetMapping("/mobile")
    @ApiOperation("获取移动端的应用信息")
    public R mobile(@PathVariable String appId) {
        //只返回目录
        String userId = UserCurrentUtils.getUserId();
        boolean mobile = IpUtil.isMobile();

        List<Tree<Object>> tree = useComponent.menu(appId, userId, mobile);
        List<Tree<Object>> trees = tree
                .stream()
                .findFirst()
                .map(e -> e.getChildren())
                .orElseThrow(() -> new BusinessException("没有找到应用"));
        return R.ok(trees);
    }

    @Log
    @GetMapping("/h5/{id}")
    @ApiOperation("H5设计数据")
    @Transactional(rollbackFor = Exception.class)
    public R h5(@PathVariable("id") String id, @PathVariable String appId) {
        H5Design po = h5DesignService.getById(id);
        if (!po.getJvsAppId().equals(appId)) {
            return R.failed(DesignConstant.APP_ERROR_MSG);
        }
        JvsApp byId = jvsAppService.getById(po.getJvsAppId());
        boolean isAdmin = UserCurrentUtils.getCurrentUser().getAdminFlag() || byId.getCreateById().equals(UserCurrentUtils.getUserId()) || po.getCreateById().equals(UserCurrentUtils.getUserId());
        if (isAdmin) {
            return R.ok(po);
        }
        if (Objects.isNull(po)) {
            return R.failed("没有权限, 请联系管理员");
        }
        return R.ok(po);
    }

    @Log
    @GetMapping("/screen/{resourceId}")
    @ApiOperation("大屏获取")
    @Transactional(rollbackFor = Exception.class)
    public R screen(@PathVariable("resourceId") String id, @PathVariable String appId) {
        ScreenPo one = screenService.getOne(Wrappers.query(new ScreenPo().setId(id).setJvsAppId(appId)));
        if (ObjectNull.isNull(one)) {
            return R.failed(DesignConstant.APP_ERROR_MSG);
        }
        return R.ok(one);
    }

    @Log
    @GetMapping("/form/{id}")
    @ApiOperation("表单设计数据")
    @Transactional(rollbackFor = Exception.class)
    public R<FormPo> form(@PathVariable("id") String id, @PathVariable String appId) {
        FormPo po = formService.getById(id);
        if (Objects.isNull(po)) {
            return R.failed("设计页面未找到, 请联系管理员");
        }
        if (!po.getJvsAppId().equals(appId)) {
            return R.failed(DesignConstant.APP_ERROR_MSG);
        }
        JvsApp byId = jvsAppService.getById(po.getJvsAppId());
        boolean isAdmin = UserCurrentUtils.getCurrentUser().getAdminFlag() || byId.getCreateById().equals(UserCurrentUtils.getUserId()) || po.getCreateById().equals(UserCurrentUtils.getUserId());


        //判断权限类型
        FormDesignHtml design = DesignUtils.parseForm(po.getViewJson());
        if (ObjectNull.isNull(design.getFormdata())) {
            return R.ok(po);
        }
        //TODO 根据表单结构判断是否存在动态关联属性关系,如果存在 ,则查询模型数据进行处理回显
        //找到了有多条存在数据关联的数据值
        for (Map<String, Object> map : design.getFormdata().get(0).getForms()) {
            //如果有值才处理
            if (!map.containsKey("dataLinkageEnable")) {
                continue;
            }
            //1、寻找这个模型设计,确定字段
            //获取多个字段集
            Optional<FieldBasicsHtml> formId = dataFieldService.getAllField(appId, String.valueOf(map.get("formId")))
                    .stream()
                    //必须要是多选的控件组件
                    .filter(s -> s.getType().equals(DataFieldType.checkbox))
                    .findFirst();
            //根据控件确定字段,根据控件确定组件模型数据
            if (formId.isPresent()) {
                //找到关联数据
                List<String> fieldKey = new ArrayList<>();
                fieldKey.add("id");
                String fieldKey1 = formId.get().getFieldKey();
                fieldKey.add(fieldKey1);
                String key = String.valueOf(((Map) map.get("props")).get("label"));
                fieldKey.add(key);
                //获取到多种不同的设备属性匹配规则
                List<Map<String, Object>> props = dynamicDataService.queryList(String.valueOf(map.get("formId")), fieldKey);
                for (Map<String, Object> form : design.getFormdata().get(0).getForms()) {
                    for (Map<String, Object> prop : props) {
                        //获取多选的属性值
                        ArrayList arrayList = (ArrayList) prop.get(fieldKey1);
                        if (arrayList.contains(form.get("prop"))) {
                            List displayExpress = (List) form.getOrDefault("displayExpress", new ArrayList<>());
                            displayExpress.add(Dict.create().set("prop", map.get("prop")).set("label", map.get("label")).set("value", prop.get("id")));
                            form.put("displayExpress", displayExpress);
                        }
                    }
                }

            }
        }
        //操作按钮权限过滤目前只有一个表单，没有多个表单获取 0
        List<ButtonDesignHtml> formButtonList = design.getFormdata().get(0).getFormsetting().getBtnSetting();
        if (ObjectNull.isNotNull(formButtonList)) {
            //删除未应用的， 包括超级管理员
            formButtonList.removeIf(button -> !Boolean.TRUE.equals(button.getEnable()));
        }
        //根据结构添加公式组件数据
        List<String> execs = businessMapper.selectList(Wrappers.query(new FunctionBusinessPo().setDesignId(po.getId())))
                .stream()
                .filter(e -> ObjectNull.isNotNull(e.getRelatedIds()))
                .flatMap(e -> e.getRelatedIds().stream())
                .distinct()
                .collect(Collectors.toList());
        design.setExecs(execs);
        //如果是移动端，需要移掉其它的控件
        if (IpUtil.isMobile()) {
            DesignUtils.filterMobile(design);
            po.setViewJson(JSONObject.toJSONString(design));
        }
        if (isAdmin) {
            return R.ok(po.setViewJson(JSONObject.toJSONString(design)));
        }

        AppMenu appMenu = appMenuService.getDesignMenu(po.getId(), po.getJvsAppId());
        //判断是否有菜单权限，避免直接访问
        //判断按钮权限进行解析排除
        List<DesignRole> designRoleList = appMenu.getRoles();
        //没有菜单的表单不判断权限
        if (ObjectNull.isNotNull(designRoleList)) {
            boolean hasPermit = RoleUtils.hasPermit(designRoleList);
            if (!hasPermit) {
                return R.failed("没有权限, 请联系管理员");
            }
        }
        Set<String> permit = RoleUtils.getPermitOperation(designRoleList, formButtonList);
        //删除没有权限的
        formButtonList.removeIf(button -> !permit.contains(button.getName()));
        return R.ok(po.setViewJson(JSONObject.toJSONString(design)));
    }

    /*@Log
    @GetMapping("/chart/{resourceId}")
    @ApiOperation("图表设计数据")
    @Transactional(rollbackFor = Exception.class)
    public R<ChartPage> chart(@PathVariable("resourceId") String id, @PathVariable String appId) {
        ChartPage po = chartPageService.getById(id);
        if (Objects.isNull(po)) {
            return R.failed("设计页面未找到, 请联系管理员");
        }
        if (!po.getJvsAppId().equals(appId)) {
            return R.failed(DesignConstant.APP_ERROR_MSG);
        }
        JvsApp byId = jvsAppService.getById(po.getJvsAppId());
        boolean isAdmin = UserCurrentUtils.getCurrentUser().getAdminFlag() || byId.getCreateById().equals(UserCurrentUtils.getUserId()) || po.getCreateById().equals(UserCurrentUtils.getUserId());

        if (isAdmin) {
            po = chartPageService.getData(po, new HashMap<>(0), Boolean.FALSE);
            return R.ok(po);
        }
        //判断是否有菜单权限，避免直接访问
        //判断按钮权限进行解析排除
        List<JSONObject> roleList = po.getRole();
        List<DesignRole> designRoleList = JSONArray.parseArray(JSONObject.toJSONString(roleList), DesignRole.class);
        boolean hasPermit = RoleUtils.hasPermit(designRoleList);
        if (!hasPermit) {
            return R.failed("没有权限, 请联系管理员");
        }
        po = chartPageService.getData(po, new HashMap<>(0), Boolean.FALSE);
        return R.ok(po);
    }*/

    @Log
    @ApiOperation("获取表单")
    @GetMapping("/form/design/{resourceId}")
    public R<FormPo> getById(@PathVariable("resourceId") String id, @PathVariable String appId) {
        return R.ok(formService.getOne(Wrappers.query(new FormPo().setJvsAppId(appId).setId(id))));
    }
}

