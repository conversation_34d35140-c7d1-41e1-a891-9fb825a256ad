package cn.bctools.design.use;

import cn.bctools.design.data.fields.enums.DesignType;
import cn.bctools.design.project.entity.JvsApp;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 菜单构建上下文
 * 用于封装菜单构建过程中的所有相关数据
 * 
 * <AUTHOR>
 */
@Data
public class MenuContext {
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 是否移动端
     */
    private boolean mobile;
    
    /**
     * 目标应用
     */
    private JvsApp targetApp;
    
    /**
     * 用户有权限的应用映射
     */
    private Map<String, JvsApp> authorizedApps;
    
    /**
     * 是否有设计权限
     */
    private boolean hasDesignPermission;
    
    /**
     * 需要权限检查的设计类型列表
     */
    private List<DesignType> checkTypeList;
    
    /**
     * 菜单列表
     */
    private List<JvsMenuVo> menuList;
    
    /**
     * 用户角色ID列表
     */
    private List<String> roleIds;
    
    /**
     * 是否查询单个应用
     */
    public boolean isSingleApp() {
        return appId != null;
    }
    
    /**
     * 是否有指定应用的设计权限
     */
    public boolean hasDesignPermissionForApp() {
        return hasDesignPermission;
    }
}