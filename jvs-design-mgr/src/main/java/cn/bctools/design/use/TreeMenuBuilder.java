package cn.bctools.design.use;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.function.Get;
import cn.bctools.database.util.SqlFunctionUtil;
import cn.bctools.design.chart.service.ChartService;
import cn.bctools.design.crud.entity.AppUrlPo;
import cn.bctools.design.crud.service.AppUrlService;
import cn.bctools.design.data.fields.enums.DesignType;
import cn.bctools.design.data.util.RoleUtils;
import cn.bctools.design.menu.entity.AppMenu;
import cn.bctools.design.menu.service.AppMenuService;
import cn.bctools.design.menu.service.AppMenuTypeService;
import cn.bctools.design.project.entity.JvsApp;
import cn.bctools.design.project.service.JvsAppService;
import cn.bctools.design.report.service.ReportService;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 优化后的菜单构建器
 * 将复杂的菜单构建逻辑拆分为多个职责单一的方法
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class TreeMenuBuilder {
    
    private final JvsAppService jvsAppService;
    private final AppMenuService appMenuService;
    private final AppMenuTypeService appMenuTypeService;
    private final ChartService chartService;
    private final ReportService reportService;
    private final AppUrlService appUrlService;
    
    /**
     * 处理单个应用的菜单构建
     */
    public List<JvsMenuVo> handleSingleAppMenu(MenuContext context) {
        try {
            List<JvsMenuVo> menuList = new ArrayList<>();
            
            if (context.hasDesignPermissionForApp()) {
                // 有设计权限，构建应用树
                buildAuthorizedAppTree(context, menuList);
            } else {
                // 无设计权限，查询被授权的菜单
                buildGrantedMenus(context, menuList);
            }
            
            return menuList;
        } catch (Exception e) {
            log.error("处理单个应用菜单失败: appId={}", context.getAppId(), e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 处理多个应用的菜单构建
     */
    public List<JvsMenuVo> handleMultipleAppMenu(MenuContext context) {
        try {
            List<JvsMenuVo> menuList = new ArrayList<>();
            
            // 1. 构建有设计权限的应用树
            buildAuthorizedAppTree(context, menuList);
            
            // 2. 查询其他被授权的应用菜单
            buildOtherGrantedMenus(context, menuList);
            
            return menuList;
        } catch (Exception e) {
            log.error("处理多个应用菜单失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 构建有设计权限的应用树
     */
    private void buildAuthorizedAppTree(MenuContext context, List<JvsMenuVo> menuList) {
        try {
            Map<String, JvsApp> authorizedApps = context.getAuthorizedApps();
            if (ObjectNull.isNull(authorizedApps) || authorizedApps.isEmpty()) {
                return;
            }
            
            // 构建应用树（这里需要调用原有的appTree方法）
            // appTree(authorizedApps.values(), menuList, true);
            
            log.debug("构建授权应用树完成: appCount={}", authorizedApps.size());
        } catch (Exception e) {
            log.error("构建授权应用树失败", e);
        }
    }
    
    /**
     * 构建被授权的菜单（针对单个应用）
     */
    private void buildGrantedMenus(MenuContext context, List<JvsMenuVo> menuList) {
        try {
            String appId = context.getAppId();
            if (ObjectNull.isNull(appId)) {
                return;
            }
            
            // 查询指定应用的已部署状态
            List<JvsApp> deployedApps = jvsAppService.list(
                new LambdaQueryWrapper<JvsApp>()
                    .eq(JvsApp::getId, appId)
                    .eq(JvsApp::getIsDeploy, true)
            );
            
            if (!deployedApps.isEmpty()) {
                // 构建应用树（调用原有方法）
                // appTree(deployedApps, menuList, false);
                log.debug("构建单个应用菜单完成: appId={}", appId);
            }
        } catch (Exception e) {
            log.error("构建被授权菜单失败: appId={}", context.getAppId(), e);
        }
    }
    
    /**
     * 构建其他被授权的应用菜单
     */
    private void buildOtherGrantedMenus(MenuContext context, List<JvsMenuVo> menuList) {
        try {
            List<String> roleIds = UserCurrentUtils.getRole();
            if (ObjectNull.isNull(roleIds) || roleIds.isEmpty()) {
                return;
            }
            
            context.setRoleIds(roleIds);
            
            // 1. 查询不在授权应用中但有角色权限的应用
            List<String> otherAppIds = findOtherAuthorizedApps(context);
            if (ObjectNull.isNull(otherAppIds) || otherAppIds.isEmpty()) {
                return;
            }
            
            // 2. 查询这些应用的菜单
            List<AppMenu> appMenus = queryAppMenus(otherAppIds);
            if (ObjectNull.isNull(appMenus) || appMenus.isEmpty()) {
                return;
            }
            
            // 3. 处理菜单权限和数据
            List<AppMenu> processedMenus = processMenuPermissions(appMenus, context);
            
            // 4. 转换为菜单VO并添加到结果中
            convertAndAddMenus(processedMenus, menuList, context);
            
            log.debug("构建其他授权菜单完成: menuCount={}", processedMenus.size());
        } catch (Exception e) {
            log.error("构建其他授权菜单失败", e);
        }
    }
    
    /**
     * 查找其他有权限的应用ID
     */
    private List<String> findOtherAuthorizedApps(MenuContext context) {
        try {
            Map<String, JvsApp> authorizedApps = context.getAuthorizedApps();
            List<String> roleIds = context.getRoleIds();
            
            LambdaQueryWrapper<JvsApp> queryWrapper = Wrappers.lambdaQuery(new JvsApp().setIsDeploy(true))
                .select(JvsApp::getId, JvsApp::getIsDeploy)
                .notIn(ObjectNull.isNotNull(authorizedApps.keySet()), JvsApp::getId, authorizedApps.keySet());
            
            // 构建角色权限查询条件
            queryWrapper.nested(nestedQuery -> {
                for (String roleId : roleIds) {
                    JSONObject conditionRole = new JSONObject();
                    conditionRole.put("type", "role");
                    conditionRole.put("id", roleId);
                    String conditionRoleJson = JSON.toJSONString(conditionRole);
                    
                    nestedQuery.or(orQuery -> 
                        orQuery.apply(SqlFunctionUtil.jsonContainsObject(
                            Get.name(JvsApp::getRole), "$.appMember", conditionRoleJson))
                    );
                }
            });
            
            List<String> appIds = jvsAppService.list(queryWrapper)
                .stream()
                .map(JvsApp::getId)
                .collect(Collectors.toList());
            
            log.debug("找到其他授权应用: count={}", appIds.size());
            return appIds;
        } catch (Exception e) {
            log.error("查找其他授权应用失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 查询应用菜单
     */
    private List<AppMenu> queryAppMenus(List<String> appIds) {
        try {
            return appMenuService.list(
                new LambdaQueryWrapper<AppMenu>()
                    .select(AppMenu::getRole, AppMenu::getRoleType, AppMenu::getJvsAppId,
                           AppMenu::getId, AppMenu::getDesignType, AppMenu::getDesignId,
                           AppMenu::getName, AppMenu::getType, AppMenu::getSort,
                           AppMenu::getMobileDisplay, AppMenu::getPcDisplay, AppMenu::getDataModelId)
                    .in(AppMenu::getJvsAppId, appIds)
                    .isNotNull(AppMenu::getType)
            );
        } catch (Exception e) {
            log.error("查询应用菜单失败: appIds={}", appIds, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 处理菜单权限和数据
     */
    private List<AppMenu> processMenuPermissions(List<AppMenu> menuList, MenuContext context) {
        try {
            // 缓存设计数据权限
            Map<String, List<JSONObject>> designIdMap = cacheDesignPermissions(menuList);
            
            return menuList.stream()
                .peek(menu -> processMenuDesignData(menu, designIdMap))
                .filter(menu -> context.isMobile() ? menu.getMobileDisplay() : menu.getPcDisplay())
                .filter(menu -> RoleUtils.hasPermit(menu.getRoles()))
                .filter(menu -> !context.getCheckTypeList().contains(menu.getDesignType()) || 
                               RoleUtils.hasShowPermit(menu.getRoles()))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("处理菜单权限失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 缓存设计数据权限
     */
    private Map<String, List<JSONObject>> cacheDesignPermissions(List<AppMenu> menuList) {
        Map<String, List<JSONObject>> designIdMap = new HashMap<>();
        Set<String> processedApps = new HashSet<>();
        
        for (AppMenu menu : menuList) {
            String appId = menu.getJvsAppId();
            if (processedApps.contains(appId)) {
                continue;
            }
            
            try {
                switch (menu.getDesignType()) {
                    case chart:
                        chartService.getAll(appId)
                            .forEach(chart -> designIdMap.put(chart.getId(), chart.getRole()));
                        break;
                    case report:
                        reportService.getAll(appId)
                            .forEach(report -> designIdMap.put(report.getId(), report.getRole()));
                        break;
                    default:
                        // 其他类型暂不处理
                        break;
                }
                processedApps.add(appId);
            } catch (Exception e) {
                log.error("缓存设计权限失败: appId={}, designType={}", appId, menu.getDesignType(), e);
            }
        }
        
        return designIdMap;
    }
    
    /**
     * 处理单个菜单的设计数据
     */
    private void processMenuDesignData(AppMenu menu, Map<String, List<JSONObject>> designIdMap) {
        try {
            switch (menu.getDesignType()) {
                case report:
                case chart:
                    List<JSONObject> designRoles = designIdMap.getOrDefault(menu.getDesignId(), new ArrayList<>());
                    menu.setRole(JSONArray.copyOf(designRoles));
                    break;
                default:
                    // 其他类型保持原有角色配置
                    break;
            }
        } catch (Exception e) {
            log.error("处理菜单设计数据失败: menuId={}", menu.getId(), e);
        }
    }
    
    /**
     * 转换并添加菜单到结果列表
     */
    private void convertAndAddMenus(List<AppMenu> appMenus, List<JvsMenuVo> menuList, MenuContext context) {
        try {
            // 获取URL应用映射
            Map<String, AppUrlPo> appUrlMap = getAppUrlMapping(appMenus);
            
            // 用于收集类型ID和应用ID
            Set<String> typeIds = new HashSet<>();
            Set<String> appIds = new HashSet<>();
            
            // 转换菜单并添加到结果
            appMenus.stream()
                .map(menu -> convertToMenuVo(menu, appUrlMap))
                .peek(menuVo -> {
                    typeIds.add(menuVo.getParentId());
                    appIds.add(menuVo.getJvsAppId());
                })
                .forEach(menuList::add);
            
            // 添加菜单类型
            addMenuTypes(typeIds, menuList);
            
            // 添加应用信息
            addAppInfo(appIds, context.getAuthorizedApps().keySet(), menuList);
            
        } catch (Exception e) {
            log.error("转换并添加菜单失败", e);
        }
    }
    
    /**
     * 获取URL应用映射
     */
    private Map<String, AppUrlPo> getAppUrlMapping(List<AppMenu> appMenus) {
        try {
            List<String> appUrlIds = appMenus.stream()
                .filter(menu -> Objects.equals(menu.getDesignType(), DesignType.URL))
                .map(AppMenu::getDesignId)
                .collect(Collectors.toList());
            
            if (appUrlIds.isEmpty()) {
                return new HashMap<>();
            }
            
            return appUrlService.getMapByIds(appUrlIds);
        } catch (Exception e) {
            log.error("获取URL应用映射失败", e);
            return new HashMap<>();
        }
    }
    
    /**
     * 转换为菜单VO
     */
    private JvsMenuVo convertToMenuVo(AppMenu menu, Map<String, AppUrlPo> appUrlMap) {
        // 这里需要调用原有的transition方法
        // return transition(menu, enableWorkflowMap, false, appUrlMap);
        
        // 临时实现，实际需要调用原有方法
        JvsMenuVo vo = new JvsMenuVo();
        vo.setId(menu.getId());
        vo.setName(menu.getName());
        vo.setJvsAppId(menu.getJvsAppId());
        // ... 其他属性设置
        return vo;
    }
    
    /**
     * 添加菜单类型
     */
    private void addMenuTypes(Set<String> typeIds, List<JvsMenuVo> menuList) {
        try {
            if (ObjectNull.isNotNull(typeIds) && !typeIds.isEmpty()) {
                appMenuTypeService.listByIds(typeIds)
                    .forEach(menuType -> {
                        // 调用原有的addMenuType方法
                        // addMenuType(menuType, menuList, false);
                    });
            }
        } catch (Exception e) {
            log.error("添加菜单类型失败", e);
        }
    }
    
    /**
     * 添加应用信息
     */
    private void addAppInfo(Set<String> appIds, Set<String> authorizedAppIds, List<JvsMenuVo> menuList) {
        try {
            if (ObjectNull.isNull(appIds) || appIds.isEmpty()) {
                return;
            }
            
            // 移除已有权限的应用
            appIds.removeAll(authorizedAppIds);
            
            if (!appIds.isEmpty()) {
                List<JvsApp> apps = jvsAppService.list(
                    new LambdaQueryWrapper<JvsApp>()
                        .in(JvsApp::getId, appIds)
                        .eq(JvsApp::getIsDeploy, true)
                );
                
                // 调用原有的appTree方法
                // appTree(apps, menuList, false);
            }
        } catch (Exception e) {
            log.error("添加应用信息失败", e);
        }
    }
}