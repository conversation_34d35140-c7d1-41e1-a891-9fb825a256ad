package cn.bctools.design.workflow.expression;

import cn.bctools.design.data.fields.dto.FieldBasicsHtml;
import cn.bctools.design.data.service.DataFieldService;
import cn.bctools.design.expression.EnvConstant;
import cn.bctools.design.workflow.entity.FlowDesign;
import cn.bctools.design.workflow.service.FlowDesignService;
import cn.bctools.function.entity.vo.ElementVo;
import cn.bctools.function.enums.JvsParamType;
import cn.bctools.function.handler.IJvsParam;
import cn.bctools.function.handler.JvsExpression;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: ZhuXiaoKang
 * @Description: 工作流数据字段
 */
@Slf4j
@Service
@AllArgsConstructor
@JvsExpression(groupName = "数据字段", useCase = EnvConstant.FLOW_FUNCTION_ITEM_VALUE)
public class FlowItemParam implements IJvsParam<ElementVo> {

    private final DataFieldService dataFieldService;
    private final FlowDesignService flowDesignService;

    @Override
    public List<ElementVo> getAllElements() {
        String designId = this.getDesignId();
        if (StringUtils.isBlank(designId)) {
            return Collections.emptyList();
        }
        FlowDesign flowDesign = flowDesignService.getById(designId);
        if (ObjectUtils.isEmpty(flowDesign)) {
            return Collections.emptyList();
        }
        String modeId = flowDesign.getDataModelId();
        List<FieldBasicsHtml> dataFieldDtos = dataFieldService.getAllField(flowDesign.getJvsAppId(), modeId);
        if (CollectionUtils.isEmpty(dataFieldDtos)) {
            return Collections.emptyList();
        }
        return dataFieldDtos.stream().map(param ->
                new ElementVo()
                        .setId(param.getFieldKey())
                        .setName(param.getFieldName())
                        .setJvsParamType(JvsParamType.getByClass(param.getType().getAClass()))).collect(Collectors.toList());
    }

    @Override
    public Object get(String paramName, Map<String, Object> data) {
        return data.get(paramName);
    }
}
