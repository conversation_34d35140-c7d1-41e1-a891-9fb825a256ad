server:
  port: ${random.int[50001,50099]}
version: @project.version@
spring:
  application:
    name: @project.artifactId@
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        server-addr: http://jvs-nacos:8848
        group: jvs
        namespace: ${spring.cloud.nacos.discovery.group}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        group: ${spring.cloud.nacos.discovery.group}
        namespace: ${spring.cloud.nacos.discovery.group}
    inetutils:
      #选择使用此网段进行处理
      preferred-networks: 10.*
  config:
    import:
      #公共配置
      - optional:nacos:application.yml
      #公共配置项目配置
      - optional:nacos:${spring.application.name}.yml
      #父级配置
      - optional:nacos:${project.parent.artifactId}.yml
      #授权配置
      - optional:nacos:license.yml

swagger:
  title: "低代码模块"
  description: "低代码模块,包含表单,逻辑,工作流,列表页,数据模型服务等"
