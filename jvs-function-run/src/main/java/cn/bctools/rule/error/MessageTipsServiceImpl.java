package cn.bctools.rule.error;

import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import cn.bctools.rule.utils.RuleSystemThreadLocal;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 两种状态返回给前端，主要返回示例为 R.ok("xxx")  R.failed("xxxx")
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Rule(value = "提示消息",
        group = RuleGroup.常用插件,
        returnType = ClassType.对象,
//        iconUrl = "rule-drdsfenbushiguanxixingshujukufuwuDRD",
        explain = "将业务消息返回给前端进行展示，此节点需要同步逻辑才会生效。此节点将中止程序的运行"
)
public class MessageTipsServiceImpl implements BaseCustomFunctionInterface<MessageTipsDto> {

    @Override
    public Object execute(MessageTipsDto errorDto, Map<String, Object> params) {
        RuleSystemThreadLocal.getRule().getExecuteDto().setMessageResult(errorDto.getData()).setStats(errorDto.getOnOff()).setSyncMessageTips(errorDto.getMessage());
        return errorDto;
    }

}
