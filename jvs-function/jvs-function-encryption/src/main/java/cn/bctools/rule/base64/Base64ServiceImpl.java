//package cn.bctools.rule.base64;
//
//import cn.bctools.common.utils.PasswordUtil;
//import cn.bctools.rule.annotations.Rule;
//import cn.bctools.rule.entity.enums.ClassType;
//import cn.bctools.rule.entity.enums.RuleGroup;
//import cn.bctools.rule.entity.enums.TestShowEnum;
//import cn.bctools.rule.function.BaseCustomFunctionInterface;
//import cn.hutool.core.codec.Base64;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//
//import java.util.Map;
//
///**
// * <AUTHOR>
// */
//@Slf4j
//@AllArgsConstructor
//@Rule(value = "Base64",
//        group = RuleGroup.加密插件,
//        test = true,
//        returnType = ClassType.文本,
//        testShowEnum = TestShowEnum.TEXT,
//        order = 2,
////        iconUrl = "rule-ipvgateway",
//        explain = "数据解析将数据进行解密为明文"
//
//)
//public class Base64ServiceImpl implements BaseCustomFunctionInterface<Base64Dto> {
//
//    @Override
//    public Object execute(Base64Dto dto, Map<String, Object> params) {
//        if (dto.isType()) {
//            return Base64.encode(dto.getBody().getBytes());
//        } else {
//            return Base64.decodeStr(dto.getBody());
//        }
//    }
//
//}
