package cn.bctools.rule.tools.file;

import cn.bctools.rule.annotations.ParameterValue;
import cn.bctools.rule.entity.enums.InputType;
import cn.bctools.rule.entity.enums.type.RuleFile;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class UnzipDto {
    @ParameterValue(info = "zip压缩包文件（可传多个），数组格式", type = InputType.input)
    public List<RuleFile> file;

    @ParameterValue(info = "允许保存文件格式（不传则不限制）", type = InputType.list)
    public List<String> extension = new ArrayList<>();

}
