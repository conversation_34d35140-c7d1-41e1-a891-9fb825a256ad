package cn.bctools.rule.tools.message;


import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.auth.api.api.MessageServiceApi;
import cn.bctools.auth.api.dto.MessageDto;
import cn.bctools.auth.api.dto.PersonnelDto;
import cn.bctools.auth.api.enums.PersonnelTypeEnum;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.SystemThreadLocal;
import cn.bctools.common.utils.collection.CollectionUtils;
import cn.bctools.message.push.dto.messagepush.ReceiversDto;
import cn.bctools.rule.annotations.Rule;
import cn.bctools.rule.entity.enums.ClassType;
import cn.bctools.rule.entity.enums.RuleGroup;
import cn.bctools.rule.entity.enums.TestShowEnum;
import cn.bctools.rule.function.BaseCustomFunctionInterface;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Rule(value = "站内信-项目通知",
        group = RuleGroup.工具插件,
        test = true,
        returnType = ClassType.未识别,
        testShowEnum = TestShowEnum.JSON,
        order = 50,
//        iconUrl = "rule-Messages",
        explain = "发送站内信-项目通知工具"
)
/**
 * 和站内信组件@cn.bctools.rule.tools.message.MessageServiceImpl不同，站内信组件是直接发送，这个组件会先保存消息到sys_message再发送
 */
public class InteriorMessageServiceImpl implements BaseCustomFunctionInterface<InteriorMessageDto> {

    @Autowired
    MessageServiceApi messageServiceApi;
    @Autowired
    AuthUserServiceApi userServiceApi;

    @Override
    public Object execute(InteriorMessageDto messageDto, Map<String, Object> params) {
        MessageDto interiorMessage = new MessageDto();
        BeanUtils.copyProperties(messageDto, interiorMessage);
        //站内信
        interiorMessage.setSentType("interior");
        //项目通知只支持所有人
        List<PersonnelDto> personnelDtos = messageDto.getPersonnelDtos();
        if ("project".equals(interiorMessage.getInsideNotificationType())) {
            if (!CollectionUtils.isAnyEmpty(personnelDtos)) {
                throw new BusinessException("项目通知不需要传，只允许所有人");
            }
            personnelDtos = new ArrayList<>();
            PersonnelDto personnelDto = new PersonnelDto();
            personnelDto.setName("所有人").setType(PersonnelTypeEnum.all);
            personnelDtos.add(personnelDto);
        }
        if (CollectionUtils.isAnyEmpty(personnelDtos)) {
            throw new BusinessException("没有找到接收用户");
        }
        //设置接收人
        interiorMessage.setRecipients(personnelDtos);
        //业务id默认为应用id
        if (StringUtils.isBlank(interiorMessage.getBusinessId())) {
            interiorMessage.setBusinessId(SystemThreadLocal.get("jvsAppId"));
        }
        try {
            messageServiceApi.saveAndSend(interiorMessage);
        } catch (Exception e) {
            log.error("发送消息失败：", e);
            throw new BusinessException("发送消息失败：", e.getMessage());
        }
        return "ok";
    }

}