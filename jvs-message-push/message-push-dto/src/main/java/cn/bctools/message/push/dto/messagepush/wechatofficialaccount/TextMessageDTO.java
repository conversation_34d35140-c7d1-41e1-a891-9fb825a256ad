package cn.bctools.message.push.dto.messagepush.wechatofficialaccount;

import cn.bctools.message.push.dto.messagepush.BaseMessage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 企业微信消息发送
 *
 *
 * <AUTHOR>
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel("企业微信消息发送")
public class TextMessageDTO extends BaseMessage {
    private static final long serialVersionUID = -3289428483627765265L;

    @ApiModelProperty("请输入内容...")
    private String content;

}
