/**
 * 请假天数动态计算工具类
 */
public class LeaveDurationCalculator {
    
    /**
     * 根据指定月份动态计算请假天数
     * @param leaveDataList 请假数据列表
     * @param targetMonth 目标月份，格式："2025-06"
     */
    public static void calculateLeaveDurationForMonth(List<Map<String, Object>> leaveDataList, String targetMonth) {
        // 解析目标月份
        YearMonth yearMonth = YearMonth.parse(targetMonth);
        LocalDate monthStart = yearMonth.atDay(1);
        LocalDate monthEnd = yearMonth.atEndOfMonth();
        
        for (Map<String, Object> leaveData : leaveDataList) {
            List<String> leaveTimeList = (List<String>) leaveData.get("leaveTime");
            if (leaveTimeList == null || leaveTimeList.size() < 2) {
                continue;
            }
            
            // 解析请假开始和结束时间
            LocalDateTime startDateTime = LocalDateTime.parse(leaveTimeList.get(0), 
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime endDateTime = LocalDateTime.parse(leaveTimeList.get(1), 
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            LocalDate startDate = startDateTime.toLocalDate();
            LocalDate endDate = endDateTime.toLocalDate();
            
            // 计算与目标月份的交集
            LocalDate actualStartDate = startDate.isBefore(monthStart) ? monthStart : startDate;
            LocalDate actualEndDate = endDate.isAfter(monthEnd) ? monthEnd : endDate;
            
            // 如果请假时间完全不在目标月份内，跳过
            if (actualStartDate.isAfter(monthEnd) || actualEndDate.isBefore(monthStart)) {
                leaveData.put("leaveDuration", 0);
                continue;
            }
            
            // 计算实际请假天数
            double actualDuration = calculateWorkingDays(actualStartDate, actualEndDate, startDateTime, endDateTime);
            
            // 更新请假天数
            leaveData.put("leaveDuration", actualDuration);
        }
    }
    
    /**
     * 计算工作日天数（考虑半天请假的情况）
     */
    private static double calculateWorkingDays(LocalDate startDate, LocalDate endDate, 
                                             LocalDateTime originalStart, LocalDateTime originalEnd) {
        
        if (startDate.equals(endDate)) {
            // 同一天请假，计算小时数
            Duration duration = Duration.between(
                originalStart.toLocalTime(), 
                originalEnd.toLocalTime()
            );
            long hours = duration.toHours();
            
            // 假设一天工作8小时，超过4小时算1天，否则算0.5天
            return hours >= 4 ? (hours >= 8 ? 1.0 : 0.5) : 0.5;
        } else {
            // 跨天请假，按天数计算
            long daysBetween = ChronoUnit.DAYS.between(startDate, endDate) + 1;
            
            // 这里可以根据业务需求调整，比如排除周末
            return filterWorkingDays(startDate, endDate);
        }
    }
    
    /**
     * 过滤工作日（排除周末，可扩展排除节假日）
     */
    private static double filterWorkingDays(LocalDate startDate, LocalDate endDate) {
        double workingDays = 0;
        LocalDate current = startDate;
        
        while (!current.isAfter(endDate)) {
            // 排除周六周日
            if (current.getDayOfWeek() != DayOfWeek.SATURDAY && 
                current.getDayOfWeek() != DayOfWeek.SUNDAY) {
                workingDays++;
            }
            current = current.plusDays(1);
        }
        
        return workingDays;
    }
}

// 使用示例
public class LeaveService {

    public void processLeaveData(List<Map<String, Object>> leaveList, String month) {
        // 动态计算指定月份的请假天数
        LeaveDurationCalculator.calculateLeaveDurationForMonth(leaveList, month);

        // 处理后的数据示例：
        // 原始数据：leaveTime: ["2025-06-30 08:00:00", "2025-07-01 17:00:00"], leaveDuration: 2
        // 处理后：leaveTime: ["2025-06-30 08:00:00", "2025-07-01 17:00:00"], leaveDuration: 1
        // 因为只有2025-06-30这一天在2025-06月份内
    }

    /**
     * 批量处理请假数据的月份天数计算
     */
    @PostMapping("/leave/calculate-duration")
    public R<List<Map<String, Object>>> calculateLeaveDuration(
            @RequestBody List<Map<String, Object>> leaveList,
            @RequestParam String targetMonth) {

        try {
            // 验证月份格式
            if (!targetMonth.matches("\\d{4}-\\d{2}")) {
                return R.failed("月份格式错误，应为：YYYY-MM");
            }

            // 计算请假天数
            LeaveDurationCalculator.calculateLeaveDurationForMonth(leaveList, targetMonth);

            return R.ok(leaveList);
        } catch (Exception e) {
            log.error("计算请假天数失败", e);
            return R.failed("计算请假天数失败：" + e.getMessage());
        }
    }
}