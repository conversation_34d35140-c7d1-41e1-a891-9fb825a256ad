package cn.bctools.custom.component;

import cn.bctools.design.use.api.DataModelApi;
import cn.bctools.design.use.api.dto.DataModelSearchDto;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@Slf4j
@AllArgsConstructor
public class DataModelComponent
{
    @Autowired
    DataModelApi dataModelApi;

    public String getModelName(String id){
        return dataModelApi.getModelName(id).getData();
    }

    public List<Map<String, Object>> searchWithEcho(DataModelSearchDto dataModelSearchDto){
        return dataModelApi.searchWithEcho(dataModelSearchDto).getData();
    }

    public String saveData(String modelId, Map<String, Object> data) {
        return dataModelApi.saveData(modelId,data);
    }

    public void updateData(String appId,
                           String modelId,
                           String designId,
                           String dataId,
                           Map<String, Object> data) {
        dataModelApi.updateData(appId, modelId, designId, dataId, data);
    }


    public Map<String,Object> getMap(String modelId, String dataId,List<String> fieldKeyList) {
        return dataModelApi.getMap(modelId,dataId,fieldKeyList);
    }

    public void deleteData(String modelId,
                           String dataId) {
        dataModelApi.deleteData(modelId,dataId);
    }
}
