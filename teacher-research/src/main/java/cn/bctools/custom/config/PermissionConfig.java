package cn.bctools.custom.config;

import cn.bctools.custom.filter.TeacherInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class PermissionConfig {
    @Bean
    TeacherInterceptor teacherInterceptor(){
        return new TeacherInterceptor();
    }
}
