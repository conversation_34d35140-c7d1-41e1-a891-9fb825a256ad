package cn.bctools.custom.controller;


import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.R;
import cn.bctools.custom.entity.vo.*;
import cn.bctools.custom.service.AssignGradeTaskService;
import cn.bctools.log.annotation.Log;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 作业评分任务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Slf4j
@Api(value = "评分相关", tags = "评分相关接口")
@AllArgsConstructor
@RestController
@RequestMapping("/assign-grade-task")
public class AssignGradeTaskController {

    AssignGradeTaskService assignGradeTaskService;
    @Log
    @ApiOperation(value = "评分列表", notes = "分页查询评分列表")
    @GetMapping("/page")
    public R<Page<AssignGradeTaskVO>> page(Page<AssignGradeTaskVO> page, @Validated AssignGradeTaskQueryVO assignGradeTaskQueryVO) {
        if(StrUtil.isEmpty(assignGradeTaskQueryVO.getGradeBy())){
            assignGradeTaskQueryVO.setGradeBy(UserCurrentUtils.getUserId());
        }
        Page<AssignGradeTaskVO>  assignGradeTaskPage= assignGradeTaskService.searchPageList(page,assignGradeTaskQueryVO);
        return R.ok(assignGradeTaskPage);
    }

    @Log
    @ApiOperation(value = "导出评分列表", notes = "导出评分列表")
    @PostMapping("/exportGradeTask")
    public R<Boolean> exportGradeTask(HttpServletResponse response,Page<AssignGradeTaskVO> page, @Validated @RequestBody AssignGradeTaskQueryVO assignGradeTaskQueryVO) {
        if(StrUtil.isEmpty(assignGradeTaskQueryVO.getGradeBy())){
            assignGradeTaskQueryVO.setGradeBy(UserCurrentUtils.getUserId());
        }
        assignGradeTaskService.exportGradeTask(page,assignGradeTaskQueryVO,response);
        return R.ok(true,"导出成功");
    }


    @Log
    @ApiOperation(value = "作业评分进度列表-分页查询作业评分进度列表", notes = "分页查询作业评分进度列表")
    @GetMapping("/plan/assign/page")
    public R<Page<AssignGradeTaskPlanVO>> AssignPlanPage(Page<AssignGradeTaskPlanVO> page, @Validated AssignGradeTaskPlanQueryVO assignGradeTaskPlanQueryVO) {
        if(ObjectUtils.isEmpty(assignGradeTaskPlanQueryVO.getAssignId())){
            throw new BusinessException("作业id不能为空");
        }
        Page<AssignGradeTaskPlanVO>  assignGradeTaskPlanVOPage= assignGradeTaskService.AssignPlanPage(page,assignGradeTaskPlanQueryVO);
        return R.ok(assignGradeTaskPlanVOPage);
    }

    @Log
    @ApiOperation(value = "作业评分进度列表-作业评分进度数据统计", notes = "作业评分进度数据统计")
    @GetMapping("/plan/assign/statistic/{assignId}")
    public R<AssignPlanStatisticVO> AssignPlanStatistic(@PathVariable("assignId") String assignId) {
        AssignPlanStatisticVO result = assignGradeTaskService.AssignPlanStatistic(assignId);
        return R.ok(result);
    }

    @Log
    @ApiOperation(value = "专家评分进度列表-专家评分进度数据统计", notes = "专家评分进度数据统计")
    @GetMapping("/plan/expert/statistic/{assignId}")
    public R<ExpertPlanStatisticVO> ExpertPlanStatistic(@PathVariable("assignId") String assignId) {
        ExpertPlanStatisticVO result = assignGradeTaskService.expertPlanStatistic(assignId);
        return R.ok(result);
    }

    @Log
    @ApiOperation(value = "作业评分进度列表-查看详情", notes = "查看详情")
    @GetMapping("/plan/assign/get")
    public R<Page<AssignPlanDetailVO>> AssignPlanGet(Page page, @Validated AssignPlanDetailQueryVO assignPlanDetailQueryVO) {

        Page<AssignPlanDetailVO>  assignPlanDetailVOPage= assignGradeTaskService.AssignPlanGet(page,assignPlanDetailQueryVO);
        return R.ok(assignPlanDetailVOPage);
    }


    @Log
    @ApiOperation(value = "作业评分进度列表-查看详情-清除评审记录", notes = "查看详情-清除评审记录")
    @GetMapping("/plan/assign/get/{assignGradeId}")
    public R<Boolean> deleteGrade(@PathVariable("assignGradeId") String assignGradeId) {
        if(ObjectUtils.isEmpty(assignGradeId)){
            throw new BusinessException("作业评分id不能为空");
        }
        Boolean result =  assignGradeTaskService.deleteGrade(assignGradeId);
        return BooleanUtils.isTrue(result) ? R.ok(true,"删除成功") : R.failed(false,"删除失败");
    }


    @Log
    @ApiOperation(value = "作业评分进度列表-导出作业评分进度列表", notes = "导出作业评分进度列表")
    @PostMapping("/plan/assign/export")
    public R<Boolean> exportAssignPlan(HttpServletResponse response,Page<AssignGradeTaskPlanVO> page, @Validated @RequestBody AssignGradeTaskPlanQueryVO assignGradeTaskPlanQueryVO) {
        if(ObjectUtils.isEmpty(assignGradeTaskPlanQueryVO.getAssignId())){
            throw new BusinessException("作业id不能为空");
        }
        assignGradeTaskService.exportAssignPlan(page,assignGradeTaskPlanQueryVO,response);
        return R.ok(true,"导出成功");
    }


    @Log
    @ApiOperation(value = "评分列表-单个专家评分任务数据统计", notes = "单个专家评分任务数据统计")
    @GetMapping("/gradeTaskStatisticWithOne/{assignId}")
    public R<AssignGradeTaskStatisticVO> gradeTaskStatisticWithOne(@PathVariable("assignId") String assignId) {

        AssignGradeTaskStatisticVO result = assignGradeTaskService.gradeTaskStatisticWithOne(assignId,UserCurrentUtils.getUserId());
        return R.ok(result);
    }


    @Log
    @ApiOperation(value = "专家评分进度列表-分页查询专家评分进度列表", notes = "分页查询专家评分进度列表")
    @GetMapping("/plan/expert/page")
    public R<Page<ExpertGradePlanVO>> expertPlanPage(Page page, ExpertGradePlanQueryVO expertGradePlanQueryVO) {
        if(ObjectUtils.isEmpty(expertGradePlanQueryVO.getAssignId())){
            throw new BusinessException("作业id不能为空");
        }
        Page<ExpertGradePlanVO>  expertedPlanPage= assignGradeTaskService.expertPlanPage(page,expertGradePlanQueryVO);
        return R.ok(expertedPlanPage);
    }

    @Log
    @ApiOperation(value = "专家评分进度列表-导出专家评分进度列表", notes = "导出专家评分进度列表")
    @PostMapping("/plan/expert/export")
    public R<Boolean> exportExpertPlan(HttpServletResponse response,Page page, @Validated @RequestBody ExpertGradePlanQueryVO expertGradePlanQueryVO) {
        if(ObjectUtils.isEmpty(expertGradePlanQueryVO.getAssignId())){
            throw new BusinessException("作业id不能为空");
        }
        assignGradeTaskService.exportExpertPlan(page,expertGradePlanQueryVO,response);
        return R.ok(true,"导出成功");
    }


    @Log
    @PostMapping("/submitOrStag")
    @ApiOperation("提交/暂存评分")
    public R<Boolean> submitOrStag(@RequestBody @Validated AssignGradeVO assignGradeVO) {
        Boolean result = assignGradeTaskService.submitOrStag(assignGradeVO);
        return BooleanUtils.isTrue(result) ? R.ok(true,"成功") : R.failed(false,"失败");
    }


    @Log
    @PostMapping("/oneClickSubmit/{assignId}")
    @ApiOperation("一键提交评分")
    public R<Boolean> oneClickSubmit(@PathVariable("assignId") String assignId) {
        Boolean result = assignGradeTaskService.oneClickSubmit(assignId);
        return BooleanUtils.isTrue(result) ? R.ok(true,"成功") : R.failed(false,"失败");
    }

}
