package cn.bctools.custom.controller;


import cn.bctools.common.utils.R;
import cn.bctools.custom.entity.vo.ClassPreparationFileVo;
import cn.bctools.custom.entity.vo.CollaborativeDocumentFileVo;
import cn.bctools.custom.entity.vo.CollaborativeDocumentVo;
import cn.bctools.custom.entity.vo.ResourceVo;
import cn.bctools.custom.service.CollaborativeDocumentsService;
import cn.bctools.log.annotation.Log;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 协同文档表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-01
 */
@Slf4j
@AllArgsConstructor
@RestController
@Api(value = "协同文档管理", tags = "协同文档管理接口")
@RequestMapping("/course/collaborative-documents")
public class CollaborativeDocumentsController {
    @Autowired
    CollaborativeDocumentsService collaborativeDocumentsService;

    @GetMapping("/{courseModuleId}")
    @Transactional(rollbackFor = Exception.class)
    @Log
    @ApiOperation(value = "查看协同文档", notes = "查看协同文档")
    public R<CollaborativeDocumentVo> getResource(@PathVariable(value = "courseModuleId") String courseModuleId) {
        CollaborativeDocumentVo collaborativeDocument = collaborativeDocumentsService.getCollaborativeDocument(courseModuleId);
        return R.ok(collaborativeDocument);
    }

    @PostMapping("/create")
    @Transactional(rollbackFor = Exception.class)
    @Log
    @ApiOperation(value = "新增协同文档", notes = "新增协同文档")
    public R<Boolean> create(@Validated @RequestBody CollaborativeDocumentVo collaborativeDocumentVo) {
        Boolean flag = collaborativeDocumentsService.create(collaborativeDocumentVo);
        return R.ok(flag, "新增成功");
    }

    @PutMapping("/edit")
    @Transactional(rollbackFor = Exception.class)
    @Log
    @ApiOperation(value = "编辑协同文档", notes = "编辑协同文档")
    public R<Boolean> edit(@Validated @RequestBody CollaborativeDocumentVo collaborativeDocumentVo) {
        Boolean flag = collaborativeDocumentsService.edit(collaborativeDocumentVo);
        return R.ok(flag, "编辑成功");
    }

    @PutMapping("/edit-documents")
    @Transactional(rollbackFor = Exception.class)
    @Log
    @ApiOperation(value = "学员提交协同文档文件", notes = "学员提交协同文档文件")
    public R<Boolean> editDocuments(@Validated @RequestBody CollaborativeDocumentFileVo collaborativeDocumentFileVo) {
        Boolean flag = collaborativeDocumentsService.editDocuments(collaborativeDocumentFileVo);
        return R.ok(flag, "完成提交");
    }
}
