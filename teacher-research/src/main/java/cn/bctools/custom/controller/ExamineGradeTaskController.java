package cn.bctools.custom.controller;


import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.R;
import cn.bctools.custom.entity.vo.*;
import cn.bctools.custom.service.ExamineGradeTaskService;
import cn.bctools.log.annotation.Log;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 考核评分任务表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Slf4j
@Api(value = "考核评分相关", tags = "考核评分相关接口")
@AllArgsConstructor
@RestController
@RequestMapping("/examine-grade-task")
public class ExamineGradeTaskController {

    ExamineGradeTaskService examineGradeTaskService;
    @Log
    @ApiOperation(value = "评分列表", notes = "分页查询评分列表")
    @GetMapping("/page")
    public R<Page<ExamineGradeTaskVO>> page(Page<ExamineGradeTaskVO> page, @Validated ExamineGradeTaskQueryVO examineGradeTaskQueryVO) {
        if(StrUtil.isEmpty(examineGradeTaskQueryVO.getGradeBy())){
            examineGradeTaskQueryVO.setGradeBy(UserCurrentUtils.getUserId());
        }
        Page<ExamineGradeTaskVO>  examineGradeTaskPage= examineGradeTaskService.searchPageList(page,examineGradeTaskQueryVO);
        return R.ok(examineGradeTaskPage);
    }

    @Log
    @ApiOperation(value = "导出评分列表", notes = "导出评分列表")
    @PostMapping("/exportGradeTask")
    public R<Boolean> exportGradeTask(HttpServletResponse response,Page<ExamineGradeTaskVO> page, @Validated @RequestBody ExamineGradeTaskQueryVO examineGradeTaskQueryVO) {
        if(StrUtil.isEmpty(examineGradeTaskQueryVO.getGradeBy())){
            examineGradeTaskQueryVO.setGradeBy(UserCurrentUtils.getUserId());
        }
        examineGradeTaskService.exportGradeTask(page,examineGradeTaskQueryVO,response);
        return R.ok(true,"导出成功");
    }


    @Log
    @ApiOperation(value = "考核评分进度列表-分页查询考核评分进度列表", notes = "分页查询考核评分进度列表")
    @GetMapping("/plan/examine/page")
    public R<Page<ExamineGradeTaskPlanVO>> examinePlanPage(Page<ExamineGradeTaskPlanVO> page, @Validated ExamineGradeTaskPlanQueryVO examineGradeTaskPlanQueryVO) {
        if(ObjectUtils.isEmpty(examineGradeTaskPlanQueryVO.getExamineId())){
            throw new BusinessException("考核id不能为空");
        }
        Page<ExamineGradeTaskPlanVO>  examineGradeTaskPlanVOPage= examineGradeTaskService.ExaminePlanPage(page,examineGradeTaskPlanQueryVO);
        return R.ok(examineGradeTaskPlanVOPage);
    }

    @Log
    @ApiOperation(value = "考核评分进度列表-考核评分进度数据统计", notes = "考核评分进度数据统计")
    @GetMapping("/plan/examine/statistic/{examineId}")
    public R<ExaminePlanStatisticVO> ExaminePlanStatistic(@PathVariable("examineId") String examineId) {
        ExaminePlanStatisticVO result = examineGradeTaskService.ExaminePlanStatistic(examineId);
        return R.ok(result);
    }

    @Log
    @ApiOperation(value = "专家评分进度列表-专家评分进度数据统计", notes = "专家评分进度数据统计")
    @GetMapping("/plan/expert/statistic/{examineId}")
    public R<ExamineExpertPlanStatisticVO> ExpertPlanStatistic(@PathVariable("examineId") String ExamineId) {
        ExamineExpertPlanStatisticVO result = examineGradeTaskService.expertPlanStatistic(ExamineId);
        return R.ok(result);
    }

    @Log
    @ApiOperation(value = "考核评分进度列表-查看详情", notes = "查看详情")
    @GetMapping("/plan/examine/get")
    public R<Page<ExaminePlanDetailVO>> ExaminePlanGet(Page page, @Validated ExaminePlanDetailQueryVO ExaminePlanDetailQueryVO) {

        Page<ExaminePlanDetailVO>  ExaminePlanDetailVOPage= examineGradeTaskService.ExaminePlanGet(page,ExaminePlanDetailQueryVO);
        return R.ok(ExaminePlanDetailVOPage);
    }


    @Log
    @ApiOperation(value = "考核评分进度列表-查看详情-清除评审记录", notes = "查看详情-清除评审记录")
    @GetMapping("/plan/examine/get/{examineGradeId}")
    public R<Boolean> deleteGrade(@PathVariable("examineGradeId") String ExamineGradeId) {
        if(ObjectUtils.isEmpty(ExamineGradeId)){
            throw new BusinessException("考核评分id不能为空");
        }
        Boolean result =  examineGradeTaskService.deleteGrade(ExamineGradeId);
        return BooleanUtils.isTrue(result) ? R.ok(true,"删除成功") : R.failed(false,"删除失败");
    }


    @Log
    @ApiOperation(value = "考核评分进度列表-导出考核评分进度列表", notes = "导出考核评分进度列表")
    @PostMapping("/plan/examine/export")
    public R<Boolean> exportExaminePlan(HttpServletResponse response,Page<ExamineGradeTaskPlanVO> page, @Validated @RequestBody ExamineGradeTaskPlanQueryVO ExamineGradeTaskPlanQueryVO) {
        if(ObjectUtils.isEmpty(ExamineGradeTaskPlanQueryVO.getExamineId())){
            throw new BusinessException("考核id不能为空");
        }
        examineGradeTaskService.exportExaminePlan(page,ExamineGradeTaskPlanQueryVO,response);
        return R.ok(true,"导出成功");
    }


    @Log
    @ApiOperation(value = "评分列表-单个专家评分任务数据统计", notes = "单个专家评分任务数据统计")
    @GetMapping("/gradeTaskStatisticWithOne/{ExamineId}")
    public R<ExamineGradeTaskStatisticVO> gradeTaskStatisticWithOne(@PathVariable("ExamineId") String ExamineId) {

        ExamineGradeTaskStatisticVO result = examineGradeTaskService.gradeTaskStatisticWithOne(ExamineId,UserCurrentUtils.getUserId());
        return R.ok(result);
    }


    @Log
    @ApiOperation(value = "专家评分进度列表-分页查询专家评分进度列表", notes = "分页查询专家评分进度列表")
    @GetMapping("/plan/expert/page")
    public R<Page<ExamineExpertGradePlanVO>> expertPlanPage(Page page, ExamineExpertGradePlanQueryVO examineExpertGradePlanQueryVO) {
        if(ObjectUtils.isEmpty(examineExpertGradePlanQueryVO.getExamineId())){
            throw new BusinessException("考核id不能为空");
        }
        Page<ExamineExpertGradePlanVO>  expertedPlanPage= examineGradeTaskService.expertPlanPage(page,examineExpertGradePlanQueryVO);
        return R.ok(expertedPlanPage);
    }

    @Log
    @ApiOperation(value = "专家评分进度列表-导出专家评分进度列表", notes = "导出专家评分进度列表")
    @PostMapping("/plan/expert/export")
    public R<Boolean> exportExpertPlan(HttpServletResponse response,Page page, @Validated @RequestBody ExamineExpertGradePlanQueryVO examineExpertGradePlanQueryVO) {
        if(ObjectUtils.isEmpty(examineExpertGradePlanQueryVO.getExamineId())){
            throw new BusinessException("考核id不能为空");
        }
        examineGradeTaskService.exportExpertPlan(page,examineExpertGradePlanQueryVO,response);
        return R.ok(true,"导出成功");
    }


    @Log
    @PostMapping("/submitOrStag")
    @ApiOperation("提交/暂存评分")
    public R<Boolean> submitOrStag(@RequestBody @Validated ExamineGradeVO examineGradeVO) {
        Boolean result = examineGradeTaskService.submitOrStag(examineGradeVO);
        return BooleanUtils.isTrue(result) ? R.ok(true,"成功") : R.failed(false,"失败");
    }


    @Log
    @PostMapping("/oneClickSubmit/{examineId}")
    @ApiOperation("一键提交评分")
    public R<Boolean> oneClickSubmit(@PathVariable("examineId") String ExamineId) {
        Boolean result = examineGradeTaskService.oneClickSubmit(ExamineId);
        return BooleanUtils.isTrue(result) ? R.ok(true,"成功") : R.failed(false,"失败");
    }

}
