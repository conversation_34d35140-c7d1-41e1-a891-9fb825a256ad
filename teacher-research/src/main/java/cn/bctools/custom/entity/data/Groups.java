package cn.bctools.custom.entity.data;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 协同研究工作室小组表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-02
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("course_groups")
@ApiModel(value="Groups对象", description="协同研究工作室小组表")
public class Groups implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "协同研究工作室id")
    @TableField("course_id")
    private String courseId;

    @ApiModelProperty(value = "父级群组id")
    @TableField("parent_id")
    private String parentId;

    @ApiModelProperty(value = "群组名称")
    @TableField("group_name")
    private String groupName;

    @ApiModelProperty(value = "群组编码")
    @TableField("group_code")
    private String groupCode;

    @ApiModelProperty(value = "群组类型")
    @TableField("group_type")
    private String groupType;

    @ApiModelProperty(value = "群组描述")
    @TableField("group_desc")
    private String groupDesc;

    @ApiModelProperty(value = "创建人")
    @TableField("create_by_id")
    private String createUser;

    @ApiModelProperty(value = "创建人姓名")
    @TableField("create_by")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField("update_by_id")
    private String updateUser;

    @ApiModelProperty(value = "更新人姓名")
    @TableField("update_by")
    private String updateUserName;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识（0-正常,1-删除）")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;

    @ApiModelProperty(value = "租户id")
    @TableField("tenant_id")
    private String tenantId;


}
