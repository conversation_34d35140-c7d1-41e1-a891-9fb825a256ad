package cn.bctools.custom.entity.data;

import cn.bctools.custom.entity.enums.ModuleType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 协同研究工作室活动模块表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-02
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("course_modules")
@ApiModel(value = "Modules对象", description = "协同研究工作室活动模块表")
public class Modules implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    @ApiModelProperty(value = "协同研究工作室id")
    @TableField("course_id")
    private String courseId;

    @ApiModelProperty(value = "模块id")
    @TableField("module")
    private String module;

    @ApiModelProperty(value = "模块主表id")
    @TableField("instance")
    private String instance;

    @ApiModelProperty(value = "章节id")
    @TableField("section")
    private String section;

    @ApiModelProperty(value = "显隐：false隐藏，true显示")
    @TableField("visible")
    private Boolean visible;

    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识（0-正常,1-删除）")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;

    @ApiModelProperty(value = "是否允许下载附件 true是 false否")
    @TableField("can_download")
    private Boolean canDownload;

    @ApiModelProperty(value = "租户id")
    @TableField("tenant_id")
    private String tenantId;

    @ApiModelProperty(value = "创建人")
    @TableField("create_by")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    @TableField("update_by")
    private String updateBy;

    @ApiModelProperty(value = "创建人")
    @TableField("create_by_id")
    private String createById;

    @ApiModelProperty(value = "更新人")
    @TableField("update_by_id")
    private String updateById;
}
