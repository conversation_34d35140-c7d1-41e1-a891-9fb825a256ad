package cn.bctools.custom.entity.data;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 协同研究工作室用户学段学科
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-02
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("course_user_subjects")
@ApiModel(value = "UserSubjects对象", description = "协同研究工作室用户学段学科")
public class UserSubjects implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty(value = "协同研究工作室id")
    @TableField("course_id")
    private String courseId;

    @ApiModelProperty(value = "用户id")
    @TableField("user_id")
    private String userId;

    @ApiModelProperty(value = "学段学科id")
    @TableField("per_and_sub_id")
    private String perAndSubId;

    @ApiModelProperty(value = "学段学科")
    @TableField("per_and_sub")
    private String perAndSub;

    @ApiModelProperty(value = "学段id")
    @TableField("period_id")
    private String periodId;

    @ApiModelProperty(value = "学科id")
    @TableField("subject_id")
    private String subjectId;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "租户id")
    @TableField("tenant_id")
    private String tenantId;


}
