package cn.bctools.custom.entity.template.user;

import cn.bctools.auth.api.api.AuthDeptServiceApi;
import cn.bctools.auth.api.api.AuthRoleServiceApi;
import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.auth.api.dto.SysDeptDto;
import cn.bctools.auth.api.dto.SysRoleDto;
import cn.bctools.common.entity.dto.DeptDto;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.custom.constant.PermissionConstant;
import cn.bctools.custom.constant.RoleConstant;
import cn.bctools.custom.entity.data.*;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.enums.RoleType;
import cn.bctools.custom.entity.vo.UsersVo;
import cn.bctools.custom.service.*;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.design.use.api.TreeApi;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.lang.Validator;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: kingcii
 * @Description: 导入用户Excel解析处理
 */
public class ImportUserListener implements ReadListener<UserImportFeedbackExcelTemplate> {

    /**
     * 用户信息
     */
    private UserDto userDto;
    /**
     * 所有部门
     */
    private List<SysDeptDto> deptTree;
    /**
     * 部门路径缓存
     * Map<部门全路径, Map<部门名称, 部门id>>
     */
    private Map<String, Map<String, String>> deptPathCache = new HashMap<>(1);
    /**
     * 部门路径缓存
     * Map<部门全路径, Map<部门名称, 部门id>>
     */
    private Map<String, String> deptMap = new LinkedHashMap<>();
    /**
     * 需要自动创建的部门信息
     */
    private List<SysDeptDto> addDepts = new ArrayList<>();

    private String courseId;

    private final TreeApi treeApi = SpringContextUtil.getBean(TreeApi.class);
    private final UsersService usersService = SpringContextUtil.getBean(UsersService.class);
    private final GroupsService groupsService = SpringContextUtil.getBean(GroupsService.class);
    private final UserGroupsService userGroupsService = SpringContextUtil.getBean(UserGroupsService.class);
    private final UserSubjectsService userSubjectsService = SpringContextUtil.getBean(UserSubjectsService.class);
    private final UserRolesService userRolesService = SpringContextUtil.getBean(UserRolesService.class);
    private final AuthRoleServiceApi authRoleServiceApi = SpringContextUtil.getBean(AuthRoleServiceApi.class);
    private final AuthUserServiceApi authUserServiceApi = SpringContextUtil.getBean(AuthUserServiceApi.class);

    public ImportUserListener(UserDto userDto, List<SysDeptDto> deptTree, String courseId) {
        this.deptTree = deptTree;
        this.userDto = userDto;
        this.courseId = courseId;
    }

    /**
     * 单次缓存的数据量
     */
    private static final int BATCH_COUNT = 1000;

    /**
     * 缓存数据
     */
    private List<UserImportFeedbackExcelTemplate> userExcelDatas = new ArrayList<>();

    /**
     * 成员缓存数据
     */
    private List<UsersVo> usersVoDatas = new ArrayList<>();

    /**
     * 导入反馈
     */
    private List<UserImportFeedbackExcelTemplate> importFeedbackList = new ArrayList<>();

    /**
     * 错误记录
     */
    private List<String> errorMsgList = new ArrayList<>();

    /**
     * 导入成功数、导入失败数
     */
    private int successNum = 0;
    private int failNum = 0;

    private String errorMsg;

    private boolean EXCEL_TEMPLATE_ERROR = false;

    private final String COMMA_SPLIT_REGEX = ",|，";
    public static final String REAL_NAME_REGEX = "^[\\u4e00-\\u9fa5A-Za-z]{1,50}$";
    public static final String REAL_NAME_INTERVALS_REGEX = "^(?![·])(?!.*[·]{2})[\\u4e00-\\u9fa5·]{1,50}(?<![·])$";


    /**
     * 在转换异常 获取其他异常下会调用本接口。抛出异常则停止读取。如果这里不抛出异常则 继续读取下一行
     *
     * @param exception
     * @param context
     * @throws Exception
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        if (EXCEL_TEMPLATE_ERROR) {
            throw new Exception("模板错误");
        }
        throw new Exception(exception);
    }

    /**
     * 这里会一行行的返回头
     *
     * @param headMap
     * @param context
     */
    @Override
    public void invokeHead(Map<Integer, CellData> headMap, AnalysisContext context) {
        Map<Integer, String> map = new LinkedHashMap<>();
        int num = 0;
        for (CellData cellData : headMap.values()) {
            map.put(num, cellData.toString());
            num++;
        }
        if (!headMap.containsKey(0) || !headMap.containsKey(1) || !headMap.containsKey(2) ||
                !headMap.containsKey(3) || !headMap.containsKey(4) || !headMap.containsKey(5) ||
                !map.get(0).equals("*姓名") || !map.get(1).equals("*手机号") || !map.get(2).equals("学段学科") ||
                !map.get(3).equals("单位") || !map.get(4).equals("小组") || !map.get(5).equals("角色")) {

            EXCEL_TEMPLATE_ERROR = true;
            throw new BusinessException("模板错误");

        }
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data
     * @param context
     */
    @Override
    public void invoke(UserImportFeedbackExcelTemplate data, AnalysisContext context) {
        ReadRowHolder readRowHolder = context.readRowHolder();
        Integer rowIndex = readRowHolder.getRowIndex();


        // 校验数据
        List<String> checkList = checkData(data);
//        List<String> checkList = new ArrayList<>();

        if (CollectionUtils.isEmpty(checkList)) {
            // 记录成功数
            successNum++;
            data.setRowIndex(rowIndex);
            data.setFeedback("导入成功！");
            // 加入缓存
            userExcelDatas.add(data);
        } else {
            // 记录失败数
            failNum++;
            data.setRowIndex(rowIndex);
            data.setFeedback("导入失败，" + checkList.toString().replace("[", "").replace("]", ""));
            // 记录失败行信息
            errorMsgList.add("第" + data.getRowIndex() + "行：" + checkList.toString().replace("[", "").replace("]", ""));
        }

        data.setSuccessNum(successNum);
        data.setFailNum(failNum);
        // 添加导入反馈
        importFeedbackList.add(data);

        // 分批处理
        if (userExcelDatas.size() >= BATCH_COUNT) {
            checkErrorMsg();
            // 清理缓存
            clear();
            convertData();
            // 当前批次处理完毕，清空缓存
            userExcelDatas = new ArrayList<>();
        }
    }

    /**
     * 额外信息
     *
     * @param extra
     * @param context
     */
    @Override
    public void extra(CellExtra extra, AnalysisContext context) {
        // 不处理额外信息
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        checkErrorMsg();
        userExcelDatas.sort(Comparator.comparing(UserImportFeedbackExcelTemplate::getRowIndex));
        // 清理缓存
        clear();
        convertData();
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        return true;
    }

    /**
     * 数据校验
     *
     * @param data
     */
    private List<String> checkData(UserImportFeedbackExcelTemplate data) {
        List<String> countErrorMsg = new ArrayList<>();
        if (StringUtils.isBlank(data.getRealName())) {
            errorMsg = "姓名不能为空";
            countErrorMsg.add(errorMsg);
        }
//        if (StringUtils.isNotBlank(data.getRealName()) &&
//                !(data.getRealName().matches(REAL_NAME_REGEX) || data.getRealName().matches(REAL_NAME_INTERVALS_REGEX))) {
//            errorMsg = "姓名只能包含中文和英文字母，长度最大为50个字符";
//            countErrorMsg.add(errorMsg);
//        }
        UserDto userDto = authUserServiceApi.getOneByRealName(data.getRealName()).getData();
        if (ObjectNull.isNull(userDto)) {
            errorMsg = "系统不存在此用户";
            countErrorMsg.add(errorMsg);
        }else{
            if (StringUtils.isNotBlank(data.getRealName())) {
                if (StringUtils.isNotBlank(data.getPhone())) {
                    if (!userDto.getPhone().equals(data.getPhone())) {
                        errorMsg = "该用户的姓名与手机号不匹配";
                        countErrorMsg.add(errorMsg);
                    }
                }
            }
            if (StringUtils.isNotBlank(data.getRealName())) {
                int checkNum = usersService.checkUser(courseId, userDto.getId());
                long count = usersService.count(new LambdaQueryWrapper<Users>().eq(Users::getUserId, userDto.getId()));
                if ((ObjectNull.isNotNull(checkNum) && !(checkNum < 1)) && count > 0) {
                    errorMsg = "当前被邀请用户已提交加入申请或已存在协同研究工作室内";
                    countErrorMsg.add(errorMsg);
                }
            }
        }

        if (StringUtils.isBlank(data.getPhone())) {
            errorMsg = "手机号不能为空";
            countErrorMsg.add(errorMsg);
        }
        if (StringUtils.isNotBlank(data.getPhone()) && Boolean.FALSE.equals(Validator.isMobile(data.getPhone()))) {
            errorMsg = "手机号格式错误";
            countErrorMsg.add(errorMsg);
        }
        if (StringUtils.isNotBlank(data.getPhone()) && Boolean.TRUE.equals(Validator.isMobile(data.getPhone()))) {
            if (!checkPhone(data)) {
                errorMsg = "系统暂无此手机号";
                countErrorMsg.add(errorMsg);
            }
        }
        if (StringUtils.isNotBlank(data.getOrgName()) && checkDept(data)) {
            errorMsg = "系统不存在此单位";
            countErrorMsg.add(errorMsg);
        }
        if (StringUtils.isNotBlank(data.getGroupName())) {
            Groups group = groupsService.getOne(new LambdaQueryWrapper<Groups>()
                    .eq(Groups::getCourseId, courseId)
                    .eq(Groups::getGroupName, data.getGroupName())
                    .last("LIMIT 1"));
            if (ObjectNull.isNull(group)) {
                errorMsg = "该协同研究工作室不存在此小组";
                countErrorMsg.add(errorMsg);
            }
        }
        if (StringUtils.isNotBlank(data.getRoleNames())) {
            String[] splitList = data.getRoleNames().split(COMMA_SPLIT_REGEX);
//            Object result = authRoleServiceApi.checkRoleByRoleNames(Arrays.asList(splitList)).getData();
//            if (ObjectUtils.notEqual(result, true)) {
//                errorMsg = "系统无该角色";
//                countErrorMsg.add(errorMsg);
//            }
            for (String roleName : splitList) {
                if (!RoleType.getUsersRolesByLimit4().contains(roleName)) {
                    errorMsg = "\"" + roleName + "\"超出协同研究工作室成员权限";
                    countErrorMsg.add(errorMsg);
                }
            }
        }
        if (StringUtils.isNotBlank(data.getPerAndSubs()) && !data.getPerAndSubs().contains("/")) {
            errorMsg = "学段学科缺少必要的分隔符“/”";
            countErrorMsg.add(errorMsg);
        }
        if (StringUtils.isNotBlank(data.getPerAndSubs())) {
            Map<String, String> perAndSubMap = treeApi.getPerAndSubMap().getData();
            String[] splitList = data.getPerAndSubs().split(COMMA_SPLIT_REGEX);
            for (String str : splitList) {
                String perAndSubId = perAndSubMap.get(str);
                if (ObjectNull.isNull(perAndSubId)) {
                    errorMsg = "系统不存在此学段学科";
                    countErrorMsg.add(errorMsg);
                    break;
                }
            }
        }
        // 判断excel中重复数据
        // 判断重复手机号
        boolean isDuplicatePhone = userExcelDatas.stream().anyMatch(user ->
                user.getPhone().equals(data.getPhone()));
        if (isDuplicatePhone) {
            errorMsg = "文档内有相同的手机号，请检查文档信息";
            countErrorMsg.add(errorMsg);
        }
        // 判断重复姓名
//        boolean isDuplicateRealName = userExcelDatas.stream().anyMatch(user ->
//                user.getRealName().equals(data.getRealName()));
//        if (isDuplicateRealName) {
//            errorMsg = "文档内有相同的姓名，请检查文档信息";
//            countErrorMsg.add(errorMsg);
//        }
        return countErrorMsg;
    }

    public Boolean checkPhone(UserImportFeedbackExcelTemplate data) {
        String phone = data.getPhone();
        return authUserServiceApi.checkPhone(phone).getData();
    }

    public Boolean checkDept(UserImportFeedbackExcelTemplate data) {
        // 获取部门路径
        String deptNamePath = data.getOrgName();

        // 检查缓存中是否包含部门路径
        if (deptPathCache.containsKey(deptNamePath)) {
            // 如果缓存中有，说明部门存在，返回 false
            return false;
        }

        String[] deptNameArr = deptNamePath.split("/");
        // 如果没有找到匹配的子部门，返回匹配失败（true）
        return matchDeptPathRecursive(deptNameArr, deptTree);

    }

    private static boolean matchDeptPathRecursive(String[] deptNames, List<SysDeptDto> currentDepts) {
        if (deptNames.length == 0) {
            return false;
        }

        String currentDeptName = deptNames[0];
        for (SysDeptDto dept : currentDepts) {
            if (dept.getName().equals(currentDeptName)) {
                String[] remainingNames = Arrays.copyOfRange(deptNames, 1, deptNames.length);
                // 递归匹配剩余路径
                if (!matchDeptPathRecursive(remainingNames, dept.getChildList())) {
                    // 如果递归返回false，表示剩余路径也匹配成功，返回匹配成功（false）
                    return false;
                }
            }
        }
        // 如果没有找到匹配的子部门，返回匹配失败（true）
        return true;
    }

    /**
     * 校验是否有异常信息，有则抛出
     */
    private void checkErrorMsg() {
        if (StringUtils.isNotBlank(errorMsg)) {
            //throw new BusinessException(errorMsg);
        }
    }

    /**
     * 清理缓存
     */
    private void clear() {
    }

    /**
     * 数据转换
     */
    private void convertData() {
        if (CollectionUtils.isEmpty(userExcelDatas)) {
            return;
        }
        // 得到部门路径集合
        Set<String> deptNamePaths = userExcelDatas.stream().filter(u -> StringUtils.isNotBlank(u.getOrgName())).map(UserImportFeedbackExcelTemplate::getOrgName).collect(Collectors.toSet());
        deptNamePaths.forEach(deptNamePath -> {
            if (Boolean.FALSE.equals(deptPathCache.containsKey(deptNamePath))) {
                String[] deptNameArr = deptNamePath.split("/");
                parseDept(deptNamePath, userDto.getTenantId(), deptTree, deptTree, null, deptNameArr, 0, deptNameArr.length);
            }
        });

        for (UserImportFeedbackExcelTemplate data : userExcelDatas) {
            UsersVo usersVo = new UsersVo();
            usersVo.setCourseId(courseId);
            usersVo.setPhone(data.getPhone());
            UserDto user = authUserServiceApi.getOneByRealName(data.getRealName()).getData();
            usersVo.setUserId(user.getId());
//            usersVo.setUserId("34f0520c54dded9e9dccc7b8543fd76c");
            usersVo.setOrgCode(ObjectNull.isNotNull(data.getOrgName()) && ObjectNull.isNotNull(deptMap) ? deptMap.get(data.getOrgName()) : user.getDeptId());
//            usersVo.setOrgCode("440000000001");
            if (ObjectNull.isNotNull(data.getGroupName())) {
                Groups group = groupsService.getOne(new LambdaQueryWrapper<Groups>()
                        .eq(Groups::getCourseId, courseId)
                        .eq(Groups::getGroupName, data.getGroupName())
                        .last("LIMIT 1"));
                usersVo.setGroupId(group.getId());
            }

            if (ObjectNull.isNotNull(data.getPerAndSubs())) {
                List<UserSubjects> userSubjects = new ArrayList<>();
                Map<String, String> perAndSubMap = treeApi.getPerAndSubMap().getData();
                String[] splitList = data.getPerAndSubs().split(COMMA_SPLIT_REGEX);
                String[] newSplitList = Arrays.stream(splitList).distinct().toArray(String[]::new);
                for (String str : newSplitList) {
                    String perAndSubId = perAndSubMap.get(str);
//                    String perAndSubId = "123-123";
                    String[] strSplit = perAndSubId.split("-");
                    String periodId = strSplit[0];
                    String subjectId = strSplit[1];
                    if (ObjectNull.isNotNull(perAndSubId)) {
                        UserSubjects userPeriodSubject = new UserSubjects();
                        userPeriodSubject.setUserId(user.getId());
//                        userPeriodSubject.setUserId("34f0520c54dded9e9dccc7b8543fd76c");
                        userPeriodSubject.setPerAndSub(str);
                        userPeriodSubject.setPerAndSubId(perAndSubId);
                        userPeriodSubject.setPeriodId(periodId);
                        userPeriodSubject.setSubjectId(subjectId);
                        userSubjects.add(userPeriodSubject);
                    }
                }
                usersVo.setPerAndSubList(userSubjects);
            } else if (ObjectNull.isNotNull(user.getPerAndSubNames())) {
                List<UserSubjects> userSubjects = new ArrayList<>();
                Map<String, String> perAndSubMap = treeApi.getPerAndSubMap().getData();
                String[] splitList = user.getPerAndSubNames().split(COMMA_SPLIT_REGEX);
                String[] newSplitList = Arrays.stream(splitList).distinct().toArray(String[]::new);
                for (String str : newSplitList) {
                    String perAndSubId = perAndSubMap.get(str);
//                    String perAndSubId = "123-123";
                    String[] strSplit = perAndSubId.split("-");
                    String periodId = strSplit[0];
                    String subjectId = strSplit[1];
                    if (ObjectNull.isNotNull(perAndSubId)) {
                        UserSubjects userPeriodSubject = new UserSubjects();
                        userPeriodSubject.setUserId(user.getId());
//                        userPeriodSubject.setUserId("34f0520c54dded9e9dccc7b8543fd76c");
                        userPeriodSubject.setPerAndSub(str);
                        userPeriodSubject.setPerAndSubId(perAndSubId);
                        userPeriodSubject.setPeriodId(periodId);
                        userPeriodSubject.setSubjectId(subjectId);
                        userSubjects.add(userPeriodSubject);
                    }
                }
                usersVo.setPerAndSubList(userSubjects);
            }

            if (ObjectNull.isNotNull(data.getRoleNames())) {
                List<UserRoles> userRoles = new ArrayList<>();
                String[] splitList = data.getRoleNames().split(COMMA_SPLIT_REGEX);
                String[] newSplitList = Arrays.stream(splitList).distinct().toArray(String[]::new);
                for (String str : newSplitList) {
                    UserRoles userRole = new UserRoles();
                    List<SysRoleDto> sysRoleDtos = authRoleServiceApi.getAll().getData();
                    for (SysRoleDto sysRoleDto : sysRoleDtos) {
                        if (sysRoleDto.getRoleName().equals(str)) {
                            userRole.setRoleId(sysRoleDto.getId());
                            userRoles.add(userRole);
                        }
                    }
//                    userRole.setRoleId("roleid");
//                    userRoles.add(userRole);
                }
                usersVo.setRoles(userRoles);
            } else {
                UserRoles userRole = new UserRoles();
                userRole.setRoleId(RoleType.student.getValue());
                usersVo.setRoles(Collections.singletonList(userRole));
            }

            usersVoDatas.add(usersVo);
        }

        // 保存
        save();
    }

    /**
     * 保存
     */
    private void save() {
        // 保存用户信息
        saveUser();
    }

    /**
     * 解析部门
     *
     * @param deptPath     部门全路径名
     * @param rootParentId 最上级父节点（租户id）
     * @param deptTree     所有部门
     * @param deptDtos     用以迭代查询的部门信息
     * @param parentIds    父级部门id集合
     * @param deptNames    部门名集合（由部门全路径名分解）
     * @param i            部门名集合中当前部门名下标
     * @param size         部门名集合中总部门名数量
     */
    private void parseDept(String deptPath, String rootParentId, List<SysDeptDto> deptTree, List<SysDeptDto> deptDtos, Set<String> parentIds, String[] deptNames, int i, int size) {
        String deptName = deptNames[i];
        i++;
        List<SysDeptDto> sysDeptDtos = deptDtos.stream().filter(dept -> dept.getName().equals(deptName)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sysDeptDtos)) {
            // 得到父级id集合
            if (CollectionUtils.isEmpty(parentIds)) {
                parentIds = deptDtos.stream().map(SysDeptDto::getParentId).collect(Collectors.toSet());
                // 将根目录加入集合。 防止数据库中无部门信息时，出现栈溢出
                parentIds.add(rootParentId);
            }
            buildDept(rootParentId, deptTree, parentIds, deptName);
            i = 0;
            parseDept(deptPath, null, deptTree, deptTree, parentIds, deptNames, i, size);
        } else {
            if (i == size) {
                Map<String, String> dept = new HashMap<>(1);
                dept.put(deptName, sysDeptDtos.get(0).getId());
                deptMap.put(deptPath, sysDeptDtos.get(0).getId());
                deptPathCache.put(deptPath, dept);
                return;
            } else {
                // 获取匹配的所有下级部门
                List<SysDeptDto> nextSysDeptDtos = new ArrayList<>();
                sysDeptDtos.forEach(d -> {
                    if (CollectionUtils.isNotEmpty(d.getChildList())) {
                        nextSysDeptDtos.addAll(d.getChildList());
                    }
                });
                parentIds = sysDeptDtos.stream().map(SysDeptDto::getId).collect(Collectors.toSet());
                parseDept(deptPath, null, deptTree, nextSysDeptDtos, parentIds, deptNames, i, size);
            }
        }
    }

    /**
     * 构造新部门节点
     *
     * @param rootParentId 最上级父节点（租户id）
     * @param deptTree     所有部门
     * @param parentIds    父级节点id集合
     * @param deptName     部门名
     */
    private void buildDept(String rootParentId, List<SysDeptDto> deptTree, Set<String> parentIds, String deptName) {
        boolean addDept = Boolean.FALSE;
        List<SysDeptDto> depts = deptTree.stream().filter(d -> parentIds.contains(d.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(depts)) {
            for (SysDeptDto dept : depts) {
                SysDeptDto sysDeptDto = createDeptDto(deptName, dept.getId());
                if (CollectionUtils.isEmpty(dept.getChildList())) {
                    dept.setChildList(new ArrayList() {{
                        add(sysDeptDto);
                    }});
                } else {
                    dept.getChildList().add(sysDeptDto);
                }
                addDepts.add(sysDeptDto);
                addDept = Boolean.TRUE;
            }
        } else {
            if (parentIds.contains(rootParentId)) {
                SysDeptDto sysDeptDto = createDeptDto(deptName, rootParentId);
                deptTree.add(sysDeptDto);
                addDepts.add(sysDeptDto);
                addDept = Boolean.TRUE;
            }
        }

        if (Boolean.FALSE.equals(addDept)) {
            deptTree.forEach(d -> {
                buildDept(rootParentId, Optional.ofNullable(d.getChildList()).orElse(new ArrayList<>()), parentIds, deptName);
            });
        }
    }

    /**
     * 创建系统部门信息对象
     *
     * @param deptName 部门名
     * @param parentId 父级部门id
     * @return 系统部门信息
     */
    private SysDeptDto createDeptDto(String deptName, String parentId) {
        return new SysDeptDto()
                .setId(IdWorker.get32UUID())
                .setName(deptName)
                .setParentId(parentId);
    }

    /**
     * 保存用户
     */
    private void saveUser() {
        if (CollectionUtils.isEmpty(usersVoDatas)) {
            return;
        }

        List<Users> users = new ArrayList<>();
        List<UserGroups> userGroups = new ArrayList<>();
        List<UserRoles> userRoles = new ArrayList<>();
        List<UserSubjects> userSubjects = new ArrayList<>();

        usersVoDatas.forEach(u -> {
            // 通用数据
            UserDto currentUser = UserCurrentUtils.getCurrentUser();
            String tenantId = currentUser.getTenantId();
            String courseId = u.getCourseId();
            String userId = u.getUserId();
            LocalDateTime currentTime = LocalDateTime.now();

            // 处理vo数据， 转换为成员表所需实体类
            Users user = BeanCopyUtil.copy(u, Users.class);
            user.setEnrolType(1);
            user.setTenantId(tenantId);
            user.setCreateById(currentUser.getId());
            user.setCreateBy(currentUser.getRealName());
            user.setCreateTime(currentTime);
            users.add(user);

            // 处理vo数据， 转换为小组成员表所需实体类
            UserGroups userGroup = new UserGroups();
            userGroup.setCourseId(courseId);
            userGroup.setGroupId(u.getGroupId());
            userGroup.setUserId(userId);
            userGroup.setCreateUser(currentUser.getId());
            userGroup.setCreateUserName(currentUser.getRealName());
            userGroup.setCreateTime(currentTime);
            userGroup.setTenantId(tenantId);
            userGroups.add(userGroup);

            // 处理vo数据， 转换为成员角色表所需实体类
            if (ObjectNull.isNotNull(u.getRoles())) {
                for (UserRoles userRole : u.getRoles()) {
                    userRole.setCourseId(courseId);
                    userRole.setUserId(userId);
                    userRole.setCreateTime(currentTime);
                    userRole.setTenantId(tenantId);
                    userRoles.add(userRole);
                    try {
                        authRoleServiceApi.setUser(userRole.getRoleId(), Collections.singletonList(userId));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            // 处理vo数据， 转换为成员学科学段表所需实体类
            if (ObjectNull.isNotNull(u.getPerAndSubList())) {
                for (UserSubjects subjects : u.getPerAndSubList()) {
                    subjects.setCourseId(courseId);
                    subjects.setUserId(userId);
                    subjects.setCreateTime(currentTime);
                    subjects.setTenantId(tenantId);
                    userSubjects.add(subjects);
                }
            }
        });

        usersService.saveBatch(users);
        userGroupsService.saveBatch(userGroups);
        if (ObjectNull.isNotNull(userRoles)) {
            userRolesService.saveBatch(userRoles);
        }
        if (ObjectNull.isNotNull(userSubjects)) {
            userSubjectsService.saveBatch(userSubjects);
        }

    }

    /**
     * 返回导入反馈
     */
    public List<UserImportFeedbackExcelTemplate> getFeedbackList() {
        return importFeedbackList;
    }

    /**
     * 返回是否发生错误
     */
    public Boolean hasErrorMessage() {
        return errorMsg != null;
    }

    /**
     * 返回错误信息
     */
    public List<String> getErrorMsgList() {
        return errorMsgList;
    }
}
