package cn.bctools.custom.entity.vo;

import cn.bctools.custom.entity.data.ActivityAgendas;
import cn.bctools.custom.entity.data.ModuleFiles;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @project jvs-backend
 * @date 2024/6/4
 * @description
 * @package cn.bctools.custom.entity.vo
 */
@Data
@Accessors(chain = true)
@ApiModel("活动议程传输对象VO")
public class ActivityAgendasVO extends ActivityAgendas {

    @ApiModelProperty(value = "活动附件列表")
    private List<ModuleFiles> fileList;
}
