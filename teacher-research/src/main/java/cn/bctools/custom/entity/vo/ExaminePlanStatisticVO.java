package cn.bctools.custom.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @project jvs-backend
 * @date 2024/6/11
 * @description
 * @package cn.bctools.custom.entity.vo
 */
@Accessors(chain = true)
@Data
@ApiModel(value = "考核评分进度统计对象VO")
public class ExaminePlanStatisticVO {

    @ApiModelProperty(value = "考核对象VO")
    private ExamineVO examineVO;

    @ApiModelProperty(value = "参与人数")
    private Integer joinNum;
    @ApiModelProperty(value = "已完成数")
    private Integer finishNum;

}
