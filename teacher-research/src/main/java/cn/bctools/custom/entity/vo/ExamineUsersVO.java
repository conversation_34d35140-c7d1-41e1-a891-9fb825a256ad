package cn.bctools.custom.entity.vo;

import cn.bctools.custom.entity.data.ExamineUsers;
import cn.bctools.custom.entity.data.ModuleFiles;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @project jvs-backend
 * @date 2024/6/5
 * @description
 * @package cn.bctools.custom.entity.vo
 */
@Data
@Accessors(chain = true)
@ApiModel("学员考核传输对象VO")
public class ExamineUsersVO extends ExamineUsers {

    @ApiModelProperty(value = "学员提交附件信息")
    private List<ModuleFiles> submitFileList;
}
