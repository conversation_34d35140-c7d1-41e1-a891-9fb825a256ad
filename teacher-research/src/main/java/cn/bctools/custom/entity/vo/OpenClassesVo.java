package cn.bctools.custom.entity.vo;

import cn.bctools.custom.entity.data.ModuleFiles;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
public class OpenClassesVo {

    private String id;

    @ApiModelProperty(value = "小组id，0为所有小组，多选用 ,隔开")
    @NotEmpty(message = "小组不能为空")
    private String groupIds;

    @ApiModelProperty(value = "章节id")
    @NotEmpty(message = "环节不能为空")
    private String section;

    @ApiModelProperty(value = "显隐：false隐藏，true显示")
    @NotNull(message = "显隐不能为空")
    private Boolean visible;

    @ApiModelProperty(value = "文件资源")
    List<ModuleFiles> fileList;

    @ApiModelProperty(value = "协同研究工作室id")
    @NotEmpty(message = "研修活动id不能为空")
    private String courseId;

    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "模块类型")
    private String module;

    @ApiModelProperty(value = "创建人id")
    private String createById;
    @ApiModelProperty(value = "是否允许下载附件 true是 false否")
    private Boolean canDownload;
    @ApiModelProperty(value = "是否有下载权限 true是 false否")
    private Boolean enableDownload;
}
