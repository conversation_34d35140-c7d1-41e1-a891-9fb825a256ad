package cn.bctools.custom.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@ApiModel(value="ResourceAccessVo对象", description="资源访问日志传输对象")
@Accessors(chain = true)
@Data
public class ResourceAccessVo {
    @ApiModelProperty(value = "日志id")
    private String id;

    @ApiModelProperty(value = "共同体id")
    private String courseId;

    @NotNull(message = "活动id不能为空")
    @ApiModelProperty(value = "活动id")
    private String cmId;

    @NotNull(message = "文件id不能为空")
    @ApiModelProperty(value = "文件id")
    private String fileId;

    @ApiModelProperty(value = "访问时长(单位毫秒)")
    private Long accessDuration;

}
