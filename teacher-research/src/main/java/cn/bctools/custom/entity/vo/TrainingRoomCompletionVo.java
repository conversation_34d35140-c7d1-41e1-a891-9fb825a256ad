package cn.bctools.custom.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


@Accessors(chain = true)
@Data
@ApiModel(value = "研修室完成情况Vo")
public class TrainingRoomCompletionVo {
    @ApiModelProperty(value = "用户id")
    private String id;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "学段学科")
    private String periodAndSubjects;

    @ApiModelProperty(value = "所属单位")
    private String deptName;

    @ApiModelProperty(value = "最后浏览时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastViewTime;
}
