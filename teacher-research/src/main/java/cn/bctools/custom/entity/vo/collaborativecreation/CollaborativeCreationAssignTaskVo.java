package cn.bctools.custom.entity.vo.collaborativecreation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-25
 */
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = false)
public class CollaborativeCreationAssignTaskVo {

    @ApiModelProperty(value = "协同创作 instanceId", required = true)
    @NotEmpty(message = "协同创作 instanceId 不能为空")
    private String id;

    @ApiModelProperty(value = "任务分配阶段的任务列表，完成分配任务后会生成实际的在线文档", required = true)
    @NotEmpty(message = "任务列表不能为空")
    private List<CollaborativeCreationAssignTaskListVo> assignTaskList;
}
