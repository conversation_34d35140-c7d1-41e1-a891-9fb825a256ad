package cn.bctools.custom.entity.vo.collaborativecreation;

import cn.bctools.custom.entity.enums.CollaborativeCreationOutlineType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024-07-24
 */
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = false)
public class CollaborativeCreationUpdateOutlineVo {

    @ApiModelProperty(value = "协同创作 instanceId", required = true)
    @NotEmpty(message = "协同创作 instanceId 不能为空")
    private String id;

    @ApiModelProperty(value = "大纲名称", required = true)
    @NotEmpty(message = "大纲名称不能为空")
    private String name;

    @ApiModelProperty(value = "大纲编写方式，目前只支持手动创建", required = true)
    @NotNull(message = "大纲编写方式不能为空")
    private CollaborativeCreationOutlineType type;

    @ApiModelProperty(value = "大纲内容（富文本）", required = true)
    @NotEmpty(message = "大纲内容不能为空")
    private String content;
}
