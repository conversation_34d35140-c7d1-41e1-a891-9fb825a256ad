package cn.bctools.custom.entity.vo.stat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("活动统计")
public class CourseActivityStatVO {

    @ApiModelProperty(value = "课程活动id")
    private String cmid;

    @ApiModelProperty(value = "活动id")
    private String instance;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "访问人数")
    private Integer viewUsers;

    @ApiModelProperty(value = "访问量")
    private Integer viewNum;

    @ApiModelProperty(value = "活动完成情况")
    private Integer completionNum;
}
