package cn.bctools.custom.filter;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.entity.data.Course;
import cn.bctools.custom.service.CourseService;
import cn.bctools.custom.util.TeacherThreadLocal;
import cn.hutool.core.util.URLUtil;
import lombok.AllArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Order
@Component
@AllArgsConstructor
public class CourseFilter extends GenericFilterBean {

    private final CourseService courseService;

    public static final String HEADER_COURSE_ID = "courseId";

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String courseId = URLUtil.decode(request.getHeader(HEADER_COURSE_ID));
        if (ObjectNull.isNotNull(courseId)) {
            TeacherThreadLocal.setCourseId(courseId);
            Course course = courseService.getById(courseId);
            Optional.ofNullable(course).map(Course::getLeaderId).ifPresent(TeacherThreadLocal::setLeaderId);
        }
        filterChain.doFilter(servletRequest, servletResponse);
    }
}
