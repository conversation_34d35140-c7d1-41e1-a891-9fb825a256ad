package cn.bctools.custom.handler;

import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.auth.api.dto.SearchUserDto;
import cn.bctools.auth.api.enums.UserQueryType;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.annotation.ModuleField;
import cn.bctools.custom.component.CompletionComponent;
import cn.bctools.custom.component.CourseUserRoleComponent;
import cn.bctools.custom.component.DataModelComponent;
import cn.bctools.custom.constant.DataModelNameConstant;
import cn.bctools.custom.constant.FlowDesignNameConstant;
import cn.bctools.custom.constant.PermissionConstant;
import cn.bctools.custom.constant.RoleConstant;
import cn.bctools.custom.entity.data.*;
import cn.bctools.custom.entity.enums.CollaborativeCreationStatus;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.vo.ClassPreparationCompletionPageVo;
import cn.bctools.custom.entity.vo.ClassPreparationCompletionVo;
import cn.bctools.custom.entity.vo.OnlineClassCompletionVo;
import cn.bctools.custom.service.*;
import cn.bctools.custom.util.CommonUtil;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.custom.util.PermissionUtil;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2024-07-24
 */
@ModuleField("collaborative_creation")
@Slf4j
public class CollaborativeCreationHandler implements ModuleHandler {

    @Autowired
    CollaborativeCreationsService collaborativeCreationsService;

    @Autowired
    ModuleFilesService moduleFilesService;

    @Autowired
    CourseModuleOnlineDocumentsService courseModuleOnlineDocumentsService;

    @Autowired
    ModuleGroupsService moduleGroupsService;

    @Autowired
    ModulesService modulesService;

    @Autowired
    UserGroupsService userGroupsService;

    @Autowired
    UserSubjectsService userSubjectsService;

    @Autowired
    CourseModuleCompletionService completionService;

    @Autowired
    CompletionComponent completionComponent;

    @Autowired
    AuthUserServiceApi authUserServiceApi;

    @Autowired
    CourseUserRoleComponent roleComponent;

    @Autowired
    UsersService usersService;

    @Autowired
    DataModelComponent dataModelComponent;

    @Autowired
    CourseService courseService;

    @Override
    public Object getInfo(String courseModuleId, String instanceId) {
        return collaborativeCreationsService.get(courseModuleId);
    }

    @Override
    public void delete(String courseModuleId, String instanceId) {
        CollaborativeCreations collaborativeCreations = collaborativeCreationsService.checkEditPermission(instanceId);
        //删除资源表
        collaborativeCreationsService.removeById(instanceId);
        //删除文件表
        moduleFilesService.remove(new LambdaQueryWrapper<ModuleFiles>().eq(ModuleFiles::getCmId, courseModuleId));

        // TODO 删除文库文档，之后才能删除关联记录
//        courseModuleOnlineDocumentsService.remove(
//                new LambdaQueryWrapper<CourseModuleOnlineDocuments>()
//                        .eq(CourseModuleOnlineDocuments::getCmId, courseModuleId)
//        );

        //删除小组关联表
        moduleGroupsService.remove(new LambdaQueryWrapper<ModuleGroups>().eq(ModuleGroups::getCmId, courseModuleId));
        //删除课程模组表
        modulesService.removeById(courseModuleId);
        //删除关联模型数据
        String dataId = collaborativeCreations.getDataId();
        if(StrUtil.isEmpty(dataId)){
            return;
        }
        dataModelComponent.deleteData(DataModelNameConstant.XZRW,dataId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copy(String courseModuleId, String instanceId) {
        collaborativeCreationsService.checkEditPermission(instanceId);
        //复制协同创作表
        CollaborativeCreations collaborativeCreation = collaborativeCreationsService.getById(instanceId);

        // 获取当前最大编号
        String baseName = collaborativeCreation.getName();
        List<String> nameList = collaborativeCreationsService.list(new LambdaQueryWrapper<CollaborativeCreations>()
                        .select(CollaborativeCreations::getName)
                        .likeRight(CollaborativeCreations::getName, baseName + "("))
                .stream()
                .map(CollaborativeCreations::getName)
                .collect(Collectors.toList());

        String copyName = CommonUtil.getCopyName(baseName, nameList);

        // 复制后的协同创作活动为【新建文档】状态
        CollaborativeCreations copyCollaborativeCreation = new CollaborativeCreations();
        copyCollaborativeCreation.setCourseId(collaborativeCreation.getCourseId());
        copyCollaborativeCreation.setName(copyName);
        copyCollaborativeCreation.setDescription(collaborativeCreation.getDescription());
        copyCollaborativeCreation.setStartTime(collaborativeCreation.getStartTime());
        copyCollaborativeCreation.setEndTime(collaborativeCreation.getEndTime());
        copyCollaborativeCreation.setSummaryPublished(false);
        copyCollaborativeCreation.setStatus(CollaborativeCreationStatus.NEW);
        EntityFillUtil.fillEntityFields(copyCollaborativeCreation, EntityFillType.CREATE);
        EntityFillUtil.fillEntityFields(copyCollaborativeCreation, EntityFillType.UPDATE);
        collaborativeCreationsService.save(copyCollaborativeCreation);
        
        //复制模组表
        Modules module = modulesService.getModule(courseModuleId);
        Modules copyModule = BeanCopyUtil.copy(module, Modules.class);
        copyModule.setId(null);
        copyModule.setInstance(copyCollaborativeCreation.getId());
        EntityFillUtil.fillEntityFields(copyModule, EntityFillType.CREATE);
        EntityFillUtil.fillEntityFields(copyModule, EntityFillType.UPDATE);
        modulesService.save(copyModule);

        //复制小组关联表
        List<ModuleGroups> groupList = moduleGroupsService.list((new LambdaQueryWrapper<ModuleGroups>()
                .eq(ModuleGroups::getCmId, courseModuleId)
                .eq(ModuleGroups::getCourseId, module.getCourseId())));
        List<ModuleGroups> copyGroupList = groupList.stream().map(e -> {
            ModuleGroups copyGroup = BeanCopyUtil.copy(e, ModuleGroups.class);
            copyGroup.setId(null);
            copyGroup.setCmId(copyModule.getId());
            EntityFillUtil.fillEntityFields(copyGroup, EntityFillType.CREATE);
            EntityFillUtil.fillEntityFields(copyGroup, EntityFillType.UPDATE);
            return copyGroup;
        }).collect(Collectors.toList());
        moduleGroupsService.saveBatch(copyGroupList);

        try {
            // 同步数据到 MongoDB
            Map<String, Object> data = new HashMap<>();
            data.put(FlowDesignNameConstant.TASK_NAME, copyCollaborativeCreation.getName());
            data.put(FlowDesignNameConstant.BINd_FLOW, FlowDesignNameConstant.FLOW_DESIGN_ID);
            data.put(FlowDesignNameConstant.JVS_FlOW_TASK_STATE, CollaborativeCreationStatus.NEW.getName());
            data.put(FlowDesignNameConstant.JVS_FlOW_TASK_Progress, CollaborativeCreationStatus.NEW.getName());
            data.put(FlowDesignNameConstant.TASK_ID, 0);
            String dataId = dataModelComponent.saveData(DataModelNameConstant.XZRW, data);
            copyCollaborativeCreation.setDataId(dataId);
        } catch (Exception e) {
            throw new BusinessException("复制失败，请重试");
        }
        collaborativeCreationsService.updateById(copyCollaborativeCreation);
    }

    @Override
    public Boolean getCompletionStatus(String courseModuleId, String instanceId) {
        List<String> role = UserCurrentUtils.getCurrentUser().getRoleIds();
        if (PermissionUtil.checkCourseLeader() || role.contains(RoleConstant.PROVINCIAL_ADMINISTRATORS_ROLE)) {
            return null;
        }

        Boolean flag = moduleGroupsService.checkUserInModuleGroup(courseModuleId);
        if (!flag) {
            return null;
        }

        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, courseModuleId)
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));
        if (ObjectNull.isNull(completion)) {
            return false;
        }
        return completion.getCompletionStatus();
    }

    @Override
    public Page completionList(Page page, Object completionListQueryVo, String courseModuleId) {
        Page<ClassPreparationCompletionVo> completionPage = new Page<>(page.getCurrent(), page.getSize(), 0);

        Modules module = modulesService.getModule(courseModuleId);

        ClassPreparationCompletionPageVo queryVo = BeanCopyUtil.copy(completionListQueryVo, ClassPreparationCompletionPageVo.class);

        List<String> userIds = completionComponent.getUserIdsByCompletionListQueryVo(queryVo, courseModuleId);
        if (ObjectNull.isNull(userIds)) {
            return completionPage;
        }

        SearchUserDto searchUserDto = new SearchUserDto().setUserIds(userIds).setType(UserQueryType.and);
        if (ObjectNull.isNotNull(queryVo.getKeyword())) {
            searchUserDto.setRealName(queryVo.getKeyword());
        }
        List<UserDto> userDtos = authUserServiceApi.searchUserGetInfo(searchUserDto).getData();
        if (ObjectNull.isNull(userDtos)) {
            return completionPage;
        }

        Map<String, UserDto> userDtoMap = userDtos.stream()
                .collect(Collectors.toMap(UserDto::getId, Function.identity()));

        userIds = new ArrayList<>(userDtoMap.keySet());

        Page<Users> usersPage = usersService
                .page(page, new LambdaQueryWrapper<Users>()
                        .eq(Users::getCourseId, module.getCourseId())
                        .in(Users::getUserId, userIds));
        List<ClassPreparationCompletionVo> list = new ArrayList<>();

        usersPage.getRecords().stream().forEach(e -> {
            ClassPreparationCompletionVo classPreparationCompletionVo = new ClassPreparationCompletionVo();
            classPreparationCompletionVo.setId(e.getUserId());
            UserDto userDto = userDtoMap.get(e.getUserId());
            if (ObjectNull.isNotNull(userDto)) {
                classPreparationCompletionVo.setDeptName(userDto.getDeptName());
                classPreparationCompletionVo.setRealName(userDto.getRealName());
            }
            CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                    .eq(CourseModuleCompletion::getCmId, courseModuleId)
                    .eq(CourseModuleCompletion::getUserId, e.getUserId()));
            if (ObjectNull.isNotNull(completion)) {
                classPreparationCompletionVo.setLastViewTime(completion.getLastViewTime());
            }

            classPreparationCompletionVo.setPeriodAndSubjects(userSubjectsService.getPerAndSubs(e.getUserId(), e.getCourseId()));
            list.add(classPreparationCompletionVo);
        });

        completionPage.setTotal(usersPage.getTotal());
        completionPage.setRecords(list);

        return completionPage;
    }

    @Override
    public void exportCompletion(Page page, Object completionListQueryVo, String courseModuleId, HttpServletResponse response) {
        Page<OnlineClassCompletionVo> onlineClassCompletionVoPage = completionList(page, completionListQueryVo, courseModuleId);
        List<OnlineClassCompletionVo> list = onlineClassCompletionVoPage.getRecords();

        try {
            //在内存操作写到浏览器
            ExcelWriter writer = ExcelUtil.getWriter(true);

            //自定义别名
            writer.addHeaderAlias("realName", "姓名");
            writer.addHeaderAlias("periodAndSubjects", "学科学段");
            writer.addHeaderAlias("deptName", "所属单位");
            writer.addHeaderAlias("lastViewTime", "最后浏览时间");
            //只输出有别名的字段
            writer.setOnlyAlias(true);

            //设置所有列宽
            writer.setColumnWidth(-10, 15);
            //设置内容左对齐
            writer.getStyleSet().setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);

            //一次性写出list对象到Excel使用默认样式，强制输出标题
            writer.write(list, true);
            getExportWriter(response, writer, "协同创作完成情况");
        } catch (Exception ex) {
            log.error("协同创作完成情况导出异常", ex);
            throw new BusinessException("导出失败：" + ex.getMessage());
        }
    }

    @Override
    public void remarkCompletion(String courseModuleId, String instanceId) {
        Modules module = modulesService.getModule(courseModuleId);
        if (!roleComponent.checkStudent(module.getCourseId()) && !roleComponent.checkTeamLeader(module.getCourseId())) {
            throw new BusinessException("只有学员或小组长可标记已完成");
        }

        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, courseModuleId)
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));

        if (ObjectNull.isNull(completion)) {
            throw new BusinessException("暂未查看，不可标记已完成");
        }
        completion.setCompletionStatus(true);
        completionService.updateById(completion);
    }

    @Override
    public Boolean checkShow(String courseModuleId, String instance) {
        if (PermissionUtil.checkTrainingAdmin()) {
            return true;
        }

        CollaborativeCreations creation = collaborativeCreationsService.getById(instance);
        CollaborativeCreations collaborativeCreation = BeanCopyUtil.copy(creation, CollaborativeCreations.class);

        String userId = UserCurrentUtils.getCurrentUser().getId();

        Modules module = modulesService.getModule(courseModuleId);
        Course course = courseService.getById(module.getCourseId());

        // 工作室负责人可见
        if (course.getLeaderId().equals(userId)) {
            return true;
        }

        if (!module.getVisible()) {
            return false;
        }

        // 班主任可以看到所有未隐藏的活动
        if (roleComponent.checkHeadTeacher(module.getCourseId())) {
            return true;
        }

        // 审核人员能看到，即使不在小组里
        if (collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.CREATION_AUDIT)
                && collaborativeCreation.getCreationAuditorIds().contains(userId)) {
            return true;
        }
        if (collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.OUTLINE_AUDIT)
                && collaborativeCreation.getOutlineAuditorIds().contains(userId)) {
            return true;
        }

        Boolean flag = moduleGroupsService.checkUserInModuleGroup(module.getId());
        if (!flag) {
            return false;
        }

        // 成员只能看到编写内容、内容编排状态
        List<CollaborativeCreationStatus> statusList = Arrays.asList(
                CollaborativeCreationStatus.WRITE_CONTENT,
                CollaborativeCreationStatus.ARRANGE_CONTENT
        );
        return statusList.contains(collaborativeCreation.getStatus());
    }

    @Override
    public List<String> getPermissions(String courseModuleId, String instanceId) {
        if (PermissionUtil.checkTrainingAdmin()) {
            return PermissionConstant.DEFAULT_ALL_PERMISSIONS;
        }
        CollaborativeCreations collaborativeCreation = collaborativeCreationsService.getById(instanceId);
        if (!ObjectUtils.isEmpty(collaborativeCreation)) {
            String createById = collaborativeCreation.getCreateById();
            if (UserCurrentUtils.getUserId().equals(createById)) {
                return PermissionConstant.DEFAULT_ALL_PERMISSIONS;
            }
        }
        Modules modules = modulesService.getById(courseModuleId);
        String courseId = modules.getCourseId();
        //班主任
        if (roleComponent.checkHeadTeacher(courseId)) {
            List<String> defaultAllPermissions = new ArrayList<>();
            defaultAllPermissions.add(PermissionConstant.COMPLETION);
            return defaultAllPermissions;
        }
        //小组长
        if (roleComponent.checkTeamLeader(courseId)) {
            List<String> defaultAllPermissions = new ArrayList<>();
            defaultAllPermissions.add(PermissionConstant.COMPLETION);
            return defaultAllPermissions;
        }
        return new ArrayList<>();
    }
}
