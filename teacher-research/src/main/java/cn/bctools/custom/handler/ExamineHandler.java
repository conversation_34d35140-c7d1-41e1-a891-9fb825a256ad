package cn.bctools.custom.handler;

import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.auth.api.dto.SearchUserDto;
import cn.bctools.auth.api.enums.UserQueryType;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.annotation.ModuleField;
import cn.bctools.custom.component.CompletionComponent;
import cn.bctools.custom.component.CourseUserRoleComponent;
import cn.bctools.custom.constant.PermissionConstant;
import cn.bctools.custom.entity.data.*;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.enums.ModuleType;
import cn.bctools.custom.entity.vo.ExamineCompletionQueryVO;
import cn.bctools.custom.entity.vo.ExamineCompletionVO;
import cn.bctools.custom.entity.vo.ExamineUsersVO;
import cn.bctools.custom.entity.vo.ExamineVO;
import cn.bctools.custom.service.*;
import cn.bctools.custom.util.CommonUtil;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.custom.util.PermissionUtil;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


@ModuleField(value = "examine")
@Slf4j
public class ExamineHandler implements ModuleHandler{
    

    @Autowired
    ModuleGroupsService moduleGroupsService;

    @Autowired
    ModulesService modulesService;

    @Autowired
    ExamineService ExamineService;
    @Autowired
    ModuleFilesService moduleFilesService;
    @Autowired
    CourseModuleCompletionService completionService;
    @Autowired
    private ExamineUsersService ExamineUsersService;

    @Autowired
    UserGroupsService userGroupsService;

    @Autowired
    CompletionComponent completionComponent;

    @Autowired
    AuthUserServiceApi authUserServiceApi;
    @Autowired
    UserSubjectsService userSubjectsService;
    @Autowired
    ExamineGradeTaskService ExamineGradeTaskService;

    @Autowired
    CourseUserRoleComponent roleComponent;
    @Autowired
    ExamineGradeService ExamineGradeService;
    @Autowired
    UsersService usersService;
    String USER = "u";
    String EXAMINE_USER = "eu";


    @Override
    public Object getInfo(String courseModuleId, String instanceId) {
        ExamineVO ExamineVO = ExamineService.getExamineById(instanceId);
        ExamineVO.setModule(ModuleType.examine.name());

        //考核已分配评分专家
        List<ExamineGradeTask> ExamineGradeTaskList = ExamineGradeTaskService.list(new LambdaQueryWrapper<ExamineGradeTask>().eq(ExamineGradeTask::getExamineId,instanceId));
        if(!CollectionUtils.isEmpty(ExamineGradeTaskList)){
            List<String> ExpertIds = ExamineGradeTaskList.stream().map(ExamineGradeTask::getUserId).collect(Collectors.toList());
            ExamineVO.setExpertIds(ExpertIds);
        }

        Boolean canSubmit = true;
        ExamineUsersVO ExamineUserVO = ExamineUsersService.getExamineUserVO(instanceId,UserCurrentUtils.getUserId());
        if(!ObjectUtils.isEmpty(ExamineUserVO)){
            ExamineVO.setExamineUsersVO(ExamineUserVO);
            long gradeCount = ExamineGradeService.count(new LambdaQueryWrapper<ExamineGrade>().eq(ExamineGrade::getExamineId, instanceId)
                    .eq(ExamineGrade::getUserId, UserCurrentUtils.getUserId()).eq(ExamineGrade::getStatus, 1).last("limit 1"));
            if(gradeCount >0){
                canSubmit=false;
                ExamineVO.setAlreadyGrade(true);
            }
        }
        //是否存在评分
        long ExistGrade = ExamineGradeService.count(new LambdaQueryWrapper<ExamineGrade>().eq(ExamineGrade::getExamineId, instanceId)
                .eq(ExamineGrade::getStatus, 1).last("limit 1"));
        if(ExistGrade >0){
            ExamineVO.setExistGrade(true);
        }
        //是否可以修改提交考核
        //判断是否在 时间范围内 当前时间不在startTime 和endTime 之前的 不能提交考核
        if(!ObjectUtils.isEmpty(ExamineVO.getStartTime()) && !ObjectUtils.isEmpty(ExamineVO.getEndTime())){
            LocalDate startDate = ExamineVO.getStartTime().toLocalDate();
            LocalDate endDate = ExamineVO.getEndTime().toLocalDate();
            LocalDate today = LocalDate.now();
            if (today.isBefore(startDate) || today.isAfter(endDate)) {
                canSubmit=false;
            }
        }

        ExamineVO.setCanSubmit(canSubmit);
        return ExamineVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String courseModuleId, String instanceId) {
        if (!PermissionUtil.checkTrainingAdmin()) {
            throw new BusinessException("暂无权限");
        }
        //删除资源表
        ExamineService.removeById(instanceId);
        //删除文件表
        moduleFilesService.remove(new LambdaQueryWrapper<ModuleFiles>().eq(ModuleFiles::getCmId, courseModuleId));
        //删除小组关联表
        moduleGroupsService.remove(new LambdaQueryWrapper<ModuleGroups>().eq(ModuleGroups::getCmId, courseModuleId));
        //删除模组表
        modulesService.removeById(courseModuleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copy(String courseModuleId, String instanceId) {
        if (!PermissionUtil.checkTrainingAdmin()) {
            throw new BusinessException("暂无权限");
        }
        //复制资源表
        Examine examine = ExamineService.getById(instanceId);
        Examine copyExamine = BeanCopyUtil.copy(examine, Examine.class);
        copyExamine.setId(null);
        // 获取当前最大编号
        String baseName = examine.getName();
        List<String> nameList = ExamineService.list(new LambdaQueryWrapper<Examine>()
                        .select(Examine::getName)
                        .likeRight(Examine::getName, baseName + "("))
                .stream()
                .map(Examine::getName)
                .collect(Collectors.toList());

        String copyName = CommonUtil.getCopyName(baseName, nameList);
        copyExamine.setName(copyName);
        EntityFillUtil.fillEntityFields(copyExamine, EntityFillType.CREATE);
        EntityFillUtil.fillEntityFields(copyExamine, EntityFillType.UPDATE);
        ExamineService.save(copyExamine);

        //复制模组表
        Modules module = modulesService.getModule(courseModuleId);
        Modules copyModule = BeanCopyUtil.copy(module, Modules.class);
        copyModule.setId(null);
        copyModule.setInstance(copyExamine.getId());
        EntityFillUtil.fillEntityFields(copyModule, EntityFillType.CREATE);
        EntityFillUtil.fillEntityFields(copyModule, EntityFillType.UPDATE);
        modulesService.save(copyModule);

        //复制文件表
        List<ModuleFiles> fileList = moduleFilesService.list(new LambdaQueryWrapper<ModuleFiles>()
                .eq(ModuleFiles::getCmId, courseModuleId)
                .eq(ModuleFiles::getCourseId, module.getCourseId()));
        List<ModuleFiles> copyFileList = fileList.stream().map(e -> {
            ModuleFiles copyFile = BeanCopyUtil.copy(e, ModuleFiles.class);
            copyFile.setId(null);
            copyFile.setCmId(copyModule.getId());
            EntityFillUtil.fillEntityFields(copyFile, EntityFillType.CREATE);
            EntityFillUtil.fillEntityFields(copyFile, EntityFillType.UPDATE);
            return copyFile;
        }).collect(Collectors.toList());
        moduleFilesService.saveBatch(copyFileList);

        //复制小组关联表
        List<ModuleGroups> groupList = moduleGroupsService.list((new LambdaQueryWrapper<ModuleGroups>()
                .eq(ModuleGroups::getCmId, courseModuleId)
                .eq(ModuleGroups::getCourseId, module.getCourseId())));
        List<ModuleGroups> copyGroupList = groupList.stream().map(e -> {
            ModuleGroups copyGroup = BeanCopyUtil.copy(e, ModuleGroups.class);
            copyGroup.setId(null);
            copyGroup.setCmId(copyModule.getId());
            EntityFillUtil.fillEntityFields(copyGroup, EntityFillType.CREATE);
            EntityFillUtil.fillEntityFields(copyGroup, EntityFillType.UPDATE);
            return copyGroup;
        }).collect(Collectors.toList());
        moduleGroupsService.saveBatch(copyGroupList);
    }

    @Override
    public Page completionList(Page page, Object completionListQueryVo, String courseModuleId) {
        Modules module = modulesService.getModule(courseModuleId);
        if(roleComponent.checkExpert(module.getCourseId()) || roleComponent.checkStudent(module.getCourseId())){
            throw new BusinessException("暂无权限");
        }
        Page<ExamineCompletionVO> completionPage = new Page<>(page.getCurrent(), page.getSize(), 0);

        ExamineCompletionQueryVO queryVo = BeanCopyUtil.copy(completionListQueryVo, ExamineCompletionQueryVO.class);

        List<String> userIds = completionComponent.getUserIdsByCompletionListQueryVo(queryVo, courseModuleId);
        if (ObjectNull.isNull(userIds)) {
            return completionPage;
        }
        /*
         1、传输userIds和查询条件，远程调用获取符合条件用户ids
         2、completionService根据userIds进行分页查询
         3、根据userIds获取用户姓名、所属单位中文信息，转换成map
         4、根据map填充信息
        */
        SearchUserDto searchUserDto = new SearchUserDto().setUserIds(userIds).setType(UserQueryType.and);
        if (ObjectNull.isNotNull(queryVo.getKeyword())) {
            searchUserDto.setRealName(queryVo.getKeyword());
        }
        List<UserDto> userDtos = authUserServiceApi.searchUserGetInfo(searchUserDto).getData();
        if (ObjectNull.isNull(userDtos)) {
            return completionPage;
        }
        //应该是用UserDtos 的userIds??

        Map<String, UserDto> userDtoMap = userDtos.stream()
                .collect(Collectors.toMap(UserDto::getId, Function.identity()));

        userIds = new ArrayList<>(userDtoMap.keySet());

        QueryWrapper<Users> wrapper = new QueryWrapper<>();

        wrapper.eq( USER+ ".course_id", module.getCourseId());
        wrapper.in( USER+ ".user_id", userIds);


        wrapper.eq( USER+".del_flag", false);

        wrapper.orderByDesc(EXAMINE_USER+".create_time");

        IPage<Users> usersPage  = ExamineUsersService.completionList(page,wrapper,module.getInstance());
        List<ExamineCompletionVO> list = new ArrayList<>();

        usersPage.getRecords().forEach(e -> {
            ExamineCompletionVO examineCompletionVO = new ExamineCompletionVO();
            examineCompletionVO.setId(e.getUserId());
            UserDto userDto = userDtoMap.get(e.getUserId());
            if (ObjectNull.isNotNull(userDto)) {
                examineCompletionVO.setDeptName(userDto.getDeptName());
                examineCompletionVO.setRealName(userDto.getRealName());
                examineCompletionVO.setPhone(userDto.getPhone());
            }
            CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                    .eq(CourseModuleCompletion::getCmId, courseModuleId)
                    .eq(CourseModuleCompletion::getUserId, e.getUserId()));
            if (ObjectNull.isNotNull(completion)) {
                examineCompletionVO.setLastViewTime(completion.getLastViewTime());
            }

            ExamineUsers examineUsers = ExamineUsersService.getOne(new LambdaQueryWrapper<ExamineUsers>().
                    eq(ExamineUsers::getUserId, e.getUserId()
                    ).eq(ExamineUsers::getExamineId,module.getInstance()).last("limit 1"));
            if(!ObjectUtils.isEmpty(examineUsers)){
                if(examineUsers.getStatus() ==1){
                    examineCompletionVO.setCompletionStatus("已完成");
                    examineCompletionVO.setSubmitTime(examineUsers.getCreateTime());
                }

            }else {
                examineCompletionVO.setCompletionStatus("未完成");
            }
            ExamineUsersVO ExamineUserVO = ExamineUsersService.getExamineUserVO(examineUsers);
            if(!ObjectUtils.isEmpty(ExamineUserVO)){
                examineCompletionVO.setExamineUsersVO(ExamineUserVO);
            }

            examineCompletionVO.setPeriodAndSubjects(userSubjectsService.getPerAndSubs(e.getUserId(),e.getCourseId()));
            list.add(examineCompletionVO);
        });

        completionPage.setTotal(usersPage.getTotal());
        completionPage.setRecords(list);

        return completionPage;
    }

    @Override
    public void exportCompletion(Page page, Object completionListQueryVo, String courseModuleId, HttpServletResponse response) {
        Page<ExamineCompletionVO> ExamineCompletionVOPage = completionList(page, completionListQueryVo, courseModuleId);
        List<ExamineCompletionVO> list = ExamineCompletionVOPage.getRecords();

        try {
            //在内存操作写到浏览器
            ExcelWriter writer = ExcelUtil.getWriter(true);

            //自定义别名
            writer.addHeaderAlias("realName", "姓名");
            writer.addHeaderAlias("periodAndSubjects", "学科学段");
            writer.addHeaderAlias("deptName", "所属单位");
            writer.addHeaderAlias("completionStatus", "活动完成情况");
            writer.addHeaderAlias("submitTime", "提交时间");
            writer.addHeaderAlias("lastViewTime", "最后浏览时间");
            //只输出有别名的字段
            writer.setOnlyAlias(true);

            //设置所有列宽
            writer.setColumnWidth(-10, 15);
            //设置内容左对齐
            writer.getStyleSet().setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);

            //一次性写出list对象到Excel使用默认样式，强制输出标题
            writer.write(list, true);
            getExportWriter(response, writer, "考核完成情况");
        } catch (Exception Ex) {
            log.error("考核完成情况导出异常", Ex);
            throw new BusinessException("导出失败：" + Ex.getMessage());
        }
    }

    @Override
    public void getExportWriter(HttpServletResponse response, ExcelWriter ExcelWriter, String fileName) throws Exception {
        ModuleHandler.super.getExportWriter(response, ExcelWriter, fileName);
    }

    @Override
    public Boolean getCompletionStatus(String courseModuleId, String instanceId) {

        //完成情况：false未完成，true已完成
        Boolean flag = moduleGroupsService.checkUserInModuleGroup(courseModuleId);
        if (!flag) {
            return null;
        }
        Modules module = modulesService.getModule(courseModuleId);
        boolean trainOrHeadTeacher = PermissionUtil.checkTrainingAdmin() || roleComponent.checkHeadTeacher(module.getCourseId());
        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, courseModuleId)
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));
        if(ObjectUtils.isEmpty(completion)){
            completion = new CourseModuleCompletion();
            completion.setCourseId(module.getCourseId());
            completion.setCmId(courseModuleId);
            completion.setUserId(UserCurrentUtils.getUserId());
        }else {
            if( !trainOrHeadTeacher && BooleanUtil.isTrue(completion.getCompletionStatus())){
                return true;
            }
        }

        //如果是管理员、教研员、班主任 判断共同体成员是否都提交完考核
        if(trainOrHeadTeacher){
            ExamineCompletionQueryVO queryVo = new ExamineCompletionQueryVO();
            List<String> userIds = completionComponent.getUserIdsByCompletionListQueryVo(queryVo, courseModuleId);
            if(CollectionUtils.isEmpty(userIds)){
                completion.setCompletionStatus(false);
            }else {
                List<ExamineUsers> ExamineUsersList = ExamineUsersService.list(new LambdaQueryWrapper<ExamineUsers>().eq(ExamineUsers::getExamineId, instanceId)
                        .in(!CollectionUtils.isEmpty(userIds),ExamineUsers::getUserId,userIds));
                if(CollectionUtils.isEmpty(ExamineUsersList)){
                    completion.setCompletionStatus(false);
                }else {
                    long count = ExamineUsersList.stream().filter(ExamineUsers -> ExamineUsers.getStatus() ==1).count();
                    if(count >0 && count == userIds.size()){
                        completion.setCompletionStatus(true);
                    }else {
                        completion.setCompletionStatus(false);
                    }
                }
            }
            completionService.saveOrUpdate(completion);
        }

        return completion.getCompletionStatus();
    }

    @Override
    public void remarkCompletion(String courseModuleId, String instanceId) {

        if(PermissionUtil.checkTrainingAdmin()){
            //如果是管理员 判断学员、专家是否都提交完考核
            List<ExamineUsers> ExamineUsersList = ExamineUsersService.list(new LambdaQueryWrapper<ExamineUsers>().eq(ExamineUsers::getExamineId, instanceId));
            if(CollectionUtils.isEmpty(ExamineUsersList)){
                throw new BusinessException("未提交完考核,不可设置已完成");
            }else {
                long count = ExamineUsersList.stream().filter(ExamineUsers -> ExamineUsers.getStatus() == 0 || ExamineUsers.getStatus() == 2).count();
                if(count >0){
                    throw new BusinessException("未提交完考核,不可设置已完成");
                }
            }
        }
        Modules module = modulesService.getModule(courseModuleId);
        if(roleComponent.checkExpert(module.getCourseId()) || roleComponent.checkStudent(module.getCourseId())){
            //如果是专家、学员
            //判断是否提交完考核
            ExamineUsers examineUsers = ExamineUsersService.getOne(new LambdaQueryWrapper<ExamineUsers>().eq(ExamineUsers::getExamineId, instanceId)
                    .eq(ExamineUsers::getUserId, UserCurrentUtils.getUserId()).last("limit 1"));
            if(!ObjectUtils.isEmpty(examineUsers)){
                if(examineUsers.getStatus() != 1){
                    throw new BusinessException("未提交完考核,不可设置已完成");
                }
            }else {
                throw new BusinessException("未提交完考核,不可设置已完成");
            }

        }
        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, courseModuleId)
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));
        if (ObjectNull.isNull(completion)) {
            completion = new CourseModuleCompletion();
            completion.setCmId(courseModuleId);
            completion.setUserId(UserCurrentUtils.getUserId());
        }

        completion.setCompletionStatus(true);
        completionService.updateById(completion);
    }

    @Override
    public Boolean checkShow(String courseModuleId, String instance) {
        if (PermissionUtil.checkTrainingAdmin()) {
            return true;
        }
        Modules module = modulesService.getModule(courseModuleId);
        if (!module.getVisible()){
            return false;
        }

        if (roleComponent.checkHeadTeacher(module.getCourseId())) {
            return true;
        }

        Boolean flag = moduleGroupsService.checkUserInModuleGroup(module.getId());
        return flag;
    }

    @Override
    public List<String> getPermissions(String courseModuleId, String instanceId) {
        if (PermissionUtil.checkTrainingAdmin()) {
            List<String> defaultAllPermissions = new ArrayList<>(PermissionConstant.DEFAULT_ALL_PERMISSIONS);
            defaultAllPermissions.add(PermissionConstant.ASSIGN_RATINGS);
            defaultAllPermissions.add(PermissionConstant.RATING_PROGRESS);
            addScorePermission(instanceId,defaultAllPermissions);
            return defaultAllPermissions;
        }
        Modules modules = modulesService.getById(courseModuleId);

        String courseId = modules.getCourseId();
        //班主任
        if (roleComponent.checkHeadTeacher(courseId)) {
            List<String> defaultAllPermissions = new ArrayList<>();
            defaultAllPermissions.add(PermissionConstant.RATING_PROGRESS);
            defaultAllPermissions.add(PermissionConstant.COMPLETION);
            return defaultAllPermissions;
        }
        //小组长
        if (roleComponent.checkTeamLeader(courseId)) {
            List<String> defaultAllPermissions = new ArrayList<>();
            defaultAllPermissions.add(PermissionConstant.COMPLETION);
            return defaultAllPermissions;
        }
        //专家
        if(roleComponent.checkExpert(courseId)){
            List<String> defaultAllPermissions = new ArrayList<>();
            addScorePermission(instanceId, defaultAllPermissions);
            return defaultAllPermissions;
        }
        return new ArrayList<>();
    }

    private void addScorePermission(String instanceId, List<String> defaultAllPermissions) {
        ExamineGradeTask examineGradeTask = ExamineGradeTaskService.getOne(new LambdaQueryWrapper<ExamineGradeTask>().eq(ExamineGradeTask::getExamineId, instanceId)
                .eq(ExamineGradeTask::getUserId, UserCurrentUtils.getUserId()).last("limit 1"));
        if(!ObjectUtils.isEmpty(examineGradeTask)){
            defaultAllPermissions.add(PermissionConstant.SCORE);
        }
    }
}
