package cn.bctools.custom.handler;

import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.auth.api.dto.SearchUserDto;
import cn.bctools.auth.api.enums.UserQueryType;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.annotation.ModuleField;
import cn.bctools.custom.component.CompletionComponent;
import cn.bctools.custom.component.CourseUserRoleComponent;
import cn.bctools.custom.constant.PermissionConstant;
import cn.bctools.custom.constant.RoleConstant;
import cn.bctools.custom.entity.data.*;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.vo.OpenClassCompletionPageVo;
import cn.bctools.custom.entity.vo.OpenClassCompletionVo;
import cn.bctools.custom.service.*;
import cn.bctools.custom.util.CommonUtil;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.custom.util.PermissionUtil;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ModuleField(value = "open_class")
@Slf4j
public class OpenClassHandler implements ModuleHandler {

    @Autowired
    OpenClassesService openClassesService;

    @Autowired
    ModuleFilesService moduleFilesService;

    @Autowired
    ModuleGroupsService moduleGroupsService;

    @Autowired
    ModulesService modulesService;

    @Autowired
    UserGroupsService userGroupsService;

    @Autowired
    UserSubjectsService userSubjectsService;

    @Autowired
    CourseModuleCompletionService completionService;

    @Autowired
    CompletionComponent completionComponent;

    @Autowired
    AuthUserServiceApi authUserServiceApi;

    @Autowired
    CourseUserRoleComponent roleComponent;

    @Autowired
    UsersService usersService;

    @Override
    public Object getInfo(String courseModuleId, String instanceId) {
        return openClassesService.getOpenClass(courseModuleId);
    }

    @Override
    public void delete(String courseModuleId, String instanceId) {
        openClassesService.checkEditPermission();
        //删除资源表
        openClassesService.removeById(instanceId);
        //删除文件表
        moduleFilesService.remove(new LambdaQueryWrapper<ModuleFiles>().eq(ModuleFiles::getCmId, courseModuleId));
        //删除小组关联表
        moduleGroupsService.remove(new LambdaQueryWrapper<ModuleGroups>().eq(ModuleGroups::getCmId, courseModuleId));
        //删除课程模组表
        modulesService.removeById(courseModuleId);
    }

    @Override
    public Boolean getCompletionStatus(String courseModuleId, String instanceId) {
        List<String> role = UserCurrentUtils.getCurrentUser().getRoleIds();
        if (PermissionUtil.checkCourseLeader() || role.contains(RoleConstant.PROVINCIAL_ADMINISTRATORS_ROLE)) {
            return null;
        }

        Boolean flag = moduleGroupsService.checkUserInModuleGroup(courseModuleId);
        if (!flag) {
            return null;
        }

        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, courseModuleId)
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));
        if (ObjectNull.isNull(completion)) {
            return false;
        }
        return completion.getCompletionStatus();
    }

    @Override
    public void copy(String courseModuleId, String instanceId) {
        openClassesService.checkEditPermission();
        //复制公开课表
        OpenClasses openClass = openClassesService.getById(instanceId);
        OpenClasses copyOpenClass = BeanCopyUtil.copy(openClass, OpenClasses.class);
        copyOpenClass.setId(null);
        // 获取当前最大编号
        String baseName = openClass.getName();
        List<String> nameList = openClassesService.list(new LambdaQueryWrapper<OpenClasses>()
                        .select(OpenClasses::getName)
                        .likeRight(OpenClasses::getName, baseName + "("))
                .stream()
                .map(OpenClasses::getName)
                .collect(Collectors.toList());

        String copyName = CommonUtil.getCopyName(baseName, nameList);
        copyOpenClass.setName(copyName);
        EntityFillUtil.fillEntityFields(copyOpenClass, EntityFillType.CREATE);
        EntityFillUtil.fillEntityFields(copyOpenClass, EntityFillType.UPDATE);
        openClassesService.save(copyOpenClass);

        //复制模组表
        Modules module = modulesService.getModule(courseModuleId);
        Modules copyModule = BeanCopyUtil.copy(module, Modules.class);
        copyModule.setId(null);
        copyModule.setInstance(copyOpenClass.getId());
        EntityFillUtil.fillEntityFields(copyModule, EntityFillType.CREATE);
        EntityFillUtil.fillEntityFields(copyModule, EntityFillType.UPDATE);
        modulesService.save(copyModule);

        //复制文件表
        List<ModuleFiles> fileList = moduleFilesService.list(new LambdaQueryWrapper<ModuleFiles>()
                .eq(ModuleFiles::getCmId, courseModuleId)
                .eq(ModuleFiles::getCourseId, module.getCourseId()));
        List<ModuleFiles> copyFileList = fileList.stream().map(e -> {
            ModuleFiles copyFile = BeanCopyUtil.copy(e, ModuleFiles.class);
            copyFile.setId(null);
            copyFile.setCmId(copyModule.getId());
            EntityFillUtil.fillEntityFields(copyFile, EntityFillType.CREATE);
            EntityFillUtil.fillEntityFields(copyFile, EntityFillType.UPDATE);
            return copyFile;
        }).collect(Collectors.toList());
        moduleFilesService.saveBatch(copyFileList);

        //复制小组关联表
        List<ModuleGroups> groupList = moduleGroupsService.list((new LambdaQueryWrapper<ModuleGroups>()
                .eq(ModuleGroups::getCmId, courseModuleId)
                .eq(ModuleGroups::getCourseId, module.getCourseId())));
        List<ModuleGroups> copyGroupList = groupList.stream().map(e -> {
            ModuleGroups copyGroup = BeanCopyUtil.copy(e, ModuleGroups.class);
            copyGroup.setId(null);
            copyGroup.setCmId(copyModule.getId());
            EntityFillUtil.fillEntityFields(copyGroup, EntityFillType.CREATE);
            EntityFillUtil.fillEntityFields(copyGroup, EntityFillType.UPDATE);
            return copyGroup;
        }).collect(Collectors.toList());
        moduleGroupsService.saveBatch(copyGroupList);
    }

    @Override
    public Page completionList(Page page, Object completionListQueryVo, String courseModuleId) {
        Page<OpenClassCompletionVo> completionPage = new Page<>(page.getCurrent(), page.getSize(), 0);

        Modules module = modulesService.getModule(courseModuleId);
        OpenClassCompletionPageVo queryVo = BeanCopyUtil.copy(completionListQueryVo, OpenClassCompletionPageVo.class);

        List<String> userIds = completionComponent.getUserIdsByCompletionListQueryVo(queryVo, courseModuleId);
        if (ObjectNull.isNull(userIds)) {
            return completionPage;
        }
        /*
         1、传输userIds和查询条件，远程调用获取符合条件用户ids
         2、completionService根据userIds进行分页查询
         3、根据userIds获取用户姓名、所属单位中文信息，转换成map
         4、根据map填充信息
        */
        SearchUserDto searchUserDto = new SearchUserDto().setUserIds(userIds).setType(UserQueryType.and);
        if (ObjectNull.isNotNull(queryVo.getKeyword())) {
            searchUserDto.setRealName(queryVo.getKeyword());
        }
        List<UserDto> userDtos = authUserServiceApi.searchUserGetInfo(searchUserDto).getData();
        if (ObjectNull.isNull(userDtos)) {
            return completionPage;
        }

        Map<String, UserDto> userDtoMap = userDtos.stream()
                .collect(Collectors.toMap(UserDto::getId, Function.identity()));

        userIds = new ArrayList<>(userDtoMap.keySet());

        Page<Users> usersPage = usersService
                .page(page, new LambdaQueryWrapper<Users>()
                        .eq(Users::getCourseId, module.getCourseId())
                        .in(Users::getUserId, userIds));
        List<OpenClassCompletionVo> list = new ArrayList<>();

        usersPage.getRecords().stream().forEach(e -> {
            OpenClassCompletionVo openClassCompletionVo = new OpenClassCompletionVo();
            openClassCompletionVo.setId(e.getUserId());
            UserDto userDto = userDtoMap.get(e.getUserId());
            if (ObjectNull.isNotNull(userDto)) {
                openClassCompletionVo.setDeptName(userDto.getDeptName());
                openClassCompletionVo.setRealName(userDto.getRealName());
            }
            CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                    .eq(CourseModuleCompletion::getCmId, courseModuleId)
                    .eq(CourseModuleCompletion::getUserId, e.getUserId()));
            if (ObjectNull.isNotNull(completion)) {
                openClassCompletionVo.setLastViewTime(completion.getLastViewTime());
            }

            openClassCompletionVo.setPeriodAndSubjects(userSubjectsService.getPerAndSubs(e.getUserId(),e.getCourseId()));
            list.add(openClassCompletionVo);
        });

        completionPage.setTotal(usersPage.getTotal());
        completionPage.setRecords(list);

        return completionPage;
    }

    @Override
    public void exportCompletion(Page page, Object completionListQueryVo, String courseModuleId, HttpServletResponse response) {
        Page<OpenClassCompletionVo> openClassCompletionVoPage = completionList(page, completionListQueryVo, courseModuleId);
        List<OpenClassCompletionVo> list = openClassCompletionVoPage.getRecords();

        try {
            //在内存操作写到浏览器
            ExcelWriter writer = ExcelUtil.getWriter(true);

            //自定义别名
            writer.addHeaderAlias("realName", "姓名");
            writer.addHeaderAlias("periodAndSubjects", "学科学段");
            writer.addHeaderAlias("deptName", "所属单位");
            writer.addHeaderAlias("lastViewTime", "最后浏览时间");
            //只输出有别名的字段
            writer.setOnlyAlias(true);

            //设置所有列宽
            writer.setColumnWidth(-10, 15);
            //设置内容左对齐
            writer.getStyleSet().setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);

            //一次性写出list对象到Excel使用默认样式，强制输出标题
            writer.write(list, true);
            getExportWriter(response, writer, "公开课完成情况");
        } catch (Exception ex) {
            log.error("公开课完成情况导出异常", ex);
            throw new BusinessException("导出失败：" + ex.getMessage());
        }
    }

    @Override
    public void remarkCompletion(String courseModuleId, String instanceId) {
        Modules module = modulesService.getModule(courseModuleId);
        if (!roleComponent.checkStudent(module.getCourseId()) && !roleComponent.checkTeamLeader(module.getCourseId())) {
            throw new BusinessException("只有学员或小组长可标记已完成");
        }

        OpenClasses openClasses = openClassesService.getById(instanceId);
        if(!ObjectUtils.isEmpty(openClasses.getStartTime()) && !ObjectUtils.isEmpty(openClasses.getEndTime())){
            if(LocalDateTimeUtil.now().isBefore(openClasses.getStartTime())||LocalDateTimeUtil.now().isAfter(openClasses.getEndTime())){
                throw new BusinessException("不在活动时间范围内，不可标记已完成");
            }
        }
        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, courseModuleId)
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));

        if (ObjectNull.isNull(completion)) {
            throw new BusinessException("暂未查看，不可标记已完成");
        }

        completion.setCompletionStatus(true);
        completionService.updateById(completion);
    }

    @Override
    public Boolean checkShow(String courseModuleId, String instance) {
        if (PermissionUtil.checkTrainingAdmin()) {
            return true;
        }
        Modules module = modulesService.getModule(courseModuleId);
        if (!module.getVisible()) {
            return false;
        }

        if (roleComponent.checkHeadTeacher(module.getCourseId())) {
            return true;
        }

        Boolean flag = moduleGroupsService.checkUserInModuleGroup(module.getId());
        return flag;
    }

    @Override
    public List<String> getPermissions(String courseModuleId, String instanceId) {
        if (PermissionUtil.checkTrainingAdmin()) {
            return PermissionConstant.DEFAULT_ALL_PERMISSIONS;
        }
        Modules module = modulesService.getModule(courseModuleId);

        if (roleComponent.checkHeadTeacher(module.getCourseId())) {
            return Collections.singletonList(PermissionConstant.COMPLETION);
        }

        if (roleComponent.checkTeamLeader(module.getCourseId())) {
            Boolean flag = moduleGroupsService.checkUserInModuleGroup(courseModuleId);
            if (flag) {
                return Collections.singletonList(PermissionConstant.COMPLETION);
            }
        }

        return new ArrayList<>();
    }
}
