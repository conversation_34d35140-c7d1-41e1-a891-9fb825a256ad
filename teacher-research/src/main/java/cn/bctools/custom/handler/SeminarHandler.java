package cn.bctools.custom.handler;

import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.auth.api.dto.SearchUserDto;
import cn.bctools.auth.api.enums.UserQueryType;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.annotation.ModuleField;
import cn.bctools.custom.component.CompletionComponent;
import cn.bctools.custom.component.CourseUserRoleComponent;
import cn.bctools.custom.constant.PermissionConstant;
import cn.bctools.custom.constant.RoleConstant;
import cn.bctools.custom.entity.data.*;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.vo.OnlineClassCompletionVo;
import cn.bctools.custom.entity.vo.SeminarCompletionPageVo;
import cn.bctools.custom.entity.vo.SeminarCompletionVo;
import cn.bctools.custom.service.*;
import cn.bctools.custom.util.CommonUtil;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.custom.util.PermissionUtil;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


@ModuleField("seminar")
@Slf4j
public class SeminarHandler implements ModuleHandler {

    @Autowired
    SeminarService seminarService;
    @Autowired
    SeminarUsersReplyService seminarUsersReplyService;

    @Autowired
    ModuleFilesService moduleFilesService;

    @Autowired
    ModuleGroupsService moduleGroupsService;

    @Autowired
    ModulesService modulesService;

    @Autowired
    UserGroupsService userGroupsService;

    @Autowired
    UserSubjectsService userSubjectsService;

    @Autowired
    CourseModuleCompletionService completionService;

    @Autowired
    CompletionComponent completionComponent;

    @Autowired
    AuthUserServiceApi authUserServiceApi;

    @Autowired
    CourseUserRoleComponent roleComponent;

    @Autowired
    UsersService usersService;

    @Override
    public Object getInfo(String courseModuleId, String instanceId) {
        return seminarService.getSeminar(courseModuleId);
    }

    @Override
    public void delete(String courseModuleId, String instanceId) {
        seminarService.checkEditPermission(instanceId);
        //删除资源表
        seminarService.removeById(instanceId);
        //删除文件表
        moduleFilesService.remove(new LambdaQueryWrapper<ModuleFiles>().eq(ModuleFiles::getCmId, courseModuleId));
        //删除小组关联表
        moduleGroupsService.remove(new LambdaQueryWrapper<ModuleGroups>().eq(ModuleGroups::getCmId, courseModuleId));
        //删除课程模组表
        modulesService.removeById(courseModuleId);
    }

    @Override
    public void copy(String courseModuleId, String instanceId) {
        seminarService.checkEditPermission(instanceId);
        //复制线上听课表
        Seminar seminar = seminarService.getById(instanceId);
        Seminar copySeminar = BeanCopyUtil.copy(seminar, Seminar.class);
        copySeminar.setId(null);
        // 获取当前最大编号
        String baseName = seminar.getName();
        List<String> nameList = seminarService.list(new LambdaQueryWrapper<Seminar>()
                        .select(Seminar::getName)
                        .likeRight(Seminar::getName, baseName + "("))
                .stream()
                .map(Seminar::getName)
                .collect(Collectors.toList());

        String copyName = CommonUtil.getCopyName(baseName, nameList);
        copySeminar.setName(copyName);
        EntityFillUtil.fillEntityFields(copySeminar, EntityFillType.CREATE);
        EntityFillUtil.fillEntityFields(copySeminar, EntityFillType.UPDATE);
        seminarService.save(copySeminar);


        //复制模组表
        Modules module = modulesService.getModule(courseModuleId);
        Modules copyModule = BeanCopyUtil.copy(module, Modules.class);
        copyModule.setId(null);
        copyModule.setInstance(copySeminar.getId());
        EntityFillUtil.fillEntityFields(copyModule, EntityFillType.CREATE);
        EntityFillUtil.fillEntityFields(copyModule, EntityFillType.UPDATE);
        modulesService.save(copyModule);

        //复制文件表
        List<ModuleFiles> fileList = moduleFilesService.list(new LambdaQueryWrapper<ModuleFiles>()
                .eq(ModuleFiles::getCmId, courseModuleId)
                .eq(ModuleFiles::getCourseId, module.getCourseId()));
        List<ModuleFiles> copyFileList = fileList.stream().map(e -> {
            ModuleFiles copyFile = BeanCopyUtil.copy(e, ModuleFiles.class);
            copyFile.setId(null);
            copyFile.setCmId(copyModule.getId());
            EntityFillUtil.fillEntityFields(copyFile, EntityFillType.CREATE);
            EntityFillUtil.fillEntityFields(copyFile, EntityFillType.UPDATE);
            return copyFile;
        }).collect(Collectors.toList());
        moduleFilesService.saveBatch(copyFileList);

        //复制小组关联表
        List<ModuleGroups> groupList = moduleGroupsService.list((new LambdaQueryWrapper<ModuleGroups>()
                .eq(ModuleGroups::getCmId, courseModuleId)
                .eq(ModuleGroups::getCourseId, module.getCourseId())));
        List<ModuleGroups> copyGroupList = groupList.stream().map(e -> {
            ModuleGroups copyGroup = BeanCopyUtil.copy(e, ModuleGroups.class);
            copyGroup.setId(null);
            copyGroup.setCmId(copyModule.getId());
            EntityFillUtil.fillEntityFields(copyGroup, EntityFillType.CREATE);
            EntityFillUtil.fillEntityFields(copyGroup, EntityFillType.UPDATE);
            return copyGroup;
        }).collect(Collectors.toList());
        moduleGroupsService.saveBatch(copyGroupList);
    }

    @Override
    public Boolean getCompletionStatus(String courseModuleId, String instanceId) {
        List<String> role = UserCurrentUtils.getCurrentUser().getRoleIds();
        if (PermissionUtil.checkCourseLeader() || role.contains(RoleConstant.PROVINCIAL_ADMINISTRATORS_ROLE)) {
            return null;
        }

        Boolean flag = moduleGroupsService.checkUserInModuleGroup(courseModuleId);
        if (!flag) {
            return null;
        }

        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, courseModuleId)
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));
        if (ObjectNull.isNull(completion)) {
            return false;
        }
        return completion.getCompletionStatus();
    }

    @Override
    public Page completionList(Page page, Object completionListQueryVo, String courseModuleId) {
        Page<SeminarCompletionVo> completionPage = new Page<>(page.getCurrent(), page.getSize(), 0);

        Modules module = modulesService.getModule(courseModuleId);

        SeminarCompletionPageVo queryVo = BeanCopyUtil.copy(completionListQueryVo, SeminarCompletionPageVo.class);

        List<String> userIds = completionComponent.getUserIdsByCompletionListQueryVo(queryVo, courseModuleId);
        if (ObjectNull.isNull(userIds)) {
            return completionPage;
        }

        SearchUserDto searchUserDto = new SearchUserDto().setUserIds(userIds).setType(UserQueryType.and);
        if (ObjectNull.isNotNull(queryVo.getKeyword())) {
            searchUserDto.setRealName(queryVo.getKeyword());
        }
        List<UserDto> userDtos = authUserServiceApi.searchUserGetInfo(searchUserDto).getData();
        if (ObjectNull.isNull(userDtos)) {
            return completionPage;
        }

        Map<String, UserDto> userDtoMap = userDtos.stream()
                .collect(Collectors.toMap(UserDto::getId, Function.identity()));

        userIds = new ArrayList<>(userDtoMap.keySet());

        Page<Users> usersPage = usersService
                .page(page, new LambdaQueryWrapper<Users>()
                        .eq(Users::getCourseId, module.getCourseId())
                        .in(Users::getUserId, userIds));
        List<SeminarCompletionVo> list = new ArrayList<>();

        usersPage.getRecords().stream().forEach(e -> {
            SeminarCompletionVo seminarCompletionVo = new SeminarCompletionVo();
            seminarCompletionVo.setId(e.getUserId());
            UserDto userDto = userDtoMap.get(e.getUserId());
            if (ObjectNull.isNotNull(userDto)) {
                seminarCompletionVo.setDeptName(userDto.getDeptName());
                seminarCompletionVo.setRealName(userDto.getRealName());
            }
            CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                    .eq(CourseModuleCompletion::getCmId, courseModuleId)
                    .eq(CourseModuleCompletion::getUserId, e.getUserId()));
            if (ObjectNull.isNotNull(completion)) {
                seminarCompletionVo.setLastViewTime(completion.getLastViewTime());
            }

            seminarCompletionVo.setPeriodAndSubjects(userSubjectsService.getPerAndSubs(e.getUserId(), e.getCourseId()));
            list.add(seminarCompletionVo);
        });

        completionPage.setTotal(usersPage.getTotal());
        completionPage.setRecords(list);

        return completionPage;
    }

    @Override
    public void exportCompletion(Page page, Object completionListQueryVo, String courseModuleId, HttpServletResponse response) {
        Page<OnlineClassCompletionVo> onlineClassCompletionVoPage = completionList(page, completionListQueryVo, courseModuleId);
        List<OnlineClassCompletionVo> list = onlineClassCompletionVoPage.getRecords();

        try {
            //在内存操作写到浏览器
            ExcelWriter writer = ExcelUtil.getWriter(true);

            //自定义别名
            writer.addHeaderAlias("realName", "姓名");
            writer.addHeaderAlias("periodAndSubjects", "学科学段");
            writer.addHeaderAlias("deptName", "所属单位");
            writer.addHeaderAlias("lastViewTime", "最后浏览时间");
            //只输出有别名的字段
            writer.setOnlyAlias(true);

            //设置所有列宽
            writer.setColumnWidth(-10, 15);
            //设置内容左对齐
            writer.getStyleSet().setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);

            //一次性写出list对象到Excel使用默认样式，强制输出标题
            writer.write(list, true);
            getExportWriter(response, writer, "话题研讨完成情况");
        } catch (Exception ex) {
            log.error("话题研讨完成情况导出异常", ex);
            throw new BusinessException("导出失败：" + ex.getMessage());
        }
    }

    @Override
    public void remarkCompletion(String courseModuleId, String instanceId) {

        Seminar seminar = seminarService.getById(instanceId);
        if(LocalDateTimeUtil.now().isBefore(seminar.getStartTime())||LocalDateTimeUtil.now().isAfter(seminar.getEndTime())){
            throw new BusinessException("不在活动时间范围内，不可标记已完成");
        }
        long count = seminarUsersReplyService.count(new LambdaQueryWrapper<SeminarUsersReply>()
                .eq(SeminarUsersReply::getSeminarId, instanceId).
                eq(SeminarUsersReply::getUserId, UserCurrentUtils.getUserId()).last("limit 1"));
        if(count ==0){
            throw new BusinessException("暂未评论，不可标记已完成");
        }
        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, courseModuleId)
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));

        if (ObjectNull.isNull(completion)) {
            throw new BusinessException("暂未查看，不可标记已完成");
        }
        completion.setCompletionStatus(true);
        completionService.updateById(completion);
    }

    @Override
    public Boolean checkShow(String courseModuleId, String instance) {
        if (PermissionUtil.checkTrainingAdmin()) {
            return true;
        }

        Seminar seminar = seminarService.getById(instance);
        if(!ObjectUtils.isEmpty(seminar)){
            String createById = seminar.getCreateById();
            if(UserCurrentUtils.getUserId().equals(createById)){
                return true;
            }
        }
        Modules module = modulesService.getModule(courseModuleId);
        if (!module.getVisible()) {
            return false;
        }

        if (roleComponent.checkHeadTeacher(module.getCourseId())) {
            return true;
        }

        Boolean flag = moduleGroupsService.checkUserInModuleGroup(module.getId());
        return flag;
    }

    @Override
    public List<String> getPermissions(String courseModuleId, String instanceId) {
        if (PermissionUtil.checkTrainingAdmin()) {
            return PermissionConstant.DEFAULT_ALL_PERMISSIONS;
        }
        Seminar seminar = seminarService.getById(instanceId);
        if(!ObjectUtils.isEmpty(seminar)){
            String createById = seminar.getCreateById();
            if(UserCurrentUtils.getUserId().equals(createById)){
                return PermissionConstant.DEFAULT_ALL_PERMISSIONS;
            }
        }
        Modules modules = modulesService.getById(courseModuleId);
        String courseId = modules.getCourseId();
        //班主任
        if (roleComponent.checkHeadTeacher(courseId)) {
            List<String> defaultAllPermissions = new ArrayList<>();
            defaultAllPermissions.add(PermissionConstant.COMPLETION);
            return defaultAllPermissions;
        }
        //小组长
        if (roleComponent.checkTeamLeader(courseId)) {
            List<String> defaultAllPermissions = new ArrayList<>();
            defaultAllPermissions.add(PermissionConstant.COMPLETION);
            return defaultAllPermissions;
        }
        return new ArrayList<>();
    }
}
