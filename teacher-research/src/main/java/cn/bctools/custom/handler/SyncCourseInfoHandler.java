package cn.bctools.custom.handler;

import cn.bctools.auth.api.api.AuthRoleServiceApi;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.component.DataModelComponent;
import cn.bctools.custom.constant.DataModelNameConstant;
import cn.bctools.custom.constant.RoleConstant;
import cn.bctools.custom.entity.data.*;
import cn.bctools.custom.entity.vo.SyncCourseInfoVo;
import cn.bctools.custom.entity.vo.SyncIndicatorsVO;
import cn.bctools.custom.entity.vo.SyncUserInfoVO;
import cn.bctools.custom.service.*;
import cn.bctools.design.use.api.dto.DataModelSearchDto;
import cn.bctools.design.use.api.enums.DataModelQueryType;
import cn.bctools.redis.utils.RedisUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.bouncycastle.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@Component
public class SyncCourseInfoHandler {

    @Autowired
    CourseService courseService;

    @Autowired
    DataModelComponent dataModelComponent;

    @Autowired
    PeriodSubjectsService periodSubjectsService;

    @Autowired
    ModuleFilesService moduleFilesService;

    @Autowired
    UsersService usersService;

    @Autowired
    UserSubjectsService userSubjectsService;

    @Autowired
    UserRolesService userRolesService;

    @Autowired
    UserGroupsService userGroupsService;

    @Autowired
    ActivityAgendasService activityAgendasService;

    @Autowired
    IndicatorsService indicatorsService;

    @Autowired
    IndicatorsOptionsService indicatorsOptionsService;

    @Autowired
    AuthRoleServiceApi authRoleServiceApi;

    @Autowired
    CronsService cronsService;
    @Autowired
    SectionsService sectionsService;

    @Autowired
    MongoTemplate mongoTemplate;

    @Autowired
    RedisUtils redisUtils;

    private static final String COURSE_UPDATE_TIME_KEY = "jvs:teacher-research:course:updateTime";


    @XxlJob("sync-course-info")
    @Transactional(rollbackFor = Exception.class)
    public void syncCourseInfo() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时任务【同步研究工作室信息】开始执行，入参为:{}", param);
        JSONObject json = compatibilityJsonCheck(param);
        DataModelSearchDto dataModelSearchDto = new DataModelSearchDto();
        dataModelSearchDto.setId(DataModelNameConstant.GTTGL);
        dataModelSearchDto.setSize(0);
        dataModelSearchDto.setCurrent(0);
        Boolean all = json.getBoolean("all");
        LocalDateTime now = LocalDateTime.now().minusSeconds(5);
        if (!all) {
            LocalDateTime latestUpdateTime = redisUtils.getTimeValue(COURSE_UPDATE_TIME_KEY);
            if (latestUpdateTime == null) {
                latestUpdateTime = LocalDateTime.now().minusYears(50);
            }
//            LocalDateTime latestUpdateTime = courseService.getLatestUpdateTime();
            String lastUpdate = LocalDateTimeUtil.format(latestUpdateTime, "yyyy-MM-dd HH:mm:ss");
            if (ObjectNull.isNotNull(lastUpdate)) {
                DataModelSearchDto.SearchGroup searchGroup = buildUpdateTimeGroup(lastUpdate);
                dataModelSearchDto.setGroup(Collections.singletonList(searchGroup));
            }
        }

        List<Map<String, Object>> mapList = dataModelComponent.searchWithEcho(dataModelSearchDto);
        List<SyncCourseInfoVo> list = BeanCopyUtil.copys(mapList, SyncCourseInfoVo.class);
        List<Course> courseList = new ArrayList<>();
        List<PeriodSubjects> perAndSubList = new ArrayList<>();
        List<String> couseIdList = new ArrayList<>();
        List<String> deleteCourseIds = new ArrayList<>();
        List<ActivityAgendas> activityAgendasList = new ArrayList<>();
        List<Sections> sectionsList = new ArrayList<>();
        list.stream().filter(e -> "已通过".equals(e.getJvsFlowTaskState()) || "管理员创建".equals(e.getJvsFlowTaskState()))
                .forEach(e -> {
                    if (e.getDelFlag()) {
                        deleteCourseIds.add(e.getId());
                    } else {
                        Course course = new Course();
                        couseIdList.add(e.getId());
                        course.setId(e.getId());
                        course.setLeaderId(e.getLeader());
                        if(StrUtil.isNotEmpty(e.getLeader1())){
                            course.setLeader(e.getLeader1());
                        }

                        Optional.ofNullable(e.getActivityTime()).ifPresent(time->{
                            if (time.size() == 2){
                                course.setActivityStartTime(time.get(0));
                                course.setActivityEndTime(time.get(1));
                            }
                        });
                        course.setEnrollStartTime(e.getEnrollStime());
                        course.setEnrollEndTime(e.getEnrollEtime());
                        course.setName(e.getName());
                        course.setTheme(e.getDesc());
                        course.setYear(e.getYear());
                        course.setCreateTime(e.getCreateTime());
                        course.setUpdateTime(e.getUpdateTime());
                        course.setCreateBy(e.getCreateBy());
                        course.setCreateById(e.getCreateById());
                        course.setUpdateBy(e.getUpdateBy());
                        course.setUpdateById(e.getUpdateById());
                        course.setCoverImg(e.getCoverImg());
                        course.setAttachments(e.getAttachments());
                        courseList.add(course);
                        if (ObjectNull.isNotNull(e.getPeriodSubject()) && Boolean.FALSE.equals(e.getDelFlag())) {
                            List<String> periodSubjects = e.getPeriodSubject();
                            String[] splitName = new String[0];
                            if(StrUtil.isNotEmpty(e.getPeriodSubject1())){
                                splitName = Strings.split(e.getPeriodSubject1(), ',');
                            }

                            for (int i = 0; i < periodSubjects.size(); i++) {
                                String pas = periodSubjects.get(i);

                                PeriodSubjects userSubjects = new PeriodSubjects();
                                userSubjects.setPerAndSubId(pas);
                                userSubjects.setCourseId(e.getId());
                                String[] split = Strings.split(pas, '-');
                                if (split.length == 2) {
                                    userSubjects.setPeriodId(split[0]);
                                    userSubjects.setSubjectId(split[1]);
                                }

                                if (splitName.length == periodSubjects.size()) {
                                    userSubjects.setPerAndSub(splitName[i]);
                                }

                                perAndSubList.add(userSubjects);
                            }
                        }

                        ActivityAgendas activityAgendas = activityAgendasService.getOne(new LambdaQueryWrapper<ActivityAgendas>()
                                .eq(ActivityAgendas::getCourseId, e.getId()).last("limit 1"));
                        if (ObjectNull.isNull(activityAgendas)) {
                            ActivityAgendas activityAgenda = new ActivityAgendas();
                            activityAgenda.setCourseId(e.getId());
                            activityAgenda.setCreateTime(e.getCreateTime());
                            activityAgenda.setCreateBy(e.getCreateBy());
                            activityAgenda.setCreateById(e.getCreateById());
                            activityAgendasList.add(activityAgenda);
                        }
                        Sections sections = sectionsService.getOne(new LambdaQueryWrapper<Sections>()
                                .eq(Sections::getCourseId, e.getId()).last("limit 1"));
                        if(ObjectUtil.isNull(sections)){
                            sections = new Sections();
                            sections.setName("环节1");
                            sections.setCourseId(e.getId());
                            sections.setCreateTime(e.getCreateTime());
                            sections.setCreateBy(e.getCreateBy());
                            sections.setCreateById(e.getCreateById());
                            sectionsList.add(sections);
                        }
                    }

                });

        if (ObjectNull.isNotNull(deleteCourseIds)) {
            //TODO 删除研究工作室相关信息
        }

        if (ObjectNull.isNotNull(couseIdList)) {
            periodSubjectsService.getBaseMapper().delete(new LambdaQueryWrapper<PeriodSubjects>().in(PeriodSubjects::getCourseId, couseIdList));
//            moduleFilesService.getBaseMapper().delete(new LambdaQueryWrapper<ModuleFiles>()
//                    .in(ModuleFiles::getCourseId, couseIdList)
//                    .in(ModuleFiles::getRemarks,Arrays.asList("cover","attachments"))
//                    .eq(ModuleFiles::getCmId, "0"));
        }

        XxlJobHelper.log("【新增活动议程】开始执行，新增新增活动议程条数为:{}", activityAgendasList.size());
        for (int i = 0; i < activityAgendasList.size(); i += 1000) {
            List<ActivityAgendas> activityAgendasBatchList = activityAgendasList.subList(i, Math.min(i + 1000, activityAgendasList.size()));
            activityAgendasService.saveOrUpdateBatch(activityAgendasBatchList);
        }
        XxlJobHelper.log("【新增活动议程】执行完成");


        XxlJobHelper.log("【新增章节】开始执行，新增章节条数为:{}", sectionsList.size());
        for (int i = 0; i < activityAgendasList.size(); i += 1000) {
            List<Sections> sectionsBatchList = sectionsList.subList(i, Math.min(i + 1000, sectionsList.size()));
            sectionsService.saveOrUpdateBatch(sectionsBatchList);
        }
        XxlJobHelper.log("【新增章节】执行完成");

//        XxlJobHelper.log("【同步研究工作室文件信息】开始执行，新增文件表条数为:{}", filesList.size());
//        for (int i = 0; i < filesList.size(); i += 1000) {
//            List<ModuleFiles> batchFilesList = filesList.subList(i, Math.min(i + 1000, filesList.size()));
//            moduleFilesService.saveOrUpdateBatch(batchFilesList);
//        }
//        XxlJobHelper.log("【同步研究工作室文件信息】执行完成");

        XxlJobHelper.log("【同步研究工作室学段学科信息】开始执行，新增学段学科表条数为:{}", perAndSubList.size());
        for (int i = 0; i < perAndSubList.size(); i += 1000) {
            List<PeriodSubjects> batchPerAndSubList = perAndSubList.subList(i, Math.min(i + 1000, perAndSubList.size()));
            periodSubjectsService.saveOrUpdateBatch(batchPerAndSubList);
        }
        XxlJobHelper.log("【同步研究工作室学段学科信息】执行完成");

        XxlJobHelper.log("【同步研究工作室信息】开始执行，新增研究工作室信息表条数为:{}", courseList.size());

        for (int i = 0; i < courseList.size(); i += 1000) {
            List<Course> batchCourseList = courseList.subList(i, Math.min(i + 1000, courseList.size()));
            courseService.saveOrUpdateBatch(batchCourseList);
        }
        // 更新同步时间为当前时间
        redisUtils.setTimeValue(COURSE_UPDATE_TIME_KEY, now);
        XxlJobHelper.log("【同步研究工作室信息】执行完成");
    }

    @XxlJob("sync-course-users")
    @Transactional(rollbackFor = Exception.class)
    public void execute() throws Exception {
        // 获取所有数据
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时任务【同步研究工作室成员】开始执行，入参为:{}", param);
        JSONObject json = compatibilityJsonCheck(param);

        DataModelSearchDto dataModelSearchDto = new DataModelSearchDto();
        dataModelSearchDto.setId(DataModelNameConstant.GTTCYLB);
        dataModelSearchDto.setSize(0);
        dataModelSearchDto.setCurrent(0);

        Boolean all = json.getBoolean("all");
        if (!all) {
            // 获取1分钟前的时间
            LocalDateTime latestUpdateTime = cronsService.getLatestUpdateTime();
            if (ObjectNull.isNull(latestUpdateTime)) {
                latestUpdateTime = LocalDateTime.now().minusMinutes(1);
            }
            String lastUpdate = LocalDateTimeUtil.format(latestUpdateTime, "yyyy-MM-dd HH:mm:ss");
            if (ObjectNull.isNotNull(lastUpdate)) {
                DataModelSearchDto.SearchGroup searchGroup = buildUpdateTimeGroup(lastUpdate);
                dataModelSearchDto.setGroup(Collections.singletonList(searchGroup));
            }
        }

        List<Map<String, Object>> mapList = dataModelComponent.searchWithEcho(dataModelSearchDto);

        List<SyncUserInfoVO> list = BeanCopyUtil.copys(mapList, SyncUserInfoVO.class);

        List<Users> usersList = new ArrayList<>();
        List<UserSubjects> periodSubjectsList = new ArrayList<>();
        List<UserRoles> userRolesList = new ArrayList<>();
        List<UserGroups> userGroupsList = new ArrayList<>();

        list.stream()
                .filter(u -> Boolean.FALSE.equals(u.getDelFlag()) && ("已通过".equals(u.getJvsFlowTaskState())))
                .forEach(u -> {
                    // 跳过
                    Users userdata = usersService.getOne(Wrappers.<Users>lambdaQuery()
                            .eq(Users::getUserId, u.getCreateById())
                            .eq(Users::getCourseId, u.getCourseId())
                            .last("limit 1"), false);

                    if (userdata != null) {
                        return;
                    }

                    Users user = new Users();
                    user.setCourseId(u.getCourseId())
                            .setEnrolType(2)
                            .setUserId(u.getCreateById())
                            .setOrgCode(u.getDeptId())
                            .setPhone(u.getPhone())
                            .setCreateBy(u.getCreateBy())
                            .setUpdateBy(u.getUpdateBy())
                            .setCreateById(u.getCreateById())
                            .setUpdateById(u.getUpdateById());
                    usersList.add(user);

                    // 同步学科信息
                    if (u.getPeriodSubject() != null) {
                        List<UserSubjects> periodSubjectsTempList = new ArrayList<>();
                        u.getPeriodSubject().forEach(s -> {
                            String[] periodSubject = s.split("-");
                            UserSubjects us = new UserSubjects();
                            us.setPerAndSubId(s)
                                    .setPeriodId(periodSubject[0])
                                    .setSubjectId(periodSubject[1])
                                    .setUserId(u.getCreateById())
                                    .setCourseId(u.getCourseId());
                            periodSubjectsTempList.add(us);
                        });
                        if (u.getPeriodSubject1() != null) {
                            String[] periodSubject1 = u.getPeriodSubject1().split(",");
                            if (periodSubjectsTempList.size() > 0) {
                                for (int i = 0; i < periodSubjectsTempList.size(); i++) {
                                    if (periodSubject1[i] != null) {
                                        periodSubjectsTempList.get(i).setPerAndSub(periodSubject1[i]);
                                        periodSubjectsList.add(periodSubjectsTempList.get(i));
                                    }
                                }
                            }
                        }
                    }

                    // 同步角色信息
                    UserRoles ur = new UserRoles();
                    ur.setUserId(u.getCreateById())
                            .setCourseId(u.getCourseId())
                            .setRoleId(RoleConstant.STUDENT_ROLE);
                    userRolesList.add(ur);

                    // 给用户加一个null的小组
                    UserGroups ug = new UserGroups();
                    ug.setUserId(u.getCreateById())
                            .setCourseId(u.getCourseId())
                            .setGroupId(null);
                    userGroupsList.add(ug);
                });


        XxlJobHelper.log("同步用户信息，共{}条", usersList.size());
        for (int i = 0; i < usersList.size(); i += 1000) {
            List<Users> batchCourseList = usersList.subList(i, Math.min(i + 1000, usersList.size()));
            usersService.saveOrUpdateBatch(batchCourseList);
        }

        for (int i = 0; i < periodSubjectsList.size(); i += 1000) {
            List<UserSubjects> batchCourseList = periodSubjectsList.subList(i, Math.min(i + 1000, periodSubjectsList.size()));
            userSubjectsService.saveOrUpdateBatch(batchCourseList);
        }

        for (int i = 0; i < userRolesList.size(); i += 1000) {
            List<UserRoles> batchCourseList = userRolesList.subList(i, Math.min(i + 1000, userRolesList.size()));
            userRolesService.saveOrUpdateBatch(batchCourseList);
            List<String> batchUserIds = batchCourseList.stream().map(UserRoles::getUserId).collect(Collectors.toList());
            authRoleServiceApi.setUser(RoleConstant.STUDENT_ROLE, batchUserIds);
        }

        for (int i = 0; i < userGroupsList.size(); i += 1000) {
            List<UserGroups> batchCourseList = userGroupsList.subList(i, Math.min(i + 1000, userGroupsList.size()));
            userGroupsService.saveOrUpdateBatch(batchCourseList);
        }

        cronsService.save(new Crons().setType("sync-course-users"));

        XxlJobHelper.log("定时任务【同步研究工作室成员】执行完成");
    }

    @XxlJob("sync-indicators")
    @Transactional(rollbackFor = Exception.class)
    public void syncIndicators() {
        // 获取所有数据
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时任务【同步量表】开始执行，入参为:{}", param);

        DataModelSearchDto dataModelSearchDto = new DataModelSearchDto();
        dataModelSearchDto.setId(DataModelNameConstant.LBGL);
        dataModelSearchDto.setSize(0);
        dataModelSearchDto.setCurrent(0);

        List<Map<String, Object>> mapList = dataModelComponent.searchWithEcho(dataModelSearchDto);
        List<SyncIndicatorsVO> datalist = BeanCopyUtil.copys(mapList, SyncIndicatorsVO.class);

        List<Indicators> indicatorsList = new ArrayList<>();
        List<IndicatorsOptions> indicatorsOptionsList = new ArrayList<>();

        datalist.forEach(d -> {

            Indicators indicators = new Indicators();
            indicators.setId(d.getId());
            indicators.setName(d.getScoreIndicatorName());
            indicators.setFirstHeader(d.getHeaderFirstIndicator());
            indicators.setSecondHeader(d.getHeaderSecondIndicator());
            indicators.setThirdHeader(d.getHeaderThirdIndicator());
            indicators.setCreateBy(d.getCreateBy());
            indicators.setCreateById(d.getCreateById());
            indicatorsList.add(indicators);

            if (d.getDetails() != null) {
                // 删除旧数据
                indicatorsOptionsService.remove(Wrappers.<IndicatorsOptions>lambdaQuery()
                        .eq(IndicatorsOptions::getIndicatorId, d.getId()));

                AtomicInteger index1 = new AtomicInteger();
                d.getDetails().forEach(d1 -> {
                    com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(d1);
                    String firstUniqueId = d.getId() + jsonObject.getString("unique_id");
                    String firstContent = jsonObject.getString("first");
                    String firstScore = jsonObject.getString("score");

                    IndicatorsOptions indicatorsOptions = this.addIndicatorsOptions(firstUniqueId, d.getId(), null, firstContent, firstScore, index1.get());
                    indicatorsOptionsList.add(indicatorsOptions);
                    index1.getAndIncrement();

                    // 处理"second"数组
                    JSONArray secondArray = jsonObject.getJSONArray("second");
                    for (int i = 0; i < secondArray.size(); i++) {
                        com.alibaba.fastjson.JSONObject secondObject = secondArray.getJSONObject(i);
                        String secondUniqueId = d.getId() + secondObject.getString("unique_id");
                        String secondContent = secondObject.getString("content");
                        String secondScore = secondObject.getString("score");

                        IndicatorsOptions indicatorsOptions2 = this.addIndicatorsOptions(secondUniqueId, d.getId(), firstUniqueId, secondContent, secondScore, i);
                        indicatorsOptionsList.add(indicatorsOptions2);

                        // 处理"third"数组
                        JSONArray thirdArray = secondObject.getJSONArray("third");
                        for (int j = 0; j < thirdArray.size(); j++) {
                            com.alibaba.fastjson.JSONObject thirdObject = thirdArray.getJSONObject(j);
                            String thirdUniqueId = d.getId() + thirdObject.getString("unique_id");
                            String thirdContent = thirdObject.getString("content");
                            String thirdScore = thirdObject.getString("score");

                            IndicatorsOptions indicatorsOptions3 = this.addIndicatorsOptions(thirdUniqueId, d.getId(), secondUniqueId, thirdContent, thirdScore, i);
                            indicatorsOptionsList.add(indicatorsOptions3);
                        }
                    }
                });
            }
        });

        for (int i = 0; i < indicatorsList.size(); i += 1000) {
            List<Indicators> batchIndicatorsList = indicatorsList.subList(i, Math.min(i + 1000, indicatorsList.size()));
            indicatorsService.saveOrUpdateBatch(batchIndicatorsList);
        }

        for (int i = 0; i < indicatorsOptionsList.size(); i += 1000) {
            List<IndicatorsOptions> batchIndicatorsOptionList = indicatorsOptionsList.subList(i, Math.min(i + 1000, indicatorsOptionsList.size()));
            indicatorsOptionsService.saveOrUpdateBatch(batchIndicatorsOptionList);
        }

        XxlJobHelper.log("定时任务【同步量表】执行完成");
    }

    private IndicatorsOptions addIndicatorsOptions(String uniqueId, String indicatorId, String patentId, String content, String score, Integer sort) {
        IndicatorsOptions indicatorsOptions = new IndicatorsOptions();
        indicatorsOptions.setId(uniqueId);
        indicatorsOptions.setIndicatorId(indicatorId);
        indicatorsOptions.setParentId(patentId);
        indicatorsOptions.setContent(content);
        indicatorsOptions.setSort(sort);
        indicatorsOptions.setScore(score);

        return indicatorsOptions;
    }

    /**
     * 参数兼容json格式
     *
     * @param param 入参
     * @return JSONObject
     */
    private JSONObject compatibilityJsonCheck(String param) {
        XxlJobHelper.log("调用逻辑的数据为{}", param);
        if (ObjectNull.isNull(param)) {
            XxlJobHelper.log("数据传递参数为空,{},执行执行错误", param);
            throw new BusinessException("执行错误");
        }
        try {
            //做json格式兼容
            JSONObject jsonObject = JSONObject.parseObject(param);
            XxlJobHelper.log("数据格式为json格式,兼容检测成功,调用参数为,{}", jsonObject.toString());
            return jsonObject;
        } catch (Exception e) {
            XxlJobHelper.log("定时任务请求参数异常 不是json ，请以 【{\"key\":\"xxx\",\"startTime\":\"xxxx\",\"xxx\":\"xxxx\"}】 ，值为,{}", param);
            throw new BusinessException("逻辑定时任务参数不正确,请使用Json格式");
        }
    }

    private DataModelSearchDto.SearchGroup buildUpdateTimeGroup(String time) {
        DataModelSearchDto.SearchGroup group = new DataModelSearchDto.SearchGroup();
        DataModelSearchDto.SearchItem item = new DataModelSearchDto.SearchItem();
        group.setItems(new ArrayList<>());
        item.setKey("updateTime");
        item.setValue(time);
        item.setType("字符串");
        item.setQueryType(DataModelQueryType.ge);
        item.setAndOr(false);
        group.getItems().add(item);
        group.setAndOr(false);
        return group;
    }


    @XxlJob("sync-del-course-info")
    @Transactional(rollbackFor = Exception.class)
    public void syncDelCourseInfo() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时任务【同步删除工作室信息】开始执行，入参为:{}", param);
        JSONObject json = compatibilityJsonCheck(param);
        Boolean all = json.getBoolean("all");
        Query query = new Query();
        query.with(Sort.by(Sort.Direction.ASC, "updateTime"));
        int skip = 0;
        query.limit(500);
        query.fields().include("id");
        if (!all) {
            LocalDateTime latestUpdateTime = courseService.getLatestUpdateTime();
            String lastUpdate = LocalDateTimeUtil.format(latestUpdateTime, "yyyy-MM-dd HH:mm:ss");
            query.addCriteria(Criteria.where("updateTime").gt(lastUpdate));
        }

        while (true) {
            query.skip(skip);
            List<Map> maps = mongoTemplate.find(query, Map.class, "del_992033968788246528");
            if (CollectionUtil.isEmpty(maps)){
                break;
            }
            Set<String> ids = maps.stream().map(e -> (String) e.get("id")).collect(Collectors.toSet());
            if (ObjectNull.isNotNull(ids)) {
                courseService.removeByIds(ids);
            }

            ids.forEach(e->{
                Set<String> fileIds = moduleFilesService.list(new LambdaQueryWrapper<ModuleFiles>()
                                .eq(ModuleFiles::getCourseId, e)
                                .select(ModuleFiles::getId))
                        .stream().map(ModuleFiles::getId).collect(Collectors.toSet());
                IntStream.range(0, (int)Math.ceil((double)fileIds.size() / 500))
                        .mapToObj(i -> ids.stream().skip(i * 500L).limit(500).collect(Collectors.toList()))
                        .forEach(batchIds -> {
                            LambdaQueryWrapper<ModuleFiles> queryWrapper = new LambdaQueryWrapper<>();
                            queryWrapper.in(ModuleFiles::getCourseId, batchIds);

                            moduleFilesService.remove(queryWrapper);
                        });
            });

            skip += 500;
        }

        XxlJobHelper.log("定时任务【同步删除工作室信息】执行结束");
    }
}
