package cn.bctools.custom.handler;

import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.custom.component.DataModelComponent;
import cn.bctools.custom.constant.DataModelNameConstant;
import cn.bctools.custom.entity.data.UserRoles;
import cn.bctools.custom.entity.data.UserSubjects;
import cn.bctools.custom.entity.data.Users;
import cn.bctools.custom.entity.vo.SyncUserInfoVO;
import cn.bctools.custom.service.UserRolesService;
import cn.bctools.custom.service.UserSubjectsService;
import cn.bctools.custom.service.UsersService;
import cn.bctools.design.use.api.dto.DataModelSearchDto;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class SyncCourseUserHandler {


}
