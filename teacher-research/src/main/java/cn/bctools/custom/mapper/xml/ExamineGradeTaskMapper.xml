<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.custom.mapper.ExamineGradeTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.custom.entity.data.ExamineGradeTask">
        <id column="id" property="id" />
        <result column="examine_id" property="ExamineId" />
        <result column="user_id" property="userId" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_by_id" property="createById" />
        <result column="update_by_id" property="updateById" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, examine_id, user_id, create_by, update_by, create_by_id, update_by_id, create_time, update_time, del_flag, tenant_id
    </sql>

    <select id="searchPageList" parameterType="cn.bctools.custom.entity.vo.ExamineGradeTaskQueryVO" resultType="cn.bctools.custom.entity.vo.ExamineGradeTaskVO">
        SELECT
            agt.id as examine_grade_task_id,
            agt.examine_id as examine_id,
            ag.id as examine_grade_id,
            ag.score,
            au.score as average_score,
            au.id as examine_user_id,
            au.content as submit_content,
            au.status as submit_status,
            au.update_time as submit_time,
            au.user_id as submit_by_id,
            ag.status as grade_status,
            ag.create_time as grade_time,
            ag.comment as grade_content,
            ag.rating_type,
            agt.user_id as grade_by
        FROM
            examine_users au
             LEFT JOIN examine_grade_task agt ON agt.examine_id = au.examine_id and agt.del_flag=0
            LEFT JOIN examine_grade ag on agt.examine_id = ag.examine_id and  ag.del_flag =0 and ag.create_by_id = #{criteria.gradeBy} and ag.user_id  = au.user_id
        <where>
            <if test="criteria.submitStatus != null">
                and au.status = #{criteria.submitStatus}
            </if>
            <if test="criteria.gradeStatus != null">
                and ag.status = #{criteria.gradeStatus}
            </if>

            <if test="criteria.userIds != null and criteria.userIds.size() > 0">
                AND  au.user_id IN
                <foreach item="userId" collection="criteria.userIds" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>

            <if test="criteria.examineUserIds != null and criteria.examineUserIds.size() > 0">
                AND  au.id IN
                <foreach item="examineUserId" collection="criteria.examineUserIds" open="(" separator="," close=")">
                    #{examineUserId}
                </foreach>
            </if>
            and  agt.user_id = #{criteria.gradeBy}  and au.examine_id = #{criteria.examineId} and au.del_flag =0
        </where>

    </select>



    <select id="searchPlanPageList" parameterType="cn.bctools.custom.entity.vo.ExamineGradeTaskPlanQueryVO" resultType="cn.bctools.custom.entity.vo.ExamineGradeTaskPlanVO">
        SELECT
        au.id as examine_user_id,
        au.examine_id as examine_id,
        au.user_id as submit_by_id,
        a.trimmed_score,
        a.name as examine_name
        FROM
        examine_users au
        left join examine a on au.examine_id = a.id and a.del_flag =0
        <where>
            <if test="criteria.userIds != null and criteria.userIds.size() > 0">
                AND  au.user_id IN
                <foreach item="userId" collection="criteria.userIds" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>

            <if test="criteria.examineUserIds != null and criteria.examineUserIds.size() > 0">
                AND  au.id IN
                <foreach item="examineUserId" collection="criteria.examineUserIds" open="(" separator="," close=")">
                    #{examineUserId}
                </foreach>
            </if>
            and  au.examine_id = #{criteria.examineId} and au.del_flag =0
        </where>

    </select>


</mapper>
