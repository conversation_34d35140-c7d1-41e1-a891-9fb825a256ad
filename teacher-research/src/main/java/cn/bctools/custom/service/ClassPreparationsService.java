package cn.bctools.custom.service;

import cn.bctools.custom.entity.data.ClassPreparations;
import cn.bctools.custom.entity.vo.ClassPreparationFileVo;
import cn.bctools.custom.entity.vo.ClassPreparationVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sun.org.apache.xpath.internal.operations.Bool;

/**
 * <p>
 * 集体备课表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
public interface ClassPreparationsService extends IService<ClassPreparations> {
    ClassPreparationVo get(String courseModuleId);

    Boolean create(ClassPreparationVo classPreparationVo);

    Boolean edit(ClassPreparationVo classPreparationVo);

    Boolean checkEditPermission(String instance);

    Boolean editDocuments(ClassPreparationFileVo classPreparationFileVo);
}
