package cn.bctools.custom.service;

import cn.bctools.custom.entity.data.CourseModuleCompletion;
import cn.bctools.custom.entity.vo.UpdateAccessVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 协同研究工作室模块用户情况表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06
 */
public interface CourseModuleCompletionService extends IService<CourseModuleCompletion> {

    void view(String courseModuleId);

    @Transactional(rollbackFor = Exception.class)
    void updateAccess(UpdateAccessVo updateAccessVo);

    @Transactional(rollbackFor = Exception.class)
    void updateDownload(String courseModuleId);
}
