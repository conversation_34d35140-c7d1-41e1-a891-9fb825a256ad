package cn.bctools.custom.service;

import cn.bctools.custom.entity.data.StudyNotes;
import cn.bctools.custom.entity.vo.StudyNoteVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 研修笔记 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-01
 */
public interface StudyNotesService extends IService<StudyNotes> {

    StudyNoteVo getStudyNote(String courseModuleId);

    <PERSON><PERSON>an create(StudyNoteVo studyNoteVo);

    Boolean edit(StudyNoteVo studyNoteVo);

    Boolean checkEditPermission(String instanceId);

    StudyNotes getNoteById(String instanceId);
}
