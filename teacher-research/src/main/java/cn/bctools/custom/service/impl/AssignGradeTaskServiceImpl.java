package cn.bctools.custom.service.impl;

import cn.bctools.auth.api.api.AuthDeptServiceApi;
import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.auth.api.dto.SearchUserDto;
import cn.bctools.auth.api.enums.UserQueryType;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import cn.bctools.custom.component.CompletionComponent;
import cn.bctools.custom.component.CourseUserRoleComponent;
import cn.bctools.custom.constant.BucketConstant;
import cn.bctools.custom.entity.data.*;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.vo.*;
import cn.bctools.custom.mapper.AssignGradeTaskMapper;
import cn.bctools.custom.service.*;
import cn.bctools.custom.util.BuildExportResponseUtil;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 作业评分任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AssignGradeTaskServiceImpl extends ServiceImpl<AssignGradeTaskMapper, AssignGradeTask> implements AssignGradeTaskService {

    @Autowired
    AssignGradeTaskMapper assignGradeTaskMapper;
    @Autowired
    AssignService assignService;
    @Autowired
    AssignGradeService assignGradeService;
    @Autowired
    AssignGradeScaleService assignGradeScaleService;
    @Autowired
    ModuleFilesService moduleFilesService;
    @Autowired
    ModulesService modulesService;
    @Autowired
    AuthUserServiceApi authUserServiceApi;
    @Autowired
    AuthDeptServiceApi authDeptServiceApi;

    @Autowired
    CompletionComponent completionComponent;
    @Autowired
    UserSubjectsService userSubjectsService;
    @Autowired
    private AssignUsersService assignUsersService;
    @Autowired
    ModuleGroupsService moduleGroupsService;
    @Autowired
    UserGroupsService userGroupsService;

    @Autowired
    CourseUserRoleComponent roleComponent;
    @Autowired
    CourseModuleCompletionService completionService;


    @Override
    public Page<AssignGradeTaskVO> searchPageList(Page<AssignGradeTaskVO> page, AssignGradeTaskQueryVO assignGradeTaskQueryVO) {

        List<String> userIds = completionComponent.getUserIdsByCompletionListQueryVo(assignGradeTaskQueryVO, assignGradeTaskQueryVO.getCourseModuleId());
        if (ObjectNull.isNull(userIds)) {
            return new Page<>();
        }

        SearchUserDto searchUserDto = new SearchUserDto().setUserIds(userIds).setType(UserQueryType.and);
        if (ObjectNull.isNotNull(assignGradeTaskQueryVO.getKeyword())) {
            searchUserDto.setRealName(assignGradeTaskQueryVO.getKeyword());
        }
        List<UserDto> userDtos = authUserServiceApi.searchUserGetInfo(searchUserDto).getData();
        if (ObjectNull.isNull(userDtos)) {
            return new Page<>();
        }
        Map<String, UserDto> userDtoMap = userDtos.stream()
                .collect(Collectors.toMap(UserDto::getId, Function.identity()));
        assignGradeTaskQueryVO.setUserIds(userDtoMap.keySet().stream().collect(Collectors.toList()));
        Page<AssignGradeTaskVO> assignGradeTaskVOPage = assignGradeTaskMapper.searchPageList(page, assignGradeTaskQueryVO);
        List<AssignGradeTaskVO> records = assignGradeTaskVOPage.getRecords();
        AssignVO assignVO = assignService.getAssignById(assignGradeTaskQueryVO.getAssignId());

        Modules module = modulesService.getModule(assignGradeTaskQueryVO.getCourseModuleId());
        if(!CollectionUtils.isEmpty(records)){
            records.forEach(record -> {
//                Optional.ofNullable(record.getSubmitBy())
//                        .map(authUserServiceApi::getById)
//                        .map(R::getData)
//                        .ifPresent(data -> record.setSubmitBy(data.getRealName()));
//
//                Optional.ofNullable(record.getOrgCode())
//                        .map(authDeptServiceApi::getById)
//                        .map(R::getData)
//                        .ifPresent(deptDto -> {
//                            String orgCodeStr = Optional.ofNullable(deptDto.getShName()).orElse("")
//                                    + Optional.ofNullable(deptDto.getDsName()).orElse("")
//                                    + Optional.ofNullable(deptDto.getXqName()).orElse("")
//                                    + Optional.ofNullable(deptDto.getJdName()).orElse("");
//                            record.setOrgCode(orgCodeStr);
//                        });
                UserDto userDto = userDtoMap.get(record.getSubmitById());
                if (ObjectNull.isNotNull(userDto)) {
                    record.setOrgCode(userDto.getDeptName());
                    record.setSubmitBy(userDto.getRealName());
                }

                record.setPerAndSub(userSubjectsService.getPerAndSubs(record.getSubmitById(),module.getCourseId()));
                //学员提交作业文件
                Optional.ofNullable(modulesService.getOne(new LambdaQueryWrapper<Modules>()
                                .eq(Modules::getInstance, record.getAssignId())
                                .last("limit 1")))
                        .ifPresent(courseModule -> {
                            List<ModuleFiles> fileList = moduleFilesService.getFileList(courseModule.getId(),courseModule.getCourseId(),BucketConstant.MOD_ASSIGN_ANSWER,"",record.getAssignUserId());
                            if (!CollectionUtils.isEmpty(fileList)) {
                                record.setSubmitFileList(fileList);
                            }
                        });
                //量表信息
                //评分类型0不评分1直接打分；2评分量表
                if(!ObjectUtils.isEmpty(record.getRatingType()) && record.getRatingType() ==2){
                    List<IndicatorsVO> indicatorsVOList = new ArrayList<>();

                    List<AssignGradeScale> assignGradeScaleList = assignGradeScaleService.list(new LambdaQueryWrapper<AssignGradeScale>().eq(AssignGradeScale::getAssignId, assignGradeTaskQueryVO.getAssignId())
                            .eq(AssignGradeScale::getUserId, record.getSubmitById()).eq(AssignGradeScale::getCreateById, record.getGradeBy()));
                    if(!CollectionUtils.isEmpty(assignGradeScaleList)){
                        for (AssignGradeScale assignGradeScale : assignGradeScaleList) {
                            IndicatorsVO indicatorsVO = new IndicatorsVO();
                            indicatorsVO.setScaleOptionId(assignGradeScale.getScaleOptionId());
                            indicatorsVO.setScore(assignGradeScale.getScore());
                            indicatorsVOList.add(indicatorsVO);
                        }

                        record.setIndicatorsVOList(indicatorsVOList);
                        record.setIndicatorsId(assignVO.getRatingWith());
                    }
                }
                record.setAssignVO(assignVO);
                //评分反馈附件
                if(StrUtil.isNotEmpty(record.getAssignGradeId())){
                    Optional.ofNullable(modulesService.getOne(new LambdaQueryWrapper<Modules>()
                                    .eq(Modules::getInstance, record.getAssignId())
                                    .last("limit 1")))
                            .ifPresent(courseModule -> {
                                List<ModuleFiles> fileList = moduleFilesService.getFileList(courseModule.getId(),courseModule.getCourseId(),BucketConstant.MOD_ASSIGN_GRADE,"",record.getAssignGradeId());
                                if (!CollectionUtils.isEmpty(fileList)) {
                                    record.setGradeFileList(fileList);
                                }
                            });
                }

                //评分状态为空，设置为 未评分
                if(ObjectUtils.isEmpty(record.getGradeStatus())){
                    record.setGradeStatus((short) 0);
                }

            });
        }
        assignGradeTaskVOPage.setRecords(records);
        return assignGradeTaskVOPage;
    }

    @Override
    public Boolean submitOrStag(AssignGradeVO assignGradeVO) {
        String assignId = assignGradeVO.getAssignId();
        if(StrUtil.isEmpty(assignId)){
            throw new BusinessException("作业id不能为空");
        }
        String submitUserId = assignGradeVO.getUserId();
        if(StrUtil.isEmpty(submitUserId)){
            throw new BusinessException("提交用户id不能为空");
        }
        //状态，0未提交1已提交2暂存
        Short status = assignGradeVO.getStatus();
        if(ObjectUtils.isEmpty(status)){
            throw new BusinessException("提交状态不能为空");
        }
        Assign assign = assignService.getById(assignId);
        if(ObjectUtils.isEmpty(assign)){
            throw new BusinessException("作业不存在");
        }
        //判断学员提交作业状态是否是暂存状态
        AssignUsers assignUsers = assignUsersService.getOne(new LambdaQueryWrapper<AssignUsers>().eq(AssignUsers::getAssignId, assignId)
                .eq(AssignUsers::getUserId, submitUserId).last("limit 1"));
        if(!ObjectUtils.isEmpty(assignUsers) && assignUsers.getStatus() ==2 ){
            throw new BusinessException("学员提交作业是暂存状态，不可提交或暂存评分");
        }

        AssignGrade oldAssignGrade = assignGradeService.getOne(new LambdaQueryWrapper<AssignGrade>().eq(AssignGrade::getAssignId, assignId)
                .eq(AssignGrade::getUserId, submitUserId).eq(AssignGrade::getCreateById, UserCurrentUtils.getUserId()));
        if(!ObjectUtils.isEmpty(oldAssignGrade)){
            if(oldAssignGrade.getStatus() ==1){
                throw new BusinessException("作业已提交评分");
            }
        }
        String courseId = assign.getCourseId();
        //评分类型 0不评分1直接打分；2评分量表
        Short ratingType = assign.getRatingType();
        if(ratingType ==0){
            throw new BusinessException("作业不评分");
        }
        if(ratingType ==1){
            String ratingWith = assign.getRatingWith();
            if(StrUtil.isNotEmpty(ratingWith)){
                Float maxScoreLimit = Float.valueOf(ratingWith);
                if(assignGradeVO.getScore() > maxScoreLimit){
                    throw new BusinessException("提交的分数超过了评分最大分数");
                }
            }

        }

        AssignGrade assignGrade = new AssignGrade();
        BeanUtils.copyProperties(assignGradeVO,assignGrade);
        assignGrade.setRatingType(ratingType);
        if(ObjectUtils.isEmpty(assignGrade.getId())){
            EntityFillUtil.fillEntityFields(assignGrade, EntityFillType.CREATE);
        }else {
            EntityFillUtil.fillEntityFields(assignGrade, EntityFillType.UPDATE);
        }
        boolean result = assignGradeService.saveOrUpdate(assignGrade);

        if(ratingType ==2){
            //量表评分
            List<IndicatorsVO> indicatorsVOList = assignGradeVO.getIndicatorsVOList();
            if(CollectionUtils.isEmpty(indicatorsVOList)){
                throw new BusinessException("量表不能为空");
            }
            for (IndicatorsVO indicatorsVO : indicatorsVOList) {
                String scaleOptionId = indicatorsVO.getScaleOptionId();
                Float score = indicatorsVO.getScore();

                AssignGradeScale assignGradeScale = assignGradeScaleService.getOne(new LambdaQueryWrapper<AssignGradeScale>()
                        .eq(AssignGradeScale::getScaleOptionId, scaleOptionId)
                        .eq(AssignGradeScale::getAssignId, assignId)
                        .eq(AssignGradeScale::getUserId, submitUserId)
                        .eq(AssignGradeScale::getCreateById, UserCurrentUtils.getUserId())
                        .last("limit 1"));
                if(ObjectUtils.isEmpty(assignGradeScale)){
                    assignGradeScale = new AssignGradeScale();
                    assignGradeScale.setAssignId(assignId);
                    assignGradeScale.setUserId(submitUserId);
                    assignGradeScale.setScaleOptionId(scaleOptionId);
                    EntityFillUtil.fillEntityFields(assignGradeScale, EntityFillType.CREATE);
                }else {
                    EntityFillUtil.fillEntityFields(assignGradeScale, EntityFillType.UPDATE);
                }
                assignGradeScale.setScore(score);
                assignGradeScaleService.saveOrUpdate(assignGradeScale);
            }
        }
        //附件信息
        // 获取模块
        Modules modules = modulesService.getOne(new LambdaQueryWrapper<Modules>()
                .eq(Modules::getInstance, assignId)
                .last("limit 1"));

        if (modules != null) {
            List<ModuleFiles> fileList = assignGradeVO.getGradeFileList();
            moduleFilesService.saveOrUpdateFileContact(
                    fileList,
                    courseId,
                    modules.getId(),
                    BucketConstant.MOD_ASSIGN_GRADE,
                    "",
                    assignGrade.getId()
            );
        }
        //更新assign_user表的score字段
        if(status ==1){
            assignUsersService.updateScore(assignId, submitUserId, assign);

        }

        //如果是专家，判断是否评分完成
        if(roleComponent.checkExpert(modules.getCourseId())){
            CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                    .eq(CourseModuleCompletion::getCmId, modules.getId())
                    .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                    .last("limit 1"));
            if(ObjectUtils.isEmpty(completion)){
                completion = new CourseModuleCompletion();
                completion.setCourseId(modules.getCourseId());
                completion.setCmId(modules.getId());
                completion.setUserId(UserCurrentUtils.getUserId());
            }

            AssignCompletionQueryVO queryVo = new AssignCompletionQueryVO();
            List<String> userIds = completionComponent.getUserIdsByCompletionListQueryVo(queryVo, modules.getId());
            if (CollectionUtils.isEmpty(userIds)) {
                completion.setCompletionStatus(false);
            } else {
                long count = assignGradeService.count(new LambdaQueryWrapper<AssignGrade>()
                        .eq(AssignGrade::getAssignId, assignId).eq(AssignGrade::getCreateById, UserCurrentUtils.getUserId())
                        .in(!CollectionUtils.isEmpty(userIds), AssignGrade::getUserId, userIds)
                        .eq(AssignGrade::getStatus, 1));
                if (count == 0) {
                    completion.setCompletionStatus(false);
                }
                if (count > 1 && count == userIds.size()) {
                    completion.setCompletionStatus(true);
                }
            }
            completionService.saveOrUpdate(completion);

        }
        return result;
    }

    @Override
    public Boolean oneClickSubmit(String assignId) {
        if(StrUtil.isEmpty(assignId)){
            throw new BusinessException("作业id不能为空");
        }
        Optional.ofNullable(assignGradeService.list(new LambdaQueryWrapper<AssignGrade>()
                        .eq(AssignGrade::getAssignId,assignId).eq(AssignGrade::getCreateById,UserCurrentUtils.getUserId())))
                .filter(assignGrades -> !CollectionUtils.isEmpty(assignGrades))
                .ifPresent(assignGrades -> {
                    assignGrades.forEach(assignGrade -> assignGrade.setStatus((short) 1));
                    assignGradeService.saveOrUpdateBatch(assignGrades);
                });
        return true;
    }

    @Override
    public Page<AssignGradeTaskPlanVO> AssignPlanPage(Page<AssignGradeTaskPlanVO> page, AssignGradeTaskPlanQueryVO assignGradeTaskPlanQueryVO) {

        List<String> userIds = completionComponent.getUserIdsByCompletionListQueryVo(assignGradeTaskPlanQueryVO, assignGradeTaskPlanQueryVO.getCourseModuleId());
        if (ObjectNull.isNull(userIds)) {
            return new Page<>();
        }

        SearchUserDto searchUserDto = new SearchUserDto().setUserIds(userIds).setType(UserQueryType.and);
        if (ObjectNull.isNotNull(assignGradeTaskPlanQueryVO.getKeyword())) {
            searchUserDto.setRealName(assignGradeTaskPlanQueryVO.getKeyword());
        }
        List<UserDto> userDtos = authUserServiceApi.searchUserGetInfo(searchUserDto).getData();
        if (ObjectNull.isNull(userDtos)) {
            return new Page<>();
        }
        Map<String, UserDto> userDtoMap = userDtos.stream()
                .collect(Collectors.toMap(UserDto::getId, Function.identity()));
        assignGradeTaskPlanQueryVO.setUserIds(userDtoMap.keySet().stream().collect(Collectors.toList()));
        Page<AssignGradeTaskPlanVO> assignGradeTaskPlanVOPage = assignGradeTaskMapper.searchPlanPageList(page, assignGradeTaskPlanQueryVO);

        List<AssignGradeTaskPlanVO> records = assignGradeTaskPlanVOPage.getRecords();
        if(!CollectionUtils.isEmpty(records)){
            Modules module = modulesService.getModule(assignGradeTaskPlanQueryVO.getCourseModuleId());
            //评审专家数
            List<AssignGradeTask> expertNum = list(new LambdaQueryWrapper<AssignGradeTask>().eq(AssignGradeTask::getAssignId, assignGradeTaskPlanQueryVO.getAssignId()));
            records.forEach(record -> {
                UserDto userDto = userDtoMap.get(record.getSubmitById());
                if (ObjectNull.isNotNull(userDto)) {
                    record.setOrgCode(userDto.getDeptName());
                    record.setSubmitBy(userDto.getRealName());
                }

                record.setPerAndSub(userSubjectsService.getPerAndSubs(record.getSubmitById(),module.getCourseId()));

                record.setExpertNum(expertNum.size());

                List<AssignGrade> assignGradeList = assignGradeService.list(new LambdaQueryWrapper<AssignGrade>()
                        .eq(AssignGrade::getAssignId, assignGradeTaskPlanQueryVO.getAssignId())
                        .eq(AssignGrade::getUserId,record.getSubmitById()));

                // 已评分且分数大于0的记录
                List<AssignGrade> filteredList = assignGradeList.stream()
                        .filter(assignGrade -> assignGrade.getStatus() == 1 && assignGrade.getScore() > 0)
                        .collect(Collectors.toList());

                Short trimmedScore = record.getTrimmedScore();
                if (trimmedScore != null && trimmedScore > 0) {
                    // 排序
                    filteredList.sort(Comparator.comparingDouble(AssignGrade::getScore));

                    int size = filteredList.size();
                    int removeCount = Math.min(trimmedScore, size / 2);

                    if (removeCount * 2 <= size) {
                        filteredList = filteredList.subList(removeCount, size - removeCount);
                    } else {
                        filteredList = Collections.emptyList();
                    }
                }

                // 计算平均分
                OptionalDouble averageScore = filteredList.stream()
                        .mapToDouble(AssignGrade::getScore)
                        .average();
                double avgScore = averageScore.isPresent() ? averageScore.getAsDouble() : 0.0;
                record.setAverageScore(avgScore);

//                long notSubmittedCount = assignGradeList.stream()
//                        .filter(assignGrade -> assignGrade.getStatus() == 0)
//                        .count();
//
//                record.setNoSubmitNum((int) notSubmittedCount);
                long submittedCount = assignGradeList.stream()
                        .filter(assignGrade -> assignGrade.getStatus() == 1)
                        .count();
                record.setSubmitNum((int) submittedCount);

                long storeCount = assignGradeList.stream()
                        .filter(assignGrade -> assignGrade.getStatus() == 2)
                        .count();

                record.setStoreNum((int) storeCount);
                record.setNoSubmitNum( record.getExpertNum() - record.getSubmitNum()-record.getStoreNum());

                String reviewProgress = (record.getExpertNum() > 0)
                        ? String.format("%.2f%%", (double) submittedCount / record.getExpertNum() * 100)
                        : "0.00%";

                record.setReviewProgress(reviewProgress);

            });
        }
        assignGradeTaskPlanVOPage.setRecords(records);
        return assignGradeTaskPlanVOPage;
    }

    @Override
    public AssignGradeTaskStatisticVO gradeTaskStatisticWithOne(String assignId, String userId) {
        if(StrUtil.isEmpty(assignId)){
            throw new BusinessException("作业id不能为空");
        }
        Modules modules = modulesService.getOne(new LambdaQueryWrapper<Modules>().eq(Modules::getInstance, assignId).last("limit 1"));

        long count = moduleGroupsService.count(new LambdaQueryWrapper<ModuleGroups>().eq(ModuleGroups::getCmId, modules.getId())
                .eq(ModuleGroups::getGroupId, "0"));
        List<String>  groupUserIds = new ArrayList<>();
        if(count ==0){
            List<Groups> groups = moduleGroupsService.groupSelectList(modules.getId(), modules.getCourseId());
            List<String> groupIds = groups.stream().map(Groups::getId).collect(Collectors.toList());
            //查询在符合小组内的用户
            groupUserIds = userGroupsService.list(new LambdaQueryWrapper<UserGroups>()
                            .in(!CollectionUtils.isEmpty(groupIds),UserGroups::getGroupId, groupIds))
                    .stream().map(UserGroups::getUserId).collect(Collectors.toList());
        }

        //共需评分数
        long totalGradeNum = assignUsersService.count(new LambdaQueryWrapper<AssignUsers>().eq(AssignUsers::getAssignId, assignId)
                .in(!CollectionUtils.isEmpty(groupUserIds),AssignUsers::getUserId,groupUserIds));

        //状态，0未提交1已提交2暂存
        List<AssignGrade> assignGradeList = assignGradeService.list(
                new LambdaQueryWrapper<AssignGrade>()
                        .eq(AssignGrade::getAssignId, assignId)
                        .eq(AssignGrade::getCreateById, userId)
                        .in(!CollectionUtils.isEmpty(groupUserIds),AssignGrade::getUserId,groupUserIds)
        );

        //已提交数
        long submitNum = assignGradeList.stream().filter(assignGrade -> assignGrade.getStatus() == 1).count();
        long storeNum = assignGradeList.stream().filter(assignGrade -> assignGrade.getStatus() == 2).count();
        AssignGradeTaskStatisticVO result = new AssignGradeTaskStatisticVO();
        result.setTotalGradeNum((int) totalGradeNum);
        result.setSubmitNum((int) submitNum);

        result.setStoreNum((int) storeNum);
        int noSubmitSum = result.getTotalGradeNum() - result.getSubmitNum() - result.getStoreNum();
        if(noSubmitSum <0){
            noSubmitSum=0;
        }
        result.setNoSubmitNum(noSubmitSum);
        AssignVO assignVO = assignService.getAssignById(assignId);
        if(!ObjectUtils.isEmpty(assignVO)){
            result.setAssignVO(assignVO);
        }
        return result;
    }

    @Override
    public void exportGradeTask(Page<AssignGradeTaskVO> page, AssignGradeTaskQueryVO assignGradeTaskQueryVO, HttpServletResponse response) {

        Page<AssignGradeTaskVO> assignGradeTaskVOPage = searchPageList(page, assignGradeTaskQueryVO);
        List<AssignGradeTaskVO> records = assignGradeTaskVOPage.getRecords();
        try{
            Map<String, String> headerAlias  = new LinkedHashMap<>();
            headerAlias.put("submitBy", "姓名");
            headerAlias.put("perAndSub", "学科学段");
            headerAlias.put("orgCode", "所属单位");
            headerAlias.put("score", "分数");
            headerAlias.put("averageScore", "平均分");
            headerAlias.put("submitStatusText", "提交状态");
            headerAlias.put("submitTime", "提交时间");
            headerAlias.put("gradeStatusText", "评分状态");
            headerAlias.put("gradeTime", "评分时间");
            BuildExportResponseUtil.buildExportResponse(response,records,headerAlias,"评分列表数据");
        } catch (Exception ex) {
            log.error("数据导出异常", ex);
            throw new BusinessException("导出失败：" + ex.getMessage());
        }
    }

    @Override
    public void exportAssignPlan(Page<AssignGradeTaskPlanVO> page, AssignGradeTaskPlanQueryVO assignGradeTaskPlanQueryVO, HttpServletResponse response) {
        Page<AssignGradeTaskPlanVO> assignGradeTaskPlanVOPage = AssignPlanPage(page, assignGradeTaskPlanQueryVO);

        List<AssignGradeTaskPlanVO> records = assignGradeTaskPlanVOPage.getRecords();
        try{
            Map<String, String> headerAlias  = new LinkedHashMap<>();
            headerAlias.put("submitBy", "姓名");
            headerAlias.put("perAndSub", "学科学段");
            headerAlias.put("orgCode", "所属单位");
            headerAlias.put("expertNum", "专家数");
            headerAlias.put("averageScore", "平均分");
            headerAlias.put("reviewProgress", "评分进度");
            headerAlias.put("submitNum", "已评数");
            headerAlias.put("noSubmitNum", "待评数");
            headerAlias.put("storeNum", "暂存数");
            BuildExportResponseUtil.buildExportResponse(response,records,headerAlias,"作业评分进度列表数据");
        } catch (Exception ex) {
            log.error("数据导出异常", ex);
            throw new BusinessException("导出失败：" + ex.getMessage());
        }
    }

    @Override
    public Page<AssignPlanDetailVO> AssignPlanGet(Page page, AssignPlanDetailQueryVO assignPlanDetailQueryVO) {
        Page<AssignPlanDetailVO> result = new Page<>(page.getCurrent(), page.getSize(), 0);
        List<String>  userIds = new ArrayList<>();
        if(StrUtil.isNotEmpty(assignPlanDetailQueryVO.getKeyword())){
            SearchUserDto searchUserDto = new SearchUserDto().setRealName(assignPlanDetailQueryVO.getKeyword());
            List<UserDto> userDtos = authUserServiceApi.searchUserGetInfo(searchUserDto).getData();
            if (ObjectNull.isNull(userDtos)) {
                return result;
            }
            userIds = userDtos.stream().map(UserDto::getId).collect(Collectors.toList());
        }

        Page<AssignGrade> assignGradePage = assignGradeService.page(page, new LambdaQueryWrapper<AssignGrade>().eq(AssignGrade::getAssignId, assignPlanDetailQueryVO.getAssignId())
                .eq(AssignGrade::getUserId, assignPlanDetailQueryVO.getSubmitById())
                .in(!CollectionUtils.isEmpty(userIds),AssignGrade::getUserId,userIds));
        List<AssignGrade> records = assignGradePage.getRecords();
        if(CollectionUtils.isEmpty(records)){
            return result;
        }
//        if(CollectionUtils.isEmpty(userIds)){
//            userIds = records.stream().map(AssignGrade::getCreateById).collect(Collectors.toList());
//            SearchUserDto searchUserDto = new SearchUserDto().setUserIds(userIds);
//            List<UserDto> userDtos = authUserServiceApi.searchUserGetInfo(searchUserDto).getData();
//            if (ObjectNull.isNotNull(userDtos)) {
//                userDtoMap = userDtos.stream()
//                        .collect(Collectors.toMap(UserDto::getId, Function.identity()));
//            }
//
//        }


        List<AssignPlanDetailVO> assignPlanDetailVOList = new ArrayList<>();
        for (AssignGrade record : records) {
            AssignPlanDetailVO assignPlanDetailVO = new AssignPlanDetailVO();
            BeanUtils.copyProperties(record,assignPlanDetailVO);
            if(assignPlanDetailVO.getRatingType() ==2){
                Assign assign = assignService.getById(assignPlanDetailVO.getAssignId());
                if(!ObjectUtils.isEmpty(assign)){
                    assignPlanDetailVO.setIndicatorsId(assign.getRatingWith());

                    List<IndicatorsVO> indicatorsVOList = new ArrayList<>();

                    List<AssignGradeScale> assignGradeScaleList = assignGradeScaleService.list(new LambdaQueryWrapper<AssignGradeScale>().eq(AssignGradeScale::getAssignId, assignPlanDetailVO.getAssignId())
                            .eq(AssignGradeScale::getUserId, record.getUserId()).eq(AssignGradeScale::getCreateById, record.getCreateById()));
                    if(!CollectionUtils.isEmpty(assignGradeScaleList)){
                        for (AssignGradeScale assignGradeScale : assignGradeScaleList) {
                            IndicatorsVO indicatorsVO = new IndicatorsVO();
                            indicatorsVO.setScaleOptionId(assignGradeScale.getScaleOptionId());
                            indicatorsVO.setScore(assignGradeScale.getScore());
                            indicatorsVOList.add(indicatorsVO);
                        }

                        assignPlanDetailVO.setIndicatorsVOList(indicatorsVOList);
                    }
                }
            }
            if(StrUtil.isNotEmpty(assignPlanDetailVO.getCreateBy())){
                assignPlanDetailVO.setName(assignPlanDetailVO.getCreateBy());
            }

            //附件信息
            // 获取模块
            Modules modules = modulesService.getOne(new LambdaQueryWrapper<Modules>()
                    .eq(Modules::getInstance, assignPlanDetailVO.getAssignId())
                    .last("limit 1"));
            List<ModuleFiles> fileList = moduleFilesService.getFileList(modules.getId(), modules.getCourseId(), BucketConstant.MOD_ASSIGN_GRADE, "", assignPlanDetailVO.getId());
            if(!CollectionUtils.isEmpty(fileList)){
                assignPlanDetailVO.setGradeFileList(fileList);
            }
            assignPlanDetailVOList.add(assignPlanDetailVO);
        }
        result.setTotal(assignGradePage.getTotal());
        result.setRecords(assignPlanDetailVOList);
        return result;
    }

    @Override
    public Boolean deleteGrade(String assignGradeId) {
        AssignGrade assignGrade = assignGradeService.getById(assignGradeId);
        String assignId = assignGrade.getAssignId();
        String userId = assignGrade.getUserId();
        boolean assignGradeResult = assignGradeService.removeById(assignGradeId);

        boolean fileResult = moduleFilesService.remove(new LambdaQueryWrapper<ModuleFiles>().eq(ModuleFiles::getInfoId, assignGradeId)
                .eq(ModuleFiles::getRemarks,BucketConstant.MOD_ASSIGN_GRADE));
        //更新assign_user的score字段
        assignUsersService.updateScore(assignId,userId,assignService.getById(assignId));
        return assignGradeResult && fileResult;
    }

    @Override
    public Page<ExpertGradePlanVO> expertPlanPage(Page page, ExpertGradePlanQueryVO expertGradePlanQueryVO) {
        Page<ExpertGradePlanVO> result = new Page<>(page.getCurrent(), page.getSize(), 0);


        List<String>  userIds = new ArrayList<>();
        Map<String, UserDto> userDtoMap = new HashMap<>();
        Modules modules = modulesService.getOne(new LambdaQueryWrapper<Modules>().eq(Modules::getInstance, expertGradePlanQueryVO.getAssignId()).last("limit 1"));
        long count = moduleGroupsService.count(new LambdaQueryWrapper<ModuleGroups>().eq(ModuleGroups::getCmId, modules.getId())
                .eq(ModuleGroups::getGroupId, "0"));
        List<String>  groupUserIds = new ArrayList<>();
        if(count ==0){
            List<Groups> groups = moduleGroupsService.groupSelectList(modules.getId(), modules.getCourseId());
            List<String> groupIds = groups.stream().map(Groups::getId).collect(Collectors.toList());
            //查询在符合小组内的用户
            groupUserIds = userGroupsService.list(new LambdaQueryWrapper<UserGroups>()
                            .in(!CollectionUtils.isEmpty(groupIds),UserGroups::getGroupId, groupIds))
                    .stream().map(UserGroups::getUserId).collect(Collectors.toList());
        }

        if(StrUtil.isNotEmpty(expertGradePlanQueryVO.getKeyword())){
            SearchUserDto searchUserDto = new SearchUserDto().setRealName(expertGradePlanQueryVO.getKeyword());
            List<UserDto> userDtos = authUserServiceApi.searchUserGetInfo(searchUserDto).getData();
            if (ObjectNull.isNull(userDtos)) {
                return result;
            }
            userDtoMap = userDtos.stream()
                    .collect(Collectors.toMap(UserDto::getId, Function.identity()));
            userIds = userDtos.stream().map(UserDto::getId).collect(Collectors.toList());
        }
        Page<AssignGradeTask> assignGradeTaskPage = page(page, new LambdaQueryWrapper<AssignGradeTask>()
                .eq(AssignGradeTask::getAssignId, expertGradePlanQueryVO.getAssignId())
                .in(!CollectionUtils.isEmpty(userIds),AssignGradeTask::getUserId,userIds)
                .in(!CollectionUtils.isEmpty(expertGradePlanQueryVO.getAssignGradeTaskIds()),AssignGradeTask::getId,expertGradePlanQueryVO.getAssignGradeTaskIds()));
        List<AssignGradeTask> records = assignGradeTaskPage.getRecords();
        if(CollectionUtils.isEmpty(records)){
            return result;
        }


        if(CollectionUtils.isEmpty(userIds)){
            userIds = records.stream().map(AssignGradeTask::getUserId).collect(Collectors.toList());
            SearchUserDto searchUserDto = new SearchUserDto().setUserIds(userIds);
            List<UserDto> userDtos = authUserServiceApi.searchUserGetInfo(searchUserDto).getData();
            if (ObjectNull.isNotNull(userDtos)) {
                userDtoMap = userDtos.stream()
                        .collect(Collectors.toMap(UserDto::getId, Function.identity()));
            }

        }

        List<ExpertGradePlanVO> expertGradePlanVOList = new ArrayList<>();
        for (AssignGradeTask record : records) {
            ExpertGradePlanVO expertGradePlanVO = new ExpertGradePlanVO();
            // 专家姓名
            if(!CollectionUtils.isEmpty(userDtoMap)){
                UserDto userDto = userDtoMap.get(record.getUserId());
                if (ObjectNull.isNotNull(userDto)) {
                    expertGradePlanVO.setName(userDto.getRealName());
                }
            }
            expertGradePlanVO.setAssignGradeTaskId(record.getId());
            expertGradePlanVO.setAssignId(record.getAssignId());
            expertGradePlanVO.setGradeBy(record.getUserId());

            // 共需评分数
            long gradeNum = assignUsersService.count(new LambdaQueryWrapper<AssignUsers>().eq(AssignUsers::getAssignId, expertGradePlanQueryVO.getAssignId())
                    .in(!CollectionUtils.isEmpty(groupUserIds),AssignUsers::getUserId,groupUserIds).eq(AssignUsers::getStatus,1));

            // 统计评分状态
            List<AssignGrade> assignGradeList = assignGradeService.list(
                    new LambdaQueryWrapper<AssignGrade>()
                            .eq(AssignGrade::getAssignId, expertGradePlanQueryVO.getAssignId())
                            .eq(AssignGrade::getCreateById, record.getUserId())
                            .in(!CollectionUtils.isEmpty(groupUserIds),AssignGrade::getUserId,groupUserIds)
            );

            long submitNum = assignGradeList.stream().filter(assignGrade -> assignGrade.getStatus() == 1).count();
            long storeNum = assignGradeList.stream().filter(assignGrade -> assignGrade.getStatus() == 2).count();


            expertGradePlanVO.setGradeNum((int) gradeNum);
            expertGradePlanVO.setSubmitNum((int) submitNum);
            expertGradePlanVO.setStoreNum((int) storeNum);
            int noSubmitNum = expertGradePlanVO.getGradeNum() - expertGradePlanVO.getSubmitNum() - expertGradePlanVO.getStoreNum();
            if(noSubmitNum <0){
                noSubmitNum=0;
            }
            expertGradePlanVO.setNoSubmitNum(noSubmitNum);

            String reviewProgress = (gradeNum > 0)
                    ? String.format("%.2f%%", (double) submitNum / gradeNum * 100)
                    : "0.00%";
            expertGradePlanVO.setReviewProgress(reviewProgress);
            expertGradePlanVOList.add(expertGradePlanVO);
        }
        result.setTotal(assignGradeTaskPage.getTotal());
        result.setRecords(expertGradePlanVOList);
        return result;
    }

    @Override
    public void exportExpertPlan(Page page, ExpertGradePlanQueryVO expertGradePlanQueryVO, HttpServletResponse response) {
        Page<ExpertGradePlanVO> expertGradePlanVOPage = expertPlanPage(page, expertGradePlanQueryVO);

        List<ExpertGradePlanVO> records = expertGradePlanVOPage.getRecords();
        try{
            Map<String, String> headerAlias  = new LinkedHashMap<>();
            headerAlias.put("name", "姓名");
            headerAlias.put("reviewProgress", "评分进度");
            headerAlias.put("gradeNum", "共需评分数");
            headerAlias.put("submitNum", "已评数");
            headerAlias.put("storeNum", "已暂存数");
            headerAlias.put("noSubmitNum", "待评数");
            BuildExportResponseUtil.buildExportResponse(response,records,headerAlias,"专家评分进度列表数据");
        } catch (Exception ex) {
            log.error("数据导出异常", ex);
            throw new BusinessException("导出失败：" + ex.getMessage());
        }
    }

    @Override
    public AssignPlanStatisticVO AssignPlanStatistic(String assignId) {
        if(StrUtil.isEmpty(assignId)){
            throw new BusinessException("作业id不能为空");
        }
        AssignPlanStatisticVO assignPlanStatisticVO = new AssignPlanStatisticVO();
        AssignVO assignVO = assignService.getAssignById(assignId);
        if(!ObjectUtils.isEmpty(assignVO)){
            assignPlanStatisticVO.setAssignVO(assignVO);
        }
        //参与人数
        long joinNum = assignUsersService.count(new LambdaQueryWrapper<AssignUsers>().eq(AssignUsers::getAssignId, assignId));
        //已完成数
        long finishNum = assignGradeService.count(new LambdaQueryWrapper<AssignGrade>().eq(AssignGrade::getAssignId, assignId).eq(AssignGrade::getStatus,1));
        assignPlanStatisticVO.setJoinNum((int) joinNum);
        assignPlanStatisticVO.setFinishNum((int) finishNum);
        return assignPlanStatisticVO;
    }

    @Override
    public ExpertPlanStatisticVO expertPlanStatistic(String assignId) {
        if(StrUtil.isEmpty(assignId)){
            throw new BusinessException("作业id不能为空");
        }
        ExpertPlanStatisticVO expertPlanStatisticVO = new ExpertPlanStatisticVO();
        AssignVO assignVO = assignService.getAssignById(assignId);
        if(!ObjectUtils.isEmpty(assignVO)){
            expertPlanStatisticVO.setAssignVO(assignVO);
        }
        Modules modules = modulesService.getOne(new LambdaQueryWrapper<Modules>().eq(Modules::getInstance, assignId).last("limit 1"));
        long count = moduleGroupsService.count(new LambdaQueryWrapper<ModuleGroups>().eq(ModuleGroups::getCmId, modules.getId())
                .eq(ModuleGroups::getGroupId, "0"));
        List<String>  groupUserIds = new ArrayList<>();
        if(count ==0){
            List<Groups> groups = moduleGroupsService.groupSelectList(modules.getId(), modules.getCourseId());
            List<String> groupIds = groups.stream().map(Groups::getId).collect(Collectors.toList());
            //查询在符合小组内的用户
            groupUserIds = userGroupsService.list(new LambdaQueryWrapper<UserGroups>()
                            .in(!CollectionUtils.isEmpty(groupIds),UserGroups::getGroupId, groupIds))
                    .stream().map(UserGroups::getUserId).collect(Collectors.toList());
        }
        //分配多少专家
        long expertNum = count(new LambdaQueryWrapper<AssignGradeTask>().eq(AssignGradeTask::getAssignId, assignId));
        // 共需评分数
        long joinNum = assignUsersService.count(new LambdaQueryWrapper<AssignUsers>().eq(AssignUsers::getAssignId,assignId)
                .in(!CollectionUtils.isEmpty(groupUserIds),AssignUsers::getUserId,groupUserIds).eq(AssignUsers::getStatus,1));

        if(expertNum >0){
            joinNum = joinNum* expertNum;
        }
        List<AssignGrade> assignGradeList = assignGradeService.list(
                new LambdaQueryWrapper<AssignGrade>()
                        .eq(AssignGrade::getAssignId, assignId).in(!CollectionUtils.isEmpty(groupUserIds),AssignGrade::getUserId,groupUserIds)
        );
        long submitNum = assignGradeList.stream().filter(assignGrade -> assignGrade.getStatus() == 1).count();
        long storeNum = assignGradeList.stream().filter(assignGrade -> assignGrade.getStatus() == 2).count();

        expertPlanStatisticVO.setJoinNum((int) joinNum);
        expertPlanStatisticVO.setSubmitNum((int) submitNum);
        expertPlanStatisticVO.setStoreNum((int) storeNum);
        int noSubmitSum = expertPlanStatisticVO.getJoinNum() - expertPlanStatisticVO.getSubmitNum() - expertPlanStatisticVO.getStoreNum();
        if(noSubmitSum <0){
            noSubmitSum=0;
        }
        expertPlanStatisticVO.setNoSubmitNum(noSubmitSum);
        return expertPlanStatisticVO;
    }

}
