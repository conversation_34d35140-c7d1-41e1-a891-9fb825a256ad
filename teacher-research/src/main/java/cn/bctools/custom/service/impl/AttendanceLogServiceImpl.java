package cn.bctools.custom.service.impl;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.entity.data.*;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.enums.ModuleType;
import cn.bctools.custom.entity.vo.AttendanceLogVo;
import cn.bctools.custom.entity.vo.AttendanceSignInOrCancel;
import cn.bctools.custom.mapper.AttendanceLogMapper;
import cn.bctools.custom.service.*;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.custom.util.PermissionUtil;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 签到详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AttendanceLogServiceImpl extends ServiceImpl<AttendanceLogMapper, AttendanceLog> implements AttendanceLogService {

    @Autowired
    ModulesService modulesService;

    @Autowired
    CourseService courseService;

    @Autowired
    CourseModuleCompletionService completionService;

    @Autowired
    BaseMapper<AttendanceSessions> attendanceSessionsService;

    @Autowired
    BaseMapper<Attendances> attendancesService;

    @Override
    public List<AttendanceLogVo> getUserAttendanceLogVos(String courseModuleId) {
        String userId = UserCurrentUtils.getUserId();
        Modules module = modulesService.getById(courseModuleId);
        List<AttendanceLog> attendanceLogs;
        if (PermissionUtil.checkTrainingAdmin()) {
            AttendanceLog existLog = getOne(new LambdaQueryWrapper<AttendanceLog>()
                    .eq(AttendanceLog::getCourseId, module.getCourseId())
                    .eq(AttendanceLog::getAttendanceId, module.getInstance())
                    .orderByAsc(AttendanceLog::getUserId)
                    .last("LIMIT 1"));
            attendanceLogs = list(new LambdaQueryWrapper<AttendanceLog>()
                    .eq(AttendanceLog::getCourseId, module.getCourseId())
                    .eq(AttendanceLog::getAttendanceId, module.getInstance())
                    .eq(AttendanceLog::getUserId, existLog.getUserId()));
        } else {
            attendanceLogs = list(new LambdaQueryWrapper<AttendanceLog>()
                    .eq(AttendanceLog::getCourseId, module.getCourseId())
                    .eq(AttendanceLog::getAttendanceId, module.getInstance())
                    .eq(AttendanceLog::getUserId, userId));
        }

        if (ObjectNull.isNull(attendanceLogs)) {
            List<AttendanceSessions> attendanceSessions = attendanceSessionsService.selectList(new LambdaQueryWrapper<AttendanceSessions>()
                    .eq(AttendanceSessions::getCourseId, module.getCourseId())
                    .eq(AttendanceSessions::getAttendanceId, module.getInstance()));
            for (AttendanceSessions attendanceSession : attendanceSessions) {
                AttendanceLogVo attendanceLogVo = new AttendanceLogVo();
                attendanceLogVo.setCourseId(module.getCourseId());
                attendanceLogVo.setAttendanceId(attendanceSession.getAttendanceId());
                attendanceLogVo.setSessionId(attendanceSession.getId());
                attendanceLogVo.setUserId(userId);
                AttendanceLog attendanceLog = create(attendanceLogVo);

                CourseModuleCompletion completion = new CourseModuleCompletion()
                        .setCourseId(module.getCourseId())
                        .setUserId(userId)
                        .setCmId(attendanceLog.getId())
                        .setCompletionStatus(false)
                        .setLastViewTime(LocalDateTime.now());
                EntityFillUtil.fillEntityFields(completion, EntityFillType.CREATE);
                completionService.save(completion);
            }

            List<AttendanceLog> newAttendanceLogs = list(new LambdaQueryWrapper<AttendanceLog>()
                    .eq(AttendanceLog::getCourseId, module.getCourseId())
                    .eq(AttendanceLog::getAttendanceId, module.getInstance())
                    .eq(AttendanceLog::getUserId, userId));

            return syncOriginalData(newAttendanceLogs);
        }

        return syncOriginalData(attendanceLogs);
    }

    private List<AttendanceLogVo> syncOriginalData(List<AttendanceLog> attendanceLogs) {
        List<AttendanceLogVo> attendanceLogVos = new ArrayList<>();
        for (AttendanceLog attendanceLog : attendanceLogs) {
            AttendanceLogVo attendanceLogVo = BeanCopyUtil.copy(attendanceLog, AttendanceLogVo.class);
            AttendanceSessions attendanceSession = attendanceSessionsService.selectById(attendanceLog.getSessionId());
            Attendances attendance = attendancesService.selectById(attendanceLog.getAttendanceId());
            attendanceLogVo.setAttendanceName(attendance.getName());
            attendanceLogVo.setSessionName(attendanceSession.getName());
            attendanceLogVo.setStartTime(attendanceSession.getStartTime());
            attendanceLogVo.setEndTime(attendanceSession.getEndTime());
            attendanceLogVo.setStatusStr(attendanceLog.getStatus() ? "已签到" : "未签到");
            attendanceLogVos.add(attendanceLogVo);
        }
        return attendanceLogVos;
    }

    @Override
    public AttendanceLog create(AttendanceLogVo attendanceLogVo) {
        AttendanceLog attendanceLog = BeanCopyUtil.copy(attendanceLogVo, AttendanceLog.class);
        EntityFillUtil.fillEntityFields(attendanceLog, EntityFillType.CREATE);
        saveOrUpdate(attendanceLog);
        return attendanceLog;
    }

    @Override
    public AttendanceLogVo edit(String attendanceLogId) {
        AttendanceLog attendanceLog = getById(attendanceLogId);
        if (attendanceLog.getStatus()) {
            throw new BusinessException("该场次已签到，请勿重复签到");
        }
        AttendanceSessions attendanceSession = attendanceSessionsService.selectById(attendanceLog.getSessionId());
        if (LocalDateTime.now().isBefore(attendanceSession.getStartTime()) || LocalDateTime.now().isAfter(attendanceSession.getEndTime())) {
            return new AttendanceLogVo();
        }
        attendanceLog.setStatus(true);
        attendanceLog.setTime(LocalDateTime.now());
        EntityFillUtil.fillEntityFields(attendanceLog, EntityFillType.UPDATE);
        saveOrUpdate(attendanceLog);

        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, attendanceLog.getId())
                .eq(CourseModuleCompletion::getUserId, attendanceLog.getUserId()));
        completion.setCompletionStatus(true);
        completion.setLastViewTime(LocalDateTime.now());
        EntityFillUtil.fillEntityFields(completion, EntityFillType.UPDATE);
        completionService.updateById(completion);

        AttendanceLogVo attendanceLogVo = BeanCopyUtil.copy(attendanceLog, AttendanceLogVo.class);
        Attendances attendance = attendancesService.selectById(attendanceLog.getAttendanceId());
        attendanceLogVo.setAttendanceName(attendance.getName());
        attendanceLogVo.setSessionName(attendanceSession.getName());
        attendanceLogVo.setStartTime(attendanceSession.getStartTime());
        attendanceLogVo.setEndTime(attendanceSession.getEndTime());
        attendanceLogVo.setStatusStr(attendanceLog.getStatus() ? "已签到" : "未签到");
        return attendanceLogVo;
    }

    @Override
    public AttendanceLogVo edit2(String attendanceLogId, String courseModuleId) {
        AttendanceLog attendanceLog = getById(attendanceLogId);

        AttendanceSessions attendanceSession = attendanceSessionsService.selectById(attendanceLog.getSessionId());

        if (attendanceLog.getStatus()) {
            AttendanceLogVo attendanceLogVo = BeanCopyUtil.copy(attendanceLog, AttendanceLogVo.class);
            Attendances attendance = attendancesService.selectById(attendanceLog.getAttendanceId());
            attendanceLogVo.setCourseName(courseService.getCourse(attendance.getCourseId()).getName());
            attendanceLogVo.setAttendanceName(attendance.getName());
            attendanceLogVo.setSessionName(attendanceSession.getName());
            attendanceLogVo.setStartTime(attendanceSession.getStartTime());
            attendanceLogVo.setEndTime(attendanceSession.getEndTime());
            attendanceLogVo.setStatusStr(attendanceLog.getStatus() ? "已签到" : "未签到");
            return attendanceLogVo.setAttendanceStatus(2);
        }

        attendanceLog.setStatus(true);
        attendanceLog.setTime(LocalDateTime.now());
        EntityFillUtil.fillEntityFields(attendanceLog, EntityFillType.UPDATE);
        saveOrUpdate(attendanceLog);

        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, attendanceLog.getId())
                .eq(CourseModuleCompletion::getUserId, attendanceLog.getUserId()));
        completion.setCompletionStatus(true);
        completion.setLastViewTime(LocalDateTime.now());
        EntityFillUtil.fillEntityFields(completion, EntityFillType.UPDATE);
        completionService.updateById(completion);

        AttendanceLogVo attendanceLogVo = BeanCopyUtil.copy(attendanceLog, AttendanceLogVo.class);
        Attendances attendance = attendancesService.selectById(attendanceLog.getAttendanceId());
        attendanceLogVo.setCourseName(courseService.getCourse(attendance.getCourseId()).getName());
        attendanceLogVo.setAttendanceName(attendance.getName());
        attendanceLogVo.setSessionName(attendanceSession.getName());
        attendanceLogVo.setStartTime(attendanceSession.getStartTime());
        attendanceLogVo.setEndTime(attendanceSession.getEndTime());
        attendanceLogVo.setStatusStr(attendanceLog.getStatus() ? "已签到" : "未签到");
        return attendanceLogVo.setAttendanceStatus(0);
    }

    @Override
    public Boolean signInOrCancel(AttendanceSignInOrCancel attendanceSignInOrCancel) {
        AttendanceLog attendanceLog = getOne(new LambdaQueryWrapper<AttendanceLog>()
                .eq(AttendanceLog::getCourseId, attendanceSignInOrCancel.getCourseId())
                .eq(AttendanceLog::getAttendanceId, attendanceSignInOrCancel.getAttendanceId())
                .eq(AttendanceLog::getSessionId, attendanceSignInOrCancel.getSessionId())
                .eq(AttendanceLog::getUserId, attendanceSignInOrCancel.getUserId()));
        if (ObjectNull.isNull(attendanceLog)) {
            return false;
        }
        attendanceLog.setStatus(attendanceSignInOrCancel.getStatus());
        attendanceLog.setTime(LocalDateTime.now());
        EntityFillUtil.fillEntityFields(attendanceLog, EntityFillType.UPDATE);
        saveOrUpdate(attendanceLog);

        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, attendanceLog.getId())
                .eq(CourseModuleCompletion::getUserId, attendanceLog.getUserId()));
        if (ObjectNull.isNull(completion)) {
            return false;
        }
        completion.setCompletionStatus(attendanceSignInOrCancel.getStatus());
        EntityFillUtil.fillEntityFields(completion, EntityFillType.UPDATE);
        completionService.updateById(completion);
        return true;
    }
}
