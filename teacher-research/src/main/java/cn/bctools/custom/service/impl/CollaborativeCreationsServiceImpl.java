package cn.bctools.custom.service.impl;

import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.auth.api.dto.PersonnelDto;
import cn.bctools.auth.api.enums.PersonnelTypeEnum;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.common.utils.function.Get;
import cn.bctools.custom.api.DcLibraryApi;
import cn.bctools.custom.component.DataModelComponent;
import cn.bctools.custom.constant.DataModelNameConstant;
import cn.bctools.custom.constant.FlowDesignNameConstant;
import cn.bctools.custom.constant.ModuleFileTagConstant;
import cn.bctools.custom.constant.RoleConstant;
import cn.bctools.custom.dto.MergeDocumentsDto;
import cn.bctools.custom.dto.MergeDocumentsReqVo;
import cn.bctools.custom.dto.UpdateDocumentsPermissionReqVo;
import cn.bctools.custom.entity.data.CollaborativeCreations;
import cn.bctools.custom.entity.data.Course;
import cn.bctools.custom.entity.data.CourseModuleOnlineDocuments;
import cn.bctools.custom.entity.data.Modules;
import cn.bctools.custom.entity.enums.*;
import cn.bctools.custom.entity.vo.collaborativecreation.*;
import cn.bctools.custom.entity.vo.flowTask.FlowNodeVO;
import cn.bctools.custom.mapper.CollaborativeCreationsMapper;
import cn.bctools.custom.service.*;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.custom.util.PermissionUtil;
import cn.bctools.design.use.api.FlowTaskApi;
import cn.bctools.design.use.api.dto.FlowTaskDto;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 协同创作表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class CollaborativeCreationsServiceImpl extends ServiceImpl<CollaborativeCreationsMapper, CollaborativeCreations> implements CollaborativeCreationsService {

    ModulesService modulesService;

    ModuleGroupsService moduleGroupsService;

    CourseModuleOnlineDocumentsService courseModuleOnlineDocumentsService;

    DcLibraryApi dcLibraryApi;

    CourseService courseService;

    DataModelComponent dataModelComponent;

    FlowTaskApi flowTaskApi;

    @Override
    public CollaborativeCreationVo get(String courseModuleId) {
        CollaborativeCreationVo vo = new CollaborativeCreationVo();
        Modules module = modulesService.getById(courseModuleId);
        if (ObjectNull.isNull(module)) {
            return vo;
        }
        CollaborativeCreations collaborativeCreation = checkAndGet(module.getInstance());
        if (ObjectNull.isNull(collaborativeCreation)) {
            return vo;
        }

        vo = BeanCopyUtil.copy(collaborativeCreation, CollaborativeCreationVo.class);

        String groupIds = moduleGroupsService.getGroupIds(courseModuleId);
        vo.setGroupIds(groupIds);

        vo.setModule(ModuleType.collaborative_creation.getName());
        vo.setSection(module.getSection());

        vo.setVisible(module.getVisible());

        String currentUserId = UserCurrentUtils.getUserId();
        List<CourseModuleOnlineDocuments> taskList = courseModuleOnlineDocumentsService.list(
                courseModuleId,
                module.getCourseId(),
                currentUserId,
                ModuleFileTagConstant.TASK_LIST
        );
        vo.setTaskList(taskList);

        List<CourseModuleOnlineDocuments> taskListSummaryList = courseModuleOnlineDocumentsService.list(
                courseModuleId,
                module.getCourseId(),
                currentUserId,
                ModuleFileTagConstant.TASK_LIST_SUMMARY
        );
        if (!taskListSummaryList.isEmpty()) {
            CourseModuleOnlineDocuments taskListSummary = taskListSummaryList.get(0);
            vo.setTaskListSummary(taskListSummary);
        }

        Course course = courseService.getById(module.getCourseId());
        vo.setLeader(course.getLeader());
        vo.setCanDownload(module.getCanDownload());
        if(PermissionUtil.checkTrainingAdmin()){
            //文件是否允许下载
            vo.setEnableDownload(true);
        }else {
            vo.setEnableDownload(module.getCanDownload());
        }
        return vo;
    }

    @Override
    public Boolean create(CollaborativeCreationCreateVo createVo) {
        CollaborativeCreations collaborativeCreation = BeanCopyUtil.copy(createVo, CollaborativeCreations.class);
        EntityFillUtil.fillEntityFields(collaborativeCreation, EntityFillType.CREATE);

        collaborativeCreation.setStatus(CollaborativeCreationStatus.NEW);
        String courseId = collaborativeCreation.getCourseId();
        try {
            //同步数据到 mongoDB
            Map<String, Object> data = new HashMap<>();
            data.put(FlowDesignNameConstant.TASK_NAME, createVo.getName());
            data.put(FlowDesignNameConstant.BINd_FLOW, FlowDesignNameConstant.FLOW_DESIGN_ID);
            data.put(FlowDesignNameConstant.JVS_FlOW_TASK_STATE, CollaborativeCreationStatus.NEW.getName());
            data.put(FlowDesignNameConstant.JVS_FlOW_TASK_Progress,CollaborativeCreationStatus.NEW.getName());
            data.put(FlowDesignNameConstant.FlOW_TASK_STATE, FlowTaskState.PASS.getName());
            data.put(FlowDesignNameConstant.FlOW_TASK_Progress,CollaborativeCreationStatus.NEW.getName());
            data.put(FlowDesignNameConstant.DATA_ROLE,Arrays.asList(RoleConstant.PROVINCIAL_ADMINISTRATORS_ROLE));
            data.put(FlowDesignNameConstant.TASK_ID,0);
            Course course = courseService.getById(courseId);
            data.put(FlowDesignNameConstant.STUDIO_ID,courseId);
            data.put(FlowDesignNameConstant.STUDIO_NAME,course.getName());
            data.put(FlowDesignNameConstant.POSSESSOR,course.getLeader());
            data.put(FlowDesignNameConstant.POSSESSOR_ID,course.getLeaderId());
            String dataId = dataModelComponent.saveData(DataModelNameConstant.XZRW, data);
            collaborativeCreation.setDataId(dataId);
        } catch (Exception e) {
            log.error("创建协同创作同步协同任务数据到mongoDB出现异常:{}", e);
            throw new BusinessException("创建协同创作错误");
        }
        boolean flag = save(collaborativeCreation);
        if (!flag) {
            return false;
        }

        String moduleType = ModuleType.collaborative_creation.getName();
        String instanceId = collaborativeCreation.getId();
        Boolean visible = createVo.getVisible();
        Modules module = modulesService.saveCourseModuleContact(courseId, moduleType, instanceId, visible, createVo.getSection(), createVo.getCanDownload());
        moduleGroupsService.saveOrUpdateCourseGroupContact(courseId, createVo.getGroupIds(), module.getId());
        return true;
    }

    @Override
    public Boolean edit(CollaborativeCreationEditVo editVo) {
        String instanceId = editVo.getId();
        if (ObjectNull.isNull(instanceId)) {
            throw new BusinessException(("协同创作id不能为空"));
        }
        checkEditPermission(instanceId);

        CollaborativeCreations oldCollaborativeCreation = checkAndGet(instanceId);
        CollaborativeCreationStatus status = oldCollaborativeCreation.getStatus();
        if (status.equals(CollaborativeCreationStatus.CREATION_AUDIT) || status.equals(CollaborativeCreationStatus.OUTLINE_AUDIT)) {
            throw new BusinessException("审核中，不能修改");
        }

        CollaborativeCreations collaborativeCreation = BeanCopyUtil.copy(editVo, CollaborativeCreations.class);

        if (ObjectNull.isNull(editVo.getStartTime())) {
            collaborativeCreation.setStartTime(null);
        }
        if (ObjectNull.isNull(editVo.getEndTime())) {
            collaborativeCreation.setEndTime(null);
        }

        EntityFillUtil.fillEntityFields(collaborativeCreation, EntityFillType.UPDATE);
        boolean flag = updateById(collaborativeCreation);
        if (!flag) {
            return false;
        }

        String courseId = editVo.getCourseId();
        String moduleType = ModuleType.collaborative_creation.getName();
        Boolean visible = editVo.getVisible();
        Modules module = modulesService.saveCourseModuleContact(courseId, moduleType, instanceId, visible, editVo.getSection(), editVo.getCanDownload());
        moduleGroupsService.saveOrUpdateCourseGroupContact(courseId, editVo.getGroupIds(), module.getId());
        if (!StrUtil.equals(oldCollaborativeCreation.getName(), editVo.getName())) {
            try {
                //同步数据到 mongoDB
                Map<String, Object> data = new HashMap<>();
                data.put(FlowDesignNameConstant.TASK_NAME, editVo.getName());
                String dataId = oldCollaborativeCreation.getDataId();
                dataModelComponent.updateData(FlowDesignNameConstant.XTYC_APP_ID,
                        DataModelNameConstant.XZRW,
                        "",
                        dataId,
                        data);
            } catch (Exception e) {
                log.error("编辑协同创作同步协同任务数据到mongoDB出现异常:{}", e);
                throw new BusinessException("编辑协同创作错误");
            }
        }
        return true;
    }

    @Override
    public Boolean startCreationAudit(CollaborativeCreationStartAuditVo startAuditVo) {
        CollaborativeCreations collaborativeCreation = checkAndGet(startAuditVo.getId());
        if (!collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.NEW)) {
            throw new BusinessException("当前不是新建文档状态，不能发起创作审核");
        }

        checkEditPermission(startAuditVo.getId());

        collaborativeCreation.setStatus(CollaborativeCreationStatus.CREATION_AUDIT);
        collaborativeCreation.setCreationAuditorIds(startAuditVo.getUserIds());
        //同步状态到mongoDB
        String dataId = collaborativeCreation.getDataId();
        if (StrUtil.isNotEmpty(dataId)) {
            try {
                Map<String, Object> data = new HashMap<>();
                //初始化审批人
                //退回到新建文档状态后重新发起审核
                if(ObjectNull.isNotNull(collaborativeCreation.getFlowTaskId())){
                    FlowTaskDto flowTaskDto = new FlowTaskDto();
                    flowTaskDto.setDataId(dataId);
                    data = dataModelComponent.getMap(DataModelNameConstant.XZRW, dataId, null);
                    data.remove("_id");

                    data.put(FlowDesignNameConstant.FlOW_TASK_STATE, FlowTaskState.AUDIT.getName());
                    data.put(FlowDesignNameConstant.FlOW_TASK_Progress,CollaborativeCreationStatus.CREATION_AUDIT.getName());

                    flowTaskDto.setNodeId(CollaborativeTaskNode.NEW.getValue());
                    flowTaskDto.setDataId(dataId);
                    flowTaskDto.setData(BeanCopyUtil.copy(data, JSONObject.class));
                    flowTaskDto.setUserDto(UserCurrentUtils.getCurrentUser());
                    flowTaskDto.setDesignId(FlowDesignNameConstant.XTRW_DESIGN_ID);
                    flowTaskDto.setModelId(DataModelNameConstant.XZRW);

                    // 选择下一节点的审批人
                    if (!CollectionUtils.isEmpty(collaborativeCreation.getCreationAuditorIds())) {
                        List<Map<String, Object>> approvers = assignApproversToNode(collaborativeCreation.getCreationAuditorIds(), CollaborativeTaskNode.CREATION_AUDIT);
                        flowTaskDto.setApprovers(approvers);
                    }
                    flowTaskApi.execute(flowTaskDto);
                }else {
                    data.put(FlowDesignNameConstant.BINd_FLOW, FlowDesignNameConstant.FLOW_DESIGN_ID);
                    data.put(FlowDesignNameConstant.FLOW_NODE_1, FlowDesignNameConstant.FLOW_DESIGN_ID);
                    data.put(FlowDesignNameConstant.FLOW_NODE_FLOWID, FlowDesignNameConstant.FLOW_DESIGN_ID);
                    data.put("id", collaborativeCreation.getDataId());
                    data.put(FlowDesignNameConstant.CREATION_AUDITOR_IDS,collaborativeCreation.getCreationAuditorIds());
                    data.put(FlowDesignNameConstant.JVS_FlOW_TASK_Progress,CollaborativeTaskNode.CREATION_AUDIT.getFlowTaskProgress());
                    data.put(FlowDesignNameConstant.JVS_FlOW_TASK_STATE,FlowTaskState.AUDIT.getName());

                    data.put(FlowDesignNameConstant.FlOW_TASK_STATE, FlowTaskState.AUDIT.getName());
                    data.put(FlowDesignNameConstant.FlOW_TASK_Progress,CollaborativeCreationStatus.CREATION_AUDIT.getName());

                    data.put(FlowDesignNameConstant.MODEL_ID, DataModelNameConstant.XZRW);
                    //初始化审批人 节点信息
                    data.put(FlowDesignNameConstant.FLOW_NODE, assignApproversToAllNodes(startAuditVo.getUserIds()));
                    dataModelComponent.updateData(FlowDesignNameConstant.XTYC_APP_ID,
                            DataModelNameConstant.XZRW,
                            FlowDesignNameConstant.START_AUDIT_DESIGN_ID,
                            dataId,
                            data);
                    //获取对应的流程任务id
                    String flowTaskId = flowTaskApi.getFlowTaskIdByDataId(dataId).getData();
                    if(StrUtil.isNotEmpty(flowTaskId)){
                        collaborativeCreation.setFlowTaskId(flowTaskId);
                    }
                }

            } catch (Exception e) {
                log.error("发起创作审核同步协同任务数据到mongoDB出现异常:{}", e);
                throw new BusinessException("发起创作审核错误");
            }
        }
        updateById(collaborativeCreation);
        return true;
    }

    @Override
    public Boolean updateOutline(CollaborativeCreationUpdateOutlineVo updateOutlineVo) {
        CollaborativeCreations collaborativeCreation = checkAndGet(updateOutlineVo.getId());
        if (!collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.EDIT_OUTLINE)) {
            throw new BusinessException("当前不是编写大纲状态");
        }

        checkEditPermission(updateOutlineVo.getId());

        collaborativeCreation.setOutlineName(updateOutlineVo.getName());
        // 目前只支持手动创建
        collaborativeCreation.setOutlineType(CollaborativeCreationOutlineType.MANUAL);
        collaborativeCreation.setOutlineContent(updateOutlineVo.getContent());
        updateById(collaborativeCreation);
        return true;
    }

    @NotNull
    private static CollaborativeCreationUpdateFlowTaskStatusVo getFlowTaskStatusVo(CollaborativeCreations collaborativeCreation,CollaborativeCreationStatus creationStatus) {
        CollaborativeCreationUpdateFlowTaskStatusVo flowTaskStatusVo = new CollaborativeCreationUpdateFlowTaskStatusVo();
        flowTaskStatusVo.setId(collaborativeCreation.getId());
        flowTaskStatusVo.setNodeOperationType(NodeOperationType.PASS);
        flowTaskStatusVo.setNode(CollaborativeTaskNode.valueOf(creationStatus.name()));
        flowTaskStatusVo.setOption("同意");
        flowTaskStatusVo.setCollaborativeCreation(collaborativeCreation);
        return flowTaskStatusVo;
    }

    @Override
    public Boolean startOutlineAudit(CollaborativeCreationStartAuditVo startAuditVo) {
        CollaborativeCreations collaborativeCreation = checkAndGet(startAuditVo.getId());
        if (!collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.EDIT_OUTLINE)) {
            throw new BusinessException("当前不是编写大纲状态，不能发起审核");
        }

        checkEditPermission(startAuditVo.getId());

        collaborativeCreation.setStatus(CollaborativeCreationStatus.OUTLINE_AUDIT);
        collaborativeCreation.setOutlineAuditorIds(startAuditVo.getUserIds());
        updateById(collaborativeCreation);
        CollaborativeCreationUpdateFlowTaskStatusVo flowTaskStatusVo = getFlowTaskStatusVo(collaborativeCreation,CollaborativeCreationStatus.EDIT_OUTLINE);
        flowTaskNodeAudit(flowTaskStatusVo);
        return true;
    }

    @Override
    public Boolean assignTask(CollaborativeCreationAssignTaskVo assignTaskVo) {
        CollaborativeCreations collaborativeCreation = checkAndGet(assignTaskVo.getId());
        if (!collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.ASSIGN_TASK)) {
            throw new BusinessException("当前不是分配任务状态");
        }

        collaborativeCreation.setAssignTaskList(assignTaskVo.getAssignTaskList());
        updateById(collaborativeCreation);
        return true;
    }

    @Override
    public Boolean completeAssignTask(CollaborativeCreationCompleteAssignTaskVo completeAssignTaskVo) {
        CollaborativeCreations collaborativeCreation = checkAndGet(completeAssignTaskVo.getId());
        if (!collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.ASSIGN_TASK)) {
            throw new BusinessException("当前不是分配任务状态");
        }

        checkEditPermission(completeAssignTaskVo.getId());

        List<CourseModuleOnlineDocuments> taskList = completeAssignTaskVo.getTaskList();
        if (taskList.size() != collaborativeCreation.getAssignTaskList().size()) {
            throw new BusinessException("任务文档数量不匹配");
        }

        Modules module = modulesService.getModuleByInstanceId(collaborativeCreation.getId());

        courseModuleOnlineDocumentsService.saveOrUpdateContact(
                taskList,
                module.getId(),
                collaborativeCreation.getCourseId(),
                ModuleFileTagConstant.TASK_LIST,
                true
        );

        collaborativeCreation.setStatus(CollaborativeCreationStatus.WRITE_CONTENT);
        updateById(collaborativeCreation);
        CollaborativeCreationUpdateFlowTaskStatusVo flowTaskStatusVo = getFlowTaskStatusVo(collaborativeCreation,CollaborativeCreationStatus.ASSIGN_TASK);
        flowTaskNodeAudit(flowTaskStatusVo);
        return true;
    }

    @Override
    public Boolean intoArrangeContent(CollaborativeCreationIntoArrangeContentVo vo) {
        CollaborativeCreations collaborativeCreation = checkAndGet(vo.getId());
        if (!collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.WRITE_CONTENT)) {
            throw new BusinessException("当前不是内容编写状态");
        }

        // libraryIds
        String currentUserId = UserCurrentUtils.getUserId();

        Modules module = modulesService.getModuleByInstanceId(collaborativeCreation.getId());
        List<CourseModuleOnlineDocuments> taskList = courseModuleOnlineDocumentsService.list(
                module.getId(),
                module.getCourseId(),
                currentUserId,
                ModuleFileTagConstant.TASK_LIST
        );
        List<String> libraryIds = taskList.stream().map(CourseModuleOnlineDocuments::getFileId).collect(Collectors.toList());
        // module工作室名称
        Course course = courseService.getById(module.getCourseId());
        String moduleName = course.getName();
        // activity活动名称
        String activityName = collaborativeCreation.getName();

        // 合并文档
        MergeDocumentsReqVo mergeDocumentsReqVo = new MergeDocumentsReqVo();
        mergeDocumentsReqVo.setLibraryIds(libraryIds);
        mergeDocumentsReqVo.setName("协同文档");
        mergeDocumentsReqVo.setDirectory("research");
        mergeDocumentsReqVo.setModule(moduleName);
        mergeDocumentsReqVo.setActivity(activityName);
        List<String> courseUserIds = new ArrayList<>();
        courseUserIds.add(currentUserId);
        mergeDocumentsReqVo.setCanEditUserIds(courseUserIds);
        List<String> courseRoleIds = new ArrayList<>();
        courseRoleIds.add(RoleConstant.PROVINCIAL_ADMINISTRATORS_ROLE);
        mergeDocumentsReqVo.setCanEditRoleIds(courseRoleIds);

        R<MergeDocumentsDto> newDocumentResponse = dcLibraryApi.mergeDocuments(mergeDocumentsReqVo);
        if (newDocumentResponse.getCode() != 0) {
            throw new BusinessException("合并文档错误：" + newDocumentResponse.getMsg());
        }
        MergeDocumentsDto newDocument = newDocumentResponse.getData();

        List<CourseModuleOnlineDocuments> taskListSummaryList = new ArrayList<>();
        taskListSummaryList.add(new CourseModuleOnlineDocuments()
                .setFileId(newDocument.getId())
                .setName(newDocument.getName())
                .setType(newDocument.getType())
                .setKnowledgeId(newDocument.getKnowledgeId())
                .setEditUserIds("")
        );

        courseModuleOnlineDocumentsService.saveOrUpdateContact(
                taskListSummaryList,
                module.getId(),
                module.getCourseId(),
                ModuleFileTagConstant.TASK_LIST_SUMMARY,
                true
        );

        collaborativeCreation.setStatus(CollaborativeCreationStatus.ARRANGE_CONTENT);
        collaborativeCreation.setSummaryPublished(false);
        updateById(collaborativeCreation);
        CollaborativeCreationUpdateFlowTaskStatusVo flowTaskStatusVo = getFlowTaskStatusVo(collaborativeCreation,CollaborativeCreationStatus.WRITE_CONTENT);
        flowTaskNodeAudit(flowTaskStatusVo);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePublishStatus(CollaborativeCreationUpdatePublishStatusVo updatePublishStatusVo) {
        CollaborativeCreations collaborativeCreation = checkAndGet(updatePublishStatusVo.getId());
        if (!collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.ARRANGE_CONTENT)) {
            throw new BusinessException("当前不是内容编排状态");
        }

        checkEditPermission(updatePublishStatusVo.getId());

        if (collaborativeCreation.getSummaryPublished().equals(updatePublishStatusVo.getSummaryPublished())) {
            return true;
        }

        collaborativeCreation.setSummaryPublished(updatePublishStatusVo.getSummaryPublished());
        updateById(collaborativeCreation);

        String currentUserId = UserCurrentUtils.getUserId();
        Modules module = modulesService.getModuleByInstanceId(updatePublishStatusVo.getId());
        List<CourseModuleOnlineDocuments> taskListSummaryList = courseModuleOnlineDocumentsService.list(
                module.getId(),
                module.getCourseId(),
                currentUserId,
                ModuleFileTagConstant.TASK_LIST_SUMMARY
        );
        CourseModuleOnlineDocuments summaryDocument = taskListSummaryList.get(0);
        String summaryFileId = summaryDocument.getFileId();
        Course course = courseService.getById(module.getCourseId());

        // 取消发布，设置权限
        UpdateDocumentsPermissionReqVo.PermissionSetting permissionSetting = generatePermissionSetting(
                summaryFileId,
                course.getLeaderId(),
                RoleConstant.PROVINCIAL_ADMINISTRATORS_ROLE,
                summaryDocument.editUserIdsList(),
                null
        );

        // 发布，清空权限
        if (collaborativeCreation.getSummaryPublished()) {
            permissionSetting = generatePermissionSetting(
                    summaryFileId,
                    null,
                    null,
                    new ArrayList<>(),
                    null
            );
        }

        UpdateDocumentsPermissionReqVo reqVo = new UpdateDocumentsPermissionReqVo();
        List<UpdateDocumentsPermissionReqVo.PermissionSetting> permissionSettings = new ArrayList<>();
        permissionSettings.add(permissionSetting);
        reqVo.setPermissionSettings(permissionSettings);

        R<Boolean> updateResponse = dcLibraryApi.batchUpdatePermission(reqVo);
        if (updateResponse.getCode() != 0) {
            throw new BusinessException("更新权限错误：" + updateResponse.getMsg());
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSummaryPermission(CollaborativeCreationUpdateSummaryPermissionVo updateSummaryPermissionVo) {
        CollaborativeCreations collaborativeCreation = checkAndGet(updateSummaryPermissionVo.getId());
        if (!collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.ARRANGE_CONTENT)) {
            throw new BusinessException("当前不是内容编排状态");
        }

        checkEditPermission(updateSummaryPermissionVo.getId());

        String currentUserId = UserCurrentUtils.getUserId();
        Modules module = modulesService.getModuleByInstanceId(updateSummaryPermissionVo.getId());

        List<CourseModuleOnlineDocuments> taskListSummaryList = courseModuleOnlineDocumentsService.list(
                module.getId(),
                module.getCourseId(),
                currentUserId,
                ModuleFileTagConstant.TASK_LIST_SUMMARY
        );
        if (taskListSummaryList.isEmpty()) {
            throw new BusinessException("当前没有生成协同文档，请通知管理员处理问题");
        }

        CourseModuleOnlineDocuments summaryDocument = taskListSummaryList.get(0);
        String summaryFileId = summaryDocument.getFileId();
        courseModuleOnlineDocumentsService.updateUserIds(summaryDocument.getId(), updateSummaryPermissionVo.getUserIds());

        if (collaborativeCreation.getSummaryPublished()) {
            // 等取消发布再改在线文档编辑权限
            return true;
        }

        Course course = courseService.getById(module.getCourseId());

        UpdateDocumentsPermissionReqVo reqVo = new UpdateDocumentsPermissionReqVo();
        List<UpdateDocumentsPermissionReqVo.PermissionSetting> permissionSettings = new ArrayList<>();
        permissionSettings.add(generatePermissionSetting(
                summaryFileId,
                course.getLeaderId(),
                RoleConstant.PROVINCIAL_ADMINISTRATORS_ROLE,
                summaryDocument.editUserIdsList(),
                null
        ));
        reqVo.setPermissionSettings(permissionSettings);

        R<Boolean> updateResponse = dcLibraryApi.batchUpdatePermission(reqVo);
        if (updateResponse.getCode() != 0) {
            throw new BusinessException("更新权限错误：" + updateResponse.getMsg());
        }

        return true;
    }

    private UpdateDocumentsPermissionReqVo.PermissionSetting generatePermissionSetting(
            String fileId,
            String courseLeaderId,
            String adminRoleId,
            List<String> editUserIds,
            String filename
    ) {
        List<String> canEditUserIds = new ArrayList<>(editUserIds);
        List<String> canEditRoleIds = new ArrayList<>();

        if (ObjectNull.isNotNull(courseLeaderId)) {
            canEditUserIds.add(courseLeaderId);
        }
        if (ObjectNull.isNotNull(adminRoleId)) {
            canEditRoleIds.add(adminRoleId);
        }

        UpdateDocumentsPermissionReqVo.PermissionSetting setting = new UpdateDocumentsPermissionReqVo.PermissionSetting()
                .setLibraryId(fileId)
                .setCanEditUserIds(canEditUserIds)
                .setCanEditRoleIds(canEditRoleIds)
                .setName(filename);

        if (ObjectNull.isNotNull(filename)) {
            setting.setName(filename);
        }

        return setting;
    }

    @Override
    public Boolean updateTaskList(CollaborativeCreationUpdateTaskVo updateTaskVo) {
        CollaborativeCreations collaborativeCreation = checkAndGet(updateTaskVo.getId());
        if (!collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.WRITE_CONTENT)) {
            throw new BusinessException("当前不是编写内容状态");
        }

        checkEditPermission(updateTaskVo.getId());

        CourseModuleOnlineDocuments task = courseModuleOnlineDocumentsService.getById(updateTaskVo.getTaskId());
        if (ObjectNull.isNull(task)) {
            throw new BusinessException("任务不存在");
        }

        Course course = courseService.getById(task.getCourseId());

        task.setEditUserIds(String.join(",", task.getEditUserIds()));
        if (ObjectNull.isNotNull(updateTaskVo.getTaskName())) {
            task.setName(updateTaskVo.getTaskName());
        }
        courseModuleOnlineDocumentsService.updateById(task);

        UpdateDocumentsPermissionReqVo reqVo = new UpdateDocumentsPermissionReqVo();
        List<UpdateDocumentsPermissionReqVo.PermissionSetting> permissionSettings = new ArrayList<>();

        permissionSettings.add(generatePermissionSetting(
                        task.getFileId(),
                        course.getLeaderId(),
                        RoleConstant.PROVINCIAL_ADMINISTRATORS_ROLE,
                        updateTaskVo.getEditUserIds(),
                        updateTaskVo.getTaskName()
                )
        );
        reqVo.setPermissionSettings(permissionSettings);
        R<Boolean> updateResponse = dcLibraryApi.batchUpdatePermission(reqVo);
        if (updateResponse.getCode() != 0) {
            throw new BusinessException("更新在线文档错误：" + updateResponse.getMsg());
        }

        return true;
    }

    private CollaborativeCreations checkAndGet(String instanceId) {
        CollaborativeCreations collaborativeCreation = getById(instanceId);
        if (ObjectNull.isNull(collaborativeCreation)) {
            throw new BusinessException("协同创作不存在");
        }

        Modules module = modulesService.getModuleByInstanceId(collaborativeCreation.getId());
        if (ObjectNull.isNull(module)) {
            throw new BusinessException("模块活动不存在");
        }

        if (!module.getModule().equals(ModuleType.collaborative_creation.getName())) {
            throw new BusinessException("模块不是协同创作");
        }

        return syncStatusWithModelData(collaborativeCreation, collaborativeCreation.getDataId());
    }

    /**
     * 拿MongoDB的状态
     * 比较、同步状态
     */
    private CollaborativeCreations syncStatusWithModelData(CollaborativeCreations collaborativeCreation, String dataId) {
        if (!collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.CREATION_AUDIT)
                && !collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.OUTLINE_AUDIT)
        ) {
            return collaborativeCreation;
        }

        Map<String, Object> data = dataModelComponent.getMap(DataModelNameConstant.XZRW, dataId, null);
        if (data == null) {
            return collaborativeCreation;
        }

        String dataStatus = data.get(FlowDesignNameConstant.JVS_FlOW_TASK_Progress).toString();

        // 只同步审核通过的状态
        if (collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.CREATION_AUDIT)
                && dataStatus.equals(CollaborativeTaskNode.EDIT_OUTLINE.getFlowTaskProgress())
        ) {
            collaborativeCreation.setStatus(CollaborativeCreationStatus.EDIT_OUTLINE);
            updateById(collaborativeCreation);
            return collaborativeCreation;
        }

        if (collaborativeCreation.getStatus().equals(CollaborativeCreationStatus.OUTLINE_AUDIT)
                && dataStatus.equals(CollaborativeTaskNode.ASSIGN_TASK.getFlowTaskProgress())
        ) {
            collaborativeCreation.setStatus(CollaborativeCreationStatus.ASSIGN_TASK);
            updateById(collaborativeCreation);
            return collaborativeCreation;
        }

        // 在MongoDB数据比MySQL数据新时更新状态
        String dataUpdateTimeStr = data.get("updateTime").toString();
        if (ObjectNull.isNull(dataUpdateTimeStr)) {
            return collaborativeCreation;
        }

        LocalDateTime dataUpdateTime = LocalDateTime.parse(dataUpdateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime updateTime = collaborativeCreation.getUpdateTime();
        if (updateTime.isAfter(dataUpdateTime)) {
            return collaborativeCreation;
        }

        Map<String, CollaborativeCreationStatus> statusMap = new HashMap<>();
        statusMap.put(CollaborativeTaskNode.NEW.getFlowTaskProgress(), CollaborativeCreationStatus.NEW);
        statusMap.put(CollaborativeTaskNode.CREATION_AUDIT.getFlowTaskProgress(), CollaborativeCreationStatus.CREATION_AUDIT);
        statusMap.put(CollaborativeTaskNode.EDIT_OUTLINE.getFlowTaskProgress(), CollaborativeCreationStatus.EDIT_OUTLINE);
        statusMap.put(CollaborativeTaskNode.OUTLINE_AUDIT.getFlowTaskProgress(), CollaborativeCreationStatus.OUTLINE_AUDIT);
        statusMap.put(CollaborativeTaskNode.ASSIGN_TASK.getFlowTaskProgress(), CollaborativeCreationStatus.ASSIGN_TASK);
        statusMap.put(CollaborativeTaskNode.WRITE_CONTENT.getFlowTaskProgress(), CollaborativeCreationStatus.WRITE_CONTENT);
        statusMap.put(CollaborativeTaskNode.ARRANGE_CONTENT.getFlowTaskProgress(), CollaborativeCreationStatus.ARRANGE_CONTENT);

        CollaborativeCreationStatus status = statusMap.get(dataStatus);
        if (ObjectNull.isNull(status)) {
            return collaborativeCreation;
        }

        if (collaborativeCreation.getStatus().equals(status)) {
            return collaborativeCreation;
        }

        collaborativeCreation.setStatus(status);
        updateById(collaborativeCreation);

        return collaborativeCreation;
    }

    @Override
    public CollaborativeCreations checkEditPermission(String instanceId) {
        CollaborativeCreations collaborativeCreation = checkAndGet(instanceId);

        if (PermissionUtil.checkTrainingAdmin()) {
            return collaborativeCreation;
        }

        String createById = collaborativeCreation.getCreateById();
        if (!UserCurrentUtils.getUserId().equals(createById)) {
            throw new BusinessException("暂无权限");
        }

        return collaborativeCreation;
    }

    @Override
    public Boolean flowTaskNodeAudit(CollaborativeCreationUpdateFlowTaskStatusVo collaborativeCreationUpdateFlowTaskStatusVo) {
        //  所有节点的审批操作都用这个方法
        CollaborativeCreations collaborativeCreation;
        if(ObjectNull.isNotNull(collaborativeCreationUpdateFlowTaskStatusVo.getCollaborativeCreation())){
            collaborativeCreation = collaborativeCreationUpdateFlowTaskStatusVo.getCollaborativeCreation();
        }else {
            String id = collaborativeCreationUpdateFlowTaskStatusVo.getId();
            collaborativeCreation = checkAndGet(id);
            if (ObjectNull.isNull(collaborativeCreation)) {
                throw new BusinessException("协同创作不存在");
            }
        }

        List<String> userIds = new ArrayList<>(); //指定下一节点的审批人 可为空
        UserDto userDto = UserCurrentUtils.getCurrentUser();
        String userId = userDto.getId();
        //创作审核 -审核人
        if (CollaborativeTaskNode.CREATION_AUDIT.equals(collaborativeCreationUpdateFlowTaskStatusVo.getNode())) {
            List<String> creationAuditorIds = collaborativeCreation.getCreationAuditorIds();
            if (!PermissionUtil.checkTrainingAdmin() && !creationAuditorIds.contains(userId)) {
                throw new BusinessException("您不是创作审核的的审核人，暂无权限");
            }
        }
        //大纲审核-审核人
        if (CollaborativeTaskNode.OUTLINE_AUDIT.equals(collaborativeCreationUpdateFlowTaskStatusVo.getNode())) {
            List<String> outlineAuditorIds = collaborativeCreation.getOutlineAuditorIds();
            if (!PermissionUtil.checkTrainingAdmin() && !outlineAuditorIds.contains(userId)) {
                throw new BusinessException("您不是大纲审核的的审核人，暂无权限");
            }
        }

        String dataId = collaborativeCreation.getDataId();
        if (StrUtil.isEmpty(dataId)) {
            throw new BusinessException("未找到关联的模型数据id");
        }
        //构建流程流转的数据
        FlowTaskDto flowTaskDto = buildFlowTaskDTO(collaborativeCreationUpdateFlowTaskStatusVo, dataId, collaborativeCreation,userIds);
        flowTaskDto.setUserDto(userDto);
        flowTaskDto.setDesignId(FlowDesignNameConstant.XTRW_DESIGN_ID);
        flowTaskDto.setModelId(DataModelNameConstant.XZRW);
        flowTaskApi.execute(flowTaskDto);
        return true;
    }


    /**
     * 给所有节点 初始化审批人
     * @param userIds
     * @return
     */
    public static List<FlowNodeVO>  assignApproversToAllNodes(List<String> userIds) {
        List<FlowNodeVO> nodeApproversList = new ArrayList<>();
        CollaborativeTaskNode currentNode = CollaborativeTaskNode.CREATION_AUDIT;
        AuthUserServiceApi authUserServiceApi = SpringContextUtil.getBean(AuthUserServiceApi.class);
        List<UserDto> userDtoList = authUserServiceApi.getBasicInfoById(userIds, Arrays.asList(Get.name(UserDto::getRealName),Get.name(UserDto::getId))).getData();
        Map<String, String> userDtoMap = userDtoList.stream().collect(Collectors.toMap(UserDto::getId, UserDto::getRealName));
        while (currentNode != null) {
            List<PersonnelDto> approvers = userIds.stream()
                    .map(id -> new PersonnelDto().setId(id).setName(userDtoMap.get(id)).setType(PersonnelTypeEnum.user))
                    .collect(Collectors.toList());

            FlowNodeVO nodeApprovers = new FlowNodeVO().setNodeId(currentNode.getValue()).setApprovers(approvers);
            nodeApproversList.add(nodeApprovers);
            currentNode = currentNode.getNextNode();
        }

        return nodeApproversList;
    }

    /**
     * 给单个节点设置审批人
     * @param userIds
     * @return
     */
    public static List<Map<String,Object>> assignApproversToNode(List<String> userIds,CollaborativeTaskNode node) {
        List<Map<String,Object>> nodeApproversList = new ArrayList<>();

        AuthUserServiceApi authUserServiceApi = SpringContextUtil.getBean(AuthUserServiceApi.class);
        List<UserDto> userDtoList = authUserServiceApi.getBasicInfoById(userIds, Arrays.asList(Get.name(UserDto::getRealName),Get.name(UserDto::getId))).getData();
        Map<String, String> userDtoMap = userDtoList.stream().collect(Collectors.toMap(UserDto::getId, UserDto::getRealName));
        List<PersonnelDto> approvers = userIds.stream()
                .map(id -> new PersonnelDto().setId(id).setName(userDtoMap.get(id)).setType(PersonnelTypeEnum.user))
                .collect(Collectors.toList());
        Map<String,Object> approver = new HashMap<>();
        approver.put("nodeId",node.getValue());
        approver.put("approvers",approvers);
        nodeApproversList.add(approver);
        return nodeApproversList;
    }

    @NotNull
    private FlowTaskDto buildFlowTaskDTO(CollaborativeCreationUpdateFlowTaskStatusVo collaborativeCreationUpdateFlowTaskStatusVo,
                                         String dataId,
                                         CollaborativeCreations collaborativeCreation,
                                         List<String> userIds) {
        NodeOperationType nodeOperationType = collaborativeCreationUpdateFlowTaskStatusVo.getNodeOperationType();
        CollaborativeTaskNode backNode = collaborativeCreationUpdateFlowTaskStatusVo.getBackNode();
        CollaborativeTaskNode node = collaborativeCreationUpdateFlowTaskStatusVo.getNode();
        String option = collaborativeCreationUpdateFlowTaskStatusVo.getOption();


        if (nodeOperationType.equals(NodeOperationType.BACK) && ObjectNull.isNull(backNode)) {
            throw new BusinessException("退回节点不能为空");
        }
        FlowTaskDto flowTaskDto = new FlowTaskDto();
        flowTaskDto.setDataId(dataId);
        Map<String, Object> data = dataModelComponent.getMap(DataModelNameConstant.XZRW, dataId, null);
        data.remove("_id");
        flowTaskDto.setNodeId(node.getValue());

        flowTaskDto.setNodeOperationType(nodeOperationType.name());
        flowTaskDto.setOpinion(option);
        //流转到下一节点的审批人
        if (nodeOperationType.equals(NodeOperationType.PASS)) {

            CollaborativeTaskNode nextNodeByValue = CollaborativeTaskNode.getNextNodeByValue(node);

            if(CollaborativeTaskNode.OUTLINE_AUDIT.equals(nextNodeByValue)){
                data.put(FlowDesignNameConstant.OUTLINE_AUDITOR_IDS,collaborativeCreation.getOutlineAuditorIds());

                data.put(FlowDesignNameConstant.JVS_FlOW_TASK_Progress,nextNodeByValue.getFlowTaskProgress());
                data.put(FlowDesignNameConstant.JVS_FlOW_TASK_STATE,FlowTaskState.AUDIT.getName());

                data.put(FlowDesignNameConstant.FlOW_TASK_STATE, FlowTaskState.AUDIT.getName());
                data.put(FlowDesignNameConstant.FlOW_TASK_Progress,nextNodeByValue.getName());

                // 选择下一节点的审批人
                if (!CollectionUtils.isEmpty(userIds)) {
                    List<Map<String, Object>> approvers = assignApproversToNode(userIds, nextNodeByValue);
                    flowTaskDto.setApprovers(approvers);
                }
            }else {
                data.put(FlowDesignNameConstant.JVS_FlOW_TASK_Progress,nextNodeByValue.getFlowTaskProgress());
                data.put(FlowDesignNameConstant.JVS_FlOW_TASK_STATE,FlowTaskState.PASS.getName());

                data.put(FlowDesignNameConstant.FlOW_TASK_STATE, FlowTaskState.PASS.getName());
                data.put(FlowDesignNameConstant.FlOW_TASK_Progress,nextNodeByValue.getName());

            }
            //修改本地数据表的状态
            collaborativeCreation.setStatus(CollaborativeCreationStatus.valueOf(nextNodeByValue.name()));
        }
        if (nodeOperationType.equals(NodeOperationType.BACK)) {
            flowTaskDto.setBackNodeId(backNode.getValue());

            data.put(FlowDesignNameConstant.JVS_FlOW_TASK_Progress,backNode.getFlowTaskProgress());
            data.put(FlowDesignNameConstant.JVS_FlOW_TASK_STATE,FlowTaskState.AUDIT.getName());

            if(CollaborativeTaskNode.OUTLINE_AUDIT.equals(backNode)){

                data.put(FlowDesignNameConstant.JVS_FlOW_TASK_Progress,backNode.getFlowTaskProgress());
                data.put(FlowDesignNameConstant.JVS_FlOW_TASK_STATE,FlowTaskState.AUDIT.getName());

                data.put(FlowDesignNameConstant.FlOW_TASK_STATE, FlowTaskState.AUDIT.getName());
                data.put(FlowDesignNameConstant.FlOW_TASK_Progress,backNode.getName());
            }else {
                data.put(FlowDesignNameConstant.JVS_FlOW_TASK_Progress,backNode.getFlowTaskProgress());
                data.put(FlowDesignNameConstant.JVS_FlOW_TASK_STATE,FlowTaskState.PASS.getName());

                data.put(FlowDesignNameConstant.FlOW_TASK_STATE, FlowTaskState.PASS.getName());
                data.put(FlowDesignNameConstant.FlOW_TASK_Progress,backNode.getName());
            }

            if(CollaborativeTaskNode.NEW.equals(backNode)){
                data.put(FlowDesignNameConstant.JVS_FlOW_TASK_Progress,backNode.getFlowTaskProgress());
                data.put(FlowDesignNameConstant.JVS_FlOW_TASK_STATE,FlowTaskState.BACK.getName());

                data.put(FlowDesignNameConstant.FlOW_TASK_STATE, FlowTaskState.BACK.getName());
                data.put(FlowDesignNameConstant.FlOW_TASK_Progress,backNode.getName());
            }
            //修改本地数据表的状态
            collaborativeCreation.setStatus(CollaborativeCreationStatus.valueOf(backNode.name()));
        }
        flowTaskDto.setData(BeanCopyUtil.copy(data, JSONObject.class));
        saveOrUpdate(collaborativeCreation);
        return flowTaskDto;
    }

    @Override
    public CollaborativeCreationVo getByDataId(String dataId) {
        CollaborativeCreations collaborativeCreation = getOne(new LambdaQueryWrapper<CollaborativeCreations>().eq(CollaborativeCreations::getDataId, dataId));
        if (ObjectNull.isNull(collaborativeCreation)) {
            throw new BusinessException("当前id没有对应的协同创作数据");
        }

        Modules module = modulesService.getModuleByInstanceId(collaborativeCreation.getId());
        return get(module.getId());
    }
}
