package cn.bctools.custom.service.impl;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.custom.constant.BucketConstant;
import cn.bctools.custom.entity.data.*;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.enums.ModuleType;
import cn.bctools.custom.entity.vo.*;
import cn.bctools.custom.mapper.ExamineMapper;
import cn.bctools.custom.service.*;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.custom.util.PermissionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <p>
 * 考核 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ExamineServiceImpl extends ServiceImpl<ExamineMapper, Examine> implements ExamineService {
    @Autowired
    ModuleFilesService moduleFilesService;
    @Autowired
    ModulesService modulesService;
    @Autowired
    ModuleGroupsService moduleGroupsService;
    @Lazy
    @Autowired
    ExamineGradeTaskService examineGradeTaskService;

    @Autowired
    IndicatorsService indicatorsService;
    @Autowired
    ExamineGradeService examineGradeService;
    @Autowired
    public ExamineServiceImpl() {
    }

    @Override
    public Boolean saveExamine(ExamineVO examineVO) {
        if (!PermissionUtil.checkTrainingAdmin()) {
            throw new BusinessException("暂无权限");
        }
        Examine examine = new Examine();
        BeanUtils.copyProperties(examineVO, examine);
        if (ObjectUtils.isEmpty(examineVO.getId())) {
            EntityFillUtil.fillEntityFields(examine, EntityFillType.CREATE);
        } else {
            EntityFillUtil.fillEntityFields(examine, EntityFillType.UPDATE);
        }
        //是否开启文件提交0否1是
        Short enableFile = examineVO.getEnableFile();
        if(enableFile ==0){
            examine.setMaxFiles(0);
            examine.setMaxFileSize(0);
            examine.setAllowFileTypes("");
        }
        //是否开启在线文本0否1是
        Short enableOnlineText = examineVO.getEnableOnlineText();
        if(enableOnlineText ==0){
            examine.setWordsLimit(0);
        }
        if(enableFile ==0 && enableOnlineText ==0){
            throw new BusinessException("考核类型不能为空");
        }
        //评分类型0不评分1直接打分；2评分量表
        Short ratingType = examineVO.getRatingType();
        if(ratingType ==0){
            examine.setRatingWith("");
        }
        if(ratingType ==1){
            if(StrUtil.isEmpty(examineVO.getRatingWith())){
                throw new BusinessException("直接打分分数值不能为空");
            }
        }
        Examine oldExamine = getById(examineVO.getId());
        if(!ObjectUtils.isEmpty(oldExamine) &&oldExamine.getRatingType() != ratingType){
            //评分方式修改 清除原有的评分记录
            List<ExamineGrade> ExamineGradeList = examineGradeService.list(new LambdaQueryWrapper<ExamineGrade>().in(ExamineGrade::getExamineId, examineVO.getId()));
            if(!CollectionUtils.isEmpty(ExamineGradeList)){
                List<String> ExamineGradeIds = ExamineGradeList.stream().map(ExamineGrade::getId).collect(Collectors.toList());
                boolean ExamineGradeResult = examineGradeService.remove(new LambdaQueryWrapper<ExamineGrade>()
                        .in(ExamineGrade::getId,ExamineGradeIds));

                boolean fileResult = moduleFilesService.remove(new LambdaQueryWrapper<ModuleFiles>().in(ModuleFiles::getInfoId, ExamineGradeIds)
                        .eq(ModuleFiles::getRemarks,BucketConstant.MOD_EXAMINE_GRADE));
            }
            ExamineUsersService examineUsersService = SpringContextUtil.getBean(ExamineUsersService.class);
            List<ExamineUsers> examineUsersList = examineUsersService.list(new LambdaQueryWrapper<ExamineUsers>().in(ExamineUsers::getExamineId, examineVO.getId()));
            //清掉平均分
            if(!CollectionUtils.isEmpty(examineUsersList)){
                List<String> examineUserIds = examineUsersList.stream().map(ExamineUsers::getId).collect(Collectors.toList());
                UpdateWrapper<ExamineUsers> wrapper = new UpdateWrapper<>();
                wrapper.in("id",examineUserIds);
                wrapper.set("score",null);
                examineUsersService.update(wrapper);
            }

        }
        if(ratingType ==2){
            if(StrUtil.isEmpty(examineVO.getRatingWith())){
                throw new BusinessException("量表不能为空");
            }
            // 更新量表使用次数
            try {
                if (ObjectUtils.isEmpty(examineVO.getId())) {
                    indicatorsService.updateUsedCount(examineVO.getRatingWith(),1);
                } else {
                    if(!oldExamine.getRatingWith().equals(examineVO.getRatingWith())){
                        indicatorsService.updateUsedCount(examineVO.getRatingWith(),1);
                        indicatorsService.updateUsedCount(oldExamine.getRatingWith(),2);
                    }
                }
            }catch (Exception e){
                log.error("同步量表使用次数错误:{}",e);
            }

        }
        boolean ExamineResult = saveOrUpdate(examine);

        //协同研究工作室模块关联表
        Boolean visible = examineVO.getVisible();
        String courseId = examine.getCourseId();
        String moduleTypeName = ModuleType.examine.name();
        String id = examine.getId();


        Modules courseModule = modulesService.saveCourseModuleContact(courseId, moduleTypeName, id, visible, examineVO.getSection(), examineVO.getCanDownload());
        String groupIds = examineVO.getGroupIds();
        String moduleId = courseModule.getId();
        moduleGroupsService.saveOrUpdateCourseGroupContact(courseId, groupIds, moduleId);

        List<ModuleFiles> fileList = examineVO.getFileList();
        //附件信息

        moduleFilesService.saveOrUpdateFileContact(fileList, courseId, moduleId, BucketConstant.MOD_EXAMINE,"","");

        return ExamineResult;
    }


    @Override
    public Boolean updateExamine(ExamineVO ExamineVO) {
        if(StrUtil.isEmpty(ExamineVO.getId())){
            throw new BusinessException("考核id不能为空");
        }
        return this.saveExamine(ExamineVO);
    }

    @Override
    public ExamineVO getExamineById(String id) {
        Examine Examine = getById(id);
        ExamineVO ExamineVO = new ExamineVO();
        BeanUtils.copyProperties(Examine, ExamineVO);
        //活动附件
        Modules courseModule = modulesService.getOne(new LambdaQueryWrapper<Modules>().eq(Modules::getInstance, id).last("limit 1"));
        if (!ObjectUtils.isEmpty(courseModule)) {
            String courseId = courseModule.getCourseId();
            String cmId = courseModule.getId();
            Boolean visible = courseModule.getVisible();
            String section = courseModule.getSection();
            ExamineVO.setVisible(visible);
            ExamineVO.setSection(section);
            List<ModuleFiles> fileList = moduleFilesService.getFileList(cmId, courseId, BucketConstant.MOD_EXAMINE, "");
            if (!CollectionUtils.isEmpty(fileList)) {
                ExamineVO.setFileList(fileList);
            }
            ExamineVO.setCanDownload(courseModule.getCanDownload());
            if(PermissionUtil.checkTrainingAdmin()){
                //文件是否允许下载
                ExamineVO.setEnableDownload(true);
            }else {
                ExamineVO.setEnableDownload(courseModule.getCanDownload());
            }
        }
        //小组信息
        List<ModuleGroups> moduleGroupsList = moduleGroupsService.list(new LambdaQueryWrapper<ModuleGroups>().eq(ModuleGroups::getCourseId, Examine.getCourseId()
        ).eq(ModuleGroups::getCmId, courseModule.getId()));


        String groupIds = Optional.ofNullable(moduleGroupsList)
                .filter(list -> !list.isEmpty())
                .map(list -> list.stream()
                        .map(ModuleGroups::getGroupId)
                        .collect(Collectors.joining(",")))
                .orElse("");
        ExamineVO.setGroupIds(groupIds);
        return ExamineVO;
    }

    @Override
    public Boolean distributeScore(ExamineDistributeScoreVO examineDistributeScoreVO) {
        if (!PermissionUtil.checkTrainingAdmin()) {
            throw new BusinessException("暂无权限");
        }
        String examineId = examineDistributeScoreVO.getExamineId();
        Examine Examine = getById(examineId);
        if (ObjectUtils.isEmpty(Examine)) {
            throw new BusinessException("考核不存在");
        }
        //评分类型 0不评分1直接打分；2评分量表
        Short ratingType = Examine.getRatingType();
        if (ratingType == 0) {
            throw new BusinessException("考核不评分");
        }
        List<String> userIdList = examineDistributeScoreVO.getUserIdList();
        if (CollectionUtils.isEmpty(userIdList)) {
            throw new BusinessException("请选择评分专家");
        }
        //分配评分任务
        List<ExamineGradeTask> existingTasks = examineGradeTaskService.list(
                new LambdaQueryWrapper<ExamineGradeTask>().eq(ExamineGradeTask::getExamineId, examineId)
        );

        Map<String, ExamineGradeTask> existingTaskMap = existingTasks.stream()
                .collect(Collectors.toMap(ExamineGradeTask::getUserId, task -> task));

        List<ExamineGradeTask> ExamineGradeTasks = new ArrayList<>();
        Set<String> userIdSet = new HashSet<>(userIdList);

        for (String userId : userIdList) {
            ExamineGradeTask ExamineGradeTask = existingTaskMap.get(userId);
            if (ExamineGradeTask == null) {
                ExamineGradeTask = new ExamineGradeTask();
                ExamineGradeTask.setExamineId(examineId);
                ExamineGradeTask.setUserId(userId);
                EntityFillUtil.fillEntityFields(ExamineGradeTask, EntityFillType.CREATE);
            } else {
                EntityFillUtil.fillEntityFields(ExamineGradeTask, EntityFillType.UPDATE);
            }
            ExamineGradeTasks.add(ExamineGradeTask);
        }

        // 找出需要删除的任务
        List<ExamineGradeTask> tasksToDelete = existingTasks.stream()
                .filter(task -> !userIdSet.contains(task.getUserId()))
                .collect(Collectors.toList());

        if (!tasksToDelete.isEmpty()) {
            List<String> idsToDelete = tasksToDelete.stream()
                    .map(ExamineGradeTask::getId)
                    .collect(Collectors.toList());
            examineGradeTaskService.removeByIds(idsToDelete);
        }
        // 批量保存或更新任务
        if (!ExamineGradeTasks.isEmpty()) {
            return examineGradeTaskService.saveOrUpdateBatch(ExamineGradeTasks);
        }
        return true;
    }

    @Override
    public void exportCompletionZip(Page page, ExamineCompletionQueryVO examineCompletionQueryVO, String courseModuleId, HttpServletResponse response) throws IOException {
        CourseDynamicDataService courseDynamicDataService = SpringContextUtil.getBean(CourseDynamicDataService.class);
        page.setSize(-1);
        examineCompletionQueryVO.setCompletionStatus(true);
        Page completionList = courseDynamicDataService.completionList(page, examineCompletionQueryVO, courseModuleId);
        List records = completionList.getRecords();
        if(CollectionUtils.isEmpty(records)){
            return;
        }
        Modules module = modulesService.getModule(courseModuleId);
        if(ObjectUtils.isEmpty(module)){
            throw new BusinessException("模块不存在");
        }
        Examine examine = getById(module.getInstance());
        if(ObjectUtils.isEmpty(examine)){
            throw new BusinessException("考核不存在");
        }
        List<ExamineCompletionVO> examineCompletionVOList = BeanCopyUtil.copys(records, ExamineCompletionVO.class);
        exportZip(examine.getName(),examineCompletionVOList,response);
    }
    public void exportZip(String zipFileName, List<ExamineCompletionVO> examineCompletionVOList, HttpServletResponse response) throws IOException {

        String tempDirPath = System.getProperty("user.dir") + File.separator + "temp_download_dir";
        File tempDir = new File(tempDirPath);

        // 创建临时文件夹
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }

        String zipFile = URLEncoder.encode(zipFileName + "附件.zip", StandardCharsets.UTF_8.toString());
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment; filename=" + zipFile);
        try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
            for (ExamineCompletionVO examineCompletionVO : examineCompletionVOList) {
                // 创建文件夹名称：姓名（手机号）
                if(StrUtil.isEmpty(examineCompletionVO.getPhone())){
                    examineCompletionVO.setPhone("");
                }
                String folderName = examineCompletionVO.getRealName() + "（" + examineCompletionVO.getPhone() + "）" + "/";
                ZipEntry folderEntry = new ZipEntry(folderName);
                zipOut.putNextEntry(folderEntry);
                zipOut.closeEntry();
                ExamineUsersVO examineUsersVO = examineCompletionVO.getExamineUsersVO();
                if (!ObjectUtils.isEmpty(examineUsersVO)) {
                    List<ModuleFiles> submitFileList = examineUsersVO.getSubmitFileList();

                    if (!CollectionUtils.isEmpty(submitFileList)) {
                        for (ModuleFiles moduleFiles : submitFileList) {
                            String fileLink = moduleFiles.getFileLink();
                            if (!ObjectUtils.isEmpty(fileLink)) {
                                HttpUtil.downloadFile(fileLink,tempDirPath);
                                // 创建文件条目
                                String originalFileName = moduleFiles.getOriginalFileName();
                                String fileName = moduleFiles.getFileName().substring(moduleFiles.getFileName().lastIndexOf("/") + 1);
                                ZipEntry zipEntry = new ZipEntry(folderName + originalFileName);
                                zipOut.putNextEntry(zipEntry);

                                // 读取文件并写入压缩包
                                try (FileInputStream fis = new FileInputStream(tempDirPath+"/"+ fileName)) {
                                    byte[] buffer = new byte[1024];
                                    int len;
                                    while ((len = fis.read(buffer)) != -1) {
                                        zipOut.write(buffer, 0, len);
                                    }
                                }
                                zipOut.closeEntry();
                            }
                        }
                    }
                }
            }
        }finally {
            deleteDirectory(tempDir);
        }
    }
    private void deleteDirectory(File directoryToBeDeleted) {
        File[] allContents = directoryToBeDeleted.listFiles();
        if (allContents != null) {
            for (File file : allContents) {
                deleteDirectory(file);
            }
        }
        directoryToBeDeleted.delete();
    }
}
