package cn.bctools.custom.service.impl;

import cn.bctools.custom.constant.BucketConstant;
import cn.bctools.custom.entity.data.*;
import cn.bctools.custom.entity.vo.AssignUsersVO;
import cn.bctools.custom.entity.vo.IndicatorsVO;
import cn.bctools.custom.entity.vo.OnlineCourseGradeVO;
import cn.bctools.custom.mapper.OnlineCourseGradeMapper;
import cn.bctools.custom.service.ModuleFilesService;
import cn.bctools.custom.service.ModulesService;
import cn.bctools.custom.service.OnlineCourseGradeScaleService;
import cn.bctools.custom.service.OnlineCourseGradeService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 在线评课评分记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-29
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class OnlineCourseGradeServiceImpl extends ServiceImpl<OnlineCourseGradeMapper, OnlineCourseGrade> implements OnlineCourseGradeService {
    @Autowired
    ModuleFilesService moduleFilesService;
    @Autowired
    ModulesService modulesService;
    @Autowired
    OnlineCourseGradeScaleService onlineCourseGradeScaleService;

    @Override
    public OnlineCourseGradeVO getOnlineCourseGradeVo(OnlineCourseGrade onlineCourseGrade) {
        if (!ObjectUtils.isEmpty(onlineCourseGrade)) {
            OnlineCourseGradeVO onlineCourseGradeVO = new OnlineCourseGradeVO();
            BeanUtils.copyProperties(onlineCourseGrade, onlineCourseGradeVO);
            //是否有附件信息
            Optional.ofNullable(modulesService.getOne(new LambdaQueryWrapper<Modules>()
                            .eq(Modules::getInstance, onlineCourseGrade.getOnlineCourseId())
                            .last("limit 1")))
                    .ifPresent(courseModule -> {
                        List<ModuleFiles> fileList = moduleFilesService.getFileList(courseModule.getId(), courseModule.getCourseId(), BucketConstant.MOD_ONLINE_COURSE_GRADE, "", onlineCourseGrade.getId());
                        if (!CollectionUtils.isEmpty(fileList)) {
                            onlineCourseGradeVO.setGradeFileList(fileList);
                        }
                    });

            //评分类型0不评分1直接打分；2评分量表
            if (!ObjectUtils.isEmpty(onlineCourseGradeVO.getRatingType()) && onlineCourseGradeVO.getRatingType() == 2) {
                List<IndicatorsVO> indicatorsVOList = new ArrayList<>();

                List<OnlineCourseGradeScale> onlineCourseGradeScaleList = onlineCourseGradeScaleService.list(new LambdaQueryWrapper<OnlineCourseGradeScale>().eq(OnlineCourseGradeScale::getOnlineCourseId, onlineCourseGrade.getOnlineCourseId())
                        .eq(OnlineCourseGradeScale::getUserId, onlineCourseGrade.getUserId()));
                if (!CollectionUtils.isEmpty(onlineCourseGradeScaleList)) {
                    for (OnlineCourseGradeScale onlineCourseGradeScale : onlineCourseGradeScaleList) {
                        IndicatorsVO indicatorsVO = new IndicatorsVO();
                        indicatorsVO.setScaleOptionId(onlineCourseGradeScale.getScaleOptionId());
                        indicatorsVO.setScore(onlineCourseGradeScale.getScore());
                        indicatorsVOList.add(indicatorsVO);
                    }

                    onlineCourseGradeVO.setIndicatorsVOList(indicatorsVOList);
                }

                return onlineCourseGradeVO;
            }
            return onlineCourseGradeVO;
        }

        return null;
    }

    @Override
    public OnlineCourseGradeVO getOnlineCourseGradeVo(String onlineCourseId, String userId) {
        OnlineCourseGrade onlineCourseGrade = getOne(new LambdaQueryWrapper<OnlineCourseGrade>().eq(OnlineCourseGrade::getOnlineCourseId, onlineCourseId)
                .eq(OnlineCourseGrade::getUserId, userId).last("limit 1"));
        if (!ObjectUtils.isEmpty(onlineCourseGrade)) {
            OnlineCourseGradeVO onlineCourseGradeVO = new OnlineCourseGradeVO();
            BeanUtils.copyProperties(onlineCourseGrade, onlineCourseGradeVO);
            //是否有附件信息
            //学员提交作业文件
            Optional.ofNullable(modulesService.getOne(new LambdaQueryWrapper<Modules>()
                            .eq(Modules::getInstance, onlineCourseId)
                            .last("limit 1")))
                    .ifPresent(courseModule -> {
                        List<ModuleFiles> fileList = moduleFilesService.getFileList(courseModule.getId(), courseModule.getCourseId(), BucketConstant.MOD_ONLINE_COURSE_GRADE, "", onlineCourseGrade.getId());
                        if (!CollectionUtils.isEmpty(fileList)) {
                            onlineCourseGradeVO.setGradeFileList(fileList);
                        }
                    });

            //评分类型0不评分1直接打分；2评分量表
            if (!ObjectUtils.isEmpty(onlineCourseGradeVO.getRatingType()) && onlineCourseGradeVO.getRatingType() == 2) {
                List<IndicatorsVO> indicatorsVOList = new ArrayList<>();

                List<OnlineCourseGradeScale> onlineCourseGradeScaleList = onlineCourseGradeScaleService.list(new LambdaQueryWrapper<OnlineCourseGradeScale>().eq(OnlineCourseGradeScale::getOnlineCourseId, onlineCourseId)
                        .eq(OnlineCourseGradeScale::getUserId, userId));
                if (!CollectionUtils.isEmpty(onlineCourseGradeScaleList)) {
                    for (OnlineCourseGradeScale onlineCourseGradeScale : onlineCourseGradeScaleList) {
                        IndicatorsVO indicatorsVO = new IndicatorsVO();
                        indicatorsVO.setScaleOptionId(onlineCourseGradeScale.getScaleOptionId());
                        indicatorsVO.setScore(onlineCourseGradeScale.getScore());
                        indicatorsVOList.add(indicatorsVO);
                    }

                    onlineCourseGradeVO.setIndicatorsVOList(indicatorsVOList);
                }
            }
            return onlineCourseGradeVO;
        }
        return null;
    }

    @Override
    public IPage<Users> completionList(Page page, QueryWrapper<Users> wrapper, String instance) {
        return baseMapper.completionList(page,wrapper,instance);
    }

}
