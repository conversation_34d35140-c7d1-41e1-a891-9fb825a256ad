package cn.bctools.custom.service.impl;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.custom.entity.data.Modules;
import cn.bctools.custom.entity.data.Sections;
import cn.bctools.custom.entity.vo.ModuleSortVo;
import cn.bctools.custom.entity.vo.SectionSortVo;
import cn.bctools.custom.entity.vo.SectionsCreateVO;
import cn.bctools.custom.entity.vo.SectionsVO;
import cn.bctools.custom.mapper.SectionsMapper;
import cn.bctools.custom.service.ModulesService;
import cn.bctools.custom.service.SectionsService;
import cn.bctools.custom.util.PermissionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <p>
 * 协同研究工作室章节表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-02
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class SectionsServiceImpl extends ServiceImpl<SectionsMapper, Sections> implements SectionsService {

    @Autowired
    private ModulesService modulesService;

    @Override
    public List<Sections> getCourseSectionList(String courseId) {
        LambdaQueryWrapper<Sections> query = new LambdaQueryWrapper<Sections>().eq(Sections::getCourseId, courseId);
        if(!PermissionUtil.checkTrainingAdmin()){
            query.eq(Sections::getVisible,true);
        }
        query.orderByAsc(Sections::getSection);
        return list(query);
    }

    @Override
    public Boolean create(SectionsCreateVO sectionsCreateVO) {
        Integer num = sectionsCreateVO.getNum(); //章节数量
        String courseId = sectionsCreateVO.getCourseId();
        //新增章节记录
        // 获取当前课程的章节数
        List<Sections> currentSections = list(new LambdaQueryWrapper<Sections>().eq(Sections::getCourseId,courseId).or()
                .eq(Sections::getId,0));
        int currentSectionCount = CollectionUtils.isEmpty(currentSections) ? 0 : currentSections.size();

        // 生成新的章节名称
        List<Sections> newSections = IntStream.range(0, num)
                .mapToObj(i ->
                        createSection(courseId, currentSectionCount + i + 1))
                .collect(Collectors.toList());

        // 新增章节记录
        boolean result = saveOrUpdateBatch(newSections);
        return result;
    }

    @Override
    public Boolean update(SectionsVO sectionsVO) {
        if(StrUtil.isEmpty(sectionsVO.getId())){
            throw new BusinessException("环节id不能为空");
        }
//        if("0".equals(sectionsVO.getId())){
//            throw new BusinessException("默认章节不能允许修改");
//        }
        if (!PermissionUtil.checkTrainingAdmin()) {
            throw new BusinessException("暂无权限");
        }
        Sections sections = BeanCopyUtil.copy(sectionsVO, Sections.class);
        return updateById(sections);
    }

    private Sections createSection(String courseId, int sectionNumber) {
        Sections section = new Sections();
        section.setCourseId(courseId);
        section.setName("环节" + sectionNumber);
        section.setSection((long) (sectionNumber-1));
        return section;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(String id) {
        if(StrUtil.isEmpty(id)){
            throw new BusinessException("环节id不能为空");
        }
//        if("0".equals(id)){
//            throw new BusinessException("默认章节不能删除");
//        }
        if (!PermissionUtil.checkTrainingAdmin()) {
            throw new BusinessException("暂无权限");
        }
        Sections sections = getById(id);
        if(sections.getSection() ==0){
            throw new BusinessException("默认环节不能删除");
        }
        String courseId = sections.getCourseId();
        Long section = sections.getSection();
        List<Sections> courseSectionList = getCourseSectionList(courseId);
        //删除章节后 Modules回到上一章节
        if(!CollectionUtils.isEmpty(courseSectionList)){
            String preSectionId = "";
            for (Sections courseSection : courseSectionList) {
                if(courseSection.getSection().equals(section-1)){
                    preSectionId = courseSection.getId();
                }
            }

            List<Modules> sectionModuleList = modulesService.list(new LambdaQueryWrapper<Modules>().eq(Modules::getSection, id)
                    .eq(Modules::getCourseId,courseId));
            if(!CollectionUtils.isEmpty(sectionModuleList)){
                List<String> sectionModuleIds = sectionModuleList.stream().map(Modules::getId).collect(Collectors.toList());
                UpdateWrapper<Modules> wrapper = new UpdateWrapper<>();
                wrapper.in("id",sectionModuleIds);
                wrapper.set("section",preSectionId);
                modulesService.update(wrapper);
            }
        }
        boolean result = removeById(id);
        return result;
    }

    @Override
    public void sort(String courseId, List<SectionSortVo> list) {
        if (!PermissionUtil.checkTrainingAdmin()) {
            throw new BusinessException("暂无权限");
        }

//        boolean hasInvalidSection = list.stream()
//                .anyMatch(sectionSortVo ->  sectionSortVo.getSection() != 0);
//        if (hasInvalidSection) {
//            throw new BusinessException("默认章节不允许排序");
//        }
        if(list.get(0).getSection() !=0){
            throw new BusinessException("默认环节不允许排序");
        }
        for (int i = 1; i < list.size(); i++) {
            if (list.get(i).getSection() == 0) {
                throw new BusinessException("其他环节排序不能为0");
            }
        }

        List<Sections> sectionsList = BeanCopyUtil.copys(list, Sections.class);
        List<String> ids = list(new LambdaQueryWrapper<Sections>()
                .eq(Sections::getCourseId, courseId)
                .select(Sections::getId))
                .stream().map(Sections::getId).collect(Collectors.toList());
        List<Sections> updates = sectionsList.stream().filter(module -> ids.contains(module.getId())).collect(Collectors.toList());
        updateBatchById(updates);

//        List<ModuleSortVo> totalModuleSort = new ArrayList<>();
        List<Modules> saveOrUpdateModuleList = new ArrayList<>();
        for (SectionSortVo sectionSortVo : list) {
            String sectionId = sectionSortVo.getId();  //sectionId
            Long section = sectionSortVo.getSection();

            //活动重新关联章节
            List<ModuleSortVo> moduleSortlist = sectionSortVo.getList();
            if(!CollectionUtils.isEmpty(moduleSortlist)){
                for (ModuleSortVo moduleSortVo : moduleSortlist) {
                    String moduleId = moduleSortVo.getId();
                    Integer sort = moduleSortVo.getSort();
                    Modules modules = modulesService.getById(moduleId);
                    modules.setSort(sort);
                    modules.setSection(sectionId);
                    saveOrUpdateModuleList.add(modules);
                }
//                totalModuleSort.addAll(moduleSortlist);
            }
        }
        if(!CollectionUtils.isEmpty(saveOrUpdateModuleList)){
            modulesService.saveOrUpdateBatch(saveOrUpdateModuleList);
        }
//        modulesService.sort(courseId,totalModuleSort);
    }
}
