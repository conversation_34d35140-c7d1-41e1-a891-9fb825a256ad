package cn.bctools.custom.service.impl;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.entity.data.ModuleFiles;
import cn.bctools.custom.entity.data.Modules;
import cn.bctools.custom.entity.data.ThemeLecture;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.enums.ModuleType;
import cn.bctools.custom.entity.vo.ThemeLectureVo;
import cn.bctools.custom.mapper.ThemeLectureMapper;
import cn.bctools.custom.service.ModuleFilesService;
import cn.bctools.custom.service.ModuleGroupsService;
import cn.bctools.custom.service.ModulesService;
import cn.bctools.custom.service.ThemeLectureService;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.custom.util.PermissionUtil;
import cn.bctools.oss.template.OssTemplate;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static cn.bctools.custom.constant.BucketConstant.MOD_THEME_LECTURE;

/**
 * <p>
 * 主题讲座 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ThemeLectureServiceImpl extends ServiceImpl<ThemeLectureMapper, ThemeLecture> implements ThemeLectureService {

    @Autowired
    ModuleFilesService moduleFilesService;

    @Autowired
    ModulesService modulesService;

    @Autowired
    ModuleGroupsService moduleGroupsService;

    @Autowired
    OssTemplate ossTemplate;


    @Override
    public ThemeLectureVo getThemeLecture(String courseModuleId) {
        ThemeLectureVo themeLectureVo = new ThemeLectureVo();
        Modules module = modulesService.getById(courseModuleId);
        if (ObjectNull.isNull(module)) {
            return themeLectureVo;
        }
        ThemeLecture themeLecture = getById(module.getInstance());
        if (ObjectNull.isNull(themeLecture)) {
            return themeLectureVo;
        }

        themeLectureVo = BeanCopyUtil.copy(themeLecture, ThemeLectureVo.class);

        List<ModuleFiles> files = moduleFilesService.getFileList(courseModuleId, module.getCourseId(), MOD_THEME_LECTURE, "");
        themeLectureVo.setFileList(files);

        String groupIds = moduleGroupsService.getGroupIds(courseModuleId);
        themeLectureVo.setGroupIds(groupIds);

        themeLectureVo.setModule(ModuleType.theme_lecture.getName());
        themeLectureVo.setSection(module.getSection());
        themeLectureVo.setCanDownload(module.getCanDownload());
        if(PermissionUtil.checkTrainingAdmin()){
            //文件是否允许下载
            themeLectureVo.setEnableDownload(true);
        }else {
            themeLectureVo.setEnableDownload(module.getCanDownload());
        }
        return themeLectureVo;
    }

    @Override
    public Boolean create(ThemeLectureVo themeLectureVo) {
        checkEditPermission();
        ThemeLecture themeLecture = BeanCopyUtil.copy(themeLectureVo, ThemeLecture.class);
        EntityFillUtil.fillEntityFields(themeLecture, EntityFillType.CREATE);
        boolean flag = saveOrUpdate(themeLecture);
        String courseId = themeLecture.getCourseId();
        String moduleType = ModuleType.theme_lecture.getName();
        String openClassId = themeLecture.getId();
        Boolean visible = themeLectureVo.getVisible();
        Modules module = modulesService.saveCourseModuleContact(courseId, moduleType, openClassId, visible,themeLectureVo.getSection(), themeLectureVo.getCanDownload());
        moduleGroupsService.saveOrUpdateCourseGroupContact(courseId, themeLectureVo.getGroupIds(), module.getId());
        List<ModuleFiles> fileList = themeLectureVo.getFileList();
        //附件信息

        moduleFilesService.saveOrUpdateFileContact(fileList, courseId, module.getId(), MOD_THEME_LECTURE, "", "");

        return flag;
    }

    @Override
    public Boolean edit(ThemeLectureVo themeLectureVo) {
        ThemeLecture themeLecture = BeanCopyUtil.copy(themeLectureVo, ThemeLecture.class);
        EntityFillUtil.fillEntityFields(themeLecture, EntityFillType.UPDATE);
        checkEditPermission();
        boolean flag = saveOrUpdate(themeLecture);
        String courseId = themeLectureVo.getCourseId();
        String moduleType = ModuleType.theme_lecture.getName();
        String openClassId = themeLecture.getId();
        Boolean visible = themeLectureVo.getVisible();
        Modules module = modulesService.saveCourseModuleContact(courseId, moduleType, openClassId, visible,themeLectureVo.getSection(), themeLectureVo.getCanDownload());
        moduleGroupsService.saveOrUpdateCourseGroupContact(courseId, themeLectureVo.getGroupIds(), module.getId());
        List<ModuleFiles> fileList = themeLectureVo.getFileList();
        //附件信息

        moduleFilesService.saveOrUpdateFileContact(fileList, courseId, module.getId(), MOD_THEME_LECTURE, "", "");

        return flag;
    }

    @Override
    public Boolean checkEditPermission() {
        if (!PermissionUtil.checkTrainingAdmin()) {
            throw new BusinessException("暂无权限");
        }

        return true;
    }
}
