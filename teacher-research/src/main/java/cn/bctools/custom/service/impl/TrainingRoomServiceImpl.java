package cn.bctools.custom.service.impl;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.constant.BucketConstant;
import cn.bctools.custom.entity.data.ModuleFiles;
import cn.bctools.custom.entity.data.Modules;
import cn.bctools.custom.entity.data.TrainingRoom;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.enums.ModuleType;
import cn.bctools.custom.entity.vo.TrainingRoomVo;
import cn.bctools.custom.mapper.TrainingRoomMapper;
import cn.bctools.custom.service.ModuleFilesService;
import cn.bctools.custom.service.ModuleGroupsService;
import cn.bctools.custom.service.ModulesService;
import cn.bctools.custom.service.TrainingRoomService;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.custom.util.PermissionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 研修室表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class TrainingRoomServiceImpl extends ServiceImpl<TrainingRoomMapper, TrainingRoom> implements TrainingRoomService {

    @Autowired
    ModuleFilesService moduleFilesService;

    @Autowired
    ModulesService modulesService;
    @Autowired
    ModuleGroupsService moduleGroupsService;

    @Override
    public TrainingRoomVo getTrainingRoom(String courseModuleId) {
        TrainingRoomVo trainingRoomVo = new TrainingRoomVo();
        Modules module = modulesService.getById(courseModuleId);
        if (ObjectNull.isNull(module)) {
            return trainingRoomVo;
        }
        TrainingRoom trainingRoom = getById(module.getInstance());
        if (ObjectNull.isNull(trainingRoom)) {
            return trainingRoomVo;
        }

        trainingRoomVo = BeanCopyUtil.copy(trainingRoom, TrainingRoomVo.class);

        List<ModuleFiles> files = moduleFilesService.getFileList(courseModuleId, module.getCourseId(), BucketConstant.MOD_TRAINING_ROOM, "");
        trainingRoomVo.setFileList(files);

        String groupIds = moduleGroupsService.getGroupIds(courseModuleId);
        trainingRoomVo.setGroupIds(groupIds);

        trainingRoomVo.setModule(ModuleType.training_room.getName());
        trainingRoomVo.setSection(module.getSection());
        trainingRoomVo.setCanDownload(module.getCanDownload());
        if(PermissionUtil.checkTrainingAdmin()){
            //文件是否允许下载
            trainingRoomVo.setEnableDownload(true);
        }else {
            trainingRoomVo.setEnableDownload(module.getCanDownload());
        }
        return trainingRoomVo;
    }

    @Override
    public Boolean create(TrainingRoomVo trainingRoomVo) {
        TrainingRoom trainingRoom = BeanCopyUtil.copy(trainingRoomVo, TrainingRoom.class);
        EntityFillUtil.fillEntityFields(trainingRoom, EntityFillType.CREATE);
        boolean flag = saveOrUpdate(trainingRoom);
        String courseId = trainingRoom.getCourseId();
        String moduleType = ModuleType.training_room.getName();
        String seminarId = trainingRoom.getId();
        Boolean visible = trainingRoomVo.getVisible();
        Modules module = modulesService.saveCourseModuleContact(courseId, moduleType, seminarId, visible, trainingRoomVo.getSection(), trainingRoomVo.getCanDownload());
        moduleGroupsService.saveOrUpdateCourseGroupContact(courseId, trainingRoomVo.getGroupIds(), module.getId());
        List<ModuleFiles> fileList = trainingRoomVo.getFileList();
        //附件信息
        moduleFilesService.saveOrUpdateFileContact(fileList, courseId, module.getId(), BucketConstant.MOD_TRAINING_ROOM, "", "");
        return flag;
    }

    @Override
    public Boolean edit(TrainingRoomVo trainingRoomVo) {
        String id = trainingRoomVo.getId();
        if(StrUtil.isEmpty(id)){
            throw new BusinessException("研修室id不能为空");
        }
        checkEditPermission();
        TrainingRoom trainingRoom = BeanCopyUtil.copy(trainingRoomVo, TrainingRoom.class);
        EntityFillUtil.fillEntityFields(trainingRoom, EntityFillType.UPDATE);
        boolean flag = saveOrUpdate(trainingRoom);
        String courseId = trainingRoomVo.getCourseId();
        String moduleType = ModuleType.training_room.getName();
        String seminarId = trainingRoom.getId();
        Boolean visible = trainingRoomVo.getVisible();
        Modules module = modulesService.saveCourseModuleContact(courseId, moduleType, seminarId, visible, trainingRoomVo.getSection(), trainingRoomVo.getCanDownload());
        moduleGroupsService.saveOrUpdateCourseGroupContact(courseId, trainingRoomVo.getGroupIds(), module.getId());
        List<ModuleFiles> fileList = trainingRoomVo.getFileList();
        //附件信息
        moduleFilesService.saveOrUpdateFileContact(fileList, courseId, module.getId(), BucketConstant.MOD_TRAINING_ROOM, "", "");
        return flag;
    }

    @Override
    public Boolean checkEditPermission() {
        if (!PermissionUtil.checkTrainingAdmin()) {
            throw new BusinessException("暂无权限");
        }

        return true;
    }
}
