package cn.bctools.custom.service.impl;

import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.entity.data.UserGroups;
import cn.bctools.custom.mapper.UserGroupsMapper;
import cn.bctools.custom.service.UserGroupsService;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 协同研究工作室小组用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-02
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class UserGroupsServiceImpl extends ServiceImpl<UserGroupsMapper, UserGroups> implements UserGroupsService {
    @Override
    public String gerUserCourseGroup(String courseId) {
        UserGroups userGroup = getOne(new LambdaQueryWrapper<UserGroups>()
                .eq(UserGroups::getCourseId, courseId)
                .eq(UserGroups::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));
        if (ObjectNull.isNotNull(userGroup)) {
            return userGroup.getGroupId();
        }
        return null;
    }
}
