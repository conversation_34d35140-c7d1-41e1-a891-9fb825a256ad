package cn.bctools.custom.util;

import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.function.Get;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.vo.BaseDO;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 */
@Slf4j
public class EntityFillUtil {
    public static <T> T fillEntityFields(T entity, EntityFillType fillType) {
        try {
            UserDto user = UserCurrentUtils.getNullableUser();
            if (EntityFillType.CREATE.equals(fillType)) {
                Field createTimeField = getFieldByName(entity, Get.name(BaseDO::getCreateTime));
                if (createTimeField != null) {
                    createTimeField.setAccessible(true);
                    createTimeField.set(entity, LocalDateTimeUtil.now());
                }
                if (ObjectNull.isNotNull(user)) {
                    Field createByIdField = getFieldByName(entity, Get.name(BaseDO::getCreateById));
                    if (createByIdField != null) {
                        createByIdField.setAccessible(true);
                        String userId = user.getId();
                        createByIdField.set(entity, userId);
                    }

                    Field createByField = getFieldByName(entity, Get.name(BaseDO::getCreateBy));
                    if (createByField != null) {
                        createByField.setAccessible(true);
                        String userName = user.getRealName();
                        createByField.set(entity, userName);
                    }
                }

            }
            if (EntityFillType.UPDATE.equals(fillType)) {
                Field updateTimeField = getFieldByName(entity, Get.name(BaseDO::getUpdateTime));
                if (updateTimeField != null) {
                    updateTimeField.setAccessible(true);
                    updateTimeField.set(entity, LocalDateTimeUtil.now());
                }
                if (ObjectNull.isNotNull(user)) {
                    Field updateByIdField = getFieldByName(entity, Get.name(BaseDO::getUpdateById));
                    if (updateByIdField != null) {
                        updateByIdField.setAccessible(true);
                        String userId = user.getId();
                        updateByIdField.set(entity, userId);
                    }

                    Field updateByField = getFieldByName(entity, Get.name(BaseDO::getUpdateBy));
                    if (updateByField != null) {
                        updateByField.setAccessible(true);
                        String userName = user.getRealName();
                        updateByField.set(entity, userName);
                    }
                }
            }

        } catch (IllegalAccessException e) {
            log.error("数据填充异常:{}", JSONObject.toJSONString(entity));
        }

        return entity;
    }

    private static Field getFieldByName(Object entity, String fieldName) {
        try {
            return entity.getClass().getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            return null;
        }
    }
}
