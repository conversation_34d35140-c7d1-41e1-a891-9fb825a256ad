package cn.bctools.custom.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class PermissionConstant {
    //更改设置
    public static final String SETTING = "setting";

    //完成情况
    public static final String COMPLETION = "completion";

    //显示/隐藏
    public static final String VISIBLE = "visible";

    //复制
    public static final String COPY = "copy";

    //删除
    public static final String DELETE = "delete";

    //分配评分
    public static final String ASSIGN_RATINGS = "assignRatings";

    //评分
    public static final String SCORE = "score";

    //评分进度
    public static final String RATING_PROGRESS = "ratingProgress";

    //置顶
    public static final String TOP = "top";

    public static final List<String> DEFAULT_ADMIN_PERMISSIONS = new ArrayList<String>() {{
        add(SETTING);
        add(VISIBLE);
        add(COPY);
        add(DELETE);
    }};

    public static final List<String> DEFAULT_ALL_PERMISSIONS = new ArrayList<String>() {{
        add(SETTING);
        add(COMPLETION);
        add(VISIBLE);
        add(COPY);
        add(DELETE);
    }};

}
