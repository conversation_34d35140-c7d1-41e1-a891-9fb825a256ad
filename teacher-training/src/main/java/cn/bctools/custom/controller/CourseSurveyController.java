package cn.bctools.custom.controller;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.R;
import cn.bctools.custom.config.SurveyProperties;
import cn.bctools.custom.entity.data.Course;
import cn.bctools.custom.entity.data.CourseSurvey;
import cn.bctools.custom.entity.data.ModuleFiles;
import cn.bctools.custom.entity.vo.CourseSurveyVO;
import cn.bctools.custom.entity.vo.survey.SurveyApiViewVO;
import cn.bctools.custom.service.CourseService;
import cn.bctools.custom.service.CourseSurveyService;
import cn.bctools.custom.service.ModuleFilesService;
import cn.bctools.custom.survey.service.SurveyService;
import cn.bctools.custom.util.PermissionUtil;
import cn.bctools.log.annotation.Log;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.oss.template.OssTemplate;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.bctools.custom.constant.BucketConstant.MOD_EVALUATION_QRCODE;

/**
 * <AUTHOR>
 * @project jvs-backend
 * @date 2024/6/22
 * @description 共同体问卷管理接口
 * @package cn.bctools.custom.controller
 */
@Slf4j
@RestController
@RequestMapping("/course-survey")
public class CourseSurveyController {

    @Autowired
    CourseSurveyService courseSurveyService;
    @Autowired
    CourseService courseService;

    @Autowired
    SurveyProperties surveyProperties;
    @Autowired
    SurveyService surveyService;
    @Autowired
    ModuleFilesService moduleFilesService;

    @Autowired
    OssTemplate ossTemplate;

    @Log
    @ApiOperation(value = "共同体问卷管理", notes = "分页查询共同体问卷管理")
    @GetMapping("/page")
    public R<Page<CourseSurveyVO>> page(Page<CourseSurvey> page) {
        if(!PermissionUtil.checkAllAdmin()){
            throw new BusinessException("暂无权限");
        }
        Page<CourseSurveyVO> result = new Page<>();
        LambdaQueryWrapper<CourseSurvey> queryWrapper = new LambdaQueryWrapper<>();
        if(PermissionUtil.checkAllResearcher()){
            //查询本人所负责的共同体
            List<Course> courses = courseService.list(new LambdaQueryWrapper<Course>().eq(Course::getLeaderId, UserCurrentUtils.getUserId()));
            if(!CollectionUtils.isEmpty(courses)){
                List<String> courseIds = courses.stream().map(Course::getId).collect(Collectors.toList());
                queryWrapper.in(CourseSurvey::getCourseId,courseIds);
            }
        }
        Page<CourseSurvey> courseSurveyPage = courseSurveyService.page(page,queryWrapper);
        List<CourseSurveyVO> courseSurveyVOList = new ArrayList<>();
        for (CourseSurvey record : courseSurveyPage.getRecords()) {
            CourseSurveyVO courseSurveyVO = new CourseSurveyVO();
            BeanUtils.copyProperties(record,courseSurveyVO);
            String courseId = courseSurveyVO.getCourseId();
            Course course = courseService.getById(courseId);
            if(!ObjectUtils.isEmpty(course)){
                courseSurveyVO.setCourseName(course.getName());
            }
            courseSurveyVO.setClientId(surveyProperties.getClientId());
//            courseSurveyVO.setViewButton(String.format(surveyProperties.getHost()+surveyProperties.getViewButton(),record.getSurveyId()));
//            courseSurveyVO.setStatisticButton(String.format(surveyProperties.getHost()+surveyProperties.getStatisticButton(),record.getSurveyId()));
            try {
                JSONObject surveyJsonObject = surveyService.getSurveyById(courseSurveyVO.getCreateById(), courseSurveyVO.getSurveyId(),
                        surveyProperties.getClientId(),
                        surveyProperties.getClientSecret(), surveyProperties.getHost());
                if(!ObjectUtils.isEmpty(surveyJsonObject)){
                    if(surveyJsonObject.getInt("code") == 0){
                        JSONObject data = surveyJsonObject.getJSONObject("data");
                        SurveyApiViewVO surveyApiViewVO = data.toBean(SurveyApiViewVO.class);
                        courseSurveyVO.setSurveyName(surveyApiViewVO.getName());
                        courseSurveyVO.setSurveyFolder(surveyApiViewVO.getFolder());
                        courseSurveyVO.setSurveyUrl(surveyApiViewVO.getLinkUrl());
                        courseSurveyVO.setTotal(surveyApiViewVO.getTotal());
                        ModuleFiles moduleFiles = moduleFilesService.getOne(new LambdaQueryWrapper<ModuleFiles>().eq(ModuleFiles::getCmId, record.getCmId())
                                .eq(ModuleFiles::getCourseId, record.getCourseId()).eq(ModuleFiles::getRemarks, MOD_EVALUATION_QRCODE)
                                .last("limit 1"));
                        if(!ObjectUtils.isEmpty(moduleFiles)){
                            courseSurveyVO.setSurveyQrCode(ossTemplate.fileLink(moduleFiles.getFileName(), moduleFiles.getBucketName()));
                        }
                    }
                }


            }catch (Exception e){
                log.error("获取问卷信息异常:{}",e);
            }
            courseSurveyVOList.add(courseSurveyVO);
        }
        result.setRecords(courseSurveyVOList);
        result.setTotal(courseSurveyPage.getTotal());
        result.setSize(courseSurveyPage.getSize());
        result.setCurrent(courseSurveyPage.getCurrent());
        return R.ok(result);
    }
}
