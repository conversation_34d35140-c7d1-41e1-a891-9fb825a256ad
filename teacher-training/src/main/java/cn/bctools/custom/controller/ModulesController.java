package cn.bctools.custom.controller;


import cn.bctools.common.utils.R;
import cn.bctools.custom.entity.data.CourseModuleCompletion;
import cn.bctools.custom.entity.data.Groups;
import cn.bctools.custom.entity.data.Modules;
import cn.bctools.custom.entity.vo.*;
import cn.bctools.custom.service.*;
import cn.bctools.log.annotation.Log;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


/**
 * <p>
 * 共同体活动模块表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-02
 */
@Slf4j
@AllArgsConstructor
@RestController
@Api(value = "共同体模块管理", tags = "共同体模块管理接口")
@RequestMapping("/course/modules")
public class ModulesController {

    @Autowired
    ModulesService modulesService;

    @Autowired
    CourseModuleCompletionService moduleCompletionService;

    @Autowired
    CourseDynamicDataService courseDynamicDataService;

    @Autowired
    ModuleGroupsService moduleGroupsService;

    @PostMapping("/list/{courseId}")
    @Log
    @ApiOperation(value = "共同体模块列表", notes = "共同体模块列表")
    public R<CourseModuleRtnVo> list(@Validated @RequestBody CourseModuleListReqVo reqVo, @PathVariable("courseId") String courseId) {
        CourseModuleRtnVo data = courseDynamicDataService.getPage(reqVo, courseId);

        return R.ok(data);
    }

    @PostMapping("/section/list/{courseId}")
    @Log
    @ApiOperation(value = "共同体章节模块列表", notes = "共同体章节模块列表")
    public R<CourseModuleRtnVo> sectionList(@Validated @RequestBody CourseModuleListReqVo reqVo, @PathVariable("courseId") String courseId) {
        CourseModuleRtnVo data = courseDynamicDataService.getSectionPage(reqVo, courseId);

        return R.ok(data);
    }

    @PostMapping("/{courseModuleId}")
    @Log
    @ApiOperation(value = "模块详情", notes = "模块详情")
    public R<Object> getInfo(@PathVariable("courseModuleId") String courseModuleId) {
        Object o = courseDynamicDataService.getInfo(courseModuleId);

        return R.ok(o);
    }

    @DeleteMapping("/{courseModuleId}")
    @Log
    @ApiOperation(value = "删除模块", notes = "删除模块")
    public R<Boolean> delete(@PathVariable("courseModuleId") String courseModuleId) {
        courseDynamicDataService.delete(courseModuleId);

        return R.ok(true, "删除成功");
    }

    @PostMapping("/copy/{courseModuleId}")
    @Log
    @ApiOperation(value = "复制模块", notes = "复制模块")
    public R<Boolean> copy(@PathVariable("courseModuleId") String courseModuleId) {
        courseDynamicDataService.copy(courseModuleId);

        return R.ok(true, "复制成功");
    }


    @PostMapping("/visible/{courseModuleId}")
    @Log
    @ApiOperation(value = "设置模块显示情况", notes = "设置模块显示情况")
    public R<Boolean> visible(@PathVariable("courseModuleId") String courseModuleId, @RequestParam boolean visible) {
        boolean flag = modulesService.update(new LambdaUpdateWrapper<Modules>()
                .set(Modules::getVisible, visible)
                .eq(Modules::getId, courseModuleId));

        return R.ok(flag, "成功");
    }

    @PostMapping("/view/{courseModuleId}")
    @Log
    @ApiOperation(value = "查看模块调用-记录查看次数", notes = "查看模块调用-记录查看次数")
    public R<Boolean> view(@PathVariable("courseModuleId") String courseModuleId) {
        moduleCompletionService.view(courseModuleId);

        return R.ok();
    }

    @GetMapping("/groupSelectList/{courseModuleId}")
    @Log
    @ApiOperation(value = "查看模块完成情况小组下拉列表", notes = "查看模块完成情况小组下拉列表")
    public R<List<Groups>> groupSelectList(@PathVariable("courseModuleId") String courseModuleId) {
        Modules module = modulesService.getModule(courseModuleId);
        List<Groups> groups = moduleGroupsService.groupSelectList(courseModuleId, module.getCourseId());

        return R.ok(groups);
    }

    @PostMapping("/completionList/{courseModuleId}")
    @Log
    @ApiOperation(value = "模块完成情况列表", notes = "模块完成情况列表")
    public R<Page> completionList(Page page, @Validated @RequestBody Object completionListQueryVo, @PathVariable("courseModuleId") String courseModuleId) {
        Page dataPage = courseDynamicDataService.completionList(page, completionListQueryVo, courseModuleId);

        return R.ok(dataPage);
    }

    @PostMapping("/exportCompletion/{courseModuleId}")
    @Log
    @ApiOperation(value = "导出模块完成情况", notes = "导出模块完成情况")
    public R<Boolean> exportCompletion(Page page, @Validated @RequestBody Object completionListQueryVo, @PathVariable("courseModuleId") String courseModuleId, HttpServletResponse response) throws IOException {
        courseDynamicDataService.exportCompletion(page, completionListQueryVo, courseModuleId, response);

        return R.ok(true, "导出成功");
    }

    @PostMapping("/remark/{courseModuleId}")
    @Log
    @ApiOperation(value = "标记已完成", notes = "标记已完成")
    public R<Boolean> remarkCompletion(@PathVariable("courseModuleId") String courseModuleId, HttpServletResponse response) {
        courseDynamicDataService.remarkCompletion(courseModuleId);

        return R.ok(true, "标记成功");
    }

    @PostMapping("/sort/{courseId}")
    @Log
    @ApiOperation(value = "模块排序", notes = "模块排序")
    public R<Boolean> sort(@PathVariable("courseId") String courseId, @RequestBody List<ModuleSortVo> list) {
        modulesService.sort(courseId, list);
        return R.ok(true, "成功");
    }

    @PostMapping("/updateAccess")
    @Log
    @ApiOperation(value = "更新访问时长", notes = "更新访问时长")
    public R<Boolean> updateAccess(@RequestBody UpdateAccessVo updateAccessVo) {
        moduleCompletionService.updateAccess(updateAccessVo);
        return R.ok(true, "成功");
   }

    @PostMapping("/updateDownload/{cmId}")
    @Log
    @ApiOperation(value = "更新下载次数", notes = "更新下载次数")
    public R<Boolean> updateDownload(@PathVariable("cmId") String cmId) {
        moduleCompletionService.updateDownload(cmId);
        return R.ok(true, "成功");
   }
}
