package cn.bctools.custom.controller;


import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import cn.bctools.custom.entity.data.Notice;
import cn.bctools.custom.entity.vo.NoticeReqVo;
import cn.bctools.custom.entity.vo.NoticeSortVo;
import cn.bctools.custom.entity.vo.NoticeVO;
import cn.bctools.custom.service.NoticeService;
import cn.bctools.log.annotation.Log;
import cn.hutool.core.util.BooleanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 通知公告表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Api(value = "通知公告", tags = "通知公告接口")
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/notice")
public class NoticeController {

    NoticeService noticeService;


    @PostMapping("/list/{courseId}")
    @Log
    @ApiOperation(value = "通知公告列表", notes = "通知公告列表")
    public R<List<NoticeVO>> list(@ApiParam(value = "研修活动id/共同体id",required = true)@PathVariable("courseId") String courseId,
    @RequestBody NoticeReqVo noticeReqVo) {
        List<NoticeVO> noticeVOList = noticeService.listByCourseId(courseId,noticeReqVo);
        return R.ok(noticeVOList);
    }
    @GetMapping("/{id}")
    @Log
    @ApiOperation(value = "通知公告详情", notes = "")
    public R<NoticeVO> get(@ApiParam(value = "通知公告id",required = true)@PathVariable("id") String id) {
        NoticeVO noticeVO = noticeService.get(id);
        return R.ok(noticeVO);
    }


    @Log
    @PostMapping("/create")
    @ApiOperation(value ="新增通知公告",notes = "")
    public R<Boolean> create(@RequestBody @Validated NoticeVO noticeVO) {
        Boolean result = noticeService.create(noticeVO);
        return BooleanUtils.isTrue(result) ? R.ok(true,"新增成功") : R.failed(false,"新增失败");
    }


    @Log
    @PutMapping("/edit")
    @ApiOperation(value = "修改通知公告",notes = "")
    public R<Boolean> edit(@RequestBody @Validated NoticeVO noticeVO) {
        if(ObjectUtils.isEmpty(noticeVO.getId())){
            throw new BusinessException("通知公告id不能为空");
        }
        Boolean result = noticeService.edit(noticeVO);
        return BooleanUtils.isTrue(result) ? R.ok(true,"修改成功") : R.failed(false,"修改失败");
    }


    @DeleteMapping("/{id}")
    @Log
    @ApiOperation(value = "删除通知公告", notes = "删除通知公告")
    public R<Boolean> delete(@ApiParam(value = "通知公告id",required = true)@PathVariable("id") String id) {
        noticeService.delete(id);
        return R.ok(true, "删除成功");
    }

    @PostMapping("/copy/{id}")
    @Log
    @ApiOperation(value = "复制通知公告", notes = "复制通知公告")
    public R<Boolean> copy(@ApiParam(value = "通知公告id",required = true)@PathVariable("id") String id) {
        noticeService.copy(id);
        return R.ok(true, "复制成功");
    }


    @PostMapping("/visible/{id}")
    @Log
    @ApiOperation(value = "设置通知公告显示情况", notes = "设置通知公告显示情况")
    public R<Boolean> visible(@ApiParam(value = "通知公告id",required = true)@PathVariable("id") String id, @RequestParam boolean visible) {
        boolean flag = noticeService.update(new LambdaUpdateWrapper<Notice>()
                .set(Notice::getVisible, visible)
                .eq(Notice::getId, id));
        return R.ok(flag, "成功");
    }

    @PostMapping("/sort/{courseId}")
    @Log
    @ApiOperation(value = "通知公告排序", notes = "通知公告排序")
    public R<Boolean> sort(@ApiParam(value = "研修活动id/共同体id",required = true)@PathVariable("courseId") String courseId, @RequestBody List<NoticeSortVo> list) {
        noticeService.sort(courseId, list);
        return R.ok(true, "成功");
    }

    @PostMapping("/isTop/{id}")
    @Log
    @ApiOperation(value = "置顶/取消置顶", notes = "置顶/取消置顶")
    public R<Boolean> isTop(@ApiParam(value = "通知公告id",required = true)@PathVariable("id") String id, @RequestParam boolean isTop) {

        Notice notice = noticeService.getById(id);
        if(ObjectNull.isNull(notice)){
            throw new BusinessException("通知公告不存在");
        }
        if(BooleanUtil.isTrue(isTop)){

            List<Notice> noticeList = noticeService.list(new LambdaQueryWrapper<Notice>().eq(Notice::getIsTop, true)
                    .eq(Notice::getCourseId,notice.getCourseId()));
            if(ObjectNull.isNotNull(noticeList)){
                if(noticeList.size() >=3){
                    throw new BusinessException("当前置顶公告已到达上限，请先取消置顶公告再置顶");
                }
            }
        }
        boolean flag = noticeService.update(new LambdaUpdateWrapper<Notice>()
                .set(Notice::getIsTop, isTop)
                .eq(Notice::getId, id));
        return R.ok(flag, "成功");
    }
}
