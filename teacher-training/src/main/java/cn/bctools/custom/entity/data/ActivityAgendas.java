package cn.bctools.custom.entity.data;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

/**
 * <p>
 * 活动议程表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("activity_agendas")
@ApiModel(value="ActivityAgendas对象", description="活动议程表")
public class ActivityAgendas implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键，活动议程id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "共同体id")
    @NotEmpty(message = "研修活动id不能为空")
    @TableField("course_id")
    private String courseId;

    @ApiModelProperty(value = "活动安排")
    @TableField(value = "arrange",updateStrategy = FieldStrategy.IGNORED)
    private String arrange;

    @ApiModelProperty(value = "显隐：false隐藏，true显示")
    @TableField("visible")
    private Boolean visible;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "删除标识（0-正常,1-删除）")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;

    @ApiModelProperty(value = "租户id")
    @TableField("tenant_id")
    private String tenantId;

    @ApiModelProperty(value = "创建人")
    @TableField("create_by")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    @TableField("update_by")
    private String updateBy;

    @ApiModelProperty(value = "创建人")
    @TableField("create_by_id")
    private String createById;

    @ApiModelProperty(value = "更新人")
    @TableField("update_by_id")
    private String updateById;
    @ApiModelProperty(value = "是否允许下载附件 true是 false否")
    @TableField("can_download")
    private Boolean canDownload;

    @ApiModelProperty(value = "是否有下载权限 true是 false否")
    @TableField(exist = false)
    private Boolean enableDownload;

}
