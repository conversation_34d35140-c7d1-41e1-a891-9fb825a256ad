package cn.bctools.custom.entity.data;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 考核
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("examine")
@ApiModel(value="Examine对象", description="考核")
public class Examine implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "共同体id")
    @TableField("course_id")
    private String courseId;

    @ApiModelProperty(value = "名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "描述")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "通知")
    @TableField("notice")
    private String notice;

    @ApiModelProperty(value = "范围")
    @TableField("`range`")
    private String range;

    @ApiModelProperty(value = "开始时间")
    @TableField(value = "start_time",updateStrategy = FieldStrategy.IGNORED)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @TableField(value = "end_time",updateStrategy = FieldStrategy.IGNORED)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "是否开启在线文本0否1是")
    @TableField("enable_online_text")
    private Short enableOnlineText;

    @ApiModelProperty(value = "字数限制")
    @TableField("words_limit")
    private Integer wordsLimit;

    @ApiModelProperty(value = "是否开启文件提交0否1是")
    @TableField("enable_file")
    private Short enableFile;

    @ApiModelProperty(value = "最大文件限制")
    @TableField("max_files")
    private Integer maxFiles;

    @ApiModelProperty(value = "文件大小限制")
    @TableField("max_file_size")
    private Integer maxFileSize;

    @ApiModelProperty(value = "允许上传文件类型,传字符串类型,如 word,pdf,excel ")
    @TableField("allow_file_types")
    private String allowFileTypes;

    @ApiModelProperty(value = "评分类型0不评分1直接打分；2评分量表")
    @TableField("rating_type")
    private Short ratingType;

    @ApiModelProperty(value = "评分依据，rating_type=1时，为直接打分的数值；rating_type=2时，为评分量表id")
    @TableField("rating_with")
    private String ratingWith;

    @ApiModelProperty(value = "去掉最高最低分组数")
    @TableField("trimmed_score")
    private Short trimmedScore;

    @ApiModelProperty(value = "通过分数")
    @TableField("pass_score")
    private Integer passScore;

    @ApiModelProperty(value = "学员端是否能看到提交成绩：false隐藏，true显示")
    @TableField("grade_visible")
    private Boolean gradeVisible;

    @ApiModelProperty(value = "逻辑删除")
    @TableField("del_flag")
    @TableLogic
    private Boolean delFlag;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人")
    @TableField("create_by")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    @TableField("update_by")
    private String updateBy;

    @ApiModelProperty(value = "所属租户")
    @TableField("tenant_id")
    private String tenantId;

    @ApiModelProperty(value = "创建人")
    @TableField("create_by_id")
    private String createById;

    @ApiModelProperty(value = "更新人")
    @TableField("update_by_id")
    private String updateById;


}
