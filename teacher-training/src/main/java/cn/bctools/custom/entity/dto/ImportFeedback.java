package cn.bctools.custom.entity.dto;

import cn.bctools.oss.dto.FileNameDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ImportFeedback extends FileNameDto {

    @ApiModelProperty(value = "错误反馈列表")
    private List<String> errorMsgList;

    @ApiModelProperty(value = "导入成功数")
    private int succeedNum;

    @ApiModelProperty(value = "导入失败数")
    private int failedNum;

    private FileNameDto fileNameDto;
}
