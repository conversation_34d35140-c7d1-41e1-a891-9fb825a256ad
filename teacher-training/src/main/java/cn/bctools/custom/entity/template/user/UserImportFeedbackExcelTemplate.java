package cn.bctools.custom.entity.template.user;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *   共同体成员管理导入反馈模板
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/15
 */
@Data
@EqualsAndHashCode
@ExcelIgnoreUnannotated
public class UserImportFeedbackExcelTemplate {

    @ExcelIgnore
    private Integer rowIndex;

    @ExcelIgnore
    private int successNum;
    @ExcelIgnore
    private int failNum;

    @ExcelProperty(value = "*姓名")
    private String realName;
    @ExcelProperty(value = "*手机号")
    private String phone;
    @ExcelProperty(value = "学段学科")
    private String perAndSubs;
    @ExcelProperty(value = "单位")
    private String orgName;
    @ExcelProperty(value = "小组")
    private String groupName;
    @ExcelProperty(value = "角色")
    private String roleNames;;
    @ExcelProperty(value = "导入反馈")
    private String feedback;
}
