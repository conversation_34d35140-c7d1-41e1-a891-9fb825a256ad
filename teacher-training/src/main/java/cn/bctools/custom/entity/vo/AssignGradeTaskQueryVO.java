package cn.bctools.custom.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @project jvs-backend
 * @date 2024/6/9
 * @description
 * @package cn.bctools.custom.entity.vo
 */
@Accessors(chain = true)
@Data
@ApiModel(value = "评分列表查询条件Vo")
public class AssignGradeTaskQueryVO extends CompletionListQueryVo{

    @ApiModelProperty(value = "提交状态，0未提交1已提交2暂存")
    private Short submitStatus;

    @ApiModelProperty(value = "评分状态，0未提交1已提交2暂存")
    private Short gradeStatus;

    @ApiModelProperty(value = "共同体模块id")
    @NotEmpty(message = "研修活动模块id不能为空")
    private String courseModuleId;

    @ApiModelProperty(value = "作业id")
    @NotEmpty(message = "作业id不能为空")
    private String assignId;
    private String gradeBy;
    private List<String> userIds;

    @ApiModelProperty(value = "评分任务id数组，评分列表返回的assignUserId 导出可选")
    private List<String> assignUserIds;
}
