package cn.bctools.custom.entity.vo;

import cn.bctools.custom.entity.data.Assign;
import cn.bctools.custom.entity.data.ModuleFiles;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @project jvs-backend
 * @date 2024/6/5
 * @description
 * @package cn.bctools.custom.entity.vo
 */
@Data
@Accessors(chain = true)
@ApiModel("作业传输对象VO")
public class AssignVO extends Assign {

    @ApiModelProperty(value = "显隐：false隐藏，true显示")
    private Boolean visible;

    @ApiModelProperty(value = "小组id，0为所有小组，多选用 ,隔开")
    private String groupIds;

    @ApiModelProperty(value = "章节id")
    @NotEmpty(message = "环节不能为空")
    private String section;

    @ApiModelProperty(value = "活动附件列表")
    private List<ModuleFiles> fileList;

    @ApiModelProperty(value = "学员提交作业对象VO")
    private AssignUsersVO assignUsersVO;

    @ApiModelProperty(value = "模块类型")
    private String module;

    @ApiModelProperty(value = "是否有评分权限 true有 false 没有")
    private Boolean isGrade;

    @ApiModelProperty(value = "是否可以修改 true是 false 否")
    private Boolean canSubmit;

    @ApiModelProperty(value = "作业是否已评分 true是 false 否")
    private Boolean alreadyGrade =false;

    @ApiModelProperty(value = "作业是否存在评分 true是 false 否")
    private Boolean existGrade =false;

    @ApiModelProperty(value = "已分配评分专家的id集合")
    private List<String> expertIds;

    @ApiModelProperty(value = "是否允许下载附件 true是 false否")
    private Boolean canDownload;

    @ApiModelProperty(value = "是否有下载权限 true是 false否")
    private Boolean enableDownload;
}
