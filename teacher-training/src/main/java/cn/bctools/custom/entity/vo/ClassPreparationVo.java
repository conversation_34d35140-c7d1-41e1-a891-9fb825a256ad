package cn.bctools.custom.entity.vo;

import cn.bctools.custom.entity.data.CourseModuleOnlineDocuments;
import cn.bctools.custom.entity.data.ModuleFiles;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-09
 */
@Accessors(chain = true)
@Data
public class ClassPreparationVo {

    @ApiModelProperty(value = "集体备课 instanceId")
    private String id;

    @ApiModelProperty(value = "章节id")
    @NotEmpty(message = "环节id不能为空")
    private String section;

    @ApiModelProperty(value = "显隐：false隐藏，true显示")
    @NotNull(message = "显隐不能为空")
    private Boolean visible;

    @ApiModelProperty(value = "附件，不用该字段")
    List<ModuleFiles> fileList;

    @ApiModelProperty(value = "共同体id")
    @NotEmpty(message = "研修活动id不能为空")
    private String courseId;

    @ApiModelProperty(value = "标题")
    @NotEmpty(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "任务详情")
    private String description;

    @ApiModelProperty(value = "小组id，0为所有小组，多选用 ,隔开")
    @NotEmpty(message = "小组不能为空")
    private String groupIds;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "模块类型")
    private String module;

    @ApiModelProperty(value = "创建人id")
    private String createById;

    @ApiModelProperty(value = "学段id")
    private String periodId;

    @ApiModelProperty(value = "学科id，基于所选学段")
    private String subjectId;

    @ApiModelProperty(value = "年级，基于所选学段")
    private String grade;

    @ApiModelProperty(value = "教材版本id，基于所选学段")
    private String textbookVersionId;

    @ApiModelProperty(value = "教材id，基于所选学段")
    private String textbookId;

    @ApiModelProperty(value = "教材目录id，基于所选学段")
    private String textbookCatalogueId;

//    @ApiModelProperty(value = "参与人员 user_id，只能是共同体成员，0为所有人员，多选用英文逗号隔开")
//    @NotEmpty(message = "参与人员不能为空")
//    private String userIds;

    @ApiModelProperty(value = "共案，资源中心的文件对象列表，新建文档接口拿到的 id 要重命名为 fileId")
    @NotNull(message = "资源中心的文件对象列表不能为空")
    private List<CourseModuleOnlineDocuments> onlineDocuments;

    @ApiModelProperty(value = "学员是否可以提交")
    private Boolean canSubmit;

    @ApiModelProperty(value = "是否允许下载附件 true是 false否")
    private Boolean canDownload;
    @ApiModelProperty(value = "是否有下载权限 true是 false否")
    private Boolean enableDownload;

}
