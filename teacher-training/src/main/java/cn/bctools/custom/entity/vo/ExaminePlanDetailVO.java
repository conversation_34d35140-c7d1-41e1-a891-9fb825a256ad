package cn.bctools.custom.entity.vo;

import cn.bctools.custom.entity.data.ExamineGrade;
import cn.bctools.custom.entity.data.ModuleFiles;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @project jvs-backend
 * @date 2024/6/10
 * @description 考核评分进度详情对象VO
 * @package cn.bctools.custom.entity.vo
 */
@Accessors(chain = true)
@Data
@ApiModel(value = "考核评分进度详情对象VO")
public class ExaminePlanDetailVO extends ExamineGrade {


    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "量表id")
    private String indicatorsId;
    @ApiModelProperty(value = "量表分数对象集合")
    private List<IndicatorsVO> indicatorsVOList;

    @ApiModelProperty(value = "评分附件列表")
    private List<ModuleFiles> gradeFileList;
}
