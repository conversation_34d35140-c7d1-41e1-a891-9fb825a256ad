package cn.bctools.custom.entity.vo;

import cn.bctools.custom.entity.data.ModuleFiles;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;


@Accessors(chain = true)
@Data
public class OnlineCourseEvaluationVo {

    private String id;

    @ApiModelProperty(value = "章节id")
    @NotEmpty(message = "环节不能为空")
    private String section;

    @ApiModelProperty(value = "小组id，0为所有小组，多选用 ,隔开")
    @NotEmpty(message = "小组不能为空")
    private String groupIds;

    @ApiModelProperty(value = "显隐：false隐藏，true显示")
    @NotNull(message = "显隐不能为空")
    private Boolean visible;

    @ApiModelProperty(value = "文件资源")
    List<ModuleFiles> fileList;

    @ApiModelProperty(value = "共同体id")
    @NotEmpty(message = "研修活动id不能为空")
    private String courseId;

    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "在线评课链接,多个用,拼接")
    private String url;
    @ApiModelProperty(value = "评分类型0不评分1直接打分；2评分量表")
    @NotNull(message = "评分类型不能为空")
    private Short ratingType;

    @ApiModelProperty(value = "评分依据，rating_type=1时，为直接打分的数值；rating_type=2时，为评分量表id")
    private String ratingWith;

    @ApiModelProperty(value = "去掉最高最低分组数")
    private Short trimmedScore;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "模块类型")
    private String module;

    @ApiModelProperty(value = "是否可以评分 true是 false 否")
    private Boolean canGrade;

    @ApiModelProperty(value = "创建人id")
    private String createById;

    @ApiModelProperty(value = "评分信息对象")
    private OnlineCourseGradeVO onlineCourseGradeVO;


    @ApiModelProperty(value = "在线评课二维码")
    private String qrCode;

    @ApiModelProperty(value = "在线评课二维码地址")
    private String qrCodeUrl;

    @ApiModelProperty(value = "评课方式 0评课码 1公开评课")
    @NotNull(message = "评课方式不能为空")
    private Short evaluationType;

    @ApiModelProperty(value = "评课依据，evaluationType=0时，为评课码")
    private String evaluationWith;
    @ApiModelProperty(value = "是否需要输入评课码 true是 false否")
    private Boolean enableCode;
    @ApiModelProperty(value = "在线评课是否存在评分 true是 false 否")
    private Boolean existGrade =false;

    @ApiModelProperty(value = "是否允许下载附件 true是 false否")
    private Boolean canDownload;
    @ApiModelProperty(value = "是否有下载权限 true是 false否")
    private Boolean enableDownload;

}
