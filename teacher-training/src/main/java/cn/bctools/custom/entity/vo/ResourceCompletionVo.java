package cn.bctools.custom.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
@ApiModel(value = "资源完成情况Vo")
public class ResourceCompletionVo {

    @ApiModelProperty(value = "用户id")
    private String id;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "学段学科")
    private String periodAndSubjects;

    @ApiModelProperty(value = "所属单位")
    private String deptName;

    @ApiModelProperty(value = "最后浏览时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastViewTime;

    @ApiModelProperty(value = "播放次数")
    private Long accessCount;

    @ApiModelProperty(value = "播放时长显示")
    private String durationTxt;

}
