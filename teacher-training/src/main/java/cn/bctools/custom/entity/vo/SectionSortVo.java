package cn.bctools.custom.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Data
@ApiModel(value = "章节排序Vo", description = "章节排序Vo")
public class SectionSortVo {
    @ApiModelProperty(value = "章节id（sectionId）")
    private String id;

    @ApiModelProperty(value = "排序")
    private Long section;

    @ApiModelProperty(value = "模块排序集合")
    private List<ModuleSortVo> list;

    private List<SectionSortVo> children;
}
