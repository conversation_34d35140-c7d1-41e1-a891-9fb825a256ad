package cn.bctools.custom.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @project jvs-backend
 * @date 2024/7/1
 * @description
 * @package cn.bctools.custom.entity.vo
 */
@Data
@Accessors(chain = true)
@ApiModel("共同体子章节创建对象VO")
public class SectionsCreateChildVO {

    @ApiModelProperty(value = "章节数量")
    @NotNull(message = "环节数量不能为空")
    private Integer num;

    @ApiModelProperty(value = "父章节id")
    @NotNull(message = "父章节id不能为空")
    private String parentId;
    @ApiModelProperty(value = "共同体id")
    @NotEmpty(message = "研修活动id不能为空")
    private String courseId;

}
