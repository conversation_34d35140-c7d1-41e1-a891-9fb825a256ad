package cn.bctools.custom.entity.vo.completion;

import cn.bctools.custom.entity.data.UserSubjects;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *   公共完成情况
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Accessors(chain = true)
@Data
public class Completion {

    @ApiModelProperty(value = "共同体id")
    private String courseId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "学科学段")
    private List<UserSubjects> perAndSubList;

    @ApiModelProperty(value = "学科学段str")
    private String periodAndSubjects;

    @ApiModelProperty(value = "学段名称")
    @TableField("period_id")
    private String periodName;

    @ApiModelProperty(value = "学科名称")
    @TableField("subject_id")
    private String subjectName;

    @ApiModelProperty(value = "所属单位id")
    private String orgCode;

    @ApiModelProperty(value = "所属单位")
    private String deptName;

    @ApiModelProperty(value = "所属小组id")
    private String groupId;

    @ApiModelProperty(value = "最后浏览时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastViewTime;

}
