package cn.bctools.custom.entity.vo.stat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("签到统计")
public class CourseAttendanceStatVO {


    @ApiModelProperty(value = "课程活动id")
    private String cmId;

    @ApiModelProperty(value = "活动id")
    private String instance;

    @ApiModelProperty(value = "签到名称")
    private String name;

    @ApiModelProperty(value = "签到时间")
    private String sessions;

    @ApiModelProperty(value = "签到场次")
    private Integer sessionNum;

    @ApiModelProperty(value = "签到人数")
    private Integer attendanceNum;

    @ApiModelProperty(value = "活动完成情况")
    private Integer completionNum;

}
