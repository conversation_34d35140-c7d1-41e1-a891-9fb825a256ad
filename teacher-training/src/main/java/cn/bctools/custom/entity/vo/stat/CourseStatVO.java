package cn.bctools.custom.entity.vo.stat;

import cn.bctools.custom.entity.vo.CourseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("共同体数据统计")
public class CourseStatVO {

    @ApiModelProperty(value = "课程信息")
    CourseVo course;

    @ApiModelProperty(value = "基本情况")
    CourseBaseStatVO base;

    @ApiModelProperty(value = "资源统计")
    List<CourseResourceStatVO> resources;

    @ApiModelProperty(value = "作业统计")
    List<CourseAssignStatVO> assigns;

    @ApiModelProperty(value = "签到统计")
    List<CourseAttendanceStatVO> attendances;

    @ApiModelProperty(value = "公开课统计")
    List<CourseActivityStatVO> openClass;

    @ApiModelProperty(value = "在线听课统计")
    List<CourseActivityStatVO> onlineClass;

    @ApiModelProperty(value = "评价统计")
    List<CourseActivityStatVO> evaluate;
}
