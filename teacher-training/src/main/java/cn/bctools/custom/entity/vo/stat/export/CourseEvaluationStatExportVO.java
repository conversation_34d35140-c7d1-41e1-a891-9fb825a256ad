package cn.bctools.custom.entity.vo.stat.export;

import cn.bctools.custom.entity.vo.stat.CourseActivityStatVO;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
public class CourseEvaluationStatExportVO {

    @ExcelProperty(value = "评价名称", index = 0)
    private String name;

    @ExcelProperty(value = "访问人数", index = 1)
    private Integer viewUsers;

    @ExcelProperty(value = "访问量", index = 2)
    private Integer viewNum;

    @ExcelProperty(value = "活动完成情况", index = 3)
    private Integer completionNum;

    public static List<Object> build(List<CourseActivityStatVO> activities) {
        List<Object> list = new ArrayList<>();
        activities.forEach(activity -> {
           CourseEvaluationStatExportVO exportVO = CourseEvaluationStatExportVO.builder()
                   .name(activity.getName())
                   .viewUsers(activity.getViewUsers())
                   .viewNum(activity.getViewNum())
                   .completionNum(activity.getCompletionNum())
                   .build();
           list.add(exportVO);
        });
        return list;
    }
}
