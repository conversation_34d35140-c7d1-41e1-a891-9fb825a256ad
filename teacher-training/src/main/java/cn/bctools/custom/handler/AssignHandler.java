package cn.bctools.custom.handler;

import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.auth.api.dto.SearchUserDto;
import cn.bctools.auth.api.enums.UserQueryType;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.annotation.ModuleField;
import cn.bctools.custom.component.CompletionComponent;
import cn.bctools.custom.component.CourseUserRoleComponent;
import cn.bctools.custom.constant.PermissionConstant;
import cn.bctools.custom.constant.RoleConstant;
import cn.bctools.custom.entity.data.*;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.enums.ModuleType;
import cn.bctools.custom.entity.vo.*;
import cn.bctools.custom.service.*;
import cn.bctools.custom.util.CommonUtil;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.custom.util.PermissionUtil;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@ModuleField(value = "assign")
@Slf4j
public class AssignHandler implements ModuleHandler{

    @Autowired
    AttendancesService attendancesService;

    @Autowired
    AttendanceSessionsService attendanceSessionsService;

    @Autowired
    ModuleGroupsService moduleGroupsService;

    @Autowired
    ModulesService modulesService;

    @Autowired
    AssignService assignService;
    @Autowired
    ModuleFilesService moduleFilesService;
    @Autowired
    CourseModuleCompletionService completionService;
    @Autowired
    private AssignUsersService assignUsersService;

    @Autowired
    UserGroupsService userGroupsService;

    @Autowired
    CompletionComponent completionComponent;

    @Autowired
    AuthUserServiceApi authUserServiceApi;
    @Autowired
    UserSubjectsService userSubjectsService;
    @Autowired
    AssignGradeTaskService assignGradeTaskService;

    @Autowired
    CourseUserRoleComponent roleComponent;
    @Autowired
    AssignGradeService assignGradeService;
    @Autowired
    UsersService usersService;

    String USER = "u";
    String ASSIGN_USER = "au";

    @Override
    public Object getInfo(String courseModuleId, String instanceId) {
        AssignVO assignVO = assignService.getAssignById(instanceId);
        assignVO.setModule(ModuleType.assign.name());
        //是否有评分权限
//        AssignGradeTask assignGradeTask = assignGradeTaskService.getOne(new LambdaQueryWrapper<AssignGradeTask>().eq(AssignGradeTask::getAssignId, instanceId)
//                .eq(AssignGradeTask::getUserId, UserCurrentUtils.getUserId()).last("limit 1"));
//        if(ObjectUtils.isEmpty(assignGradeTask)){
//            assignVO.setIsGrade(false);
//        }else {
//            assignVO.setIsGrade(true);
//        }
//
//        if (PermissionUtil.checkTrainingAdmin()) {
//            assignVO.setIsGrade(true);
//        }
        //作业已分配评分专家
        List<AssignGradeTask> assignGradeTaskList = assignGradeTaskService.list(new LambdaQueryWrapper<AssignGradeTask>().eq(AssignGradeTask::getAssignId,instanceId));
        if(!CollectionUtils.isEmpty(assignGradeTaskList)){
            List<String> expertIds = assignGradeTaskList.stream().map(AssignGradeTask::getUserId).collect(Collectors.toList());
            assignVO.setExpertIds(expertIds);
        }

        Boolean canSubmit = true;
        AssignUsersVO assignUserVO = assignUsersService.getAssignUserVO(instanceId,UserCurrentUtils.getUserId());
        if(!ObjectUtils.isEmpty(assignUserVO)){
            assignVO.setAssignUsersVO(assignUserVO);
            long gradeCount = assignGradeService.count(new LambdaQueryWrapper<AssignGrade>().eq(AssignGrade::getAssignId, instanceId)
                    .eq(AssignGrade::getUserId, UserCurrentUtils.getUserId()).eq(AssignGrade::getStatus, 1).last("limit 1"));
            if(gradeCount >0){
                canSubmit=false;
                assignVO.setAlreadyGrade(true);
            }
        }
        //是否存在评分
        long existGrade = assignGradeService.count(new LambdaQueryWrapper<AssignGrade>().eq(AssignGrade::getAssignId, instanceId)
                .eq(AssignGrade::getStatus, 1).last("limit 1"));
        if(existGrade >0){
            assignVO.setExistGrade(true);
        }
        //是否可以修改提交作业
        //判断是否在 时间范围内 当前时间不在startTime 和endTime 之前的 不能提交作业
        if(!ObjectUtils.isEmpty(assignVO.getStartTime()) && !ObjectUtils.isEmpty(assignVO.getEndTime())){
            LocalDate startDate = assignVO.getStartTime().toLocalDate();
            LocalDate endDate = assignVO.getEndTime().toLocalDate();
            LocalDate today = LocalDate.now();
            if (today.isBefore(startDate) || today.isAfter(endDate)) {
                canSubmit=false;
            }
        }
        assignVO.setCanSubmit(canSubmit);
        return assignVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String courseModuleId, String instanceId) {
        if (!PermissionUtil.checkTrainingAdmin()) {
            throw new BusinessException("暂无权限");
        }
        //删除资源表
        assignService.removeById(instanceId);
        //删除文件表
        moduleFilesService.remove(new LambdaQueryWrapper<ModuleFiles>().eq(ModuleFiles::getCmId, courseModuleId));
        //删除小组关联表
        moduleGroupsService.remove(new LambdaQueryWrapper<ModuleGroups>().eq(ModuleGroups::getCmId, courseModuleId));
        //删除模组表
        modulesService.removeById(courseModuleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copy(String courseModuleId, String instanceId) {
        if (!PermissionUtil.checkTrainingAdmin()) {
            throw new BusinessException("暂无权限");
        }
        //复制资源表
        Assign assign = assignService.getById(instanceId);
        Assign copyAssign = BeanCopyUtil.copy(assign, Assign.class);
        copyAssign.setId(null);
        // 获取当前最大编号
        String baseName = assign.getName();
        List<String> nameList = assignService.list(new LambdaQueryWrapper<Assign>()
                        .select(Assign::getName)
                        .likeRight(Assign::getName, baseName + "("))
                .stream()
                .map(Assign::getName)
                .collect(Collectors.toList());

        String copyName = CommonUtil.getCopyName(baseName, nameList);
        copyAssign.setName(copyName);
        EntityFillUtil.fillEntityFields(copyAssign, EntityFillType.CREATE);
        EntityFillUtil.fillEntityFields(copyAssign, EntityFillType.UPDATE);
        assignService.save(copyAssign);

        //复制模组表
        Modules module = modulesService.getModule(courseModuleId);
        Modules copyModule = BeanCopyUtil.copy(module, Modules.class);
        copyModule.setId(null);
        copyModule.setInstance(copyAssign.getId());
        EntityFillUtil.fillEntityFields(copyModule, EntityFillType.CREATE);
        EntityFillUtil.fillEntityFields(copyModule, EntityFillType.UPDATE);
        modulesService.save(copyModule);

        //复制文件表
        List<ModuleFiles> fileList = moduleFilesService.list(new LambdaQueryWrapper<ModuleFiles>()
                .eq(ModuleFiles::getCmId, courseModuleId)
                .eq(ModuleFiles::getCourseId, module.getCourseId()));
        List<ModuleFiles> copyFileList = fileList.stream().map(e -> {
            ModuleFiles copyFile = BeanCopyUtil.copy(e, ModuleFiles.class);
            copyFile.setId(null);
            copyFile.setCmId(copyModule.getId());
            EntityFillUtil.fillEntityFields(copyFile, EntityFillType.CREATE);
            EntityFillUtil.fillEntityFields(copyFile, EntityFillType.UPDATE);
            return copyFile;
        }).collect(Collectors.toList());
        moduleFilesService.saveBatch(copyFileList);

        //复制小组关联表
        List<ModuleGroups> groupList = moduleGroupsService.list((new LambdaQueryWrapper<ModuleGroups>()
                .eq(ModuleGroups::getCmId, courseModuleId)
                .eq(ModuleGroups::getCourseId, module.getCourseId())));
        List<ModuleGroups> copyGroupList = groupList.stream().map(e -> {
            ModuleGroups copyGroup = BeanCopyUtil.copy(e, ModuleGroups.class);
            copyGroup.setId(null);
            copyGroup.setCmId(copyModule.getId());
            EntityFillUtil.fillEntityFields(copyGroup, EntityFillType.CREATE);
            EntityFillUtil.fillEntityFields(copyGroup, EntityFillType.UPDATE);
            return copyGroup;
        }).collect(Collectors.toList());
        moduleGroupsService.saveBatch(copyGroupList);
    }

    @Override
    public Page completionList(Page page, Object completionListQueryVo, String courseModuleId) {
        Modules module = modulesService.getModule(courseModuleId);
        if(roleComponent.checkOnlyExpert(module.getCourseId()) || roleComponent.checkOnlyStudent(module.getCourseId())){
            throw new BusinessException("暂无权限");
        }
        Page<AssignCompletionVO> completionPage = new Page<>(page.getCurrent(), page.getSize(), 0);

        AssignCompletionQueryVO queryVo = BeanCopyUtil.copy(completionListQueryVo, AssignCompletionQueryVO.class);

        List<String> userIds = completionComponent.getUserIdsByCompletionListQueryVo(queryVo, courseModuleId);
        if (ObjectNull.isNull(userIds)) {
            return completionPage;
        }
        /*
         1、传输userIds和查询条件，远程调用获取符合条件用户ids
         2、completionService根据userIds进行分页查询
         3、根据userIds获取用户姓名、所属单位中文信息，转换成map
         4、根据map填充信息
        */
        SearchUserDto searchUserDto = new SearchUserDto().setUserIds(userIds).setType(UserQueryType.and);
        if (ObjectNull.isNotNull(queryVo.getKeyword())) {
            searchUserDto.setRealName(queryVo.getKeyword());
        }
        List<UserDto> userDtos = authUserServiceApi.searchUserGetInfo(searchUserDto).getData();
        if (ObjectNull.isNull(userDtos)) {
            return completionPage;
        }
        //应该是用UserDtos 的userIds??

        Map<String, UserDto> userDtoMap = userDtos.stream()
                .collect(Collectors.toMap(UserDto::getId, Function.identity()));

        userIds = new ArrayList<>(userDtoMap.keySet());

        QueryWrapper<Users> wrapper = new QueryWrapper<>();

        wrapper.eq( USER+ ".course_id", module.getCourseId());
        wrapper.in( USER+ ".user_id", userIds);


        wrapper.eq( USER+".del_flag", false);

        wrapper.orderByDesc(ASSIGN_USER+".create_time");

        IPage<Users> usersPage  = assignUsersService.completionList(page,wrapper,module.getInstance());
        List<AssignCompletionVO> list = new ArrayList<>();

        usersPage.getRecords().forEach(e -> {
            AssignCompletionVO assignCompletionVO = new AssignCompletionVO();
            assignCompletionVO.setId(e.getUserId());
            UserDto userDto = userDtoMap.get(e.getUserId());
            if (ObjectNull.isNotNull(userDto)) {
                assignCompletionVO.setDeptName(userDto.getDeptName());
                assignCompletionVO.setRealName(userDto.getRealName());
                assignCompletionVO.setPhone(userDto.getPhone());
            }
            CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                    .eq(CourseModuleCompletion::getCmId, courseModuleId)
                    .eq(CourseModuleCompletion::getUserId, e.getUserId()));
            if (ObjectNull.isNotNull(completion)) {
                assignCompletionVO.setLastViewTime(completion.getLastViewTime());
            }

            AssignUsers assignUsers = assignUsersService.getOne(new LambdaQueryWrapper<AssignUsers>().
                    eq(AssignUsers::getUserId, e.getUserId()
                    ).eq(AssignUsers::getAssignId,module.getInstance()).last("limit 1"));
            if(!ObjectUtils.isEmpty(assignUsers)){
                if(assignUsers.getStatus() ==1){
                    assignCompletionVO.setCompletionStatus("已完成");
                    assignCompletionVO.setSubmitTime(assignUsers.getCreateTime());
                }
            }else {
                assignCompletionVO.setCompletionStatus("未完成");
            }
            AssignUsersVO assignUserVO = assignUsersService.getAssignUserVO(assignUsers);
            if(!ObjectUtils.isEmpty(assignUserVO)){
                assignCompletionVO.setAssignUsersVO(assignUserVO);
            }

            assignCompletionVO.setPeriodAndSubjects(userSubjectsService.getPerAndSubs(e.getUserId(),e.getCourseId()));
            list.add(assignCompletionVO);
        });

        completionPage.setTotal(usersPage.getTotal());
        completionPage.setRecords(list);

       return completionPage;
    }

    @Override
    public void exportCompletion(Page page, Object completionListQueryVo, String courseModuleId, HttpServletResponse response) {
        Page<AssignCompletionVO> assignCompletionVOPage = completionList(page, completionListQueryVo, courseModuleId);
        List<AssignCompletionVO> list = assignCompletionVOPage.getRecords();

        try {
            //在内存操作写到浏览器
            ExcelWriter writer = ExcelUtil.getWriter(true);

            //自定义别名
            writer.addHeaderAlias("realName", "姓名");
            writer.addHeaderAlias("periodAndSubjects", "学科学段");
            writer.addHeaderAlias("deptName", "所属单位");
            writer.addHeaderAlias("completionStatus", "活动完成情况");
            writer.addHeaderAlias("submitTime", "提交时间");
            writer.addHeaderAlias("lastViewTime", "最后浏览时间");
            //只输出有别名的字段
            writer.setOnlyAlias(true);

            //设置所有列宽
            writer.setColumnWidth(-10, 15);
            //设置内容左对齐
            writer.getStyleSet().setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);

            //一次性写出list对象到Excel使用默认样式，强制输出标题
            writer.write(list, true);
            getExportWriter(response, writer, "作业完成情况");
        } catch (Exception ex) {
            log.error("作业完成情况导出异常", ex);
            throw new BusinessException("导出失败：" + ex.getMessage());
        }
    }

    @Override
    public void getExportWriter(HttpServletResponse response, ExcelWriter excelWriter, String fileName) throws Exception {
        ModuleHandler.super.getExportWriter(response, excelWriter, fileName);
    }

    @Override
    public Boolean getCompletionStatus(String courseModuleId, String instanceId) {

        //完成情况：false未完成，true已完成
        Boolean flag = moduleGroupsService.checkUserInModuleGroup(courseModuleId);
        if (!flag) {
            return null;
        }
        Modules module = modulesService.getModule(courseModuleId);
        boolean trainOrHeadTeacher = PermissionUtil.checkTrainingAdmin() || roleComponent.checkHeadTeacher(module.getCourseId());
        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, courseModuleId)
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));
        if(ObjectUtils.isEmpty(completion)){
            completion = new CourseModuleCompletion();
            completion.setCourseId(module.getCourseId());
            completion.setCmId(courseModuleId);
            completion.setUserId(UserCurrentUtils.getUserId());
        }else {
            if( !trainOrHeadTeacher && BooleanUtil.isTrue(completion.getCompletionStatus())){
                return true;
            }
        }

        //如果是管理员、教研员、班主任 判断共同体成员是否都提交完作业
        if(trainOrHeadTeacher){
            AssignCompletionQueryVO queryVo = new AssignCompletionQueryVO();
            List<String> userIds = completionComponent.getUserIdsByCompletionListQueryVo(queryVo, courseModuleId);
            if(CollectionUtils.isEmpty(userIds)){
                completion.setCompletionStatus(false);
            }else {
                List<AssignUsers> assignUsersList = assignUsersService.list(new LambdaQueryWrapper<AssignUsers>().eq(AssignUsers::getAssignId, instanceId)
                        .in(!CollectionUtils.isEmpty(userIds),AssignUsers::getUserId,userIds));
                if(CollectionUtils.isEmpty(assignUsersList)){
                    completion.setCompletionStatus(false);
                }else {
                    long count = assignUsersList.stream().filter(assignUsers -> assignUsers.getStatus() ==1).count();
                    if(count >0 && count == userIds.size()){
                        completion.setCompletionStatus(true);
                    }else {
                        completion.setCompletionStatus(false);
                    }
                }
            }
            completionService.saveOrUpdate(completion);
        }

        return completion.getCompletionStatus();
    }

    @Override
    public void remarkCompletion(String courseModuleId, String instanceId) {

        if(PermissionUtil.checkTrainingAdmin()){
            //如果是管理员 判断学员、专家是否都提交完作业
            List<AssignUsers> assignUsersList = assignUsersService.list(new LambdaQueryWrapper<AssignUsers>().eq(AssignUsers::getAssignId, instanceId));
            if(CollectionUtils.isEmpty(assignUsersList)){
                throw new BusinessException("未提交完作业,不可设置已完成");
            }else {
                long count = assignUsersList.stream().filter(assignUsers -> assignUsers.getStatus() == 0 || assignUsers.getStatus() == 2).count();
                if(count >0){
                    throw new BusinessException("未提交完作业,不可设置已完成");
                }
            }
        }
        Modules module = modulesService.getModule(courseModuleId);
        if(roleComponent.checkExpert(module.getCourseId()) || roleComponent.checkStudent(module.getCourseId())){
            //如果是专家、学员
            //判断是否提交完作业
            AssignUsers assignUsers = assignUsersService.getOne(new LambdaQueryWrapper<AssignUsers>().eq(AssignUsers::getAssignId, instanceId)
                    .eq(AssignUsers::getUserId, UserCurrentUtils.getUserId()).last("limit 1"));
            if(!ObjectUtils.isEmpty(assignUsers)){
                if(assignUsers.getStatus() != 1){
                    throw new BusinessException("未提交完作业,不可设置已完成");
                }
            }else {
                throw new BusinessException("未提交完作业,不可设置已完成");
            }

        }
        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, courseModuleId)
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));
        if (ObjectNull.isNull(completion)) {
            completion = new CourseModuleCompletion();
            completion.setCmId(courseModuleId);
            completion.setUserId(UserCurrentUtils.getUserId());
        }

        completion.setCompletionStatus(true);
        completionService.updateById(completion);
    }

    @Override
    public Boolean checkShow(String courseModuleId, String instance) {
        if (PermissionUtil.checkTrainingAdmin()) {
            return true;
        }
        Modules module = modulesService.getModule(courseModuleId);
        if (!module.getVisible()){
            return false;
        }

        if (roleComponent.checkHeadTeacher(module.getCourseId())) {
            return true;
        }

        Boolean flag = moduleGroupsService.checkUserInModuleGroup(module.getId());
        return flag;
    }

    @Override
    public List<String> getPermissions(String courseModuleId, String instanceId) {
        if (PermissionUtil.checkTrainingAdmin()) {
            List<String> defaultAllPermissions = new ArrayList<>(PermissionConstant.DEFAULT_ALL_PERMISSIONS);
            defaultAllPermissions.add(PermissionConstant.ASSIGN_RATINGS);
            defaultAllPermissions.add(PermissionConstant.RATING_PROGRESS);
            addScorePermission(instanceId,defaultAllPermissions);
            return defaultAllPermissions;
        }
        Modules modules = modulesService.getById(courseModuleId);

        String courseId = modules.getCourseId();
        //班主任
        if (roleComponent.checkHeadTeacher(courseId)) {
            List<String> defaultAllPermissions = new ArrayList<>();
            defaultAllPermissions.add(PermissionConstant.COMPLETION);
            return defaultAllPermissions;
        }
        //小组长
        if (roleComponent.checkTeamLeader(courseId)) {
            List<String> defaultAllPermissions = new ArrayList<>();
            defaultAllPermissions.add(PermissionConstant.COMPLETION);
            return defaultAllPermissions;
        }
        //专家
        if(roleComponent.checkExpert(courseId)){
            List<String> defaultAllPermissions = new ArrayList<>();
            addScorePermission(instanceId, defaultAllPermissions);
            return defaultAllPermissions;
        }
        return new ArrayList<>();
    }

    private void addScorePermission(String instanceId, List<String> defaultAllPermissions) {
        AssignGradeTask assignGradeTask = assignGradeTaskService.getOne(new LambdaQueryWrapper<AssignGradeTask>().eq(AssignGradeTask::getAssignId, instanceId)
                .eq(AssignGradeTask::getUserId, UserCurrentUtils.getUserId()).last("limit 1"));
        if(!ObjectUtils.isEmpty(assignGradeTask)){
            defaultAllPermissions.add(PermissionConstant.SCORE);
        }
    }
}
