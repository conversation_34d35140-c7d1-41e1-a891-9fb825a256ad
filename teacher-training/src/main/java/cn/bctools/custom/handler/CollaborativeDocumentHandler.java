package cn.bctools.custom.handler;

import cn.bctools.auth.api.api.AuthUserServiceApi;
import cn.bctools.auth.api.dto.SearchUserDto;
import cn.bctools.auth.api.enums.UserQueryType;
import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.annotation.ModuleField;
import cn.bctools.custom.component.CompletionComponent;
import cn.bctools.custom.component.CourseUserRoleComponent;
import cn.bctools.custom.constant.PermissionConstant;
import cn.bctools.custom.constant.RoleConstant;
import cn.bctools.custom.entity.data.*;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.vo.CollaborativeDocumentCompletionPageVo;
import cn.bctools.custom.entity.vo.CollaborativeDocumentCompletionVo;
import cn.bctools.custom.service.*;
import cn.bctools.custom.util.CommonUtil;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.custom.util.PermissionUtil;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ModuleField(value = "collaborative_document")
@Slf4j
public class CollaborativeDocumentHandler implements ModuleHandler {

    @Autowired
    CollaborativeDocumentsService collaborativeDocumentsService;

    @Autowired
    ModuleFilesService moduleFilesService;

    @Autowired
    CourseModuleOnlineDocumentsService courseModuleOnlineDocumentsService;

    @Autowired
    ModuleGroupsService moduleGroupsService;

    @Autowired
    ModulesService modulesService;

    @Autowired
    UserGroupsService userGroupsService;

    @Autowired
    UserSubjectsService userSubjectsService;

    @Autowired
    CourseModuleCompletionService completionService;

    @Autowired
    CompletionComponent completionComponent;

    @Autowired
    AuthUserServiceApi authUserServiceApi;

    @Autowired
    CourseUserRoleComponent roleComponent;

    @Autowired
    UsersService usersService;

    @Override
    public Object getInfo(String courseModuleId, String instanceId) {
        return collaborativeDocumentsService.getCollaborativeDocument(courseModuleId);
    }

    @Override
    public void delete(String courseModuleId, String instanceId) {
        collaborativeDocumentsService.checkEditPermission();
        //删除资源表
        collaborativeDocumentsService.removeById(instanceId);
        //删除文件表
        moduleFilesService.remove(new LambdaQueryWrapper<ModuleFiles>().eq(ModuleFiles::getCmId, courseModuleId));

        // TODO 删除文库文档，之后才能删除关联记录
//        courseModuleOnlineDocumentsService.remove(
//                new LambdaQueryWrapper<CourseModuleOnlineDocuments>()
//                        .eq(CourseModuleOnlineDocuments::getCmId, courseModuleId)
//        );

        //删除小组关联表
        moduleGroupsService.remove(new LambdaQueryWrapper<ModuleGroups>().eq(ModuleGroups::getCmId, courseModuleId));
        //删除课程模组表
        modulesService.removeById(courseModuleId);
    }

    @Override
    public void copy(String courseModuleId, String instanceId) {
        collaborativeDocumentsService.checkEditPermission();
        //复制公开课表
        CollaborativeDocuments collaborativeDocument = collaborativeDocumentsService.getById(instanceId);
        CollaborativeDocuments copyCollaborativeDocument = BeanCopyUtil.copy(collaborativeDocument, CollaborativeDocuments.class);
        copyCollaborativeDocument.setId(null);
        // 获取当前最大编号
        String baseName = collaborativeDocument.getName();
        List<String> nameList = collaborativeDocumentsService.list(new LambdaQueryWrapper<CollaborativeDocuments>()
                        .select(CollaborativeDocuments::getName)
                        .likeRight(CollaborativeDocuments::getName, baseName + "("))
                .stream()
                .map(CollaborativeDocuments::getName)
                .collect(Collectors.toList());

        String copyName = CommonUtil.getCopyName(baseName, nameList);
        copyCollaborativeDocument.setName(copyName);
        EntityFillUtil.fillEntityFields(copyCollaborativeDocument, EntityFillType.CREATE);
        EntityFillUtil.fillEntityFields(copyCollaborativeDocument, EntityFillType.UPDATE);
        collaborativeDocumentsService.save(copyCollaborativeDocument);

        //复制模组表
        Modules module = modulesService.getModule(courseModuleId);
        Modules copyModule = BeanCopyUtil.copy(module, Modules.class);
        copyModule.setId(null);
        copyModule.setInstance(copyCollaborativeDocument.getId());
        EntityFillUtil.fillEntityFields(copyModule, EntityFillType.CREATE);
        EntityFillUtil.fillEntityFields(copyModule, EntityFillType.UPDATE);
        modulesService.save(copyModule);

        //复制小组关联表
        List<ModuleGroups> groupList = moduleGroupsService.list((new LambdaQueryWrapper<ModuleGroups>()
                .eq(ModuleGroups::getCmId, courseModuleId)
                .eq(ModuleGroups::getCourseId, module.getCourseId())));
        List<ModuleGroups> copyGroupList = groupList.stream().map(e -> {
            ModuleGroups copyGroup = BeanCopyUtil.copy(e, ModuleGroups.class);
            copyGroup.setId(null);
            copyGroup.setCmId(copyModule.getId());
            EntityFillUtil.fillEntityFields(copyGroup, EntityFillType.CREATE);
            EntityFillUtil.fillEntityFields(copyGroup, EntityFillType.UPDATE);
            return copyGroup;
        }).collect(Collectors.toList());
        moduleGroupsService.saveBatch(copyGroupList);
    }

    @Override
    public Boolean getCompletionStatus(String courseModuleId, String instanceId) {
        List<String> role = UserCurrentUtils.getCurrentUser().getRoleIds();
        if (PermissionUtil.checkCourseLeader() || role.contains(RoleConstant.PROVINCIAL_ADMINISTRATORS_ROLE)) {
            return null;
        }

        //TODO 修改，改为检测用户是否在权限设置的成员里
        Boolean flag = moduleGroupsService.checkUserInModuleGroup(courseModuleId);
        if (!flag) {
            return null;
        }

        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, courseModuleId)
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));
        if (ObjectNull.isNull(completion)) {
            return false;
        }
        return completion.getCompletionStatus();
    }

    @Override
    public Page completionList(Page page, Object completionListQueryVo, String courseModuleId) {
        Page<CollaborativeDocumentCompletionVo> completionPage = new Page<>(page.getCurrent(), page.getSize(), 0);

        Modules module = modulesService.getModule(courseModuleId);
        CollaborativeDocumentCompletionPageVo queryVo = BeanCopyUtil.copy(completionListQueryVo, CollaborativeDocumentCompletionPageVo.class);

        List<String> userIds = completionComponent.getUserIdsByCompletionListQueryVo(queryVo, courseModuleId);
        if (ObjectNull.isNull(userIds)) {
            return completionPage;
        }

        //TODO 取userIds和协同文档权限设置选择用户的交集 retainAll

        /*
         1、传输userIds和查询条件，远程调用获取符合条件用户ids
         2、completionService根据userIds进行分页查询
         3、根据userIds获取用户姓名、所属单位中文信息，转换成map
         4、根据map填充信息
        */
        SearchUserDto searchUserDto = new SearchUserDto().setUserIds(userIds).setType(UserQueryType.and);
        if (ObjectNull.isNotNull(queryVo.getKeyword())) {
            searchUserDto.setRealName(queryVo.getKeyword());
        }
        List<UserDto> userDtos = authUserServiceApi.searchUserGetInfo(searchUserDto).getData();
        if (ObjectNull.isNull(userDtos)) {
            return completionPage;
        }

        Map<String, UserDto> userDtoMap = userDtos.stream()
                .collect(Collectors.toMap(UserDto::getId, Function.identity()));

        userIds = new ArrayList<>(userDtoMap.keySet());

        Page<Users> usersPage = usersService
                .page(page, new LambdaQueryWrapper<Users>()
                        .eq(Users::getCourseId, module.getCourseId())
                        .in(Users::getUserId, userIds));
        List<CollaborativeDocumentCompletionVo> list = new ArrayList<>();

        usersPage.getRecords().stream().forEach(e -> {
            CollaborativeDocumentCompletionVo collaborativeDocumentCompletionVo = new CollaborativeDocumentCompletionVo();
            collaborativeDocumentCompletionVo.setId(e.getUserId());
            UserDto userDto = userDtoMap.get(e.getUserId());
            if (ObjectNull.isNotNull(userDto)) {
                collaborativeDocumentCompletionVo.setDeptName(userDto.getDeptName());
                collaborativeDocumentCompletionVo.setRealName(userDto.getRealName());
            }
            CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                    .eq(CourseModuleCompletion::getCmId, courseModuleId)
                    .eq(CourseModuleCompletion::getUserId, e.getUserId()));
            if (ObjectNull.isNotNull(completion)) {
                collaborativeDocumentCompletionVo.setLastViewTime(completion.getLastViewTime());
            }

            collaborativeDocumentCompletionVo.setPeriodAndSubjects(userSubjectsService.getPerAndSubs(e.getUserId(), e.getCourseId()));
            list.add(collaborativeDocumentCompletionVo);
        });

        completionPage.setTotal(usersPage.getTotal());
        completionPage.setRecords(list);

        return completionPage;
    }

    @Override
    public void exportCompletion(Page page, Object completionListQueryVo, String courseModuleId, HttpServletResponse response) {
        Page<CollaborativeDocumentCompletionVo> collaborativeDocumentCompletionVoPage = completionList(page, completionListQueryVo, courseModuleId);
        List<CollaborativeDocumentCompletionVo> list = collaborativeDocumentCompletionVoPage.getRecords();

        try {
            //在内存操作写到浏览器
            ExcelWriter writer = ExcelUtil.getWriter(true);

            //自定义别名
            writer.addHeaderAlias("realName", "姓名");
            writer.addHeaderAlias("periodAndSubjects", "学科学段");
            writer.addHeaderAlias("deptName", "所属单位");
            writer.addHeaderAlias("lastViewTime", "最后浏览时间");
            //只输出有别名的字段
            writer.setOnlyAlias(true);

            //设置所有列宽
            writer.setColumnWidth(-10, 15);
            //设置内容左对齐
            writer.getStyleSet().setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);

            //一次性写出list对象到Excel使用默认样式，强制输出标题
            writer.write(list, true);
            getExportWriter(response, writer, "协同文档完成情况");
        } catch (Exception ex) {
            log.error("协同文档完成情况导出异常", ex);
            throw new BusinessException("导出失败：" + ex.getMessage());
        }
    }

    @Override
    public void remarkCompletion(String courseModuleId, String instanceId) {

        if (!moduleCanSubmit(instanceId)) {
            throw new BusinessException("不在活动时间范围内，不可标记已完成");
        }

        Modules module = modulesService.getModule(courseModuleId);
        if (!roleComponent.checkStudent(module.getCourseId()) && !roleComponent.checkTeamLeader(module.getCourseId())) {
            throw new BusinessException("只有学员或小组长可标记已完成");
        }

        CourseModuleCompletion completion = completionService.getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, courseModuleId)
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));

        if (ObjectNull.isNull(completion)) {
            throw new BusinessException("暂未查看，不可标记已完成");
        }

        completion.setCompletionStatus(true);
        completionService.updateById(completion);

    }

    @Override
    public Boolean checkShow(String courseModuleId, String instance) {
        if (PermissionUtil.checkTrainingAdmin()) {
            return true;
        }
        Modules module = modulesService.getModule(courseModuleId);
        if (!module.getVisible()) {
            return false;
        }

        //TODO 修改，改为检测用户是否在权限设置的成员里
        if (roleComponent.checkHeadTeacher(module.getCourseId())) {
            return true;
        }

        Boolean flag = moduleGroupsService.checkUserInModuleGroup(module.getId());
        return flag;
    }

    @Override
    public List<String> getPermissions(String courseModuleId, String instanceId) {
        if (PermissionUtil.checkTrainingAdmin()) {
            return PermissionConstant.DEFAULT_ALL_PERMISSIONS;
        }
        Modules module = modulesService.getModule(courseModuleId);

        if (roleComponent.checkHeadTeacher(module.getCourseId())) {
            return Collections.singletonList(PermissionConstant.COMPLETION);
        }

        if (roleComponent.checkTeamLeader(module.getCourseId())) {
            //TODO 修改，改为检测用户是否在权限设置的成员里
            Boolean flag = moduleGroupsService.checkUserInModuleGroup(courseModuleId);
            if (flag) {
                return Collections.singletonList(PermissionConstant.COMPLETION);
            }
        }

        return new ArrayList<>();
    }

    /**
     * 活动时间内学员可以提交
     * 没有设置时间表示一直开放
     *
     * @param instanceId instanceId
     * @return 是否可以提交
     */
    private Boolean moduleCanSubmit(String instanceId) {
        CollaborativeDocuments collaborativeDocument = collaborativeDocumentsService.getById(instanceId);
        if (ObjectNull.isNotNull(collaborativeDocument.getStartTime()) &&
                (LocalDateTimeUtil.now().isBefore(collaborativeDocument.getStartTime()) || LocalDateTimeUtil.now().isAfter(collaborativeDocument.getEndTime()))
        ) {
            return false;
        }
        return true;
    }
}
