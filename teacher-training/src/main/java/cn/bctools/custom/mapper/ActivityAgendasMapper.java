package cn.bctools.custom.mapper;

import cn.bctools.custom.entity.data.ActivityAgendas;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 活动议程表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface ActivityAgendasMapper extends BaseMapper<ActivityAgendas> {

}
