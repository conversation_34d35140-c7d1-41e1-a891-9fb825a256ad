package cn.bctools.custom.mapper;

import cn.bctools.custom.entity.data.AssignGradeTask;
import cn.bctools.custom.entity.vo.AssignGradeTaskPlanQueryVO;
import cn.bctools.custom.entity.vo.AssignGradeTaskPlanVO;
import cn.bctools.custom.entity.vo.AssignGradeTaskQueryVO;
import cn.bctools.custom.entity.vo.AssignGradeTaskVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 作业评分任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface AssignGradeTaskMapper extends BaseMapper<AssignGradeTask> {

    Page<AssignGradeTaskVO> searchPageList(Page<AssignGradeTaskVO> page, @Param("criteria") AssignGradeTaskQueryVO assignGradeTaskQueryVO);

    Page<AssignGradeTaskPlanVO> searchPlanPageList(Page<AssignGradeTaskPlanVO> page, @Param("criteria") AssignGradeTaskPlanQueryVO assignGradeTaskPlanQueryVO);
}
