package cn.bctools.custom.mapper;

import cn.bctools.custom.entity.data.CourseModuleCompletion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 共同体模块用户情况表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface CourseModuleCompletionMapper extends BaseMapper<CourseModuleCompletion> {

}
