package cn.bctools.custom.mapper;

import cn.bctools.custom.entity.data.OnlineCourseGrade;
import cn.bctools.custom.entity.data.Users;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.database.interceptor.cache.JvsRedisCache;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 在线评课评分记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-29
 */
@CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
@Mapper
public interface OnlineCourseGradeMapper extends BaseMapper<OnlineCourseGrade> {

    @Select("SELECT u.* from course_users u "+
            "LEFT JOIN online_course_grade ocg ON u.user_id = ocg.user_id AND ocg.online_course_id = #{instance} AND ocg.del_flag =0 AND ocg.status=1 "+
            " ${ew.isEmptyOfNormal() ? '' : 'WHERE ' + ew.getSqlSegment() } ")
    IPage<Users> completionList(Page page, @Param("ew")QueryWrapper<Users> wrapper, @Param("instance") String instance);
}
