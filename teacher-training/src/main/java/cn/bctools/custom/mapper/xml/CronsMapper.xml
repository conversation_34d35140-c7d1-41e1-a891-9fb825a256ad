<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.custom.mapper.CronsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.custom.entity.data.Crons">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, update_time
    </sql>

</mapper>
