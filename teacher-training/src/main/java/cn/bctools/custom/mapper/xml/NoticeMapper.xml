<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.custom.mapper.NoticeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.custom.entity.data.Notice">
        <id column="id" property="id" />
        <result column="course_id" property="courseId" />
        <result column="theme" property="theme" />
        <result column="content" property="content" />
        <result column="is_top" property="isTop" />
        <result column="visible" property="visible" />
        <result column="can_download" property="canDownload" />
        <result column="sort" property="sort" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_by_id" property="createById" />
        <result column="update_by_id" property="updateById" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, course_id, theme, content, is_top, visible, can_download, sort, create_time, update_time, del_flag, tenant_id, create_by, update_by, create_by_id, update_by_id
    </sql>

</mapper>
