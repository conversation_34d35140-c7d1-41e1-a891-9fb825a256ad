<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.custom.mapper.PeriodSubjectsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.custom.entity.data.PeriodSubjects">
        <id column="id" property="id" />
        <result column="course_id" property="courseId" />
        <result column="per_and_sub_id" property="perAndSubId" />
        <result column="per_and_sub" property="perAndSub" />
        <result column="period_id" property="periodId" />
        <result column="subject_id" property="subjectId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, course_id, per_and_sub_id, per_and_sub, period_id, subject_id
    </sql>

</mapper>
