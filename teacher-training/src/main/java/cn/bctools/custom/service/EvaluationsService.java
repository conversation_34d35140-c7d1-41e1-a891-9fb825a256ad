package cn.bctools.custom.service;

import cn.bctools.custom.entity.data.Evaluations;
import cn.bctools.custom.entity.vo.EvaluationVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 评价表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface EvaluationsService extends IService<Evaluations> {

    EvaluationVo getEvaluation(String courseModuleId);

    @Transactional(rollbackFor = Exception.class)
    Boolean create(EvaluationVo evaluationVo);

    @Transactional(rollbackFor = Exception.class)
    Boolean edit(EvaluationVo evaluationVo);

    Boolean checkEditPermission();
}
