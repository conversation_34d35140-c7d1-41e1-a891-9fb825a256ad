package cn.bctools.custom.service;

import cn.bctools.custom.entity.data.ModuleFiles;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 共同体模块附件关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface ModuleFilesService extends IService<ModuleFiles> {

    void saveOrUpdateFileContact(List<ModuleFiles> fileList, String courseId, String cmId,String remark,String createById,String infoId);

    List<ModuleFiles> getFileList(String cmId,String courseId,String remark,String createById);


    List<ModuleFiles> getFileList(String cmId,String courseId,String remark,String createById,String infoId);
    
    List<ModuleFiles> getFileListByCourseIdAndCmId(String courseId,String cmId,String bucketName);

    List<ModuleFiles> setModuleFileTagToList(List<ModuleFiles> files, String moduleFileTag);

    ModuleFiles getFile(String id);
}
