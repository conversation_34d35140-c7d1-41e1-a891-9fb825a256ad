package cn.bctools.custom.service;

import cn.bctools.custom.entity.data.TrainingRoom;
import cn.bctools.custom.entity.vo.TrainingRoomVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 研修室表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
public interface TrainingRoomService extends IService<TrainingRoom> {

    TrainingRoomVo getTrainingRoom(String courseModuleId);

    <PERSON><PERSON><PERSON> create(TrainingRoomVo trainingRoomVo);

    Boolean edit(TrainingRoomVo trainingRoomVo);

    Boolean checkEditPermission();
}
