package cn.bctools.custom.service.impl;

import cn.bctools.custom.constant.BucketConstant;
import cn.bctools.custom.entity.data.ActivityAgendas;
import cn.bctools.custom.entity.data.ModuleFiles;
import cn.bctools.custom.entity.data.Modules;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.enums.ModuleType;
import cn.bctools.custom.entity.vo.ActivityAgendasVO;
import cn.bctools.custom.mapper.ActivityAgendasMapper;
import cn.bctools.custom.service.ActivityAgendasService;
import cn.bctools.custom.service.ModuleFilesService;
import cn.bctools.custom.service.ModulesService;
import cn.bctools.custom.util.EntityFillUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <p>
 * 活动议程表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ActivityAgendasServiceImpl extends ServiceImpl<ActivityAgendasMapper, ActivityAgendas> implements ActivityAgendasService {

    @Autowired
    ModuleFilesService moduleFilesService;
    @Autowired
    ModulesService modulesService;

    @Override
    public Boolean saveActivityAgendas(ActivityAgendasVO activityAgendasVO) {
        ActivityAgendas activityAgendas = new ActivityAgendas();
        BeanUtils.copyProperties(activityAgendasVO, activityAgendas);
        EntityFillUtil.fillEntityFields(activityAgendas, EntityFillType.CREATE);
        return saveOrUpdate(activityAgendas);
    }

    @Override
    public Boolean updateActivityAgendas(ActivityAgendasVO activityAgendasVO) {
        ActivityAgendas activityAgendas = new ActivityAgendas();
        BeanUtils.copyProperties(activityAgendasVO, activityAgendas);
        EntityFillUtil.fillEntityFields(activityAgendas, EntityFillType.UPDATE);
        boolean activityResult = saveOrUpdate(activityAgendas);

        //共同体模块关联表
        String courseId = activityAgendas.getCourseId();
//        String moduleTypeName = ModuleType.activity_agenda.name();
//        String id = activityAgendas.getId();
//        Modules courseModule = modulesService.saveCourseModuleContact(courseId, moduleTypeName, id,null);
//        String cmId = courseModule.getId();
        List<ModuleFiles> fileList = activityAgendasVO.getFileList();
        //存放附件信息

        moduleFilesService.saveOrUpdateFileContact(fileList, courseId, "0", BucketConstant.MOD_ACTIVITY_AGENDA,"","");

        return activityResult;
    }

    @Override
    public ActivityAgendasVO getActivityAgendasById(String id) {
        ActivityAgendas activityAgendas = getById(id);
        ActivityAgendasVO activityAgendasVO = new ActivityAgendasVO();
        BeanUtils.copyProperties(activityAgendas, activityAgendasVO);
        //活动附件信息
        List<ModuleFiles> fileList = moduleFilesService.getFileList("0", activityAgendas.getCourseId(), BucketConstant.MOD_ACTIVITY_AGENDA, "");
        if (!CollectionUtils.isEmpty(fileList)) {
            activityAgendasVO.setFileList(fileList);
        }
        return activityAgendasVO;
    }
}
