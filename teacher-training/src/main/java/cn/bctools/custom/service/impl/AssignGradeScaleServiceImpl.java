package cn.bctools.custom.service.impl;

import cn.bctools.custom.entity.data.AssignGradeScale;
import cn.bctools.custom.mapper.AssignGradeScaleMapper;
import cn.bctools.custom.service.AssignGradeScaleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 作业量表评分记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AssignGradeScaleServiceImpl extends ServiceImpl<AssignGradeScaleMapper, AssignGradeScale> implements AssignGradeScaleService {

}
