package cn.bctools.custom.service.impl;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.entity.data.ClassPreparations;
import cn.bctools.custom.entity.data.CourseModuleOnlineDocuments;
import cn.bctools.custom.entity.data.Modules;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.enums.ModuleType;
import cn.bctools.custom.entity.vo.ClassPreparationFileVo;
import cn.bctools.custom.entity.vo.ClassPreparationVo;
import cn.bctools.custom.mapper.ClassPreparationsMapper;
import cn.bctools.custom.service.*;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.custom.util.PermissionUtil;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <p>
 * 集体备课表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ClassPreparationsServiceImpl extends ServiceImpl<ClassPreparationsMapper, ClassPreparations> implements ClassPreparationsService {

    @Autowired
    CourseModuleOnlineDocumentsService courseModuleOnlineDocumentsService;

    @Autowired
    ModulesService modulesService;

    @Autowired
    ModuleGroupsService moduleGroupsService;

    @Override
    public ClassPreparationVo get(String courseModuleId) {
        ClassPreparationVo classPreparationVo = new ClassPreparationVo();
        Modules module = modulesService.getById(courseModuleId);
        if (ObjectNull.isNull(module)) {
            return classPreparationVo;
        }
        ClassPreparations classPreparation = getById(module.getInstance());
        if (ObjectNull.isNull(classPreparation)) {
            return classPreparationVo;
        }

        classPreparationVo = BeanCopyUtil.copy(classPreparation, ClassPreparationVo.class);

        String currentUserId = UserCurrentUtils.getUserId();

        List<CourseModuleOnlineDocuments> files = courseModuleOnlineDocumentsService.list(
                courseModuleId,
                module.getCourseId(),
                currentUserId,
                ""
        );
        classPreparationVo.setOnlineDocuments(files);

        String groupIds = moduleGroupsService.getGroupIds(courseModuleId);
        classPreparationVo.setGroupIds(groupIds);

        classPreparationVo.setModule(ModuleType.class_preparation.getName());

        classPreparationVo.setSection(module.getSection());
        classPreparationVo.setVisible(module.getVisible());

        classPreparationVo.setCanSubmit(canSubmit(classPreparation));
        classPreparationVo.setCanDownload(module.getCanDownload());
        if(PermissionUtil.checkTrainingAdmin()){
            //文件是否允许下载
            classPreparationVo.setEnableDownload(true);
        }else {
            classPreparationVo.setEnableDownload(module.getCanDownload());
        }
        return classPreparationVo;
    }

    @Override
    public Boolean create(ClassPreparationVo classPreparationVo) {
        ClassPreparations classPreparation = BeanCopyUtil.copy(classPreparationVo, ClassPreparations.class);
        EntityFillUtil.fillEntityFields(classPreparation, EntityFillType.CREATE);
        boolean flag = saveOrUpdate(classPreparation);
        String courseId = classPreparation.getCourseId();
        String moduleType = ModuleType.class_preparation.getName();
        String classPreparationId = classPreparation.getId();
        Boolean visible = classPreparationVo.getVisible();
        Modules module = modulesService.saveCourseModuleContact(courseId, moduleType, classPreparationId, visible, classPreparationVo.getSection(), classPreparationVo.getCanDownload());
        moduleGroupsService.saveOrUpdateCourseGroupContact(courseId, classPreparationVo.getGroupIds(), module.getId());

        List<CourseModuleOnlineDocuments> files = classPreparationVo.getOnlineDocuments();
        courseModuleOnlineDocumentsService.saveOrUpdateContact(
                files,
                module.getId(),
                courseId,
                ""
        );

        return flag;
    }

    @Override
    public Boolean edit(ClassPreparationVo classPreparationVo) {
        String id = classPreparationVo.getId();
        if (StrUtil.isEmpty(id)) {
            throw new BusinessException("集体备课id不能为空");
        }
        checkEditPermission(id);
        ClassPreparations classPreparation = BeanCopyUtil.copy(classPreparationVo, ClassPreparations.class);

        if (ObjectNull.isNull(classPreparationVo.getStartTime())) {
            classPreparation.setStartTime(null);
        }
        if (ObjectNull.isNull(classPreparationVo.getEndTime())) {
            classPreparation.setEndTime(null);
        }

        EntityFillUtil.fillEntityFields(classPreparation, EntityFillType.UPDATE);
        boolean flag = saveOrUpdate(classPreparation);

        String courseId = classPreparationVo.getCourseId();
        String moduleType = ModuleType.class_preparation.getName();
        String classPreparationId = classPreparation.getId();
        Boolean visible = classPreparationVo.getVisible();
        Modules module = modulesService.saveCourseModuleContact(courseId, moduleType, classPreparationId, visible, classPreparationVo.getSection(), classPreparationVo.getCanDownload());
        moduleGroupsService.saveOrUpdateCourseGroupContact(courseId, classPreparationVo.getGroupIds(), module.getId());

        List<CourseModuleOnlineDocuments> files = classPreparationVo.getOnlineDocuments();
        courseModuleOnlineDocumentsService.saveOrUpdateContact(
                files,
                module.getId(),
                courseId,
                ""
        );

        return flag;
    }

    @Override
    public Boolean checkEditPermission(String instance) {
        if (PermissionUtil.checkTrainingAdmin()) {
            return true;
        }
        ClassPreparations classPreparation = getById(instance);
        if (!ObjectUtils.isEmpty(classPreparation)) {
            String createById = classPreparation.getCreateById();
            if (UserCurrentUtils.getUserId().equals(createById)) {
                return true;
            }
        }

        throw new BusinessException("暂无权限");
    }

    @Override
    public Boolean editDocuments(ClassPreparationFileVo classPreparationFileVo) {
        String id = classPreparationFileVo.getId();

        ClassPreparations classPreparation = getById(id);
        if (ObjectUtils.isEmpty(classPreparation)) {
            throw new BusinessException("集体备课不存在");
        }

        if (!this.canSubmit(classPreparation)) {
            throw new BusinessException("集体备课未开放");
        }

        Modules module = modulesService.getModuleByInstanceId(classPreparation.getId());

        // TODO 学生或小组长只能删除自己的文件

        courseModuleOnlineDocumentsService.saveOrUpdateContact(
                classPreparationFileVo.getOnlineDocuments(),
                module.getId(),
                classPreparation.getCourseId(),
                ""
        );

        return true;
    }

    /**
     * 模块是否在活动范围内
     * 时间为空表示一直开放
     */
    private Boolean canSubmit(ClassPreparations classPreparation) {
        if (ObjectNull.isNotNull(classPreparation.getStartTime()) &&
                (LocalDateTimeUtil.now().isBefore(classPreparation.getStartTime()) || LocalDateTimeUtil.now().isAfter(classPreparation.getEndTime()))
        ) {
            return false;
        }
        return true;
    }
}
