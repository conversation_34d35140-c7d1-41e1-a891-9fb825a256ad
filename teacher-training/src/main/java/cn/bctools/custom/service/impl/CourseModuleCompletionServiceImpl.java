package cn.bctools.custom.service.impl;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.entity.data.CourseModuleCompletion;
import cn.bctools.custom.entity.data.Modules;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.vo.UpdateAccessVo;
import cn.bctools.custom.mapper.CourseModuleCompletionMapper;
import cn.bctools.custom.service.CourseModuleCompletionService;
import cn.bctools.custom.service.ModulesService;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 共同体模块用户情况表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class CourseModuleCompletionServiceImpl extends ServiceImpl<CourseModuleCompletionMapper, CourseModuleCompletion> implements CourseModuleCompletionService {

    @Autowired
    ModulesService modulesService;

    @Override
    public void view(String courseModuleId) {

        Modules module = modulesService.getById(courseModuleId);
        if (ObjectNull.isNull(module)){
            throw new BusinessException("找不到对应模块");
        }

        CourseModuleCompletion completion = getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, courseModuleId)
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));
        if (ObjectNull.isNull(completion)) {
            CourseModuleCompletion courseModuleCompletion = new CourseModuleCompletion();
            courseModuleCompletion.setCourseId(module.getCourseId());
            courseModuleCompletion.setCmId(courseModuleId);
            courseModuleCompletion.setUserId(UserCurrentUtils.getUserId());
            courseModuleCompletion.setView(1);
            courseModuleCompletion.setLastViewTime(LocalDateTimeUtil.now());
            EntityFillUtil.fillEntityFields(courseModuleCompletion, EntityFillType.CREATE);
            save(courseModuleCompletion);
        } else {
            completion.setView(completion.getView() + 1);
            completion.setLastViewTime(LocalDateTimeUtil.now());
            EntityFillUtil.fillEntityFields(completion, EntityFillType.UPDATE);
            saveOrUpdate(completion);
        }
    }

    @Override
    public void updateAccess(UpdateAccessVo updateAccessVo) {
        Modules module = modulesService.getById(updateAccessVo.getCmId());
        if (ObjectNull.isNull(module)){
            throw new BusinessException("找不到对应模块");
        }

        CourseModuleCompletion completion = getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, updateAccessVo.getCmId())
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));
        if (ObjectNull.isNull(completion)) {
            throw new BusinessException("找不到活动完成情况");
        }

        completion.setAccess(completion.getAccess() + updateAccessVo.getAccess());
        EntityFillUtil.fillEntityFields(completion, EntityFillType.UPDATE);
        saveOrUpdate(completion);
    }

    @Override
    public void updateDownload(String courseModuleId) {
        Modules module = modulesService.getById(courseModuleId);
        if (ObjectNull.isNull(module)){
            throw new BusinessException("找不到对应模块");
        }

        CourseModuleCompletion completion = getOne(new LambdaQueryWrapper<CourseModuleCompletion>()
                .eq(CourseModuleCompletion::getCmId, courseModuleId)
                .eq(CourseModuleCompletion::getUserId, UserCurrentUtils.getUserId())
                .last("limit 1"));
        if (ObjectNull.isNull(completion)) {
            CourseModuleCompletion courseModuleCompletion = new CourseModuleCompletion();
            courseModuleCompletion.setCourseId(module.getCourseId());
            courseModuleCompletion.setCmId(courseModuleId);
            courseModuleCompletion.setUserId(UserCurrentUtils.getUserId());
            courseModuleCompletion.setDownload(1);
            EntityFillUtil.fillEntityFields(courseModuleCompletion, EntityFillType.CREATE);
            save(courseModuleCompletion);
        } else {
            completion.setDownload(completion.getDownload() + 1);
            EntityFillUtil.fillEntityFields(completion, EntityFillType.UPDATE);
            saveOrUpdate(completion);
        }
    }

}
