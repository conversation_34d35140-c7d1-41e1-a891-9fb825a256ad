package cn.bctools.custom.service.impl;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.entity.data.ModuleFiles;
import cn.bctools.custom.entity.data.Modules;
import cn.bctools.custom.entity.data.OnlineClasses;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.enums.ModuleType;
import cn.bctools.custom.entity.vo.OnlineClassVo;
import cn.bctools.custom.mapper.OnlineClassesMapper;
import cn.bctools.custom.service.ModuleFilesService;
import cn.bctools.custom.service.ModuleGroupsService;
import cn.bctools.custom.service.ModulesService;
import cn.bctools.custom.service.OnlineClassesService;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.custom.util.PermissionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

import java.util.List;

import static cn.bctools.custom.constant.BucketConstant.MOD_ONLINE_CLASS;

/**
 * <p>
 * 在线听课 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class OnlineClassesServiceImpl extends ServiceImpl<OnlineClassesMapper, OnlineClasses> implements OnlineClassesService {

    @Autowired
    ModuleFilesService moduleFilesService;

    @Autowired
    ModulesService modulesService;

    @Autowired
    ModuleGroupsService moduleGroupsService;

    @Override
    public OnlineClassVo getOnlineClass(String courseModuleId) {
        OnlineClassVo onlineClassVo = new OnlineClassVo();
        Modules module = modulesService.getById(courseModuleId);
        if (ObjectNull.isNull(module)) {
            return onlineClassVo;
        }
        OnlineClasses onlineClass = getById(module.getInstance());
        if (ObjectNull.isNull(onlineClass)) {
            return onlineClassVo;
        }

        onlineClassVo = BeanCopyUtil.copy(onlineClass, OnlineClassVo.class);

        List<ModuleFiles> files = moduleFilesService.getFileList(courseModuleId, module.getCourseId(), MOD_ONLINE_CLASS, "");
        onlineClassVo.setFileList(files);

        String groupIds = moduleGroupsService.getGroupIds(courseModuleId);
        onlineClassVo.setGroupIds(groupIds);

        onlineClassVo.setModule(ModuleType.online_class.getName());
        onlineClassVo.setSection(module.getSection());
        onlineClassVo.setCanDownload(module.getCanDownload());
        if(PermissionUtil.checkTrainingAdmin()){
            //文件是否允许下载
            onlineClassVo.setEnableDownload(true);
        }else {
            onlineClassVo.setEnableDownload(module.getCanDownload());
        }
        return onlineClassVo;
    }

    @Override
    public Boolean create(OnlineClassVo onlineClassVo) {
        checkEditPermission();
        OnlineClasses onlineClass = BeanCopyUtil.copy(onlineClassVo, OnlineClasses.class);
        EntityFillUtil.fillEntityFields(onlineClass, EntityFillType.CREATE);
        boolean flag = saveOrUpdate(onlineClass);
        String courseId = onlineClass.getCourseId();
        String moduleType = ModuleType.online_class.getName();
        String onlineClassId = onlineClass.getId();
        Boolean visible = onlineClassVo.getVisible();
        Modules module = modulesService.saveCourseModuleContact(courseId, moduleType, onlineClassId, visible, onlineClassVo.getSection(), onlineClassVo.getCanDownload());
        moduleGroupsService.saveOrUpdateCourseGroupContact(courseId, onlineClassVo.getGroupIds(), module.getId());
        List<ModuleFiles> fileList = onlineClassVo.getFileList();
        //附件信息

        moduleFilesService.saveOrUpdateFileContact(fileList, courseId, module.getId(), MOD_ONLINE_CLASS, "", "");

        return flag;
    }

    @Override
    public Boolean edit(OnlineClassVo onlineClassVo) {
        checkEditPermission();
        OnlineClasses onlineClass = BeanCopyUtil.copy(onlineClassVo, OnlineClasses.class);
        EntityFillUtil.fillEntityFields(onlineClass, EntityFillType.UPDATE);
        boolean flag = saveOrUpdate(onlineClass);
        String courseId = onlineClassVo.getCourseId();
        String moduleType = ModuleType.online_class.getName();
        String onlineClassId = onlineClass.getId();
        Boolean visible = onlineClassVo.getVisible();
        Modules module = modulesService.saveCourseModuleContact(courseId, moduleType, onlineClassId, visible,onlineClassVo.getSection(), onlineClassVo.getCanDownload());
        moduleGroupsService.saveOrUpdateCourseGroupContact(courseId, onlineClassVo.getGroupIds(), module.getId());
        List<ModuleFiles> fileList = onlineClassVo.getFileList();
        //附件信息

        moduleFilesService.saveOrUpdateFileContact(fileList, courseId, module.getId(), MOD_ONLINE_CLASS, "", "");

        return flag;
    }

    @Override
    public Boolean checkEditPermission() {
        if (!PermissionUtil.checkTrainingAdmin()) {
            throw new BusinessException("暂无权限");
        }

        return true;
    }
}
