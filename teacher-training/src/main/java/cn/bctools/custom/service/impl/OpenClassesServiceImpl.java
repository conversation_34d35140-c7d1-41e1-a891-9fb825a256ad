package cn.bctools.custom.service.impl;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.custom.entity.data.*;
import cn.bctools.custom.entity.enums.EntityFillType;
import cn.bctools.custom.entity.enums.ModuleType;
import cn.bctools.custom.entity.vo.OpenClassesVo;
import cn.bctools.custom.mapper.OpenClassesMapper;
import cn.bctools.custom.service.ModuleFilesService;
import cn.bctools.custom.service.ModuleGroupsService;
import cn.bctools.custom.service.ModulesService;
import cn.bctools.custom.service.OpenClassesService;
import cn.bctools.custom.util.EntityFillUtil;
import cn.bctools.custom.util.PermissionUtil;
import cn.bctools.oss.template.OssTemplate;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

import java.util.List;

import static cn.bctools.custom.constant.BucketConstant.MOD_OPEN_CLASS;

/**
 * <p>
 * 公开课 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class OpenClassesServiceImpl extends ServiceImpl<OpenClassesMapper, OpenClasses> implements OpenClassesService {

    @Autowired
    ModuleFilesService moduleFilesService;

    @Autowired
    ModulesService modulesService;

    @Autowired
    ModuleGroupsService moduleGroupsService;

    @Autowired
    OssTemplate ossTemplate;


    @Override
    public OpenClassesVo getOpenClass(String courseModuleId) {
        OpenClassesVo openClassesVo = new OpenClassesVo();
        Modules module = modulesService.getById(courseModuleId);
        if (ObjectNull.isNull(module)) {
            return openClassesVo;
        }
        OpenClasses openClass = getById(module.getInstance());
        if (ObjectNull.isNull(openClass)) {
            return openClassesVo;
        }

        openClassesVo = BeanCopyUtil.copy(openClass, OpenClassesVo.class);

        List<ModuleFiles> files = moduleFilesService.getFileList(courseModuleId, module.getCourseId(), MOD_OPEN_CLASS, "");
        openClassesVo.setFileList(files);

        String groupIds = moduleGroupsService.getGroupIds(courseModuleId);
        openClassesVo.setGroupIds(groupIds);

        openClassesVo.setModule(ModuleType.open_class.getName());
        openClassesVo.setSection(module.getSection());
        openClassesVo.setCanDownload(module.getCanDownload());
        if(PermissionUtil.checkTrainingAdmin()){
            //文件是否允许下载
            openClassesVo.setEnableDownload(true);
        }else {
            openClassesVo.setEnableDownload(module.getCanDownload());
        }
        return openClassesVo;
    }

    @Override
    public Boolean create(OpenClassesVo openClassesVo) {
        checkEditPermission();
        OpenClasses openClass = BeanCopyUtil.copy(openClassesVo, OpenClasses.class);
        EntityFillUtil.fillEntityFields(openClass, EntityFillType.CREATE);
        boolean flag = saveOrUpdate(openClass);
        String courseId = openClass.getCourseId();
        String moduleType = ModuleType.open_class.getName();
        String openClassId = openClass.getId();
        Boolean visible = openClassesVo.getVisible();
        Modules module = modulesService.saveCourseModuleContact(courseId, moduleType, openClassId, visible,openClassesVo.getSection(), openClassesVo.getCanDownload());
        moduleGroupsService.saveOrUpdateCourseGroupContact(courseId, openClassesVo.getGroupIds(), module.getId());
        List<ModuleFiles> fileList = openClassesVo.getFileList();
        //附件信息

        moduleFilesService.saveOrUpdateFileContact(fileList, courseId, module.getId(), MOD_OPEN_CLASS, "", "");

        return flag;
    }

    @Override
    public Boolean edit(OpenClassesVo openClassesVo) {
        OpenClasses openClass = BeanCopyUtil.copy(openClassesVo, OpenClasses.class);
        EntityFillUtil.fillEntityFields(openClass, EntityFillType.UPDATE);
        checkEditPermission();
        boolean flag = saveOrUpdate(openClass);
        String courseId = openClassesVo.getCourseId();
        String moduleType = ModuleType.open_class.getName();
        String openClassId = openClass.getId();
        Boolean visible = openClassesVo.getVisible();
        Modules module = modulesService.saveCourseModuleContact(courseId, moduleType, openClassId, visible,openClassesVo.getSection(), openClassesVo.getCanDownload());
        moduleGroupsService.saveOrUpdateCourseGroupContact(courseId, openClassesVo.getGroupIds(), module.getId());
        List<ModuleFiles> fileList = openClassesVo.getFileList();
        //附件信息

        moduleFilesService.saveOrUpdateFileContact(fileList, courseId, module.getId(), MOD_OPEN_CLASS, "", "");

        return flag;
    }

    @Override
    public Boolean checkEditPermission() {
        if (!PermissionUtil.checkTrainingAdmin()) {
            throw new BusinessException("暂无权限");
        }

        return true;
    }
}
