package cn.bctools.custom.service.impl;

import cn.bctools.custom.entity.data.PeriodSubjects;
import cn.bctools.custom.mapper.PeriodSubjectsMapper;
import cn.bctools.custom.service.PeriodSubjectsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 共同体学段学科 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Slf4j
@Service
@AllArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class PeriodSubjectsServiceImpl extends ServiceImpl<PeriodSubjectsMapper, PeriodSubjects> implements PeriodSubjectsService {

}
