package cn.bctools.custom.util;

import cn.bctools.custom.entity.vo.AssignGradeTaskVO;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @project jvs-backend
 * @date 2024/6/9
 * @description
 * @package cn.bctools.custom.util
 */
public class BuildExportResponseUtil {
    public static void buildExportResponse(HttpServletResponse response, List<?> records, Map<String, String> headerAlias, String fileName) throws IOException {
        //在内存操作写到浏览器
        ExcelWriter writer = ExcelUtil.getWriter(true);

        //自定义别名
        for (String s : headerAlias.keySet()) {
            writer.addHeaderAlias(s, headerAlias.get(s));
        }

        //设置所有列宽
        writer.setColumnWidth(-10, 15);
        //设置内容左对齐
        writer.getStyleSet().setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);

        //只输出有别名的字段
        writer.setOnlyAlias(true);
        //一次性写出list对象到Excel使用默认样式，强制输出标题
        writer.write(records, true);

        //设置浏览器响应的格式
        response.setContentType("application/vnd.openxmlformats-officeedocument.sheet;charset=utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

        ServletOutputStream out = response.getOutputStream();
        writer.flush(out, true);
        out.close();
        writer.close();
    }
}
