#!/bin/bash

# CSV 文件路径
CSV_FILE="/data-remote/download/2024ronghe/待审批.csv"
# 基础文件夹路径
BASE_DIR="/data-remote/download/2024ronghe/files"

# 读取 CSV 文件中的每一行
while IFS=, read -r filename url; do
    # 去除 URL 前后的空白字符和回车符
    url=$(echo "$url" | tr -d '\r' | xargs)

    # 输出调试信息
    echo "Processing file: $filename"
    echo "URL: $url"

    # 替换文件名中的特殊字符
    filename=$(echo "$filename" | sed 's/[[:space:]]\+/_/g' | sed 's/,/，/g' | sed 's/^\xef\xbb\xbf//')
#    filename=$(echo "$filename" | sed 's/[^a-zA-Z0-9._-]/_/g')
    echo "${filename%/*}"
    # 创建必要的目录结构
    directory="${BASE_DIR}"/"${filename%/*}"
    if ! mkdir -p "$directory"; then
        echo "Failed to create directory: $directory"
        continue
    fi

    # 下载文件到正确的目录
    output_path="${directory}"/"${filename##*/}"
    output_path=$(echo "$output_path" | tr -d '\r' | xargs)
    output_path=$(echo "$output_path" | sed 's/,/，/g')

    # 输出路径信息
    echo "Saving to: $output_path"

    if ! curl -o "$output_path" -L "$url"; then
        echo "Failed to download file: $url"
        continue
    fi

    echo "Downloaded file to: $output_path"
done < "$CSV_FILE"
